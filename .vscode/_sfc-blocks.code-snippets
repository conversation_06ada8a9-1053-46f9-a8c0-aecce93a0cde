{"script": {"scope": "vue", "prefix": "script", "body": ["<script>", "export default {", "\t${0}", "}", "</script>"], "description": "<script>"}, "template": {"scope": "vue", "prefix": "template", "body": ["<template>", "\t${0}", "</template>"], "description": "<template>"}, "style": {"scope": "vue", "prefix": "style", "body": ["<style lang=\"less\" scoped>", "${0}", "</style>"], "description": "<style lang=\"less\" scoped>"}}