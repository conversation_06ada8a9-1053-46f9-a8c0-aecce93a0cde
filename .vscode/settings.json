{
  // ===
  // Spacing
  // ===

  "editor.insertSpaces": true,
  "editor.tabSize": 2,
  "editor.trimAutoWhitespace": true,
  "files.trimTrailingWhitespace": true,
  "files.eol": "\n",
  "files.insertFinalNewline": true,
  "files.trimFinalNewlines": true,

  // ===
  // Files
  // ===

  "files.exclude": {
    "**/*.log": true,
    "**/*.log*": true,
    "**/dist": true,
    "**/coverage": true
  },
  "files.associations": {
    ".markdownlintrc": "jsonc"
  },

  // ===
  // Event Triggers
  // ===

  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.fixAll.stylelint": "explicit",
    "source.fixAll.markdownlint": "explicit"
  },
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "vue",
    "vue-html",
    "html"
  ],
  "vetur.format.enable": false,
  "vetur.completion.scaffoldSnippetSources": {
    "user": "🗒️",
    "workspace": "💼",
    "vetur": ""
  },
  "prettier.disableLanguages": [],

  // ===
  // HTML
  // ===

  "html.format.enable": false,
  "vetur.validation.template": false,
  "emmet.triggerExpansionOnTab": true,
  "emmet.includeLanguages": {
    "vue-html": "html"
  },
  "vetur.completion.tagCasing": "initial",

  // ===
  // JS(ON)
  // ===

  "jest.autoEnable": false,
  "jest.enableCodeLens": false,
  "javascript.format.enable": false,
  "json.format.enable": false,
  "vetur.validation.script": false,

  // ===
  // CSS
  // ===

  "stylelint.enable": true,
  "css.validate": false,
  "scss.validate": false,
  "vetur.validation.style": false,

  // ===
  // MARKDOWN
  // ===

  "[markdown]": {
    "editor.wordWrap": "wordWrapColumn",
    "editor.wordWrapColumn": 80
  },
  // "[vue]": {
  //   // "editor.defaultFormatter": "johnsoncodehk.volar"
  // },
  "vue3snippets.enable-compile-vue-file-on-did-save-code": false
}
