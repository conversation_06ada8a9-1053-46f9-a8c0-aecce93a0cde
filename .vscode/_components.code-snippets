{"BaseButton": {"scope": "vue-html", "prefix": "BaseButton", "body": ["<BaseButton>", "\t${3}", "</BaseButton>"], "description": "<BaseButton>"}, "BaseIcon": {"scope": "vue-html", "prefix": "BaseIcon", "body": ["<BaseIcon name=\"${1}\">", "\t${2}", "</BaseIcon>"], "description": "<BaseIcon>"}, "BaseInputText": {"scope": "vue-html", "prefix": "BaseInputText", "body": ["<BaseInputText ${1}/>"], "description": "<BaseInputText>"}, "BaseLink": {"scope": "vue-html", "prefix": "BaseLink", "body": ["<BaseLink ${1|name,:to,href|}=\"${2:route}\">", "\t${3}", "</BaseLink>"], "description": "<BaseLink>"}}