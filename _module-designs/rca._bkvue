<template>
  <div class="min-h-0 flex flex-col flex-1">
    <FlotoContentLoader :loading="loading">
      <div class="flex flex-col min-h-0 flex-1 w-full mt-3">
        <FlotoScrollView>
          <MRow :gutter="0">
            <MCol :size="3" class="flex items-center">
              <MIcon
                name="application"
                size="2x"
                class="icon-style text-primary"
              />
              <div class="flex-1 flex flex-col justify-start ml-4">
                <div class="text-base text-neutral">
                  Affected Applications
                </div>
                <h3 class="text-primary">{{ rca.affectedApps }}</h3>
              </div>
            </MCol>
            <MCol :size="3" class="flex items-center">
              <MIcon name="service" size="2x" class="icon-style text-primary" />
              <div class="flex-1 flex flex-col justify-start ml-4">
                <div class="text-base text-neutral">
                  Affected Service
                </div>
                <h3 class="text-primary">{{ rca.affectedService }}</h3>
              </div>
            </MCol>
            <MCol :size="3" class="flex items-center">
              <MIcon
                name="infrastructure"
                size="2x"
                class="icon-style text-primary"
              />
              <div class="flex-1 flex flex-col justify-start ml-4">
                <div class="text-base text-neutral">
                  Affected Infrastructure
                </div>
                <h3 class="text-primary">{{ rca.affectedInfrastructure }}</h3>
              </div>
            </MCol>
            <MCol :size="3" class="flex items-center">
              <MIcon
                name="infrastructure"
                size="2x"
                class="icon-style text-primary"
              />
              <div class="flex-1 flex flex-col justify-start ml-4">
                <div class="text-base text-neutral">
                  Total Dependencies Evaluated
                </div>
                <h3 class="text-primary">{{ rca.totalDependencyEvaluated }}</h3>
              </div>
            </MCol>
          </MRow>
          <MRow :gutter="0" class="flex-1  mt-5">
            <MCol :size="6" class="pr-2">
              <MRow :gutter="0" class="flex ">
                <MCol>
                  <h5 class="text-primary-alt">Correlated Alarm</h5>
                </MCol>
              </MRow>
              <div class="health-box-panel widget-border mt-5">
                <div style="height:335px;">
                  <FlotoPaginatedCrud
                    as-table
                    default-sort="-service"
                    :columns="correlatedAlarmColumns"
                    :fetch-fn="getRCACorrelatedAlarm"
                    :paging="false"
                  >
                    <template v-slot:alert="{ item }">
                      <div class="flex items-center">
                        <Severity :severity="item.severity" class="mr-2" />
                        {{ item.alert }}
                      </div>
                    </template>
                  </FlotoPaginatedCrud>
                </div>
              </div>
            </MCol>
            <MCol :size="6" class="pr-2">
              <MRow :gutter="0" class="flex">
                <MCol>
                  <h5 class="text-primary-alt">Correlated Metric</h5>
                </MCol>
                <MCol class="text-right">
                  <MButton
                    id="correlated-metric"
                    class=""
                    outline
                    @click="onClickCorrelatedMetric"
                  >
                    Correlated Metric
                  </MButton>
                </MCol>
              </MRow>
              <MRow :gutter="0">
                <MCol>
                  <div class="flex mt-5">
                    <div class=" flex ml-3 mr-2 severity-dot-shadow square" />
                    172.16.8.23 Vmware Memory Utilization (%)
                  </div>
                </MCol>
              </MRow>
              <div class="flex-1 health-box-panel widget-border rounded mt-5">
                <Chart :options="vmwareLineChartOptions" />
              </div>
            </MCol>
          </MRow>
          <MRow :gutter="0" class="flex-1  mt-5">
            <MCol :size="6" class="pr-2">
              <MRow :gutter="0" class="flex ">
                <MCol>
                  <h5 class="text-primary-alt">Slow SQL Queries</h5>
                </MCol>
              </MRow>
              <div class="health-box-panel widget-border mt-5">
                <div style="height:335px;">
                  <FlotoPaginatedCrud
                    as-table
                    default-sort="-service"
                    :columns="sqlQueriesColumns"
                    :fetch-fn="getSlowSQLQueries"
                    :paging="false"
                  >
                    <template v-slot:time="{ item }">
                      <Progress :width="item.time" type="active" />
                      <span class="flex">{{ item.time }}%</span>
                    </template>
                  </FlotoPaginatedCrud>
                </div>
              </div>
            </MCol>
            <MCol :size="6" class="pr-2">
              <MRow :gutter="0" class="flex">
                <MCol>
                  <div class="flex mt-5">
                    <div class=" flex ml-3 mr-2 severity-dot-shadow square" />
                    ************ ESX/ESXI Memorys Utilization (%)
                  </div>
                </MCol>
              </MRow>
              <div class="flex-1 health-box-panel widget-border rounded mt-5">
                <Chart :options="esxLineChartOptions" />
              </div>
            </MCol>
          </MRow>
        </FlotoScrollView>
      </div>
    </FlotoContentLoader>
  </div>
</template>

<script>
import Chart from '@components/chart/chart.vue'
import Severity from '@components/severity.vue'
import Progress from '@components/progress.vue'
import { UserPreferenceComputed } from '@state/modules/user-preference'
import buildLineChartData from '@components/chart/build-chart-config'
// import {
//   getAlertRCAByIDApi,
//   getRCACorrelatedAlarmApi,
//   getRowDetailChartDataApi,
//   getSlowSQLQueriesApi,
// } from '../../alert-api'
export default {
  name: 'AlertRCATab',
  components: {
    Severity,
    Chart,
    Progress,
  },
  props: {
    alert: {
      type: Object,
      required: true,
    },
  },
  data() {
    this.sqlQueriesColumns = [
      {
        key: 'queryDetail',
        name: 'Query Detail',
        sortable: true,
      },
      {
        key: 'executionTime',
        name: 'Execution Time (ms)',
        sortable: true,
        width: '200px',
      },
      {
        key: 'time',
        name: 'Time',
        sortable: true,
      },
    ]
    this.correlatedAlarmColumns = [
      {
        key: 'alert',
        name: 'Alert',
        sortable: true,
      },
      {
        key: 'node',
        name: 'Node',
        sortable: true,
      },
      {
        key: 'metric',
        name: 'Metric',
        sortable: true,
      },
      {
        key: 'metricValue',
        name: 'Metric Value',
        sortable: true,
      },
    ]
    return {
      loading: true,
      height: null,
      rca: {},
      chartOptions: {},
      vmwareLineChartOptions: {},
      esxLineChartOptions: {},
    }
  },
  computed: {
    ...UserPreferenceComputed,
  },
  created() {
    this.alertId = this.$route.params.id
    this.getAlertRCAByID()
    this.getRCAVMWareLineChart()
    this.getRCAEsxLineChart()
  },
  methods: {
    async getRCAEsxLineChart() {
      // let resultData = {}
      // await getRowDetailChartDataApi().then((data) => {
      //   resultData = data.alertTrend.data
      // })
      this.esxLineChartOptions = {}
    },
    async getRCAVMWareLineChart() {
      let resultData = {}
      // await getRowDetailChartDataApi().then((data) => {
      //   resultData = data.alertTrend.data
      // })
      this.vmwareLineChartOptions = buildLineChartData(
        {
          timezone: this.timezone,
          chart: {
            height: 295,
          },
          exporting: {
            enabled: false,
          },
          title: {
            text: '',
          },
          yAxis: {
            title: {
              text: '',
            },
          },
          legend: {
            verticalAlign: 'bottom',
            x: -30,
            y: 10,
          },
          xAxis: {
            type: 'datetime',
            title: {
              text: 'Time',
            },
            dateTimeLabelFormats: {
              hour: '%H:%M',
            },
          },
        },
        resultData
      )
    },
    // getAlertRCAByID() {
    //   getAlertRCAByIDApi(this.alert.id).then((data) => {
    //     this.rca = data
    //     this.loading = false
    //   })
    // },
    // getRCACorrelatedAlarm() {
    //   return getRCACorrelatedAlarmApi()
    // },
    // onClickCorrelatedMetric() {},
    // getSlowSQLQueries() {
    //   return getSlowSQLQueriesApi()
    // },
  },
}
</script>

<style lang="less" scoped>
.icon-style {
  font-size: 22px;
}

.severity-dot-shadow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 15px;
  height: 15px;
  padding: 3px;
  border-radius: 15%;

  &.square {
    background: var(--primary-alt);
  }
}
</style>
