import Range from 'lodash/range'
import Random from 'lodash/random'
import Moment from 'moment'
import { generateId } from '@utils/id'

export const data = {
  duration: '37013200',
  http: {
    method: 'post',
    request: {
      header: {
        host: 'localhost:32843',
      },
    },
    url: 'https//localhost:3000',
  },
  language: 'dotNet',
  span: {
    kind: 'server',
  },
}

function getRendomType() {
  return ['MAJOR', 'CRITICAL', 'CLEAR'][Random(0, 2, false)]
}
export const DummyTransactionLogsData = Range(1, 100).map((i) => ({
  id: i,
  type: getRendomType(),
  date: 'Jan 16 06:18:55.126',
  service: 'example-web-service',
  host: '**********:8080',
  log: 'slow,time:1234',
}))
export const DummyTransactionHostChartData = (node) => ({
  alertTrend: {
    name: 'Response Time Trend',
    data: [
      {
        name: 'Response time - 1',
        color: '#4169e1',
        data: [
          {
            x: Moment().subtract(3, 'hours').valueOf(),
            y: Random(1, 100, false),
          },
          {
            x: Moment().subtract(3, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 100, false),
          },
          {
            x: Moment().subtract(2, 'hours').valueOf(),
            y: Random(1, 100, false),
          },
          {
            x: Moment().subtract(2, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 100, false),
          },
          {
            x: Moment().subtract(1, 'hours').valueOf(),
            y: Random(1, 100, false),
          },
          {
            x: Moment().subtract(1, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 100, false),
          },
          {
            x: Moment().add(30, 'minutes').valueOf(),
            y: Random(1, 100, false),
          },
        ],
      },
    ],
  },
})
function getRendomSpanLayer() {
  return [
    'java',
    'spring',
    'apiHandler',
    'http.application',
    'mongoDB',
    'http.request',
    'http.servicerequest',
    'http.HandlerFunc',
    'http.client',
  ][Random(0, 8, false)]
}
export const DummyTransactionOrganizationChartData = {
  id: generateId(),
  name: 'trasnaction-1',
  time: '01/03/2020 5:43',
  throughput: '11.2K/min',
  responseTime: '67.3 ms',
  errorRate: '25%',

  operations: Range(1, 2)
    .map((i) => ({
      id: 'root-operation-1',
      parentId: '',
      spanLayer: getRendomSpanLayer(),
      duration: Random(0, 500, true).toFixed(2),
      traceDuration: Random(0, 100, true).toFixed(2),
      spanCount: Random(1, 5, false),
    }))
    .concat(
      Range(1, 3).map((i) => ({
        id: `operation-child-${i}`,
        parentId: `root-operation-1`,
        spanLayer: getRendomSpanLayer(),
        duration: Random(0, 500, true).toFixed(2),
        traceDuration: Random(0, 100, true).toFixed(2),
        spanCount: Random(1, 5, false),
      }))
    )
    .concat(
      Range(1, 5).map((i) => ({
        id: `operation-child-child-${i}`,
        parentId: `operation-child-${Random(1, 2, false)}`,
        spanLayer: getRendomSpanLayer(),
        duration: Random(0, 500, true).toFixed(2),
        traceDuration: Random(0, 100, true).toFixed(2),
        spanCount: Random(1, 5, false),
      }))
    ),
}
export const DummyXRangeChartData = {
  operations: [
    {
      spanLayer: 'java',
      spanCount: 1,
      timeSlots: [
        {
          start: 30,
          end: 70,
        },
      ],
    },
    {
      spanLayer: 'spring',
      spanCount: 2,
      timeSlots: [
        {
          start: 20,
          end: 60,
        },
        {
          start: 80,
          end: 140,
        },
      ],
    },
    {
      spanLayer: 'apiHandler',
      spanCount: 2,
      timeSlots: [
        {
          start: 25,
          end: 100,
        },
        {
          start: 130,
          end: 170,
        },
        {
          start: 200,
          end: 220,
        },
      ],
    },
    {
      spanLayer: 'http.application',
      spanCount: 2,
      timeSlots: [
        {
          start: 0,
          end: 50,
        },
        {
          start: 70,
          end: 100,
        },
      ],
    },
    {
      spanLayer: 'mongoDB',
      spanCount: 2,
      timeSlots: [
        {
          start: 0,
          end: 30,
        },
        {
          start: 40,
          end: 210,
        },
      ],
    },
    {
      spanLayer: 'http.request',
      spanCount: 2,
      timeSlots: [
        {
          start: 0,
          end: 50,
        },
        {
          start: 110,
          end: 210,
        },
      ],
    },
  ],
}
