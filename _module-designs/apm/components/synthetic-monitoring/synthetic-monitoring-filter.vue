<template>
  <MCol class="filter-box">
    <MRow class="px-2">
      <MCol>
        <FlotoFormItem v-model="currentValue.application" label="Application" />
      </MCol>
      <MCol>
        <FlotoFormItem label="Availability">
          <div class="mt-spacer">
            <ValueSlider v-model="currentValue.availability" range />
          </div>
        </FlotoFormItem>
      </MCol>

      <MCol>
        <FlotoFormItem label="Check Frequency">
          <div class="mt-spacer">
            <ValueSlider v-model="currentValue.checkFrequency" range />
          </div>
        </FlotoFormItem>
      </MCol>

      <MCol>
        <FlotoFormItem label="Response Time">
          <div class="mt-spacer">
            <ValueSlider v-model="currentValue.responseTime" range />
          </div>
        </FlotoFormItem>
      </MCol>
      <MCol>
        <div class="ant-form-item-label">
          <label title="Response Time" class="">Apply to</label>
        </div>
        <div class="">
          <MCheckbox v-model="currentValue.america" class="m-5"
            >America</MCheckbox
          >
          <MCheckbox v-model="currentValue.asia" class="m-5">Asia</MCheckbox>
          <MCheckbox v-model="currentValue.europ" class="m-5">Europ</MCheckbox>
        </div>
      </MCol>
    </MRow>
    <MRow :gutter="0">
      <MCol class="mt-2 text-right pb-2 pr-3">
        <MButton class="mr-2" @click="apply">Apply</MButton>
        <MButton variant="default" @click="$emit('resetFilter', {})">
          Reset
        </MButton>
      </MCol>
    </MRow>
  </MCol>
</template>
<script>
import ValueSlider from '@components/value-slider'

export default {
  name: 'ServicesFilters',
  components: { ValueSlider },
  model: { event: 'change' },
  props: { value: { type: Object, required: true } },
  data() {
    return {
      currentValue: { ...this.value },
    }
  },
  watch: {
    value(newValue) {
      this.currentValue = { ...newValue }
    },
  },
  methods: {
    apply() {
      this.$emit('change', this.currentValue)
    },
  },
}
</script>
