<template>
  <div class="flex flex-col">
    <MRow class="info-item" :gutter="0">
      <MCol :size="2">Status</MCol>
      <MCol :size="10" class="value flex items-center">
        <Severity :severity="node.status" class="mr-2" />
        <a>{{ node.status }}</a>
      </MCol>
    </MRow>
    <MRow :gutter="0">
      <MCol :size="12" class="mb-4 mt-2">
        <Chart :options="alertChart" />
      </MCol>
    </MRow>
    <MRow v-if="nodeData.alerts.length" :gutter="0">
      <MCol :size="12" class="mb-4">
        <MCollapse
          :active-key="nodeData.alerts[0].key"
          :bordered="false"
          class="flex-1 size-large left-tree-list-box no-border-right"
          :accordion="false"
        >
          <MCollapsePanel v-for="alert in nodeData.alerts" :key="alert.key">
            <template v-slot:header>
              <MCol class="list-title flex items-center">
                <Severity :severity="alert.status" class="mr-2" />
                {{ alert.name }}
              </MCol>
            </template>
            <MetricInfoBox :info="alert" />
          </MCollapsePanel>
        </MCollapse>
      </MCol>
    </MRow>
  </div>
</template>

<script>
import Chart from '@components/chart/chart.vue'
// import ColumnAdapter from '@components/chart/column-adapter'
import Severity from '@components/severity'
import MetricInfoBox from './metric-info-box'

export default {
  name: 'NodeAlertTab',
  components: { Severity, MetricInfoBox, Chart },
  props: {
    node: { type: Object, required: true },
    nodeData: { type: Object, required: true },
  },
  computed: {
    alertChart() {
      return {}
      // return ColumnAdapter(
      //   {
      //     title: {
      //       text: 'Alert Trend',
      //     },
      //     yAxis: {
      //       title: {
      //         text: '',
      //       },
      //     },
      //     xAxis: {
      //       type: 'datetime',
      //       title: {
      //         text: 'Time',
      //       },
      //       dateTimeLabelFormats: {
      //         hour: '%H:%M',
      //       },
      //     },
      //   },
      //   this.nodeData.alertTrend.data || []
      // )
    },
  },
}
</script>

<style lang="less" scoped>
.info-item {
  color: var(--neutral-regular);

  @apply py-2;

  .value {
    color: var(--page-text-color);
  }

  border-bottom: 1px solid var(--border-color);
}
</style>
