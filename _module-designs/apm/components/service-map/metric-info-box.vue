<template>
  <div class="flex flex-col bg-neutral-lightest px-4 my-2 rounded">
    <MRow :gutter="0" class="info-item">
      <MCol :size="4">Metric:</MCol>
      <MCol :size="8" class="value">{{ info.metric }}</MCol>
    </MRow>
    <MRow :gutter="0" class="info-item">
      <MCol :size="4">Value:</MCol>
      <MCol :size="8" class="value">{{ info.value }}</MCol>
    </MRow>
    <MRow :gutter="0" class="info-item">
      <MCol :size="4">First Seen:</MCol>
      <MCol :size="8" class="value">{{ info.firstSeen }}</MCol>
    </MRow>
    <MRow :gutter="0" class="info-item no-border">
      <MCol :size="4">Policy Name:</MCol>
      <MCol :size="8" class="value">
        <a>{{ info.policy }}</a>
      </MCol>
    </MRow>
  </div>
</template>

<script>
export default {
  name: 'MetricInfoBox',
  props: { info: { type: Object, required: true } },
}
</script>

<style lang="less" scoped>
.info-item {
  color: var(--neutral-regular);

  @apply py-2;

  .value {
    color: var(--page-text-color);
  }

  border-bottom: 1px solid var(--border-color);

  &.no-border {
    border-bottom: 0;
  }
}
</style>
