<template>
  <div class="flex flex-col">
    <MRow class="info-item" :gutter="0">
      <MCol :size="3">Node:</MCol>
      <MCol :size="9" class="value">
        <a>{{ nodeData.name }}</a>
      </MCol>
    </MRow>
    <MRow class="info-item" :gutter="0">
      <MCol :size="3">IP:</MCol>
      <MCol :size="9" class="value">{{ nodeData.ip }}</MCol>
    </MRow>
    <MRow class="info-item" :gutter="0">
      <MCol :size="3">OS:</MCol>
      <MCol :size="9" class="value">{{ nodeData.os }}</MCol>
    </MRow>
    <MRow class="info-item" :gutter="0">
      <MCol :size="3">ESX/ESXi:</MCol>
      <MCol :size="9" class="value">
        <a>{{ nodeData.esx }}</a>
      </MCol>
    </MRow>
    <MRow :gutter="0" class="my-2">
      <MCol :size="12" style="height: 350px" class="mb-4">
        <Chart :options="memoryChart" />
      </MCol>
    </MRow>
    <MRow :gutter="0" class="my-2">
      <MCol :size="12" style="height: 350px">
        <Chart :options="cpuChart" />
      </MCol>
    </MRow>
  </div>
</template>

<script>
import Chart from '@components/chart/chart.vue'
import LineAdapter from '@/src/components/chart/build-chart-config'

export default {
  name: 'NodeInfoTab',
  components: { Chart },
  props: {
    node: { type: Object, required: true },
    nodeData: { type: Object, required: true },
  },
  data() {
    this.memoryChart = LineAdapter(
      {
        title: {
          text: `${this.nodeData.ip} Memory Utilization (%)`,
        },
        xAxis: {
          title: {
            text: 'Time',
          },
          dateTimeLabelFormats: {
            hour: '%H:%M',
          },
        },
        yAxis: {
          title: {
            text: '',
          },
        },
        tooltip: {
          pointFormat:
            '<span style="color:{point.color}">●</span> {series.name}: <b>{point.y}%</b><br/>',
        },
        legend: {
          enabled: false,
        },
      },
      [this.nodeData.memoryUtilization]
    )
    this.cpuChart = LineAdapter(
      {
        title: {
          text: `${this.nodeData.ip} CPU Utilization (%)`,
        },
        xAxis: {
          title: {
            text: 'Time',
          },
          dateTimeLabelFormats: {
            hour: '%H:%M',
          },
        },
        yAxis: {
          title: {
            text: '',
          },
        },
        tooltip: {
          pointFormat:
            '<span style="color:{point.color}">●</span> {series.name}: <b>{point.y}%</b><br/>',
        },
        legend: {
          enabled: false,
        },
      },
      [this.nodeData.cpuUtilization]
    )
    return {}
  },
}
</script>

<style lang="less" scoped>
.info-item {
  color: var(--neutral-regular);

  @apply py-2;

  .value {
    color: var(--page-text-color);
  }

  border-bottom: 1px solid var(--border-color);
}
</style>
