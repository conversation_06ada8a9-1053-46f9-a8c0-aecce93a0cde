// import Mem from 'mem'

// const makeSvg = Mem((ele) => {
//   // const type = ele.data().type
//   const size = 30 // may need to calculate this yourself
//   const width = size
//   const height = size
//   const svg = `<svg data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" height="${height}" width="${width}"><path d="M256 326.48a11.88 11.88 0 01-11.88-11.88V212.36a11.88 11.88 0 0123.76 0V314.6A11.88 11.88 0 01256 326.48z" fill="#e4e4e4"/><path d="M267.88 314.6V212.36A11.88 11.88 0 00256 200.48v126a11.88 11.88 0 0011.88-11.88z" fill="#cfcfcf"/><path d="M422 432.8H90a34 34 0 01-34-34v-62.12a34 34 0 0134-34h332a34 34 0 0134 34v62.16a34 34 0 01-34 33.96z" fill="#3b4958"/><path d="M422 302.72H256V432.8h166a34 34 0 0034-34v-62.12a34 34 0 00-34-33.96z" fill="#17202a"/><path d="M306.3 379.64h-184a11.88 11.88 0 110-23.76h184a11.88 11.88 0 010 23.76z" fill="#ff7045"/><path d="M306.3 355.88H256v23.76h50.3a11.88 11.88 0 000-23.76z" fill="#f64c1c"/><path d="M256 224.24a44.59 44.59 0 1131.53-13.06A44.34 44.34 0 01256 224.24z" fill="#ff7045"/><path d="M256 135.05v89.19a44.6 44.6 0 000-89.19z" fill="#f64c1c"/><path d="M350.47 379.64c6.21 0 12.16-5.46 11.88-11.88s-5.22-11.88-11.88-11.88c-6.22 0-12.17 5.47-11.88 11.88s5.22 11.88 11.88 11.88zm39.26 0c6.21 0 12.17-5.46 11.88-11.88s-5.22-11.88-11.88-11.88c-6.22 0-12.17 5.47-11.88 11.88s5.22 11.88 11.88 11.88z" fill="#cfcfcf"/><path d="M198.92 248.6a11.84 11.84 0 01-8.4-3.48 92.71 92.71 0 010-130.95 11.88 11.88 0 1116.8 16.8 68.91 68.91 0 000 97.35 11.88 11.88 0 01-8.4 20.28z" fill="#00c8c8"/><path d="M167.43 280.09a11.84 11.84 0 01-8.4-3.48c-53.47-53.47-53.47-140.47 0-193.93a11.88 11.88 0 0116.8 16.8 113.5 113.5 0 000 160.33 11.88 11.88 0 01-8.4 20.28z" fill="#00c8c8"/><path d="M313.07 248.6a11.88 11.88 0 01-8.39-20.28 68.93 68.93 0 000-97.36 11.88 11.88 0 0116.8-16.79 92.71 92.71 0 010 131 11.88 11.88 0 01-8.41 3.43z" fill="#00a0aa"/><path d="M344.57 280.09a11.88 11.88 0 01-8.41-20.28 113.36 113.36 0 000-160.33A11.88 11.88 0 01353 82.68c53.47 53.46 53.47 140.46 0 193.93a11.84 11.84 0 01-8.43 3.48z" fill="#00a0aa"/></svg>`
//   return {
//     svg: 'data:image/svg+xml;base64,' + btoa(svg),
//     width,
//     height,
//   }
// })

export default [
  {
    selector: 'node',
    shape: 'circle',
    style: {
      'background-color': 'white',
      // 'background-image': function(ele) {
      //   return makeSvg(ele).svg
      // },
      // width: function(ele) {
      //   return makeSvg(ele).width + 15
      // },
      // height: function(ele) {
      //   return makeSvg(ele).height + 15
      // },
    },
  },
  {
    selector: 'edge',
    style: {
      width: 1.5,
      'line-style': 'solid',
      'curve-style': 'bezier',
      'line-color': '#364658',
      'target-arrow-color': '#364658',
      'target-arrow-shape': 'triangle',
    },
  },
]
