<template>
  <div class="flex flex-col flex-1 min-h-0">
    <MRow>
      <MCol class="flex justify-between items-start">
        <h3 class="text-primary-alt m-0">{{ node.name }}</h3>
        <MButton :shadow="false" variant="transparent" @click="$emit('close')">
          <MIcon name="times" size="lg" />
        </MButton>
      </MCol>
      <MDivider class="my-1 mx-2" />
    </MRow>
    <MRow>
      <MCol>
        <MTab v-model="tab">
          <MTabPane key="info" :tab="node.type" />
          <MTabPane key="alert" tab="Alert" />
        </MTab>
      </MCol>
    </MRow>
    <MRow class="flex-1 min-h-0" :gutter="0">
      <MCol class="h-full">
        <FlotoScrollView>
          <FlotoContentLoader :loading="loading" class="flex flex-col flex-1">
            <template v-if="!loading">
              <NodeInfoTab
                v-if="tab === 'info'"
                :node="node"
                :node-data="nodeData"
              />
              <NodeAlertTab v-else :node="node" :node-data="nodeData" />
            </template>
          </FlotoContentLoader>
        </FlotoScrollView>
      </MCol>
    </MRow>
  </div>
</template>

<script>
import NodeInfoTab from './node-info-tab'
import NodeAlertTab from './node-alert-tab'
import { getNodeInformationApi } from '../../service-map-api'

export default {
  name: 'NodeViewer',
  components: { NodeInfoTab, NodeAlertTab },
  props: {
    node: { type: Object, required: true },
  },
  data() {
    return {
      tab: 'info',
      loading: true,
      nodeData: {},
    }
  },
  created() {
    this.getNodeInfo()
  },
  methods: {
    getNodeInfo() {
      getNodeInformationApi(this.node).then((data) => {
        this.nodeData = Object.freeze(data)
        this.loading = false
      })
    },
  },
}
</script>
