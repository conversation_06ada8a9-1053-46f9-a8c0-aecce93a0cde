<template>
  <FlotoDropdownPicker
    :value="value"
    :options="options"
    @change="$emit('change', $event)"
  >
    <template v-slot:trigger="{ toggle }">
      <MButton
        variant="transparent"
        :shadow="false"
        class="mr-2"
        @click="toggle"
      >
        <MIcon name="calendar-alt" class="mr-2" />
        {{ selectedText }}
        <MIcon name="chevron-down" class="ml-1" />
      </MButton>
    </template>
  </FlotoDropdownPicker>
</template>

<script>
export default {
  name: 'TimePicker',
  model: { event: 'change' },
  props: { value: { type: [String, Number], default: undefined } },
  data() {
    this.options = [
      { key: 'last_1_Hour', text: 'Last 1 Hour' },
      { key: 'last_6_Hours', text: 'Last 6 Hours' },
      { key: 'last_12_Hours', text: 'Last 12 Hours' },
      { key: 'last_24_Hours', text: 'Last 24 Hours' },
      { key: 'last_48_Hours', text: 'Last 48 Hours' },
      { key: 'last_Week', text: 'Last Week' },
      { key: 'last_30_Days', text: 'Last 30 Days' },
      { key: 'custom', text: 'Custom' },
    ]
    return {}
  },
  computed: {
    listeners() {
      const { change, ...listeners } = this.$listeners
      return listeners
    },
    selectedText() {
      const currentValue = this.value
      const item = this.options.find((i) => i.key === currentValue)
      if (item) {
        return item.text
      }
      return ''
    },
  },
}
</script>
