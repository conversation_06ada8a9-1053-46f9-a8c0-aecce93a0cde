<template>
  <MCol class="transaction-filter text-left">
    <MRow>
      <MCol :size="2">
        <FlotoFormItem label="Service">
          <FlotoDropdownPicker
            v-model="currentValue.service"
            class="w-full"
            :options="serviceTypeOptions"
            allow-clear
          />
        </FlotoFormItem>
      </MCol>
      <MCol :size="2">
        <FlotoFormItem label="HTTP Method">
          <FlotoDropdownPicker
            v-model="currentValue.httpMethod"
            class="w-full"
            allow-clear
            multiple
            :options="httpMethodOptions"
          />
        </FlotoFormItem>
      </MCol>
      <MCol :size="2">
        <FlotoFormItem label="Status Code">
          <FlotoDropdownPicker
            v-model="currentValue.statusCode"
            :options="statusCodeSelection"
            class="w-full"
            multiple
            allow-clear
          />
        </FlotoFormItem>
      </MCol>
      <MCol :size="2">
        <FlotoFormItem label="Status">
          <FlotoDropdownPicker
            v-model="currentValue.status"
            :options="statusSelection"
            class="w-full"
            multiple
            allow-clear
          />
        </FlotoFormItem>
      </MCol>
      <MCol :size="2">
        <FlotoFormItem label="Type">
          <FlotoDropdownPicker
            v-model="currentValue.type"
            :options="statusTypeSelection"
            class="w-full"
            multiple
            allow-clear
          />
        </FlotoFormItem>
      </MCol>
      <MCol :size="2">
        <FlotoFormItem label="Host">
          <FlotoDropdownPicker
            v-model="currentValue.host"
            :options="hostSelection"
            class="w-full"
            allow-clear
          />
        </FlotoFormItem>
      </MCol>
    </MRow>
    <MRow>
      <MCol :size="2">
        <FlotoFormItem label="Duration">
          <ValueSlider v-model="currentValue.duration" range />
        </FlotoFormItem>
      </MCol>
    </MRow>
    <MRow>
      <MCol class="mt-2 text-right">
        <MButton class="mr-2" @click="apply">Apply</MButton>
        <MButton variant="default" @click="resetFilter"> Reset </MButton>
      </MCol>
    </MRow>
  </MCol>
</template>
<script>
import ValueSlider from '@components/value-slider'
import CloneDeep from 'lodash/cloneDeep'

const httpMethodOptions = [
  { key: 'GET', text: 'GET' },
  { key: 'POST', text: 'POST' },
  { key: 'PUT', text: 'PUT' },
  { key: 'DELETE', text: 'DELETE' },
]
const statusSelection = [
  { key: 'Error', text: 'Error' },
  { key: 'Ok', text: 'Ok' },
]
export default {
  name: 'TransactionsFilter',
  components: { ValueSlider },
  model: { event: 'change' },
  props: {
    value: { type: Object, required: true },
    serviceTypeOptions: { type: Array, required: true },
    statusCodeSelection: { type: Array, required: true },
    statusTypeSelection: { type: Array, required: true },
    hostSelection: { type: Array, required: true },
  },
  data() {
    this.httpMethodOptions = CloneDeep(httpMethodOptions)
    this.statusSelection = CloneDeep(statusSelection)
    return {
      currentValue: { ...this.value },
    }
  },
  watch: {
    value(newValue) {
      this.currentValue = { ...newValue }
    },
  },
  methods: {
    apply() {
      this.$emit('change', this.currentValue)
      this.$emit('apply')
    },
    resetFilter() {
      this.$emit('change', {
        service: undefined,
        httpMethod: [],
        status: [],
        statusCode: [],
        type: [],
        host: undefined,
        duration: [0, 100],
      })
      this.$emit('apply')
    },
  },
}
</script>
<style lang="less" scoped>
.transaction-filter {
  @apply shadow-md py-2 px-1;

  border: 1px solid @neutral-lighter;
  border-radius: 3px;
}
</style>
