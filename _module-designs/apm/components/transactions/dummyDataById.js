import Random from 'lodash/random'
import Range from 'lodash/range'

// Dummy data for host field for transactions tab in Services
function dummyDataHost() {
  return [
    '*************',
    '***********',
    '*************',
    '************',
    '*************',
  ][Random(0, 4, false)]
}

// Dummy data for types field for transactions tab in Services
function dummyDataTypes() {
  return ['App', 'SQL', 'Web'][Random(0, 2, false)]
}
export const transactionsFieldByIdDummyData = Range(1, 1000).map((i) => ({
  id: i,
  host: dummyDataHost(),
  type: dummyDataTypes(),
}))
