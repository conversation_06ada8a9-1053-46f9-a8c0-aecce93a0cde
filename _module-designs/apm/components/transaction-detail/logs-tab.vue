<template>
  <MRow>
    <MCol>
      <FlotoPaginatedCrud
        as-table
        default-sort="-type"
        :columns="columns"
        :fetch-fn="getTransactionLogs"
      >
        <template v-slot:type="{ item }">
          <div class="flex">
            <Severity :severity="item.type" class="mr-2" />
            <span> {{ item.type }} </span>
          </div>
        </template>
        <template v-slot:host="{ item }">
          <div class="flex">
            <a> {{ item.host }} </a>
          </div>
        </template>
      </FlotoPaginatedCrud>
    </MCol></MRow
  >
</template>
<script>
import { getTransactionLogsApi } from './../../transaction-api'
import Severity from '@components/severity'

export default {
  name: 'LogsTab',
  components: {
    Severity,
  },
  data() {
    this.columns = [
      {
        key: 'type',
        name: 'Type',
      },
      {
        key: 'date',
        name: 'Date',
      },
      {
        key: 'service',
        name: 'Service',
      },
      {
        key: 'host',
        name: 'Host',
      },
      {
        key: 'log',
        name: 'Log',
      },
    ]
    return {}
  },
  created() {
    this.getTransactionLogs()
  },
  methods: {
    getTransactionLogs() {
      return getTransactionLogsApi()
    },
  },
}
</script>
