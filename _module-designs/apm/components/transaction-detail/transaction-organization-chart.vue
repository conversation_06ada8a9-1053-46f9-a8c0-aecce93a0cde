<template>
  <div> <Chart :options="chartOptions"> </Chart> </div>
</template>
<script>
import Chart from '@components/chart/chart.vue'
import { getTransactionOperationDetailApi } from './../../transaction-api'
// import buildOrganizationChartData from './../../../../components/chart/organization-adapter'

export default {
  name: 'TransactionOrganizationChart',
  components: {
    Chart,
  },
  data() {
    return {
      chartOptions: {},
    }
  },
  created() {
    this.getTransactionHostChart()
  },
  methods: {
    async getTransactionHostChart() {
      let resultData = {}
      // let chartData = []
      let chartNodes = []
      resultData = await getTransactionOperationDetailApi()
      resultData.operations.map((item) => {
        chartNodes.push({
          id: item.id,
          title: null,
          name: item.spanLayer,
          info: item.spanLayer,
        })
      })

      // chartData = this.createDataForChart(resultData.operations)
      /* this.chartOptions = buildOrganizationChartData(
        {
          series: [
            {
              type: 'organization',
              name: 'Highsoft',
              keys: ['from', 'to'],
              data: chartData,
              nodes: chartNodes,
              levels: [
                {
                  level: 0,
                  color: '#DEDDCF',
                  borderLine: 10,
                  dataLabels: {
                    color: 'black',
                  },
                },
                {
                  level: 1,
                  color: '#DEDDCF',
                  dataLabels: {
                    color: 'black',
                  },
                  height: 25,
                },
                {
                  level: 2,
                  color: '#DEDDCF',
                  dataLabels: {
                    color: 'black',
                  },
                },
              ],

              colorByPoint: false,
              borderColor: 'white',
            },
          ],
        },
        resultData.data
      ) */
    },
    createDataForChart(operations) {
      let parents = []
      let data = []

      operations.map((operation, index) => {
        if (operation.parentId === '') {
          parents.push(operation.id)
        } else if (parents.indexOf(operation.parentId) === -1) {
          parents.push(operation.parentId)
        }
      })

      parents.map((parent, index) => {
        operations.map((operation, index) => {
          if (operation.parentId === parent) {
            data.push([parent, operation.id])
          }
        })
      })
      return data
    },
  },
}
</script>
