<template>
  <MRow>
    <FlotoContentLoader :loading="loading">
      <MCol :size="12"
        ><TransactionHostChart :options="chartOptions" :title="title" /></MCol
    ></FlotoContentLoader>
    <FlotoContentLoader :loading="loading">
      <MCol :size="12"
        ><TransactionHostChart :options="chartOptions" :title="title"
      /></MCol>
    </FlotoContentLoader>
  </MRow>
</template>
<script>
import TransactionHostChart from './transaction-host-tab-chart'
import { getTransactionHostChartApi } from './../../transaction-api'
import buildLineChartData from '../../../../components/chart/build-chart-config'

export default {
  name: 'HostTab',
  components: {
    TransactionHostChart,
  },
  data() {
    return {
      chartOptions: {},
      loading: true,
      title: 'Xyz',
    }
  },
  created() {
    this.getTransactionHostChart()
  },
  methods: {
    async getTransactionHostChart() {
      let resultData = {}
      await getTransactionHostChartApi().then((data) => {
        resultData = data.alertTrend
        this.loading = false
      })
      this.chartOptions = buildLineChartData(
        {
          chart: {
            height: 330,
          },
          title: {
            text: '',
          },
          yAxis: {
            title: {
              text: '',
            },
          },
          legend: {
            verticalAlign: 'bottom',
            x: -30,
            y: 10,
          },
          xAxis: {
            type: 'datetime',
            title: {
              text: 'Time',
            },
            dateTimeLabelFormats: {
              hour: '%H:%M',
            },
          },
        },
        resultData.data
      )
    },
  },
}
</script>
