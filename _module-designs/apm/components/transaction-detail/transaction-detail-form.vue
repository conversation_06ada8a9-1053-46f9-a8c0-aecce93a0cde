<template>
  <FlotoFixedView :gutter="0">
    <MTab v-model="activeTab">
      <MTabPane v-for="tab in tabs" :key="tab.key" :tab="tab.name" />
    </MTab>
    <MRow :gutter="0" class="flex-1 min-h-0">
      <MCol :size="12" class="h-100">
        <FlotoScrollView>
          <DetailTab v-if="activeTab === 'detail'" />
          <HostTab v-else-if="activeTab === 'host'" />
          <LogsTab v-if="activeTab === 'logs'" />
        </FlotoScrollView>
      </MCol> </MRow
  ></FlotoFixedView>
</template>
<script>
import HostTab from './host-tab'
import DetailTab from './detail-tab'
import LogsTab from './logs-tab'
export default {
  name: 'TransactionDetailForm',
  components: {
    HostTab,
    DetailTab,
    LogsTab,
  },
  data() {
    this.tabs = [
      {
        key: 'detail',
        name: 'Detail',
      },
      {
        key: 'host',
        name: 'Host',
      },
      {
        key: 'logs',
        name: 'Logs',
      },
    ]
    return {
      activeTab: 'detail',
    }
  },
}
</script>
