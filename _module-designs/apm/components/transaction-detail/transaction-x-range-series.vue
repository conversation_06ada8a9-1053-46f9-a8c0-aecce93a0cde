<template>
  <div> <Chart :options="chartOptions"> </Chart> </div>
</template>
<script>
import Chart from '@components/chart/chart.vue'
import { getTransactionXRangeChartApi } from './../../transaction-api'
// import buildXRangeChartData from './../../../../components/chart/x-range-adapter'

export default {
  name: 'TransitionXRangeSeries',
  components: {
    Chart,
  },
  data() {
    return {
      chartOptions: {},
    }
  },
  created() {
    this.getTransactionXRangeChart()
  },
  methods: {
    async getTransactionXRangeChart() {
      let resultData = {}
      let chartData = []
      resultData = await getTransactionXRangeChartApi()
      resultData.operations.map((item, index) => {
        item.timeSlots.map((item) => {
          chartData.push({
            x: item.start,
            x2: item.end,
            name: item.spanLayer,
            y: index,
          })
        })
      })

      // this.chartOptions = buildXRangeChartData({
      //   series: [
      //     {
      //       type: 'xrange',
      //       // name: 'Highcharts X-range',
      //       borderColor: 'gray',
      //       pointWidth: 20,
      //       data: chartData,

      //       dataLabels: {
      //         enabled: true,
      //       },
      //     },
      //   ],
      //   xAxis: {
      //     allowDecimals: false,
      //     title: {
      //       text: '',
      //     },
      //     min: 0,
      //     labels: {
      //       formatter: function() {
      //         return this.value + 'ms'
      //       },
      //     },
      //   },

      //   yAxis: {
      //     allowDecimals: false,
      //     title: {
      //       text: '',
      //     },
      //     visible: false,
      //     reversed: true,
      //   },
      // })
    },
  },
}
</script>
