<template>
  <FlotoDrawer open @hide="$emit('hide', $event)">
    <MRow>
      <MCol :size="4" class="small-label"> Duration</MCol>
      <MCol :size="4" class="small-label"> % of Trace Duration</MCol>
      <MCol :size="4" class="small-label"> Span Count</MCol>
    </MRow>
    <MRow>
      <MCol :size="4" class="label-content"> {{ operation.duration }}</MCol>
      <MCol :size="4" class="label-content">
        {{ operation.traceDuration }}
      </MCol>
      <MCol :size="4" class="label-content"> {{ operation.spanCount }}</MCol>
    </MRow>
    <template v-slot:title>
      {{ operation.spanLayer }}
    </template>
    <TransactionDetailForm />
  </FlotoDrawer>
</template>
<script>
import TransactionDetailForm from './transaction-detail-form'
export default {
  name: 'TransactionDetailDrawer',
  components: {
    TransactionDetailForm,
  },
  props: {
    operation: { type: Object, default: undefined },
  },
}
</script>
