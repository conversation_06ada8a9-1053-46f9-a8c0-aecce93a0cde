<template
  ><MRow>
    <MCol>
      <pre>{{ detail }}</pre>
    </MCol>
  </MRow>
</template>
<script>
import { getTransactionDetailApi } from './../../transaction-api'
export default {
  name: 'DetailTab',
  data() {
    return {
      detail: {},
    }
  },
  created() {
    this.fetchTransactionDetail()
  },
  methods: {
    fetchTransactionDetail() {
      getTransactionDetailApi().then((data) => {
        this.detail = JSON.stringify(data, undefined, 4)
      })
    },
  },
}
</script>
