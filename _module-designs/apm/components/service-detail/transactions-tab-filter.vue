<template>
  <div>
    <MRow class="filter-box">
      <MCol>
        <FlotoFormItem label="Throughput">
          <ValueSlider v-model="currentValue.throughput" range />
        </FlotoFormItem>
      </MCol>
      <MCol>
        <FlotoFormItem label="Response Time">
          <ValueSlider v-model="currentValue.responseTime" range />
        </FlotoFormItem>
      </MCol>
      <MCol>
        <FlotoFormItem label="Error Rate">
          <ValueSlider v-model="currentValue.errorRate" range />
        </FlotoFormItem>
      </MCol>
    </MRow>
    <MRow>
      <MCol class="mt-2 text-right">
        <MButton class="mr-2" @click="apply">Apply</MButton>
        <MButton variant="default" @click="resetFilter"> Reset </MButton>
      </MCol>
    </MRow>
  </div>
</template>
<script>
import ValueSlider from '@components/value-slider'

export default {
  name: 'ServicesTabTransactionFilters',
  components: { ValueSlider },
  model: { event: 'change' },
  props: { value: { type: Object, required: true } },
  data() {
    return {
      currentValue: { ...this.value },
    }
  },
  watch: {
    value(newValue) {
      this.currentValue = { ...newValue }
    },
  },
  methods: {
    apply() {
      this.$emit('change', this.currentValue)
      this.$emit('apply')
    },
    resetFilter() {
      this.$emit('change', {
        throughput: [0, 100],
        responseTime: [0, 100],
        errorRate: [0, 100],
      })
      this.$emit('apply')
    },
  },
}
</script>

<style lang="less" scoped>
.filter-box {
  @apply shadow-md p-2;

  border: 1px solid @neutral-lighter;
  border-radius: 3px;
}
</style>
