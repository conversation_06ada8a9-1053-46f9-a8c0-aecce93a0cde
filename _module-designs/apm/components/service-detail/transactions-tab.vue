<template>
  <div>
    <FlotoPaginatedCrud
      as-table
      default-sort="-type"
      :columns="columns"
      :fetch-fn="getServicesTabTransaction"
    >
      <template v-slot:throughput="{ item }">
        {{ item.throughput }} k/sec
      </template>
      <template v-slot:responseTime="{ item }">
        {{ item.responseTime }} ms
      </template>
      <template v-slot:errorRate="{ item }"> {{ item.errorRate }}% </template>
      <template v-slot:time="{ item }">
        <template v-if="item.time">
          {{ item.time | datetime }}
        </template>
      </template>
      <template v-slot:transaction="{ item }">
        <a @click="handleClick(item)"> {{ item.transaction }} </a>
      </template>
    </FlotoPaginatedCrud>
  </div>
</template>

<script>
import { getServicesTabTransactionApi } from '../../apm-api'
export default {
  name: 'TransactionsTab',
  props: {
    service: { type: Object, required: true },
  },
  data() {
    return {
      timeRange: 'last_1_Hour',
      columns: [
        {
          key: 'transaction',
          name: 'Transaction',
          searchable: true,
        },
        { key: 'time', name: 'Time', sortable: true },
        { key: 'throughput', name: 'Throughput' },
        {
          key: 'responseTime',
          name: 'Response Time',
        },
        { key: 'errorRate', name: 'Error Rate' },
      ],
    }
  },
  watch: {
    timeRange(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.getServicesTabTransaction()
      }
    },
  },
  created() {
    this.getServicesTabTransaction()
  },

  methods: {
    getServicesTabTransaction() {
      return getServicesTabTransactionApi(this.timeRange)
    },
    handleClick(item) {
      this.$router.push(
        this.$currentModule.getRoute('transaction-detail', {
          params: { id: item.id, previousRoute: this.$route },
        })
      )
    },
  },
}
</script>
