<template>
  <div>
    <FlotoPaginatedCrud
      as-table
      default-sort="-query"
      :columns="columns"
      :fetch-fn="getServicesTabQueries"
    >
      <template v-slot:queryDuration="{ item }">
        {{ item.queryDuration }} sec
      </template>
      <template v-slot:time="{ item }">
        <template v-if="item.time">
          {{ item.time | datetime }}
        </template>
      </template>
      <template v-slot:transaction="{ item }">
        <a @click="handleClick(item)">
          {{ transactionFieldDataMap[item.transaction] }}
        </a>
      </template>
      <template v-slot:database="{ item }">
        {{ databasesFieldDataMap[item.database] }}
      </template>
      <template v-slot:queryTable="{ item }">
        {{ queryTableFieldDataMap[item.queryTable] }}
      </template>
    </FlotoPaginatedCrud>
  </div>
</template>

<script>
import {
  getServicesTabQueriesApi,
  getServicesTabQueriesFieldsDataApi,
} from '../../apm-api'
export default {
  name: 'QueryTab',
  props: {
    service: { type: Object, required: true },
  },
  data() {
    this.transactionFieldDataMap = {}
    this.databasesFieldDataMap = {}
    this.queryTableFieldDataMap = {}
    return {
      timeRange: 'last_1_Hour',
      transactionFieldSelection: [],
      databasesFieldSelection: [],
      queryTableFieldSelection: [],
      columns: [
        {
          key: 'query',
          name: 'Query',
          searchable: true,
        },
        { key: 'time', name: 'Time', sortable: true },
        { key: 'transaction', name: 'Transaction' },
        {
          key: 'database',
          name: 'Database',
        },
        { key: 'queryTable', name: 'Query Table' },
        { key: 'queryDuration', name: 'Query Duration' },
      ],
    }
  },
  watch: {
    timeRange(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.getServicesTabQueries()
      }
    },
  },
  created() {
    this.fetchAllTransactions()
    this.getServicesTabQueries()
  },

  methods: {
    getServicesTabQueries() {
      return getServicesTabQueriesApi(this.timeRange)
    },

    fetchAllTransactions() {
      return getServicesTabQueriesFieldsDataApi().then((data) => {
        // To get the transaction field data
        const transactionFieldData = []
        data.forEach(({ transaction, id }) => {
          transactionFieldData.push({ key: id, text: transaction })
          this.transactionFieldDataMap[id] = transaction
        })
        this.transactionFieldSelection = transactionFieldData
        // To get the database field data
        const databasesFieldData = []
        data.forEach(({ database, id }) => {
          databasesFieldData.push({ key: id, text: database })
          this.databasesFieldDataMap[id] = database
        })
        this.databasesFieldSelection = databasesFieldData
        // To get the query field data
        const queryTableFieldData = []
        data.forEach(({ queryTable, id }) => {
          queryTableFieldData.push({ key: id, text: queryTable })
          this.queryTableFieldDataMap[id] = queryTable
        })
        this.queryTableFieldSelection = queryTableFieldData
      })
    },
    handleClick(item) {
      alert(item.transaction)
    },
  },
}
</script>
