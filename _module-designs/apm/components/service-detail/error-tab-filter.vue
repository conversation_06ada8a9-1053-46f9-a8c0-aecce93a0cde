<template>
  <MCol class="filter-box">
    <MRow>
      <MCol>
        <FlotoFormItem label="Transaction" help="Select one or more...">
          <FlotoDropdownPicker
            v-model="currentValue.transaction"
            :options="errorTransactionDataSelection"
            class="w-full"
            multiple
            allow-clear
          />
        </FlotoFormItem>
      </MCol>
      <MCol>
        <FlotoFormItem label="Method" help="Select one or more...">
          <FlotoDropdownPicker
            v-model="currentValue.method"
            :options="methodOptions"
            class="w-full"
            multiple
            allow-clear
          />
        </FlotoFormItem>
      </MCol>
      <MCol>
        <FlotoFormItem label="Status" help="Select one or more...">
          <FlotoDropdownPicker
            v-model="currentValue.status"
            :options="errorStatusDataSelection"
            class="w-full"
            multiple
            allow-clear
          />
        </FlotoFormItem>
      </MCol>
    </MRow>
    <MRow>
      <MCol :size="2">
        <FlotoFormItem label="Response Time">
          <ValueSlider v-model="currentValue.responseTime" range />
        </FlotoFormItem>
      </MCol>
    </MRow>
    <MRow>
      <MCol class="mt-2 text-right">
        <MButton class="mr-2" @click="apply">Apply</MButton>
        <MButton variant="default" @click="resetFilter"> Reset </MButton>
      </MCol>
    </MRow>
  </MCol>
</template>
<script>
import ValueSlider from '@components/value-slider'
import CloneDeep from 'lodash/cloneDeep'

const methodOptions = [
  { key: 'GET', text: 'GET' },
  { key: 'POST', text: 'POST' },
  { key: 'PUT', text: 'PUT' },
  { key: 'DELETE', text: 'DELETE' },
]
export default {
  name: 'ServicesTabErrorsFilters',
  components: { ValueSlider },
  model: { event: 'change' },
  props: {
    value: { type: Object, required: true },
    errorTransactionDataSelection: { type: Array, required: true },
    errorStatusDataSelection: { type: Array, required: true },
  },
  data() {
    this.methodOptions = CloneDeep(methodOptions)
    return {
      currentValue: { ...this.value },
    }
  },
  watch: {
    value(newValue) {
      this.currentValue = { ...newValue }
    },
  },
  methods: {
    apply() {
      this.$emit('change', this.currentValue)
      this.$emit('apply')
    },
    resetFilter() {
      this.$emit('change', {
        transaction: [],
        method: [],
        status: [],
        responseTime: [0, 100],
      })
      this.$emit('apply')
    },
  },
}
</script>

<style lang="less" scoped>
.filter-box {
  @apply shadow-md p-2;

  border: 1px solid @neutral-lighter;
  border-radius: 3px;
}
</style>
