<template>
  <MCol class="filter-box">
    <MRow>
      <MCol>
        <FlotoFormItem label="Transaction" help="Select one or more...">
          <FlotoDropdownPicker
            v-model="currentValue.transaction"
            :options="transactionFieldSelection"
            class="w-full"
            multiple
            allow-clear
          />
        </FlotoFormItem>
      </MCol>
      <MCol>
        <FlotoFormItem label="Database" help="Select one or more...">
          <FlotoDropdownPicker
            v-model="currentValue.database"
            :options="databasesFieldSelection"
            class="w-full"
            multiple
            allow-clear
          />
        </FlotoFormItem>
      </MCol>
      <MCol>
        <FlotoFormItem label="Query Table" help="Select one or more...">
          <FlotoDropdownPicker
            v-model="currentValue.queryTable"
            :options="queryTableFieldSelection"
            class="w-full"
            multiple
            allow-clear
          />
        </FlotoFormItem>
      </MCol>
    </MRow>
    <MRow>
      <MCol :size="2">
        <FlotoFormItem label="Query Duration">
          <ValueSlider v-model="currentValue.queryDuration" range />
        </FlotoFormItem>
      </MCol>
    </MRow>
    <MRow>
      <MCol class="mt-2 text-right">
        <MButton class="mr-2" @click="apply">Apply</MButton>
        <MButton variant="default" @click="resetFilter"> Reset </MButton>
      </MCol>
    </MRow>
  </MCol>
</template>
<script>
import ValueSlider from '@components/value-slider'

export default {
  name: 'ServicesTabQueriesFilters',
  components: { ValueSlider },
  model: { event: 'change' },
  props: {
    value: { type: Object, required: true },
    transactionFieldSelection: { type: Array, required: true },
    databasesFieldSelection: { type: Array, required: true },
    queryTableFieldSelection: { type: Array, required: true },
  },
  data() {
    return {
      currentValue: { ...this.value },
    }
  },
  watch: {
    value(newValue) {
      this.currentValue = { ...newValue }
    },
  },
  methods: {
    apply() {
      this.$emit('change', this.currentValue)
      this.$emit('apply')
    },
    resetFilter() {
      this.$emit('change', {
        transaction: [],
        database: [],
        queryTable: [],
        queryDuration: [0, 100],
      })
      this.$emit('apply')
    },
  },
}
</script>

<style lang="less" scoped>
.filter-box {
  @apply shadow-md p-2;

  border: 1px solid @neutral-lighter;
  border-radius: 3px;
}
</style>
