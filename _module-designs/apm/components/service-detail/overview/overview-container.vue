<template>
  <div>
    <MRow>
      <MCol class="text-right">
        <TimePicker v-model="timeRange"></TimePicker>
      </MCol>
    </MRow>
    <MRow :key="timeRange">
      <MCol :size="12" class="mb-4">
        <MRow>
          <ResponseTimeChart />
          <ThroughputChart />
          <ErrorRateChart />
        </MRow>
        <MRow>
          <AlertTrendChart />
          <StatusCodeChart />
          <ProtocolsChart />
        </MRow>
        <MRow>
          <OverviewGrid />
        </MRow>
      </MCol>
    </MRow>
  </div>
</template>
<script>
import OverviewGrid from './overview-grid'
import TimePicker from './../../time-picker'
import AlertTrend<PERSON><PERSON> from './alert-trend-chart'
import ResponseTime<PERSON>hart from './response-time-chart'
import StatusCode<PERSON><PERSON> from './status-code-chart'
import ErrorRate<PERSON>hart from './error-rate-chart'
import ThroughputChart from './throughput-chart'
import ProtocolsChart from './protocols-chart'
export default {
  name: 'OverviewContainer',
  components: {
    Overview<PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
  },
  data() {
    return {
      timeRange: 'last_1_Hour',
    }
  },
}
</script>
