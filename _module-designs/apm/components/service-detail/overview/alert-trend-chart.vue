<template>
  <MCol :size="4">
    <div class="health-box-panel widget-border rounded">
      <MRow :gutter="0" class="flex px-3 py-3 pb-0 pl-5 legend-strip">
        <MCol>
          <span class="widget-title">Alert Trend</span>
        </MCol>
        <MCol class="text-right">
          <MDropdown :options="actions" @change="actionChange($event)">
            <template v-slot:trigger>
              <a class="text-neutral-light">
                <MIcon name="ellipsis-v" />
              </a>
            </template>
            <template v-slot:menu-item="{ item, selectItem }">
              <span
                class="flex items-center"
                :class="{ 'text-secondary-red': item.isDanger }"
                @click="selectItem(item)"
              >
                <MIcon v-if="item.icon" :name="item.icon" class="mr-2" />
                {{ item.name }}
              </span>
            </template>
          </MDropdown>
        </MCol>
      </MRow>
      <div class="flex mt-5">
        <div class="alert-chart flex-1">
          <Chart style="width: 98%" :options="chartOptions" />
        </div>
      </div>
    </div>
  </MCol>
</template>
<script>
import Chart from '@components/chart/chart.vue'
// import ColumnAdapter from '@components/chart/column-adapter'
// import { getAlertTrandChartApi } from '../../../apm-service-api'
export default {
  name: 'AlertTrendChart',
  components: {
    Chart,
  },
  data() {
    return {
      chartOptions: {},
      actions: [
        { key: 'delete', name: 'Delete', icon: 'trash-alt', isDanger: true },
      ],
    }
  },
  created() {
    this.getAlertTrandChart()
  },
  methods: {
    actionChange(val) {
      // TODO Service Action Change
    },
    async getAlertTrandChart() {
      //   let resultData = {}
      //   await getAlertTrandChartApi().then((data) => {
      //     resultData = data.alertTrend
      //   })
      //   this.chartOptions = ColumnAdapter(
      //     {
      //       chart: {
      //         height: 330,
      //       },
      //       title: {
      //         text: '',
      //       },
      //       yAxis: {
      //         title: {
      //           text: '',
      //         },
      //       },
      //       plotOptions: {
      //         column: {
      //           stacking: 'normal',
      //         },
      //       },
      //       legend: {
      //         verticalAlign: 'bottom',
      //         x: -30,
      //         y: 10,
      //       },
      //       xAxis: {
      //         type: 'datetime',
      //         title: {
      //           text: 'Time',
      //         },
      //         dateTimeLabelFormats: {
      //           hour: '%H:%M',
      //         },
      //       },
      //     },
      //     resultData.data
      //   )
    },
  },
}
</script>
