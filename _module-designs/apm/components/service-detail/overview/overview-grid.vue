<template>
  <div class="flex flex-col min-h-0 h-full">
    <MRow class="flex-1 min-h-0 mt-5">
      <MCol :size="4">
        <div class="health-box-panel widget-border rounded">
          <MRow :gutter="0" class="flex px-3 py-3 pb-0 pl-5 legend-strip">
            <MCol>
              <span class="widget-title">Top 5 Destinations By Traffic</span>
            </MCol>
            <MCol class="text-right">
              <MDropdown :options="actions" @change="actionChange($event)">
                <template v-slot:trigger>
                  <a class="text-neutral-light">
                    <MIcon name="ellipsis-v" />
                  </a>
                </template>
                <template v-slot:menu-item="{ item, selectItem }">
                  <span
                    class="flex items-center"
                    :class="{ 'text-secondary-red': item.isDanger }"
                    @click="selectItem(item)"
                  >
                    <MIcon v-if="item.icon" :name="item.icon" class="mr-2" />
                    {{ item.name }}
                  </span>
                </template>
              </MDropdown>
            </MCol>
          </MRow>
          <FlotoPaginatedCrud
            as-table
            default-sort="-destination"
            :columns="destinationsColumns"
            :fetch-fn="getDestinations"
            :paging="false"
          />
        </div>
      </MCol>

      <MCol :size="4">
        <div class="health-box-panel widget-border rounded">
          <MRow :gutter="0" class="flex px-3 py-3 pb-0 pl-5 legend-strip">
            <MCol>
              <span class="widget-title">Top 5 Sources By Traffic</span>
            </MCol>
            <MCol class="text-right">
              <MDropdown :options="actions" @change="actionChange($event)">
                <template v-slot:trigger>
                  <a class="text-neutral-light">
                    <MIcon name="ellipsis-v" />
                  </a>
                </template>
                <template v-slot:menu-item="{ item, selectItem }">
                  <span
                    class="flex items-center"
                    :class="{ 'text-secondary-red': item.isDanger }"
                    @click="selectItem(item)"
                  >
                    <MIcon v-if="item.icon" :name="item.icon" class="mr-2" />
                    {{ item.name }}
                  </span>
                </template>
              </MDropdown>
            </MCol></MRow
          >
          <FlotoPaginatedCrud
            as-table
            default-sort="-source"
            :columns="sourcesColumns"
            :fetch-fn="getSourcess"
            :paging="false"
          />
        </div>
      </MCol>
      <MCol :size="4">
        <div class="health-box-panel widget-border rounded">
          <MRow :gutter="0" class="flex px-3 py-3 pb-0 pl-5 legend-strip">
            <MCol>
              <span class="widget-title"
                >Worst 5 Transactions By Apdex Score</span
              >
            </MCol>
            <MCol class="text-right">
              <MDropdown :options="actions" @change="actionChange($event)">
                <template v-slot:trigger>
                  <a class="text-neutral-light">
                    <MIcon name="ellipsis-v" />
                  </a>
                </template>
                <template v-slot:menu-item="{ item, selectItem }">
                  <span
                    class="flex items-center"
                    :class="{ 'text-secondary-red': item.isDanger }"
                    @click="selectItem(item)"
                  >
                    <MIcon v-if="item.icon" :name="item.icon" class="mr-2" />
                    {{ item.name }}
                  </span>
                </template>
              </MDropdown>
            </MCol></MRow
          >
          <FlotoPaginatedCrud
            as-table
            default-sort="-service"
            :columns="transactionsColumns"
            :fetch-fn="getTransactions"
            :paging="false"
          />
        </div>
      </MCol>
    </MRow>
  </div>
</template>
<script>
import {
  getDestinationsApi,
  getSourcesApi,
  getTransactionsApi,
} from './../../../apm-service-api'
export default {
  name: 'OverviewGrid',
  data() {
    this.destinationsColumns = [
      {
        key: 'destination',
        name: 'Destination',
      },
      {
        key: 'traffic',
        name: 'Traffic',
      },
    ]
    this.sourcesColumns = [
      {
        key: 'source',
        name: 'Source',
      },
      {
        key: 'traffic',
        name: 'Traffic',
      },
    ]
    this.transactionsColumns = [
      {
        key: 'service',
        name: 'Service',
      },
      {
        key: 'apdexScore',
        name: 'Apdex Score',
      },
    ]

    return {
      actions: [
        { key: 'delete', name: 'Delete', icon: 'trash-alt', isDanger: true },
      ],
    }
  },
  created() {
    this.getDestinations()
    this.getSourcess()
    this.getTransactions()
  },
  methods: {
    getDestinations() {
      return getDestinationsApi()
    },
    getSourcess() {
      return getSourcesApi()
    },
    getTransactions() {
      return getTransactionsApi()
    },
  },
}
</script>
