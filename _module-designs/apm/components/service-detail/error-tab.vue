<template>
  <FlotoPaginatedCrud
    as-table
    default-sort="-errorMessage"
    :columns="columns"
    :fetch-fn="getServicesTabErrors"
  >
    <template v-slot:responseTime="{ item }"
      >{{ item.responseTime }} ms</template
    >
    <template v-slot:time="{ item }">
      <template v-if="item.time">{{ item.time | datetime }}</template>
    </template>
    <template v-slot:transaction="{ item }">
      <a @click="handleClick(item)">{{
        errorTransactionDataMap[item.transaction]
      }}</a>
    </template>
    <template v-slot:status="{ item }">{{
      errorStatusDataMap[item.status]
    }}</template>
  </FlotoPaginatedCrud>
</template>

<script>
import {
  getServicesTabErrorsApi,
  getServicesTabErrorsFieldsDataApi,
} from '../../apm-api'
export default {
  name: 'ErrorTab',
  props: {
    service: { type: Object, required: true },
  },
  data() {
    this.errorTransactionDataMap = {}
    this.errorStatusDataMap = {}
    return {
      timeRange: 'last_1_Hour',
      errorTransactionDataSelection: [],
      errorStatusDataSelection: [],
      methodOptions: [],
      servicesTabErrors: [],
      columns: [
        {
          key: 'errorMessage',
          name: 'Error Message',
          searchable: true,
        },
        { key: 'time', name: 'Time', sortable: true },
        { key: 'transaction', name: 'Transaction' },
        {
          key: 'method',
          name: 'Method',
        },
        { key: 'status', name: 'Status' },
        { key: 'responseTime', name: 'Response Time' },
      ],
    }
  },
  watch: {
    timeRange(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.getServicesTabErrors()
      }
    },
  },
  created() {
    this.fetchAllTransactions()
    this.getServicesTabErrors()
  },

  methods: {
    getServicesTabErrors() {
      this.loadingServicesTabErrors = true
      return getServicesTabErrorsApi(this.timeRange).then((data) => {
        this.servicesTabErrors = Object.freeze(data)
        // To get the error status field data
        const errorStatusData = []
        data.forEach(({ status, id }) => {
          errorStatusData.push({ key: id, text: status })
          this.errorStatusDataMap[id] = status
        })
        this.errorStatusDataSelection = errorStatusData

        this.loadingServicesTabErrors = false
        return data
      })
    },
    fetchAllTransactions() {
      // To get the transaction field data
      return getServicesTabErrorsFieldsDataApi().then((data) => {
        const errorTransactionData = []
        data.forEach(({ transaction, id }) => {
          errorTransactionData.push({ key: id, text: transaction })
          this.errorTransactionDataMap[id] = transaction
        })
        this.errorTransactionDataSelection = errorTransactionData
      })
    },
    handleClick(item) {
      alert(item.transaction)
    },
  },
}
</script>
