import Random from 'lodash/random'
import Range from 'lodash/range'
// Query tabs data to get from api
function dummyDataTransaction() {
  return [
    '/hotel',
    'usercontroller.index',
    '/registration',
    'hotelcontroller.index',
    'hotelcontroller.index',
  ][Random(0, 4, false)]
}
function dummyDataDatabases() {
  return ['MongoDB', 'MySQL'][Random(0, 1, false)]
}
function dummyDataQueryTable() {
  return ['hotel', 'roomtype', 'roomsize'][Random(0, 2, false)]
}
export const queryFieldsDummyData = Range(1, 1000).map((i) => ({
  id: i,
  transaction: dummyDataTransaction(),
  database: dummyDataDatabases(),
  queryTable: dummyDataQueryTable(),
}))

// Errors tab data to get from api
export const errorsFieldsDummyData = Range(1, 1000).map((i) => ({
  id: i,
  transaction: dummyDataTransaction(),
}))
