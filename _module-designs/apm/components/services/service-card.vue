<template>
  <div id="boxDisplay" class="widget-border rounded px-4 py-4">
    <MRow>
      <MCol :size="6">
        <Severity :severity="item.severity" class="mr-2" style="float: left" />
      </MCol>
      <MCol :size="6">
        <div style="float: right">
          <FlotoGridActions
            :actions="actionOptions"
            :resource="item"
            class="mr-3 action-btn-handle"
            style="position: absolute"
          />
        </div>
      </MCol>
    </MRow>
    <MRow class="mt-3">
      <MCol :size="12" class="small-label">Service</MCol>
      <MCol :size="12" class="label-content">
        <a @click="$emit('item-click')">{{ item.name }}</a>
      </MCol>
    </MRow>
    <MRow>
      <MCol :size="6" class="small-label">Application</MCol>
      <MCol :size="6" class="small-label">Error rate</MCol>
    </MRow>
    <MRow>
      <MCol :size="6" class="label-content text-ellipsis">{{
        item.application
      }}</MCol>
      <MCol :size="6" class="label-content text-ellipsis">{{
        item.errorRate
      }}</MCol>
    </MRow>
    <MRow>
      <MCol :size="6" class="small-label">Response time</MCol>
      <MCol :size="6" class="small-label">Throughput</MCol>
    </MRow>
    <MRow>
      <MCol :size="6" class="label-content text-ellipsis">{{
        item.responseTime
      }}</MCol>
      <MCol :size="6" class="label-content text-ellipsis">{{
        item.throughput
      }}</MCol>
    </MRow>
  </div>
</template>
<script>
import Severity from '@components/severity'
export default {
  name: 'ServicesCardDisplay',
  components: { Severity },
  props: { item: { type: Object, required: true } },
  data() {
    this.actionOptions = [
      { key: 'edit', icon: 'pencil', name: 'Edit Runbook' },
      {
        key: 'delete',
        icon: 'trash-alt',
        name: 'Delete Runbook',
        isDanger: true,
      },
    ]
    return {}
  },
}
</script>
