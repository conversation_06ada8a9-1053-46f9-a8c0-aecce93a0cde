<template>
  <MCol class="filter-box">
    <MRow class="px-2">
      <MCol>
        <FlotoFormItem v-model="currentValue.application" label="Application" />
      </MCol>
      <MCol>
        <FlotoFormItem label="Response time">
          <div class="mt-spacer"
            ><ValueSlider v-model="currentValue.responseTime" range
          /></div>
        </FlotoFormItem>
      </MCol>

      <MCol>
        <FlotoFormItem label="Error rate">
          <div class="mt-spacer"
            ><ValueSlider v-model="currentValue.errorRate" range
          /></div>
        </FlotoFormItem>
      </MCol>

      <MCol>
        <FlotoFormItem label="Throughput">
          <div class="mt-spacer"
            ><ValueSlider v-model="currentValue.throughput" range
          /></div>
        </FlotoFormItem>
      </MCol>
    </MRow>
    <MRow :gutter="0">
      <MCol class="mt-2 text-right pb-2 pr-3">
        <MButton class="mr-2" @click="apply">Apply</MButton>
        <MButton
          variant="default"
          @click="
            $emit('change', {
              application: '',
              responseTime: [],
              errorRate: [],
              throughput: [],
            })
          "
        >
          Reset
        </MButton>
      </MCol>
    </MRow>
  </MCol>
</template>
<script>
import ValueSlider from '@components/value-slider'

export default {
  name: 'ServicesFilters',
  components: { ValueSlider },
  model: { event: 'change' },
  props: { value: { type: Object, required: true } },
  data() {
    return {
      currentValue: { ...this.value },
    }
  },
  watch: {
    value(newValue) {
      this.currentValue = { ...newValue }
    },
  },
  methods: {
    apply() {
      this.$emit('change', this.currentValue)
    },
  },
}
</script>
