<template>
  <MRow :gutter="0" class="flex-1">
    <MCol :size="12">
      <MRow :gutter="0" class="flex px-3 py-3">
        <MCol>
          <span class="widget-title">{{ text }}</span>
        </MCol>
        <MCol class="text-right">
          <div class="health-chart-box">
            <MDropdown :options="actions" @change="actionChange($event)">
              <template v-slot:trigger>
                <a class="text-neutral-light">
                  <MIcon name="ellipsis-v" />
                </a>
              </template>
              <template v-slot:menu-item="{ item, selectItem }">
                <span
                  class="flex items-center"
                  :class="{ 'text-secondary-red': item.isDanger }"
                  @click="selectItem(item)"
                >
                  <MIcon v-if="item.icon" :name="item.icon" class="mr-2" />
                  {{ item.name }}
                </span>
              </template>
            </MDropdown>
          </div>
        </MCol>
      </MRow>
      <MRow class="flex-1 px-3 py-3 pt-0">
        <MCol class="flex health-chart">
          <MTooltip placement="top">
            <template v-slot:trigger>
              <div class="flex clear own" :style="{ width: clearWidth + '%' }"
                >&nbsp;</div
              >
            </template>
            {{ items.clear }}
          </MTooltip>

          <MTooltip placement="top">
            <template v-slot:trigger>
              <div
                class="flex own warning"
                :style="{ width: waringWidth + '%' }"
              >
                &nbsp;</div
              >
            </template>
            {{ items.warning }}
          </MTooltip>

          <MTooltip placement="top">
            <template v-slot:trigger>
              <div class="flex own major" :style="{ width: majorWidth + '%' }"
                >&nbsp;</div
              >
            </template>
            {{ items.major }}
          </MTooltip>

          <MTooltip placement="top">
            <template v-slot:trigger>
              <div
                class="flex own critical"
                :style="{ width: criticalWidth + '%' }"
                >&nbsp;</div
              >
            </template>
            {{ items.critical }}
          </MTooltip>

          <MTooltip placement="top">
            <template v-slot:trigger>
              <div class="flex own down" :style="{ width: downWidth + '%' }"
                >&nbsp;</div
              >
            </template>
            {{ items.down }}
          </MTooltip>
        </MCol>
      </MRow>
    </MCol>
  </MRow>
</template>

<script>
export default {
  name: 'Progress',
  props: {
    items: { type: Object, default: () => {} },
    text: { type: String, default: null },
  },
  data() {
    return {
      majorWidth: '0',
      criticalWidth: '0',
      waringWidth: '0',
      clearWidth: '0',
      downWidth: '0',
      actions: [
        { key: 'delete', name: 'Delete', icon: 'trash-alt', isDanger: true },
      ],
    }
  },
  created() {
    this.percentageCalculation()
  },
  methods: {
    percentageCalculation() {
      let total =
        this.items.major +
        this.items.critical +
        this.items.warning +
        this.items.clear +
        this.items.down

      this.majorWidth = ((this.items.major * 100) / total).toFixed()
      this.criticalWidth = ((this.items.critical * 100) / total).toFixed()
      this.waringWidth = ((this.items.warning * 100) / total).toFixed()
      this.clearWidth = ((this.items.clear * 100) / total).toFixed()
      this.downWidth = ((this.items.down * 100) / total).toFixed()
    },
    actionChange(val) {
      // TODO Progress Action Change
    },
  },
}
</script>
<style scoped>
.progress {
  position: relative;
  width: auto;
  height: 76px;
  padding: 10px;
  margin: 0 auto;
}
</style>
