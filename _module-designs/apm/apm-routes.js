import lazyLoadView from '@router/lazy-loader'
import ContainerView from './views/main'
import configs from './config'

const routePrefix = configs.routePrefix

const moduleName = configs.name

const routeNamePrefix = configs.routeNamePrefix

export default [
  {
    path: `/${routePrefix}`,
    component: ContainerView,
    meta: { moduleName },
    children: [
      {
        path: '',
        redirect: {
          name: `${routeNamePrefix}.service-map`,
        },
        name: routeNamePrefix,
      },
      {
        path: 'service-map',
        name: `${routeNamePrefix}.service-map`,
        component: () =>
          lazyLoadView(
            import(/* webpackChunkName: "apm" */ './views/service-map')
          ),
      },
      {
        path: 'dashboard',
        name: `${routeNamePrefix}.dashboard`,
        component: () =>
          lazyLoadView(
            import(/* webpackChunkName: "apm" */ './views/dashboard')
          ),
      },
      {
        path: 'services',
        name: `${routeNamePrefix}.services`,
        component: () =>
          lazyLoadView(
            import(/* webpackChunkName: "apm" */ './views/services')
          ),
      },
      {
        path: 'transactions',
        name: `${routeNamePrefix}.transactions`,
        component: () =>
          lazyLoadView(
            import(/* webpackChunkName: "apm" */ './views/transactions')
          ),
      },
      {
        path: 'transactions/:id',
        name: `${routeNamePrefix}.transaction-detail`,
        component: () =>
          lazyLoadView(
            import(/* webpackChunkName: "apm" */ './views/transaction-detail')
          ),
        meta: {
          showRootTabs: false,
        },
      },
      {
        path: 'synthetic-monitoring',
        name: `${routeNamePrefix}.synthetic-monitoring`,
        component: () =>
          lazyLoadView(
            import(/* webpackChunkName: "apm" */ './views/synthetic-monitoring')
          ),
      },
      {
        path: 'services/:id',
        name: `${routeNamePrefix}.service-detail`,
        component: () =>
          lazyLoadView(
            import(/* webpackChunkName: "apm" */ './views/service-detail')
          ),
        meta: {
          showRootTabs: false,
        },
      },
    ],
  },
]
