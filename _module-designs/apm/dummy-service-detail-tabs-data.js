import Range from 'lodash/range'
import Random from 'lodash/random'
import Moment from 'moment'
export const dummyServicesTabtransactionData = Range(1, 1000).map((i) => ({
  id: i,
  transaction: [
    '/hotel',
    'usercontroller.index',
    '/registration',
    'hotelcontroller.index',
    'hotelcontroller.index',
  ][Random(0, 4, false)],
  time: Moment().subtract(2, 'minutes').valueOf(),
  throughput: 5 + i,
  responseTime: 0.12 + i,
  errorRate: 5 + i,
}))

export const dummyServicesTabQueriesData = Range(1, 1000).map((i) => ({
  id: i,
  query: [
    'db.hotel.find_one({"_id":{"$oid":?}})',
    'sdb.roomtype.find_one({"_id":{"$oid":?}})',
    'sdb.roomtype.find({"hotel":{"$id":{"$oid":?}})',
    'sdb.roomtype.find({"hotel":{"$id":{"$oid":?},$ref:?}})',
  ][Random(0, 3, false)],
  time: Moment().subtract(2, 'minutes').valueOf(),
  transaction: [1, 2, 3, 4, 5][Random(0, 4, false)],
  database: [1, 2][Random(0, 1, false)],
  queryTable: [1, 2, 3][Random(0, 2, false)],
  queryDuration: 0.12 + i,
}))

export const dummyServicesTabErrorsData = Range(1, 1000).map((i) => ({
  id: i,
  errorMessage: [
    `Get http://bookings.neta-suites.com:8080/hotels?: dial tcp
     172.18.0.2:8080:getsockopt: connection refused`,
    'connection timedout',
  ][Random(0, 1, false)],
  time: Moment().subtract(2, 'minutes').valueOf(),
  transaction: [1, 2, 3, 4, 5][Random(0, 4, false)],
  method: ['POST', 'GET', 'PUT'][Random(0, 2, false)],
  status: 404 + i,
  responseTime: 0.12 + i,
}))
