import {
  DummyServiceMapData,
  DummyNodeTypeTabData,
} from './dummy-service-map-data'

export function getAllMonitoringNodes() {
  // @TODO fire actual api here to get all the monitoring node
  return Promise.resolve(DummyServiceMapData)
}

export function getNodeInformationApi(node) {
  // @TODO fire actual api here to get data for the first tab
  return new Promise((resolve) =>
    setTimeout(() => resolve(DummyNodeTypeTabData(node)), 1000)
  )
}
