import Range from 'lodash/range'
import Random from 'lodash/random'
import Moment from 'moment'
import { generateId } from '@utils/id'

export const DummyServiceMapData = Range(1, 50).map((i) => ({
  id: i,
  name: `node-${i}`,
  type: ['Windows', 'Linux', 'SMTP Device'][Random(0, 2, false)],
  connected: Range(1, 2).map(() => Random(1, 45, false)),
  status: ['Critical', 'Major', 'Warning', 'Clear'][Random(0, 3, false)],
}))

export const DummyNodeTypeTabData = (node) => ({
  id: node.id,
  name: node.name,
  ip: '***********',
  os: 'Windows',
  esx: '1.esxihost',
  memoryUtilization: {
    name: 'Memory Utilization (%)',
    color: '#099dd9',
    data: [
      {
        x: Moment().subtract(3, 'hours').valueOf(),
        y: Random(1, 50, false),
      },
      {
        x: Moment().subtract(3, 'hours').add(30, 'minutes').valueOf(),
        y: Random(1, 50, false),
      },
      {
        x: Moment().subtract(2, 'hours').valueOf(),
        y: Random(1, 50, false),
      },
      {
        x: Moment().subtract(2, 'hours').add(30, 'minutes').valueOf(),
        y: Random(1, 50, false),
      },
      {
        x: Moment().subtract(1, 'hours').valueOf(),
        y: Random(1, 50, false),
      },
      {
        x: Moment().subtract(1, 'hours').add(30, 'minutes').valueOf(),
        y: Random(1, 50, false),
      },
      {
        x: Moment().add(30, 'minutes').valueOf(),
        y: Random(1, 50, false),
      },
    ],
  },
  cpuUtilization: {
    name: 'CPU Utilization (%)',
    color: '#099dd9',
    data: [
      {
        x: Moment().subtract(3, 'hours').valueOf(),
        y: Random(1, 50, false),
      },
      {
        x: Moment().subtract(3, 'hours').add(30, 'minutes').valueOf(),
        y: Random(1, 50, false),
      },
      {
        x: Moment().subtract(2, 'hours').valueOf(),
        y: Random(1, 50, false),
      },
      {
        x: Moment().subtract(2, 'hours').add(30, 'minutes').valueOf(),
        y: Random(1, 50, false),
      },
      {
        x: Moment().subtract(1, 'hours').valueOf(),
        y: Random(1, 50, false),
      },
      {
        x: Moment().subtract(1, 'hours').add(30, 'minutes').valueOf(),
        y: Random(1, 50, false),
      },
      {
        x: Moment().add(30, 'minutes').valueOf(),
        y: Random(1, 50, false),
      },
    ],
  },
  alerts: [
    {
      key: generateId(),
      id: 1,
      name: 'VM Memory Utilization',
      status: 'Critical',
      metric: 'Metric - 1',
      value: '85%',
      firstSeen: `3d 12h 56m 34s`, // use moment to convert to this string
      policy: 'Policy VM 12',
    },
    {
      key: generateId(),
      id: 2,
      name: 'VM Memory Utilization',
      status: 'Major',
      metric: 'Metric - 2',
      value: '55%',
      firstSeen: `3d 12h 56m 34s`, // use moment to convert to this string
      policy: 'Policy VM 1',
    },
  ],
  alertTrend: {
    name: 'Alert Trend',
    data: [
      {
        name: 'Critical',
        color: '#f04e3e',
        data: [
          {
            x: Moment().subtract(3, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(3, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(2, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(2, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(1, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(1, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
        ],
      },
      {
        name: 'Major',
        color: '#f58518',
        data: [
          {
            x: Moment().subtract(3, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(3, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(2, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(2, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(1, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(1, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
        ],
      },
      {
        name: 'Warning',
        color: '#f5bc18',
        data: [
          {
            x: Moment().subtract(3, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(3, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(2, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(2, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(1, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(1, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
        ],
      },
    ],
  },
})
