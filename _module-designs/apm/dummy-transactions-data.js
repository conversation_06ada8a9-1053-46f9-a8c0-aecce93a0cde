import Range from 'lodash/range'
import Random from 'lodash/random'
import Moment from 'moment'
import {
  data,
  DummyTransactionLogsData,
  DummyTransactionHostChartData,
  DummyTransactionOrganizationChartData,
} from './dummy-transaction-detail-tabs-data'
import { DummyTransactionDetailsData } from './services-dummy-data'
export const dummyTransactionData = Range(1, 1000).map((i) => ({
  id: i,
  date: Moment().subtract(2, 'minutes').valueOf(),

  transaction: [
    '/hotel',
    'usercontroller.index',
    '/registration',
    'hotelcontroller.index',
    'hotelcontroller.index',
  ][Random(0, 4, false)],
  serviceId: [1, 2, 3, 4, 5][Random(0, 4, false)],
  duration: 0.12 + i,
  httpMethod: ['POST', 'GET', 'PUT', 'DELETE'][Random(0, 3, false)],
  statusCode: 15 + i,
  status: ['Error', 'Ok'][Random(0, 1, false)],
  type: [1, 2, 3][Random(0, 2, false)],
  host: [1, 2, 3, 4, 5][Random(0, 4, false)],
}))
export function getTransactionDetailApi() {
  return new Promise((resolve) => setTimeout(() => resolve(data, 1000)))
  //   return Promise.resolve(DummyServicesData)
}
export function getTransactionLogsApi() {
  return new Promise((resolve) =>
    setTimeout(() => resolve(DummyTransactionLogsData, 1000))
  )
}
export function getTransactionHostChartApi() {
  return new Promise((resolve) =>
    setTimeout(() => resolve(DummyTransactionHostChartData()), 1000)
  )
}
export function getTransactionOperationDetailApi() {
  return new Promise((resolve) =>
    setTimeout(() => resolve(DummyTransactionOrganizationChartData), 1000)
  )
}
export function getTransactionDetailsApi(id) {
  return new Promise((resolve) =>
    setTimeout(() => resolve(DummyTransactionDetailsData), 1000)
  )
}
