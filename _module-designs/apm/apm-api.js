// import api from '@api'

// export function apmApi() {
//   // @TODO add api call here
// }
import { dummyTransactionData } from '../apm/dummy-transactions-data'
import { transactionsFieldByIdDummyData } from './components/transactions/dummyDataById'
import {
  queryFieldsDummyData,
  errorsFieldsDummyData,
} from '../apm/components/service-detail/dummy-data'
import {
  dummyServicesTabtransactionData,
  dummyServicesTabQueriesData,
  dummyServicesTabErrorsData,
} from './dummy-service-detail-tabs-data'
import {
  DummyServicesResponseTimeData,
  DummyTransactionsDurationsData,
  DummyDeviatingServicesData,
  DummyServiceHealthData,
  DummyInfrastructrureHealthData,
  DummyAlertTrandChartData,
  DummySyntheticMonitoringData,
} from './dummy-data'

// Dashboard Tab API here
export function getServiceResponseTimeApi() {
  return new Promise((resolve) =>
    setTimeout(() => resolve(DummyServicesResponseTimeData), 2000)
  )
}
export function getTransactionsDurationsApi() {
  return new Promise((resolve) =>
    setTimeout(() => resolve(DummyTransactionsDurationsData), 2000)
  )
}
export function getDeviatingServicesApi() {
  return new Promise((resolve) =>
    setTimeout(() => resolve(DummyDeviatingServicesData), 2000)
  )
}
export function getServiceHealthApi() {
  return DummyServiceHealthData
}
export function getInfrastructrureHealthApi() {
  return DummyInfrastructrureHealthData
}
export function getAlertTrandChartApi() {
  return new Promise((resolve) =>
    setTimeout(() => resolve(DummyAlertTrandChartData()), 1000)
  )
}

// Synthetic Moinitoring API here
export function getSyntheticMonitoringApi(timeRange) {
  return new Promise((resolve) =>
    setTimeout(() => resolve(DummySyntheticMonitoringData), 2000)
  )
}
// Transaction API here
export function getTransactionsServicesApi(timeRange) {
  return new Promise((resolve) =>
    setTimeout(() => resolve(dummyTransactionData), 2000)
  )
}
// To get the transaction fields by respective Ids
export function getTransactionsFieldsDataByIdApi() {
  return new Promise((resolve) =>
    setTimeout(() => resolve(transactionsFieldByIdDummyData))
  )
}
// Transaction tab of Services API here
export function getServicesTabTransactionApi(timeRange) {
  return new Promise((resolve) =>
    setTimeout(() => resolve(dummyServicesTabtransactionData), 2000)
  )
}
// Queries tab of Services API here
export function getServicesTabQueriesApi(timeRange) {
  return new Promise((resolve) =>
    setTimeout(() => resolve(dummyServicesTabQueriesData), 2000)
  )
}
// Errors tab of Services API here
export function getServicesTabErrorsApi(timeRange) {
  return new Promise((resolve) =>
    setTimeout(() => resolve(dummyServicesTabErrorsData), 2000)
  )
}
// To get the Queries fields by respective Ids
export function getServicesTabQueriesFieldsDataApi() {
  return new Promise((resolve) =>
    setTimeout(() => resolve(queryFieldsDummyData), 2000)
  )
}
// To get the Errors fields by respective Ids
export function getServicesTabErrorsFieldsDataApi() {
  return new Promise((resolve) =>
    setTimeout(() => resolve(errorsFieldsDummyData), 2000)
  )
}
