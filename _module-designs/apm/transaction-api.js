import Range from 'lodash/range'
import Random from 'lodash/random'
import Moment from 'moment'
import {
  data,
  DummyTransactionLogsData,
  DummyTransactionHostChartData,
  DummyTransactionOrganizationChartData,
  DummyXRangeChartData,
} from './dummy-transaction-detail-tabs-data'
import { DummyTransactionDetailsData } from './services-dummy-data'
export const transactionData = Range(1, 1000).map((i) => ({
  id: i,
  // name: `Motadata-${i}`,
  date: Moment().subtract(2, 'minutes').valueOf(),
  // date: new Date().getTime(),
  transaction: [
    '/hotel',
    'usercontroller.index',
    '/registration',
    'hotelcontroller.index',
    'hotelcontroller.index',
  ][Random(0, 4, false)],
  service: [
    'Weather Service',
    'Booking Service',
    'Web App',
    'Custom app - java app',
    false,
  ][Random(0, 3, false)],
  duration: 0.12 + i + ' sec',
  httpMethod: ['POST', 'GET', 'PUT'][Random(0, 2, false)],
  statusCode: 202 + i,
  status: ['Error', 'Ok'][Random(0, 1, false)],
  type: ['SQL', 'Web', 'App'][Random(0, 2, false)],
  host: [
    '*************',
    '***********',
    '*************',
    '************',
    '*************',
  ][Random(0, 2, false)],
}))
export function getTransactionDetailApi() {
  return new Promise((resolve) => setTimeout(() => resolve(data, 1000)))
  //   return Promise.resolve(DummyServicesData)
}
export function getTransactionLogsApi() {
  return new Promise((resolve) =>
    setTimeout(() => resolve(DummyTransactionLogsData, 1000))
  )
}
export function getTransactionHostChartApi() {
  return new Promise((resolve) =>
    setTimeout(() => resolve(DummyTransactionHostChartData()), 1000)
  )
}
export function getTransactionOperationDetailApi() {
  return new Promise((resolve) =>
    setTimeout(() => resolve(DummyTransactionOrganizationChartData), 1000)
  )
}
export function getTransactionXRangeChartApi() {
  return new Promise((resolve) =>
    setTimeout(() => resolve(DummyXRangeChartData), 1000)
  )
}
export function getTransactionDetailsApi(id) {
  return new Promise((resolve) =>
    setTimeout(() => resolve(DummyTransactionDetailsData), 1000)
  )
}
