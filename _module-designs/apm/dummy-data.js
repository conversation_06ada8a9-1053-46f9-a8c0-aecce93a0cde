import Range from 'lodash/range'
import Moment from 'moment'
import Random from 'lodash/random'
export const DummyServicesData = Range(1, 1000).map((i) => ({
  id: i,
  name: `Chai-${i}`,
  price: 18,
  stock: 39,
  discontinued: false,
}))

export const DummyServicesResponseTimeData = Range(1, 6).map((i) => ({
  service: `Service-${i}`,
  throughput: `${i}/min`,
  responseTime: `${i} ms`,
  apdexScore: `${i}.5`,
  failureRate: `${i} %`,
}))
export const DummyTransactionsDurationsData = Range(1, 6).map((i) => ({
  transaction: `Usercontroll-${i}.index`,
  service: `Booking service- ${i}`,
  duration: `${i} sec`,
}))

export const DummyDeviatingServicesData = Range(1, 6).map((i) => ({
  service: `Booking service- ${i}`,
  deviating: `Response Time -${i}`,
  deviatingPercentage: `${i} %`,
}))

export const DummyServiceHealthData = {
  major: 200,
  critical: 100,
  warning: 50,
  clear: 20,
  down: 150,
}

export const DummyInfrastructrureHealthData = {
  major: 210,
  critical: 40,
  warning: 150,
  clear: 90,
  down: 77,
}

export const DummyAlertTrandChartData = (node) => ({
  alertTrend: {
    name: 'Alert Trend',
    data: [
      {
        name: 'Critical',
        color: '#f04e3e',
        data: [
          {
            x: Moment().subtract(3, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(3, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(2, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(2, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(1, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(1, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
        ],
      },
      {
        name: 'Major',
        color: '#f58518',
        data: [
          {
            x: Moment().subtract(3, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(3, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(2, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(2, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(1, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(1, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
        ],
      },
      {
        name: 'Warning',
        color: '#f5bc18',
        data: [
          {
            x: Moment().subtract(3, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(3, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(2, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(2, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(1, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(1, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
        ],
      },
    ],
  },
})

// Synthetic Monitoring DymmyData Here
export const DummySyntheticMonitoringData = Range(1, 100).map((i) => ({
  id: i,
  name: `Motadata-${i}`,
  application: `Application-${i}`,
  americaAvailability: Random(1, 100, false),
  americaResponseTime: Random(1, 10, false),
  americaCheckFrequency: Random(1, 10, false),
  asiaAvailability: Random(1, 100, false),
  asiaResponseTime: Random(1, 10, false),
  asiaCheckFrequency: Random(1, 10, false),
  europAvailability: Random(1, 100, false),
  europResponseTime: Random(1, 10, false),
  europCheckFrequency: Random(1, 10, false),
}))
