import Range from 'lodash/range'
import Random from 'lodash/random'
import Moment from 'moment'
import { generateId } from '@utils/id'

function getRendomServiceName() {
  return [
    'Booking service',
    'Weather service',
    'Auth service',
    'Fetch service',
    'Anomaly',
  ][Random(0, 4, false)]
}
function getApplication() {
  return [
    'www.motadata.com',
    'My Custome Application',
    'Mobile app',
    'Web app',
    'Custome app - java app',
  ][Random(0, 4, false)]
}
function getRendomServiceSeverity() {
  return ['Warning', 'Critical', 'Down'][Random(0, 2, false)]
}

export const DummyServicesData = Range(1, 50).map((i) => ({
  id: i,
  name: getRendomServiceName(),
  severity: getRendomServiceSeverity(),
  application: getApplication(),
  responseTime: Random(0, 100, false),
  errorRate: Random(0, 100, false),
  throughput: Random(0, 100, false),
  action: '',
}))
export const DummyTransactionDetailsData = {
  id: generateId,
  name: 'trasnaction-1',
  time: '01/03/2020 5:43',
  throughput: '11.2K/min',
  responseTime: '67.3 ms',
  errorRate: '25%',
  operations: Range(1, 100).map((i) => ({
    id: i,
    spanLayer: getRendomSpanLayer(),
    duration: Random(0, 500, true).toFixed(2),
    traceDuration: Random(0, 100, true).toFixed(2),
    spanCount: Random(1, 5, false),
  })),
}
function getRendomSpanLayer() {
  return [
    'java',
    'spring',
    'apiHandler',
    'http.application',
    'mongoDB',
    'http.request',
    'http.servicerequest',
    'http.HandlerFunc',
    'http.client',
  ][Random(0, 8, false)]
}

export const DummyServiceOverviewData = Range(1, 6).map((i) => ({
  id: i,
  destination: `Destination-${i}`,
  traffic: `${i}56 K/sec`,
}))
export const DummyOverviewSourceData = Range(1, 6).map((i) => ({
  id: i,
  source: `Source-${i}`,
  traffic: `${i}56 K/sec`,
}))
export const DummyOverviewTransactionsData = Range(1, 6).map((i) => ({
  id: i,
  service: `/hotel/-${i}`,
  apdexScore: `${i}.05 `,
}))
export const DummyAlertTrandChartData = (node) => ({
  alertTrend: {
    name: 'Alert Trend',
    data: [
      {
        name: 'Critical',
        color: '#f04e3e',
        data: [
          {
            x: Moment().subtract(3, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(3, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(2, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(2, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(1, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(1, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
        ],
      },
      {
        name: 'Major',
        color: '#f58518',
        data: [
          {
            x: Moment().subtract(3, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(3, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(2, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(2, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(1, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(1, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
        ],
      },
      {
        name: 'Warning',
        color: '#f5bc18',
        data: [
          {
            x: Moment().subtract(3, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(3, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(2, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(2, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(1, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(1, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
        ],
      },
    ],
  },
})
export const DummyResponseTimeChartData = (node) => ({
  alertTrend: {
    name: 'Response Time Trend',
    data: [
      {
        name: 'Response time - 1',
        color: '#4169e1',
        data: [
          {
            x: Moment().subtract(3, 'hours').valueOf(),
            y: Random(1, 100, false),
          },
          {
            x: Moment().subtract(3, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 100, false),
          },
          {
            x: Moment().subtract(2, 'hours').valueOf(),
            y: Random(1, 100, false),
          },
          {
            x: Moment().subtract(2, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 100, false),
          },
          {
            x: Moment().subtract(1, 'hours').valueOf(),
            y: Random(1, 100, false),
          },
          {
            x: Moment().subtract(1, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 100, false),
          },
          {
            x: Moment().add(30, 'minutes').valueOf(),
            y: Random(1, 100, false),
          },
        ],
      },
      {
        name: 'Response time - 2',
        color: '#FFD700',
        data: [
          {
            x: Moment().subtract(3, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(3, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(2, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(2, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(1, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(1, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
        ],
      },
      {
        name: 'Response time - 3',
        color: '#808080',
        data: [
          {
            x: Moment().subtract(3, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(3, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(2, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(2, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(1, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(1, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
        ],
      },
    ],
  },
})

export const DummyStatusCodeChartData = (node) => ({
  alertTrend: {
    name: 'Response Time Trend',
    data: [
      {
        name: '1xx',
        color: '#4169e1',
        data: [
          {
            x: Moment().subtract(3, 'hours').valueOf(),
            y: Random(1, 100, false),
          },
          {
            x: Moment().subtract(3, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 100, false),
          },
          {
            x: Moment().subtract(2, 'hours').valueOf(),
            y: Random(1, 100, false),
          },
          {
            x: Moment().subtract(2, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 100, false),
          },
          {
            x: Moment().subtract(1, 'hours').valueOf(),
            y: Random(1, 100, false),
          },
          {
            x: Moment().subtract(1, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 100, false),
          },
          {
            x: Moment().add(30, 'minutes').valueOf(),
            y: Random(1, 100, false),
          },
        ],
      },
      {
        name: '2xx',
        color: ' #6B8E23',
        data: [
          {
            x: Moment().subtract(3, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(3, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(2, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(2, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(1, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(1, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
        ],
      },
      {
        name: '3xx',
        color: '#FFD700',
        data: [
          {
            x: Moment().subtract(3, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(3, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(2, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(2, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(1, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(1, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
        ],
      },
      {
        name: '4xx',
        color: '#808080',
        data: [
          {
            x: Moment().subtract(3, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(3, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(2, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(2, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(1, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(1, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
        ],
      },
    ],
  },
})
export const DummyErrorRateChartData = (node) => ({
  alertTrend: {
    name: 'Response Time Trend',
    data: [
      {
        name: 'Response time - 1',
        color: '#4169e1',
        data: [
          {
            x: Moment().subtract(3, 'hours').valueOf(),
            y: Random(1, 100, false),
          },
          {
            x: Moment().subtract(3, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 100, false),
          },
          {
            x: Moment().subtract(2, 'hours').valueOf(),
            y: Random(1, 100, false),
          },
          {
            x: Moment().subtract(2, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 100, false),
          },
          {
            x: Moment().subtract(1, 'hours').valueOf(),
            y: Random(1, 100, false),
          },
          {
            x: Moment().subtract(1, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 100, false),
          },
          {
            x: Moment().add(30, 'minutes').valueOf(),
            y: Random(1, 100, false),
          },
        ],
      },
      {
        name: 'Response time - 2',
        color: '#FFD700',
        data: [
          {
            x: Moment().subtract(3, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(3, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(2, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(2, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(1, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(1, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
        ],
      },
      {
        name: 'Response time - 3',
        color: '#808080',
        data: [
          {
            x: Moment().subtract(3, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(3, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(2, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(2, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(1, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(1, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
        ],
      },
    ],
  },
})

export const DummyThroughputChartData = (node) => ({
  alertTrend: {
    name: 'Response Time Trend',
    data: [
      {
        name: 'Throughput - 1',
        color: '#4169e1',
        data: [
          {
            x: Moment().subtract(3, 'hours').valueOf(),
            y: Random(1, 100, false),
          },
          {
            x: Moment().subtract(3, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 100, false),
          },
          {
            x: Moment().subtract(2, 'hours').valueOf(),
            y: Random(1, 100, false),
          },
          {
            x: Moment().subtract(2, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 100, false),
          },
          {
            x: Moment().subtract(1, 'hours').valueOf(),
            y: Random(1, 100, false),
          },
          {
            x: Moment().subtract(1, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 100, false),
          },
          {
            x: Moment().add(30, 'minutes').valueOf(),
            y: Random(1, 100, false),
          },
        ],
      },
    ],
  },
})

export const DummyProtocolsChartData = (node) => ({
  alertTrend: {
    name: 'Response Time Trend',
    data: [
      {
        name: 'Get',
        color: '#4169e1',
        data: [
          {
            x: Moment().subtract(3, 'hours').valueOf(),
            y: Random(1, 100, false),
          },
          {
            x: Moment().subtract(3, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 100, false),
          },
          {
            x: Moment().subtract(2, 'hours').valueOf(),
            y: Random(1, 100, false),
          },
          {
            x: Moment().subtract(2, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 100, false),
          },
          {
            x: Moment().subtract(1, 'hours').valueOf(),
            y: Random(1, 100, false),
          },
          {
            x: Moment().subtract(1, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 100, false),
          },
          {
            x: Moment().add(30, 'minutes').valueOf(),
            y: Random(1, 100, false),
          },
        ],
      },
      {
        name: 'Post',
        color: '#FFD700',
        data: [
          {
            x: Moment().subtract(3, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(3, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(2, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(2, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(1, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(1, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
        ],
      },
      {
        name: 'Put',
        color: '#808080',
        data: [
          {
            x: Moment().subtract(3, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(3, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(2, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(2, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(1, 'hours').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().subtract(1, 'hours').add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
          {
            x: Moment().add(30, 'minutes').valueOf(),
            y: Random(1, 50, false),
          },
        ],
      },
    ],
  },
})

const threeHours = Moment().subtract(3, 'hours').format('YYYY-mm-DD HH:mm')

const threeThirtyHours = Moment()
  .subtract(3, 'hours')
  .add(30, 'minutes')
  .format('YYYY-mm-DD HH:mm')
const twoHours = Moment().subtract(2, 'hours').format('YYYY-mm-DD HH:mm')

export const DummyHeatMapChartData = (node) => ({
  alertTrend: {
    name: 'HeatMap',
    data: [
      {
        name: 'HeatMap-1',
        color: '#f04e3e',
        data: [
          {
            x: threeHours,
            y: 0,
            z: Random(1, 1000, false),
          },
          {
            x: threeHours,
            y: 1,
            z: Random(1, 1000, false),
          },
          {
            x: threeHours,
            y: 2,
            z: Random(1, 1000, false),
          },
          {
            x: threeHours,
            y: 3,
            z: Random(1, 1000, false),
          },
          {
            x: threeHours,
            y: 4,
            z: Random(1, 1000, false),
          },
          {
            x: threeHours,
            y: 5,
            z: Random(1, 1000, false),
          },
          {
            x: threeHours,
            y: 6,
            z: Random(1, 1000, false),
          },
          {
            x: threeHours,
            y: 7,
            z: Random(1, 1000, false),
          },
          {
            x: threeHours,
            y: 8,
            z: Random(1, 1000, false),
          },
          {
            x: threeThirtyHours,
            y: 0,
            z: Random(1, 1000, false),
          },
          {
            x: threeThirtyHours,
            y: 1,
            z: Random(1, 1000, false),
          },
          {
            x: threeThirtyHours,
            y: 2,
            z: Random(1, 1000, false),
          },
          {
            x: threeThirtyHours,
            y: 3,
            z: Random(1, 1000, false),
          },
          {
            x: threeThirtyHours,
            y: 4,
            z: Random(1, 1000, false),
          },
          {
            x: threeThirtyHours,
            y: 5,
            z: Random(1, 1000, false),
          },
          {
            x: threeThirtyHours,
            y: 6,
            z: Random(1, 1000, false),
          },
          {
            x: threeThirtyHours,
            y: 7,
            z: Random(1, 1000, false),
          },
          {
            x: threeThirtyHours,
            y: 8,
            z: Random(1, 1000, false),
          },
          {
            x: twoHours,
            y: 0,
            z: Random(1, 1000, false),
          },
          {
            x: twoHours,
            y: 1,
            z: Random(1, 1000, false),
          },
          {
            x: twoHours,
            y: 2,
            z: Random(1, 1000, false),
          },
          {
            x: twoHours,
            y: 3,
            z: Random(1, 1000, false),
          },
          {
            x: twoHours,
            y: 4,
            z: Random(1, 1000, false),
          },
          {
            x: twoHours,
            y: 5,
            z: Random(1, 1000, false),
          },
          {
            x: twoHours,
            y: 6,
            z: Random(1, 1000, false),
          },
          {
            x: twoHours,
            y: 7,
            z: Random(1, 1000, false),
          },
          {
            x: twoHours,
            y: 8,
            z: Random(1, 1000, false),
          },
        ],
      },
    ],
  },
})
