import {
  DummyServicesData,
  DummyServiceOverviewData,
  DummyOverviewSourceData,
  DummyOverviewTransactionsData,
  DummyAlertTrandChartData,
  DummyResponseTimeChartData,
  DummyStatusCodeChartData,
  DummyErrorRateChartData,
  DummyThroughputChartData,
  DummyProtocolsChartData,
  DummyHeatMapChartData,
} from './services-dummy-data'

export function getAllServicesApi() {
  return new Promise((resolve) =>
    setTimeout(() => resolve(DummyServicesData), 1000)
  )
  //   return Promise.resolve(DummyServicesData)
  // }
}
export function getDestinationsApi() {
  return new Promise((resolve) =>
    setTimeout(() => resolve(DummyServiceOverviewData), 1000)
  )
}
export function getSourcesApi() {
  return new Promise((resolve) =>
    setTimeout(() => resolve(DummyOverviewSourceData), 1000)
  )
}
export function getTransactionsApi() {
  return new Promise((resolve) =>
    setTimeout(() => resolve(DummyOverviewTransactionsData), 1000)
  )
}
export function getAlertTrandChartApi() {
  return new Promise((resolve) =>
    setTimeout(() => resolve(DummyAlertTrandChartData()), 1000)
  )
}
export function getResponseTimeChartApi() {
  return new Promise((resolve) =>
    setTimeout(() => resolve(DummyResponseTimeChartData()), 1000)
  )
}
export function getStatusCodeChartApi() {
  return new Promise((resolve) =>
    setTimeout(() => resolve(DummyStatusCodeChartData()), 1000)
  )
}
export function getErrorRateChartApi() {
  return new Promise((resolve) =>
    setTimeout(() => resolve(DummyErrorRateChartData()), 1000)
  )
}

export function getThroughputTrendChartApi() {
  return new Promise((resolve) =>
    setTimeout(() => resolve(DummyThroughputChartData()), 1000)
  )
}
export function getProtocolsChartApi() {
  return new Promise((resolve) =>
    setTimeout(() => resolve(DummyProtocolsChartData()), 1000)
  )
}

export function getHeatMapChartApi() {
  return new Promise((resolve) =>
    setTimeout(() => resolve(DummyHeatMapChartData()), 1000)
  )
}
