<template>
  <div class="flex flex-col min-h-0 h-full">
    <MRow class="flex-1 min-h-0 mt-2" :gutter="0">
      <MCol :size="12" class="health-box-panel widget-border rounded">
        <Progress :items="getServiceHealth" text="Service Health" />
      </MCol>
      <MCol :size="12" class="health-box-panel widget-border mt-5 rounded">
        <Progress
          :items="getinfrastructrureHealth"
          text="Infrastructrure Health"
        />
      </MCol>
    </MRow>
    <MRow :gutter="0" class="flex-1 min-h-0 mt-5">
      <MCol :size="6" class="pr-2">
        <div class="health-box-panel widget-border rounded">
          <MRow :gutter="0" class="flex px-3 py-3 pb-0 pl-5 legend-strip">
            <MCol>
              <span class="widget-title">Top 5 Service By Response Time</span>
            </MCol>
            <MCol class="text-right">
              <MDropdown :options="actions" @change="actionChange($event)">
                <template v-slot:trigger>
                  <a class="text-neutral-light">
                    <MIcon name="ellipsis-v" />
                  </a>
                </template>
                <template v-slot:menu-item="{ item, selectItem }">
                  <span
                    class="flex items-center"
                    :class="{ 'text-secondary-red': item.isDanger }"
                    @click="selectItem(item)"
                  >
                    <MIcon v-if="item.icon" :name="item.icon" class="mr-2" />
                    {{ item.name }}
                  </span>
                </template>
              </MDropdown>
            </MCol>
          </MRow>

          <div>
            <FlotoPaginatedCrud
              as-table
              default-sort="-service"
              :columns="serviceColumns"
              :fetch-fn="getServiceResponseTime"
              :paging="false"
            />
          </div>
        </div>
      </MCol>
      <MCol :size="6" class="pl-2">
        <div class="health-box-panel widget-border rounded">
          <MRow :gutter="0" class="flex px-3 py-3 pb-0 pl-5 legend-strip">
            <MCol>
              <span class="widget-title">Top 5 Transactions By Duration</span>
            </MCol>
            <MCol class="text-right">
              <MDropdown :options="actions" @change="actionChange($event)">
                <template v-slot:trigger>
                  <a class="text-neutral-light">
                    <MIcon name="ellipsis-v" />
                  </a>
                </template>
                <template v-slot:menu-item="{ item, selectItem }">
                  <span
                    class="flex items-center"
                    :class="{ 'text-secondary-red': item.isDanger }"
                    @click="selectItem(item)"
                  >
                    <MIcon v-if="item.icon" :name="item.icon" class="mr-2" />
                    {{ item.name }}
                  </span>
                </template>
              </MDropdown>
            </MCol>
          </MRow>
          <div>
            <FlotoPaginatedCrud
              as-table
              default-sort="-transaction"
              :columns="transactionColumns"
              :fetch-fn="getTransactionsDurations"
              :paging="false"
            />
          </div>
        </div>
      </MCol>
    </MRow>

    <MRow :gutter="0" class="flex-1 min-h-0 mt-5 mb-5">
      <MCol :size="6" class="pr-2">
        <div class="health-box-panel widget-border rounded">
          <MRow :gutter="0" class="flex px-3 py-3 pb-0 pl-5 legend-strip">
            <MCol>
              <span class="widget-title">Alert Trend</span>
            </MCol>
            <MCol class="text-right">
              <MDropdown :options="actions" @change="actionChange($event)">
                <template v-slot:trigger>
                  <a class="text-neutral-light">
                    <MIcon name="ellipsis-v" />
                  </a>
                </template>
                <template v-slot:menu-item="{ item, selectItem }">
                  <span
                    class="flex items-center"
                    :class="{ 'text-secondary-red': item.isDanger }"
                    @click="selectItem(item)"
                  >
                    <MIcon v-if="item.icon" :name="item.icon" class="mr-2" />
                    {{ item.name }}
                  </span>
                </template>
              </MDropdown>
            </MCol>
          </MRow>
          <div class="flex mt-5">
            <div class="alert-chart flex-1">
              <Chart style="width: 98%" :options="chartOptions" />
            </div>
          </div>
        </div>
      </MCol>
      <MCol :size="6" class="pl-2">
        <div class="health-box-panel widget-border rounded">
          <MRow :gutter="0" class="flex px-3 py-3 pb-0 pl-5 legend-strip">
            <MCol>
              <span class="widget-title">Top 5 Deviating Services</span>
            </MCol>
            <MCol class="text-right">
              <MDropdown :options="actions" @change="actionChange($event)">
                <template v-slot:trigger>
                  <a class="text-neutral-light">
                    <MIcon name="ellipsis-v" />
                  </a>
                </template>
                <template v-slot:menu-item="{ item, selectItem }">
                  <span
                    class="flex items-center"
                    :class="{ 'text-secondary-red': item.isDanger }"
                    @click="selectItem(item)"
                  >
                    <MIcon v-if="item.icon" :name="item.icon" class="mr-2" />
                    {{ item.name }}
                  </span>
                </template>
              </MDropdown>
            </MCol>
          </MRow>
          <div>
            <FlotoPaginatedCrud
              as-table
              default-sort="-transaction"
              :columns="deviatingColumns"
              :fetch-fn="getDeviatingServices"
              :paging="false"
            />
          </div>
        </div>
      </MCol>
    </MRow>
  </div>
</template>

<script>
import Chart from '@components/chart/chart.vue'
// import ColumnAdapter from '@components/chart/column-adapter'
import Progress from '../components/dashboard/progress'
import {
  getServiceResponseTimeApi,
  getTransactionsDurationsApi,
  getDeviatingServicesApi,
  getServiceHealthApi,
  getInfrastructrureHealthApi,
  // getAlertTrandChartApi,
} from '../apm-api'
export default {
  name: 'Dashboard',
  components: { Progress, Chart },
  data() {
    this.serviceColumns = [
      {
        key: 'service',
        name: 'Service',
        sortable: true,
      },
      {
        key: 'throughput',
        name: 'Throughput',
        sortable: true,
      },
      {
        key: 'responseTime',
        name: 'Response Time',
        sortable: true,
      },
      {
        key: 'apdexScore',
        name: 'Apdex Score',
        sortable: true,
      },
      {
        key: 'failureRate',
        name: 'Failure Rate',
        sortable: true,
      },
    ]
    this.transactionColumns = [
      {
        key: 'transaction',
        name: 'Transaction',
      },
      {
        key: 'service',
        name: 'Service',
      },
      {
        key: 'duration',
        name: 'Duration',
      },
    ]
    this.deviatingColumns = [
      {
        key: 'service',
        name: 'Service',
      },
      {
        key: 'deviating',
        name: 'Deviating Metric',
      },
      {
        key: 'deviatingPercentage',
        name: 'Deviating Percentage',
      },
    ]
    return {
      serviceResponseTime: [],
      chartOptions: {},
      actions: [
        { key: 'delete', name: 'Delete', icon: 'trash-alt', isDanger: true },
      ],
    }
  },
  computed: {
    getServiceHealth() {
      return getServiceHealthApi()
    },
    getinfrastructrureHealth() {
      return getInfrastructrureHealthApi()
    },
  },
  created() {
    this.getDeviatingServices()
    this.getTransactionsDurations()
    this.getServiceResponseTime()
    this.getAlertTrandChart()
  },
  methods: {
    onServiceItemClick(item) {
      this.$router.push(this.$currentModule.getRoute('services'))
    },
    actionChange(val) {
      // TODO Progress Action Change
    },
    getServiceResponseTime() {
      return getServiceResponseTimeApi()
    },
    getDeviatingServices() {
      return getDeviatingServicesApi()
    },
    getTransactionsDurations() {
      return getTransactionsDurationsApi()
    },
    async getAlertTrandChart() {
      // let resultData = {}
      // await getAlertTrandChartApi().then((data) => {
      //   resultData = data.alertTrend
      // })
      // this.chartOptions = ColumnAdapter(
      //   {
      //     chart: {
      //       height: 205,
      //     },
      //     title: {
      //       text: '',
      //     },
      //     yAxis: {
      //       title: {
      //         text: '',
      //       },
      //     },
      //     legend: {
      //       verticalAlign: 'bottom',
      //       x: -30,
      //       y: 10,
      //     },
      //     plotOptions: {
      //       column: {
      //         stacking: 'normal',
      //       },
      //     },
      //     xAxis: {
      //       type: 'datetime',
      //       title: {
      //         text: 'Time',
      //       },
      //       dateTimeLabelFormats: {
      //         hour: '%H:%M',
      //       },
      //     },
      //   },
      //   resultData.data
      // )
    },
  },
}
</script>
