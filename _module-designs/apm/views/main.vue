<template>
  <FlotoModule :use-error-handler="false">
    <FlotoFixedView>
      <div class="flex flex-col h-full flex-1 content-inner-panel">
        <Transition name="settings-menu" mode="out-in">
          <MRow v-if="showMenu" :gutter="0" class="main-title-panel">
            <MCol>
              <h4 class="text-primary-alt">
                <MIcon name="apm" class="text-primary-alt" />
                APM
              </h4>
            </MCol>
          </MRow>
        </Transition>
        <Transition v-if="showMenu" name="placeholder">
          <MTab v-if="activeTab" v-model="activeTab" @change="handleTabChange">
            <MTabPane key="service-map" tab="Service Map" />
            <MTabPane key="dashboard" tab="Dashboard" />
            <MTabPane key="services" tab="Services" />
            <MTabPane key="transactions" tab="Transactions" />
            <MTabPane key="synthetic-monitoring" tab="Synthetic Monitoring" />
          </MTab>
        </Transition>
        <FlotoScrollView>
          <div class="flex flex-col flex-1 overflow-hidden">
            <Transition name="settings-sub-route" mode="out-in">
              <RouterView :key="$route.fullPath" />
            </Transition>
          </div>
        </FlotoScrollView>
      </div>
    </FlotoFixedView>
  </FlotoModule>
</template>

<script>
export default {
  name: 'ApmModule',
  provide() {
    const apmPageContext = {}
    Object.defineProperty(apmPageContext, 'isSettingMenuVisible', {
      enumerable: true,
      get: () => {
        return this.showMenu && this.menuVisible
      },
    })
    return { apmPageContext }
  },
  page() {
    return {
      title: 'APM',
    }
  },
  data() {
    return {
      showMenu: true,
      activeTab: undefined,
    }
  },
  watch: {
    $route: {
      immediate: true,
      handler(newValue) {
        if (newValue.meta && newValue.meta.showRootTabs !== false) {
          this.showMenu = true
        } else {
          this.showMenu = false
        }
        const activeTab = newValue.name.split('.')[1]
        if (activeTab !== this.activeTab) {
          this.activeTab = activeTab
        }
      },
    },
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      setTimeout(() => {
        vm.activeTab = to.name.split('.')[1]
      }, 300)
    })
  },
  methods: {
    handleTabChange(tab) {
      this.$router.push(this.$currentModule.getRoute(tab))
    },
  },
}
</script>
