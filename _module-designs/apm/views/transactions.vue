<template>
  <div class="mt-2">
    <FlotoPaginatedCrud
      as-table
      default-sort="-date"
      :columns="columns"
      :fetch-fn="getTransactionsServices"
    >
      <template v-slot:duration="{ item }"> {{ item.duration }} sec </template>
      <template v-slot:date="{ item }">
        <template v-if="item.date">{{ item.date | datetime }}</template>
      </template>
      <template v-slot:transaction="{ item }">
        <a @click="handleClick(item)">{{ item.transaction }}</a>
      </template>
      <template v-slot:statusCode="{ item }">{{
        statusCodeMap[item.statusCode]
      }}</template>
      <template v-slot:type="{ item }">{{ statusTypeMap[item.type] }}</template>
      <template v-slot:host="{ item }">{{ hostDataMap[item.host] }}</template>
    </FlotoPaginatedCrud>
  </div>
</template>

<script>
import {
  getTransactionsServicesApi,
  getTransactionsFieldsDataByIdApi,
} from '../apm-api'
import { getAllServicesApi } from '../apm-service-api'
import CloneDeep from 'lodash/cloneDeep'

const AVAILABLE_COLUMNS = [
  { key: 'date', name: 'Date', sortable: true },
  { key: 'transaction', name: 'Transaction' },
  { key: 'service', name: 'Service' },
  { key: 'duration', name: 'Duration' },
  { key: 'httpMethod', name: 'HTTP Method' },
  { key: 'statusCode', name: 'Status Code' },
  { key: 'status', name: 'Status' },
  { key: 'type', name: 'Type' },
  { key: 'host', name: 'Host' },
]

export default {
  name: 'Transactions',
  data() {
    this.availableColumns = CloneDeep(AVAILABLE_COLUMNS)
    this.serviceMap = {}
    this.statusCodeMap = {}
    this.statusTypeMap = {}
    this.hostDataMap = {}
    return {
      timeRange: 'last_1_Hour',
      serviceOptions: [],
      columnKeys: [
        'date',
        'transaction',
        'service',
        'duration',
        'httpMethod',
        'statusCode',
        'status',
        'type',
        'host',
      ],
      columns: [
        { key: 'date', name: 'Date', sortable: true, hidden: false },
        {
          key: 'transaction',
          name: 'Transaction',
          searchable: true,
          hidden: false,
        },
        { key: 'serviceId', name: 'Service', hidden: false },
        {
          key: 'duration',
          name: 'Duration',
          align: 'center',
          minWidth: '40px',
          hidden: false,
        },
        { key: 'httpMethod', name: 'HTTP Method', hidden: false },
        { key: 'statusCode', name: 'Status Code', hidden: false },
        { key: 'status', name: 'Status', hidden: false },
        { key: 'type', name: 'Type', hidden: false },
        { key: 'host', name: 'Host', hidden: false },
      ],
    }
  },
  watch: {
    timeRange(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.getTransactionsServices()
      }
    },
  },
  created() {
    this.getTransactionsServices()
  },
  mounted() {
    this.fetchAllServices()
    this.fetchFieldsDataById()
  },

  methods: {
    getTransactionsServices() {
      return getTransactionsServicesApi(this.timeRange).then((data) => {
        // To get the status code
        const transactionStatusData = []
        data.forEach(({ statusCode, id }) => {
          transactionStatusData.push({ key: id, text: statusCode })
          this.statusCodeMap[id] = statusCode
        })
        this.statusCodeSelection = transactionStatusData
        return data
      })
    },
    // To get the service data
    fetchAllServices() {
      return getAllServicesApi().then((data) => {
        const options = []
        data.forEach(({ service, id }) => {
          options.push({ key: id, text: service })
          this.serviceMap[id] = service
        })
        this.serviceOptions = options
      })
    },

    fetchFieldsDataById() {
      return getTransactionsFieldsDataByIdApi().then((data) => {
        // To get the host field data
        const hostData = []
        data.forEach(({ host, id }) => {
          hostData.push({ key: id, text: host })
          this.hostDataMap[id] = host
        })
        this.hostSelection = hostData
        // To get the type field data
        const transactionTypeData = []
        data.forEach(({ type, id }) => {
          transactionTypeData.push({ key: id, text: type })
          this.statusTypeMap[id] = type
        })
        this.statusTypeSelection = transactionTypeData
      })
    },
    handleClick(item) {
      this.$router.push(
        this.$currentModule.getRoute('transaction-detail', {
          params: {
            id: item.id,
            previousRoute: this.$route,
          },
        })
      )
    },
  },
}
</script>
