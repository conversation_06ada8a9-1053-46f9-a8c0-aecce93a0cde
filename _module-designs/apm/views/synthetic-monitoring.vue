<template>
  <div class="mt-3 pr-1">
    <FlotoPaginatedCrud
      as-table
      default-sort="-name"
      :columns="columns"
      :fetch-fn="getSyntheticMonitorings"
    >
      <template v-slot:americaAvailability="{ item }">
        {{ item.americaAvailability }}%
      </template>
      <template v-slot:americaResponseTime="{ item }">
        {{ item.americaResponseTime }} s
      </template>
      <template v-slot:americaCheckFrequency="{ item }">
        {{ item.americaCheckFrequency }} min
      </template>

      <template v-slot:asiaAvailability="{ item }">
        {{ item.asiaAvailability }}%
      </template>
      <template v-slot:asiaResponseTime="{ item }">
        {{ item.asiaResponseTime }} s
      </template>
      <template v-slot:asiaCheckFrequency="{ item }">
        {{ item.asiaCheckFrequency }} min
      </template>

      <template v-slot:europAvailability="{ item }">
        {{ item.europAvailability }}%
      </template>
      <template v-slot:europResponseTime="{ item }">
        {{ item.europResponseTime }} s
      </template>
      <template v-slot:europCheckFrequency="{ item }">
        {{ item.europCheckFrequency }} min
      </template>
    </FlotoPaginatedCrud>
  </div>
</template>

<script>
import CloneDeep from 'lodash/cloneDeep'
import { getSyntheticMonitoringApi } from '../apm-api'
const AVAILABLE_COLUMNS = [
  { key: 'name', name: 'Name' },
  { key: 'application', name: 'Application' },
  { key: 'americaAvailability', name: 'America Availability' },
  {
    key: 'americaResponseTime',
    name: 'America Response Time',
  },
  {
    key: 'americaCheckFrequency',
    name: 'America Check Frequency',
  },
  { key: 'asiaAvailability', name: 'Asia Availability' },
  { key: 'asiaResponseTime', name: 'Asia Response Time' },
  { key: 'asiaCheckFrequency', name: 'Asia Check Frequency' },
  { key: 'europAvailability', name: 'Europ Availability' },
  { key: 'europResponseTime', name: 'Europ Response Time' },
  { key: 'europCheckFrequency', name: 'Europ Check Frequency' },
]
export default {
  name: 'SyntheticMonitoring',
  data() {
    this.availableColumns = CloneDeep(AVAILABLE_COLUMNS)
    return {
      timeRange: 'last_1_Hour',
      columnKeys: [
        'name',
        'application',
        'americaAvailability',
        'americaResponseTime',
        'americaCheckFrequency',
        'asiaAvailability',
        'asiaResponseTime',
        'asiaCheckFrequency',
        'europAvailability',
        'europResponseTime',
        'europCheckFrequency',
      ],
      columns: [
        {
          key: 'name',
          name: 'Name',
          searchable: true,
          hidden: false,
        },
        {
          key: 'application',
          name: 'Application',
          searchable: true,
          hidden: false,
        },
        {
          key: 'americaAvailability',
          name: 'America Availability',
          hidden: false,
        },
        {
          key: 'americaResponseTime',
          name: 'America Response Time',
          hidden: false,
        },
        {
          key: 'americaCheckFrequency',
          name: 'America Check Frequency',
          searchable: true,
          hidden: false,
        },
        {
          key: 'asiaAvailability',
          name: 'Asia Availability',
          hidden: false,
        },
        {
          key: 'asiaResponseTime',
          name: 'Asia Response Time',
          hidden: false,
        },
        {
          key: 'asiaCheckFrequency',
          name: 'Asia Check Frequency',
          hidden: false,
        },
        {
          key: 'europAvailability',
          name: 'Europ Availability',
          hidden: false,
        },
        {
          key: 'europResponseTime',
          name: 'Europ Response Time',
          hidden: false,
        },
        {
          key: 'europCheckFrequency',
          name: 'Europ Check Frequency',
          hidden: false,
        },
      ],
    }
  },
  watch: {
    timeRange(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.getSyntheticMonitorings()
      }
    },
  },
  created() {
    this.getSyntheticMonitorings()
  },
  methods: {
    getSyntheticMonitorings() {
      return getSyntheticMonitoringApi(this.timeRange)
    },
  },
}
</script>
