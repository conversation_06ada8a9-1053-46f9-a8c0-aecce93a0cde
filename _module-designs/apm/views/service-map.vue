<template>
  <FlotoFixedView>
    <div class="flex flex-col min-h-0 h-full">
      <MRow class="flex-1 min-h-0">
        <MCol :size="activeNode ? 9 : 12" class="h-full graph-col py-2">
          <MRow class="mb-2">
            <MCol>
              <MInput
                v-model="searchTerm"
                class="search-box"
                placeholder="Search"
                @enter="applyFilter"
              >
                <template v-slot:prefix>
                  <MIcon name="search" />
                </template>
                <template v-if="searchTerm" v-slot:suffix>
                  <MIcon
                    name="times-circle"
                    class="text-neutral-light cursor-pointer"
                    @click="resetFilter"
                  />
                </template>
              </MInput>
            </MCol>
            <MCol class="flex items-center justify-end text-right">
              <span class="mx-4">Clear Status Node</span>
              <MSwitch
                v-model="showClearStatusNodes"
                checked-children="ON"
                un-checked-children="OFF"
              />
            </MCol>
          </MRow>
          <FlotoContentLoader :loading="loading">
            <!-- <MonitorGraph
              ref="monitorGraphRef"
              auto-center
              :layout="graphLayout"
              :graph-style="graphStyle"
              :filter-fn="filterNodes"
              highlight-active-node
              highlight-neighbors
              highlight-node-hover
              highlight-active-edge
              :active-node-id="activeNode && activeNode.id"
            >
              <MonitorGraphNode
                v-for="node in nodes"
                :id="node.id"
                :key="node.id"
                :name="node.name"
                :type="node.type"
                :status="node.status"
                :connections="node.connected"
                :class="[node.type, node.status.toLowerCase(), 'bordered']"
                @click="handleNodeClicked(node)"
              />
            </MonitorGraph> -->
          </FlotoContentLoader>
        </MCol>
        <MCol v-if="activeNode" :size="3" class="h-full flex flex-col py-2">
          <NodeViewer
            :key="activeNode.id"
            :node="activeNode"
            @close="clearActiveNode"
          />
        </MCol>
      </MRow>
    </div>
  </FlotoFixedView>
</template>

<script>
// import MonitorGraph from '@components/monitor-graph/monitor-graph.vue'
// import MonitorGraphNode from '@components/monitor-graph/monitor-graph-node.vue'
import NodeViewer from '../components/service-map/node-viewer'
import { getAllMonitoringNodes } from '../service-map-api'
import GraphStyle from '../components/service-map/graph-style'

export default {
  name: 'ServiceMap',
  components: { NodeViewer },
  data() {
    this.graphStyle = GraphStyle
    this.graphLayout = {
      name: 'euler',
      springLength: () => 150,
      randomize: true,
      animate: false,
    }
    return {
      showClearStatusNodes: true,
      searchTerm: '',
      nodes: [],
      loading: true,
      activeNode: null,
    }
  },
  watch: {
    searchTerm: 'applyFilter',
    showClearStatusNodes: 'applyFilter',
  },
  created() {
    this.getAllNodes()
  },
  methods: {
    filterNodes(node) {
      if (!this.showClearStatusNodes && node.status.toLowerCase() === 'clear') {
        return false
      }
      if (this.searchTerm) {
        return (
          node.name.toLowerCase().indexOf(this.searchTerm.toLowerCase()) >= 0
        )
      }
      return true
    },
    getAllNodes() {
      getAllMonitoringNodes().then((data) => {
        this.nodes = Object.freeze(data)
        this.loading = false
      })
    },
    resetFilter() {
      this.searchTerm = ''
      this.applyFilter()
    },
    applyFilter() {
      this.$refs.monitorGraphRef.applyFilter()
    },
    handleNodeClicked(node) {
      this.activeNode = node
    },
    clearActiveNode() {
      this.activeNode = null
    },
  },
}
</script>

<style lang="less" scoped>
.graph-col {
  border-right: 1px solid @neutral-lighter;
}
</style>
