<template>
  <FlotoPaginatedCrud
    :fetch-fn="fetchAllServices"
    :columns="columns"
    :as-table="viewOption === 'grid'"
    :per-page="25"
    class="mt-2"
    :item-filter="applyFilter"
  >
    <template v-slot:add-controls="{ filter, resetFilter, searchTerm }">
      <MRow :gutter="0">
        <MCol>
          <MInput
            :value="searchTerm"
            class="search-box"
            placeholder="Search"
            @update="filter"
          >
            <template v-slot:prefix>
              <MIcon name="search" />
            </template>
            <template v-if="searchTerm" v-slot:suffix>
              <MIcon
                name="times-circle"
                class="text-neutral-light cursor-pointer"
                @click="resetFilter"
              />
            </template>
          </MInput>
        </MCol>
        <MCol class="text-right">
          <MButton
            :shadow="false"
            shape="circle"
            class="mr-2"
            variant="transparent"
            @click="showFilters = !showFilters"
          >
            <MIcon name="filter" />
          </MButton>
          <MRadioGroup
            v-model="viewOption"
            :options="servicesDisplayOption"
            as-button
          >
            <template v-slot:option="{ option }">
              <MIcon
                class="mr-2"
                :name="option.value === 'grid' ? 'bars' : 'th-large'"
              />
            </template>
          </MRadioGroup>
        </MCol>
        <MCol :size="12" :class="{ 'mt-4': showFilters }">
          <FlotoExpand>
            <ServiceFilters
              v-if="showFilters"
              v-model="appliedFilters"
              @change="filter(null)"
            />
          </FlotoExpand>
        </MCol>
      </MRow>
    </template>
    <template v-slot:form>
      <span />
    </template>
    <template v-slot:item="{ item }">
      <MCol :key="item.key" :size="3" class="pr-5 pb-5 service-card-box">
        <ServiceCard :item="item" @item-click="redirectToServiceDetail(item)" />
      </MCol>
    </template>

    <!-- column slots -->
    <template v-slot:name="{ item }">
      <div class="flex">
        <Severity :severity="item.severity" class="mr-2" />
        <a @click="redirectToServiceDetail(item)">
          {{ item.name }}
        </a>
      </div>
    </template>

    <!-- pagination slot -->
    <template v-slot:pagination="{ pageInfo, navigateToPage, changePageSize }">
      <MRow class="my-2">
        <MCol>
          <MPagination
            v-model="pageInfo.current"
            size="small"
            :page-size="pageInfo.pageSize"
            show-size-changer
            :total="pageInfo.total"
            :show-total="
              (total, range) =>
                `Showing ${range[0]} to ${
                  range[1]
                } from total ${total} ${'Records'}`
            "
            @change="navigateToPage"
            @update:page-size="changePageSize"
          />
        </MCol>
        <MCol class="flex ml-4" auto-size>
          <div class="mr-2 flex items-center health-chart">
            <div class="round clear" /> Clear
          </div>
          <div class="mx-2 flex items-center health-chart">
            <div class="round warning" /> Warning
          </div>
          <div class="mx-2 flex items-center health-chart">
            <div class="round major" /> Major
          </div>
          <div class="mx-2 flex items-center health-chart">
            <div class="round critical" /> Critical
          </div>
          <div class="mx-2 flex items-center health-chart">
            <div class="round down" /> Down
          </div>
          <div class="mx-2 flex items-center health-chart">
            <div class="round maintainance" /> Maintainance
          </div>
          <div class="mx-2 flex items-center health-chart">
            <div class="round none" /> None
          </div>
        </MCol>
      </MRow>
    </template>
  </FlotoPaginatedCrud>
</template>
<script>
import { getAllServicesApi } from '../apm-service-api'
import Severity from '@components/severity'
import ServiceCard from '../components/services/service-card'
import ServiceFilters from '../components/services/service-filters'

export default {
  name: 'Services',
  components: { Severity, ServiceCard, ServiceFilters },
  data() {
    this.columns = [
      {
        key: 'name',
        name: 'Service',
        searchable: true,
      },
      {
        key: 'application',
        name: 'Application',
      },
      {
        key: 'responseTime',
        name: 'Response time',
      },
      {
        key: 'errorRate',
        name: 'Error rate',
      },
      {
        key: 'throughput',
        name: 'Throughput',
      },
      {
        key: 'action',
        name: 'Action',
        width: '120px',
      },
    ]
    this.servicesDisplayOption = [
      { value: 'grid', label: 'Grid' },
      { value: 'box', label: 'Box' },
    ]

    return {
      viewOption: 'grid',
      appliedFilters: {
        applications: '',
        responseTime: [0, 100],
        errorRate: [0, 100],
        throughput: [0, 100],
      },
      showFilters: false,
    }
  },
  created() {
    this.fetchAllServices()
  },

  methods: {
    fetchAllServices() {
      return getAllServicesApi()
    },
    applyFilter(item) {
      let isValid = true
      if (
        this.appliedFilters.application &&
        this.appliedFilters.application.length
      ) {
        if (
          (item.application || '')
            .toLowerCase()
            .indexOf(this.appliedFilters.application.toLowerCase()) === -1
        ) {
          return false
        }
      }

      // 2nd con
      if (
        item.responseTime <= this.appliedFilters.responseTime[0] ||
        item.responseTime >= this.appliedFilters.responseTime[1]
      ) {
        return false
      }
      // 3rd con
      if (
        item.errorRate <= this.appliedFilters.errorRate[0] ||
        item.errorRate >= this.appliedFilters.errorRate[1]
      ) {
        return false
      }

      if (
        item.throughput <= this.appliedFilters.throughput[0] ||
        item.throughput >= this.appliedFilters.throughput[1]
      ) {
        return false
      }

      return isValid
    },
    redirectToServiceDetail(item) {
      this.$router.push(
        this.$currentModule.getRoute('service-detail', {
          params: { id: item.id },
        })
      )
    },
  },
}
</script>
<style scoped lang="less">
.service-card-box:nth-child(4n + 4) {
  padding-right: 4px;
}
</style>
