<template>
  <FlotoFixedView :gutter="0">
    <MRow :gutter="0">
      <MCol>
        <FlotoPageHeader
          use-divider
          :back-link="backLink"
          :title="`${service.name}`"
        />
      </MCol>
    </MRow>
    <MTab v-model="activeTab">
      <MTabPane v-for="tab in tabs" :key="tab.key" :tab="tab.name" />
    </MTab>
    <MRow :gutter="0" class="flex-1 min-h-0">
      <MCol :size="12" class="h-100">
        <FlotoScrollView>
          <OverviewTab v-if="activeTab === 'overview'" :service="service" />
          <TransactionsTab
            v-else-if="activeTab === 'transactions'"
            :service="service"
          />
          <QueryTab v-if="activeTab === 'queries'" :service="service" />
          <ErrorTab v-if="activeTab === 'errors'" :service="service" />
        </FlotoScrollView>
      </MCol>
    </MRow>
  </FlotoFixedView>
</template>

<script>
import OverviewTab from '../components/service-detail/overview-tab'
import TransactionsTab from '../components/service-detail/transactions-tab'
import QueryTab from '../components/service-detail/query-tab'
import ErrorTab from '../components/service-detail/error-tab'

export default {
  name: 'ServiceDetail',
  components: { OverviewTab, TransactionsTab, QueryTab, ErrorTab },
  data() {
    this.tabs = [
      {
        key: 'overview',
        name: 'Overview',
      },
      {
        key: 'transactions',
        name: 'Transactions',
      },
      {
        key: 'queries',
        name: 'Queries',
      },
      {
        key: 'errors',
        name: 'Errors',
      },
    ]
    return {
      service: {
        name: 'Weather Service',
      },
      activeTab: 'overview',
    }
  },
  computed: {
    backLink() {
      return this.$currentModule.getRoute('services')
    },
  },
}
</script>
