<template>
  <FlotoFixedView>
    <MRow :gutter="0">
      <MCol>
        <FlotoPageHeader
          use-divider
          :back-link="backLink"
          :title="`${service.name}`"
        />
      </MCol>
    </MRow>
    <MRow>
      <MCol :size="3" class="small-label">Time</MCol>
      <MCol :size="3" class="small-label">Throughput</MCol>
      <MCol :size="3" class="small-label">Response Time</MCol>
      <MCol :size="3" class="small-label">Error Rate</MCol>
    </MRow>
    <MRow>
      <MCol :size="3" class="label-content">{{ transaction.time }}</MCol>
      <MCol :size="3" class="label-content">{{ transaction.throughput }}</MCol>
      <MCol :size="3" class="label-content">{{
        transaction.responseTime
      }}</MCol>
      <MCol :size="3" class="label-content">
        <MRow>
          <MCol>Rate</MCol>
          <MCol>
            <MRadioGroup
              v-model="viewOption"
              :options="transactionDisplayOption"
              as-button
              @change="handleChartTypeChange"
            >
              <template v-slot:option="{ option }">
                <MIcon
                  class="mr-2"
                  :name="option.value === 'grid' ? 'bars' : 'th-large'"
                />
              </template>
            </MRadioGroup>
          </MCol>
        </MRow>
      </MCol>
    </MRow>
    <MRow>
      <MCol>
        <TransactionOrganizationChart v-if="viewOption === 'treeView'" />
      </MCol>
      <MCol>
        <TransitionXRangeSeries v-if="viewOption === 'sequential'" />
      </MCol>
    </MRow>

    <MRow
      ><MCol>
        <FlotoPaginatedCrud
          as-table
          default-sort="-spanLayer"
          :columns="transactionColumns"
          :fetch-fn="getTransactionGridDetails"
        >
          <template v-slot:spanLayer="{ item }">
            <a @click="showDrawer(item)"> {{ item.spanLayer }} </a>
          </template>
        </FlotoPaginatedCrud>
        <TransactionDetailDrawer
          v-if="openDrawerForOperation !== null"
          :operation="openDrawerForOperation"
          :transaction="transaction"
          @hide="openDrawerForOperation = null"
        >
        </TransactionDetailDrawer>
      </MCol>
    </MRow>
  </FlotoFixedView>
</template>

<script>
import Omit from 'lodash/omit'
import { getTransactionOperationDetailApi } from './../transaction-api'
import TransactionDetailDrawer from './../components/transaction-detail/transaction-detail-drawer'
import TransactionOrganizationChart from './../components/transaction-detail/transaction-organization-chart'
import TransitionXRangeSeries from './../components/transaction-detail/transaction-x-range-series'
export default {
  name: 'TransactionDetail',
  components: {
    TransactionDetailDrawer,
    TransactionOrganizationChart,
    TransitionXRangeSeries,
  },

  data() {
    this.transactionColumns = [
      {
        key: 'spanLayer',
        name: 'Span Layer',
      },
      {
        key: 'duration',
        name: 'Duration',
      },
      {
        key: 'traceDuration',
        name: '% of Trace Duration',
      },
      {
        key: 'spanCount',
        name: 'Span Count',
      },
    ]
    this.transactionDisplayOption = [
      { value: 'sequential', label: 'Sequential' },
      { value: 'treeView', label: 'treeView' },
    ]
    return {
      viewOption: 'sequential',
      openDrawerForOperation: null,
      transaction: [],
      operations: [],
      loading: true,
      service: {
        name: 'Weather Service',
      },
    }
  },
  computed: {
    backLink() {
      return this.$currentModule.getRoute('transactions')
    },
  },
  created() {
    this.getTransactionGridDetails(this.$route.params.id)
  },
  methods: {
    getTransactionGridDetails(id) {
      return getTransactionOperationDetailApi().then((data) => {
        this.transaction = Object.freeze(Omit(data, ['operations']))
        this.operations = Object.freeze(data.operations)
        return data.operations
      })
    },
    showDrawer(item) {
      this.openDrawerForOperation = item
    },
    handleChartTypeChange(viewOption) {
      this.viewOption = viewOption
    },
  },
}
</script>
