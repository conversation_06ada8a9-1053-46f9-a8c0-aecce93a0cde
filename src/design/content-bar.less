.main-content-panel {
  float: left;
  width: 100%;

  & .content-inner-panel {
    float: left;
    width: 100%;
    min-height: 0;
    // padding: 0 0 0 8px;

    & .main-title-panel {
      float: left;
      width: 100%;
      padding-right: 15px;
      padding-left: 0;
      border-bottom: solid 1px var(--border-color);

      & .header-spacer {
        position: relative;
        top: -3px;
      }

      & h4 {
        // margin-bottom: 7px;
        color: var(--header-title-text-color);

        & i:not(.excluded-header-icon) {
          margin-right: 4px;
          font-size: 1.3rem;
          color: var(--header-title-text-color);
        }
      }
    }

    & .main-inner-title-panel {
      float: left;
      width: 100%;
      // padding-left: 4px;
      background: var(--page-background-color);
      border-bottom: solid 1px var(--border-color);

      & h4 {
        color: var(--header-title-text-color);

        & i:not(.excluded-header-icon) {
          margin-right: 4px;
          font-size: 1.6rem;
        }
      }
    }

    & .main-sub-title-panel {
      float: left;
      width: 100%;
      padding-top: 15px;
      padding-bottom: 5px;
      border-bottom: solid 1px var(--border-color);

      & h4 {
        float: left;
        color: var(--primary);

        & i:not(.excluded-header-icon) {
          margin-right: 4px;
          font-size: 1.6rem;
          color: var(--primary-alt);
        }
      }
    }

    & .right-side-content-view-panel {
      & .content-common-view-panel {
        float: left;
        width: 100%;
      }
    }
  }

  .custome-spacer {
    float: left;
    margin: 6px 6px 0 0;
  }

  & .list-slide-icon-arrow {
    position: absolute;
    top: 22px;
    left: -13px;
    z-index: 11;
    display: none;
    align-items: center;
    justify-content: center;
    width: 25px;
    height: 25px;
    line-height: 12px;
    color: var(--left-menu-arrow-color);
    text-align: center;
    cursor: pointer;
    background: var(--page-background-color);
    border: solid 1px var(--border-color);
    border-radius: 150px;
    box-shadow: 0 0 0 1px rgba(9, 30, 66, 0.03),
      0 2px 4px 1px rgba(9, 30, 66, 0.03);
    // box-shadow: 0 2px 5px -2px var(--neutral-darkest);
    &:hover {
      color: var(--white-regular);
      background: var(--primary);
      border-color: var(--primary);
    }

    & i.anticon {
      font-size: 0.6rem;

      &.menu-visible {
        position: relative;
        left: -1px;
      }
    }
  }

  .right-side-content-view-panel:hover {
    & .list-slide-icon-arrow {
      display: flex;
    }
  }

  & .menu-hidden {
    & .@{ant-prefix}-tabs-bar {
      margin-left: 16px;
    }

    & .list-slide-icon-arrow {
      top: 56px;
      left: 2px;
    }
  }
}

& .login-banner-slider-panel {
  & h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--page-text-color);
  }

  & p {
    font-size: 0.9rem;
    color: var(--page-text-color);
  }
}

.page-background {
  background: var(--page-background-color);
}

.affixed-sidebar-icon {
  position: absolute;
  top: 10px;
  right: -15px; //calc(-100% + 15px);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 25px;
  height: 25px;
  font-size: 0.6rem;
  line-height: 12px;
  color: var(--left-menu-arrow-color);
  text-align: center;
  cursor: pointer;
  background: var(--page-background-color);
  border: 1px solid var(--border-color);
  border-radius: 50%;
  box-shadow: 0 0 0 1px rgba(9, 30, 66, 0.03),
    0 2px 4px 1px rgba(9, 30, 66, 0.03);

  .menu-visible {
    margin-right: 2px;
  }
}
