// stylelint-disable-next-line
@panel-side-box-shadow: -1px 6px 6px 0 rgba(0, 0, 0, 0.2);

.text-inherit {
  color: inherit;
}

.blank-avatar {
  @apply bg-primary flex items-center !important;
}

.disabled {
  cursor: not-allowed;
}

.left-shadow-only {
  box-shadow: @panel-side-box-shadow;

  @apply relative;

  &::after {
    position: absolute;
    right: 0;
    bottom: -15px;
    left: 0;
    height: 15px;
    content: ' ';
    background-color: var(--page-background-color);
  }
}

.used-count-pill {
  line-height: @tag-height;
  color: var(--primary);
  background: var(--tag-bg);
  border: none;
}

.page-background-color {
  background: var(--page-background-color);
}

.page-text-color {
  color: var(--page-text-color);
}
