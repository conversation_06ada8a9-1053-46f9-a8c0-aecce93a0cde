.health-box-panel {
  float: left;
  width: 99.9%;
}

.widget-border {
  border: solid 1px var(--neutral-lighter);
}

.widget-title {
  position: relative;
  top: 2px;
  font-size: @text-sm;
  font-weight: normal;
}

.legend-strip {
  position: relative;

  &::after {
    position: absolute;
    top: 10px;
    left: -1px;
    width: 6px;
    height: 30px;
    content: '';
    background: var(--secondary-yellow);
  }
}
.@{ant-prefix}-slider {
  .@{ant-prefix}-slider-track {
    background-color: var(--primary-alt);
  }
  .@{ant-prefix}-slider-handle {
    border: solid 2px var(--primary-alt);
  }
  &:hover div.@{ant-prefix}-slider-track {
    background-color: var(--primary-alt);
  }
  &:hover div.@{ant-prefix}-slider-handle {
    border: solid 2px var(--primary-alt);
  }
  &:hover div.@{ant-prefix}-slider-handle:not(.@{ant-prefix}-tooltip-open) {
    border: solid 2px var(--primary-alt);
  }
}

.health-chart {
  & .own {
    float: left;
    height: 20px;
  }

  & .round {
    width: 12px;
    height: 12px;
    margin-right: 5px;
    border-radius: 50%;
  }

  & .clear {
    background: var(--secondary-green);
  }

  & .warning {
    background: var(--secondary-yellow);
  }

  & .major {
    background: var(--secondary-orange);
  }

  & .critical {
    background: var(--secondary-red);
  }

  & .down {
    background: var(--neutral-regular);
  }

  & .maintainance {
    background: var(--primary);
  }

  & .none {
    background: var(--neutral-dark);
  }
}

.metric-explorer-chart {
  &.has-actions {
    .remove-action-btn {
      opacity: 0;
      transition: transform opacity 0.5s linear;
      transform: translateX(20px);
    }

    &:hover {
      .remove-action-btn {
        opacity: 1;
        transform: translateX(0);
      }
    }
  }

  .chart-not-allowed-overlay {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 2;
    opacity: 0.6;
    backdrop-filter: blur(4px);
  }

  .chart-not-allowed-text {
    .chart-not-allowed-overlay;

    z-index: 3;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    opacity: 1;

    h3 {
      color: var(--secondary-red);
    }
  }
}

.hc-tooltip-bg {
  backdrop-filter: blur(15px);
}

@keyframes dash {
  0% {
    stroke-dashoffset: 100;
  }

  100% {
    stroke-dashoffset: 20;
  }
}

.animated-line {
  stroke-dasharray: 2;
  stroke-dashoffset: 12;
  animation: dash 15s linear infinite;
}

.bubbleLabelText {
  text {
    font-weight: 600 !important;
  }
}
