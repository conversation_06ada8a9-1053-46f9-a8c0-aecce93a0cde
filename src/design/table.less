.report-group-header {
  position: absolute;
  top: 0;
  right: 200px;
  bottom: 0;
  left: 0;
  display: flex;
  flex: 1;
  align-items: center;

  &.flap-count {
    right: 0;
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: flex-end;
  }
}

.sticky-headers {
  position: sticky;
  top: 0;
  z-index: 1;
  background-color: var(--page-background-color);

  td {
    background-color: var(--page-background-color);
  }
}

col.k-sorted,
th.k-sorted {
  background-color: transparent !important;
}

.hide-header {
  .k-grid-header {
    display: none;
  }
}

.item-list-table {
  float: left;
  width: 100%;

  & thead {
    & tr {
      min-height: 40px;

      & td {
        font-size: 0.7rem;
        font-weight: 500;
        color: var(--text-color-common-primery) !important;
        background: var(--grid-header-bg) !important;
        border-color: var(--border-color);
        border-left: none;

        @apply text-neutral-dark py-1 px-4 border-b border-l-0 border-r-0 border-t-0 border-solid;

        // stylelint-disable-next-line
        &.pr-0 {
          @apply pr-0 !important;
        }

        & .anticon {
          font-size: 0.7rem;
          opacity: 0.5;
        }

        & > div {
          display: inline-block;
        }
        & .@{ant-prefix}-checkbox-wrapper {
          float: left;
          padding-right: 0.5rem;
        }
      }
    }
  }

  & tbody {
    & tr {
      min-height: 40px;

      & td {
        font-size: 0.7rem;
        color: var(--text-color-common-primery);
        border-color: var(--border-color);

        @apply py-1 px-4 border-b border-l-0 border-r-0 border-t-0 border-solid;
      }
    }
  }

  &.with-out-border {
    tbody tr td {
      border-color: transparent !important;
    }
  }
}

.virtual-list-table-container table {
  width: 100%;
  table-layout: fixed;
}

.pager-border-t {
  margin-top: -1px;
  border-bottom: solid 1px var(--border-color);
}

.main-content-panel {
  & .columns-resizable {
    & div {
      & .resize-handler {
        top: 0;
        width: 1px;
        height: 38px;
        opacity: 0.4;
      }
    }

    & .discovery-result-table-header {
      &:hover {
        & .resize-handler {
          background: rgba(165, 186, 208, 0.4);
        }
      }
    }
  }

  .virtual-list-table-container,
  table.item-list-table {
    table-layout: fixed;

    & tr {
      height: auto !important;
      border-left: 0;
      transition: border-color 0.5s linear;

      & td.bg-neutral-lighter {
        background: var(--neutral-lightest);
      }

      &.active-cursor {
        border-left-color: var(--primary);
      }
    }

    td.sticky {
      position: sticky;
      top: 0;
      z-index: 1;
      background: white;
    }

    thead,
    tbody {
      // stylelint-disable-next-line
      tr {
        // stylelint-disable
        th.checkbox,
        td.checkbox {
          width: 60px;
          min-width: 60px;
          max-width: 60px;
          word-break: break-all;
        }

        // stylelint-enable
      }
    }

    &.resource-link {
      //a {
      //  display: block;
      //  color: @text-color;
      //  .textEllipsis();
      //
      //  &:hover {
      //    color: @text-color;
      //  }
      //}
    }

    .hover-action-container:not(.sticky-action) {
      opacity: 0;
    }

    tbody tr {
      &:hover {
        .hover-action-container {
          opacity: 1;
        }
      }
    }

    tbody > tr td {
      font-size: 0.7rem;
      border-color: var(--border-color);

      @apply py-2 px-4 border-b border-l-0 border-r-0 border-t-0 border-solid;

      & .@{ant-prefix}-form-item {
        & .@{ant-prefix}-form-item-control-wrapper {
          & .@{ant-prefix}-form-item-control {
            line-height: 30px;
            & .@{ant-prefix}-input-affix-wrapper {
              & .@{ant-prefix}-input {
                margin-bottom: 0;
              }
            }
          }
        }
      }

      a {
        color: var(--primary-alt);

        &.icon {
          @apply text-neutral text-lg mx-1;

          &:hover {
            @apply text-primary;
          }
        }

        &.@{ant-prefix}-dropdown-trigger {
          color: var(--neutral-light);
        }
      }

      & div {
        & .@{ant-prefix}-form-item {
          padding-bottom: 0;
          margin-bottom: 0;
        }
      }

      & .role-name {
        & .@{ant-prefix}-input {
          padding: 0;
          background: none;
          border: none;
        }

        & .@{ant-prefix}-input-suffix {
          display: none;
        }
      }

      & .@{ant-prefix}-switch {
        height: 23px;
        margin: 0;
      }

      & .user-count {
        display: inline-block;
      }

      & input.@{ant-prefix}-input {
        padding-left: 0;
      }

      & .form-spacer {
        & input.@{ant-prefix}-input {
          width: 40%;
        }
      }

      & button.button-transparent {
        width: auto;
        height: auto;
        padding: 0;
        margin: 0 0 0 7px;
        background: none;

        &:hover {
          outline: none;
          box-shadow: none;
        }
      }

      & .option-text {
        width: 100%;

        & .@{ant-prefix}-form-item {
          padding-bottom: 0;
          margin-bottom: 0;
        }

        & .@{ant-prefix}-input-affix-wrapper-sm {
          & input.@{ant-prefix}-input.@{ant-prefix}-input-sm {
            height: 35px;
            padding: 4px 11px 4px 0;
            margin: 0;
            line-height: initial;
          }
        }
      }

      & .@{ant-prefix}-select {
        & .@{ant-prefix}-select-selection--multiple {
          padding: 0;
          background: none;
          border: none;

          & ul {
            top: 5px;
            padding: 0;
            margin: 0 0 2px;

            & li {
              padding: @tag-pad;
              margin: @tag-mar;
              background: none;
              border-radius: 100px;

              & .@{ant-prefix}-select-selection__choice__content {
                height: @tag-height;
                line-height: @tag-height;
              }

              & .@{ant-prefix}-select-selection__choice__remove {
                right: 6px;
              }

              &:last-child {
                display: none;
              }

              & .dropdown-item {
                //& span.ml-2 {
                //  margin-left: 0;
                //}
              }
            }
          }

          & .@{ant-prefix}-select-arrow {
            display: none;
          }
        }
      }

      & .tag-user {
        position: relative;
        top: 3px;
        height: auto;
        padding: 0;
        margin: 0;
        font-size: @text-sm;
        background: none;
        border: none;
      }
    }

    // stylelint-disable-next-line
    thead > tr td {
      font-size: @text-sm;
      font-weight: 500;
      background: var(--grid-header-bg) !important;
      border-color: var(--border-color);
      border-left: none;

      @apply text-neutral-dark py-2 px-4 border-b border-t border-l-0 border-r-0 border-solid;
    }

    thead > tr th {
      font-size: @text-sm;
      font-weight: 500;
      background: var(--grid-header-bg) !important;
      border-color: var(--border-color);
      border-left: none;

      @apply text-neutral-dark py-2 px-4 border-b border-l-0 border-r-0 border-t-0 border-solid;
    }

    &.horizontal-list {
      tbody > tr > td:not(:last-child) {
        @apply border-r;
      }
    }

    &.bordered {
      thead,
      tbody {
        //stylelint-disable-next-line
        tr td {
          @apply border-r border-l border-t;
        }
      }
    }
  }
}

// Kendo Grid
& div.k-grid {
  margin-top: 15px;
  background: none;
  border: none;

  .k-icon {
    color: var(--neutral-light);
  }

  .k-indicator-container {
    color: var(--dropdown-text);

    .k-group-indicator,
    .k-chip-solid-base {
      color: var(--page-color);
      background: var(--dropdown-background);
      border-color: var(--border-color);
    }
  }

  td,
  th {
    &.text-right {
      text-align: right;
    }
  }

  th.checkbox,
  td.checkbox {
    width: 75px;
    min-width: 75px;
    max-width: 75px;
    word-break: break-all;
  }

  &.hide-expand-column {
    col[width='32px'] {
      width: 0 !important;
    }

    .k-detail-cell {
      border-left: solid 1px var(--table-details-bg) !important;
    }
  }

  & .k-grid-header {
    background: transparent !important;
    border-bottom-width: 0 !important;

    & .k-grid-header-wrap {
      border-right: none;

      & table {
        margin-bottom: 0;

        & thead {
          & tr {
            min-height: 40px;

            & th {
              // height: 40px;
              font-size: 0.75rem;
              font-weight: 600;
              color: var(--text-color-common-primery);
              text-transform: uppercase;
              letter-spacing: 0.25px;
              background: transparent !important;
              border-color: var(--border-color);
              border-left: none;

              @apply py-1 px-2 border-b border-t
                border-l-0 border-r-0 border-solid;
              & .@{ant-prefix}-checkbox-inner {
                top: 1px;
              }

              &:hover {
                background: var(--grid-header-hover-bg) !important;
              }

              &.text-center {
                text-align: center;
              }

              &.text-right {
                text-align: right;
              }
            }
          }
        }
      }
    }
  }

  & .k-grid-content {
    overflow-y: auto;
    background: none;

    & table {
      & tbody {
        .k-group-cell {
          background-color: transparent;
        }

        & tr {
          &.k-state-selected {
            td {
              background: var(--page-background-color);
            }
          }

          &.k-grouping-row {
            + tr .k-group-cell {
              background-color: transparent;
            }

            td {
              color: var(--primary-alt);
              background: var(--table-group-header-highlight) !important;

              .k-i-collapse,
              .k-i-expand {
                &::before {
                  color: var(--primary-alt);
                }
              }

              & p {
                & a.k-icon {
                  margin-right: 20px;
                }
              }
            }

            & td.checkbox {
              padding-left: 0;
            }
          }

          & td {
            height: 40px;
            font-size: 0.7rem;
            color: var(--text-color-common-primery);
            // background: var(--page-background-color);
            border-color: var(--code-tag-background-color);

            @apply py-1 px-2 border-b border-l-0 border-r-0 border-t-0 border-solid;

            & a {
              color: var(--text-color-common-link);

              &:hover {
                text-decoration: underline;
              }
            }

            & a + i {
              color: var(--text-color-common-link);
            }

            .has-color * {
              color: inherit;
            }

            & a.text-neutral-light {
              color: var(--neutral-light);

              &:hover {
                text-decoration: none;
              }
            }

            & a.text-neutral-light + i {
              color: var(--neutral-light);
            }

            & .@{ant-prefix}-form-item {
              padding-bottom: 0;
              margin-bottom: 0;
              & .@{ant-prefix}-input {
                margin-bottom: 0;
              }
            }
            & .@{ant-prefix}-input {
              padding-left: 0;
            }

            .dropdown-search {
              .@{ant-prefix}-input {
                padding-left: 30px;
              }
            }

            & .number-input {
              & .@{ant-prefix}-input {
                width: 60px;
              }
            }

            &.text-center {
              text-align: center;
            }

            &.text-right {
              text-align: right;
            }

            &:has(.report-pivot-group-header) {
              background-color: var(--action-dropdown-hover-bg);
            }

            .report-pivot-group-header {
              font-size: 1rem;
            }
          }

          &.k-master-row {
            & td:first-child {
              // background: var(--page-background-color) !important;
            }

            & td.checkbox {
              // padding-left: 5px;
            }
          }

          &:hover {
            background: none;
          }

          &.k-detail-row {
            & > td {
              border: solid 3px var(--table-details-bg);
              border-bottom-right-radius: 6px;
              border-bottom-left-radius: 6px;

              & h5 {
                font-weight: normal;
              }

              &.k-hierarchy-cell {
                border-right: none;
              }
            }
          }

          td.has-detail-row-open {
            background: var(--table-details-bg) !important;
            border-bottom: none;

            // &:nth-child(2) {
            // border-top-left-radius: 6px;
            // }

            // &:last-child {
            // border-top-right-radius: 6px;
            // }
          }
        }

        & .k-alt {
          background: none;
        }
      }
    }
  }

  & .k-grouping-header {
    background: var(--group-header-upper);
  }

  & .k-grouping-header + .k-grid-header {
    & .k-grid-header-wrap {
      & table {
        & thead {
          & th:nth-child(2) {
            // padding-left: 5px;
          }
        }
      }
    }
  }

  .k-pager-wrap {
    color: var(--page-text-color);
  }

  & .k-grid-pager {
    padding-right: 0;
    padding-left: 0;
    font-size: 0.75rem;
    background: none;
    border-top: solid 1px var(--border-color);

    & a.k-pager-nav {
      font-weight: bold;
      color: var(--page-text-color);

      &:hover {
        font-weight: bold;
        color: var(--neutral-regular);
        background: var(--neutral-lightest);
        border-radius: 2px;
        // box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      }

      &:focus {
        outline: none;
        box-shadow: none;
      }
    }

    & ul.k-pager-numbers {
      & li {
        margin-right: 3px;

        & .k-link {
          font-weight: 400;
          color: var(--page-text-color);

          &:hover {
            font-weight: 400;
            color: var(--pagination-active-text);
            background: var(--pagination-active-bg);
            border-radius: 2px;
            // box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            &:focus {
              outline: none;
              box-shadow: none;
            }
          }
        }

        & .k-state-selected {
          font-weight: 400;
          color: var(--pagination-active-text);
          background: var(--pagination-active-bg);
          border-radius: 2px;
          // box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
          &:focus {
            outline: none;
            box-shadow: none;
          }
        }
      }
    }

    & .k-pager-sizes {
      & .k-dropdown-wrap {
        background: none;
        border-top: var(--border-color);
      }

      & select {
        padding: 2px 5px;
        font-weight: 400;
        color: var(--pagination-select-text);
        background: var(--pagination-select-bg);
        border: none;
        border-color: var(--border-color);
        border-radius: 2px;
        // box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        &:focus {
          outline: none;
          box-shadow: none;
        }
      }
    }
  }
}

.business-hours-table {
  & table.item-list-table {
    & thead {
      & tr {
        & td {
          border: none;
          border-bottom: solid 1px var(--border-color);
        }

        & td:first-child {
          border-right: solid 1px var(--border-color);
        }
      }
    }

    & tbody {
      & tr {
        & td {
          border: none;
          border-bottom: solid 1px var(--border-color);
        }

        & td:first-child {
          background: var(--grid-header-bg) !important;
          border-right: solid 1px var(--border-color);
        }
      }
    }
  }
}

// .stripped-grid {
//   padding-right: 0.5rem;
//   padding-left: 0.5rem;

//   div.k-grid .k-grid-content {
//     table {
//       border-spacing: 0 0.5rem;
//       border-collapse: separate;

//       tbody tr.k-master-row td {
//         background: var(--code-tag-background-color) !important;
//         border-width: 0;
//       }
//     }
//   }

//   .font-lg {
//     font-size: 15px;
//   }

//   .font-bold {
//     font-weight: 500 !important;
//   }
// }

.k-grid {
  .k-grid-content table tbody tr td {
    &.font-lg {
      font-size: 15px;
    }

    &.font-bold {
      font-weight: 500 !important;
    }
  }
}

.hide-grouping {
  .k-grid {
    .k-grouping-header {
      display: none;
    }
  }
}

.k-pager-sizes .k-input-inner {
  font-size: 0.75rem;
}
