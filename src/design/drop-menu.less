.header-user-dropmenu {
  // top: 59px !important;
  width: @header-drop-width;
  padding: 0;

  & .@{ant-prefix}-popover-content {
    & .@{ant-prefix}-popover-arrow {
      display: none;
    }

    & .@{ant-prefix}-popover-inner {
      & .@{ant-prefix}-popover-inner-content {
        padding: 0;

        & .user-name-title {
          font-size: 1rem;
        }

        & .user-name-sub-title {
          font-size: 0.7rem;
        }
      }
    }
  }
}

.picker-action-dropdown {
  .@{ant-prefix}-popover-arrow {
    display: none;
  }
  .@{ant-prefix}-popover-inner {
    position: relative;
    top: -40px;

    @apply shadow-lg;
  }
  .@{ant-prefix}-popover-inner-content {
    padding: 0;
  }

  & ul.action-dropdown {
    right: 0;
    padding: 30px 0 5px;
    background: var(--action-dropdown-backgroud);
    border: solid 1px var(--border-color);
    & li.@{ant-prefix}-dropdown-menu-item {
      height: unset;
      padding: @droplist-padd;
      margin: 0;
      line-height: 22px;
      color: var(--action-dropdown-text);
      background: var(--action-dropdown-backgroud);

      a {
        color: var(--action-dropdown-text);
        background: var(--action-dropdown-backgroud);

        &::before,
        &::after {
          display: none;
        }
      }

      &::after {
        display: none;
      }

      &:hover {
        color: var(--action-dropdown-text);
        background: var(--action-dropdown-hover-bg);

        a {
          color: var(--action-dropdown-text);
          background: var(--action-dropdown-hover-bg);
        }
      }

      &-divider {
        background: var(--action-dropdown-divider);
      }
    }

    &::after {
      position: absolute;
      top: 9px;
      right: 8px;
      width: 5px;
      height: 20px;
      content: url("data:image/svg+xml,%3Csvg aria-hidden='true' focusable='false' data-prefix='fas' fill='currentColor' data-icon='ellipsis-v' role='img' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 192 512' class='svg-inline--fa fa-ellipsis-v fa-w-6'%3E%3Cpath d='M96 184c39.8 0 72 32.2 72 72s-32.2 72-72 72-72-32.2-72-72 32.2-72 72-72zM24 80c0 39.8 32.2 72 72 72s72-32.2 72-72S135.8 8 96 8 24 40.2 24 80zm0 352c0 39.8 32.2 72 72 72s72-32.2 72-72-32.2-72-72-72-72 32.2-72 72z' class=''%3E%3C/path%3E%3C/svg%3E%0A");
      background-repeat: no-repeat;
      opacity: 0.3;
    }
  }
  &.@{ant-prefix}-popover-placement-topRight {
    padding: 0;
    .@{ant-prefix}-popover-inner {
      top: unset;
      bottom: -50px;
    }

    & ul.action-dropdown {
      padding: 5px 0 30px;

      &::after {
        top: unset;
        bottom: 9px;
      }
    }
  }
}

[data-theme='dark-theme'] {
  .picker-action-dropdown {
    & ul.action-dropdown {
      &::after {
        content: url("data:image/svg+xml,%3Csvg aria-hidden='true' focusable='false' data-prefix='fas' data-icon='ellipsis-v' role='img' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 192 512' class='svg-inline--fa fa-ellipsis-v fa-w-6'%3E%3Cpath d='M96 184c39.8 0 72 32.2 72 72s-32.2 72-72 72-72-32.2-72-72 32.2-72 72-72zM24 80c0 39.8 32.2 72 72 72s72-32.2 72-72S135.8 8 96 8 24 40.2 24 80zm0 352c0 39.8 32.2 72 72 72s72-32.2 72-72-32.2-72-72-72-72 32.2-72 72z' fill='white' class=''%3E%3C/path%3E%3C/svg%3E%0A");
        opacity: 0.7;
      }
    }
  }
}
