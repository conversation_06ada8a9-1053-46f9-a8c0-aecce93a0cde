:root {
  --primary: #099dd9;
  --primary-alt: #3279be;
  --neutral-darker: #14212d;
  --neutral-dark: #364658;
  --neutral-regular: #7b8fa5;
  --neutral-light: #a5bad0;
  --neutral-darkest: #000;
  --neutral-lightest: #eef2f6;
  --neutral-lighter: #dee5ed;
  --secondary-green: #89c540;
  --secondary-yellow: #f5bc18;
  --secondary-orange: #f58518;
  --secondary-red: #f04e3e;
  --secondary-red-dark: #c84235;
  --secondary-red-light: #f17a73;
  --page-background-color: #fff;
  --faded-page-background-color: rgba(255, 255, 255, 0.4);
  --page-text-color: #364658;
  --input-placeholder-color: fade(#364658, 50);
  --white-regular: var(--page-background-color);
  --list-menu-hover: #e7f5fb;
  --table-group-header-highlight: #d6edf8;
  --mesasge-success: #e7f4d9;
  --mesasge-error: #fcdbd8;
  --active-text-color: var(--page-background-color);
  --primary-alt-lighter: #e0ebf5;
  --secondary-green-lighter: #edf7e2;
  --neutral-regular-lighter: #ebeef2;
  --neutral-theme-color: #7b8fa5;

  // Severity Color
  --severity-down-lighter: fade(#e72b25, 20);
  --severity-down-lightest: fade(#e72b25, 20);
  --severity-down: #c71c16;
  --severity-down-darker: darken(#e72b25, 20);
  --severity-critical-lighter: fade(#f04e3e, 20);
  --severity-critical-lightest: fade(#f04e3e, 20);
  --severity-critical: #f25c4e;
  --severity-critical-darker: darken(#f04e3e, 20);
  --severity-major-lighter: fade(#f58518, 20);
  --severity-major-lightest: fade(#f58518, 20);
  --severity-major: #f58518;
  --severity-major-darker: darken(#f58518, 20);
  --severity-warning-lighter: fade(#f5bc18, 20);
  --severity-warning-lightest: fade(#f5bc18, 20);
  --severity-warning: #f5bc18;
  --severity-warning-darker: darken(#f5bc18, 20);
  --severity-clear-lighter: fade(#89c540, 20);
  --severity-clear-lightest: fade(#89c540, 20);
  --severity-clear: #89c540;
  --severity-clear-darker: darken(#89c540, 20);
  --severity-up-lighter: fade(#89c540, 20);
  --severity-up-lightest: fade(#89c540, 20);
  --severity-up: #89c540;
  --severity-up-darker: darken(#89c540, 20);
  --severity-maintenance-lighter: fade(#099dd9, 20);
  --severity-maintenance-lightest: fade(#099dd9, 20);
  --severity-maintenance: #099dd9;
  --severity-maintenance-darker: darken(#099dd9, 20);
  --severity-stop-lighter: fade(#8d3abc, 20);
  --severity-stop-lightest: fade(#8d3abc, 20);
  --severity-stop: #8d3abc;
  --severity-stop-darker: darken(#8d3abc, 20);
  --severity-unreachable-lighter: fade(#8d3abc, 20);
  --severity-unreachable-lightest: fade(#8d3abc, 20);
  --severity-unreachable: #8d3abc;
  --severity-unreachable-darker: darken(#8d3abc, 20);
  --severity-disable-lighter: fade(#808080, 20);
  --severity-disable-lightest: fade(#808080, 20);
  --severity-disable: #808080;
  --severity-disable-darker: darken(#808080, 20);
  --severity-unknown-lighter: fade(#d3d3d3, 20);
  --severity-unknown-lightest: fade(#d3d3d3, 20);
  --severity-unknown: #d3d3d3;
  --severity-unknown-darker: darken(#d3d3d3, 20);
  --severity-suspended-lighter: fade(#555, 20);
  --severity-suspended-lightest: fade(#555, 20);
  --severity-suspended: #555;
  --severity-suspended-darker: darken(#555, 20);
  --severity-none-lighter: fade(#7b8fa5, 20);
  --severity-none-lightest: fade(#7b8fa5, 20);
  --severity-none: #7b8fa5;
  --severity-none-darker: darken(#7b8fa5, 20);

  // Navigation Bar Panel
  --nav-panel-bg: white;
  --nav-hover-bg: var(--neutral-dark);
  --nav-text-color: var(--page-text-color);
  --nav-selected-text-color: white;
  --nav-divider-bg: var(--primary);

  // user dropdown
  --user-dropdown-button-bg: var(--neutral-lightest);
  --user-dropdown-button-text: var(--page-text-color);
  --user-dropdown-button-hover-bg: var(--neutral-lighter);
  --user-dropdown-button-hover-text: var(--page-text-color);
  --dropdown-btn-background: linear-gradient(0deg, #f2f4f7 0%, #fff 100%);

  // Left List Content
  --left-menu-text-color: var(--neutral-dark);
  --left-menu-hover-bg: var(--list-menu-hover);
  --left-menu-text-color-hover: var(--primary);
  --left-menu-text-hover-color: var(--primary);
  --left-menu-arrow-color: var(--neutral-light);
  --left-menu-arrow-opacity: 1;

  // Main Content
  --common-main-bg: var(--neutral-lightest);
  --common-widget-bg: var(--page-background-color);

  // Header Title
  --header-title-text-color: var(--primary-alt);

  // Common Border Color
  --border-color: var(--neutral-lighter);
  --transparent-border-color: var(--neutral-lighter);
  --widget-border-color: #e2e5ed;

  // Common Text Color
  --text-neutral-ligher: var(--neutral-light);
  --text-color-common-primery: var(--neutral-dark);
  --text-color-common-secondary: var(--neutral-regular);
  --text-color-common-link: var(--primary);

  // highcharts range
  --hc-range-background: #099dd9;
  --chart-legend-color: rgba(54, 70, 88, 0.8);
  // --chart-font-family: 'IAWriter Duospace', sans-serif;
  --chart-font-family: 'Poppins', sans-serif;
  --chart-border-color: fade(#000, 10%);

  // Table Grid
  --grid-header-bg: var(--neutral-lightest);
  --grid-header-hover-bg: var(--neutral-lighter);
  --pagination-active-bg: var(--neutral-lighter);
  --pagination-active-text: var(--neutral-regular);
  --pagination-select-bg: var(--neutral-lighter);
  --pagination-select-text: var(--neutral-regular);
  --group-header-upper: #f6f6f6;
  --table-details-bg: fade(#099dd9, 30%);
  --grid-header-solid-bg: var(--neutral-lightest);

  // Form Element
  --checkbox-bg: var(--white-regular);
  --checkbox-checked-color: var(--neutral-regular);
  --input-suffix-bg: var(--neutral-lightest);
  --input-suffix-text: var(--neutral-regular);
  --search-icon: var(--neutral-dark);
  --default-tag-color: var(--neutral-lighter);
  --tag-bg: rgba(9, 157, 217, 0.3);
  --tag-color: var(--neutral-regular);
  --tag-border: var(--neutral-lighter);
  --default-tag-bg: rgba(9, 157, 217, 0.3);
  --switch-bg: var(--page-background-color);
  --switch-border: var(--neutral-lighter);
  --input-text-color: var(--page-text-color);
  --input-addon-color: var(--neutral-light);
  --text-input-bg: var(--page-background-color);
  --group-list-bg: var(--neutral-lightest);
  --group-list-hover-bg: var(--left-menu-hover-bg);
  --group-icon-color: var(--neutral-light);
  --group-text-color: var(--neutral-dark);
  --radio-btn-box-bg: var(--page-background-color);
  --radio-btn-box-selected-bg: var(--primary);
  --radio-btn-box-border-color: var(--border-color);
  --disabled-checkbox-opacity: 0.8;

  // tabs
  --tabs-text-color: var(--neutral-regular);

  // notification
  --toast-background-color: var(--white-regular);
  --toast-text-color: var(--neutral-darkest);

  //modal
  --modal-background-color: var(--white-regular);
  --modal-text-color: var(--neutral-darkest);

  // drawer
  --drawer-background-color: var(--page-background-color);

  // code color
  --code-tag-background-color: var(--neutral-lightest);
  --code-tag-text-color: var(--page-text-color);

  // tooltip
  --tooltip-text-color: var(--white-regular);
  --tooltip-background-color: var(--neutral-darker);
  --tooltip-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  --chart-tooltip-background: rgba(255, 255, 255, 0.8);

  // action dropdown
  --action-dropdown-backgroud: var(--page-background-color);
  --action-dropdown-text: var(--neutral-regular);
  --action-dropdown-hover-bg: var(--neutral-lighter);
  --action-dropdown-divider: var(--neutral-lighter);

  // dropdown
  --dropdown-background: var(--page-background-color);
  --dropdown-text: var(--neutral-regular);
  --hieararchy-border-color: var(--border-color);

  // drawer
  --drawer-mask-color: rgba(0, 0, 0, 0.45);

  // progress bar
  --progress-bg: var(--neutral-lighter);

  // buttons
  --primary-button-bg: var(--primary);
  --primary-button-text: var(--white-regular);
  --primary-button-hover-bg: fade(#099dd9, 70);
  --primary-button-hover-text: var(--white-regular);
  --error-button-bg: var(--secondary-red);
  --error-button-text: var(--white-regular);
  --error-button-hover-bg: var(--secondary-red-dark);
  --error-button-hover-text: var(--white-regular);
  --success-button-bg: var(--secondary-green);
  --success-button-text: var(--white-regular);
  --success-button-hover-bg: var(--secondary-green);
  --success-button-hover-text: var(--white-regular);
  --default-button-bg: var(--white-regular);
  --default-button-text: var(--page-text-color);
  --default-button-border: var(--neutral-lighter);
  --default-button-hover-bg: var(--neutral-lightest);
  --default-button-hover-text: var(--page-text-color);
  --default-button-hover-border: var(--neutral-lighter);
  --neutral-button-bg: var(--neutral-lighter);
  --neutral-button-text: var(--neutral-regular);
  --neutral-button-hover-bg: var(--neutral-lightest);
  --neutral-button-hover-text: var(--neutral-regular);
  --outline-button-bg: transparent;
  --outline-button-text: var(--primary);
  --outline-button-hover-bg: fade(#099dd9, 20);
  --outline-button-hover-text: var(--primary);
  --error-outline-button-bg: transparent;
  --error-outline-button-text: var(--secondary-red);
  --error-outline-button-hover-bg: fade(#f04e3e, 20);
  --error-outline-button-hover-text: var(--secondary-red);
  --success-outline-button-bg: transparent;
  --success-outline-button-text: var(--secondary-green);
  --success-outline-button-hover-bg: fade(#89c540, 20);
  --success-outline-button-hover-text: var(--secondary-green);
  --button-disabled-bg: var(--neutral-lightest);
  --button-disabled-border: var(--neutral-lighter);
  --button-disabled-text: rgba(0, 0, 0, 0.5);
  --button-transparent-text: var(--neutral-regular);
  --button-transparent-hover-text: var(--neutral-regular);

  // misc.
  --filter-viewer-bg-color: var(--white-regular);
  // notification dropdown
  --notification-dropdown-header: #dfe5ec;
  --notification-dropdown-footer: #eff2f6;
  --notification-dropdown-background: white;
  --notification-dropdown-background-hover: #eff2f6;
  --notification-dropdown-border-color: #dfe5ec;
  --notification-dropdown-datetime-color: #7b8fa5;
  --alert-notification-popup-background-color: #fff;
  --alert-notification-popup-border-color: #eff2f6;

  // screenshot share
  --screenshot-share-overlay-bg: rgba(0, 0, 0, 0.4);
  --screenshot-share-form-bg: var(--page-background-color);

  // template widget specific
  --partition-icon-background-color: var(--neutral-lighter);
  --partition-icon-text-color: var(--page-text-color);
  --availability-icon-background-color: var(--neutral-lighter);
  --availability-icon-text-color: var(--page-text-color);
  --switch-port-view-bg: #7b8fa5;
  --gauge-base-color: #dee5ed;
  --gauge-text-color: var(--primary-alt);

  // topology variables
  --topology-graph-tooltip-bg: rgba(30, 30, 30, 0.9);
  --topology-graph-tooltip-text-color: #fff;
  --graph-bg: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iMTAiIHZpZXdCb3g9IjAgMCAxMCAxMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNEg2VjZINFY0WiIgZmlsbD0iI0RGRTVFQyIvPgo8L3N2Zz4K');

  // overlay
  --overlay-bg: rgba(0, 0, 0, 0.25);

  // dashboard
  --dashboard-background: #f9fafb;
  --widget-background-color: var(--page-background-color);
  --timerange-background-color: var(--neutral-lightest);
  --timerange-text-color: var(--neutral-regular);
  --widget-background: var(--page-background-color);
  --widget-box-shadow: none;

  // tour section background
  --tour-metric-bg-color: #cfdce6;
  --tour-log-bg-color: #d6f1f7;
  --tour-flow-bg-color: #def1e2;

  //stacked switch port backgrounds
  --status-up: rgb(110, 158, 51);
  --status-down: rgb(192, 62, 50);

  // Chart Colors
  --chart-null-color: var(--neutral-button-bg);

  // filter background
  --filter-background: var(--page-background-color);

  //license background
  --license-background: var(--neutral-lightest);

  // topology background color
  --topology-background: var(--page-background-color);

  // gridLineColor
  --chart-grid-line-color: rgba(223, 229, 236, 0.4);

  // lineColor
  --bottom-line-color: rgba(223, 229, 236, 0.6);

  // chart-type-icon
  --chart-type-icon: #7b8fa5;

  // chart-line-type-icon
  --chart-line-type-icon: #a5bad0;

  // policy-border-color
  --policy-border-color: var(--transparent-border-color);

  //disk spacefull background
  --disk-space-full-background: var(--neutral-lightest);
  --status-progress-color: transparent;
  --map-line-color: #bbc8d6;
  --timeline-scrollbar-background-color: var(--neutral-lighter);
}

[data-theme='dark-theme'] {
  --primary: #099dd9;
  --primary-alt: #3279be;
  --neutral-darker: #14212d;
  --neutral-dark: #1e1e30;
  --neutral-regular: #7b8fa5;
  --neutral-light: #a5bad0;
  --neutral-darkest: #000;
  --neutral-lightest: var(--neutral-dark);
  --neutral-lighter: #22223c;
  --secondary-green: #89c540;
  --secondary-yellow: #f5bc18;
  --secondary-orange: #f58518;
  --secondary-red: #f04e3e;
  --secondary-red-dark: #c84235;
  --secondary-red-light: #f17a73;
  --page-background-color: rgb(22, 22, 22);
  --faded-page-background-color: rgba(30, 30, 30, 0.4);
  --faded-text-color: rgba(30, 30, 30, 0.4);
  --page-text-color: rgba(255, 255, 255, 0.8);
  --input-placeholder-color: fade(#fff, 50);
  --white-regular: rgba(255, 255, 255, 0.8);
  --list-menu-hover: #e7f5fb;
  --table-group-header-highlight: var(--neutral-darker);
  --mesasge-success: #e7f4d9;
  --mesasge-error: #fcdbd8;
  --focus-color: #204ca2;
  --secondery-focus-bg: rgb(40, 40, 40);
  --active-text-color: #fff;
  --primary-alt-lighter: #e0ebf5;
  --secondary-green-lighter: #edf7e2;
  --neutral-regular-lighter: #ebeef2;
  --neutral-theme-color: #a5bad0;

  // severity
  --severity-down-lightest: fade(#e72b25, 20);
  --severity-critical-lightest: fade(#f04e3e, 20);
  --severity-major-lightest: fade(#f58518, 20);
  --severity-warning-lightest: fade(#f5bc18, 20);
  --severity-clear-lightest: fade(#89c540, 20);
  --severity-up-lightest: fade(#89c540, 20);
  --severity-maintenance-lightest: fade(#099dd9, 20);
  --severity-stop-lightest: fade(#8d3abc, 20);
  --severity-unreachable-lightest: fade(#8d3abc, 20);
  --severity-disable-lightest: fade(#808080, 20);
  --severity-unknown-lightest: fade(#d3d3d3, 20);
  --severity-suspended-lightest: fade(#555, 20);
  --severity-none-lightest: fade(#7b8fa5, 20);

  // Navigation Bar Panel
  --nav-panel-bg: rgb(30, 30, 30);
  --nav-hover-bg: rgb(15, 15, 15);
  --nav-text-color: var(--white-regular);
  --nav-selected-text-color: white;
  --nav-divider-bg: var(--primary);

  // Main Content
  --common-main-bg: var(--nav-panel-bg);
  --common-widget-bg: var(--nav-panel-bg);

  // user dropdown colors
  --user-dropdown-button-bg: rgb(40, 40, 40);
  --user-dropdown-button-text: var(--page-text-color);
  --user-dropdown-button-hover-bg: var(--secondery-focus-bg);
  --user-dropdown-button-hover-text: var(--page-text-color);
  --dropdown-btn-background: linear-gradient(
    180deg,
    rgba(70, 68, 68, 0.26) 0%,
    rgba(41, 41, 41, 0.49) 100%
  );

  // Left List Content
  --left-menu-text-color: rgba(255, 255, 255, 0.65);
  --left-menu-hover-bg: var(--secondery-focus-bg);
  --left-menu-text-color-hover: var(--white-regular);
  --left-menu-text-hover-color: white;
  --left-menu-arrow-color: rgba(255, 255, 255, 0.65);
  --left-menu-arrow-opacity: 0.7;

  // Commin Border Color
  --border-color: rgb(60, 60, 60);
  --transparent-border-color: 'transparent';
  --widget-border-color: #323232;

  // Header Title
  --header-title-text-color: var(--primary);

  // Common Text Color
  --text-neutral-ligher: var(--neutral-light);
  --text-color-common-primery: var(--white-regular);
  --text-color-common-secondary: var(--neutral-light);
  --text-color-common-link: var(--primary);

  // highcharts range
  --hc-range-background: #099dd9;
  --chart-legend-color: rgba(255, 255, 255, 0.8);
  --chart-border-color: fade(#fff, 50%);

  // Table Grid
  --grid-header-bg: rgba(70, 70, 70, 0.4);
  --grid-header-hover-bg: rgb(40, 40, 40);
  --pagination-active-bg: var(--left-menu-hover-bg);
  --pagination-active-text: var(--page-text-color);
  --pagination-select-bg: var(--left-menu-hover-bg);
  --pagination-select-text: var(--page-text-color);
  --group-header-upper: var(--neutral-darker);
  --table-details-bg: rgb(34, 59, 89);
  --grid-header-solid-bg: rgba(70, 70, 70, 1);

  // Form Element
  --checkbox-bg: rgb(100, 100, 100);
  --checkbox-checked-color: var(--page-text-color);
  --input-suffix-bg: rgb(45, 45, 45);
  --input-suffix-text: var(--regular-white);
  --search-icon: var(--list-menu-hover);
  --tag-bg: rgba(9, 157, 217, 0.2);
  --tag-color: var(--page-text-color);
  --default-tag-color: rgba(255, 255, 255, 0.2);
  --default-tag-bg: rgba(9, 157, 217, 0.3);
  --tag-border: var(--focus-color);
  --switch-bg: var(--left-menu-hover-bg);
  --switch-border: var(--border-color);
  --input-text-color: var(--page-text-color);
  --input-addon-color: var(--page-text-color);
  --group-list-bg: var(--left-menu-hover-bg);
  --group-list-hover-bg: var(--secondery-focus-bg);
  --group-icon-color: var(--page-text-color);
  --group-text-color: var(--page-text-color);
  --radio-btn-box-bg: var(--page-background-color);
  --radio-btn-box-selected-bg: var(--primary);
  --radio-btn-box-border-color: var(--focus-color);
  --disabled-checkbox-opacity: 0.5;

  // tabs
  --tabs-text-color: var(--page-text-color);

  // notification
  --toast-background-color: var(--neutral-darker);
  --toast-text-color: var(--white-regular);

  //modal
  --modal-background-color: var(--page-background-color);
  --modal-text-color: var(--page-text-color);

  // drawer
  --drawer-background-color: var(--page-background-color);
  --drawer-mask-color: rgba(255, 255, 255, 0.45);

  // code color
  --code-tag-background-color: var(--left-menu-hover-bg);
  --code-tag-text-color: var(--page-text-color);

  // tooltip
  --tooltip-text-color: var(--page-text-color);
  --tooltip-background-color: rgba(90, 90, 90, 0.8);
  --tooltip-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  --chart-tooltip-background: rgba(90, 90, 90, 0.8);

  // action dropdown
  --action-dropdown-backgroud: rgb(65, 65, 65);
  --action-dropdown-text: var(--page-text-color);
  --action-dropdown-hover-bg: var(--left-menu-hover-bg);
  --action-dropdown-divider: rgba(255, 255, 255, 0.2);

  // dropdown
  --dropdown-background: #313131;
  --dropdown-text: var(--page-text-color);
  --hieararchy-border-color: var(--neutral-regular);

  // progress bar
  --progress-bg: var(--neutral-regular);

  // buttons
  --primary-button-bg: var(--primary-alt);
  --primary-button-text: var(--page-text-color);
  --primary-button-hover-bg: fade(#3279be, 70);
  --primary-button-hover-text: var(--page-text-color);
  --error-button-bg: var(--secondary-red);
  --error-button-text: var(--page-text-color);
  --error-button-hover-bg: var(--secondary-red-dark);
  --error-button-hover-text: var(--page-text-color);
  --success-button-bg: var(--secondary-green);
  --success-button-text: var(--page-text-color);
  --success-button-hover-bg: var(--secondary-green);
  --success-button-hover-text: var(--page-text-color);
  --default-button-bg: var(--page-background-color);
  --default-button-text: var(--page-text-color);
  --default-button-border: var(--white-regular);
  --default-button-hover-bg: rgb(60, 60, 60);
  --default-button-hover-text: var(--page-text-color);
  --default-button-hover-border: var(--white-regular);
  --neutral-button-bg: rgb(70, 70, 70);
  --neutral-button-text: var(--page-text-color);
  --neutral-button-hover-bg: rgb(70, 70, 70);
  --neutral-button-hover-text: var(--page-text-color);
  --outline-button-bg: transparent;
  --outline-button-text: var(--primary-alt);
  --outline-button-hover-bg: fade(#3279be, 20);
  --outline-button-hover-text: var(--primary-alt);
  --error-outline-button-bg: transparent;
  --error-outline-button-text: var(--secondary-red-dark);
  --error-outline-button-hover-bg: fade(#c84235, 20);
  --error-outline-button-hover-text: var(--secondary-red-dark);
  --button-disabled-bg: rgba(60, 60, 60, 0.7);
  --button-disabled-border: rgba(70, 70, 70, 0.7);
  --button-disabled-text: rgba(255, 255, 255, 0.5);
  --button-transparent-text: var(--page-text-color);
  --button-transparent-hover-text: var(--page-text-color);

  // misc.
  --filter-viewer-bg-color: rgb(80, 80, 80);
  // notification dropdown
  --notification-dropdown-header: rgb(70, 70, 70);
  --notification-dropdown-background: var(--code-tag-background-color);
  --notification-dropdown-background-hover: #3c3c3c;
  --notification-dropdown-footer: #323232;
  --notification-dropdown-border-color: #383838;
  --notification-dropdown-datetime-color: #a5bad0;
  --alert-notification-popup-background-color: #282828;
  --alert-notification-popup-border-color: rgb(22, 22, 22);

  // screenshot share
  --screenshot-share-overlay-bg: rgba(255, 255, 255, 0.4);
  --screenshot-share-form-bg: var(--page-background-color);

  // template widget specific
  --partition-icon-background-color: var(--page-background-color);
  --partition-icon-text-color: var(--page-text-color);
  --availability-icon-background-color: var(--page-background-color);
  --availability-icon-text-color: var(--page-text-color);
  --switch-port-view-bg: #364658;
  --gauge-base-color: #404244;
  --gauge-text-color: var(--white-regular);

  // topology variables
  --topology-graph-tooltip-bg: rgba(30, 30, 30, 0.6);
  --topology-graph-tooltip-text-color: #fff;
  --graph-bg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='10' viewBox='0 0 10 10' fill='none'%3E%3Crect x='4' y='4' width='2' height='2' fill='%23404040'/%3E%3C/svg%3E");

  // overlay
  --overlay-bg: rgba(255, 255, 255, 0.25);

  // dashboard
  --dashboard-background: #1d1d1d;
  --widget-background-color: rgb(35, 35, 35);
  --timerange-background-color: var(--tag-bg);
  --timerange-text-color: var(--primary);
  --widget-background: var(--dashboard-background);
  --widget-box-shadow: #000;

  // tour section background
  --tour-metric-bg-color: #142c40;
  --tour-log-bg-color: #236777;
  --tour-flow-bg-color: #376842;

  // Chart Colors
  --chart-null-color: var(--neutral-darkest);

  // filter background
  --filter-background: var(--secondery-focus-bg);

  //license background
  --license-background: var(--page-background-color);

  // topology background color
  --topology-background: var(--code-tag-background-color);

  // gridLineColor
  --chart-grid-line-color: rgba(66, 66, 66, 0.4);

  // lineColor
  --bottom-line-color: rgba(66, 66, 66, 0.6);

  // chart-type-icon
  --chart-type-icon: #fff;

  // chart-line-type-icon
  --chart-line-type-icon: #5e5e5e;

  // policy-border-color
  --policy-border-color: none;
  --status-progress-color: #485564ad;
  --map-line-color: var(--border-color);
  --timeline-scrollbar-background-color: #222223;
}
