.counter-steps {
  .@{ant-prefix}-steps-item-finish
    > .@{ant-prefix}-steps-item-content
    > .@{ant-prefix}-steps-item-title::after {
    background-color: var(--secondary-green);
  }

  .@{ant-prefix}-steps-item-process
    > .@{ant-prefix}-steps-item-content
    > .@{ant-prefix}-steps-item-title::after {
    background-color: var(--primary);
  }

  // .@{ant-prefix}-steps-item-wait>.@{ant-prefix}-steps-item-content>.@{ant-prefix}-steps-item-title::after {
  //   background-color: @primary-color;
  // }

  .@{ant-prefix}-steps-item-error
    > .@{ant-prefix}-steps-item-content
    > .@{ant-prefix}-steps-item-title::after {
    background-color: var(--secondary-red);
  }

  .@{ant-prefix}-steps-item-title {
    padding: 0;

    a {
      color: var(--page-text-color);

      &:hover {
        color: var(--page-text-color);
      }
    }

    &::after {
      top: 50%;
      height: 2px;
    }
  }

  .@{ant-prefix}-steps-item {
    margin-right: 0 !important;
  }

  .@{ant-prefix}-steps-item-icon {
    display: none;
  }
}
.@{ant-prefix}-tabs-nav {
  .@{ant-prefix}-tabs-tab {
    padding-right: 0;
    padding-bottom: 8px;
    padding-left: 0;
    margin-right: 20px;
    border-bottom: solid 4px transparent;
    &.@{ant-prefix}-tabs-tab-active {
      border-color: var(--primary);
    }

    &:hover {
      border-color: var(--primary);
    }
  }

  & .ant-tabs-ink-bar {
    display: none !important;
  }
}
