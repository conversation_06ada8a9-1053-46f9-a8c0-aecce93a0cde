.@{ant-prefix}-drawer {
  &.widget-selector-drawer {
    transition: opacity 0.5s ease-in-out;
  }

  &-content-wrapper,
  &-content {
    background: var(--drawer-background-color);
  }

  &.@{ant-prefix}-drawer-open {
    transform: none !important;
    .@{ant-prefix}-drawer-mask {
      background: none !important;
      backdrop-filter: blur(3px);
      opacity: 1 !important;
    }
  }

  &.@{ant-prefix}-drawer-right {
    .@{ant-prefix}-drawer-content-wrapper {
      border-top: 2px solid var(--primary);
      border-bottom: 2px solid var(--primary);
    }
  }
}

&.@{ant-prefix}-drawer-open {
  transform: none !important;

  .@{ant-prefix}-drawer-content,
  .@{ant-prefix}-drawer-wrapper-body {
    border-top-left-radius: @overlay-border-radius;
    border-bottom-left-radius: @overlay-border-radius;
  }
}

.@{ant-prefix}-drawer-content {
  .@{ant-prefix}-drawer-header {
    padding: 16px 0;
    margin: 0 24px;
    background: var(--drawer-background-color);
    border-color: var(--border-color);
  }

  .@{ant-prefix}-drawer-wrapper-body {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden !important;
    background: var(--drawer-background-color);

    .@{ant-prefix}-drawer-body {
      position: relative;
      flex: 1;
      height: calc(100% - 55px);
      min-height: 0;
      padding: 0;
    }
  }

  .@{ant-prefix}-drawer-body {
    .@{ant-prefix}-form-item-label {
      line-height: 1.5;
    }
  }

  .actions {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 60px;
    background: var(--drawer-background-color);
    border-bottom-left-radius: 15px;

    @apply border-solid border-t border-neutral-lighter border-l-0 border-r-0 border-b-0 mx-6;
  }
}

.ant-drawer-close-btn-center {
  .@{ant-prefix}-drawer-content-wrapper {
    .@{ant-prefix}-drawer-content {
      .@{ant-prefix}-drawer-header {
        .@{ant-prefix}-drawer-close {
          top: 12px !important;
        }
      }
    }
  }
}
