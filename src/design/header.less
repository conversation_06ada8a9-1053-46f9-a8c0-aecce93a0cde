.mainAppHeader {
  z-index: 10;
  padding-left: 75px;
  background: var(--page-background-color);
  // box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.3);
  // box-shadow: none;
  .rightContainer {
    line-height: 0;
  }

  .box {
    @apply px-6 border-r-2 border-l-0 border-t-0 border-b-0 border-neutral-lighter border-solid flex items-center justify-center;

    height: calc(100% - 1rem);

    &:last-child {
      @apply border-r-0;
    }
    .@{ant-prefix}-badge {
      @apply mr-2;

      a {
        @apply mr-0;
      }
    }
  }

  .admin-page-link {
    &.router-link-active {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 0.5rem;
      color: var(--page-text-color);
      background-color: var(--primary);
      border-radius: 50%;
    }
  }
}

.mainLayout {
  & .header-panel {
    height: @header-height;
    border-bottom: solid 1px var(--border-color);

    & .customer-logo {
      display: inline-block;
      height: @header-height;
      line-height: @header-height;
      text-align: center;

      & a.logo {
        display: inline-block;
        float: none;

        & img {
          height: 50px;
          margin: 0;
        }
      }
    }

    & .user-box {
      & .user-icon-box {
        padding: 0;
        margin-right: 8px;

        & a {
          width: @user-icon-wh;
          height: @user-icon-wh;
          & span.@{ant-prefix}-avatar-icon {
            width: @user-icon-wh;
            height: @user-icon-wh;

            & i {
              font-size: 1.4rem;
            }

            & img {
              width: @user-icon-wh;
              height: @user-icon-wh;
            }
          }
        }
      }
    }
  }
}

.header-border-bottom {
  border-bottom: 1px solid var(--border-color);
}

.header-has-divider {
  border-bottom: 1px solid var(--border-color);
}
