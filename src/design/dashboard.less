@widget-border-radius: 2px;

.dashboard-container {
  background: var(--dashboard-background);
}

.setup-background {
  background: var(--widget-background-color);
}

.vue-grid-item {
  background: var(--widget-background);
  border: 1px solid var(--widget-border-color);
  border-radius: @widget-border-radius;
  box-shadow: 0 0 0 1px var(--widget-box-shadow);
  // @apply shadow;

  &.shadow-disaled {
    box-shadow: none;
  }

  .widget-view {
    background: var(--widget-background);
    border-radius: @widget-border-radius;

    &.blinking-div {
      background: fade(#099dd9, 50);
    }

    .widget-view-container {
      border-radius: @widget-border-radius;
    }

    .widget-action {
      visibility: hidden;
    }

    &:hover {
      .widget-action {
        visibility: visible;
      }
    }
  }

  > .vue-resizable-handle {
    display: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8'%3E%3Cpath d='M 8 8 L 0 8 L 0 5.601562 L 5.601562 5.601562 L 5.601562 0 L 8 0 Z M 8 8 ' fill='%23364658' opacity='0.4' /%3E%3C/svg%3E");
  }

  &:hover {
    > .vue-resizable-handle {
      display: block;
    }
  }
}

[data-theme='dark-theme'] .vue-grid-item {
  > .vue-resizable-handle {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8'%3E%3Cpath d='M 8 8 L 0 8 L 0 5.601562 L 5.601562 5.601562 L 5.601562 0 L 8 0 Z M 8 8 ' fill='%23a5bad0' opacity='0.9' /%3E%3C/svg%3E");
  }
}

.vue-grid-item.vue-grid-placeholder {
  background: var(--primary-alt) !important;
  border: 1px solid var(--border-color);
  // @apply shadow-md;
}

.highcharts-tooltip-container {
  z-index: 9999 !important;

  .highcharts-tooltip {
    width: auto !important;

    span {
      width: auto !important;
    }
  }

  .sparkline-tooltip {
    min-width: 180px;
  }
}

.highcharts-loading-inner {
  color: var(--page-text-color);
}

.highcharts-loading {
  background: none !important;
  opacity: 1 !important;
  backdrop-filter: blur(3px);
}

.dashboard-widget-grid {
  .k-grid {
    display: flex;
    flex: 1;
    min-width: 0;
    min-height: 0;
    margin-top: 0;

    table.k-grid-table {
      tbody > tr > td {
        position: relative;
        height: 40px;

        .has-color {
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          display: flex;
          align-items: center;
          min-width: 0;
          padding: 0.25rem 0.5rem;
        }
        /* stylelint-disable */
        &:has(a) {
          color: var(--text-color-common-link);
        }
      }
    }

    .k-header {
      &.small {
        font-size: 10px;
      }

      &.medium {
        font-size: 12px;
      }

      &.large {
        font-size: 14px;
      }
    }

    &.non-relative-td {
      table.k-grid-table {
        tbody > tr {
          position: relative;
        }

        // tbody > tr > td {
        //   position: unset;
        // }
      }
    }
  }
}

.chart-placeholder {
  height: 100%;
  border-bottom: 1px dashed var(--border-color);
  // border-radius: 4px;
}
