.scrollable-modal {
  .@{ant-prefix}-modal {
    top: 50px;
  }
  .@{ant-prefix}-modal-header {
    border-radius: 20px 20px 0 0;
  }
  .@{ant-prefix}-modal-content {
    padding-bottom: 0;
    border-color: @info-color;
  }
  .@{ant-prefix}-modal-body {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 200px);
    padding: 6px 12px;
  }

  &.restrict-width {
    .@{ant-prefix}-modal-content {
      min-width: 1020px;
      max-width: 1020px;
    }
  }

  &.smaller-modal {
    .@{ant-prefix}-modal-body {
      height: 50vh;
    }
  }

  &.compare-metric-modal {
    .@{ant-prefix}-modal-body {
      height: calc(100vh - 150px);
    }
  }
}

.hide-footer {
  .@{ant-prefix}-modal-footer {
    display: none;
    border: none;
  }
}

.@{ant-prefix}-modal {
  &-mask {
    backdrop-filter: blur(3px);
  }
}

.@{ant-prefix}-modal-wrap {
  z-index: 1050;

  .@{ant-prefix}-modal-header {
    padding: 5px 16px;
    color: var(--page-text-color);
    background-color: var(--page-background-color);
    border-color: var(--border-color);
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
  }

  .@{ant-prefix}-modal-content {
    padding-bottom: 0;
    color: var(--page-text-color);
    background-color: var(--page-background-color);
    border-top: 2px solid var(--primary);
    border-right: @overlay-border-width solid var(--primary);
    border-bottom: 2px solid var(--primary);
    border-left: @overlay-border-width solid var(--primary);
    border-radius: @overlay-border-radius;
  }

  &.primary-alt {
    .@{ant-prefix}-modal-content {
      border-color: var(--primary-alt);
    }
  }

  &.success {
    .@{ant-prefix}-modal-content {
      border-color: var(--secondary-green);
    }
  }

  &.confirm-modal {
    .pop-content {
      float: left;
      width: 80%;
    }

    .action-container {
      button:first-child {
        @apply mr-2;
      }
    }
    .@{ant-prefix}-modal-footer {
      @apply hidden;
    }
  }
}

.floto-confirm-modal {
  z-index: 1050;

  .@{ant-prefix}-modal-content {
    padding-bottom: 0;
    color: var(--page-text-color);
    background-color: var(--page-background-color);
    border-top: 2px solid var(--secondary-red);
    border-right: @overlay-border-width solid var(--secondary-red);
    border-bottom: 2px solid var(--secondary-red);
    border-left: @overlay-border-width solid var(--secondary-red);
    border-radius: @overlay-border-radius;
  }

  & .icon-container {
    border: solid 2px var(--secondary-red);
  }

  & .pop-content {
    float: left;
    width: 80%;
  }

  .action-container {
    button:first-child {
      @apply mr-2;
    }
  }
  .@{ant-prefix}-modal-footer {
    @apply hidden;
  }
}

.widget-form-modal {
  .@{ant-prefix}-modal {
    height: 100%;
    padding: 12px;

    &-content {
      display: flex;
      flex-direction: column;
      height: 100%;
    }

    &-body {
      display: flex;
      flex-direction: column;
      height: 100%;
      min-height: 0;
      padding: 8px;
    }
  }
}

.no-padding-modal {
  .@{ant-prefix}-modal {
    padding: 12px;

    &-body {
      padding: 8px;
    }
  }
}

.no-padding-confrim-modal {
  .@{ant-prefix}-modal {
    padding: 12px;

    &-body {
      padding: 0;
    }

    .pop-content {
      float: left;
      width: 80%;
    }

    .action-container {
      button:first-child {
        @apply mr-2;
      }
    }
    .@{ant-prefix}-modal-footer {
      @apply hidden;
    }
  }
}

.share-modal {
  .ant-modal {
    .ant-modal-content {
      display: flex;
      flex-direction: column;
      height: 90vh;

      .ant-modal-body {
        display: flex;
        flex: 1;
        flex-direction: column;
        min-height: 0;
      }
    }
  }
}
