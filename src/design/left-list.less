.@{ant-prefix}-collapse.left-tree-list-box {
  float: left;
  width: 100%;
  height: 100%;
  background: none;
  border-radius: 0;

  &.no-border-right {
    border-right: none;
  }

  & .@{ant-prefix}-collapse-item {
    float: left;
    width: 100%;
    border-color: var(--border-color);

    & .@{ant-prefix}-collapse-header {
      padding: 0;

      & .@{ant-prefix}-collapse-arrow {
        right: 0;
        left: initial;
        color: var(--left-menu-arrow-color);
        opacity: var(--left-menu-arrow-opacity);
      }

      & .list-title {
        display: flex;
        align-items: center;
        width: 100%;
        padding: 10px 0;
        font-size: @text-sm;
        color: var(--left-menu-text-color);

        & i.list-icon {
          position: relative;
          top: -1px;
          margin-right: 5px;
          font-size: 1.3rem;
          color: var(--page-text-color);
          opacity: 0.7;
        }
      }
    }

    & .@{ant-prefix}-collapse-content {
      float: left;
      width: 100%;

      & .@{ant-prefix}-collapse-content-box {
        float: left;
        width: 100%;
        padding: 0;
        padding-left: 25px;

        & ul.sub-level-list {
          float: left;
          width: 100%;
          margin-bottom: 3px;
          background: none;
          border: 0;

          & li {
            float: left;
            width: 100%;
            height: @nav-menu-height;
            padding: 0;
            margin: 0 0 1px;
            background: none;

            & a {
              float: left;
              width: 100%;
              height: @nav-menu-height;
              padding: 0 10px;
              margin: 0;
              font-size: @text-sm;
              line-height: @nav-menu-height;
              color: var(--left-menu-text-color);

              &:hover,
              &.router-link-exact-active {
                font-weight: 500;
                color: var(--left-menu-text-color-hover);
                background: var(--left-menu-hover-bg);
                border-radius: 4px;
              }
            }

            &.@{ant-prefix}-menu-item.@{ant-prefix}-menu-item-selected {
              a {
                overflow: hidden;
                text-overflow: ellipsis;

                &.router-link-active {
                  font-weight: 500;
                  color: var(--left-menu-text-hover-color);
                  background: var(--left-menu-hover-bg);
                }
              }
            }
          }
        }
      }
    }
  }
}

.selection-menu {
  .@{ant-prefix}-menu-item {
    color: var(--left-menu-text-color);

    &:hover,
    &-selected {
      font-weight: 500;
      color: var(--left-menu-text-hover-color) !important;
      background: var(--left-menu-hover-bg) !important;
    }
  }

  &.no-border {
    border: none;
  }
}

.left-menu-item {
  &:hover {
    background-color: var(--left-menu-hover-bg);
  }
}
