.float-l {
  float: left;
}

.float-r {
  float: right;
}

.border-bot {
  border-bottom: solid 1px var(--border-color);

  &-transparent {
    border-bottom: 1px solid var(--transparent-border-color);
  }
}

.border-top {
  border-top: solid 1px var(--border-color);

  &-transparent {
    border-top: 1px solid var(--transparent-border-color);
  }
}

.border-right {
  border-right: solid 1px var(--border-color);

  &-transparent {
    border-right: 1px solid var(--transparent-border-color);
  }
}

.border-left {
  border-left: solid 1px var(--border-color);

  &-transparent {
    border-left: 1px solid var(--transparent-border-color);
  }
}

.bordered {
  border: 1px solid var(--border-color);

  &-transparent {
    border: 1px solid var(--transparent-border-color);
  }
}
.@{ant-prefix}-position-t-2 {
  position: relative;
  top: 2px;
}

.col-special-wrap {
  width: 95%;
}

.radius-corner {
  border-radius: 3px;
}

.pos-r {
  position: relative;
}

.bord-r {
  border-right: solid 1px var(--border-color);
}

.radius-full {
  border-radius: @btn-raidus-full !important;
}

& .opacity-mid {
  opacity: 0.7;
}

.menu-divider {
  width: 1px;
  height: 25px;
  background: var(--border-color);
}

.disable-pie-color {
  color: var(--page-text-color) !important;
  opacity: 50%;
}
