.multiple-dropdown-trigger {
  height: @input-height-base;
  padding: @input-padding-horizontal-base @input-padding-vertical-base;
  background: var(--dropdown-btn-background);
  border: 1px solid var(--border-color);
  border-radius: @btn-radius;
}

.dropdown-trigger-input {
  line-height: 32px;
  input.@{ant-prefix}-input {
    padding: @input-padding-horizontal-base @input-padding-vertical-base !important;
    padding-right: 30px !important;
    cursor: pointer !important;
    background: var(--dropdown-btn-background) !important;
    border: 1px solid var(--border-color) !important;
    border-radius: @btn-radius !important;

    &.@{ant-prefix}-input-disabled {
      cursor: auto !important;

      + .@{ant-prefix}-input-suffix {
        i {
          cursor: auto;
        }
      }
    }
  }

  &.@{ant-prefix}-input-affix-wrapper {
    span.@{ant-prefix}-input-prefix + input.@{ant-prefix}-input {
      padding-left: 30px !important;
    }
  }
}

.no-box-dropdown {
  .multiple-dropdown-trigger {
    padding: 0;
    background: var(--page-background-color);
    border: none;
    border-bottom: 1px solid var(--border-color);
    border-radius: 0;
  }

  .dropdown-trigger-input {
    input.@{ant-prefix}-input {
      padding: 0 !important;
      background: var(--widget-background-color) !important;
      border: none !important;
      border-bottom: 1px solid var(--border-color) !important;
      border-radius: 0 !important;
    }
  }
}

.search-box-dropdown {
  .multiple-dropdown-trigger {
    padding: 0;
    background: transparent;
    border: none;
    border-radius: 0;
  }

  .dropdown-trigger-input {
    input.@{ant-prefix}-input {
      padding: 0 !important;
      background: transparent !important;
      border: none !important;
      border-radius: 0 !important;
    }
  }
}

.dropdown-picker-menu {
  border-right: none;

  .@{ant-prefix}-menu-item {
    @apply px-0 !important;
  }
}

.@{ant-prefix}-select-dropdown {
  max-width: @picker-popover-max-width;
  max-height: @picker-popover-max-height;

  .@{ant-prefix}-select-tree-treemonitor-disabled {
    display: none;
  }

  &-menu-item-selected {
    display: none;
  }
}
.@{ant-prefix}-menu {
  &.virtual-scrollable-dropdown-menu {
    background: var(--dropdown-background);
  }

  & li {
    padding: 0;

    & .dropdown-item {
      float: left;
      width: 100%;

      & span {
        float: left;
        width: 100%;
      }
    }
  }
}

.k-list .k-item.k-state-selected,
.k-list-optionlabel.k-state-selected,
.k-list-container .k-button:active,
.k-list-container .k-button.k-state-active,
.k-columnmenu-item.k-state-selected,
.k-column-menu .k-menu:not(.k-context-menu).k-item.k-state-selected,
.k-spreadsheet-popup .k-button:active,
.k-spreadsheet-popup .k-button.k-state-active,
.k-spreadsheet-popup .k-button.k-state-selected,
.k-menu-group .k-item.k-state-selected,
.k-menu.k-context-menu .k-item.k-state-selected {
  background-color: var(--primary) !important;
}
.@{ant-prefix}-popover-inner {
  // box-shadow: 0 1px 2px 0 rgb(60 64 67 / 30%), 0 2px 6px 2px rgb(60 64 67 / 15%) !important;
}

.sample-download-list {
  padding: 0;
  margin: 0;
  list-style: none;

  li {
    border-bottom: 1px solid var(--border-color);

    @apply px-1 py-3;

    a {
      display: flex;
      justify-content: space-between;
      color: var(--left-menu-text-color);
    }

    &:hover {
      color: var(--left-menu-text-color-hover);
      background: var(--left-menu-hover-bg);

      a {
        color: var(--left-menu-text-color-hover);
      }
    }
  }
}

.v-popover {
  .dropdown-icon {
    opacity: 0.8;
    transition: transform 0.5s ease;

    &.is-open {
      opacity: 0.8;
      transform: rotate(180deg);
    }
  }
}

.headerUserMenu {
  ul.user-menu {
    margin: 0;
    list-style: none;

    @apply px-4 py-2;

    li {
      @apply rounded;

      color: var(--page-text-color);

      &:first-child {
        @apply mt-1;
      }

      a {
        @apply px-1 py-2;

        display: block;
        font-weight: 500;
        color: inherit;

        &:hover {
          color: inherit;
        }
      }

      &:hover {
        color: var(--page-text-color);
        background: var(--code-tag-background-color);
      }

      &.logout {
        color: var(--secondary-red);
        background: var(--code-tag-background-color);

        @apply pl-4;

        &:hover {
          color: var(--secondary-red-dark);
        }
      }

      &.divider {
        position: relative;
        height: 20px;

        &:hover {
          background: unset;
        }

        &::after {
          position: absolute;
          top: 7px;
          right: 0;
          left: 0;
          height: 1px;
          content: ' ';
          background: var(--border-color);
        }
      }
    }
  }
}

.disabled-bordered-dropdown {
  .@{ant-prefix}-form-item
    .@{ant-prefix}-form-item-control-wrapper
    .@{ant-prefix}-form-item-control
    .@{ant-prefix}-input {
    padding: @input-padding-horizontal-base @input-padding-vertical-base !important;
    padding-right: 30px !important;
    cursor: not-allowed !important;
    background: var(--dropdown-btn-background) !important;
    border: 1px solid var(--border-color) !important;
    border-radius: @btn-radius !important;
  }
}

.no-label {
  .ant-form-item-label {
    display: none !important;
  }

  .ant-form-item {
    padding: 0;
    margin: 0;
  }
}

.@{ant-prefix}-collapse {
  &.height-full {
    .@{ant-prefix}-collapse-item {
      display: flex;
      flex-direction: column;
      height: 100%;

      .@{ant-prefix}-collapse-content {
        flex: 1;
      }

      .@{ant-prefix}-collapse-content-box {
        height: 100%;
      }
    }
  }
}

.color-box-dropdown {
  .dropdown-trigger-input {
    input.@{ant-prefix}-input {
      padding: 0;
    }
  }
}

.use-large-padding {
  line-height: 32px;
  input.@{ant-prefix}-input {
    padding: @input-padding-horizontal-base @input-padding-vertical-base !important;
    padding-right: 30px !important;
    cursor: pointer !important;
    background: var(--dropdown-btn-background) !important;
    border: 1px solid var(--border-color) !important;
    border-radius: @btn-radius !important;

    &.@{ant-prefix}-input-disabled {
      cursor: auto !important;

      + .@{ant-prefix}-input-suffix {
        i {
          cursor: auto;
        }
      }
    }
  }

  &.@{ant-prefix}-input-affix-wrapper {
    span.@{ant-prefix}-input-prefix + input.@{ant-prefix}-input {
      padding-left: 42px !important;
    }
  }
}
