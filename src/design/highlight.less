// stylelint-disable
mark {
  padding: 1px 2px;
  // font-weight: bold;
  user-select: none;
  background: var(--secondary-yellow);
  border-radius: @btn-radius;
}
.rounded-widget {
  cursor: pointer;
  background: var(--code-tag-background-color);
  // border-bottom: solid 3px var(--neutral-light);
  border-radius: @btn-radius;
  & .w-title {
    font-size: 0.8rem;
    line-height: 20px;
    color: inherit;
  }
  & .w-count {
    font-size: 1.7rem;
    font-size: 1.2rem;
    font-weight: 500;
    font-weight: 600;
    line-height: 20px;
    line-height: 20px;
    color: inherit;
  }
  &.total {
    &:not(.hovered) {
      color: white;
      background: var(--neutral-dark);
    }
    &.hovered:hover {
      color: white;
      background: var(--neutral-dark);
    }
  }

  &.active {
    .w-count {
      color: white;
    }
  }

  &.hovered:hover {
    .w-count {
      color: white;
    }
  }
}
.severity-status-panel {
  & .@{ant-prefix}-radio-group {
    & label.@{ant-prefix}-radio-button-wrapper {
      border: none;
      &::before {
        background: none;
      }
    }
  }
}
.background-neutral-light {
  background: var(--group-list-bg) !important;
}
& .header-primery-inner-color {
  color: var(--header-title-text-color);
}

.alert-table-td {
  height: 40px;
  color: var(--page-text-color) !important;
  background: var(--code-tag-background-color) !important;
}
.alert-td-width {
  width: 20% !important;
}
