// stylelint-disable-next-line
code {
  padding: 3px 4px;
  font-weight: bold;
  color: var(--secondary-red);
  background: rgba(0, 0, 0, 0.06);

  @apply rounded;
}

.search-box {
  max-width: @search-width;
  height: @search-height;
  color: var(--input-text-color);
  background: var(--page-background-color);
  border-radius: 4px;

  & .@{ant-prefix}-input {
    background: none;
    border-color: var(--border-color);

    &:focus {
      border-color: var(--primary);
      box-shadow: none;
    }
  }
  & .@{ant-prefix}-input-prefix {
    & i.@{ant-prefix}icon {
      color: var(--search-icon);
      opacity: 0.5;
    }
  }
}

.dropdown-search {
  .@{ant-prefix}-input {
    padding-left: 30px !important;
    color: var(--input-text-color) !important;
    background: var(--page-background-color) !important;
    border: 1px solid var(--border-color) !important;
    border-bottom-color: var(--border-color) !important;
    border-radius: 4px !important;

    &:focus {
      border-color: var(--primary) !important;
      box-shadow: none;
    }
  }
  .@{ant-prefix}-input-suffix,
  .@{ant-prefix}-input-prefix {
    & i.@{ant-prefix}icon {
      color: var(--search-icon) !important;
      opacity: 0.5;
    }
  }
}

.item-list-header-row {
  @apply text-neutral;
}

.item-list-row {
  @apply py-4;
}

.subject-title {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  @apply text-primary mb-0;
}

.form-edit-mode .selection-menu {
  .@{ant-prefix}-menu-item {
    cursor: auto;

    &:not(.@{ant-prefix}-menu-item-selected) {
      cursor: not-allowed;
      opacity: 0.5;
    }
  }

  .@{ant-prefix}-tabs-tab {
    cursor: auto;

    &:not(.@{ant-prefix}-tabs-tab-active) {
      cursor: not-allowed;
      opacity: 0.5;
    }
  }
}
.@{ant-prefix}-col-0 {
  display: none !important;
}

div.k-widget ::selection,
div.k-block ::selection,
div.k-panel ::selection {
  background-color: var(--primary);
}

.mt-spacer {
  margin-top: 1.7rem;
}

.filter-box {
  @apply shadow-md p-2;

  border: 1px solid var(--neutral-lighter);
  border-radius: 3px;
  & .@{ant-prefix}-input-affix-wrapper {
    & .@{ant-prefix}-input {
      padding-left: 0;
    }
  }
  & .@{ant-prefix}-form-item-control {
    & .label-list {
      padding-left: 0 !important;
      line-height: 29px;
    }

    & .create-group {
      & .@{ant-prefix}-divider-horizontal {
        margin-top: 8px;
      }
    }
  }
}

.slide-filters {
  @apply shadow-md py-2 px-1;

  position: relative;
  border: 1px solid var(--border-color);
  border-radius: 3px;
}

.slide-filters-close {
  position: absolute;
  top: 0;
  right: 0;
  width: 39px;
}

.filter-close {
  position: relative;
  right: -9px;
}

.primary-alt-header {
  background-color: var(--primary-alt);
}

.secondary-green-header {
  background-color: var(--secondary-green);
}

.neutral-regular-header {
  background-color: var(--neutral-regular);
}

.title-head-row {
  border-radius: 4px 4px 0 0;
}

.ant-content-row {
  border-radius: 0 0 4px 4px;

  & .checkbox-label {
    padding-left: 0;
  }
}

.bg-primary-lighter {
  background-color: var(--primary-alt-lighter);
}

.bg-secondary-green-lighter {
  background-color: var(--secondary-green-lighter);
}

.bg-neutral-regular-lighter {
  background-color: var(--neutral-regular-lighter);
}

//stylelint-disable-next-line
.h-100 {
  min-height: 0;
}
//stylelint-disable-next-line
.w-100 {
  width: 100%;
}

.justify-space-between {
  justify-content: space-between;
}

.@{ant-prefix}-divider {
  background: var(--border-color);
}

.bg-neutral-lightest {
  color: var(--page-text-color);
  background: var(--code-tag-background-color);
}

.bg-neutral-lighter {
  background: var(--neutral-lighter);
}

.linux-os-help {
  background: var(--left-menu-hover-bg);
}

.@{ant-prefix}-progress-inner {
  background: var(--progress-bg);
}

.@{ant-prefix}-layout {
  background: var(--page-background-color) !important;
}

* {
  &:focus {
    outline: none;
  }
}

.severity {
  &.down {
    background: var(--neutral-regular);
  }

  &.clear,
  &.up {
    background: var(--secondary-green);
  }

  &.critical {
    background: var(--secondary-red);
  }

  &.major,
  &.unknown {
    background: var(--secondary-orange);
  }

  &.warning {
    background: var(--secondary-yellow);
  }

  &.none {
    background: var(--neutral-darkest);
  }
}

.severity-lightest {
  &.down {
    background: var(--severity-down-lightest) !important;
  }

  &.down-text {
    color: var(--severity-down) !important;
  }

  &.clear,
  &.up {
    background: var(--severity-clear-lightest) !important;
  }

  &.clear-text,
  &.up-text {
    color: var(--severity-clear) !important;
  }

  &.critical {
    background: var(--severity-critical-lightest) !important;
  }

  &.critical-text {
    color: var(--severity-critical) !important;
  }

  &.major {
    background: var(--severity-major-lightest) !important;
  }

  &.major-text {
    color: var(--severity-major) !important;
  }

  &.warning {
    background: var(--severity-warning-lightest) !important;
  }

  &.warning-text {
    color: var(--severity-warning) !important;
  }
}

.font-semibold {
  font-weight: 500;
}

.no-wrap {
  flex-wrap: nowrap;
}

// stylelint-disable-next-line
body {
  // stylelint-disable-next-line
  .border-neutral-lighter {
    border-color: var(--border-color);
  }
}

.@{ant-prefix}-divider-inner-text {
  color: var(--page-text-color);
}
.@{ant-prefix}-divider-horizontal {
  &.@{ant-prefix}-divider-with-text {
    &::before,
    &::after {
      border-color: var(--border-color);
    }
  }
}

.heatmap-box-border {
  border-radius: 0;
  border-top-left-radius: 8px;
  border-bottom-right-radius: 8px;
  transition: transform 0.2s ease-in-out;

  &.active,
  &:hover {
    position: relative;
    z-index: 2;
    opacity: 1 !important;
    transform: scale(1.5);

    .heatmapbox {
      top: 3px !important;
      right: 3px !important;
      bottom: 3px !important;
      left: 3px !important;
      transform: scale(1.5);
    }

    &.max-scale {
      transform: scale(1.2);

      .heatmapbox {
        transform: scale(1.2);
      }
    }

    @apply shadow-lg;
  }
}

.text-size-medium {
  font-size: 13px;
}

.@{ant-prefix}-collapse.collapse-witout-border {
  > .@{ant-prefix}-collapse-item {
    border-bottom: none;

    a.completed-link {
      background: var(--severity-clear-lighter);
    }
  }
}
.@{ant-prefix}-collapse.collapse-without-padding {
  .@{ant-prefix}-collapse-content-box {
    padding: 0 !important;
  }
}

.@{ant-prefix}-collapse.collapse-reduced-padding {
  > .@{ant-prefix}-collapse-item {
    .ant-collapse-header {
      padding: 6px 0 6px 24px;

      .ant-collapse-arrow {
        left: 8px;
      }
    }
  }
}

.@{ant-prefix}-collapse.collapse-scrollable {
  > .@{ant-prefix}-collapse-item {
    .@{ant-prefix}-collapse-content {
      max-height: 300px;
      overflow: scroll;
    }
  }
}

.@{ant-prefix}icon {
  font-size: 1.1rem;
}

.@{ant-prefix}-menu-item,
.@{ant-prefix}-menu-submenu-title {
  .@{ant-prefix}icon {
    font-size: 1rem;
  }
}

.k-animation-container.k-animation-container-relative.k-animation-container-shown {
  z-index: 1051 !important;
}

.highcharts-button-pressed {
  text {
    color: var(--page-text-color) !important;
    fill: var(--page-text-color) !important;
  }
}

.wiget-background-color {
  background: var(--widget-background-color);
}

.code-container {
  border: 1px solid var(--border-color);
  border-radius: 5px;

  .copy-text {
    position: absolute;
    top: 0;
    right: 0;
    padding: 0.2rem 0.5rem;
    visibility: hidden;
    background: var(--page-background-color);
    border: 1px dashed var(--border-color);
  }

  &:hover {
    .copy-text {
      visibility: visible;
    }
  }

  .text-primary {
    font-weight: 600;
    color: var(--primary) !important;
  }
}

.dashboard-category-name {
  &.@{ant-prefix}-collapse {
    .@{ant-prefix}-collapse-item {
      .@{ant-prefix}-collapse-content-active {
        .@{ant-prefix}-collapse-content-box {
          padding-top: 0;
          padding-bottom: 0;
        }
      }

      .@{ant-prefix}-collapse-header {
        &:hover {
          background: var(--group-list-hover-bg);
          border-radius: 4px;
        }

        padding: 0 6px 0 30px;
        .@{ant-prefix}-collapse-arrow {
          top: 45%;
          z-index: 1;
          font-size: 1.25em;
          line-height: normal;
          color: #7b8fa5;
        }
      }
    }
  }
}

.@{ant-prefix}-collapse.benchmark-form-collapse {
  > .@{ant-prefix}-collapse-item {
    &.ant-collapse-item-disabled.collapse-rule {
      .ant-collapse-header {
        padding: 0 !important;
        cursor: unset !important;

        .ant-collapse-arrow {
          display: none !important;
        }
      }
    }

    &.collapse-breakdown-last:not(.ant-collapse-item-active):not(.ant-collapse-item-disabled)
      > div {
      border-bottom: 0 !important;
    }

    .ant-collapse-header {
      border-bottom: 1px solid var(--code-tag-background-color);

      .ant-collapse-arrow {
        font-size: 1rem;
      }
    }

    .ant-collapse-content {
      .ant-collapse-content-box {
        padding: 0 !important;
      }
    }
  }

  &.collapse-item-no-border-bot {
    > .@{ant-prefix}-collapse-item {
      border-bottom: 0 !important;
    }
  }

  &.bordered-panel {
    > .@{ant-prefix}-collapse-item {
      margin-bottom: 16px;
      border: 1px solid var(--code-tag-background-color);
    }
  }
}

.@{ant-prefix}-collapse.benchmark-brekdown {
  > .@{ant-prefix}-collapse-item {
    &.ant-collapse-item-disabled.collapse-rule {
      .ant-collapse-header {
        padding: 0 !important;
        cursor: unset !important;
        background: var(--page-background-color);

        .ant-collapse-arrow {
          display: none !important;
        }
      }
    }

    &.collapse-breakdown {
      .ant-collapse-header {
        background: var(--code-tag-background-color);
        border-radius: 5px;

        .ant-collapse-arrow {
          font-size: 1rem;
        }
      }
    }

    &.collapse-breakdown-normal-bg {
      .ant-collapse-header {
        background: var(--page-background-color);
      }
    }

    .ant-collapse-content {
      .ant-collapse-content-box {
        padding: 0 !important;
      }
    }
  }

  &.collapse-item-no-border-bot {
    > .@{ant-prefix}-collapse-item {
      border-bottom: 0 !important;
    }
  }

  &.bordered-panel {
    > .@{ant-prefix}-collapse-item {
      margin-bottom: 16px;
      border: none;
    }
  }
}

.no-disabled-menu {
  .ant-menu-item.ant-menu-item-disabled {
    font-weight: 500;
    color: var(--left-menu-text-hover-color) !important;
  }
}

.font-size-three-two-pexels {
  font-size: 32px;
}

.text-size-four-two-pexels {
  font-size: 42px;
}

.text-size-ten-pexels {
  font-size: 10px;
}

.text-neutral-regular {
  color: var(--neutral-regular);
}

.omnibox-model-z-index {
  z-index: 1055 !important;
}

.no-pb-collapse {
  &.@{ant-prefix}-collapse {
    .@{ant-prefix}-collapse-item {
      .@{ant-prefix}-collapse-content-active {
        .@{ant-prefix}-collapse-content-box {
          padding: 0;
        }
      }
    }
  }
}
