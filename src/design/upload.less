.@{ant-prefix}-upload-disabled {
  display: none !important;
}

.@{ant-prefix}-upload {
  &.@{ant-prefix}-upload-drag {
    background: transparent;
    border-color: var(--border-color);

    p.@{ant-prefix}-upload-text {
      color: var(--page-text-color);
    }
  }

  &.@{ant-prefix}-upload-select {
    width: 100%;
  }
}

.@{ant-prefix}-upload-list {
  // &:hover {
  //   .@{ant-prefix}-upload-list-item{
  //     width: unset;
  //   }
  // }
  &.@{ant-prefix}-upload-list-text {
    display: flex;
    flex-wrap: wrap;
  }

  &-item {
    display: flex;
    flex-direction: column;
    width: 100%;

    // &:hover {
    //   display: unset;
    // }
    &-info {
      max-width: 220px;
      padding-right: 20px;
    }

    &:hover {
      .@{ant-prefix}-upload-list-item-info {
        background-color: var(--code-tag-background-color);
        border-radius: 4px;
      }

      background-color: var(--code-tag-background-color);
    }

    a {
      color: var(--page-text-color);
    }

    .@{ant-prefix}icon-close {
      top: 0;
      height: 100%;
      margin-top: 3px;
      color: var(--page-text-color);
    }
  }
  .@{ant-prefix}icon-paper-clip {
    display: none;
  }
  a.@{ant-prefix}-upload-list-item-name {
    padding-left: 0;
  }
}
