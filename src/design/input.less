::placeholder {
  color: var(--input-placeholder-color);
}

.placeholder-text {
  color: var(--input-placeholder-color);
}

.@{ant-prefix}-input,
.@{ant-prefix}-input-number {
  color: var(--input-text-color);
  background: var(--page-background-color);
  border-color: var(--border-color);

  &:disabled {
    background: transparent !important;
  }
}

.@{ant-prefix}-input-number {
  background: transparent;

  &-focused {
    box-shadow: none;
  }

  &-handler-wrap {
    border-left: 1px solid var(--border-color);

    .@{ant-prefix}-input-number-handler {
      &-down {
        border-color: var(--border-color);
      }

      &-up-inner,
      &-down-inner {
        color: var(--page-text-color);
      }
    }
  }
}

.@{ant-prefix}-dropdown-trigger {
  &:focus {
    outline: none;
  }
}

.@{ant-prefix}-input-group {
  .@{ant-prefix}-input,
  .@{ant-prefix}-input-number {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .input-prefix {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 32px;
    padding: 8px 10px;
    background: var(--neutral-lighter);
  }
}

.@{ant-prefix}-input-prefix {
  color: var(--input-addon-color) !important;

  + .@{ant-prefix}-input {
    padding-left: 40px !important;
  }
}

.@{ant-prefix}-input-affix-wrapper {
  .@{ant-prefix}-input:not(:first-child) {
    padding-left: 40px;
  }
}

.monitor-type-picker {
  .@{ant-prefix}-input-affix-wrapper {
    // .@{ant-prefix}-input {
    //   padding-left: 40px !important;
    // }
    .@{ant-prefix}-input-prefix {
      left: 0 !important;
    }
  }
}

.material-input {
  .@{ant-prefix}-input-affix-wrapper:hover {
    .@{ant-prefix}-input,
    .@{ant-prefix}-input-number {
      border-color: var(--border-color);
      border-right-width: 0 !important;
    }
  }
  .@{ant-prefix}-input,
  .@{ant-prefix}-input-number {
    @apply border-neutral-lighter border-b border-t-0 border-l-0 border-r-0 rounded-none;

    background: transparent;
    border-color: var(--border-color);
    border-right-width: 0 !important;

    &:focus {
      border-right-width: 0 !important;
      outline: none;
      box-shadow: none;
    }

    &-handler-wrap {
      background: transparent;
      border-color: var(--border-color);

      span {
        color: var(--page-text-color);
        border-color: var(--border-color);

        i {
          color: inherit;
        }
      }
    }
  }

  &:focus,
  .@{ant-prefix}-input:focus,
  .@{ant-prefix}-select-selection:focus {
    border-right-width: 0 !important;
    outline: none;
    box-shadow: none;
  }

  &.@{ant-prefix}-input-affix-wrapper:hover {
    .@{ant-prefix}-input:not(.@{ant-prefix}-input-disabled) {
      border-color: @info-color;
      border-right-width: 0 !important;
    }
  }

  .@{ant-prefix}-select-selection {
    border-top-color: transparent;
    border-right-color: transparent;
    border-left-color: transparent;
  }
}

.read-only {
  .@{ant-prefix}-input-disabled {
    color: var(--neutral-dark);
    pointer-events: none;
    cursor: text;
  }

  .@{ant-prefix}-input-suffix {
    display: none;
  }

  .@{ant-prefix}-input:not(.material-input) {
    border: none;
  }

  &.material-input,
  .material-input {
    .@{ant-prefix}-input,
    .material-input {
      border-bottom: 1px solid var(--neutral-lighter);
    }
  }

  &:focus,
  .@{ant-prefix}-input:focus,
  .@{ant-prefix}-select-selection:focus {
    border-right-width: 0 !important;
    outline: none;
    box-shadow: none;
  }

  &.@{ant-prefix}-select-disabled {
    color: var(--neutral-dark);
  }

  &.@{ant-prefix}-input-affix-wrapper:hover {
    .@{ant-prefix}-input:not(.@{ant-prefix}-input-disabled) {
      border-color: @info-color;
      border-right-width: 0 !important;
    }
  }

  .@{ant-prefix}-select-selection,
  .@{ant-prefix}-input,
  .@{ant-prefix}-select-selection:hover {
    cursor: text;
    background: transparent;
    border-color: transparent;

    .@{ant-prefix}-select-arrow {
      display: none;
    }
  }

  &.material-input {
    .@{ant-prefix}-select-selection,
    .@{ant-prefix}-input,
    .@{ant-prefix}-select-selection:hover {
      border-bottom-color: var(--neutral-lighter);
    }
  }
}

.no-border {
  .@{ant-prefix}-select-selection {
    border: none;
  }
}

.transparent {
  .@{ant-prefix}-select-selection {
    background: transparent;
  }
}

.@{ant-prefix}-radio-button-wrapper {
  border-color: var(--border-color) !important;

  &:not(:first-child) {
    &::before {
      background-color: var(--border-color);
    }
  }

  &-checked {
    box-shadow: none !important;
  }
}

.@{ant-prefix}-checkbox-wrapper {
  color: var(--page-text-color) !important;

  .@{ant-prefix}-checkbox-inner {
    background: var(--checkbox-bg);
    border-color: var(--border-color);

    &::after {
      border-color: var(--checkbox-checked-color) !important;
    }
  }
  .@{ant-prefix}-checkbox-checked {
    .@{ant-prefix}-checkbox-inner {
      background: var(--primary);
      border-color: var(--primary);

      &::after {
        border-color: var(--white-regular) !important;
      }
    }
  }
  .@{ant-prefix}-checkbox-disabled {
    opacity: var(--disabled-checkbox-opacity);
    .@{ant-prefix}-checkbox-inner {
      background: var(--checkbox-bg);
      border-color: var(--border-color) !important;

      &::after {
        border-color: var(--checkbox-checked-color) !important;
      }
    }

    + span {
      color: var(--page-text-color);
    }
  }
}

&.@{ant-prefix}-input-affix-wrapper {
  .@{ant-prefix}-input-suffix {
    color: var(--input-suffix-text) !important;
  }
}

.@{ant-prefix}-calendar-picker {
  .@{ant-prefix}-calendar-picker-icon {
    color: var(--page-text-color);
  }
}

.rounded-radio-group {
  height: 30px;
  padding: 2px;
  background: var(--page-background-color);
  border-radius: 20px;

  .@{ant-prefix}-radio-button-wrapper {
    position: relative;
    top: -5px;
    height: 100%;
    padding: 0 16px !important;
    border: none;

    &::before {
      display: none;
    }

    &-checked {
      border-radius: 20px;
    }

    &:first-child {
      border-top-left-radius: 20px;
      border-bottom-left-radius: 20px;
    }

    &:last-child {
      border-top-right-radius: 20px;
      border-bottom-right-radius: 20px;
    }
  }

  &.top-group-match {
    .@{ant-prefix}-radio-button-wrapper {
      top: 0;
    }
  }
}

.rounded-background {
  .@{ant-prefix}-input {
    height: 30px;
    padding: @input-padding-horizontal-base @input-padding-vertical-base !important;
    background: var(--page-background-color) !important;
    border: 1px solid var(--border-color) !important;
    border-radius: 4px !important;
  }

  .has-error {
    .@{ant-prefix}-input {
      border-color: var(--secondary-red) !important;
    }
  }
}

.@{ant-prefix}-select {
  &-search__field {
    color: var(--page-text-color);
  }
  .@{ant-prefix}-select-selection {
    box-shadow: none !important;

    &:focus,
    &:active {
      box-shadow: none !important;
    }
  }

  &-selected-icon {
    display: none;
  }

  &-dropdown {
    background: var(--dropdown-background);

    &-menu-item {
      &-active {
        background: var(--left-menu-hover-bg) !important;
      }

      &-selected,
      &:hover {
        font-weight: 500;
        color: var(--left-menu-text-color-hover) !important;
        background: var(--left-menu-hover-bg) !important;
      }
    }
  }

  &-clear-icon {
    color: var(--text-neutral-ligher);
  }

  .@{ant-prefix}-select-selection__choice {
    display: inline-flex;
    align-items: center;
    padding: 0 17px 0 8px !important;
    line-height: @tag-height !important;
    color: var(--primary) !important;
    background: var(--tag-bg) !important;
    border: none !important;

    &__remove {
      line-height: 19px !important;
    }
    .@{ant-prefix}-select-remove-icon {
      font-size: 0.7rem;
      color: var(--primary);
    }
  }
}

.@{ant-prefix}-select.loose-tags-input,
.loose-tags-input {
  .@{ant-prefix}-select-selection {
    background: transparent;

    &__clear {
      display: none;
    }
  }
  &.@{ant-prefix}-select-open {
    .@{ant-prefix}-select-arrow-icon svg {
      transform: none;
    }
  }
  .@{ant-prefix}-select-selection__choice,
  .@{ant-prefix}-tag.@{ant-prefix}-tag-has-color {
    position: relative;
    height: 22px;
    margin-left: 12px !important;
    overflow: visible;
    color: var(--secondary-orange) !important;
    background: rgba(245, 133, 24, 0.2) !important;
    border-radius: 4px !important;
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;

    &::after {
      position: absolute;
      top: 50%;
      left: -22px;
      width: 0;
      height: 0;
      margin-top: -11px;
      pointer-events: none;
      content: '';
      border: solid transparent;
      border-width: 11px;
      border-right-color: rgba(245, 133, 24, 0.2);
    }

    .@{ant-prefix}-select-remove-icon {
      color: var(--secondary-orange);
    }
  }
}

.full-border-text-area {
  width: 100%;
  // padding: 10px;
  & .@{ant-prefix}-form-item {
    & .@{ant-prefix}-form-item-control-wrapper {
      & .@{ant-prefix}-form-item-control {
        & .@{ant-prefix}-input {
          padding: 10px;
          border: 1px solid var(--border-color);
          border-radius: 8px;
        }

        & textarea {
          padding: 10px !important;
        }
      }
    }
  }
}

.text-lg-input {
  input {
    @apply text-sm;
  }
}

.interval-time {
  .input-number {
    .@{ant-prefix}-input-number {
      border-bottom: none !important;
    }
  }
}
