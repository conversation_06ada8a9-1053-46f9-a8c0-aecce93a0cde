.search-bar-content {
  strong {
    color: var(--primary);
    text-transform: uppercase;
  }
}

[contenteditable='true'],
[contenteditable='false'] {
  &:focus {
    outline: none !important;
  }

  &:not(.iframe) {
    min-height: 100%;
  }

  &.search-bar-content {
    display: inline;
    overflow: hidden;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* Internet Explorer 10+ */

    br {
      display: none;
    }

    * {
      display: inline;
      white-space: nowrap;
    }

    &::-webkit-scrollbar {
      /* WebKit */
      width: 0;
      height: 0;
    }

    strong {
      color: var(--primary);
      text-transform: uppercase;
    }

    &:empty::before {
      color: var(--neutral-light);
      content: attr(data-placeholder);
    }
  }
}

.focusable {
  &:focus-within {
    border-radius: 4px;
    box-shadow: 0 0 0 2px rgba(50, 121, 190, 0.2);
  }
}

p.is-empty:first-child {
  &::before {
    float: left;
    height: 0;
    color: var(--neutral-light);
    pointer-events: none;
    content: attr(data-empty-text);
  }
}

.editor-container {
  .mention {
    padding: 0.2rem 0.5rem;
    white-space: nowrap;
    background: var(--neutral-lighter);

    @apply font-bold rounded;
  }

  img {
    max-width: 100%;
  }

  // stylelint-disable-next-line
  .ProseMirror {
    .tableWrapper {
      overflow: auto;
    }

    &.resize-cursor {
      cursor: col-resize;
    }
  }

  table {
    width: 100%;
    margin: 0;
    table-layout: fixed;
    border-collapse: collapse;

    td {
      position: relative;
      box-sizing: border-box;
      min-width: 1em;
      padding: 0.5rem;
      vertical-align: top;
      border: 1px solid var(--neutral-lighter);
    }

    .column-resize-handle {
      position: absolute;
      top: 0;
      right: -2px;
      bottom: 0;
      z-index: 20;
      width: 4px;
      pointer-events: none;
      background-color: @item-active-bg;
    }
  }
}

.tippy-tooltip.mentions-theme {
  padding: 0;
}

.editor-content {
  // * {
  //   line-height: 1rem !important;
  // }

  p {
    margin-bottom: 0.2rem;
    line-height: initial;
  }

  hr {
    display: block;
    width: 100%;
    min-width: 100%;
    height: 1px;
    min-height: unset !important;
    margin: 1rem 0;
    clear: both;
    border-color: var(--neutral-lighter);
    border-style: solid;
  }
}
