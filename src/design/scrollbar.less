// stylelint-disable

* {
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0);
    border-radius: 10px;
    scrollbar-color: #7f7f7f white;
    scrollbar-width: auto;
  }
  &:hover {
    &::-webkit-scrollbar-thumb {
      background: hsla(0, 0%, 53.3%, 0.4);
    }
  }
  ::-webkit-scrollbar-corner {
    background: transparent;
  }
}

* {
  scrollbar-width: auto;
}

.settings-left-menu-split-pane {
  .splitpanes__splitter {
    position: relative;
    width: 2px;
    cursor: col-resize;
  }
  .splitpanes__splitter::after {
    position: absolute;
    top: 97px;
    left: 0;
    z-index: 1;
    width: 5px;
    height: 30px;
    content: '';
    border: dotted 1px var(--border-color);
  }
}
.menu-invisible {
  .splitpanes__splitter {
    display: none;
  }
}

.report-form-collapse {
  display: flex;
  flex-direction: column;
  min-height: 0;

  .@{ant-prefix}-collapse-item {
    border-bottom: none;
  }
  .@{ant-prefix}-collapse-content-box {
    padding-left: 0 !important;
  }

  .@{ant-prefix}-collapse-item-active {
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;
    border-bottom: none;
  }
  .@{ant-prefix}-collapse-content-active {
    flex: 1;
    min-height: 0;
    .@{ant-prefix}-collapse-content-box {
      height: 100%;
    }
  }
  .splitpanes {
    display: flex;
    width: 100%;
    height: 100%;

    &--vertical {
      flex-direction: row;
    }
    &--horizontal {
      flex-direction: column;
    }
    &--dragging * {
      user-select: none;
    }

    &__pane {
      width: 100%;
      height: 100%;
      overflow: hidden;

      .splitpanes--vertical & {
        transition: width 0.2s ease-out;
      }
      .splitpanes--horizontal & {
        transition: height 0.2s ease-out;
      }
      .splitpanes--dragging & {
        transition: none;
      }
    }

    // Disable default zoom behavior on touch device when double tapping splitter.
    &__splitter {
      touch-action: none;
    }
    &--vertical > .splitpanes__splitter {
      min-width: 1px;
      cursor: col-resize;
    }
    &--horizontal > .splitpanes__splitter {
      min-height: 1px;
      cursor: row-resize;
    }
  }
  .splitpanes.default-theme {
    .splitpanes__pane {
      background-color: transparent;
    }
    .splitpanes__splitter {
      background-color: var(--border-color);
      box-sizing: border-box;
      position: relative;
      flex-shrink: 0;
      &:before,
      &:after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        background-color: var(--page-background-color);
        transition: background-color 0.3s;
      }
      &:hover:before,
      &:hover:after {
        background-color: var(--page-background-color);
      }
      &:first-child {
        cursor: auto;
      }
    }
  }
  .default-theme {
    &.splitpanes .splitpanes .splitpanes__splitter {
      z-index: 1;
    }
    &.splitpanes--vertical > .splitpanes__splitter,
    .splitpanes--vertical > .splitpanes__splitter {
      width: 7px;
      border-left: 1px solid var(-border-color);
      margin-left: -1px;
      &:before {
        transform: translateY(-50%);
        width: 1px;
        height: 30px;
      }
      &:before {
        margin-left: -2px;
      }
    }
    &.splitpanes--horizontal > .splitpanes__splitter,
    .splitpanes--horizontal > .splitpanes__splitter {
      height: 1px;
      border-top: 1px solid var(--border-color);
      margin-top: -1px;
      &:before {
        transform: translateX(-50%);
        width: 45px;
        height: 4px;
        border-radius: 5px;
      }
      &:before {
        margin-top: 2px;
        background: var(--border-color);
      }
      &:hover:before {
        background-color: var(--border-color);
      }
    }
  }
}
