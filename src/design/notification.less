.@{ant-prefix}-notification {
  z-index: 1055 !important;

  .error-notification {
    color: white;
    background-color: var(--secondary-red);
  }

  .success-notification {
    color: white;
    background-color: var(--secondary-green);
  }

  &-notice {
    cursor: pointer;
    background-color: var(--primary);
  }

  .@{ant-prefix}-notification-notice-message,
  .@{ant-prefix}-notification-notice-description,
  .@{ant-prefix}-notification-notice-icon,
  .@{ant-prefix}-notification-close-icon {
    color: white;
  }
}

.@{ant-prefix}-message {
  top: 0 !important;
  z-index: 1056;

  &-notice-content {
    padding: 0;
  }

  &-custom-content {
    padding: 10px 16px;
    color: white;
    border-radius: 4px;

    .@{ant-prefix}icon {
      color: white;
    }
  }

  &-error {
    background: var(--secondary-red);
  }

  &-success {
    background: var(--secondary-green);
  }

  &-warning {
    background: var(--secondary-orange);
  }

  &-info,
  &-loading {
    background: var(--primary-alt);
  }
}

.rounded-counter {
  width: 50px;
  height: 50px;
  font-size: 25px;
  font-weight: bold;
  line-height: 50px;
  text-align: center;
  border: 2px solid var(--primary);
  border-radius: 50%;

  &.warning {
    border-color: var(--secondary-orange);
  }

  &.success {
    border-color: var(--secondary-green);
  }

  &.error {
    border-color: var(--secondary-red);
  }

  &.neutral-lighter {
    border-color: var(--neutral-lighter);
  }

  &.info {
    border-color: @warning-color;
  }

  &.fill {
    @apply bg-primary text-white;

    &.warning {
      @apply bg-secondary-orange;
    }

    &.success {
      @apply bg-secondary-green;
    }

    &.error {
      @apply bg-secondary-red;
    }

    .info {
      background: @warning-color;
    }

    &.neutral-lighter {
      border-color: bg-neutral-lighter;
    }
  }
}
