.@{ant-prefix}-drawer-wrapper-body {
  & .@{ant-prefix}-form-item {
    & .@{ant-prefix}-form-item-control-wrapper {
      & .@{ant-prefix}-form-item-control {
        & .@{ant-prefix}-input {
          padding-left: 0.25rem;
        }
        & .@{ant-prefix}-input-affix-wrapper {
          & .@{ant-prefix}-input-prefix + .@{ant-prefix}-input {
            padding-left: 30px;
          }
        }
        & .@{ant-prefix}-calendar-picker-default {
          & .@{ant-prefix}-calendar-picker-input {
            padding-bottom: 7px;
            margin-bottom: 0;
          }
          & .@{ant-prefix}-calendar-picker-icon {
            color: var(--neutral-regular);
          }
        }
      }
    }
  }
}

.has-info-tooltip {
  .@{ant-prefix}-form-item {
    & label {
      &.@{ant-prefix}-form-item-required {
        &::after {
          right: 18px;
        }
      }
    }
  }
}

.@{ant-prefix}-form-item {
  position: unset;

  .@{ant-prefix}-form-explain {
    color: var(--text-neutral-ligher);
  }

  & .@{ant-prefix}-form-item-label {
    line-height: 1.5;

    & label {
      font-size: @text-sm;
      color: var(--text-color-common-secondary);

      &.@{ant-prefix}-form-item-required {
        &::after {
          color: var(--secondary-red);
        }
      }
    }
  }

  & .@{ant-prefix}-form-item-control-wrapper {
    & .@{ant-prefix}-form-item-control {
      position: unset;
      .@{ant-prefix}-form-item-children {
        position: unset;
      }

      & .multi-select-group {
        float: left;
        width: 100%;
        margin-top: 8px;
        line-height: 20px;
        & .@{ant-prefix}-tag {
          margin-bottom: 6px;
        }
      }

      & .create-group {
        & .label-list {
          padding: 0 !important;
          line-height: 24px;
        }
      }
      & .@{ant-prefix}-input,
      .@{ant-prefix}-input-number {
        padding-left: 0;
        //padding-left: 0;
        color: var(--input-text-color);
        background: none;
        border-top: none;
        border-right: none;
        border-bottom-color: var(--border-color);
        border-left: none;
        border-radius: 0;

        &:focus {
          outline: none;
          box-shadow: none;
        }
      }

      &.has-error {
        .@{ant-prefix}-input,
        .@{ant-prefix}-input-number {
          border-bottom-color: var(--secondary-red);
        }

        .multiple-dropdown-trigger {
          border-color: var(--secondary-red);
        }

        .dropdown-trigger-input {
          input.@{ant-prefix}-input {
            border-color: var(--secondary-red) !important;
          }
        }
      }

      & .@{ant-prefix}-input[type='text']:disabled {
        background: var(--page-background-color);
      }

      & .@{ant-prefix}-select {
        margin-top: 4px;

        & .@{ant-prefix}-select-selection {
          color: var(--neutral-darkest);
          border-top: none;
          border-right: none;
          border-bottom-color: var(--border-color);
          border-left: none;
          border-radius: 0;

          &:focus {
            outline: none;
            box-shadow: none;
          }

          & .@{ant-prefix}-select-selection__rendered {
            padding-left: 0;
            margin-left: 0;

            & .@{ant-prefix}-select-selection__placeholder {
              padding-left: 0;
              margin-left: 0;
            }

            & ul {
              & li {
                padding: @close-tag-padd;
                margin: @tag-mar;
                line-height: 26px;
                background: none;
                border-radius: 100px;

                & .@{ant-prefix}-select-selection__choice__remove {
                  line-height: 21px;
                }
              }
            }
          }
        }
      }

      & .@{ant-prefix}-select-disabled {
        & .@{ant-prefix}-select-selection {
          background: var(--page-background-color);
        }
      }
    }
    & .@{ant-prefix}-form-explain {
      font-size: 0.7rem;
    }
  }
}

& .create-group-action-panel {
  & .@{ant-prefix}-form-item-children {
    float: left;
    width: 100%;

    & .create-group,
    .role-name {
      line-height: 20px;

      & a.link-label {
        position: absolute;
        top: -5px;
        right: 15px;
        width: auto;
      }
    }
  }

  & .label-list {
    padding-left: 0 !important;
    font-weight: 300;
    line-height: 20px;
  }
  & .@{ant-prefix}-divider {
    margin: 0;
  }
}

& .create-group-action {
  float: left;
  width: 100%;

  & a {
    position: absolute;
    top: -35px;
    right: 15px;
    width: 100px;
  }
}

& .pad-input-s,
& .monitor-search-box {
  & .@{ant-prefix}-input {
    padding-left: 0;
  }
}

& .days-input-box {
  & span.days {
    position: absolute;
    right: -12px;
    bottom: -13px;
    height: 40px;
    padding: 0 8px;
    font-size: 0.9rem;
    line-height: 40px !important;
    color: var(--input-suffix-text);
    background: var(--input-suffix-bg);
  }
}

& .info-ip-host {
  position: absolute;
  top: 0;
  left: 80px;
  z-index: 1;
  cursor: pointer;

  & .anticon {
    color: var(--primary);
  }
}

& .info-guide {
  float: left;
  margin: 2px 0 0 5px;
  cursor: pointer;

  & .anticon {
    color: var(--primary-alt);
  }
}

.right-side-content-view-panel.bordered {
  border-left: solid 1px var(--border-color);
}

& .@{ant-prefix}-form {
  color: var(--text-color-common-primery);
  & .@{ant-prefix}-form-item {
    .@{ant-prefix}-form-explain {
      color: var(--page-text-color);
    }

    .has-error {
      .@{ant-prefix}-form-explain {
        color: var(--secondary-red) !important;
      }
    }
    & .@{ant-prefix}-form-item-control-wrapper {
      & .@{ant-prefix}-form-item-control {
        & .@{ant-prefix}-input {
          padding-left: 0;
        }
        & .@{ant-prefix}-input-affix-wrapper {
          & .@{ant-prefix}-input-prefix + .@{ant-prefix}-input {
            padding-left: 30px;
          }
        }

        & .multi-select-group {
          margin-top: 0;
          & .@{ant-prefix}-select-selection--multiple {
            padding-bottom: 2px;
            border-color: var(--border-color);
          }
        }
      }
    }
  }

  & label {
    color: var(--text-color-common-secondary);
  }

  & .@{ant-prefix}-radio-button-wrapper {
    background: var(--radio-btn-box-bg);
    border-color: var(--radio-btn-box-border-color);
    &.@{ant-prefix}-radio-button-wrapper-checked {
      background: var(--radio-btn-box-selected-bg);
      border-color: var(--radio-btn-box-selected-bg);
    }
  }
  & .@{ant-prefix}-input-number {
    background: none;
    & .@{ant-prefix}-input-number-input {
      padding-left: 0;
      color: var(--text-color-common-primery);
    }
    &.@{ant-prefix}-input-number-focused {
      outline: none;
      box-shadow: none;
    }

    &:focus {
      outline: none !important;
      box-shadow: none !important;
    }
  }
}

& .credential-successfully {
  font-size: 0.9rem;

  & .anticon {
    margin-right: 5px;
    font-size: 1.3rem;
  }
}

& .spacer-ldap-server {
  & .@{ant-prefix}-form-item-label {
    margin-right: 15px;
  }
}

& .spacer-auto-sync {
  & .@{ant-prefix}-form-item-label {
    margin-right: 84px;
  }

  & .@{ant-prefix}-form-item {
    margin-top: 34px;
  }
}

// required validation message remove
.has-required-validation {
  & .@{ant-prefix}-form-item-label {
    & label {
      color: var(--secondary-red);
    }
  }

  .@{ant-prefix}-form-explain {
    display: none;
  }
}

.@{ant-prefix}-switch {
  background: var(--switch-bg);
  border: 1px solid var(--switch-border);
  .@{ant-prefix}-switch-inner {
    font-size: @font-size-10;
  }

  &::after {
    top: 2px;
    left: 3px;
    width: 17px;
    height: 17px;
  }
  &.@{ant-prefix}-switch-checked::after {
    left: 100%;
  }
}

& .loginLayout {
  & .@{ant-prefix}-input {
    padding-left: 0;
  }
}

& .group-selector-s {
  & .@{ant-prefix}-form-explain {
    position: absolute;
    top: 41px;
    color: var(--neutral-light);
  }
}
& .@{ant-prefix}-radio-group {
  & .@{ant-prefix}-radio-button-wrapper {
    padding: 0 11px;
    background: var(--page-background-color);
    border-color: var(--border-color);

    & span {
      color: var(--neutral-regular);

      & i.anticon {
        position: relative;
        top: 0;
        margin-right: 0.5rem;
      }
    }
    &.@{ant-prefix}-radio-button-wrapper-checked {
      padding: 0 11px;
      background: var(--primary);

      & span {
        color: var(--active-text-color);
      }
    }
  }
}

.small-label {
  font-size: 0.8rem;
  color: var(--neutral-light);
}

.label-content {
  & a {
    font-size: 1rem;
  }
}
.@{ant-prefix}-divider {
  background: var(--border-color);
}

& .policy-form {
  & .box-line {
    & .line {
      width: 2px !important;
    }
  }
}

.number-int {
  & .@{ant-prefix}-input-number {
    border: none;
    border-bottom: solid 1px var(--border-color);
    border-radius: 0;
    & .@{ant-prefix}-input-number-input {
      padding-left: 0;
    }

    &:focus {
      outline: none;
      box-shadow: none;
    }
  }
}

.user-profile {
  & .avatar-holder {
    & .profile-picture-selector {
      width: 110px;
      border: none;

      & canvas {
        width: 100% !important;
        height: auto !important;
      }
    }
  }

  & .form-feild-align {
    & .form-item-pristine {
      & .@{ant-prefix}-form-item-label {
        display: flex;
        align-items: center;
        padding-right: 10px;

        & label {
          &::after {
            margin-right: 0;
          }
        }
      }
    }
    & .@{ant-prefix}-form-item-label {
      display: flex;
      align-items: center;
      padding-right: 10px;

      & label {
        &::after {
          margin-right: 0;
        }
      }
    }
  }
}

.and-spacer {
  background: none !important;
  & .@{ant-prefix}-divider-inner-text {
    float: left;
    padding: 4px 10px !important;
    margin-left: -2px;
    background: var(--neutral-lightest);
    border-right: solid 7px var(--border-color);
    border-radius: 3px;
  }
}

.no-padding-form {
  & .@{ant-prefix}-form-item {
    padding-bottom: 0;
    margin-bottom: 0;

    .@{ant-prefix}-form-explain {
      min-height: unset;
      margin-bottom: 5px;
    }
  }

  & .label-strate-fix {
    & label {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      &:hover {
        white-space: normal;
      }
    }
  }
}

.policy-form-panel {
  & .@{ant-prefix}-input {
    background: none !important;
    border-right: none;
  }

  .notify-full-w {
    & .option-text {
      width: 99%;
    }
  }

  & .toggleArrow {
    position: absolute;
    top: -12px;
    right: 5px;
    font-size: 1.6rem;
    font-weight: 600;
    color: var(--neutral-regular);
    cursor: pointer;
  }

  & div.content,
  div.equality-input {
    top: 0;
    height: 32px;
  }
}

.label-highlight-select-value {
  display: flex;
  align-items: center;
  border: 1px solid var(--border-color);
  border-radius: 5px;

  & .severity {
    width: 120px;
    margin-right: 10px;
    font-weight: 400;
    color: var(--white-regular);

    @apply px-6 py-2;
  }

  & .content {
    position: relative;
    top: 10px;
    border-right: 1px solid var(--border-color);

    .dropdown-trigger-input {
      input.@{ant-prefix}-input {
        border-bottom: none !important;
      }
    }
  }

  & .equality-input {
    position: relative;
    top: 10px;
    flex: 1;
    margin-left: 10px;

    input.@{ant-prefix}-input {
      border-bottom: none !important;
    }
    .@{ant-prefix}-input-suffix {
      right: 0;
      height: 37px;
    }
  }

  & .severity-wrapper {
    width: 65px;
    height: 39px;
    color: white;
  }

  & .equality-wrapper {
    width: 65px;
    height: 39px;
  }

  & .per-input-box {
    display: flex;
    align-items: center;
    justify-content: center;
    height: @input-height-base;
    padding: 0 6px;
    font-size: 0.9rem;
    color: #7b8fa5;
    background: var(--code-tag-background-color);
  }
}

.policy-form {
  .divider {
    & > div,
    span > div {
      border-bottom: solid 1px var(--border-color);
    }
  }
  // & .pr-0 {
  //   @apply pr-0 !important;
  // }
  // & .pl-0 {
  //   @apply pl-0 !important;
  // }
  .form-box-container {
    position: relative;
    background: var(--widget-background-color);
    // border: 1px solid var(--border-color);
    border: none;
    border-radius: 0.15rem;
    transition: all 0.2s linear;

    @apply px-3 py-2 mt-2 mb-4;

    &:focus-within,
    &:hover {
      border-color: var(--primary);
      // box-shadow: 0 4px 6px -1px rgba(9, 157, 217, 0.2), 0 2px 4px -1px rgba(9, 157, 217, 0.2);
      box-shadow: 0 4px 6px -1px rgba(20, 33, 45, 0.1);
    }
  }

  .box-line {
    position: relative;

    .line {
      position: absolute;
      left: 30px;
      z-index: 0;
      display: none;
      width: 3px;
      height: 100%;
      content: ' ';
      background: var(--neutral-lighter);
    }
  }
}

.alert-inner-panel {
  & .form-item-pristine {
    & .@{ant-prefix}-form-item {
      margin-bottom: 0;
      & .@{ant-prefix}-form-item-control {
        line-height: 32px;
      }
    }
  }
}

.custom-time-select {
  // background: var(--common-main-bg);

  i {
    @apply text-neutral !important;

    font-size: @font-size-base !important;
  }

  & .icon-cal {
    position: relative;
    top: -2px;
    display: inline-block;
    margin-right: 10px;
  }

  & .select-time-picker {
    display: inline-block;
  }
}

.num-spac-w {
  & input.@{ant-prefix}-input {
    width: 80px !important;
  }

  & .ant-form-explain {
    white-space: normal;
  }
}

.alert-multi-spacer {
  .form-item-pristine {
    & .@{ant-prefix}-form-item {
      & .@{ant-prefix}-form-item-control {
        .@{ant-prefix}-input {
          border-bottom: solid 1px var(--border-color);
        }
      }
    }
  }
}

.discovery-schedule-list {
  & .mar-t-spacer-s {
    margin-top: 0;
  }
}

.radio-toggle-shadow {
  .@{ant-prefix}-radio-button-wrapper {
    border: none;

    &-checked {
      color: var(--page-text-color) !important;
      background: var(--code-tag-background-color) !important;
      border: 1px solid var(--neutral-lighter);
      border-radius: 4px;

      // box-shadow: 0 0 5px 1px var(--text-neutral-ligher) inset !important;

      span {
        color: var(--page-text-color) !important;
      }
    }
  }
}

.alert-severity-buttons {
  &::before {
    position: absolute;
    top: 50%;
    left: 0;
    width: 1px;
    height: 16px;
    content: ' ';
    background: var(--border-color) !important;
    transform: translateY(-50%);
  }
  .@{ant-prefix}-radio-button-wrapper {
    margin: 0 12px;
  }
  .@{ant-prefix}-radio-button-wrapper > span {
    display: flex;
    align-items: center;
  }
  .@{ant-prefix}-radio-button-wrapper:not(:first-child)::before {
    top: 50%;
    left: -12px;
    height: 16px;
    background-color: var(--border-color) !important;
    opacity: 1;
    transform: translateY(-50%);
  }
}

// .vue-codemirror {
//   height: 100%;
//   // stylelint-disable-next-line
//   .CodeMirror {
//     height: 100%;
//   }
// }

.flex-form-control {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 0;

  .@{ant-prefix}-form-item {
    display: flex;
    flex: 1;
    flex-direction: column;
    min-height: 0;

    &-control-wrapper,
    &-control,
    &-children {
      display: flex;
      flex: 1;
      flex-direction: column;
      min-height: 0;
    }
  }
}

.flip-buttons {
  &.ant-form {
    button.ant-btn.ant-btn-primary {
      float: right;
      margin-left: 14px;
    }
  }
}

.form-item-pill {
  display: flex;
  overflow: hidden;

  & .content {
    position: relative;
    background: var(--widget-background-color) !important;
    border-left: 1px solid var(--border-color);

    .no-border-dropdown {
      .multiple-dropdown-trigger {
        padding: 0;
        background: var(--widget-background-color) !important;
        border: none;
      }

      span.dropdown-trigger-input {
        input.@{ant-prefix}-input {
          border-bottom: none !important;
        }
      }
    }
  }

  & .equality-input {
    position: relative;
    margin-left: 10px;

    input.@{ant-prefix}-input {
      border-bottom: none !important;
    }

    .ant-row.ant-form-item.ant-form-item-no-colon {
      padding: 0 !important;
      margin: 0 !important;
    }
  }
}

.no-border-text-area {
  width: 100%;
  padding: 10px;
  & .@{ant-prefix}-form-item {
    & .@{ant-prefix}-form-item-control-wrapper {
      & .@{ant-prefix}-form-item-control {
        & .@{ant-prefix}-input {
          border: none;
        }
      }
    }
  }
}

.no-label-form-item {
  .ant-form-item-label {
    display: none !important;
  }

  .ant-form-item {
    padding: 0;
    margin: 0;
  }
}

.form-feild-align-top {
  & .form-item-pristine {
    & .@{ant-prefix}-form-item-label {
      display: flex;
      align-items: flex-start;
      padding-right: 10px;

      & label {
        &::after {
          margin-right: 0;
        }
      }
    }
  }
}
