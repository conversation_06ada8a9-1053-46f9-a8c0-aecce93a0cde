.discovery-boat-panel {
  &.open {
    height: 50vh;
    max-height: 50vh;
  }

  &.minimized {
    position: fixed;
    right: 0;
    right: -111px;
    bottom: 0;
    bottom: 160px;
    z-index: 999;
    display: flex;
    flex-direction: column;
    width: @chat-width;
    width: auto;
    background: var(--page-background-color);
    border: solid 1px var(--border-color);
    border-radius: 3px 3px 0 0;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    box-shadow: -2px 0 10px -2px rgba(0, 0, 0, 0.2);
    transform: rotate(270deg);

    @apply bg-primary;

    .discovery-header {
      .action-box {
        top: 0;
        border-bottom: 0;
      }
    }
  }

  & .discovery-header {
    float: left;
    width: 100%;

    @apply px-4;

    .action-box {
      position: relative;
      top: -5px;
      border-bottom: 1px solid var(--border-color);
    }

    & .discovery-header-title {
      font-size: @chat-title;
      font-weight: normal;
      color: var(--primary);
    }

    & .btn-down {
      height: auto;
      padding: 0 0 0 5px;
      font-size: @chat-title-arrow;
    }

    & .discovery-run-btn {
      & button {
        height: auto;
        padding: 0 0 0 5px;
        font-size: @chat-title-arrow;
      }
    }
    & .@{ant-prefix}-progress {
      & .@{ant-prefix}-progress-outer {
        & .@{ant-prefix}-progress-inner {
          background: var(--neutral-lighter);
          & .@{ant-prefix}-progress-bg {
            background: var(--primary);
          }
        }
      }
    }
  }

  .content {
    &.open {
      height: 50vh;
      max-height: 50vh;
    }
  }

  & .discovery-log-box {
    padding: 0;
    margin: 0;
    list-style: none;

    & li {
      padding-bottom: 2px;
      margin-bottom: 10px;
      border-bottom: solid 1px var(--border-color);

      & a {
        padding: 0;
        margin: 0;
        font-size: @chat-title;
      }

      & p {
        padding: 0;
        margin: 0;
        font-size: @chat-title-arrow;
      }
    }
  }
}

.add-application-run {
  padding-bottom: 5px;
  margin-bottom: 20px;
  border-bottom: solid 1px var(--border-color);
  & .@{ant-prefix}-form-item-control {
    line-height: 20px;

    & input[disabled='disabled'] {
      border-bottom: none;
    }
  }
}
