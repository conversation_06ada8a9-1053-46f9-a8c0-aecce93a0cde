.@{ant-prefix}-pagination {
  text-align: left;

  &.mini {
    .@{ant-prefix}-pagination-item,
    .@{ant-prefix}-pagination-prev,
    .@{ant-prefix}-pagination-next {
      margin-right: 4px;
    }
  }

  & li {
    width: auto;
    min-width: 30px !important;
    height: 30px !important;
    padding: 0 5px !important;
    margin-right: 10px !important;
    line-height: 30px !important;
    border-radius: 3px !important;
    & ul.@{ant-prefix}-select-dropdown-menu {
      li.@{ant-prefix}-select-dropdown-menu-item {
        margin-right: 0 !important;
      }
    }

    & a {
      font-weight: normal;
    }

    &:hover {
      color: var(--neutral-regular) !important;
      background: var(--neutral-lightest) !important;

      & a {
        font-weight: normal;
        color: var(--neutral-regular) !important;
      }
    }
    &.@{ant-prefix}-pagination-item-active {
      color: var(--neutral-regular) !important;
      background: var(--neutral-lightest) !important;

      & a {
        font-size: @text-sm;
        font-weight: normal;
        color: var(--neutral-regular) !important;
      }
    }
  }
  & li.@{ant-prefix}-pagination-total-text {
    float: right;
    width: auto;
    height: auto;
    margin-right: 0;
    font-size: @text-sm;

    &:hover {
      color: var(--neutral-dark) !important;
      background: none !important;
    }
  }
  & li.@{ant-prefix}-pagination-prev,
  li.@{ant-prefix}-pagination-next {
    padding: 0 !important;

    & a {
      border-radius: 4px !important;

      &:hover {
        color: var(--neutral-regular) !important;
        background: var(--neutral-lightest) !important;
      }
    }
  }
  & li.@{ant-prefix}-pagination-options {
    padding: 0 !important;
    margin: 0 !important;
    & .@{ant-prefix}-select {
      padding: 0;
      margin: 0;
      & .@{ant-prefix}-select-selection {
        height: 30px;
        line-height: 30px;
        & .@{ant-prefix}-select-selection__rendered {
          font-size: @text-sm;
          line-height: 28px;
        }
      }
    }
  }
}
