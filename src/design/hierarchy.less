.item-action:not(.sticky-action) {
  opacity: 0;

  &:hover {
    opacity: 1;
  }
}

.hierarchy-item {
  position: relative;
  z-index: 1;

  .title-container {
    z-index: 1;
    min-height: 40px;

    @apply font-normal py-2 px-1 rounded mr-2;

    &.with-bg {
      color: var(--group-text-color);
      background: var(--group-list-bg);
    }

    &.seperator {
      border-bottom: 1px solid var(--hieararchy-border-color);
      border-radius: 0;
    }

    & label.text-ellipsis {
      height: 20px;
    }
  }

  // .item-action:not(.sticky-action) {
  //   opacity: 0;
  // }

  &:hover {
    .item-action {
      opacity: 1;
    }
  }
}

.hierarchy-explorer {
  ul {
    padding: 0;
    margin: 0;
    list-style: none;
  }

  ul,
  li {
    position: relative;
  }

  li {
    &::before,
    &::after {
      position: absolute;
      left: 15px;
      content: '';
      opacity: 0.7;
    }

    &::before {
      top: 20px;
      width: 25px;
      height: 100%;
      margin-left: -20px;
      border-top: 1px solid var(--hieararchy-border-color);
    }

    &::after {
      top: -12px;
      width: 20px;
      height: calc(100% + 20px);
      margin-left: -20px;
      border-left: 1px solid var(--hieararchy-border-color);
    }
  }

  ul.wide-gap {
    li {
      &::before,
      &::after {
        position: absolute;
        left: -12px;
        content: '';
        opacity: 0.7;
      }

      &::before {
        top: 20px;
        width: 50px;
        height: 100%;
        margin-left: -20px;
        border-top: 1px solid var(--hieararchy-border-color);
      }

      &::after {
        top: -12px;
        width: 40px;
        height: calc(100% + 12px);
        margin-left: -20px;
        border-left: 1px solid var(--hieararchy-border-color);
      }
    }
  }

  &.group-picker-hierarchy {
    min-width: 20vw;
  }

  // disable lines for first level
  .sortable-list > li {
    &::before,
    &::after {
      display: none;
    }
  }

  ul > li:last-child::after {
    // height: calc(100% + -7px);
    height: 32px;
  }
}

& .group-tree-view-panel {
  & .hierarchy-explorer {
    & ul.sortable-list {
      & li.sortable-item {
        & .hierarchy-item {
          & .title-container {
            position: relative;
            margin-right: 0;

            &:hover,
            &.active {
              background: var(--group-list-hover-bg);
            }

            & .action-handle {
              position: absolute;
              right: 15px;
            }
          }
        }
      }
    }
  }
}

& .monitor-hierarchy-view {
  & .hierarchy-explorer {
    // stylelint-disable-next-line
    .vue-recycle-scroller__item-wrapper {
      display: flex;
      flex: 1;
      width: unset !important;
      overflow: unset !important;
      // stylelint-disable-next-line
      .vue-recycle-scroller__item-view {
        display: flex;
        flex: 1;

        & .hierarchy-item {
          & .title-container {
            position: relative;
            min-height: unset;
            padding-top: 0.2rem;
            padding-bottom: 0.2rem;
            margin-right: 0;

            &:hover,
            &.active {
              background: var(--group-list-hover-bg);
            }

            & .action-handle {
              position: absolute;
              right: 15px;
            }
          }
        }
      }
    }
  }
}

& .arithmetic-option-hierarchy {
  & .hierarchy-explorer {
    li {
      &::before {
        border: none;
      }

      &::after {
        border: none;
      }
    }

    ul.sortable-list-child {
      padding: 0 !important;
    }

    & ul.sortable-list {
      & li.sortable-item {
        & .hierarchy-item {
          & .title-container {
            position: relative;
            margin-right: 0;

            &:hover,
            &.active {
              background: var(--group-list-hover-bg);
            }

            & .action-handle {
              position: absolute;
              right: 15px;
            }
          }
        }
      }
    }
  }
}
