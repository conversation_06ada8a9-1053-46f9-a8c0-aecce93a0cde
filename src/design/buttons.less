.@{ant-prefix}-btn.rounded {
  border-radius: 4px;

  &.@{ant-prefix}-btn-sm {
    line-height: 10px;
  }
}

.@{ant-prefix}-btn {
  &:not(&-sm) {
    height: @btn-height;
  }

  &.button-shadow {
    box-shadow: none !important;

    &:hover {
      box-shadow: none !important;
    }
  }
}

.@{ant-prefix}-btn.@{ant-prefix}-btn-primary {
  &:not(.button-transparent, .button-error, .button-primary-alt, .button-neutral-lighter, .button-neutral-lightest) {
    color: var(--primary-button-text);
    background: var(--primary-button-bg);

    &:hover {
      color: var(--primary-button-hover-text);
      background: var(--primary-button-hover-bg) !important;
      box-shadow: 0 0 8px -2px var(--primary-button-bg) !important;
    }
  }

  box-shadow: none;
}
.@{ant-prefix}-btn-primary {
  color: var(--primary-button-text);
  background: var(--primary-button-bg);
  border-color: var(--primary-button-bg);

  &:hover {
    color: var(--primary-button-hover-text);
    background: var(--primary-button-hover-bg) !important;
    border-color: var(--primary-button-hover-bg);
    box-shadow: 0 0 8px -2px var(--primary-button-bg) !important;
  }

  &.button-neutral-lighter {
    color: var(--neutral-button-text);
    background: var(--neutral-button-bg) !important;
    border-color: var(--neutral-button-bg);

    &:hover {
      color: var(--neutral-button-hover-text);
      background: var(--neutral-button-hover-bg) !important;
      border-color: var(--neutral-button-hover-bg);
      box-shadow: none !important;
    }
  }

  &.button-neutral-lightest {
    color: var(--neutral-button-text);
    background: var(--code-tag-background-color) !important;
    border-color: var(--code-tag-background-color);

    &:hover {
      color: var(--neutral-button-text) !important;
      background: var(--code-tag-background-color) !important;
      border-color: var(--code-tag-background-color) !important;
      box-shadow: none !important;
    }
  }
}
.@{ant-prefix}-btn-default {
  color: var(--default-button-text);
  background: var(--default-button-bg);
  border-color: var(--default-button-border);
  box-shadow: none;

  &:hover,
  &:active,
  &:focus {
    color: var(--default-button-hover-text);
    background: var(--default-button-hover-bg);
    border: solid 1px var(--default-button-hover-bg);
    border-color: var(--default-button-hover-border);
    box-shadow: 0 0 8px -2px var(--default-button-hover-bg);
  }
}

.button-transparent {
  color: var(--button-transparent-text) !important;
  background: transparent !important;
  border-color: transparent !important;

  &:hover {
    color: var(--button-transparent-hover-text) !important;
    background: initial !important;
    border-color: transparent !important;
    box-shadow: initial !important;
  }
}

.button-error {
  color: var(--error-button-text);
  background: var(--error-button-bg);
  border-color: var(--error-button-bg);

  &:hover {
    color: var(--error-button-hover-text);
    background: var(--error-button-hover-bg) !important;
    border-color: var(--error-button-bg) !important;
    box-shadow: 0 0 8px -2px var(--error-button-bg) !important;
  }

  &[disabled]:hover {
    background-color: unset !important;
    box-shadow: none !important;
  }
}

.button-success {
  color: var(--success-button-text);
  background: var(--success-button-bg);
  border-color: var(--success-button-bg);

  &:hover {
    color: var(--success-button-hover-text);
    background: var(--success-button-hover-bg) !important;
    border-color: var(--success-button-bg) !important;
    box-shadow: 0 0 8px -2px var(--success-button-bg) !important;
  }

  &[disabled]:hover {
    background-color: unset !important;
    box-shadow: none !important;
  }
}

.@{ant-prefix}-btn.button-primary-alt {
  background: var(--primary-alt);

  &:hover {
    background: var(--primary-alt);
    box-shadow: 0 0 8px -2px var(--primary-alt);
  }
}

.@{ant-prefix}-btn-primary.@{ant-prefix}-btn-background-ghost {
  &:not([disabled]) {
    color: var(--outline-button-text);
    background: var(--outline-button-bg) !important;
    box-shadow: none;

    &:hover {
      color: var(--outline-button-hover-text);
      background: var(--outline-button-hover-bg) !important;
      box-shadow: 0 0 8px -2px var(--outline-button-hover-bg);
    }
  }
}

.@{ant-prefix}-btn[disabled] {
  color: var(--button-disabled-text) !important;
  background: var(--button-disabled-bg) !important;
  border-color: var(--button-disabled-border) !important;

  &:hover {
    color: var(--button-disabled-text) !important;
    background: var(--button-disabled-bg) !important;
    border-color: var(--button-disabled-border) !important;
  }
}

.@{ant-prefix}-btn.button-error.@{ant-prefix}-btn-background-ghost {
  color: var(--error-outline-button-text);
  background: var(--error-outline-button-bg) !important;
  box-shadow: none;

  &:hover {
    color: var(--error-outline-button-hover-text);
    background: var(--error-outline-button-hover-bg) !important;
    box-shadow: 0 0 8px -2px var(--error-outline-button-hover-bg);
  }
}

.@{ant-prefix}-btn.button-success.@{ant-prefix}-btn-background-ghost {
  color: var(--success-outline-button-text);
  background: var(--success-outline-button-bg) !important;
  box-shadow: none;

  &:hover {
    color: var(--success-outline-button-hover-text);
    background: var(--success-outline-button-hover-bg) !important;
    box-shadow: 0 0 8px -2px var(--success-outline-button-hover-bg);
  }
}

.@{ant-prefix}-btn-circle,
.@{ant-prefix}-btn-circle-outline {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: @text-regular;

  .@{ant-prefix}icon {
    top: 0;
  }
}

.squared-button {
  display: inline-flex;
  align-items: center;
  justify-self: center;
  width: 35px !important;
  height: 35px !important;
  padding: 8px !important;
  font-size: 1rem;
}

.model-header-button {
  height: 2.1rem !important;
  padding: 0 15px !important;
  font-size: 0.8rem !important;
  line-height: 1.499 !important;

  &.primary {
    background: var(--primary-button-bg) !important;
  }
}
