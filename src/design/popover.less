.readable-content-overlay {
  .picker-overlay;

  .@{ant-prefix}-popover-inner {
    max-width: 50vw;
    padding: 0 10px !important;
    overflow: auto;

    &-content {
      color: unset;
    }

    @apply px-4 py-2;
  }

  &.no-padding {
    padding-top: 8px !important;

    .@{ant-prefix}-popover-inner {
      padding: 0 !important;
    }
  }

  &.widget-error {
    .@{ant-prefix}-popover-inner {
      color: var(--tooltip-text-color) !important;
      background: var(--tooltip-background-color) !important;
    }
  }

  &.@{ant-prefix}-tooltip {
    max-width: 50vw;
  }

  .@{ant-prefix}-tooltip-inner {
    max-width: 50vw;
    overflow: auto;
    color: var(--tooltip-text-color) !important;
    background: var(--tooltip-background-color) !important;
  }
}

.tippy-tooltip {
  .tippy-content {
    padding: 0;
  }
}

.picker-overlay {
  z-index: 1055;
  padding: 0;

  &.searchbar-dropdown {
    z-index: 1054;
  }

  .@{ant-prefix}-popover-arrow,
  .@{ant-prefix}-tooltip-arrow {
    display: none;
  }

  .@{ant-prefix}-popover-inner,
  .@{ant-prefix}-tooltip-inner {
    display: flex;
    max-height: @picker-popover-max-height;
    background: var(--dropdown-background);

    > div {
      flex: 1;
      min-width: 0;
      min-height: 0;
    }
  }

  .@{ant-prefix}-popover-inner-content,
  .@{ant-prefix}-tooltip-content {
    // min-width: 300px;
    max-width: @picker-popover-max-width;
    height: 100%;
    max-height: @picker-popover-max-height;
    padding: 0;

    .@{ant-prefix}-menu {
      // min-width: 300px;
      height: 100%;
      min-height: 0;
      border-right: none;
      border-left: none;

      &-item {
        display: flex;
        height: unset !important;
        padding: 5px 12px;
        margin-top: 0;
        margin-bottom: 0;
        line-height: 22px;
        vertical-align: middle;
        background: var(--dropdown-background);

        &:hover {
          a {
            color: var(--neutral-dark);
          }
        }

        .@{ant-prefix}icon {
          margin: 0;
        }
      }
    }
  }

  &.grid-dropdown {
    .@{ant-prefix}-popover-inner,
    .@{ant-prefix}-tooltip-inner {
      max-height: 50vh;
    }
    .grid-dropdown.@{ant-prefix}-popover-inner-content,
    .grid-dropdown.@{ant-prefix}-tooltip-content {
      min-width: 50vw;
      max-width: 50vw;
      height: 50vh;
      max-height: 50vh;
      padding: 0;
    }
  }

  &.pre-post-filter-dropdown {
    > .@{ant-prefix}-popover-content {
      > .@{ant-prefix}-popover-inner,
      > .@{ant-prefix}-tooltip-inner {
        min-width: 50vw;
        max-width: 50vw;
        height: 50vh;
        max-height: 50vh;
        padding: 0;
        background: var(--filter-background);
        .@{ant-prefix}-popover-inner-content,
        .@{ant-prefix}-tooltip-inner-content {
          max-height: 50vh;

          @apply px-2 py-1;
        }
      }
    }
  }

  &.auto-size-dropdown {
    .auto-size-dropdown.@{ant-prefix}-popover-inner-content,
    .auto-size-dropdown.@{ant-prefix}-tooltip-content {
      min-width: 150px;
      max-width: @picker-popover-max-width;
      height: 100%;
      max-height: @picker-popover-max-height;
      padding: 0;

      .@{ant-prefix}-menu {
        min-width: unset;
      }
    }
  }

  &.has-arrow {
    .@{ant-prefix}-popover-arrow {
      display: block;
    }
    &.@{ant-prefix}-popover {
      &-placement-top,
      &-placement-topLeft,
      &-placement-topRight {
        padding-bottom: @popover-distance;
      }

      &-placement-right,
      &-placement-rightTop,
      &-placement-rightBottom {
        padding-left: @popover-distance;
      }

      &-placement-bottom,
      &-placement-bottomLeft,
      &-placement-bottomRight {
        padding-top: @popover-distance;
      }

      &-placement-left,
      &-placement-leftTop,
      &-placement-leftBottom {
        padding-right: @popover-distance;
      }
    }
  }

  .list {
    div {
      &:not(.monitor-icon) {
        @apply py-2;

        &:not(:last-child) {
          border-bottom: 1px solid var(--border-color);
        }
      }
    }

    > div {
      @apply py-2;
    }
  }

  .dropdown-list {
    display: flex;
    flex-direction: column;
    gap: 2px;
    align-items: flex-start;
    width: 160px;
    padding: 8px;

    .list-item {
      display: flex;
      gap: 10px;
      align-items: center;
      align-self: stretch;
      padding: 4px 12px;

      &.selected {
        color: var(--primary);
        background: var(--left-menu-hover-bg);
      }

      &:hover {
        display: flex;
        gap: 10px;
        align-items: center;
        align-self: stretch;
        background: var(--left-menu-hover-bg);
        border-radius: 4px;
      }
    }
  }
}

.firmware-update {
  &.ant-calendar-picker {
    position: relative;
    display: none;
  }
}

.@{ant-prefix}-drawer-left .@{ant-prefix}-drawer-content-wrapper,
.@{ant-prefix}-drawer-right .@{ant-prefix}-drawer-content-wrapper {
  height: 96%;
  margin-top: 1%;
  border-width: 5px;

  & .@{ant-prefix}-drawer-content {
    & .@{ant-prefix}-drawer-wrapper-body {
      & .@{ant-prefix}-drawer-header {
        padding: @pop-header-padd;
        margin: @pop-header-mar;

        & .@{ant-prefix}-drawer-title {
          & h5 {
            font-size: @popup-title;
            color: var(--primary);
          }
        }

        & .@{ant-prefix}-drawer-close {
          top: 0;
          right: 0;
          width: 20px;
          font-size: @pop-closse-arrow;
          color: var(--neutral-regular);
        }
      }
      & .@{ant-prefix}-drawer-body {
        .slide-toggle {
          padding-left: 0;
        }

        & .main-content-panel {
          padding-left: 0;
        }

        & .policy-pop-search-spacer {
          & .k-grid.k-grid-virtual {
            margin-top: 13px;
          }
        }
      }

      & .sub-label {
        position: absolute;
        bottom: 20px;
        color: var(--neutral-light);
      }

      & .actions {
        // height: @pop-footer-height;
        border: none;

        & span.mandatory {
          position: absolute;
          bottom: 20px;
          left: 0;
        }

        & .mand-policy-s {
          top: -13px;
        }
      }
    }
  }
}

& .@{ant-prefix}-modal-wrap {
  & .@{ant-prefix}-modal {
    & .@{ant-prefix}-modal-content {
      & .@{ant-prefix}-modal-header {
        & .@{ant-prefix}-modal-title {
          & .@{ant-prefix}-btn {
            width: auto;
            padding: 0;
            font-size: 1.3rem;
            background: none;

            &:hover {
              box-shadow: none;
            }
          }
        }
      }
    }
  }
}

.used-count-modal {
  & .@{ant-prefix}-modal-body {
    padding: @common-padd-space !important;

    & .main-content-panel {
      padding-left: 0;
      margin-top: 0;
    }
  }
  & .@{ant-prefix}-modal-content {
    & .@{ant-prefix}-modal-header {
      padding: @pop-header-padd;
      margin: @pop-header-mar;
      & .@{ant-prefix}-modal-title {
        & .flex.items-center {
          height: 22px;
          line-height: 22px;
        }
      }
    }
  }
}

.monitor-schedule-list {
  .slide-toggle {
    display: none;
  }
}
.@{ant-prefix}-drawer.@{ant-prefix}-drawer-open {
  & .@{ant-prefix}-drawer-mask {
    background: rgba(94, 102, 112, 1);
  }
}
.@{ant-prefix}-modal-mask {
  background: rgba(94, 102, 112, 0.3);
}

.@{ant-prefix}-tooltip {
  &-arrow {
    display: none;
  }

  &-inner {
    color: var(--tooltip-text-color);
    background: var(--tooltip-background-color);
    border: var(--border-color);
    box-shadow: var(--tooltip-box-shadow);
  }
}

.@{ant-prefix}-calendar-picker-container {
  z-index: 1052;

  &-content {
    border-color: var(--border-color);
  }

  .@{ant-prefix}-calendar-input {
    background: var(--page-background-color);
  }

  .@{ant-prefix}-calendar-header,
  .@{ant-prefix}-calendar-footer,
  .@{ant-prefix}-calendar-input-wrap {
    border-color: var(--border-color);
  }

  .@{ant-prefix}-calendar-disabled-cell {
    background: transparent;
    opacity: 0.5;
  }

  .@{ant-prefix}-calendar-header {
    a {
      color: var(--neutral-light);
    }
  }
}

.tooltip-arrow,
.popover-arrow {
  display: none;
}

.save-as-widget-overlay {
  z-index: 1055;
  .@{ant-prefix}-popover-content {
    border-radius: 4px;
  }
  .@{ant-prefix}-popover-inner-content {
    display: flex;
    width: 300px;
    padding: 0;
  }
  .@{ant-prefix}-popover-inner,
  .@{ant-prefix}-tooltip-inner {
    display: flex;
    max-height: @picker-popover-max-height;
    background: var(--dropdown-background);
  }
}

.dashboard-dropdown-overlay {
  &.@{ant-prefix}-popover {
    z-index: 91;
  }
  .@{ant-prefix}-popover-content {
    border-radius: 4px;
  }
  .@{ant-prefix}-popover-inner-content {
    display: flex;
    width: 30vw;
    height: 50vh;
    padding: 0;
    background: var(--page-background-color);
  }

  .@{ant-prefix}-popover-arrow {
    border-top-color: var(--code-tag-background-color) !important;
    border-left-color: var(--code-tag-background-color) !important;
  }

  .dropdown-header {
    background: var(--page-background-color);
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;

    @apply p-4 flex justify-between items-center;
  }

  .dashboard-collapse-menu {
    border-color: var(--border-color);
    .@{ant-prefix}-collapse-item {
      color: var(--page-text-color);
      background: var(--page-background-color);
      border-color: var(--border-color);

      &-active {
        // background: var(--code-tag-background-color);
      }
    }
    .@{ant-prefix}-collapse-content {
      border-color: transparent;

      &-active {
        // background: var(--code-tag-background-color);
      }

      &-box {
        padding: 5px 0;
      }
    }
    .@{ant-prefix}-collapse-header {
      position: relative;
      padding: 10px 6px;
      color: var(--page-text-color);

      .@{ant-prefix}icon {
        position: absolute;
        right: unset;
        left: 10px;
      }
    }

    &.left-icon {
      .@{ant-prefix}-collapse-header {
        .@{ant-prefix}icon {
          position: absolute;
          right: unset;
          left: 10px;
        }
      }
    }
  }
}

.@{ant-prefix}-collapse {
  .@{ant-prefix}-collapse-item {
    border-color: var(--border-color);

    .@{ant-prefix}-collapse-header {
      color: var(--page-text-color);

      .arrow.@{ant-prefix}icon.@{ant-prefix}icon-right {
        color: var(--page-text-color);
      }
    }
  }

  &.role-form-collapse {
    .@{ant-prefix}-collapse-content-box {
      padding: 0;
    }

    .@{ant-prefix}-collapse-item-disabled {
      .@{ant-prefix}-collapse-arrow {
        display: none;
      }
    }
  }
}

.log-search-bar-overlay {
  .@{ant-prefix}-popover-content {
    position: relative;
    top: -21px;
    border-radius: 4px;

    .@{ant-prefix}-popover-inner {
      border: 1px solid var(--border-color);
      border-top-width: 0;
      border-top-left-radius: 0;
      border-top-right-radius: 0;
      box-shadow: 0 5px 6px rgba(0, 0, 0, 0.15);

      &-content {
        transition: background 0.5s ease;
      }
    }
  }

  .@{ant-prefix}-popover-arrow {
    display: none;
  }

  &.has-error {
    .@{ant-prefix}-popover-content {
      .@{ant-prefix}-popover-inner {
        border-color: var(--secondary-red);

        &-content {
          background: var(--severity-critical-lighter);
        }
      }
    }
  }
}

.@{ant-prefix}-popover {
  &.color-picker-popover {
    z-index: 1051;
  }
}

.popover-arrow {
  &.block.always-visible {
    display: block;
  }

  &::before {
    position: absolute;
    top: -4px;
    left: 0;
    z-index: -1;
    width: 10px;
    height: 10px;
    content: '';
    background: var(--tooltip-background-color);
    transition: transform 0.2s ease-out 0s, visibility 0.2s ease-out 0s;
    transform: rotate(45deg);
    transform-origin: center center;
  }
}

.heatmap-tooltip {
  .popover-arrow {
    background: var(--chart-tooltip-background);
  }
  .@{ant-prefix}-popover-inner {
    background: var(--chart-tooltip-background);
  }
}

[data-popper-placement^='top'] > [data-popper-arrow] {
  bottom: 2px;
  left: -4px !important;
}

[data-popper-placement^='left'] > [data-popper-arrow] {
  right: 5px;
}

[data-popper-placement^='right'] > [data-popper-arrow] {
  left: -4px;
}

[data-popper-placement^='bottom'] > [data-popper-arrow] {
  top: 0;
  left: -4px !important;
}

.tippy-box[data-theme~='tippy-tooltip'] {
  font-size: 12px;
  color: var(--topology-graph-tooltip-text-color);
  background-color: var(--topology-graph-tooltip-bg);
  border: var(--border-color);
  backdrop-filter: blur(8px);

  @apply shadow-md;

  h6 {
    color: inherit;
  }

  .border-left {
    border-color: rgb(60, 60, 60);
  }

  .tippy-content {
    border: 1px solid #3c3c3c;
    border-radius: 4px;
  }
}

.tippy-box[data-theme~='tippy-tooltip'][data-placement^='top']
  > .tippy-arrow::before {
  border-top-color: #3c3c3c;
}

.tippy-box[data-theme~='tippy-tooltip'][data-placement^='bottom']
  > .tippy-arrow::before {
  border-bottom-color: #3c3c3c;
}

.tippy-box[data-theme~='tippy-tooltip'][data-placement^='left']
  > .tippy-arrow::before {
  border-left-color: #3c3c3c;
}

.tippy-box[data-theme~='tippy-tooltip'][data-placement^='right']
  > .tippy-arrow::before {
  border-right-color: #3c3c3c;
}

.color-picker-popover {
  .@{ant-prefix}-popover-arrow {
    display: none;
  }
  .@{ant-prefix}-popover-inner-content {
    background: var(--dropdown-background);
  }
}
