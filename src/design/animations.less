// animations for router
.opacity-enter-active,
.opacity-leave-active {
  overflow: hidden;
  transition-timing-function: ease;
  transition-duration: 0.3s;
  transition-property: height, opacity;
}

.opacity-enter,
.opacity-leave-active {
  opacity: 0;
}

.placeholder-enter-active,
.placeholder-leave-active {
  overflow: hidden;
  transition-timing-function: ease;
  transition-duration: 0.1s;
  transition-property: transform, opacity;
}

.placeholder-enter,
.placeholder-leave-active {
  opacity: 0;
  transform: translateY(5px);
}

.slideLeft-enter-active,
.slideLeft-leave-active,
.slideRight-enter-active,
.slideRight-leave-active,
.slideBottom-leave-active,
.slideBottom-enter-active,
.slideTop-leave-active,
.slideTop-enter-active {
  overflow: hidden;
  transition-timing-function: cubic-bezier(0.55, 0, 0.1, 1);
  transition-duration: 0.1s;
  transition-property: height, opacity, transform;
}

.slideLeft-enter,
.slideRight-leave-active {
  opacity: 0;
  transform: translate(10px, 0);
}

.slideTop-enter,
.slideTop-leave-active {
  opacity: 0;
  transform: translate(0, -10px);
}

.slideBottom-enter,
.slideBottom-leave-active {
  opacity: 0;
  transform: translate(0, 10px);
}

.slideLeft-leave-active,
.slideRight-enter {
  opacity: 0;
  transform: translate(-10px, 0);
}

.disable-animation {
  transition: none !important;
}

.transition-width {
  will-change: width;
  transition: width 0.2s linear;
}

.settings-menu-enter-active {
  overflow: hidden;
  transition-timing-function: ease;
  transition-duration: 0.1s;
  transition-property: transform, opacity;
}

.settings-menu-enter {
  opacity: 0;
  transform: translateY(5px);
}

.settings-sub-route-enter-active {
  overflow: hidden;
  transition-timing-function: cubic-bezier(0.55, 0, 0.1, 1);
  transition-duration: 0.3s;
  transition-property: height, opacity, transform;
}

.settings-sub-route-enter {
  opacity: 0;
  transform: translate(10px, 0);
}

.settings-sub-route-leave-active {
  overflow: hidden;
  opacity: 0;
  transition-timing-function: cubic-bezier(0.55, 0, 0.1, 1);
  transition-duration: 0.1s;
  // transition-property: height, opacity, transform;
  transition-property: height, transform;
  transform: translate(0, 10px);
}

body.disable-animation * {
  transition: none !important;
  animation-duration: 0s !important;
}
