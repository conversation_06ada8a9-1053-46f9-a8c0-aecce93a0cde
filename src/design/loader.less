@keyframes ldio-wsao5e0vki {
  0% {
    transform: translate(6px, 40px) scale(0);
  }

  25% {
    transform: translate(6px, 40px) scale(0);
  }

  50% {
    transform: translate(6px, 40px) scale(1);
  }

  75% {
    transform: translate(40px, 40px) scale(1);
  }

  100% {
    transform: translate(74px, 40px) scale(1);
  }
}

@keyframes ldio-wsao5e0vki-r {
  0% {
    transform: translate(74px, 40px) scale(1);
  }

  100% {
    transform: translate(74px, 40px) scale(0);
  }
}

@keyframes ldio-wsao5e0vki-c {
  0% {
    background: #135487;
  }

  25% {
    background: #59bc6e;
  }

  50% {
    background: #28a8d3;
  }

  75% {
    background: #1783c5;
  }

  100% {
    background: #135487;
  }
}

.motadata-loader {
  display: inline-block;
  width: 100px;
  height: 70px;
  overflow: hidden;

  .motadata-loader-container {
    position: relative;
    width: 100%;
    height: 100%;
    transform: translateZ(0) scale(1);
    transform-origin: 0 0; /* see note above */
    backface-visibility: hidden;

    div {
      position: absolute;
      width: 20px;
      height: 20px;
      background: #135487;
      border-radius: 50%;
      transform: translate(40px, 40px) scale(1);
      animation: ldio-wsao5e0vki 1.25s infinite cubic-bezier(0, 0.5, 0.5, 1);

      &:nth-child(1) {
        background: #1783c5;
        transform: translate(74px, 40px) scale(1);
        animation: ldio-wsao5e0vki-r 0.3125s infinite
            cubic-bezier(0, 0.5, 0.5, 1),
          ldio-wsao5e0vki-c 1.25s infinite step-start;
      }

      &:nth-child(2) {
        background: #135487;
        animation-delay: -0.3125s;
      }

      &:nth-child(3) {
        background: #1783c5;
        animation-delay: -0.625s;
      }

      &:nth-child(4) {
        background: #28a8d3;
        animation-delay: -0.9375s;
      }

      &:nth-child(5) {
        background: #59bc6e;
        animation-delay: -1.25s;
      }
    }
  }
}

.dot-loader {
  .dot {
    align-self: flex-end;
    width: 2px;
    height: 2px;
    margin: 1px;
    background: var(--page-text-color);
    border: double;
    border-color: var(--page-text-color);
    border-radius: 50%;
    animation: jump 1.6s infinite;

    &:nth-child(1) {
      animation-delay: 0.1s;
    }

    &:nth-child(2) {
      animation-delay: 0.4s;
    }

    &:nth-child(3) {
      animation-delay: 0.5s;
    }
  }
}

@keyframes jump {
  0%,
  80%,
  100% {
    transform: scale(0);
  }

  40% {
    transform: scale(0.6);
  }
}
