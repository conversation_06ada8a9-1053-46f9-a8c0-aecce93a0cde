/* stylelint-disable */
// @neutral-darkest: #000000;
// @neutral-darker: #14212d;

// @neutral-dark: #364658;
// @neutral-regular: #7b8fa5;
// @neutral-light: #a5bad0;
// @secondary-green: #89c540;
// @secondary-yellow: #f5bc18;
// @secondary-orange: #f58518;
// @secondary-red: #f04e3e;
// @secondary-red-dark: #c84235;
// @agent-stopped-color: #8d3abc;
// @primary: #099dd9;
// @primary-alt: #3279be;
// @white-regular: #ffffff;
// @neutral-lighter: #dee5ed;
// @neutral-lightest: #eef2f6;
// @list-menu-hover: #e7f5fb;
// @table-group-header-highlight: #d6edf8;

// @mesasge-success: #e7f4d9;
// @mesasge-error: #fcdbd8;

// @outline-color: @primary;
// @agent-down-color: @neutral-regular;
// @agent-critical-color: @secondary-red-dark;
// @dropmenu-bordercolor: @neutral-lighter;
// /* stylelint-enable */

// //Dark Theme Color
// @black-neutral-dark: #1f2120;
// @black-neutral-darked: #404243;
// @black-neutral-regular: #353736;
// @black-neutral-light: #363837;
// @black-neutral-lighter: #808080;
// @black-yellow-regular: @secondary-yellow;

@primry-color: var(--primary);

// ant specific variables override
@font-family: 'Poppins', sans-serif;

@input-height-base: 32px;

@padding-lg: 24px; // containers

@padding-md: 16px; // small containers and buttons

@padding-sm: 12px; // Form controls and items

@padding-xs: 8px; // small items
@control-padding-horizontal: @padding-sm;
@control-padding-horizontal-sm: @padding-xs;

@input-padding-horizontal: @control-padding-horizontal - 1px;
@input-padding-horizontal-base: @input-padding-horizontal;

@input-padding-vertical-base: 4px;

// Header Css
@menu-highlight-color: @info-color;
@menu-item-active-bg: @white;
@menu-item-active-border-width: 3px;
@menu-collapsed-width: 70px;
@layout-header-padding: 0 12px;
@layout-header-height: 55px;
@menu-item-active-border-width: 0;

// Nav Bar Setting
@navbar-height: 50px;
@nav-icon-width: @navbar-height;
@nav-icon-height: 40px;
@nav-icon-line-height: 40px;
@nav-icon-size: 1.1rem;
@nav-menu-height: 30px;

// Header Bar
@header-height: 55px;
@user-icon-wh: 35px;
@user-drop-icon-wh: 70px;
@header-drop-width: 320px;

// Content Part
@text-lg: 1.5rem;
@text-regular: 1rem;
@text-sm: 0.8rem;
@text-xs: 0.6rem;
@font-size-base: @text-sm;
@font-size-lg: @text-regular;
@font-size-sm: @text-sm;

// Button Setting
@btn-radius: 4px;
@btn-raidus-full: 150px;
@btn-height: 2.1rem;

// Search Bar
@search-height: 36px;
@search-width: 18rem;

@body-background: var(--page-background-color);
// // Base background color for most components
@component-background: var(--page-background-color);

@text-color: var(--page-text-color);
@heading-color: var(--page-text-color);

//Table Grid
@user-count-height: 23px;

//Tag Style
@tag-pad: 0 0.6rem 0 0.6rem;
@tag-mar: 0 3px 3px 0;
@tag-height: 24px;
@close-tag-padd: 0 0.9rem 0 1.5rem 0 0.9rem 0 0.9rem;

//Drop Menu
@droplist-padd: 0.3rem 1.3rem 0.3rem 0.9rem;

// custom variables
@picker-popover-max-height: 35vh;
@picker-popover-max-width: 80vw;

//Pop Up
@overlay-border-color: var(--primary);
@overlay-border-width: 4px;
@popup-title: 1.1rem;
@pop-header-padd: 15px 0;
@pop-header-mar: 0 15px;
@pop-closse-arrow: 1.1rem;
@pop-footer-height: 75px;
@font-size-10: 10px;
@common-padd-space: 15px;

//Chat Boat
@chat-width: 50vw;
@chat-height: 400px;
@chat-title: 0.8rem;
@chat-title-arrow: 0.7rem;

// progress
@progress-default-color: var(--primary);

// tabs
@tabs-highlight-color: var(--primary);
@tabs-hover-color: var(--primary);
@tabs-active-color: var(--primary);
@tabs-ink-bar-color: var(--primary);
@border-color-split: var(--border-color);
@border-color-base: var(--border-color);
@tabs-card-head-background: var(--nav-panel-bg);
@tabs-card-height: 32px;

// tags
@tag-default-bg: @neutral-lighter;

// flow colors
@flow-source-color: #114e80;
@flow-destination-color: #1a80bb;
@flow-other-color: #31b9d9;
@flow-metric-color: #59bb6e;
@flow-aggregator-color: #7b8fa5;

// popover
@popover-bg: var(--dropdown-background);
@popover-arrow-width: 6px;
@popover-distance: @popover-arrow-width + 4px;

// collapse
@collapse-header-bg: var(--page-background-color);
@collapse-content-padding: 10px 6px;
@collapse-content-bg: var(--code-tag-background-color);

@input-placeholder-color: var(--input-placeholder-color);
