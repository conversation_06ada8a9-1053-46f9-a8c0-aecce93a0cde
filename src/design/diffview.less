/*
This is part of jsdifflib v1.0. <http://github.com/cemerick/jsdifflib>

Copyright 2007 - 2011 Cha<PERSON> <PERSON> <<EMAIL>>. All rights reserved.

Redistribution and use in source and binary forms, with or without modification, are
permitted provided that the following conditions are met:

   1. Redistributions of source code must retain the above copyright notice, this list of
      conditions and the following disclaimer.

   2. Redistributions in binary form must reproduce the above copyright notice, this list
      of conditions and the following disclaimer in the documentation and/or other materials
      provided with the distribution.

THIS SOFTWARE IS PROVIDED BY Chas <PERSON>erick ``AS IS'' AND ANY EXPRESS OR IMPLIED
WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF ME<PERSON>HANTABILITY AND
FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL Chas Emerick OR
CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF
ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

The views and conclusions contained in the software and documentation are those of the
authors and should not be interpreted as representing official policies, either expressed
or implied, of Chas Emerick.
*/

.diff-view-modal {
  .ant-modal-body {
    height: 85vh;
  }
}

// div.diff-view {
//   width: 100%;
//   white-space: pre-wrap;
//   border-collapse: collapse;
//   border: 1px solid var(--border-color);
// }

// div.diff-view div span {
//   padding: 0.3em 0.5em 0.1em 2em;
//   font-size: 11px;
//   font-weight: normal;
//   // color:#886;
//   text-align: right;
//   vertical-align: top;
//   // background: var(--code-tag-background-color);
//   border: 1px solid var(--border-color);

//   .equal {
//     padding: 0 0.4em;
//     padding-top: 0.4em;
//     vertical-align: top;
//     background: var(--code-tag-background-color);
//   }

//   .replace {
//     padding: 0 0.4em;
//     padding-top: 0.4em;
//     vertical-align: top;
//     background: var(--code-tag-background-color);
//   }

//   .delete {
//     padding: 0 0.4em;
//     padding-top: 0.4em;
//     vertical-align: top;
//     background: var(--code-tag-background-color);
//   }

//   .insert {
//     padding: 0 0.4em;
//     padding-top: 0.4em;
//     vertical-align: top;
//     background: var(--code-tag-background-color);
//   }

//   .empty {
//     padding: 0 0.4em;
//     padding-top: 0.4em;
//     vertical-align: top;
//     background: var(--code-tag-background-color);
//   }
// }

// div.diff-view div {
//   // position: sticky;
//   // top: -1px;
//   // display: ;
//   background: var(--code-tag-background-color);
//   border-bottom: 1px solid var(--border-color);
// }

// div.diff-view div span.texttitle {
//   text-align: left;
// }

// // div.diff-view div span {

// // }

// div.diff-view .empty {
//   background-color: var(--code-tag-background-color);
// }

// div.diff-view .replace {
//   background-color: var(--severity-warning-lighter);
// }

// div.diff-view .delete {
//   background-color: var(--severity-down-lighter);
// }

// div.diff-view .skip {
//   background-color: var(--code-tag-background-color);
//   border: 1px solid var(--border-color);
//   border-right: 1px solid var(--border-color);
// }

// div.diff-view .insert {
//   background-color: var(--severity-clear-lighter);
// }

// div.diff-view span.author {
//   display: none;
//   text-align: right;
//   background: #efefef;
//   border-top: 1px solid #bbc;
// }

div.diff-view {
  width: 100%;
  white-space: pre-wrap;
  border-collapse: collapse;

  div {
    // display: flex;
    width: 100%;

    // position: sticky;
    // top: -1px;
    // display: ;
    // border-bottom: 1px solid var(--border-color);
    // &:first-child {
    //   display: none;
    // }

    &:first-child div {
      display: none;
    }

    div {
      display: flex;
      width: 100%;

      &:last-child {
        border-bottom: 1px solid var(--border-color);

        span {
          &:nth-child(3) {
            &::before {
              height: 100%;
            }

            // Draws a line before the third span
          }
        }
      }

      span {
        &:first-child {
          // border-bottom: 0;
          flex-shrink: 0;
          width: 5%;
          background: var(--page-background-color);
          border: 1px solid var(--border-color);
        }

        &:nth-child(2) {
          flex-shrink: 0;
          width: 44%;
          margin-right: 20px;
        }

        &:nth-child(3) {
          position: relative;
          // border-bottom: 0;
          flex-shrink: 0;
          width: 5%;
          margin-left: 20px;
          background: var(--page-background-color);
          border: 1px solid var(--border-color);

          &::before {
            position: absolute;
            top: 0;
            bottom: 0;
            left: -24px;
            width: 1px;
            height: 110%;
            content: ' ';
            border-left: 1px solid var(--border-color);
          }

          // Draws a line before the third span
        }

        &:nth-child(4) {
          flex-shrink: 0;
          width: 43%;
        }

        padding: 0.3em 0.5em 0.1em 2em;
        font-size: 11px;
        font-weight: normal;
        // color:#886;
        text-align: left;
        vertical-align: top;
        // background: @code-tag-background-color;

        .equal,
        .replace,
        .delete,
        .insert,
        .empty {
          padding: 0 0.4em;
          padding-top: 0.4em;
          vertical-align: top;
          background: var(--code-tag-background-color);
        }
      }

      span.texttitle {
        text-align: left;
      }

      .equal {
        background-color: var(--code-tag-background-color);
      }

      .empty {
        background-color: var(--code-tag-background-color);
      }

      .replace {
        background-color: var(--severity-warning-lighter);
      }

      .delete {
        background-color: var(--severity-down-lighter);
      }

      .skip {
        background-color: var(--code-tag-background-color);
        border: 1px solid var(--border-color);
        border-right: 1px solid var(--border-color);
      }

      .insert {
        background-color: var(--severity-clear-lighter);
      }

      span.author {
        display: none;
        text-align: right;
        background: #efefef;
        border-top: 1px solid #bbc;
      }
    }
  }
}
