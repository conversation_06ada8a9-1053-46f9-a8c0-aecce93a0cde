// stylelint-disable
h1 {
  @apply text-3xl;
}

h2 {
  @apply text-2xl;
}

h3 {
  @apply text-xl;
}

h4 {
  @apply text-lg;
}

h5 {
  @apply text-base;
}

h6 {
  @apply text-sm;
}

small {
  @apply text-xs !important;
}

.text-xxs {
  font-size: @text-xs;
}

.font-600 {
  font-weight: 600;
}

a.delete-button:hover {
  color: var(--secondary-red-dark);
}

a:focus {
  text-decoration: none;
}

.textEllipsis() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis {
  .textEllipsis();
}

p {
  word-break: break-word;
}

.font-500 {
  font-weight: 500;
}

.text-neutral-light {
  color: var(--text-neutral-ligher);
}
