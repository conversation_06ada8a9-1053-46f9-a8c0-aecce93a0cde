.nav-bar-panel {
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 99;
  // padding-top: @header-height - 1px;
  background: var(--nav-panel-bg);
  box-shadow: 3px 10px 10px rgba(0, 0, 0, 0.1);

  &.add-z-index {
    z-index: 1053;
  }

  .@{ant-prefix}-layout-sider-children {
    & ul.mainMenu {
      z-index: 1;
      width: 100%;
      background: none;
      & li.@{ant-prefix}-menu-item {
        float: left;
        width: 100%;
        height: auto;
        padding: 0 !important;
        margin: 0;
        line-height: normal;
        text-align: left;

        &:first-child {
          display: flex;
          align-items: center;
          justify-self: center;
          height: calc(@header-height - 2px);
        }

        &:not(:first-child) {
          margin-bottom: 8px;
        }

        & a {
          display: inline-block;
          float: none;
          width: 100%;
          height: @nav-icon-height;
          padding-left: 17px;
          line-height: @nav-icon-line-height;
          color: var(--nav-text-color);

          &:hover {
            color: var(--nav-selected-text-color);
            background: var(--primary-alt);
          }

          & i {
            font-size: @nav-icon-size;
          }
        }

        &.expand-collapse-trigger:hover {
          background: none !important;

          & a {
            background: none !important;
          }

          &::after {
            background: none;
          }
        }

        &::after {
          left: 0;
          width: 0;
          background: var(--nav-divider-bg);
        }
      }
      & li.@{ant-prefix}-menu-item-selected {
        & a {
          color: var(--nav-selected-text-color);
          background: var(--primary-alt);

          &:hover {
            color: var(--nav-selected-text-color);
            background: var(--primary-alt);
          }
        }

        &::after {
          width: 0;
          background: var(--nav-divider-bg);
        }
      }

      & li.header-logo {
        height: @header-height;

        & a {
          background: none;

          &:hover {
            color: var(--primary-alt);
            background: none;
          }

          & i {
            font-size: 1.1rem;
          }
        }
      }
    }

    .ai-ops-text {
      width: 50px;
      margin-bottom: 21px;
      font-size: 1rem;
      color: var(--page-text-color);
      text-align: center;
      transform: rotate(-90deg); /* Standard syntax */
    }

    .mask-group {
      position: absolute;
      right: -75px;
      bottom: -100px;
      width: 200px;
      height: 200px;
    }
  }
}
.nav-bar-panel.@{ant-prefix}-layout-sider-collapsed {
  .@{ant-prefix}-layout-sider-children {
    & ul.mainMenu {
      & li.@{ant-prefix}-menu-item {
        text-align: center;

        & a {
          // width: @nav-icon-width;
          height: @nav-icon-height;
          padding-left: initial;
          line-height: @nav-icon-line-height;
          border-radius: 0;
        }
      }
    }
  }
}

.selection-menu {
  & li {
    margin: 0 !important;
    border-bottom: solid 1px var(--border-color);

    &:hover {
      background: var(--list-menu-hover);
    }
  }
}

.mainNavbar {
  overflow: hidden;
  // background: var(--neutral-lightest);
  .@{ant-prefix}-layout-sider-children {
    @apply flex-col flex;
  }
  .@{ant-prefix}-menu-item,
  .@{ant-prefix}-menu-item * {
    transition: none !important;
  }
  .@{ant-prefix}-layout-sider-trigger {
    display: none;
  }
  &.@{ant-prefix}-layout-sider-has-trigger {
    padding-bottom: 0;
  }
}

.mainSubMenu {
  .@{ant-prefix}-menu-sub {
    // background: var(--neutral-lightest);
  }
}

.mainMenu {
  flex: 1;
  border-right-width: 0;

  .exclude-hover {
    display: flex;
    align-items: center;
    justify-content: center;

    span {
      display: none;
    }
  }
  .@{ant-prefix}-menu-item:not(.exclude-hover):hover,
  .@{ant-prefix}-menu-item-selected {
    // color: var(--primary);
    background-color: var(--primary-alt) !important;
    // stylelint-disable-next-line
    &::after {
      left: 0;
      width: 5px;
      background-color: var(--primary);
      opacity: 1;
      transform: none !important;
    }
  }
}
