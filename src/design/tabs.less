.@{ant-prefix}-tabs {
  &.no-border {
    .@{ant-prefix}-tabs-bar {
      border-color: transparent;
    }
  }

  .@{ant-prefix}-tabs-tab-active {
    border-color: var(--primary) !important;
  }

  &-tab-prev,
  &-tab-next {
    color: var(--page-text-color);
  }

  &-tab-btn-disabled {
    color: var(--neutral-light);
  }
}

.@{ant-prefix}-tabs-top {
  &.flex-tabs {
    .@{ant-prefix}-tabs-content {
      @apply flex flex-col min-h-0;
    }
  }
  .@{ant-prefix}-tabs-tab-active {
    color: var(--primary);
    background: none;
  }
  .@{ant-prefix}-tabs-bar {
    margin-bottom: 0;
  }
}

.used-count-modal {
  .@{ant-prefix}-tabs {
    & .@{ant-prefix}-tabs-bar {
      margin-top: -12px;
    }
  }
}

.@{ant-prefix}-tabs-tab {
  color: var(--tabs-text-color);
}

.sticky-tab {
  position: sticky;
  top: 0;
  z-index: 2;
  background-color: var(--page-background-color);
}
.@{ant-prefix}-tabs-bar {
  border-color: var(--border-color);
}

.grey-tab {
  .@{ant-prefix}-tabs-bar {
    background: var(--neutral-lightest);
  }
}

.topology-hierarchy-tab {
  .@{ant-prefix}-tabs-nav .@{ant-prefix}-tabs-tab .@{ant-prefix}icon {
    margin: 0;
  }
}

.metric-picker-tabs.@{ant-prefix}-tabs {
  .@{ant-prefix}-tabs-tab {
    padding-top: 8px;
    padding-bottom: 8px;
    margin-right: 10px;
  }
}
