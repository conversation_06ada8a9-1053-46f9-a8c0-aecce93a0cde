.@{ant-prefix}-tag {
  font-size: 0.7rem;
  font-weight: 500;
  line-height: 22px;
  // text-transform: uppercase;
  background: var(--default-tag-color) !important;
  border-color: transparent !important;

  &.neutral-lighter {
    color: var(--page-text-color);
    background: var(--neutral-lighter) !important;
    border-color: transparent !important;
  }

  &.default {
    color: var(--primary);
    background: var(--tag-bg) !important;
    border: none;
    // border-color: var(--tag-border) !important;
  }

  &.used-count-pill {
    color: var(--primary);
    background: var(--tag-bg) !important;
    border: none;
    // border-color: var(--tag-border) !important;
  }

  &.new {
    color: var(--secondary-green);
    background-color: rgba(137, 197, 64, 0.2) !important;
    // border-color: rgba(137, 197, 64, 0.5) !important;
  }

  &.provision {
    color: var(--primary);
    background-color: rgba(9, 157, 217, 0.2) !important;
    // border-color: rgba(9, 157, 217, 0.5) !important;
  }

  &.unprovision {
    color: var(--secondary-orange);
    background-color: rgba(245, 133, 24, 0.2) !important;
    // border-color: rgba(245, 133, 24, 0.5) !important;
  }
}

.tag-green {
  color: var(--secondary-green);
  background: rgba(137, 197, 64, 0.2) !important;
  // border-color: rgba(137, 197, 64, 0.5) !important;
}

.tag-red {
  color: var(--secondary-red);
  background: rgba(240, 78, 62, 0.2) !important;
  // border-color: rgba(240, 78, 62, 0.5) !important;
}

.tag-yellow {
  color: var(--secondary-yellow);
  background-color: rgba(245, 188, 24, 0.2) !important;
  // border-color: rgba(245, 188, 24, 0.5) !important;
}

.tag-primary {
  color: var(--primary);
  background-color: rgba(9, 157, 217, 0.2) !important;
  // border-color: rgba(9, 157, 217, 0.5) !important;
}

.tag-orange {
  color: var(--secondary-orange);
  background-color: rgba(245, 133, 24, 0.2) !important;
  // border-color: rgba(245, 133, 24, 0.5) !important;
}

.tag-unknown {
  color: var(--severity-unknown);
  background-color: var(--severity-unknown-lighter) !important;
  // border-color: var(--severity-unknown-darker) !important;
}

.tag-purple {
  color: var(--severity-unreachable);
  background-color: var(--severity-unreachable-lightest) !important;
  // border-color: var( --severity-stop-darker) !important;
}

.compliance-tag-padding {
  padding-right: 0.69rem;
  padding-left: 0.69rem;
}
