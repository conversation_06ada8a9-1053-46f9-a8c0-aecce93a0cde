<script>
export default {
  name: 'MPersistedTab',
  model: { event: 'change' },
  props: {
    moduleKey: {
      type: String,
      required: true,
    },
    useLocalStorageTab: {
      type: Boolean,
      // eslint-disable-next-line
      default: true,
    },
    defaultValue: {
      type: String,
      default: undefined,
    },
    value: {
      type: String,
      default: undefined,
    },
  },
  data() {
    const currentTab = this.useLocalStorageTab
      ? this.getTabFromLocalStorage()
      : this.value || this.defaultValue
    if (currentTab) {
      this.$emit('change', currentTab)
    }
    return {
      currentTab,
    }
  },
  methods: {
    getTabFromLocalStorage() {
      const localStorageTab = localStorage.getItem(`${this.moduleKey}-tab`)
      if (localStorageTab) {
        this.$emit('change', localStorageTab)
      }
      return localStorageTab || this.value || this.defaultValue
    },
    setTab(tabName) {
      this.currentTab = tabName
      this.$emit('change', tabName)
      localStorage.setItem(`${this.moduleKey}-tab`, tabName)
    },
  },
  render() {
    return this.$scopedSlots.default({
      tab: this.currentTab,
      setTab: this.setTab,
    })
  },
}
</script>
