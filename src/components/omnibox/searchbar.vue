<template>
  <div class="flex items-center" style="height: 50px">
    <div class="flex-1 flex h-full items-center min-w-0">
      <div
        class="bg-neutral-lightest search-icon flex items-center justify-center h-full"
      >
        <MIcon name="search" class="text-neutral-light mx-2" />
        <FlotoDropdownPicker
          v-model="category"
          :options="categoryOptions"
          overlay-class-name="picker-overlay searchbar-dropdown"
          class="search-box-dropdown"
          :searchable="false"
        />
      </div>
      <div class="flex flex-1 min-w-0 mr-2">
        <codemirror
          ref="editorRef"
          class="omnibox-searchbar"
          :options="options"
          :events="events"
          @ready="onEditorReady"
          @cursorActivity="onChange"
        />
      </div>
      <MTooltip v-if="queryInfo.query.length > 0">
        <template v-slot:trigger>
          <a
            class="mx-1"
            :class="{
              'cursor-pointer': !parseError,
              'cursor-auto': parseError,
            }"
            v-on="parseError ? {} : { click: execute }"
          >
            <MIcon
              :name="parseError ? 'exclamation-circle' : 'enter'"
              size="lg"
              :class="{
                'text-secondary-red': parseError,
                'text-neutral-light': !parseError,
              }"
            />
          </a>
        </template>
        {{ parseError ? parseError.message : 'Execute' }}
      </MTooltip>
      <MButton
        v-if="!error && queryInfo.query.length > 0"
        variant="transparent"
        shape="circle"
        title="Clear"
        @click.prevent.stop="resetSearch"
      >
        <MIcon name="backspace" class="text-neutral-light" />
      </MButton>
    </div>
    <div
      class="px-2 py-2 radius-corner h-full items-center justify-center text-right inline-flex font-small bg-neutral-lightest"
    >
      <TimeRangePicker
        v-model="timerange"
        :allow-clear="false"
        overlay-class-name="timerange-dropdown-overlay"
        hide-selected-time
      />
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import CodeMirror from 'codemirror'
import { codemirror } from 'vue-codemirror'
import 'codemirror/addon/hint/show-hint'
import 'codemirror/addon/hint/show-hint.css'
import Bus from '@utils/emitter'
import TimeRangePicker from '@components/widgets/time-range-picker.vue'
import {
  MONITOR_NAME_SUGGESTION,
  IP_SUGGESTION,
  INSTANCE_SUGGESTION,
  SOURCE_TYPE_SUGGESTION,
} from './search-context/search-constants'
import { parseQuery } from './helper'
import MonitorSuggestionHint from './monitor-suggestion-hint.vue'

CodeMirror.defineOption('keyword', {}, function (cm, val, prev) {
  if (!Array.isArray(cm.options.keyword)) {
    return
  }
  if (prev === CodeMirror.Init) {
    prev = false
  }
  if (prev && !val) {
    cm.removeOverlay('keyword')
  } else if (!prev && val) {
    cm.addOverlay({
      token: function (stream) {
        for (var matcher of cm.options.keyword || []) {
          if (matcher.regex) {
            if (stream.match(matcher.regex, true)) {
              if (matcher.backup) {
                stream.backUp(matcher.backup)
              }
              return matcher.class
            }
          }
          if (matcher.text) {
            if (stream.match(matcher.text, true)) {
              if (matcher.backup) {
                stream.backUp(matcher.backup)
              }
              return matcher.class
            }
          }
        }
        stream.next()
      },
      name: 'keyword',
    })
  }
})

export default {
  name: 'Searchbar',
  components: {
    codemirror,
    TimeRangePicker,
  },
  inject: {
    groupContext: { default: { options: new Map() } },
    monitorTypeContext: { default: { options: new Map() } },
    omniboxContext: { default: { logSources: [], flowSources: [] } },
  },
  props: {
    timeline: {
      type: Object,
      default: undefined,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    this.__isSuggestionsOpen = false
    this.events = ['cursorActivity', 'keyHandled']
    this.categoryOptions = [
      {
        text: 'Metric',
        key: 'metric',
      },
      {
        text: 'Log',
        key: 'log',
      },
      {
        text: 'Flow',
        key: 'flow',
      },
    ]
    return {
      defaultValue: ``,
      category: 'metric',
      queryInfo: {
        query: '',
        caretPosition: 0,
      },
      searchContext: undefined,
      options: {
        autofocus: true,
        dragDrop: false,
        lineWrapping: false,
        placeholder: 'Start Typing...',
        hintOptions: {
          completeSingle: false,
        },
        mode: null,
        keyword: [
          {
            regex: /(?<!\.)(monitor|ip|group|tag|sourcetype)(?=\=)/,
            class: 'search-keyword',
          },
          {
            regex: /(top|last)\s/,
            class: 'search-keyword',
            backup: 1,
          },
          {
            text: 'where',
            class: 'filter-keyword',
          },
          {
            text: 'result by',
            class: 'filter-keyword',
          },
        ],
      },
    }
  },
  computed: {
    timerange: {
      get() {
        return this.timeline
      },
      set(timeline) {
        this.$emit('update:timeline', timeline)
      },
    },
    error() {
      return this.parseError
    },
    parseError() {
      if (this.searchContext) {
        const _error = this.searchContext.getError()
        if (_error && _error.parseError) {
          return _error
        }
        return undefined
      }

      return undefined
    },
  },
  watch: {
    category(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.queryInfo = {
          query: '',
          caretPosition: 0,
        }
        this.resetSearch()
      }
    },
    timeline(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.execute()
      }
    },
    queryInfo(newValue) {
      const result = parseQuery(
        newValue.query,
        newValue.caretPosition,
        {
          groups: this.groupContext.options,
          logSources: this.omniboxContext.logSources,
          flowSources: this.omniboxContext.flowSources,
          monitorTypes: this.monitorTypeContext.options.values(),
          tags: this.omniboxContext.tags,
        },
        this.category
      )
      this.searchContext = result
      if (newValue.query.length === 0) {
        this.$emit('search-context', undefined)
      }
      this.showSuggestions()
    },
  },
  created() {
    const cleanSuggestion = () => {
      if (this.searchContext) {
        this.searchContext.setSuggestionType(null)
      }
      if (this.$refs.editorRef) {
        this.$refs.editorRef.codemirror.closeHint()
      }
    }
    Bus.$on('search:result:rendered', cleanSuggestion)

    Bus.$once('hook:beforeDestroy', () => {
      Bus.$off('search:result:rendered', cleanSuggestion)
    })
  },
  beforeDestroy() {
    if (this.$refs.editorRef) {
      this.$refs.editorRef.codemirror.off('keyup', this.onKeyHandled)
      this.$refs.editorRef.codemirror.off('beforeChange', this.cleanNewLines)
      this.$refs.editorRef.codemirror.off('endCompletion', this.onEndCompletion)
      this.$refs.editorRef.codemirror.off(
        'startCompletion',
        this.onStartCompletion
      )
    }
  },
  methods: {
    execute() {
      const result = parseQuery(
        this.queryInfo.query,
        this.queryInfo.caretPosition,
        {
          groups: this.groupContext.options,
          logSources: this.omniboxContext.logSources,
          flowSources: this.omniboxContext.flowSources,
          tags: this.omniboxContext.tags,
        },
        this.category
      )
      this.searchContext = result
      this.notifyParentForContext()
    },
    onHintSelected(completion) {
      if (/""$/g.test(completion.text)) {
        // move cursor 1 character back
        const cursor = this.$refs.editorRef.codemirror.getCursor()
        cursor.ch -= 1
        this.$refs.editorRef.codemirror.setCursor(cursor)
      }
      if (this.searchContext) {
        setTimeout(() => {
          if (this.searchContext.getError()) {
            return
          }
          this.searchContext.setSuggestionType(null)
          this.$refs.editorRef.codemirror.closeHint()
          if (this.searchContext.isValidSearch()) {
            this.notifyParentForContext()
            this.$emit('enter-key')
          }
        })
      }
    },
    renderSingleMonitor(ele, self, data) {
      const renderedNode = new Vue({
        ...MonitorSuggestionHint,
        parent: this,
        propsData: {
          suggestionType: self.suggestionType,
          value: data,
        },
      }).$mount()
      ele.innerHTML = renderedNode.$el.innerHTML
      renderedNode.$destroy()
    },
    renderSingleSourceType(ele, self, data) {
      const renderedNode = new Vue({
        ...MonitorSuggestionHint,
        parent: this,
        propsData: {
          suggestionType: self.suggestionType,
          value: data,
          sourceTypeSuggestion: true,
        },
      }).$mount()
      ele.innerHTML = renderedNode.$el.innerHTML
      renderedNode.$destroy()
    },
    async notifyParentForContext() {
      setTimeout(async () => {
        const context = this.searchContext
        if (context && !context.getError()) {
          try {
            await context.resolveSourceId()
            const isValidSearch = await context.isValidSearch()
            if (isValidSearch) {
              context.setTimeline(this.timeline)
              context.createWidgetBuilder()
              this.$emit('search-context', context)
            } else {
              this.$emit('search-context', context)
            }
          } catch (e) {
            this.$emit('search-context', context)
            this.$emit('error', e)
          }
        }
      })
    },
    resetSearch() {
      this.$emit('reset')
      this.$refs.editorRef.codemirror.setValue('')
      this.$refs.editorRef.codemirror.focus()
    },
    cleanNewLines(instance, change) {
      var newtext = change.text.join('').replace(/\n/g, '') // remove ALL \n !
      change.update(change.from, change.to, [newtext])
      return true
    },
    onKeyHandled(cm, event) {
      if (this.__isSuggestionsOpen) {
        return
      }
      switch (event.key) {
        case 'Enter':
          event.preventDefault()
          this.notifyParentForContext()
          this.$emit('enter-key')
          break
        case 'Escape':
          event.preventDefault()
          this.$emit('hide')
          break
        case 'ArrowDown':
          event.preventDefault()
          this.$emit('arrow-down')
          break
        case 'ArrowUp':
          event.preventDefault()
          this.$emit('arrow-up')
          break
      }
    },
    onChange(editor) {
      this.queryInfo = {
        query: editor.getValue(),
        caretPosition: editor.getCursor().ch,
      }
    },
    onEditorReady(editor) {
      editor.focus()
      editor.on('keyup', this.onKeyHandled)
      editor.on('beforeChange', this.cleanNewLines)
      editor.on('endCompletion', this.onEndCompletion)
      editor.on('startCompletion', this.onStartCompletion)
    },
    onEndCompletion() {
      setTimeout(() => {
        this.__isSuggestionsOpen = false
      }, 100)
    },
    onStartCompletion() {
      this.__isSuggestionsOpen = true
    },
    async showSuggestions() {
      const suggestionType = this.searchContext.getSuggestionType()
      if (!suggestionType || !suggestionType.type) {
        return
      }
      try {
        const list = await this.searchContext.getSuggestions()
        const options = {}
        if (list && list.length > 0) {
          const startIndex = suggestionType.range
            ? suggestionType.range.start
            : 0
          const endIndex = suggestionType.range ? suggestionType.range.end : 0
          options.hint = () => ({
            from: { line: 0, ch: startIndex },
            to: {
              line: 0,
              ch: endIndex + 1, // end index is excluded so let's add 1
            },
            list: [
              MONITOR_NAME_SUGGESTION,
              INSTANCE_SUGGESTION,
              IP_SUGGESTION,
            ].includes(suggestionType.type)
              ? this.searchContext.getCategory() === 'metric'
                ? list.map((l) => ({ ...l, render: this.renderSingleMonitor }))
                : list
              : SOURCE_TYPE_SUGGESTION === suggestionType.type
              ? list.map((l) => ({
                  ...l,
                  render: this.renderSingleSourceType,
                }))
              : list,
            suggestionType,
          })
          this.$refs.editorRef.codemirror.showHint(options)
          CodeMirror.on(
            this.$refs.editorRef.codemirror.state.completionActive.data,
            'pick',
            this.onHintSelected
          )
        } else {
          options.hint = () => ({
            from: { line: 0, ch: 0 },
            to: {
              line: 0,
              ch: 0,
            },
            list: [],
          })
          this.$refs.editorRef.codemirror.on(
            options,
            'pick',
            this.onHintSelected
          )
          this.$refs.editorRef.codemirror.on(
            options,
            'select',
            this.onHintSelected
          )
          this.$refs.editorRef.codemirror.showHint(options)
        }
        this.$emit('error', undefined)
      } catch (e) {
        this.$emit('error', e)
      }
    },
  },
}
</script>
