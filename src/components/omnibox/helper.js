import { CharStreams, CommonTokenStream } from 'antlr4'
import Met<PERSON><PERSON>exer from '@src/parsers/metric/MetricLexer'
import MetricParser from '@src/parsers/metric/MetricParser'
import LogLexer from '@src/parsers/log/LogLexer'
import LogParser from '@src/parsers/log/LogParser'
// import SearchListener from './search-listener'
import SearchContext from './search-context/search-context'
import SearchErrorListener from './search-error-listener'
import LogSearchTreeVisitor from './log-search-tree-visitor'
import MetricSearchTreeVisitor from './metric-search-tree-visitor'

export const parseQuery = (query, caretPosition, cacheOptions, category) => {
  // Create input stream from the given query string
  const inputStream = CharStreams.fromString(query)
  let lexer
  let parser
  if (category === 'metric') {
    // Create lexer
    lexer = new MetricLexer(inputStream)
    const tokenStream = new CommonTokenStream(lexer)
    // Create parser based on the tokens from lexer
    parser = new MetricParser(tokenStream)
  } else {
    // Create lexer
    lexer = new LogLexer(inputStream)
    const tokenStream = new CommonTokenStream(lexer)
    // Create parser based on the tokens from lexer
    parser = new LogParser(tokenStream)
  }
  const searchContext = new SearchContext(category)

  SearchContext.setGroupCache(cacheOptions.groups)
  SearchContext.setLogSourcesCache(cacheOptions.logSources)
  SearchContext.setFlowSourcesCache(cacheOptions.flowSources)
  SearchContext.setTagCache(cacheOptions.tags)
  SearchContext.setMonitorTypeCache(cacheOptions.monitorTypes)

  parser.removeErrorListeners()

  parser.addErrorListener(new SearchErrorListener(searchContext))

  // Create Abstract Syntax Tree based on the root 'expression' from the parser
  const ast = parser.query()

  // const walker = new SearchListener(searchContext, caretPosition)

  const Instance =
    category === 'metric' ? MetricSearchTreeVisitor : LogSearchTreeVisitor
  const visitor = new Instance(searchContext, caretPosition, parser, category)

  // tree.ParseTreeWalker.DEFAULT.walk(walker, ast)

  visitor.visit(ast)

  if (!searchContext.getError()) {
    searchContext.setSearch(query)
  }

  return searchContext
}
