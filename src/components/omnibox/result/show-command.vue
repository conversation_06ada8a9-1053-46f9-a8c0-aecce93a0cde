<template>
  <div class="flex flex-col flex-1 min-h-0">
    <div class="flex">
      <RouterLink v-if="templatePath" :to="templatePath" target="_blank">
        {{ context.getSource().value }}
      </RouterLink>
      <template v-else>
        {{ context.getSource() && context.getSource().value }}
      </template>
      <span v-if="context.getSource()" class="mx-2"> | </span>
      {{ context.getCounter().counter }}
    </div>
    <div class="my-2 flex justify-between items-center">
      <MRadioGroup
        v-if="widgetTypeOptions.length"
        v-model="widget.widgetType"
        as-button
        :options="widgetTypeOptions"
        class="chart-selector-radio mx-2"
      >
        <template v-slot:option="{ option }">
          <div class="flex items-center justify-center h-full">
            <WidgetTypeIcon
              :widget-type="option.value"
              :tooltip="option.text"
              :size="28"
              :selected="option.value === widget.widgetType"
            />
          </div>
        </template>
      </MRadioGroup>
      <div class="inline-flex items-center">
        <SaveAsWidget
          v-if="hasPreviewRendered"
          :save-widget="handleSaveAsWidget"
        >
          <template v-slot:trigger>
            <a>Save as Widget</a>
          </template>
        </SaveAsWidget>

        <MButton
          class="ml-2 squared-button"
          variant="neutral-lightest"
          title="Share"
          @click="shareMetricExplorer"
        >
          <MIcon name="share-alt" class="excluded-header-icon" />
        </MButton>

        <template
          v-if="counterCategory === 'metric' && canViewInMetricExplorer"
        >
          <span class="mx-2"> | </span>
          <a @click.prevent.stop="navigateToMetricExplorer">
            View in Metric Explorer
          </a>
        </template>
      </div>
    </div>
    <div class="flex-1 flex flex-col min-h-0">
      <WidgetPreview
        ref="previewRef"
        :key="widgetKey"
        :widget.sync="widget"
        hide-title
        @preview-rendered="handlePreviewRendered"
      />
      <ShareModal
        v-if="showShareModal"
        share-type="Metric"
        :processing="processing"
        increase-z-index
        @hide="showShareModal = false"
        @submit="handleShareCapture"
      />
    </div>
  </div>
</template>

<script>
// import Omit from 'lodash/omit'
import html2canvas from 'html2canvas'
import api from '@api'
import Moment from 'moment'

import Bus from '@utils/emitter'
import { WidgetTypeConstants } from '@components/widgets/constants'
import WidgetTypeIcon from '@components/widgets/widget-type-icon/widget-type-icon.vue'
import WidgetPreview from '@components/widgets/preview.vue'
import SaveAsWidget from '@components/widgets/save-as-widget.vue'
import { createWidgetApi } from '@components/widgets/widgets-api'

import ShareModal from '@modules/alert/components/stream/share-model.vue'
import { authComputed } from '@/src/state/modules/auth'

export default {
  name: 'ShowCommand',
  components: {
    WidgetPreview,
    WidgetTypeIcon,
    SaveAsWidget,

    ShareModal,
  },

  props: {
    context: {
      type: Object,
      required: true,
    },
    timeline: {
      type: Object,
      default: undefined,
    },
  },
  data() {
    return {
      hasPreviewRendered: false,
      widgetKey: 1,
      widget: this.context.getWidgetDefinition(),
      showShareModal: false,
      processing: false,
    }
  },
  computed: {
    ...authComputed,
    counterCategory() {
      return this.context.getCounter().category
    },
    templatePath() {
      const entity = this.context.getResolvedEntity() || {}
      if (['Monitor'].includes(entity.entityType)) {
        return this.$modules.getModuleRoute('inventory', 'monitor-template', {
          params: {
            monitorId: entity.monitor.id,
            category: entity.monitor.category,
          },
        })
      }
      return null
    },
    canViewInMetricExplorer() {
      const counter = this.context.getCounter()
      const entity = this.context.getResolvedEntity() || {}
      if (['Monitor'].includes(entity.entityType)) {
        if (counter.rawCounter.indexOf('~') >= 0) {
          if (
            this.context.getInstance() &&
            this.context.getInstance().name &&
            this.context.getInstance().name
          ) {
            return true
          }
          return false
        }
      }
      return this.templatePath
    },
    widgetTypeOptions() {
      if (this.widget.category === WidgetTypeConstants.CHART) {
        return [
          WidgetTypeConstants.AREA,
          WidgetTypeConstants.LINE,
          WidgetTypeConstants.HORIZONTAL_BAR,
          WidgetTypeConstants.VERTICAL_BAR,
          WidgetTypeConstants.STACKED_AREA,
          WidgetTypeConstants.STACKED_LINE,
          WidgetTypeConstants.STACKED_HORIZONTAL_BAR,
          WidgetTypeConstants.STACKED_VERTICAL_BAR,
        ].map((t) => ({ value: t, text: t }))
      } else if (this.widget.category === WidgetTypeConstants.TOPN) {
        return [
          WidgetTypeConstants.AREA,
          WidgetTypeConstants.LINE,
          WidgetTypeConstants.HORIZONTAL_BAR,
          WidgetTypeConstants.VERTICAL_BAR,
          WidgetTypeConstants.PIE,
          WidgetTypeConstants.GRID,
        ].map((t) => ({ value: t, text: t }))
      }
      return []
    },
  },
  watch: {
    'widget.widgetType': function (newValue, oldValue) {
      if (
        [
          WidgetTypeConstants.AREA,
          WidgetTypeConstants.LINE,
          WidgetTypeConstants.HORIZONTAL_BAR,
          WidgetTypeConstants.VERTICAL_BAR,
        ].includes(oldValue)
      ) {
        if (
          newValue === WidgetTypeConstants.PIE ||
          newValue === WidgetTypeConstants.GRID
        ) {
          this.widgetKey++
        }
      } else if (oldValue === WidgetTypeConstants.PIE) {
        if (
          newValue === WidgetTypeConstants.GRID ||
          [
            WidgetTypeConstants.AREA,
            WidgetTypeConstants.LINE,
            WidgetTypeConstants.HORIZONTAL_BAR,
            WidgetTypeConstants.VERTICAL_BAR,
          ].includes(newValue)
        ) {
          this.widgetKey++
        }
      } else if (oldValue === WidgetTypeConstants.GRID) {
        if (
          newValue === WidgetTypeConstants.PIE ||
          [
            WidgetTypeConstants.AREA,
            WidgetTypeConstants.LINE,
            WidgetTypeConstants.HORIZONTAL_BAR,
            WidgetTypeConstants.VERTICAL_BAR,
          ].includes(newValue)
        ) {
          this.widgetKey++
        }
      }
    },
    context(newValue, oldValue) {
      if (!newValue.isIdenticalTo(oldValue)) {
        this.widget = this.context.getWidgetDefinition()
        this.widgetKey++
      }
    },
  },
  methods: {
    handlePreviewRendered($event) {
      this.hasPreviewRendered = $event
      if ($event) {
        Bus.$emit('search:result:rendered')
      }
    },
    navigateToMetricExplorer() {
      const counter = this.context.getCounter()
      const monitor = this.context.getResolvedEntity()
      this.$router.push(
        this.$modules.getModuleRoute('metric-explorer', undefined, {
          query: {
            monitor: monitor.monitor.id,
            counter: counter.counter,
            ...(counter.rawCounter.indexOf('~') >= 0
              ? {
                  instance: encodeURIComponent(
                    btoa(JSON.stringify(this.context.getInstance()))
                  ),
                }
              : {}),
            t: encodeURIComponent(btoa(JSON.stringify(this.timeline))),
          },
        })
      )
      this.$emit('hide-search-bar')
    },
    handleSaveAsWidget(data) {
      return createWidgetApi({
        ...this.widget,
        ...data,
      })
    },
    shareMetricExplorer() {
      // this.$refs.previewRef.captureWidget()
      this.showShareModal = true
    },
    handleShareCapture(formData, recipients) {
      this.processing = true
      this.$refs.previewRef.$el.style.backgroundColor =
        'var(--page-background-color)'

      setTimeout(() => {
        this.$nextTick(async () => {
          const canvas = await html2canvas(this.$refs.previewRef.$el, {
            scale: 1,
            useCORS: false,
          })

          const image = canvas.toDataURL()

          return api
            .getNewClient()
            .post(
              '/upload-image',
              { file: image },
              {
                notify: false,
                headers: { Authorization: `Bearer ${this.accessToken}` },
              }
            )
            .then(({ data }) => {
              Bus.$emit('server:event', {
                'event.type': this.$constants.UI_ACTION_WIDGET_SHARE,
                'event.context': {
                  filename: data['file.name'],
                  recipients,
                  message: formData.message,
                  'user.name': this.user.userName,
                  type: 'Search Query',

                  'search.query': this?.context?._search,
                  Timestamp: Moment().unix() * 1000,
                },
              })

              this.$successNotification({
                message: 'Successful',
                description: `Shared successfully`,
              })

              this.showShareModal = false
              this.processing = false
            })
        })
      }, 400)
    },
  },
}
</script>
