import IsEqual from 'lodash/isEqual'
import Api from '@api'
import { objectDBWorker } from '@/src/workers'
import { WidgetTypeConstants } from '@components/widgets/constants'
import WidgetContextBuilder from '@components/widgets/widget-context-builder'
import provideSuggestions from './suggestions'
import {
  buildWidgetContext,
  getWidgetResponseApi,
  makeCounter,
} from '@/src/utils/socket-event-as-api'

const SEPERATOR = '='

// const sourceTypeMap = {
//   source: 'source/monitor',
//   sourceip: 'ip',
//   sourcegroup: 'group',
// }

export default class SearchContext {
  _search = null

  _error = null

  _suggestion = null

  _command = null

  _source = null

  _resolvedEntity = null

  _counter = null

  _topnsorting = null

  _resultby = null

  _aggr = 'avg'

  _category = null

  _timeline = {
    selectedKey: '-1h',
  }

  _widgetBuilder = null

  _filters = []

  _instance = {}

  static counterCache = {}

  static groupCache = {}

  static logSourcesCache = []

  static flowSourcesCache = []

  static tagCache = []

  static monitorTypeCache = new Map()

  static instanceCache = {}

  constructor(category = 'metric') {
    this._category = category
  }

  static resetCounterCache() {
    SearchContext.counterCache = {}
  }

  static setGroupCache(groups) {
    SearchContext.groupCache = groups
  }

  static resetGroupCache() {
    SearchContext.groupCache = {}
  }

  static setTagCache(tags) {
    SearchContext.tagCache = tags
  }

  static setMonitorTypeCache(monitorTypes) {
    SearchContext.monitorTypeCache = monitorTypes
  }

  static resetTagCache() {
    SearchContext.tagCache = {}
  }

  static setLogSourcesCache(logSources) {
    SearchContext.logSourcesCache = logSources
  }

  static resetLogSourcesCache() {
    SearchContext.logSourcesCache = []
  }

  static setFlowSourcesCache(flowSources) {
    SearchContext.flowSourcesCache = flowSources
  }

  static resetFlowSourcesCache() {
    SearchContext.flowSourcesCache = []
  }

  static resetInstanceCache() {
    SearchContext.instanceCache = {}
  }

  setError(error) {
    this._error = error
  }

  getError() {
    return this._error
  }

  getCategory() {
    return this._category
  }

  setCategory(category) {
    this._category = category
  }

  getFilters() {
    return this._filters
  }

  setFilters(filters) {
    this._filters = filters
  }

  setInstance(name, value) {
    if (name && value) {
      const item = (SearchContext.instanceCache[name] || []).find(
        (i) => i.text === value || i.text === `"${value}"`
      )
      if (item) {
        this.setSource('monitorId', item.entity)
        this._instance = {
          name,
          value,
        }
      }
    } else {
      this._instance = {
        name,
        value,
      }
    }
  }

  getInstance() {
    return this._instance
  }

  addFilter(filter) {
    this._filters = this._filters.filter((f) => f.operand !== filter.operand)
    this._filters.push(filter)
  }

  setSearch(search) {
    this._search = search
  }

  getResolvedEntity() {
    return this._resolvedEntity
  }

  setResolvedEntity(value) {
    this._resolvedEntity = value
  }

  getSearch() {
    return this._search
  }

  setSource(type, value) {
    this._source = {
      entityType: type,
      value: value,
    }
    // SearchContext.resetCounterCache()
  }

  getSource() {
    return this._source
  }

  setResultBy(value) {
    this._resultby = value
  }

  getResultBy() {
    return this._resultby
  }

  setCounter(value) {
    const type = this.getCategory()
    let pluginIds = []
    let counterCategory
    if (SearchContext.counterCache[type]) {
      const selectedCounter = (SearchContext.counterCache[type] || []).find(
        (c) => c.displayText === value
      )
      if (selectedCounter) {
        value = selectedCounter.rawCounter
        pluginIds = selectedCounter.pluginIds
        counterCategory = selectedCounter.counterCategory
      }
    }
    this._counter = {
      category: type,
      rawCounter: value,
      pluginIds: pluginIds,
      counterCategory,
      counter: value.replace(/~/g, '.'),
    }
  }

  getCounter() {
    return this._counter
  }

  setTimeline(value) {
    this._timeline = value

    if (this.getWidgetBuilder()) {
      const _builder = this.getWidgetBuilder()
      _builder.setTimeline(this._timeline)
    }
  }

  getTimeline() {
    return this._timeline
  }

  getTopNSorting() {
    return this._topnsorting
  }

  setTopNSorting(direction, count) {
    this._topnsorting = {
      direction,
      topCount: count,
    }
  }

  setSuggestionType(obj) {
    this._suggestion = obj
  }

  getSuggestionType() {
    return this._suggestion
  }

  setAggr(aggr) {
    this._aggr = aggr
  }

  getAggr() {
    return this._aggr
  }

  setWidgetBuilder(widget) {
    this._widgetBuilder = widget
  }

  getWidgetBuilder() {
    return this._widgetBuilder
  }

  async getSuggestions() {
    const suggestionType = this.getSuggestionType()
    return provideSuggestions(this, suggestionType)
  }

  async getCounterTypeSuggestions() {
    let result = [
      { text: `metric${SEPERATOR}""`, displayText: 'metric' },
      // { text: `log${SEPERATOR}`, displayText: 'log' },
      // { text: `flow${SEPERATOR}`, displayText: 'flow' },
    ]
    return result
  }

  async getInstanceSuggestions(instanceType) {
    const cacheKey = `${instanceType}`
    if (SearchContext.instanceCache[cacheKey]) {
      return SearchContext.instanceCache[cacheKey]
    }
    const response = await getWidgetResponseApi(
      buildWidgetContext({
        groupType: 'metric',
        timeline: {
          selectedKey: 'today',
        },
        counters: [makeCounter(`${instanceType}~instance.name`, 'last')],
        resultBy: this.instance,
      }).generateWidgetDefinition()
    )
    const suggestions = response.map((item) => ({
      text: `"${item[instanceType]}"`,
      entity: item['entity.id'],
      monitor: item['monitor'],
      monitorType: item['object.type'],
      displayText: `${item[instanceType]}`,
    }))

    SearchContext.instanceCache[cacheKey] = suggestions
    return suggestions
  }

  getValidCountersForSource() {
    if (this.getCounter().category === 'metric') {
      return this._getMetricCounters()
    } else if (this.getCounter().category === 'log') {
      return this._getLogCounters()
    } else if (this.getCounter().category === 'flow') {
      return this._getFlowCounters()
    }
    return Promise.resolve([])
  }

  async _getCounterSuggetions(category) {
    const DATA_TYPE_TO_CONSIDER = [1, 2]
    const cacheKey = `${category}`
    if (SearchContext.counterCache[cacheKey]) {
      return SearchContext.counterCache[cacheKey]
    }
    const response = await Api.get('/misc/column-mappers', {
      params: {
        filter: {
          'visualization.group.type': category,
          // 'entity.type': resolvedEntity.entityType,
          // entities: resolvedEntity.entities,
        },
      },
    })
    const counters =
      category === 'log'
        ? Object.keys(response.result)
        : Object.keys(response.result).filter((key) =>
            (response.result[key]['mapper.data.categories'] || []).find(
              (type) => DATA_TYPE_TO_CONSIDER.includes(type)
            )
          )

    const suggestions = counters.map((key) => ({
      text: `${key.replace(/[~^]/g, '.')}`,
      rawCounter: key,
      counterCategory: response.result[key]['mapper.event.category'],
      pluginIds: response.result[key]['mapper.plugin.ids'],
      displayText: key.replace(/[~^]/g, '.'),
    }))

    SearchContext.counterCache[cacheKey] = suggestions
    return suggestions
  }

  async _getMetricCounters() {
    return this._getCounterSuggetions('metric')
  }

  async _getLogCounters() {
    let result = await this._getCounterSuggetions('log')
    const excludedCounters = ['source.host', 'source.plugin']
    result = result.filter(
      (r) => excludedCounters.includes(r.displayText) === false
    )
    return result
  }

  async _getFlowCounters() {
    return this._getCounterSuggetions('flow')
  }

  async resolveSourceId(category) {
    const source = this.getSource()
    if (!source) {
      return
    }
    if (!category) {
      category = this.getCounter().category
    }
    let id
    let monitor
    let entityType = 'Monitor'
    if (source.entityType === 'monitorId') {
      monitor = await objectDBWorker.getObjectById(source.value)
      if (monitor) {
        id = monitor.id
      }
      this.setSource('monitor', monitor.name)
    } else if (source.entityType === 'monitor') {
      monitor = await objectDBWorker.getObjectByName(source.value)
      if (monitor) {
        id = monitor.id
      }
    } else if (source.entityType === 'tag') {
      id = source.value
      entityType = 'Tag'
    } else if (source.entityType === 'ip') {
      if (category === 'metric') {
        monitor = await objectDBWorker.getObjectByIP(source.value)
        if (monitor) {
          id = monitor.id
        }
      } else if (category === 'log') {
        entityType = 'event.source'
        const m = SearchContext.logSourcesCache.find(
          (i) => i.text === source.value
        )
        if (m) {
          monitor = {
            ip: m.text,
            id: m.value,
            name: m.text,
          }
          id = m.value
        }
      } else if (category === 'flow') {
        entityType = 'event.source'
        const m = SearchContext.flowSourcesCache.find(
          (i) => i.text === source.value
        )
        if (m) {
          monitor = {
            ip: m.text,
            id: m.value,
            name: m.text,
          }
          id = m.value
        }
      }
    } else if (source.entityType === 'group') {
      const g = Array.from(SearchContext.groupCache.values()).find(
        (i) => i.name === source.value
      )
      if (g) {
        id = g.id || g.key
      }
      entityType = 'Group'
    } else if (source.entityType === 'sourcetype') {
      id = source.value
      entityType = 'event.source.type'
    }
    this.setResolvedEntity({
      entityType,
      entities: id ? [id] : [],
      monitor: monitor || {},
    })
  }

  async isValidSearch() {
    if (this.getError()) {
      throw this.getError()
    }
    const counter = this.getCounter()
    if ((SearchContext.counterCache[counter.category] || []).length <= 0) {
      await this.getValidCountersForSource()
      this.setCounter(counter.counter)
    }
    if (counter && counter.counter) {
      const selectedCounter = SearchContext.counterCache[counter.category].find(
        (c) => counter.counter === c.displayText
      )
      if (selectedCounter) {
        return true
      }
      const e = new Error(
        `No counter with name ${counter.counter} found for "${
          this.getSource().value
        }"`
      )
      e.contextError = true
      this.setError(e)
      throw e
    }

    return false
  }

  buildSearchResultSuggestion() {
    return {
      title: `${this.getCommand()} ${this.getCounter().counter} data of ${
        this.getSource().value
      }`,
      description: `it will display ${this.getCounter().counter} trend of ${
        this.getSource().value
      } for selected timeline`,
      json: this.toString(),
      search: this.getSearch(),
      widget: this.buildWidgetDefinition(),
    }
  }

  toString() {
    return JSON.stringify({
      counter: this.getCounter().rawCounter,
      category: this.getCounter().category,
      entity: this.getResolvedEntity(),
    })
  }

  async createWidgetBuilder() {
    if (!this.getResolvedEntity()) {
      await this.resolveSourceId(this.getCategory())
    }
    const resolvedEntity = this.getResolvedEntity()
    const builder = new WidgetContextBuilder()
    const topnsorting = this.getTopNSorting()
    const counter = this.getCounter()
    if ((SearchContext.counterCache[this.getCategory()] || []).length <= 0) {
      await this.getValidCountersForSource()
      this.setCounter(counter.counter)
    }
    const aggr = this.getAggr()
    builder.addGroup(counter.category)
    if (topnsorting) {
      builder.setCategory(WidgetTypeConstants.TOPN)
      builder.setWidgetType(WidgetTypeConstants.VERTICAL_BAR)
    } else {
      builder.setCategory(WidgetTypeConstants.CHART)
      builder.setWidgetType(WidgetTypeConstants.LINE)
    }
    if (this.getCategory() === 'log') {
      builder.setWidgetType(WidgetTypeConstants.VERTICAL_BAR)
    }
    if (this.getFilters().length > 0) {
      builder.addPreFilterGroup({
        condition: 'and',
        inclusion: 'include',
        conditions: this.getFilters(),
      })
    }
    if (this.getInstance().name && this.getInstance().value) {
      builder.addPreFilterGroup({
        condition: 'and',
        inclusion: 'include',
        conditions: [
          {
            operand: this.getInstance().name,
            operator: '=',
            value: this.getInstance().value,
          },
        ],
      })
    }
    builder.setTimeLine(this.getTimeline())
    builder.addCounterToGroup({
      counter: counter.rawCounter,
      aggrigateFn: aggr,
    })
    const widgetProperties = {
      styleSetting: {
        legendEnabled: true,
      },
    }
    if (topnsorting) {
      widgetProperties.sortingSetting = {
        column: `${counter.rawCounter}.${aggr}`,
        direction: topnsorting.direction,
        showSparklineChart: false,
        sparklineChartType: 'sparkline',
        topCount: topnsorting.topCount,
      }

      widgetProperties.styleSetting = {
        legendEnabled: true,
      }
    }
    if (topnsorting) {
      if (counter.rawCounter.indexOf('~') >= 0) {
        builder.addResultBy(counter.rawCounter.split('~')[0])
      } else {
        builder.addResultBy(
          this.getCategory() === 'metric' ? 'monitor' : 'event.source'
        )
      }
    }
    if (this.getResultBy()) {
      builder.addResultBy(this.getResultBy())
    } else {
      if (this.getInstance().name && this.getInstance().value) {
        builder.addResultBy([this.getInstance().name])
      }
    }
    builder.setWidgetProperties(widgetProperties)
    if (resolvedEntity) {
      builder.addEntities(resolvedEntity.entityType, resolvedEntity.entities)
    } else {
      builder.addEntities(undefined, undefined)
    }

    this.setWidgetBuilder(builder)
  }

  getWidgetDefinition() {
    const builder = this.getWidgetBuilder()
    if (builder) {
      return builder.getContext()
    } else {
      const error = new Error('Unable to build widget definition')
      error.contextError = true
      throw error
    }
  }

  isIdenticalTo(otherContext) {
    // if (otherContext.getSearch() !== this.getSearch()) {
    //   return false
    // }
    if (
      !IsEqual(
        (otherContext.getSource() || {}).value,
        (this.getSource() || {}).value
      )
    ) {
      return false
    }
    if (!IsEqual(otherContext.getResultBy(), this.getResultBy())) {
      return false
    }
    if (!IsEqual(otherContext.getFilters(), this.getFilters())) {
      return false
    }
    if (!IsEqual(otherContext.getCounter(), this.getCounter())) {
      return false
    }
    if (!IsEqual(otherContext.getTimeline(), this.getTimeline())) {
      return false
    }
    if (!IsEqual(otherContext.getTopNSorting(), this.getTopNSorting())) {
      return false
    }
    return true
  }
}
