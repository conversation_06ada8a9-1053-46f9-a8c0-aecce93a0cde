import Fuse from 'fuse.js'
import UniqBy from 'lodash/uniqBy'
import Trim from 'lodash/trim'
import { objectDBWorker } from '@/src/workers'
import {
  COUNTER_WITH_TOP_SUGGESTION,
  WIDGET_GROUP_TYPE_SUGGESTION,
  COUNTER_SUGGESTION,
  GROUP_SUGGESTION,
  IP_SUGGESTION,
  ENTITY_TYPE_SUGGESTION,
  MONITOR_NAME_SUGGESTION,
  TOP_WITH_GROUPTYPE_SUGGESTION,
  WHERE_OR_RESULT_BY_SUGGESTION,
  RESULT_BY_SUGGESTION,
  RESULT_BY_VALUES_SUGGESTION,
  TAG_SUGGESTION,
  WHERE_SUGGESTION,
  TOP_SUGGESTION,
  INSTANCE_SUGGESTION,
  SEPERATOR,
  SOURCE_TYPE_SUGGESTION,
} from './search-constants'
import SearchContext from './search-context'
import { getResultByOptionsApi } from '../../widgets/widgets-api'

export default async function provideSuggestions(instance, suggestionType) {
  if (!suggestionType || !suggestionType.type) {
    return
  }
  let result
  let thresold = 0.2
  let searchKeys = ['displayText']
  const typedText = Trim(
    suggestionType.text === '<missing null>' ? '' : suggestionType.text || ''
  )
    .replace(/"/g, '')
    .toLowerCase()
  switch (suggestionType.type) {
    case COUNTER_WITH_TOP_SUGGESTION:
      result = [
        { text: 'top 10 ', displayText: 'top' },
        { text: 'last 10 ', displayText: 'last' },
      ]
      let counters = []
      if (suggestionType.groupType === 'metric') {
        counters = await instance._getMetricCounters()
      } else if (suggestionType.groupType === 'log') {
        counters = await instance._getLogCounters()
      } else if (suggestionType.groupType === 'flow') {
        counters = await instance._getFlowCounters()
      }

      result = [...result, ...counters]
      thresold = 0
      break

    case WHERE_OR_RESULT_BY_SUGGESTION:
      result = [
        { text: 'result by ', displayText: 'result by' },
        { text: 'where ', displayText: 'where' },
      ]
      thresold = 0
      break
    case WHERE_SUGGESTION:
      result = [{ text: ' where ', displayText: 'where' }]
      thresold = 0
      break
    case RESULT_BY_SUGGESTION:
      result = [{ text: ' result by ', displayText: 'result by' }]
      thresold = 0
      break
    case RESULT_BY_VALUES_SUGGESTION:
      const counter = instance.getCounter()
      const category = instance.getCategory()
      if (category === 'metric') {
        result = [
          { text: `monitor`, displayText: 'monitor' },
          { text: `group`, displayText: 'group' },
          { text: `tag`, displayText: 'tag' },
          ...(counter && counter.rawCounter.indexOf('~') >= 0
            ? [
                {
                  text: `${counter.rawCounter.split('~')[0]}`,
                  displayText: counter.rawCounter.split('~')[0],
                },
              ]
            : []),
        ]
      } else {
        const data = await getResultByOptionsApi(
          counter.pluginIds,
          counter.counterCategory
        )
        result = Object.freeze(data.map((i) => ({ text: i, displayText: i })))
      }
      thresold = 0
      break
    case ENTITY_TYPE_SUGGESTION:
      result = [
        { text: `ip${SEPERATOR}""`, displayText: 'ip' },
        ...(instance.getCategory() === 'metric'
          ? [{ text: `monitor${SEPERATOR}""`, displayText: 'monitor' }]
          : []),
        ...(instance.getCategory() === 'log'
          ? [{ text: `sourcetype${SEPERATOR}""`, displayText: 'sourcetype' }]
          : []),
        { text: `group${SEPERATOR}""`, displayText: 'group' },
        ...(instance.getCategory() === 'metric'
          ? [{ text: `tag${SEPERATOR}""`, displayText: 'tag' }]
          : []),
      ]
      if (
        instance.getCategory() === 'metric' &&
        instance.getCounter().rawCounter.indexOf('~') >= 0
      ) {
        result = [
          ...result,
          {
            text: `${
              instance.getCounter().rawCounter.split('~')[0]
            }${SEPERATOR}""`,
            displayText: instance.getCounter().rawCounter.split('~')[0],
          },
        ]
      }
      thresold = 0
      break
    case TOP_SUGGESTION:
      thresold = 0
      result = [
        { text: 'top ', displayText: 'top' },
        { text: 'last ', displayText: 'last' },
      ]
      break
    case TOP_WITH_GROUPTYPE_SUGGESTION:
      result = await instance.getCounterTypeSuggestions()
      result = [
        ...result,
        { text: 'top 10 ', displayText: 'top' },
        { text: 'last 10 ', displayText: 'last' },
      ]
      thresold = 0
      break
    case MONITOR_NAME_SUGGESTION:
      let sources = await objectDBWorker.getObjects({})
      result = sources.map((m) => ({
        text: `"${m.name}"`,
        displayText: m.name,
        monitor: m,
      }))
      break
    case TAG_SUGGESTION:
      result = SearchContext.tagCache.map((i) => ({
        text: `"${i.text}"`,
        displayText: `${i.text}`,
      }))
      break
    case IP_SUGGESTION:
      if (instance.getCategory() === 'metric') {
        let ips = await objectDBWorker.getObjects({})
        result = [
          ...ips
            .map((m) =>
              m.ip
                ? { text: `"${m.ip}"`, displayText: m.ip, monitor: m }
                : false
            )
            .filter(Boolean),
        ]
      } else {
        if (instance.getCategory() === 'log') {
          result = SearchContext.logSourcesCache.map((m) => ({
            text: `"${m.text}"`,
            displayText: m.text,
            monitor: m.value,
          }))
        } else {
          result = SearchContext.flowSourcesCache.map((m) => ({
            text: `"${m.text}"`,
            displayText: m.text,
            monitor: m.value,
          }))
        }
        break
      }
      break
    case GROUP_SUGGESTION:
      const groupMap = SearchContext.groupCache
      result = Array.from(groupMap.values()).map((g) => ({
        text: `"${g.name}"`,
        displayText: g.name,
      }))
      break
    case INSTANCE_SUGGESTION:
      result = await instance.getInstanceSuggestions(
        instance.getCounter().rawCounter.split('~')[0]
      )
      searchKeys = ['displayText', 'monitor']
      break
    case SOURCE_TYPE_SUGGESTION:
      const logSourceType = SearchContext.monitorTypeCache
      result = Array.from(logSourceType).map((i) => ({
        text: `"${i.key}"`,
        displayText: i.name,
      }))
      break
    case WIDGET_GROUP_TYPE_SUGGESTION:
      result = await instance.getCounterTypeSuggestions()
      thresold = 0
      break
    case COUNTER_SUGGESTION:
      if (/(>|<|>=|<=|!=)/.test(typedText)) {
        result = []
      } else {
        if (suggestionType.groupType === 'metric') {
          result = await instance._getMetricCounters()
        } else if (suggestionType.groupType === 'log') {
          result = await instance._getLogCounters()
        } else if (suggestionType.groupType === 'flow') {
          result = await instance._getFlowCounters()
        }
      }
      break
    default:
      break
  }
  if (typedText.length > 0) {
    const fuse = new Fuse(result, { keys: searchKeys })
    result = fuse.search(typedText, { thresold })
  }
  return UniqBy(result, 'displayText')
}
