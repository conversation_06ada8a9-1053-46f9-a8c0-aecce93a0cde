import Fuse from 'fuse.js'
import UniqBy from 'lodash/uniqBy'
import IsEqual from 'lodash/isEqual'
import Trim from 'lodash/trim'
import Api from '@api'
import { objectDBWorker } from '@/src/workers'
import { WidgetTypeConstants } from '@components/widgets/constants'
import WidgetContextBuilder from '@components/widgets/widget-context-builder'
import {
  COMMAND_SUGGESTION,
  WIDGET_GROUP_TYPE_SUGGESTION,
  COUNTER_SUGGESTION,
  GROUP_SUGGESTION,
  IP_SUGGESTION,
  ENTITY_TYPE_SUGGESTION,
  MONITOR_NAME_SUGGESTION,
  COMMANDS,
  TOP_WITH_GROUPTYPE_SUGGESTION,
  WHERE_OR_RESULT_BY_SUGGESTION,
  RESULT_BY_SUGGESTION,
  RESULT_BY_VALUES_SUGGESTION,
  TAG_SUGGESTION,
  WHERE_SUGGESTION,
  TOP_SUGGESTION,
} from './helper'

const SEPERATOR = '='

// const sourceTypeMap = {
//   source: 'source/monitor',
//   sourceip: 'ip',
//   sourcegroup: 'group',
// }

export default class SearchContext {
  _search = null

  _error = null

  _suggestion = null

  _command = null

  _source = null

  _resolvedEntity = null

  _counter = null

  _topnsorting = null

  _resultby = null

  _aggr = 'avg'

  _timeline = {
    selectedKey: '-1h',
  }

  _widgetBuilder = null

  static counterCache = {}

  static groupCache = {}

  static logSourcesCache = []

  static flowSourcesCache = []

  static tagCache = []

  static resetCounterCache() {
    SearchContext.counterCache = {}
  }

  static setGroupCache(groups) {
    SearchContext.groupCache = groups
  }

  static resetGroupCache() {
    SearchContext.groupCache = {}
  }

  static setTagCache(tags) {
    SearchContext.tagCache = tags
  }

  static resetTagCache() {
    SearchContext.tagCache = {}
  }

  static setLogSourcesCache(logSources) {
    SearchContext.logSourcesCache = logSources
  }

  static resetLogSourcesCache() {
    SearchContext.logSourcesCache = []
  }

  static setFlowSourcesCache(flowSources) {
    SearchContext.flowSourcesCache = flowSources
  }

  static resetFlowSourcesCache() {
    SearchContext.flowSourcesCache = []
  }

  setError(error) {
    this._error = error
  }

  getError() {
    return this._error
  }

  setSearch(search) {
    this._search = search
  }

  getResolvedEntity() {
    return this._resolvedEntity
  }

  setResolvedEntity(value) {
    this._resolvedEntity = value
  }

  getSearch() {
    return this._search
  }

  setCommand(command) {
    this._command = command
  }

  getCommand() {
    return this._command
  }

  setSource(type, value) {
    this._source = {
      entityType: type,
      value: value,
    }
    SearchContext.resetCounterCache()
  }

  getSource() {
    return this._source
  }

  setResultBy(value) {
    this._resultby = value
  }

  getResultBy() {
    return this._resultby
  }

  setCounter(type, value) {
    if (SearchContext.counterCache[type]) {
      const selectedCounter = (SearchContext.counterCache[type] || []).find(
        (c) => c.displayText === value
      )
      if (selectedCounter) {
        value = selectedCounter.rawCounter
      }
    }
    this._counter = {
      category: type,
      rawCounter: value,
      counter: value.replace(/~/g, '.'),
    }
  }

  getCounter() {
    return this._counter
  }

  setTimeline(value) {
    this._timeline = value

    if (this.getWidgetBuilder()) {
      const _builder = this.getWidgetBuilder()
      _builder.setTimeline(this._timeline)
    }
  }

  getTimeline() {
    return this._timeline
  }

  getTopNSorting() {
    return this._topnsorting
  }

  setTopNSorting(direction, count) {
    this._topnsorting = {
      direction,
      topCount: count,
    }
  }

  setSuggestionType(obj) {
    this._suggestion = obj
  }

  getSuggestionType() {
    return this._suggestion
  }

  setAggr(aggr) {
    this._aggr = aggr
  }

  getAggr() {
    return this._aggr
  }

  setWidgetBuilder(widget) {
    this._widgetBuilder = widget
  }

  getWidgetBuilder() {
    return this._widgetBuilder
  }

  async getSuggestions() {
    const suggestionType = this.getSuggestionType()
    if (!suggestionType || !suggestionType.type) {
      return
    }
    let result
    let thresold = 0.2
    switch (suggestionType.type) {
      case COMMAND_SUGGESTION:
        result = [{ text: 'show ', displayText: 'show' }]
        thresold = 0
        break
      case WHERE_OR_RESULT_BY_SUGGESTION:
        result = [
          { text: 'result by ""', displayText: 'result by' },
          { text: 'where ', displayText: 'where' },
        ]
        thresold = 0
        break
      case WHERE_SUGGESTION:
        result = [{ text: 'where ', displayText: 'where' }]
        thresold = 0
        break
      case RESULT_BY_SUGGESTION:
        result = [{ text: 'result by ""', displayText: 'result by' }]
        thresold = 0
        break
      case RESULT_BY_VALUES_SUGGESTION:
        const counter = this.getCounter()
        result = [
          { text: `"monitor"`, displayText: 'monitor' },
          { text: `"group"`, displayText: 'group' },
          { text: `"tag"`, displayText: 'tag' },
          ...(counter && counter.rawCounter.indexOf('~') >= 0
            ? [
                {
                  text: `"${counter.rawCounter.split('~')[0]}"`,
                  displayText: counter.rawCounter.split('~')[0],
                },
              ]
            : []),
        ]
        thresold = 0
        break
      case ENTITY_TYPE_SUGGESTION:
        result = [
          { text: `monitor${SEPERATOR}""`, displayText: 'monitor' },
          { text: `ip${SEPERATOR}""`, displayText: 'ip' },
          { text: `group${SEPERATOR}""`, displayText: 'group' },
          { text: `tag${SEPERATOR}""`, displayText: 'tag' },
        ]
        thresold = 0
        break
      case TOP_SUGGESTION:
        thresold = 0
        result = [
          { text: 'top ', displayText: 'top' },
          { text: 'last ', displayText: 'last' },
        ]
        break
      case TOP_WITH_GROUPTYPE_SUGGESTION:
        result = await this.getCounterTypeSuggestions()
        result = [
          ...result,
          { text: 'top 10 ', displayText: 'top' },
          { text: 'last 10 ', displayText: 'last' },
        ]
        thresold = 0
        break
      case MONITOR_NAME_SUGGESTION:
        let sources = await objectDBWorker.getObjects({})
        result = sources.map((m) => ({
          text: `"${m.name}"`,
          displayText: m.name,
          monitor: m,
        }))
        break
      case TAG_SUGGESTION:
        result = SearchContext.tagCache.map((i) => ({
          text: `"${i.text}"`,
          displayText: `${i.text}`,
        }))
        break
      case IP_SUGGESTION:
        let ips = await objectDBWorker.getObjects({})
        result = [
          ...ips
            .map((m) =>
              m.ip
                ? { text: `"${m.ip}"`, displayText: m.ip, monitor: m }
                : false
            )
            .filter(Boolean),
          ...SearchContext.logSourcesCache.map((i) => ({
            text: `"${i.text}"`,
            displayText: `log (${i.text})`,
            monitor: { id: i.value, name: `Log (${i.text})`, ip: i.text },
          })),
          ...SearchContext.flowSourcesCache.map((i) => ({
            text: `"${i.text}"`,
            displayText: `flow (${i.text})`,
            monitor: { id: i.value, name: `Flow (${i.text})`, ip: i.text },
          })),
        ]
        break
      case GROUP_SUGGESTION:
        const groupMap = SearchContext.groupCache
        result = Array.from(groupMap.values()).map((g) => ({
          text: `"${g.name}"`,
          displayText: g.name,
        }))
        break
      case WIDGET_GROUP_TYPE_SUGGESTION:
        result = await this.getCounterTypeSuggestions()
        thresold = 0
        break
      case COUNTER_SUGGESTION:
        if (suggestionType.groupType === 'metric') {
          result = await this._getMetricCounters()
        } else if (suggestionType.groupType === 'log') {
          result = await this._getLogCounters()
        } else if (suggestionType.groupType === 'flow') {
          result = await this._getFlowCounters()
        }
        break
      default:
        break
    }
    const typedText = Trim(suggestionType.text || '')
      .replace(/"/g, '')
      .toLowerCase()
    if (typedText.length > 0) {
      const fuse = new Fuse(result, { keys: ['displayText'] })
      result = fuse.search(typedText, { thresold })
    }
    return UniqBy(result, 'displayText')
  }

  async getCounterTypeSuggestions() {
    let result = [
      { text: `metric${SEPERATOR}""`, displayText: 'metric' },
      // { text: `log${SEPERATOR}`, displayText: 'log' },
      // { text: `flow${SEPERATOR}`, displayText: 'flow' },
    ]
    return result
  }

  getValidCountersForSource() {
    if (this.getCounter().category === 'metric') {
      return this._getMetricCounters()
    } else if (this.getCounter().category === 'log') {
      return this._getLogCounters()
    } else if (this.getCounter().category === 'flow') {
      return this._getFlowCounters()
    }
    return Promise.resolve([])
  }

  async _getCounterSuggetions(category) {
    const cacheKey = `${category}`
    if (SearchContext.counterCache[cacheKey]) {
      return SearchContext.counterCache[cacheKey]
    }
    const response = await Api.get('/misc/column-mappers', {
      params: {
        filter: {
          'visualization.group.type': category,
          // 'entity.type': resolvedEntity.entityType,
          // entities: resolvedEntity.entities,
        },
      },
    })
    const counters =
      category === 'log'
        ? Object.keys(response.result)
        : Object.keys(response.result).filter(
            (key) =>
              !(response.result[key]['mapper.data.categories'] || []).includes(
                0
              )
          )

    const suggestions = counters.map((key) => ({
      text: `"${key.replace(/[~^]/g, '.')}"`,
      rawCounter: key,
      displayText: key.replace(/[~^]/g, '.'),
    }))

    SearchContext.counterCache[cacheKey] = suggestions
    return suggestions
  }

  async _getMetricCounters() {
    return this._getCounterSuggetions('metric')
  }

  async _getLogCounters() {
    let result = await this._getCounterSuggetions('log')
    const excludedCounters = ['source.host', 'source.plugin']
    result = result.filter(
      (r) => excludedCounters.includes(r.displayText) === false
    )
    return result
  }

  async _getFlowCounters() {
    return this._getCounterSuggetions('flow')
  }

  async resolveSourceId(category) {
    const source = this.getSource()
    if (!source) {
      return
    }
    if (!category) {
      category = this.getCounter().category
    }
    let id
    let monitor
    let entityType = 'Monitor'
    if (source.entityType === 'monitor') {
      monitor = await objectDBWorker.getObjectByName(source.value)
      if (monitor) {
        id = monitor.id
      }
    } else if (source.entityType === 'tag') {
      id = source.value
      entityType = 'Tag'
    } else if (source.entityType === 'ip') {
      if (category === 'metric') {
        monitor = await objectDBWorker.getObjectByIP(source.value)
        if (monitor) {
          id = monitor.id
        }
      } else if (category === 'log') {
        entityType = 'event.source'
        const m = SearchContext.logSourcesCache.find(
          (i) => i.text === source.value
        )
        if (m) {
          monitor = {
            ip: m.text,
            id: m.value,
            name: m.text,
          }
          id = m.value
        }
      } else if (category === 'flow') {
        entityType = 'event.source'
        const m = SearchContext.flowSourcesCache.find(
          (i) => i.text === source.value
        )
        if (m) {
          monitor = {
            ip: m.text,
            id: m.value,
            name: m.text,
          }
          id = m.value
        }
      }
    } else if (source.entityType === 'group') {
      const g = Array.from(SearchContext.groupCache.values()).find(
        (i) => i.name === source.value
      )
      if (g) {
        id = g.id || g.key
      }
      entityType = 'Group'
    }
    this.setResolvedEntity({
      entityType,
      entities: id ? [id] : [],
      monitor: monitor || {},
    })
  }

  async isValidSearch() {
    if (this.getError()) {
      throw this.getError()
    }
    if (
      [COMMANDS.SHOW, COMMANDS.GET, COMMANDS.VIEW].includes(this.getCommand())
    ) {
      const counter = this.getCounter()
      if (!SearchContext.counterCache[counter.category]) {
        await this.getValidCountersForSource()
        this.setCounter(counter.category, counter.counter)
      }
      if (counter && counter.counter) {
        const selectedCounter = SearchContext.counterCache[
          counter.category
        ].find((c) => counter.counter === c.displayText)
        if (selectedCounter) {
          return true
        }
        const e = new Error(
          `No counter with name ${counter.counter} found for "${
            this.getSource().value
          }"`
        )
        e.contextError = true
        this.setError(e)
        throw e
      }
    }

    return false
  }

  buildSearchResultSuggestion() {
    if (
      [COMMANDS.SHOW, COMMANDS.GET, COMMANDS.VIEW].includes(this.getCommand())
    ) {
      return {
        title: `${this.getCommand()} ${this.getCounter().counter} data of ${
          this.getSource().value
        }`,
        description: `it will display ${this.getCounter().counter} trend of ${
          this.getSource().value
        } for selected timeline`,
        json: this.toString(),
        search: this.getSearch(),
        widget: this.buildWidgetDefinition(),
      }
    }
  }

  toString() {
    return JSON.stringify({
      counter: this.getCounter().rawCounter,
      category: this.getCounter().category,
      entity: this.getResolvedEntity(),
      command: this.getCommand(),
    })
  }

  async createWidgetBuilder() {
    if (!this.getResolvedEntity()) {
      await this.resolveSourceId()
    }
    const resolvedEntity = this.getResolvedEntity()
    const builder = new WidgetContextBuilder()
    const topnsorting = this.getTopNSorting()
    const counter = this.getCounter()
    if (!SearchContext.counterCache[counter.category]) {
      await this.getValidCountersForSource()
      this.setCounter(counter.category, counter.counter)
    }
    const aggr = this.getAggr()
    builder.addGroup(counter.category)
    if (topnsorting) {
      builder.setCategory(WidgetTypeConstants.TOPN)
      builder.setWidgetType(WidgetTypeConstants.GRID)
    } else {
      builder.setCategory(WidgetTypeConstants.CHART)
      builder.setWidgetType(WidgetTypeConstants.LINE)
    }
    builder.setTimeLine(this.getTimeline())
    builder.addCounterToGroup({
      counter: counter.rawCounter,
      aggrigateFn: aggr,
    })
    const widgetProperties = {
      styleSetting: {
        legendEnabled: true,
      },
    }
    if (topnsorting) {
      widgetProperties.sortingSetting = {
        column: `${counter.rawCounter}.${aggr}`,
        direction: topnsorting.direction,
        showSparklineChart: false,
        sparklineChartType: 'sparkline',
        topCount: topnsorting.topCount,
      }

      widgetProperties.styleSetting = {
        legendEnabled: true,
      }
    }
    if (topnsorting) {
      if (counter.rawCounter.indexOf('~') >= 0) {
        builder.addResultBy(counter.rawCounter.split('~')[0])
      } else {
        builder.addResultBy('monitor')
      }
    }
    if (this.getResultBy()) {
      builder.addResultBy(this.getResultBy())
    }
    builder.setWidgetProperties(widgetProperties)
    if (resolvedEntity) {
      builder.addEntities(resolvedEntity.entityType, resolvedEntity.entities)
    }

    this.setWidgetBuilder(builder)
  }

  getWidgetDefinition() {
    const builder = this.getWidgetBuilder()
    if (builder) {
      return builder.getContext()
    } else {
      const error = new Error('Unable to build widget definition')
      error.contextError = true
      throw error
    }
  }

  isIdenticalTo(otherContext) {
    if (otherContext.getSearch() !== this.getSearch()) {
      return false
    }
    if (otherContext.getCommand() !== this.getCommand()) {
      return false
    }
    if (!IsEqual(otherContext.getSource(), this.getSource())) {
      return false
    }
    if (!IsEqual(otherContext.getCounter(), this.getCounter())) {
      return false
    }
    if (!IsEqual(otherContext.getTimeline(), this.getTimeline())) {
      return false
    }
    return true
  }
}
