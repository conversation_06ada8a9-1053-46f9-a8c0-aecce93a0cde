<script>
import { getSourceApi } from '@components/widgets/widgets-api'
import { getAllTagsApi } from '@modules/settings/monitoring/monitors-api'

export default {
  name: 'OmniboxProvider',
  provide() {
    const omniboxContext = {
      refresh: this.fetchGroups,
    }
    Object.defineProperty(omniboxContext, 'logSources', {
      enumerable: true,
      get: () => {
        return this.logSources
      },
    })
    Object.defineProperty(omniboxContext, 'flowSources', {
      enumerable: true,
      get: () => {
        return this.flowSources
      },
    })
    Object.defineProperty(omniboxContext, 'tags', {
      enumerable: true,
      get: () => {
        return this.tags
      },
    })
    return { omniboxContext }
  },
  data() {
    return {
      logSources: [],
      flowSources: [],
      tags: [],
    }
  },
  created() {
    this.getTags()
    this.getLogSources()
    this.getFlowSources()
  },
  methods: {
    getTags() {
      getAllTagsApi(false).then((data) => {
        this.tags = data.map((d) => ({ ...d, text: d.tag, value: d.tag }))
      })
    },
    getLogSources() {
      getSourceApi('log', true).then(({ result }) => {
        this.logSources = result.map((i) => ({ value: i.ip, text: i.ip }))
      })
    },
    getFlowSources() {
      getSourceApi('flow', true).then(({ result }) => {
        this.flowSources = result.map((i) => ({ value: i.ip, text: i.ip }))
      })
    },
  },
  render() {
    return this.$scopedSlots.default()
  },
}
</script>
