<template>
  <div
    ref="suggestionContainerRef"
    class="fixed suggestion-container"
    :style="listPositionStyle"
  >
    <div
      v-for="(suggestion, index) in filteredList"
      :key="suggestion"
      class="suggestion"
      :class="{ active: activeIndex === index && hoverResult === undefined }"
      @mouseenter="hoverResult = index"
      @mouseleave="hoverResult = undefined"
      @click="$emit('click', suggestion)"
    >
      {{ suggestion }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'AutoCompleteList',
  props: {
    list: {
      type: Array,
      default() {
        return []
      },
    },
    caretPosition: {
      type: Object,
      default() {
        return {
          top: '55px',
          left: '50px',
        }
      },
    },
  },
  data() {
    return {
      activeIndex: 0,
      hoverResult: undefined,
    }
  },
  computed: {
    listPositionStyle() {
      return this.caretPosition
    },
    filteredList() {
      return this.list.slice(0, 20)
    },
  },
  watch: {
    filteredList() {
      this.activeIndex = 0
    },
  },
  mounted() {
    const handleKeyup = (event) => {
      switch (event.key) {
        case 'Escape':
          event.stopPropagation()
          this.$emit('escape')
          break
        case 'Enter':
          event.stopPropagation()
          this.$emit('click', this.filteredList[this.activeIndex])
          break
        case 'ArrowDown':
          event.stopPropagation()
          this.handleArrowDown()
          break
        case 'ArrowUp':
          event.stopPropagation()
          this.handleArrowUp()
          break
      }
    }
    document.addEventListener('keyup', handleKeyup)

    this.$once('hook:beforeDestroy', () => {
      document.removeEventListener('keyup', handleKeyup)
    })
  },
  methods: {
    getActiveItem() {
      return this.filteredList[this.activeIndex]
    },
    handleArrowUp() {
      const activeIndex = this.activeIndex + this.filteredList.length - 1
      this.activeIndex = activeIndex % this.filteredList.length
      this.showActiveIndexItemInView()
    },
    handleArrowDown() {
      const activeIndex = this.activeIndex + 1
      this.activeIndex = activeIndex % this.filteredList.length
      this.showActiveIndexItemInView()
    },
    showActiveIndexItemInView() {
      const activeItem = Array.from(
        this.$refs.suggestionContainerRef.querySelectorAll(`.suggestion`)
      )[this.activeIndex]
      if (activeItem) {
        activeItem.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
          inline: 'center',
        })
      }
    },
  },
}
</script>
