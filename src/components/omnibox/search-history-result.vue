<template>
  <div
    class="search-result border-bot"
    :class="{ selected: selected }"
    v-on="$listeners"
  >
    <div class="search-result-title">
      {{ result.title }}
    </div>
    <div
      v-if="result.description"
      class="search-result-description text-neutral-light"
    >
      <small>{{ result.description }}</small>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SearchHistoryResult',
  props: {
    result: {
      type: Object,
      required: true,
    },
    selected: {
      type: Boolean,
      default: false,
    },
  },
}
</script>
