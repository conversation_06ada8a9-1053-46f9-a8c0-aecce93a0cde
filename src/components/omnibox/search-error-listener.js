import antlr4 from 'antlr4'

// This class defines a complete listener for a parse tree produced by SearchParser.
export default class SearchErrorListener extends antlr4.error.ErrorListener {
  constructor(searchContext) {
    super()
    this.context = searchContext
  }

  syntaxError(recognizer, offendingSymbol, line, column, msg, e) {
    const expectedTokens = recognizer.atn
      .getExpectedTokens(recognizer.state, recognizer._ctx)
      .toString(recognizer.literalNames, recognizer.symbolicNames)
    const error = new Error(
      `Encountered error at column ${column} with character ${offendingSymbol.text}, expecting ${expectedTokens}`
    )
    error.parseError = true
    this.context.setError(error)
  }
}
