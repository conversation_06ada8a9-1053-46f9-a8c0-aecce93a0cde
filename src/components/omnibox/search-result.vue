<template>
  <div class="border-top py-2 px-2 h-full flex flex-col flex-1">
    <component
      :is="component"
      :context="context"
      v-bind="$attrs"
      v-on="$listeners"
    />
  </div>
</template>

<script>
import ShowCommand from './result/show-command.vue'

export default {
  name: 'SearchResult',
  components: {
    ShowCommand,
  },
  inheritAttrs: false,
  props: {
    context: {
      type: Object,
      required: true,
    },
  },
  computed: {
    component() {
      const category = this.context.getCategory()
      switch (category) {
        case 'metric':
        case 'log':
        case 'flow':
          return ShowCommand
        default:
          return 'div'
      }
    },
  },
}
</script>
