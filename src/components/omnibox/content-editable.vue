<template>
  <component
    :is="tag"
    ref="element"
    :contenteditable="contenteditable"
    @input="update"
    @blur="update"
    @paste="onPaste"
    @keypress="onKeypress"
    @keyup="$emit('keyup', $event)"
  >
  </component>
</template>

<script>
export default {
  name: 'ContentEditable',
  model: { event: 'change' },
  props: {
    tag: { type: String, default: 'div' },
    contenteditable: {
      type: Boolean,
      // eslint-disable-next-line
      default: true,
    },
    value: { type: String, default: undefined },
    noHTML: {
      type: Boolean,
      default: false,
    },
    noNL: {
      type: Boolean,
      default: false,
    },
    autoFocus: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    value(newValue, oldValue) {
      if (newValue !== this.currentContent()) {
        this.updateContent(newValue || '')
        this.$nextTick(() => this.setCursorToEnd())
      }
    },
  },
  mounted() {
    this.updateContent(this.value || '')
    if (this.autoFocus) {
      this.$refs.element.focus()
    }
  },
  methods: {
    focus() {
      setTimeout(() => {
        this.$refs.element.focus()
        this.setCursorToEnd()
      })
    },
    getText() {
      return this.$refs.element.innerText
    },
    replaceAll(str, search, replacement) {
      return str.split(search).join(replacement)
    },
    update(event) {
      this.$emit('change', this.currentContent())
    },
    updateContent(newcontent) {
      if (this.noHTML) {
        this.$refs.element.innerText = newcontent
      } else {
        this.$refs.element.innerHTML = newcontent
      }
    },
    currentContent() {
      return this.noHTML
        ? this.$refs.element.innerText
        : this.$refs.element.innerHTML
    },
    onPaste(event) {
      event.preventDefault()
      let text = (event.originalEvent || event).clipboardData.getData(
        'text/plain'
      )
      if (this.noNL) {
        text = this.replaceAll(text, '\r\n', ' ')
        text = this.replaceAll(text, '\n', ' ')
        text = this.replaceAll(text, '\r', ' ')
      }
      window.document.execCommand('insertText', false, text)
    },
    onKeypress(event) {
      if (event.key === 'Enter' && this.noNL) {
        event.preventDefault()
        this.$emit('enter', this.currentContent())
      }
    },
    setCursorToEnd() {
      const contentEditableElement = this.$refs.element
      var range, selection
      if (document.createRange) {
        // Firefox, Chrome, Opera, Safari, IE 9+
        range = document.createRange() // Create a range (a range is a like the selection but invisible)
        range.selectNodeContents(contentEditableElement) // Select the entire contents of the element with the range
        range.collapse(false) // collapse the range to the end point. false means collapse to end rather than the start
        selection = window.getSelection() // get the selection object (allows you to change selection)
        selection.removeAllRanges() // remove any selections already made
        selection.addRange(range) // make the range you have just created the visible selection
      } else if (document.selection) {
        // IE 8 and lower
        range = document.body.createTextRange() // Create a range (a range is a like the selection but invisible)
        range.moveToElementText(contentEditableElement) // Select the entire contents of the element with the range
        range.collapse(false) // collapse the range to the end point. false means collapse to end rather than the start
        range.select() // Select the range (make it the visible selection
      }
    },
  },
}
</script>
