<template>
  <Portal v-if="showSearchbar" to="omnibox">
    <Transition name="placeholder" :appear="true">
      <GroupProvider>
        <MonitorTypeProvider
          :device-types="[
            $constants.SERVER,
            $constants.NETWORK,
            $constants.WIRELESS,
            $constants.OTHER,
            $constants.CLOUD,
            $constants.VIRTUALIZATION,
            $constants.SERVICE_CHECK,
          ]"
          ignore-available-filter
        >
          <OmniboxProvider>
            <div class="omnibox-container rounded" @click.self="hideSearchBar">
              <div class="relative h-full w-4/5" @click.self="hideSearchBar">
                <div
                  ref="searchContainerRef"
                  class="search-container rounded"
                  :class="{
                    'has-error': error,
                    'has-result':
                      (Boolean(searchContext) || searchHistory.length > 0) &&
                      !error,
                  }"
                >
                  <!-- <MButton
                  shape="circle"
                  variant="transparent"
                  :shadow="false"
                  class="absolute"
                  style="top: -25px; right:-5px"
                  size="small"
                  @click="hideSearchBar"
                >
                  <MIcon name="times" size="lg" class="text-neutral-light" />
                </MButton> -->
                  <Searchbar
                    :timeline.sync="timeline"
                    @search-context="searchContext = $event"
                    @enter-key="handleEnterKeyPressed"
                    @arrow-down="handleArrowDown"
                    @arrow-up="handleArrowUp"
                    @hide="toggleSearchBar"
                    @reset="reset"
                  />
                  <div
                    v-if="error"
                    class="flex items-center p-2 error-container bottom-rounded-corner"
                  >
                    <MIcon name="exclamation-triangle" size="lg" class="mr-1" />
                    <div style="white-space: pre-line">
                      {{ error.message }}
                    </div>
                  </div>
                  <div
                    v-else-if="searchHistory.length > 0"
                    ref="resultContainerRef"
                    class="flex flex-1 flex-col min-h-0 overflow-y-auto px-2 bottom-rounded-corner"
                  >
                    <SearchHistoryResult
                      v-for="(r, index) in searchHistory"
                      :key="r.key || r.title"
                      :result="r"
                      :selected="
                        activeIndex === index && hoverResult === undefined
                      "
                      @mouseenter="hoverResult = index"
                      @mouseleave="hoverResult = undefined"
                    />
                  </div>
                  <div
                    v-else-if="Boolean(searchContext) && !error"
                    class="bottom-rounded-corner flex-1 min-h-0"
                  >
                    <SearchResult
                      :context="searchContext"
                      :timeline="timeline"
                      @hide-search-bar="hideSearchBar"
                    />
                  </div>
                </div>
              </div>
            </div>
          </OmniboxProvider>
        </MonitorTypeProvider>
      </GroupProvider>
    </Transition>
  </Portal>
</template>

<script>
import Bus from '@utils/emitter'
import GroupProvider from '@components/data-provider/group-provider.vue'
import OmniboxProvider from './omnibox-provider.vue'
import SearchHistoryResult from './search-history-result.vue'
import Searchbar from './searchbar.vue'
import SearchResult from './search-result.vue'
import SearchContext from './search-context/search-context'
import MonitorTypeProvider from '../data-provider/monitor-type-provider.vue'

export default {
  name: 'OmniBox',
  components: {
    Searchbar,
    SearchHistoryResult,
    SearchResult,
    GroupProvider,
    OmniboxProvider,
    MonitorTypeProvider,
  },
  data() {
    return {
      error: undefined,
      searchContext: undefined,
      showSearchbar: false,
      searchHistory: [],
      activeIndex: 0,
      hoverResult: undefined,
      timeline: {
        selectedKey: '-1h',
      },
    }
  },
  watch: {
    searchContext(newValue) {
      if (newValue) {
        this.error = newValue.getError()
      }
    },
    showSearchbar(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.$emit('search-bar-visibility-change', newValue)
      }
    },
  },
  created() {
    Bus.$on('cmd:search', this.toggleSearchBar)

    this.$once('hook:beforeDestroy', () => {
      Bus.$off('cmd:search', this.toggleSearchBar)
    })
  },
  methods: {
    reset() {
      this.error = undefined
      this.searchContext = undefined
      this.timeline = {
        selectedKey: '-1h',
      }
      this.searchHistory = []
    },
    toggleSearchBar(e) {
      if (
        e &&
        this.$refs.searchContainerRef &&
        this.$refs.searchContainerRef.contains(e.target)
      ) {
        return
      }
      if (this.showSearchbar) {
        this.hideSearchBar()
      } else {
        this.showSearchbar = true
      }
      // this.$emit('search-bar-visibility-change', this.showSearchbar)
    },
    hideSearchBar() {
      this.showSearchbar = false
      this.reset()
      SearchContext.resetCounterCache()
      SearchContext.resetGroupCache()
      SearchContext.resetTagCache()
      SearchContext.resetLogSourcesCache()
      SearchContext.resetFlowSourcesCache()
      SearchContext.resetInstanceCache()
    },
    handleEnterKeyPressed() {
      if (this.activeIndex >= 0 && this.searchHistory.length > 0) {
        // @TODO history result selected.
      }
    },
    handleArrowUp() {
      if (this.searchHistory.length === 0) {
        return
      }
      const activeIndex = this.activeIndex + this.searchHistory.length - 1
      this.activeIndex = activeIndex % this.searchHistory.length
      this.showActiveIndexItemInView()
    },
    handleArrowDown() {
      if (this.searchHistory.length === 0) {
        return
      }
      const activeIndex = this.activeIndex + 1
      this.activeIndex = activeIndex % this.searchHistory.length
      this.showActiveIndexItemInView()
    },
    showActiveIndexItemInView() {
      const activeItem = Array.from(
        this.$refs.resultContainerRef.querySelectorAll(`.search-result`)
      )[this.activeIndex]
      if (activeItem) {
        activeItem.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
          inline: 'center',
        })
      }
    },
  },
}
</script>

<style lang="less">
@search-height: 40px;

.omnibox-container {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1053;
  display: flex;
  justify-content: center;
  background: var(--overlay-bg);
  backdrop-filter: blur(4px);

  .search-container {
    position: relative;
    top: 15%;
    display: flex;
    flex-direction: column;
    max-height: 65%;
    background: var(--page-background-color);
    border: 4px solid var(--border-color);
    border-radius: 7px;
    box-shadow: rgba(0, 0, 0, 0.24) 0 3px 8px;

    &.has-error {
      transform: translate3d(0, 0, 0);
      animation: shake 0.82s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
      backface-visibility: hidden;
      perspective: 1000px;
    }

    &.has-result {
      height: 65%;
    }

    .bottom-rounded-corner {
      border-bottom-right-radius: 4px;
      border-bottom-left-radius: 4px;
    }

    .error-container {
      color: white;
      background: var(--secondary-red);
    }

    .omnibox-searchbar {
      width: 100%;
      height: @search-height;
      padding-top: 5px;
      margin-left: 0.5rem;
      font-size: 1rem;

      // stylelint-disable-next-line
      .CodeMirror {
        height: 100%;
        font-size: 1rem;
        color: var(--page-text-color);
        background: var(--page-background-color);
        border: none;

        &-selected {
          background: var(--primary);
        }

        &-cursor {
          border-color: var(--page-text-color);
        }

        &-line {
          font-family: @font-family;
          font-size: inherit;
        }

        &-hscrollbar,
        &-vscrollbar,
        &-scrollbar-filler {
          display: none !important;
        }

        .cm-number {
          color: var(--page-text-color);
        }

        .cm-string {
          color: var(--page-text-color);
        }

        .cm-search-keyword {
          color: var(--primary);
        }

        .cm-filter-keyword {
          color: var(--secondary-green);
        }
      }

      &:focus {
        box-shadow: none;
      }

      b {
        font-weight: 500;
        color: var(--primary-alt);
      }

      i {
        color: var(--primary-alt);
      }

      &[placeholder]:empty::before {
        color: var(--neutral-light);
        content: attr(placeholder);
      }
    }

    .search-icon {
      font-size: 1.1rem;
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
    }

    // .custom-time-select {
    //   height: @search-height;
    // }

    .search-result {
      @apply py-1 cursor-pointer;

      &-title {
        font-size: 1rem;
        font-weight: 500;
      }

      &-description {
        font-size: 0.6rem;
        font-style: italic;
      }

      &.selected,
      &:hover {
        @apply -mx-2 px-2;

        color: white;
        background: var(--primary-alt);
      }
    }
  }
}

@keyframes shake {
  10%,
  90% {
    transform: translate3d(-1px, 0, 0);
  }

  20%,
  80% {
    transform: translate3d(2px, 0, 0);
  }

  30%,
  50%,
  70% {
    transform: translate3d(-4px, 0, 0);
  }

  40%,
  60% {
    transform: translate3d(4px, 0, 0);
  }
}

// stylelint-disable-next-line
.CodeMirror-hints {
  z-index: 999999999999;
  max-width: 700px;
  font-size: 0.8rem;
  background: var(--action-dropdown-backgroud);
  border: 1px solid var(--border-color);

  // stylelint-disable-next-line
  li.CodeMirror-hint {
    padding-top: 5px;
    padding-bottom: 5px;
    font-family: @font-family;
    color: var(--action-dropdown-text);

    &:hover {
      color: white;
      background: var(--primary);
    }
  }
  // stylelint-disable-next-line
  li.CodeMirror-hint-active {
    font-family: @font-family;
    color: white;
    background: var(--primary);
  }
}
</style>
