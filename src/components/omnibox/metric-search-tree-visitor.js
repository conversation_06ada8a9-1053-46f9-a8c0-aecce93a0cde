/* eslint-disable */
import antlr4 from 'antlr4'
import Trim from 'lodash/trim'
import MetricParserVisitorParent from '@src/parsers/metric/MetricParserVisitor'

import {
  COUNTER_WITH_TOP_SUGGESTION,
  WHERE_OR_RESULT_BY_SUGGESTION,
  RESULT_BY_SUGGESTION,
  RESULT_BY_VALUES_SUGGESTION,
  MONITOR_NAME_SUGGESTION,
  GROUP_SUGGESTION,
  TAG_SUGGESTION,
  IP_SUGGESTION,
  ENTITY_TYPE_SUGGESTION,
  WHERE_SUGGESTION,
  COUNTER_SUGGESTION,
  INSTANCE_SUGGESTION,
} from './search-context/search-constants'
import SearchContext from './search-context copy'

export default class MetricSearchTreeVisitor extends MetricParserVisitorParent {
  __rootContext = null

  constructor(searchContext, caretPosition, parser, category) {
    super()
    this.searchContext = searchContext
    this.caretPosition = caretPosition
    this.parser = parser
    this.searchContext.setCategory(category)

    this.entityTypeSuggestionMap = {
      monitor: MONITOR_NAME_SUGGESTION,
      group: GROUP_SUGGESTION,
      tag: TAG_SUGGESTION,
      ip: IP_SUGGESTION,
    }
  }

  // Visit a parse tree produced by MetricParser#query.
  visitQuery(ctx) {
    this.__rootContext = ctx
    const metricContext = this.visitChildren(ctx)

    this.__extractWidgetParams()

    return metricContext
  }

  // Visit a parse tree produced by MetricParser#statement.
  visitStatement(ctx) {
    if (this.isWithinCaretPosition(ctx)) {
      if (ctx.exception) {
        const expectedTokens = this.getExpectedTokens(ctx.exception)
        if (expectedTokens && expectedTokens.includes('CounterName')) {
          this.__setSuggestionInfo(COUNTER_WITH_TOP_SUGGESTION, null, {
            range: MetricSearchTreeVisitor.getRange(ctx),
            text: ctx.getText(),
            groupType: this.searchContext.getCategory(),
          })
        }
      }
    }
    if (
      ctx.exception &&
      ctx.exception instanceof antlr4.error.NoViableAltException
    ) {
      let text = ctx.getChild(ctx.getChildCount() - 1).getText() || ''
      let range = {
        start: ctx.getChild(ctx.getChildCount() - 1).getSymbol().start,
        end: ctx.getChild(ctx.getChildCount() - 1).getSymbol().stop + 1,
      }
      if (text === ' ') {
        range = this.__getNextTokenRange(ctx.getChild(ctx.getChildCount() - 1))
        if (this.searchContext.getCategory() === 'flow') {
          range.start -= 1
          range.stop -= 1
        }
      }
      this.__setSuggestionInfo(
        this.searchContext.getCategory() === 'flow'
          ? RESULT_BY_SUGGESTION
          : WHERE_OR_RESULT_BY_SUGGESTION,
        null,
        {
          range,
          text,
        }
      )
    }
    return this.visitChildren(ctx)
  }

  // Visit a parse tree produced by MetricParser#top.
  visitTop(ctx) {
    if (this.isWithinCaretPosition(ctx, true)) {
      if (ctx.exception) {
        const expectedTokens = this.getExpectedTokens(ctx.exception)
        if (expectedTokens && expectedTokens.includes('CounterName')) {
          this.__setSuggestionInfo(COUNTER_SUGGESTION, null, {
            range: MetricSearchTreeVisitor.getRange(ctx),
            text: ctx.getText() || '',
            groupType: this.searchContext.getCategory(),
          })
        } else {
          this.__setSuggestionInfo(COUNTER_WITH_TOP_SUGGESTION, ctx)
        }
      }
    }
    return this.visitChildren(ctx)
  }

  // Visit a parse tree produced by MetricParser#counter.
  visitCounter(ctx) {
    if (this.isWithinCaretPosition(ctx, true)) {
      this.__setSuggestionInfo(
        ctx.parentCtx.top() === null || this.isErrorNode(ctx.parentCtx.top())
          ? COUNTER_WITH_TOP_SUGGESTION
          : COUNTER_SUGGESTION,
        null,
        {
          range: MetricSearchTreeVisitor.getRange(
            this.isErrorNode(ctx.parentCtx.top()) ? ctx.parentCtx.top() : ctx
          ),
          text: this.isErrorNode(ctx.parentCtx.top())
            ? ctx.parentCtx.top().getText()
            : ctx.getText(),
          groupType: this.searchContext.getCategory(),
        }
      )
    }
    return this.visitChildren(ctx)
  }

  // Visit a parse tree produced by MetricParser#resultBy.
  visitResultBy(ctx) {
    if (this.isWithinCaretPosition(ctx, true)) {
      const range = MetricSearchTreeVisitor.getRange(ctx)
      this.__setSuggestionInfo(RESULT_BY_SUGGESTION, ctx, {
        ...(ctx.getText() === ''
          ? {
              range: {
                start: range.start + 2,
                stop: range.start + 2,
              },
            }
          : {}),
      })
    }
    return this.visitChildren(ctx)
  }

  // Visit a parse tree produced by MetricParser#where.
  visitWhere(ctx) {
    if (this.isWithinCaretPosition(ctx, true)) {
      const range = MetricSearchTreeVisitor.getRange(ctx)
      this.__setSuggestionInfo(WHERE_SUGGESTION, ctx, {
        ...(ctx.getText() === ''
          ? {
              range: {
                start: range.start + 2,
                stop: range.start + 2,
              },
            }
          : {}),
      })
    }
    return this.visitChildren(ctx)
  }

  visitWhereOption(ctx) {
    if (this.isWithinCaretPosition(ctx, true)) {
      const range = MetricSearchTreeVisitor.getRange(ctx)
      this.__setSuggestionInfo(ENTITY_TYPE_SUGGESTION, ctx)
    }
    return this.visitChildren(ctx)
  }

  // Visit a parse tree produced by MetricParser#value.
  visitValue(ctx) {
    if (this.isWithinCaretPosition(ctx, true)) {
      const whereOption = ctx.parentCtx.whereOption().getText().toLowerCase()
      let type
      if (whereOption === 'monitor') {
        type = MONITOR_NAME_SUGGESTION
      } else if (whereOption === 'group') {
        type = GROUP_SUGGESTION
      } else if (whereOption === 'tag') {
        type = TAG_SUGGESTION
      } else if (whereOption === 'ip') {
        type = IP_SUGGESTION
      } else {
        type = INSTANCE_SUGGESTION
      }
      if (type) {
        const range = MetricSearchTreeVisitor.getRange(ctx)
        this.__setSuggestionInfo(type, ctx)
      }
    }
    return this.visitChildren(ctx)
  }

  // Visit a parse tree produced by MetricParser#resultByOptions.
  visitResultByOption(ctx) {
    if (this.isWithinCaretPosition(ctx, true)) {
      this.__setSuggestionInfo(RESULT_BY_VALUES_SUGGESTION, ctx)
    }
    return this.visitChildren(ctx)
  }

  __setSuggestionInfo(type, node, additionalObjectProps = {}) {
    const obj = {
      type,
    }

    if (node) {
      obj.text = node.getText()
      // obj.range = this.isTerminalNode(node)
      //   ? MetricSearchTreeVisitor.getTerminalNodeRange(node)
      //   : MetricSearchTreeVisitor.getRange(node)
      obj.range = MetricSearchTreeVisitor.getRange(node)
    }
    Object.keys(additionalObjectProps).forEach((key) => {
      obj[key] = additionalObjectProps[key]
    })
    this.searchContext.setSuggestionType(obj)
  }

  __extractWidgetParams() {
    // if (this.parser._syntaxErrors > 0) {
    //   return
    // }
    this.__extractTopn()
    this.__extractCounter()
    this.__extractResultBy()
    this.__extractWhere()
    if (/.+"$/.test(this.__rootContext.getText())) {
      if (
        this.caretPosition >=
        this.__rootContext.getText().replace('<EOF>', '').length
      ) {
        this.__setSuggestionInfo(null)
      }
    }
  }

  __extractTopn() {
    if (
      this.__rootContext.statement() &&
      this.__rootContext.statement().top()
    ) {
      const topNode =
        this.__rootContext.statement().top().Top() ||
        this.__rootContext.statement().top().Last()
      if (
        topNode &&
        this.isErrorNode(topNode) === false &&
        topNode.parentCtx.DecimalLiteral()
      ) {
        this.searchContext.setTopNSorting(
          ['top', 'highest'].includes(topNode.getText().toLowerCase())
            ? 'desc'
            : 'asc',
          parseInt(topNode.parentCtx.DecimalLiteral().getText())
        )
      }
    }
  }

  __extractCounter() {
    if (
      this.__rootContext.statement() &&
      this.__rootContext.statement().counter()
    ) {
      const counterNode = this.__rootContext.statement().counter().CounterName()
      if (counterNode && this.isErrorNode(counterNode) === false) {
        this.searchContext.setCounter(counterNode.getText())
        if (this.searchContext.getCategory() === 'log') {
          this.searchContext.setAggr('count')
        } else if (this.searchContext.getCategory() === 'flow') {
          this.searchContext.setAggr('sum')
        } else {
          this.searchContext.setAggr('avg')
        }
      }
    }
  }

  __extractResultBy() {
    if (
      this.__rootContext.statement() &&
      this.__rootContext.statement().resultBy()
    ) {
      const resultByNode = this.__rootContext
        .statement()
        .resultBy()
        .resultByOption()
      if (resultByNode && this.isErrorNode(resultByNode) === false) {
        this.searchContext.setResultBy(resultByNode.getText())
      }
    }
  }

  __extractWhere() {
    if (
      this.__rootContext.statement() &&
      this.__rootContext.statement().where()
    ) {
      const whereOptionNode = this.__rootContext
        .statement()
        .where()
        .whereOption()
      const whereValueNode = this.__rootContext.statement().where().value()
      if (
        whereOptionNode &&
        this.isErrorNode(whereOptionNode) === false &&
        this.isErrorNode(whereValueNode) === false
      ) {
        if (
          whereOptionNode.getText() ===
          this.searchContext.getCounter().rawCounter.split('~')[0]
        ) {
          const whereValueNodeText = Trim(whereValueNode.getText(), '"')
          if (whereValueNodeText.length) {
            this.searchContext.setInstance(
              whereOptionNode.getText().toLowerCase(),
              whereValueNodeText
            )
          } else {
            this.searchContext.setInstance(null, null)
          }
        } else {
          this.searchContext.setSource(
            whereOptionNode.getText().toLowerCase(),
            Trim(whereValueNode.getText(), '"')
          )
        }
      }
    }
  }

  __getNextTokenRange(ctx) {
    return {
      start: ctx.getSymbol().stop + 1,
      end: ctx.getSymbol().stop + 1,
    }
  }

  getExpectedTokens(exception) {
    if (exception instanceof antlr4.error.InputMismatchException) {
      const tokens = exception
        .getExpectedTokens(this.parser.state, this.parser._ctx)
        .toString(this.parser.literalNames, this.parser.symbolicNames)
      return tokens.replace(/[\{\} ]/g, '').split(',')
    }
    return null
  }

  isTerminalNode(node) {
    return node instanceof antlr4.tree.TerminalNode
  }

  isErrorNode(node) {
    return (
      node && (node instanceof antlr4.tree.ErrorNode || Boolean(node.exception))
    )
  }

  isWithinCaretPosition(node, addPlusInTerminal = false) {
    // const range = this.isTerminalNode(node)
    //   ? MetricSearchTreeVisitor.getTerminalNodeRange(node)
    // : MetricSearchTreeVisitor.getRange(node)
    const range = MetricSearchTreeVisitor.getRange(node)
    if (addPlusInTerminal) {
      range.end += 1
    }
    return range.start <= this.caretPosition && this.caretPosition <= range.end
  }

  static getTerminalNodeRange(node) {
    return {
      start: node.getSymbol().start,
      end: node.getSymbol().stop,
    }
  }

  static getRange(node) {
    return {
      start: node.start.start,
      end: Math.max(
        (node.stop !== null && node.stop !== void 0 ? node.stop : node.start)
          .stop,
        node.start.start
      ),
    }
  }
}
