<template>
  <div>
    <div v-if="sourceTypeSuggestion" class="flex items-center py-2 px-2">
      <MonitorType :type="value.displayText" disable-tooltip />
      <span class="ml-2">
        {{ value.displayText }}
      </span>
    </div>
    <template v-else>
      <div
        v-if="suggestionType.type !== 'InstantSuggestion'"
        class="flex items-center justify-between py-2 px-2"
        style="width: 100%"
      >
        <div class="flex flex-col flex-1 min-w-0">
          <div class="text-ellipsis font-500">
            {{
              value.monitor[
                suggestionType.type === 'SourceValueResult' ? 'name' : 'ip'
              ]
            }}
          </div>
          <small
            v-if="
              value.monitor[
                suggestionType.type === 'SourceValueResult' ? 'ip' : 'name'
              ] &&
              value.monitor[
                suggestionType.type === 'SourceValueResult' ? 'ip' : 'name'
              ] !==
                value.monitor[
                  suggestionType.type === 'SourceValueResult' ? 'name' : 'ip'
                ]
            "
            class="text-ellipsis italic"
          >
            {{
              value.monitor[
                suggestionType.type === 'SourceValueResult' ? 'ip' : 'name'
              ]
            }}
          </small>
        </div>
        <div style="flex-shrink: 0" class="mx-2 inline-flex">
          <MonitorType
            v-if="value.monitor.isAgent"
            width="15px"
            :type="$constants.AGENT"
            disable-tooltip
            class="mr-1"
          />
          <MonitorType :type="value.monitor.type" disable-tooltip />
        </div>
      </div>
      <div
        v-else
        class="flex items-center justify-between py-2 px-2"
        style="width: 100%"
      >
        <div class="flex flex-col flex-1 min-w-0">
          <div class="text-ellipsis font-500">
            {{ value.text }}
          </div>
          <small class="text-ellipsis italic">
            {{ value.monitor }}
          </small>
        </div>
        <div v-if="value.monitor" style="flex-shrink: 0" class="mx-2">
          <MonitorType :type="value.monitorType" disable-tooltip />
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import MonitorType from '@components/monitor-type.vue'

export default {
  name: 'MonitorSuggestionHint',
  components: {
    MonitorType,
  },
  props: {
    sourceTypeSuggestion: {
      type: Boolean,
      default: false,
    },
    value: {
      type: Object,
      required: true,
    },
    suggestionType: {
      type: Object,
      required: true,
    },
  },
}
</script>
