<template>
  <span ref="triggerRef" class="flex flex-1 min-w-0 overflow-hidden">
    <component
      :is="usePopover ? 'MPopover' : 'MPopper'"
      :open="isOpen"
      controlled
      :overlay-class-name="`${overlayClassName || ''} ${additionalClass || ''}`"
      :overlay-style="appliedOverlayStyle"
      :disabled="disabled"
      v-bind="
        usePopover
          ? {
              placement: 'bottomRight',
              transitionName: 'none',
              ...attrs,
            }
          : {
              placement,
              ...attrs,
            }
      "
      @hide="handleHide"
      @show="handleShow"
      @visibleChange="handleVisibilityChange"
    >
      <div class="flex flex-col h-100">
        <div class="flex h-100 flex-col">
          <template v-if="searchable">
            <div class="my-2 px-2 flex justify-between items-center">
              <MInput
                ref="searchBox"
                v-model="searchQuery"
                class="dropdown-search"
                placeholder="Search"
                data-cy="dropdown-search-input"
                :data-cy-id="$attrs.id"
                @change="searchQuery = $event"
                @click.stop
              >
                <template v-slot:prefix>
                  <MIcon name="search" class="excluded-header-icon" />
                </template>
                <template v-if="searchQuery" v-slot:suffix>
                  <MIcon
                    name="times-circle"
                    class="cursor-pointer excluded-header-icon"
                    @click="searchQuery = ''"
                  />
                </template>
              </MInput>

              <div v-if="canUserAddOptions" class="p-1 rounded bordered ml-2">
                <MIcon
                  name="plus"
                  size="lg"
                  class="text-primary cursor-pointer"
                  @click="isAdding = true"
                />
              </div>
            </div>
          </template>

          <template v-if="isAdding">
            <div class="border-bot mb-1 mx-2 flex justify-between items-center">
              <FlotoFormItem
                v-model="addingItem"
                label=" "
                rule="required"
                :placeholder="`Add ${addLabel}`"
                class="no-border-input ml-1"
              />

              <div class="flex">
                <MIcon
                  name="check"
                  size="lg"
                  class="text-secondary-green ml-2 cursor-pointer"
                  @click="addItem"
                />
                <MIcon
                  name="times"
                  size="lg"
                  class="text-secondary-red ml-2 cursor-pointer"
                  @click="cancelAdding"
                />
              </div>
            </div>
          </template>
          <div
            v-if="multiple && (currentOptions || []).length && allowSelectAll"
            class="w-full"
          >
            <span style="padding: 5px 12px">
              <MCheckbox
                :checked="isAllOptionsSelected"
                @change="selectAllOptions"
                >Select All
              </MCheckbox>
            </span>

            <MDivider class="mb-0 mt-2" />
          </div>

          <slot :searchQuery="searchQuery">
            <MMenu
              v-if="currentOptions.length"
              class="flex flex-col virtual-scrollable-dropdown-menu"
              :class="menuClass"
            >
              <RecycleScroller
                ref="scrollerRef"
                :items="currentOptions"
                :item-size="itemSize"
                key-field="key"
              >
                <template v-slot="{ item, index }">
                  <MMenuItem
                    :style="{ height: `${itemSize}px !important` }"
                    :class="{
                      'scroll-dropdown-menu-item': true,
                      [menuItemClass]: true,
                      [menuItemSelectedClass]: index === currentIndex,
                      'value-active-item': value === item.key,
                    }"
                    @click="selectItem(item, $event)"
                  >
                    <slot
                      name="menu-item"
                      :item="item"
                      :selectItem="selectItem"
                    >
                      <div
                        :id="
                          item.id ||
                          (item.key || item.value || '')
                            .toString()
                            .replace(/\s/g, '-')
                        "
                        class="text-ellipsis flex items-center cursor-pointer"
                        @click.stop="selectItem(item)"
                      >
                        <MCheckbox
                          v-if="multiple"
                          :checked="
                            (value || []).indexOf(
                              item.key || item.id || item.value
                            ) >= 0
                          "
                          :disabled="item.disable"
                          @click.stop="selectItem(item)"
                        />
                        <slot name="before-menu-text" :item="item" />
                        <span
                          :title="item.text || item.name || item.label"
                          class="text-ellipsis"
                          :class="{ 'ml-2': !multiple }"
                        >
                          {{ item.text || item.name || item.label }}
                        </span>
                        <slot name="after-menu-text" :item="item" />
                      </div>
                    </slot>
                  </MMenuItem>
                </template>
                <template v-slot:after>
                  <div
                    v-if="showHelp"
                    class="text-neutral-light border-top"
                    style="padding: 12px"
                  >
                    <slot name="helpbox"> Please refine your search </slot>
                  </div>
                </template>
              </RecycleScroller>
            </MMenu>
            <template v-if="currentOptions.length <= 0">
              <div class="px-4 py-6 flex flex-col items-center justify-center">
                <DropdownNoDataSvg
                  v-if="theme === 'white'"
                  style="max-width: 85px"
                />
                <DropdownNoDataBlackSvg v-else style="max-width: 85px" />
              </div>
            </template>
          </slot>

          <slot name="after-menu"> </slot>

          <template v-if="multiple && !removeClearBtn">
            <MDivider class="my-1 border-bot" />
            <div class="flex justify-end px-2 pb-1" @click="handleChange([])">
              <MButton variant="default" :disabled="(value || []).length === 0">
                <MIcon name="times" /> Clear
              </MButton>
            </div>
          </template>
        </div>
      </div>

      <template v-slot:trigger>
        <slot
          name="trigger"
          :hide="handleHide"
          :show="handleShow"
          :toggle="toggleDropdown"
          :currentItem="selectedItem"
          :change="handleChange"
          :isOpen="isOpen"
        >
          <SingleTrigger
            v-if="!multiple"
            :selected-item="selectedItem"
            :allow-clear="allowClear"
            :disabled="disabled"
            :placeholder="placeholder"
            :text-only="textOnly"
            :as-input="asInput"
            :is-open="isOpen"
            :use-large-padding="$attrs['use-large-padding']"
            @click="toggleDropdown"
            @change="handleChange"
          >
            <template
              v-if="$scopedSlots['before-menu-text']"
              v-slot:prefix-text="{ item }"
            >
              <slot name="before-menu-text" :item="item" />
            </template>
          </SingleTrigger>
          <MultipleTrigger
            v-else
            :allow-clear="allowClear"
            :is-open="isOpen"
            :selected-items="value"
            :disabled="disabled"
            :use-popover="$attrs['use-popover']"
            :placeholder="placeholder"
            :options="allOptions"
            :wrap="wrap"
            @click="toggleDropdown"
            @change="handleChange"
          />
        </slot>
      </template>
    </component>
  </span>
</template>

<script>
import Trim from 'lodash/trim'
import FindIndex from 'lodash/findIndex'
import { arrayWorker } from '@/src/workers'
import DropdownNoDataSvg from '@assets/icons/dropdown-no-data.svg'
import DropdownNoDataBlackSvg from '@assets/icons/dropdown-no-data-black.svg'
import { UserPreferenceComputed } from '@state/modules/user-preference'
import SingleTrigger from './dropdown-trigger/single-trigger.vue'
import MultipleTrigger from './dropdown-trigger/multiple-trigger.vue'

export default {
  name: 'FlotoDropdownPicker',
  components: {
    SingleTrigger,
    MultipleTrigger,
    DropdownNoDataSvg,
    DropdownNoDataBlackSvg,
  },
  inheritAttrs: false,
  model: {
    event: 'change',
  },
  props: {
    maxAllowedSelection: { type: Number, default: 0 },
    allowClear: { type: Boolean, default: false },
    placement: { type: String, default: 'bottom-start' },
    // eslint-disable-next-line
    searchable: { type: Boolean, default: true },
    textOnly: { type: Boolean, default: false },
    multiple: { type: Boolean, default: false },
    allowSelectAll: { type: Boolean, default: false },
    fixedPosition: { type: Boolean, default: false },
    usePopover: { type: Boolean, default: false },
    externalOptions: {
      type: Array,
      default() {
        return []
      },
    },
    overlayStyle: {
      type: Object,
      default() {
        return {}
      },
    },
    disabledOptions: {
      type: Array,
      default() {
        return []
      },
    },
    // eslint-disable-next-line
    asInput: { type: Boolean, default: true },
    showHelp: { type: Boolean, default: false },
    value: {
      type: [Number, String, Object, Array, Boolean],
      default: undefined,
    },
    disabled: { type: Boolean, default: false },
    placeholder: { type: String, default: 'Select' },
    // eslint-disable-next-line
    wrap: { type: Boolean, default: true },
    avoidKeyboardNavigation: { type: Boolean, default: false },
    itemSize: { type: Number, default: 32 },
    overlayClassName: { type: String, default: 'picker-overlay' },
    removeClearBtn: { type: Boolean, default: false },
    minWidth: { type: Number, default: 0 },
    hideAllDropdownOptions: {
      type: Boolean,
      default: false,
    },
    maxValues: {
      type: Number,
      default: 0,
    },
    itemsToShow: {
      type: Number,
      default: 10,
    },
    canUserAddOptions: { type: Boolean, default: false },
    addLabel: {
      type: String,
      default: () => {
        return ''
      },
    },
  },
  data() {
    // @TODO make this classes dynamic somehow
    this.menuClass = 'ant-dropdown-menu'
    this.menuItemClass = 'ant-dropdown-menu-item ant-menu-item'
    this.menuItemSelectedClass = 'ant-dropdown-menu-item-selected'
    let initialIndex
    if (this.avoidKeyboardNavigation) {
      initialIndex = undefined
    } else {
      initialIndex = FindIndex(this.options, { key: this.value })
    }
    return {
      isOpen: false,
      searchQuery: '',
      searchedOptions: null,
      currentIndex: initialIndex === -1 ? 0 : initialIndex,
      filteredOptions: undefined,
      allOptions: [],
      currentOptions: [],
      width: undefined,
      additionalClass: null,
      isAdding: false,
      addingItem: undefined,
    }
  },
  computed: {
    ...UserPreferenceComputed,
    appliedOverlayStyle() {
      return {
        ...this.overlayStyle,
        ...(this.width ? { width: `${this.width}px` } : {}),
      }
    },
    selectedItem() {
      if (this.multiple) {
        return undefined
      }
      const options = (this.$attrs.options || this.options || []).concat(
        this.externalOptions
      )
      return options.find(
        (p) => p.key === this.value || p.key === parseInt(this.value)
      )
    },
    attrs() {
      const { options, ...attrs } = this.$attrs
      return attrs
    },
    isAllOptionsSelected() {
      if (this.multiple && this.allowSelectAll) {
        return this.allOptions?.length === this.value?.length
      } else {
        return false
      }
    },
  },
  watch: {
    searchQuery: {
      handler(newValue, oldValue) {
        if (newValue !== oldValue) {
          this.applyFilter()
          this.$nextTick(() => this.$emit('search', newValue))
        }
      },
    },
    options: 'calculateAllOptions',
    '$attrs.options': 'calculateAllOptions',
    disabledOptions: 'calculateAllOptions',
  },
  created() {
    this.calculateAllOptions()
  },
  mounted() {
    if (this.$refs.triggerRef) {
      if (this.$refs.triggerRef.closest('.ant-modal')) {
        this.additionalClass = 'add-z-index'
      }
    }
  },
  methods: {
    handleVisibilityChange(e) {
      if (this.usePopover && e) {
        this.handleShow()
      }
      if (this.usePopover && !e) {
        this.handleHide()
      }
    },
    async calculateAllOptions() {
      let options = (this.$attrs.options || this.options || []).concat(
        this.externalOptions
      )

      if (this.disabledOptions.length) {
        let currentOptions = options
        if (Trim(this.searchQuery)) {
          currentOptions = await arrayWorker.search(
            currentOptions,
            this.searchQuery,
            ['text', 'name', 'label']
          )
        }
        currentOptions = await arrayWorker.excludeFromList(
          currentOptions,
          this.disabledOptions,
          'key'
        )

        this.currentOptions = Object.freeze(
          this.searchable && this.hideAllDropdownOptions
            ? currentOptions.slice(0, this.itemsToShow)
            : currentOptions
        )
      } else {
        let currentOptions = options
        if (Trim(this.searchQuery)) {
          currentOptions = await arrayWorker.search(
            currentOptions,
            this.searchQuery,
            ['text', 'name', 'label']
          )
        }
        this.currentOptions = Object.freeze(
          this.searchable && this.hideAllDropdownOptions
            ? currentOptions.slice(0, this.itemsToShow)
            : currentOptions
        )
      }
      this.allOptions = Object.freeze(options)
    },
    async applyFilter() {
      let filteredOptions
      if (this.searchQuery) {
        filteredOptions = await arrayWorker.search(
          this.allOptions,
          this.searchQuery,
          ['text', 'name', 'label']
        )
      }
      const currentOptions = await arrayWorker.excludeFromList(
        filteredOptions || this.allOptions,
        this.disabledOptions,
        'key'
      )
      this.currentOptions = Object.freeze(
        this.searchable && this.hideAllDropdownOptions
          ? currentOptions.slice(0, 10)
          : currentOptions
      )
    },
    handleChange(value) {
      this.$emit(
        'change',
        this.multiple
          ? this.maxValues > 0
            ? value.slice(value.length - this.maxValues)
            : value
          : value && value.key
      )
      if (!this.multiple) {
        this.handleHide()
      }
    },
    handleHide($event) {
      this.$emit('hide', $event)
      this.unbindEvents()
      this.isOpen = false
      this.cancelAdding()
    },
    handleShow($event) {
      if (this.disabled) {
        return
      }
      if (this.asInput) {
        this.width = Math.max(this.$el.offsetWidth, this.minWidth)
      } else {
        this.width = Math.max(200, this.minWidth)
      }
      this.resetCurrentIndex()
      this.bindEvents()
      this.$emit('show', $event)
      this.isOpen = true
      this.searchQuery = ''
      setTimeout(() => {
        this.$refs.searchBox && this.$refs.searchBox.focus()
      }, 100)
    },
    bindEvents() {
      if (this.avoidKeyboardNavigation) {
        return
      }
      window.addEventListener('keydown', this.handleDocumentEvent)
    },
    unbindEvents() {
      if (this.avoidKeyboardNavigation) {
        return
      }
      window.removeEventListener('keydown', this.handleDocumentEvent)
    },
    resetCurrentIndex() {
      if (this.avoidKeyboardNavigation) {
        return
      }
      const initialIndex = FindIndex(this.currentOptions, { key: this.value })
      this.currentIndex = initialIndex === -1 ? 0 : initialIndex
      this.$emit('active-item-index-change', this.currentIndex)
    },
    handleDocumentEvent(e) {
      if (e.key === 'ArrowUp') {
        this.upHandler(e)
      }
      if (e.key === 'ArrowDown') {
        e.preventDefault()
        this.downHandler(e)
      }
      if (e.key === 'Enter') {
        this.enterHandler(e)
      }
      if (e.key === 'Escape') {
        this.handleHide()
      }
    },
    toggleDropdown() {
      if (this.isDropdownOpen) {
        this.handleHide()
      } else {
        this.handleShow()
      }
    },
    upHandler(e) {
      e.preventDefault()
      e.stopPropagation()
      this.currentIndex =
        (this.currentIndex + this.currentOptions.length - 1) %
        this.currentOptions.length
      this.$emit('active-item-index-change', this.currentIndex)
      this.scrollActiveItemToView()
    },
    downHandler(e) {
      e.preventDefault()
      e.stopPropagation()
      this.currentIndex = (this.currentIndex + 1) % this.currentOptions.length
      this.$emit('active-item-index-change', this.currentIndex)
      this.scrollActiveItemToView()
      return true
    },
    enterHandler(e) {
      e.preventDefault()
      e.stopPropagation()
      // e.stopImmediatePropagation()
      if (this.currentOptions.length) {
        // here the item is selected
        this.selectItem(this.currentOptions[this.currentIndex])
      }
    },
    scrollActiveItemToView() {
      if (this.$refs.scrollerRef) {
        this.$refs.scrollerRef.scrollToItem(this.currentIndex)
      }
    },
    selectItem(item, $event) {
      if ($event && $event.domEvent) {
        $event.domEvent.stopPropagation()
      }
      if (this.multiple) {
        if (item.disable) {
          return
        }
        if ((this.value || []).indexOf(item.key) >= 0) {
          this.handleChange((this.value || []).filter((i) => i !== item.key))
        } else {
          this.handleChange([...(this.value || []), item.key])
        }
      } else {
        this.handleChange(item)
        this.handleHide()
      }
    },
    addItem(data) {
      if (this.addingItem !== undefined && this.addingItem !== '') {
        this.$emit('add', this.addingItem)
        this.isAdding = false
        this.addingItem = undefined
      }
    },
    cancelAdding() {
      this.isAdding = false
      this.addingItem = undefined
    },
    selectAllOptions(event) {
      this.$emit(
        'change',
        event ? this.allOptions.map((option) => option.key) : []
      )
    },
  },
}
</script>

<style lang="less" scoped>
.dropdown-with-colors {
  .dropdown-item {
    @apply flex items-center cursor-pointer;
  }
}

.scroll-dropdown-menu-item {
  color: inherit;
  &:hover,
  &.value-active-item,
  &.@{ant-prefix}-dropdown-menu-item-selected {
    font-weight: 500;
    color: var(--left-menu-text-color-hover) !important;
    background: var(--left-menu-hover-bg) !important;

    &.value-active-item {
      font-weight: bold;
    }
  }
}
</style>

<style lang="less">
.scroll-dropdown-menu-item {
  a {
    color: inherit;
  }
}

.no-border-input {
  .ant-input {
    border: none !important;
  }

  .ant-form-item-label {
    display: none !important;
  }

  .ant-form-item {
    padding: 0;
    margin: 0;
  }
}
</style>
