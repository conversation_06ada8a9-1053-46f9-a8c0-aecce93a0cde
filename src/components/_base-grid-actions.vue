<template>
  <MPopover
    v-if="filteredActions.length"
    :key="resource.id"
    ref="dropdown"
    transition-name="slide-up"
    :get-popup-container="getRootPopupContainer"
    placement="bottomRight"
    :overlay-class-name="overlayClassName"
  >
    <template v-slot:trigger="{ toggle }">
      <slot name="trigger" :toggle="toggle">
        <a
          :class="textClassName"
          class="px-4 py-2"
          data-cy="grid-action"
          @click="toggle"
        >
          <MIcon name="ellipsis-v" class="excluded-header-icon" />
        </a>
      </slot>
    </template>
    <!-- <template v-slot:menu> -->
    <MMenu id="action-dropdown-id" class="action-dropdown ant-dropdown-menu">
      <template v-for="item in filteredActions">
        <MMenuItem
          v-if="item.type !== 'divider'"
          :key="item.key"
          class="ant-dropdown-menu-item"
        >
          <a :id="item.key" href="javascript:;" @click="handleAction(item)">
            <span
              class="flex items-center"
              :class="{
                'text-secondary-red': item.isDanger || item.redAction,
              }"
            >
              <MIcon v-if="item.icon" :name="item.icon" class="mr-2" />
              {{ item.name }}
            </span>
          </a>
        </MMenuItem>
        <MMenuDivider
          v-if="item.type === 'divider'"
          :key="item.key"
          class="ant-dropdown-menu-item-divider"
        />
      </template>
    </MMenu>
    <!-- </template> -->
  </MPopover>
</template>

<script>
import { authComputed } from '@state/modules/auth'
import Filter from 'lodash/filter'

export default {
  name: 'FlotoGridActions',
  inject: { triggerDeleteConfirmModal: { default: null } },
  props: {
    actions: {
      type: [Array, Promise],
      default() {
        return []
      },
    },
    resource: { type: Object, required: true },
    createPermissionKeys: {
      type: Array,
      default() {
        return ['create']
      },
    },
    editPermissionKeys: {
      type: Array,
      default() {
        return ['edit']
      },
    },
    deletePermissionKeys: {
      type: Array,
      default() {
        return ['delete']
      },
    },
    createPermissionName: { type: String, default: undefined },
    editPermissionName: { type: String, default: undefined },
    deletePermissionName: { type: String, default: undefined },
    getPopupContainer: { type: Function, default: undefined },
    overlayClassName: { type: String, default: 'picker-action-dropdown' },
    textClassName: { type: String, default: 'text-neutral-light' },
    useDelayedHide: { type: Boolean, default: false },
  },
  data() {
    if (this.actions instanceof Promise) {
      this.actions.then((actions) => (this.internalActions = actions))
    }
    return {
      internalActions: this.actions instanceof Promise ? [] : this.actions,
    }
  },
  computed: {
    ...authComputed,
    filteredActions() {
      let actions = this.internalActions
      const createPermission = this.createPermissionName
      const editPermission = this.editPermissionName
      const deletePermission = this.deletePermissionName
      if (createPermission) {
        const createKey = this.createPermissionKeys
        const hasCreatePermission = this.hasPermission(createPermission)
        actions = Filter(actions, (action) => {
          return hasCreatePermission
            ? true
            : createKey.indexOf(action.key) === -1
        })
      }
      if (editPermission) {
        const editKey = this.editPermissionKeys
        const hasEditPermission = this.hasPermission(editPermission)
        actions = Filter(actions, (action) => {
          return hasEditPermission ? true : editKey.indexOf(action.key) === -1
        })
      }
      if (deletePermission) {
        const deleteKey = this.deletePermissionKeys
        const hasDeletePermission = this.hasPermission(deletePermission)
        actions = Filter(actions, (action) => {
          return hasDeletePermission
            ? true
            : deleteKey.indexOf(action.key) === -1
        })
      }
      return actions
    },
  },
  watch: {
    actions(newValue, oldValue) {
      if (newValue !== oldValue) {
        if (this.actions instanceof Promise) {
          this.actions.then((actions) => (this.internalActions = actions))
        } else {
          this.internalActions = this.actions
        }
      }
    },
  },
  methods: {
    getRootPopupContainer() {
      if (this.getPopupContainer) {
        return this.getPopupContainer()
      }
      if (!this.$el) {
        return undefined
      }
      return this.$el.closest('.k-grid-content') || this.$el.closest('.__panel')
    },
    hideAction() {
      if (this.$refs.dropdown) {
        this.$refs.dropdown.hide()
      }
    },
    handleAction(action) {
      if (this.useDelayedHide) {
        setTimeout(() => {
          this.$refs.dropdown.hide()
        }, 400)
      } else {
        this.$refs.dropdown.hide()
      }
      if (action.key.toLowerCase() === 'delete' && action.isDanger) {
        if (this.triggerDeleteConfirmModal) {
          setTimeout(() => {
            this.$emit('selected', action)
            this.triggerDeleteConfirmModal(this.resource)
          }, 400)
          return
        }
      }
      if (this.useDelayedHide) {
        this.$emit('selected', action)
        this.$emit(action.key, action)
      } else {
        setTimeout(() => {
          this.$emit('selected', action)
          this.$emit(action.key, action)
        })
      }
    },
  },
}
</script>
