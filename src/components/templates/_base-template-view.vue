<template>
  <div
    class="flex flex-col min-h-0 flex-1"
    :class="{ 'bg-neutral-lightest': !disableShadow }"
  >
    <FlotoContentLoader :loading="loading">
      <div class="flex-1 min-h-0 flex flex-col">
        <Widgets
          ref="widgetsRef"
          :widgets="template.widgets"
          :dashboard-id="template.id"
          :monitor="monitor"
          for-template
          :for-monitor-template="forMonitorTemplate"
          :for-group-template="forGroupTemplate"
          :dashboard-style="template.style"
          :grid-row-height="gridRowHeight"
          :disabled="!canAddWidgets"
          :is-custom-template="canAddWidgets"
          :allow-create="canAddWidgets"
          :params="widgetParams"
          v-bind="$attrs"
          @drilldown="$emit('drilldown', $event)"
          @force-drill-down="$emit('force-drill-down', $event)"
          @change="handleWidgetsUpdated"
          @add-widget="handleAddNewWidget"
        />
      </div>
    </FlotoContentLoader>
  </div>
</template>

<script>
import Bus from '@utils/emitter'
import Widgets from '../widgets/widgets.vue'
import { getTemplateApi, updateTemplateApi } from './template-api'
import { authComputed } from '@state/modules/auth'

export default {
  name: 'TemplateView',
  components: {
    Widgets,
  },
  inheritAttrs: false,
  props: {
    templateId: { type: Number, default: undefined },
    templateType: { type: String, default: undefined },
    canAddWidgets: { type: Boolean, default: false },
    forMonitorTemplate: {
      type: Boolean,
      default: false,
    },
    forGroupTemplate: {
      type: Boolean,
      default: false,
    },
    widgetParams: {
      type: Object,
      default: undefined,
    },
    disableShadow: {
      type: Boolean,
      default: false,
    },
    gridRowHeight: {
      type: Number,
      default: 120,
    },
    monitor: {
      type: Object,
      default: undefined,
    },
    eventTabs: {
      type: Array,
      default: undefined,
    },
  },
  data() {
    return {
      data: [],
      template: {},
      loading: true,
      containerScrollTop: 0,
    }
  },
  computed: {
    ...authComputed,
  },
  watch: {
    monitor(newValue, oldValue) {
      if (newValue && newValue.id !== (oldValue || {}).id) {
        this.startMonitorTemplateWidgetsData()
      }
    },
  },
  created() {
    if (!this.templateId) {
      throw new Error('Template id not found')
    }
    this.getTemplate(this.templateId, this.templateType)

    Bus.$on('socket:connected', this.sendMonitorTemplateOpenEvent)
    this.$once('hook:beforeDestroy', () => {
      Bus.$off('socket:connected', this.sendMonitorTemplateOpenEvent)

      clearInterval(this._heartbeatInterval)
    })
  },
  beforeDestroy() {
    this.$emit('leave')
  },
  methods: {
    handleWidgetsUpdated(widgets) {
      if (!this.canAddWidgets) {
        return
      }
      const data = {
        ...this.template,
        widgets,
      }
      updateTemplateApi(data, false).then(() => {
        this.template = Object.freeze(data)
        // this.updateDashboard(data)
      })
    },
    handleAddNewWidget(widget) {
      if (!this.canAddWidgets) {
        return
      }
      const data = {
        ...this.template,
        widgets: [...this.template.widgets, widget],
      }
      updateTemplateApi(data).then(() => {
        this.template = Object.freeze(data)
        // this.updateDashboard(data)
      })
      this.$nextTick(() => {
        if (this.$refs.widgetsRef) {
          this.$refs.widgetsRef.scrollToBottom()
        }
      })
    },
    getScrollContainer() {
      if (this.$refs.widgetsRef) {
        return this.$refs.widgetsRef.getScrollContainer()
      }
    },
    getTemplate(id, templateType) {
      this.loading = true
      this.$emit('leave')
      getTemplateApi(id, templateType).then((data) => {
        this.$emit('monitor-tabs', [
          ...data.dashboardTabs.filter((t) => !t.isUserCreated),

          ...(this.eventTabs || []).map((t) => ({
            text: t.text,
            dashboardId: t['tab.id'],

            nestedTabs: t['template.tabs']?.map((nt) => ({
              text: nt.text,
              dashboardId: nt['tab.id'],
            })),
          })),

          {
            text: 'Metric Explorer',
            dashboardId: 'METRIC_EXPLORER',
          },

          ...(this.hasPermission(this.$constants.ALERT_READ_PERMISSION)
            ? [{ text: 'Active Policies', dashboardId: 'ACTIVE_POLICIES' }]
            : []),

          ...data.dashboardTabs.filter((t) => t.isUserCreated),
        ])
        this.template = Object.freeze(data)
        this.loading = false
        this.$emit('template-loaded')
        this.$nextTick(() => {
          if (this.forMonitorTemplate) {
            setTimeout(this.startMonitorTemplateWidgetsData, 5000)
          }
        })
      })
    },
    startMonitorTemplateWidgetsData() {
      if (!this.template) {
        return
      }
      if (!this.template.id) {
        return
      }
      if (!this.monitor) {
        return
      }
      this.sendMonitorTemplateOpenEvent()
      // this._heartbeatInterval = setInterval(
      //   this.sendMonitorTemplateOpenEvent,
      //   5 * 60 * 1000 - 30000
      // )
    },
    sendMonitorTemplateOpenEvent() {
      if (!this.template) {
        return
      }
      if (!this.template.id) {
        return
      }
      if (!this.monitor) {
        return
      }
      Bus.$emit('server:event', {
        'event.type': this.$constants.WIDGET_RELOAD_EVENT,
        'event.context': {
          'entity.type': 'monitor',
          'entity.id': this.monitor.id,
          'template.id': this.template.id,
          'container.type': 'Template',
        },
      })
    },
  },
}
</script>
