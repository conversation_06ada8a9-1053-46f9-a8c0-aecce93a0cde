import api from '@api'
import {
  transformTemplate,
  transformDashboard,
  transformTemplateForServer,
} from '@data/dashboard'

export function getTemplateApi(templateId, templateType = 'template') {
  return api
    .get(`/visualization/${templateType}s/${templateId}`)
    .then(({ result }) =>
      templateType === 'template'
        ? transformTemplate(result)
        : transformDashboard(result)
    )
}

export function createTemplateApi(template) {
  return api.post(
    `/visualization/templates`,
    transformTemplateForServer(template)
  )
}

export function updateTemplateApi(template) {
  return api.put(
    `/visualization/templates/${template.id}`,
    transformTemplateForServer(template)
  )
}

export function deleteTemplateApi(template) {
  return api.delete(`/visualization/templates/${template.id}`)
}
