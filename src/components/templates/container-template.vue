<template>
  <MonitorAlertCountProvider :selected-target="target">
    <template v-slot="{ convertedData }">
      <FlotoDrawer
        :open="open"
        width="90%"
        :wrap-style="{ zIndex: zIndex }"
        :get-container="getPopupContainer"
        @hide="handleHide"
      >
        <template v-if="containerItem" v-slot:title>
          <div
            class="flex justify-between items-center"
            :style="{ minHeight: '55px' }"
          >
            <div class="flex">
              <span
                class="flex-1 min-w-0 text-ellipsis"
                :title="
                  containerItem.docker_container || containerItem.container
                "
              >
                {{ containerItem.docker_container || containerItem.container }}
              </span>

              <span class="text-neutral-light ml-2 text-sm">
                | {{ containerItem.ip || containerItem.object_ip }}
              </span>

              <span class="text-neutral-light ml-2 text-sm"> | Docker </span>

              <template
                v-if="
                  containerItem.status ||
                  containerItem.docker_container_status_last
                "
              >
                <span class="text-neutral-light ml-2 text-sm">
                  <MStatusTag
                    class="ml-2"
                    :status="
                      containerItem.statusFormatted ||
                      containerItem.status ||
                      containerItem.docker_container_status_last
                    "
                  />
                </span>
              </template>
            </div>
            <div
              v-if="convertedData.series[0].data.length"
              class="inline-flex flex-grow-0 min-w-0 flex-shrink-0 text-neutral-light justify-center items-center"
            >
              <RadialView
                :data="convertedData"
                for-monitor-details
                disabled-filtering
              />
            </div>
          </div>
        </template>
        <div v-if="containerItem" class="flex flex-col flex-1 min-h-0">
          <div class="flex flex-1 flex-col">
            <TemplateView
              :key="containerItem.id"
              disabled
              template-type="dashboard"
              :time-range="timeline"
              :widget-params="widgetParams"
              :template-id="templateId"
              :grid-row-height="80"
              :grid-gap="16"
            />
          </div>
        </div>
      </FlotoDrawer>
    </template>
  </MonitorAlertCountProvider>
</template>

<script>
import { FILTER_CONDITION_DEFAULT_DATA } from '@components/widgets/constants'
import { transformConditionsForServer } from '@components/widgets/helper'
import Bus from '@utils/emitter'
import MonitorAlertCountProvider from '@components/data-provider/monitor-alert-count-provider.vue'
import RadialView from '@/src/components/widgets/views/radial-view.vue'

const CONTAINER_TEMPLATE_ID = 10000000001041

export default {
  name: 'ContainerTemplate',
  components: {
    MonitorAlertCountProvider,
    RadialView,
  },
  inject: {
    layoutContext: {
      default: { isOmniBoxVisible: false },
      policyGridContext: { default: { data: [] } },
    },
  },
  props: {
    containerItem: {
      type: Object,
      default: undefined,
    },
    open: {
      type: Boolean,
      default: false,
    },
    isFullscreen: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      template: null,
      loading: true,
      zIndex: this.layoutContext.isOmniBoxVisible ? 1054 : 999,
      timeline: {
        selectedKey: 'today',
      },
    }
  },
  computed: {
    widgetParams() {
      const defaultFilter = { ...FILTER_CONDITION_DEFAULT_DATA }
      return {
        'entity.type': 'Monitor',
        entities: [this.containerItem.monitorId],
        'filter.keys': [
          this.containerItem.docker_container || this.containerItem.container,
        ],
        filters: {
          'data.filter': transformConditionsForServer({
            ...defaultFilter,
            groups: [
              {
                ...defaultFilter.groups[0],
                conditions: [
                  {
                    operand: 'docker.container',
                    operator: '=',
                    value:
                      this.containerItem.docker_container ||
                      this.containerItem.container,
                  },
                ],
              },
            ],
          }),
        },
      }
    },
    templateId() {
      return CONTAINER_TEMPLATE_ID
    },
    isOmniBoxVisible() {
      return this.layoutContext.isOmniBoxVisible
    },

    target() {
      return {
        id: this.containerItem?.monitorId,
        interface: this.containerItem?.container,
      }
    },
  },
  watch: {
    open(newValue, oldValue) {
      if (newValue) {
        Bus.$emit('close-edge-tooltip')
        this.timeline = {
          selectedKey: 'today',
        }
        if (newValue !== oldValue) {
          if (newValue) {
            this.zIndex = this.layoutContext.isOmniBoxVisible ? 1054 : 999
          } else {
            this.zIndex = this.layoutContext.isOmniBoxVisible ? 999 : 1054
          }
        }
      }
    },
    layoutContext: {
      deep: true,
      handler(newValue, oldValue) {
        if (newValue !== oldValue) {
          if (newValue) {
            this.zIndex = this.layoutContext.isOmniBoxVisible ? 999 : 1054
          }
        }
      },
    },
  },
  created() {
    Bus.$on('row-click', this.handleHide)
  },
  beforeDestroy() {
    Bus.$off('row-click', this.handleHide)
  },
  methods: {
    getPopupContainer() {
      if (this.isFullscreen) {
        return this.$el.closest('.widget-view')
      }
      return this.$el.closest('.dashboard-container') || document.body
    },
    handleHide() {
      this.template = null
      this.loading = true
      this.$emit('hide')
    },
  },
}
</script>
