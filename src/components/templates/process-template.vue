<template>
  <MonitorAlertCountProvider :selected-target="target">
    <template v-slot="{ convertedData }">
      <FlotoDrawer
        :open="open"
        wrap-class-name="ant-drawer-close-btn-center"
        width="90%"
        :wrap-style="{ zIndex: zIndex }"
        :get-container="getPopupContainer"
        @hide="handleHide"
      >
        <template v-if="process" v-slot:title>
          <div
            class="flex justify-between items-center"
            :style="{ minHeight: '55px' }"
          >
            <div class="flex">
              <span class="flex-1 min-w-0 text-ellipsis">
                {{ process.monitorName }} - {{ processName }}
                <MTooltip v-if="commandLine">
                  <template v-slot:trigger>
                    <MIcon name="info-circle" class="mx-1" />
                  </template>
                  {{ commandLine }}
                </MTooltip>
              </span>

              <template v-if="process.status">
                <span class="text-neutral-light ml-2 text-sm">
                  <MStatusTag
                    class="ml-2"
                    :status="process.statusFormatted || process.status"
                  />
                </span> </template
            ></div>

            <!-- <TimerangePicker
            v-model="timeline"
            class="mr-2"
            style="z-index: 9999"
            :hide-custom-time-range="false"
          /> -->
            <div
              v-if="convertedData.series[0].data.length"
              class="inline-flex flex-grow-0 min-w-0 flex-shrink-0 text-neutral-light justify-center items-center"
            >
              <RadialView
                :data="convertedData"
                for-monitor-details
                disabled-filtering
              />
            </div>
          </div>
        </template>
        <div v-if="process" class="flex flex-col flex-1 min-h-0">
          <div class="flex flex-1 flex-col">
            <!-- <MRow
          class="flex flex-wrap py-2 mx-2"
          style="margin-top: 8px; margin-bottom: 2px"
          :gutter="0"
        >
          <MCol :size="12" class="px-2 justify-around mb-4">
            <MRow>
              <MCol :size="3" class="text-neutral-light"> Process ID </MCol>
              <MCol :size="9">
                {{ processName }}
                <MTooltip v-if="commandLine">
                  <template v-slot:trigger>
                    <MIcon name="info-circle" class="mx-1 text-primary" />
                  </template>
                  {{ commandLine }}
                </MTooltip>
              </MCol>
            </MRow>
          </MCol>
          <MCol :size="12" class="px-2 mb-4">
            <MRow class="items-center">
              <MCol :size="3" class="text-neutral-light"> Monitor </MCol>
              <MCol :size="9" class="flex items-center">
                <div class="inline-flex items-center">
                  <MonitorType
                    :type="process.type"
                    class="mx-1"
                    :center="false"
                  />
                </div>
                <div>
                  {{ process.monitorName }}
                </div>
              </MCol>
            </MRow>
          </MCol>
          <MCol :size="12" class="px-2 mb-4">
            <MRow>
              <MCol :size="3" class="text-neutral-light"> IP Address </MCol>
              <MCol :size="9">
                {{ process.ip || process.monitor }}
              </MCol>
            </MRow>
          </MCol>
        </MRow> -->
            <TemplateView
              :key="process.id"
              disabled
              template-type="dashboard"
              :time-range="timeline"
              :widget-params="widgetParams"
              :template-id="templateId"
              :grid-row-height="80"
              :grid-gap="16"
              @drilldown="handleDrilldown"
            />
          </div>
        </div>
      </FlotoDrawer>
    </template>
  </MonitorAlertCountProvider>
</template>

<script>
import { FILTER_CONDITION_DEFAULT_DATA } from '@components/widgets/constants'
import { transformConditionsForServer } from '@components/widgets/helper'
import Bus from '@utils/emitter'

// import MonitorType from '@components/monitor-type.vue'
// import TimerangePicker from '../widgets/time-range-picker.vue'
import MonitorAlertCountProvider from '@components/data-provider/monitor-alert-count-provider.vue'

import RadialView from '@/src/components/widgets/views/radial-view.vue'

const PROCESS_TEMPLATE_ID = 10000000000008
// const PROCESS_TEMPLATE_ID = 5550253032465

export default {
  name: 'ProcessTemplate',
  components: {
    // MonitorType,
    // TimerangePicker,
    MonitorAlertCountProvider,
    RadialView,
  },
  inject: {
    layoutContext: { default: { isOmniBoxVisible: false } },
  },
  props: {
    process: {
      type: Object,
      default: undefined,
    },
    open: {
      type: Boolean,
      default: false,
    },
    isFullscreen: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      template: null,
      loading: true,
      zIndex: this.layoutContext.isOmniBoxVisible ? 1054 : 999,
      timeline: {
        selectedKey: 'today',
      },
    }
  },
  computed: {
    processName() {
      if (!this.process.name) {
        return undefined
      }
      return this.process.name.split('|')[0]
    },
    commandLine() {
      if (!this.process.name) {
        return undefined
      }
      return this.process.name.split('|').slice(1).join('|')
    },
    widgetParams() {
      const defaultFilter = { ...FILTER_CONDITION_DEFAULT_DATA }
      return {
        'entity.type': 'Monitor',
        entities: [this.process.monitorId],
        'filter.keys': [this.process.name],
        filters: {
          'data.filter': transformConditionsForServer({
            ...defaultFilter,
            groups: [
              {
                ...defaultFilter.groups[0],
                conditions: [
                  {
                    operand: 'system.process',
                    operator: '=',
                    value: this.process.name,
                  },
                ],
              },
            ],
          }),
        },
      }
    },
    templateId() {
      return PROCESS_TEMPLATE_ID
    },
    isOmniBoxVisible() {
      return this.layoutContext.isOmniBoxVisible
    },

    target() {
      return {
        id: this.process?.monitorId,
        interface: this.process?.name,
      }
    },
  },
  watch: {
    open(newValue, oldValue) {
      if (newValue) {
        this.timeline = {
          selectedKey: 'today',
        }
        if (newValue !== oldValue) {
          if (newValue) {
            this.zIndex = this.layoutContext.isOmniBoxVisible ? 1054 : 999
          } else {
            this.zIndex = this.layoutContext.isOmniBoxVisible ? 999 : 1054
          }
        }
      }
    },
    layoutContext: {
      deep: true,
      handler(newValue, oldValue) {
        if (newValue !== oldValue) {
          if (newValue) {
            this.zIndex = this.layoutContext.isOmniBoxVisible ? 999 : 1054
          }
        }
      },
    },
  },
  created() {
    Bus.$on('row-click', this.handleHide)
  },
  methods: {
    getPopupContainer() {
      if (this.isFullscreen) {
        return this.$el.closest('.widget-view')
      }
      return this.$el.closest('.dashboard-container') || document.body
    },
    handleHide() {
      this.template = null
      this.loading = true
      this.$emit('hide')
    },
    handleDrilldown(event) {
      if (event?.type === 'metric.explorer') {
        this.$router.push(
          this.$modules.getModuleRoute('metric-explorer', '', {
            query: {
              counters: encodeURIComponent(
                btoa(JSON.stringify(event.counters))
              ),

              instanceDrillDownContext: encodeURIComponent(
                btoa(
                  JSON.stringify({
                    monitor: this.process.monitorId,
                    instanceType: 'system.process',
                    instance: this.process.name,
                  })
                )
              ),
            },
          })
        )
      }
    },
  },
}
</script>
