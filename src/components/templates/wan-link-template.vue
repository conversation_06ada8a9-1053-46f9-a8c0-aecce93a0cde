<template>
  <MonitorAlertCountProvider :selected-target="target">
    <template v-slot="{ convertedData }">
      <FlotoDrawer
        :open="open"
        width="90%"
        :wrap-style="{ zIndex: zIndex }"
        :get-container="getPopupContainer"
        @hide="handleHide"
      >
        <template v-if="wanLinkItem" v-slot:title>
          <div class="flex justify-between">
            <div class="flex flex-col w-full">
              <span class="flex min-w-0 text-ellipsis">
                {{ wanLinkItem.name }}

                <span
                  v-if="
                    wanLinkItem.wanProbe ||
                    wanProbeMap[wanLinkItem.ipsla_operation_type_last]
                  "
                  class="text-neutral-light ml-2 text-sm"
                >
                  |
                  {{
                    wanLinkItem.wanProbe ||
                    wanProbeMap[wanLinkItem.ipsla_operation_type_last]
                  }}
                </span>

                <template
                  v-if="wanLinkItem.status || wanLinkItem.ipsla_status_last"
                >
                  <span
                    v-if="wanLinkItem.vendor || wanLinkItem.object_vendor"
                    class="text-neutral-light ml-2 text-sm"
                  >
                    | &nbsp;
                    <MStatusTag
                      class="ml-2"
                      :status="
                        wanLinkItem.statusFormatted ||
                        wanLinkItem.status ||
                        wanLinkItem.ipsla_status_last
                      "
                    />
                  </span>
                </template>
              </span>

              <!-- <TimerangePicker
            v-model="timeline"
            class="mr-2"
            style="z-index: 9999"
            :hide-custom-time-range="false"
          /> -->

              <div class="flex flex-1">
                <div class="inline-flex flex-grow-0 min-w-0 flex-shrink-0">
                  <GroupPicker
                    :value="wanLinkItem.groups"
                    disabled
                    :wrap="false"
                  />
                </div>
              </div>
            </div>

            <div
              v-if="convertedData.series[0].data.length"
              class="inline-flex flex-grow-0 min-w-0 flex-shrink-0 text-neutral-light justify-center items-center"
            >
              <RadialView
                :data="convertedData"
                for-monitor-details
                disabled-filtering
              />
            </div>
          </div>
        </template>
        <div v-if="wanLinkItem" class="flex flex-col flex-1 min-h-0 -mx-4">
          <TemplateView
            :key="wanLinkItem.id"
            class="pl-2 mb-4"
            disabled
            template-type="dashboard"
            :time-range="timeline"
            :widget-params="widgetParams"
            :template-id="templateId"
            :grid-row-height="90"
            :default-grid-columns="6"
            disable-auto-column-calculation
            @drilldown="handleDrilldown"
          />
        </div>
      </FlotoDrawer>
    </template>
  </MonitorAlertCountProvider>
</template>

<script>
import { FILTER_CONDITION_DEFAULT_DATA } from '@components/widgets/constants'
import { transformConditionsForServer } from '@components/widgets/helper'
// import TimerangePicker from '../widgets/time-range-picker.vue'
import MonitorAlertCountProvider from '@components/data-provider/monitor-alert-count-provider.vue'
import RadialView from '@/src/components/widgets/views/radial-view.vue'
// import { objectDBWorker } from '@/src/workers'
import Bus from '@utils/emitter'
import { wanProbeMap } from '@src/components/rediscover-results/rediscover-api.js'

const ICMP_ECHO_DRILL_DOWN_TEMPLATE_ID = 10000000001036
const ICMP_JITTER_DRILL_DOWN_TEMPLATE_ID = 10000000001037
const ICMP_PATH_ECHO_DRILL_DOWN_TEMPLATE_ID = 10000000001038

export default {
  name: 'WanLinkTemplate',
  components: {
    // TimerangePicker,
    MonitorAlertCountProvider,
    RadialView,
  },

  inject: {
    layoutContext: {
      default: { isOmniBoxVisible: false },
      policyGridContext: { default: { data: [] } },
    },
  },
  props: {
    wanLinkItem: {
      type: Object,
      default: undefined,
    },
    open: {
      type: Boolean,
      default: false,
    },
    isFullscreen: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    this.wanProbeMap = wanProbeMap
    return {
      template: null,
      loading: true,
      zIndex: this.layoutContext.isOmniBoxVisible ? 1054 : 999,
      timeline: {
        selectedKey: 'today',
      },
    }
  },
  computed: {
    widgetParams() {
      const defaultFilter = { ...FILTER_CONDITION_DEFAULT_DATA }
      const wanLinkItem = this.wanLinkItem
      return {
        'entity.type': 'Monitor',
        entities: [wanLinkItem.monitorId],
        'filter.keys': [this.wanLinkItem.name],
        filters: {
          'data.filter': transformConditionsForServer({
            ...defaultFilter,
            groups: [
              {
                ...defaultFilter.groups[0],
                conditions: [
                  {
                    operand: 'ipsla',
                    operator: '=',
                    value: this.wanLinkItem.name,
                  },
                ],
              },
            ],
          }),
        },
      }
    },
    target() {
      return {
        id: this.wanLinkItem?.monitorId,
        interface: this.wanLinkItem?.name,
      }
    },
    templateId() {
      if (
        this.wanLinkItem.ipsla_operation_type_last === 'ipslaicmpecho' ||
        this.wanLinkItem.wanProbe === 'ICMP Echo'
      ) {
        return ICMP_ECHO_DRILL_DOWN_TEMPLATE_ID
      } else if (
        this.wanLinkItem.ipsla_operation_type_last === 'ipslaicmpjitter' ||
        this.wanLinkItem.wanProbe === 'ICMP Jitter'
      ) {
        return ICMP_JITTER_DRILL_DOWN_TEMPLATE_ID
      }
      return ICMP_PATH_ECHO_DRILL_DOWN_TEMPLATE_ID
    },
    isOmniBoxVisible() {
      return this.layoutContext.isOmniBoxVisible
    },
  },
  watch: {
    open(newValue, oldValue) {
      if (newValue) {
        Bus.$emit('close-edge-tooltip')
        this.timeline = {
          selectedKey: 'today',
        }
        if (newValue !== oldValue) {
          if (newValue) {
            this.zIndex = this.layoutContext.isOmniBoxVisible ? 1054 : 999
          } else {
            this.zIndex = this.layoutContext.isOmniBoxVisible ? 999 : 1054
          }
        }
      }
    },
    layoutContext: {
      deep: true,
      handler(newValue, oldValue) {
        if (newValue !== oldValue) {
          if (newValue) {
            this.zIndex = this.layoutContext.isOmniBoxVisible ? 999 : 1054
          }
        }
      },
    },
  },
  created() {
    Bus.$on('row-click', this.handleHide)
  },
  beforeDestroy() {
    Bus.$off('row-click', this.handleHide)
  },
  methods: {
    handleHide() {
      this.template = null
      this.loading = true
      this.$emit('hide')
    },
    getPopupContainer() {
      if (this.isFullscreen) {
        return this.$el.closest('.widget-view')
      }
      return this.$el.closest('.dashboard-container') || document.body
    },
    handleDrilldown(event) {
      if (event?.type === 'metric.explorer') {
        this.$router.push(
          this.$modules.getModuleRoute('metric-explorer', '', {
            query: {
              counters: encodeURIComponent(
                btoa(JSON.stringify(event.counters))
              ),

              instanceDrillDownContext: encodeURIComponent(
                btoa(
                  JSON.stringify({
                    monitor: this.wanLinkItem.monitorId,
                    instanceType: 'ipsla',
                    instance: this.wanLinkItem.name,
                  })
                )
              ),
            },
          })
        )
      }
    },
  },
}
</script>
