<script>
import PQueue from 'p-queue'
import Notification from 'ant-design-vue/es/notification'
import Bus from '@utils/emitter'
import { socketWorker, objectDBWorker } from '@/src/workers'
import { workerFn } from '@utils/worker-wrapper'
import { authComputed, authMethods } from '@state/modules/auth/'
import {
  showDesktopNotification,
  playAlertSoundNotification,
} from '@utils/desktop-notification'
import Constants from '../constants'
import { UserPreferenceComputed } from '../state/modules/user-preference/helpers'

function changePortAndRedirect(newPort, newPath) {
  // Get the current location
  const currentLocation = window.location

  // Create a new URL with the desired port and path
  const newUrl =
    currentLocation.protocol +
    '//' +
    currentLocation.hostname +
    ':' +
    newPort +
    newPath

  // Assign the new URL to the location object to trigger the redirection
  window.location.href = newUrl
}

const Queue = new PQueue({
  intervalCap: 1000,
  interval: 50,
  concurrency: 500,
})

const CONSOLE_IGNORED_EVENTS = [
  // 'ui.notification.streaming.response',
  // 'ui.event.metric.poll.progress',
  // 'ui.notification.task.manager',
  // 'ui.action.streaming.start',
  // 'ui.notification.agent',
  // 'widget.response.render',
  // 'inventory.response.render',
  // 'ui.notification.user.notification',
  // 'ui.event.log.statistics',
  // 'ui.notification.metric.poll.time',
  // 'ui.event.object.status',
  // 'ui.notification.remote.processor',
  // 'ui.notification.streaming.broadcast',
  // 'ui.action.session.active',
  // 'ui.action.session.inactive',
]

const GlobalEventHandlers = (guid) => ({
  [Constants.UI_EVENT_CSV_EXPORT]: {
    handler(eventContext, vm) {
      if (eventContext.status === Constants.EVENT_FAIL_STATUS) {
        vm.$errorNotification({
          message: eventContext['error.code'],
          description: eventContext.message,
          key: 'csv-export-error',
        })
        return
      }
      if (
        Constants.UI_EVENT_UUID in eventContext &&
        !guid.includes(eventContext[Constants.UI_EVENT_UUID])
      ) {
        return
      }
      if (eventContext['file.name']) {
        window.location.href = `/download?id=${eventContext['file.name']}&file.name=${eventContext['file.name']}`
      }
    },
  },
  // [Constants.UI_EVENT_MAIL_SERVICE_HEALTH]: {
  //   handler(eventContext, vm) {
  //     const key = 'mail-server-health'
  //     Notification.close(key)
  //     const methodName =
  //       eventContext.status === Constants.EVENT_SUCCESS_STATUS
  //         ? '$successNotification'
  //         : '$errorNotification'
  //     const message =
  //       eventContext.status === Constants.EVENT_SUCCESS_STATUS
  //         ? 'Success'
  //         : 'Error'
  //     vm[methodName]({
  //       message,
  //       description: eventContext.message,
  //       key,
  //     })
  //     showDesktopNotification(message, eventContext.message)
  //   },
  // },

  // [Constants.UI_EVENT_SMS_SERVICE_HEALTH]: {
  //   handler(eventContext, vm) {
  //     const key = 'sms-server-health'
  //     Notification.close(key)
  //     const methodName =
  //       eventContext.status === Constants.EVENT_SUCCESS_STATUS
  //         ? '$successNotification'
  //         : '$errorNotification'
  //     const message =
  //       eventContext.status === Constants.EVENT_SUCCESS_STATUS
  //         ? 'Success'
  //         : 'Error'
  //     vm[methodName]({
  //       message,
  //       description: eventContext.message,
  //       key,
  //     })
  //     showDesktopNotification(message, eventContext.message)
  //   },
  // },
  [Constants.UI_EVENT_SERVICE_HEALTH]: {
    handler(eventContext, vm) {
      const key = 'mail-server-health'
      Notification.close(key)
      const methodName =
        eventContext.status === Constants.EVENT_SUCCESS_STATUS
          ? '$successNotification'
          : '$errorNotification'
      const message =
        eventContext.status === Constants.EVENT_SUCCESS_STATUS
          ? 'Success'
          : 'Error'
      vm[methodName]({
        message,
        description: eventContext.message,
        key,
      })
      showDesktopNotification(message, eventContext.message)
    },
  },
  [Constants.UI_EVENT_AGENT]: {
    handler(eventContext, vm) {
      // debugger
      // const eventContext = e['event.context']
      if (
        eventContext['event.type'] === Constants.UI_EVENT_AGENT_DOWNLOAD_LOG
      ) {
        if (
          Constants.UI_EVENT_UUID in eventContext &&
          !guid.includes(eventContext[Constants.UI_EVENT_UUID])
        ) {
          return
        }
        if (eventContext.status === Constants.EVENT_SUCCESS_STATUS) {
          window.location.href = `/download?id=${eventContext.result}&file.name=${eventContext['file.name']}`
        } else {
          vm.$errorNotification({
            message: 'Error',
            description: eventContext.message,
          })
          showDesktopNotification('Error', eventContext.message)
        }
      }
    },
  },
  [Constants.DATABASE_RESTORE]: {
    handler(eventContext, vm) {
      if (eventContext['state'] === Constants.STATE_RUNNING) {
        changePortAndRedirect(eventContext.port, '/restore')
      }
    },
  },
  [Constants.MOTADATA_UPGRADE]: {
    handler(eventContext, vm) {
      if (eventContext['state'] === Constants.STATE_RUNNING) {
        changePortAndRedirect(eventContext.port, '/upgrade')
      }
    },
  },

  [Constants.UI_NOTIFICATION_POLICY_FLAP_SOUND_NOTIFICATION]: {
    handler() {
      playAlertSoundNotification()
    },
  },
  [Constants.UI_NOTIFICATION_DIAGNOSTICS_EXPORT_READY]: {
    handler(eventContext, vm) {
      if (eventContext.status === Constants.EVENT_FAIL_STATUS) {
        vm.$errorNotification({
          message: eventContext['error.code'],
          description: eventContext.message,
          key: 'csv-export-error',
        })
        return
      }
      if (
        Constants.UI_EVENT_UUID in eventContext &&
        !guid.includes(eventContext[Constants.UI_EVENT_UUID])
      ) {
        return
      }
      if (eventContext['file.name']) {
        window.location.href = `/download?id=${eventContext['file.name']}&file.name=${eventContext['file.name']}`
      }
    },
  },
})

export default {
  name: 'SocketListener',
  inject: {
    ScreenBlockerContext: { default: {} },
    SocketContext: { default: {} },
  },
  props: {
    isMotadataUpdatingOrRestoring: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    ...authComputed,
    ...UserPreferenceComputed,
  },
  watch: {
    'SocketContext.excludedEvents': function (newValue) {
      socketWorker.setExcludedEvents(newValue)
    },
    timezone: {
      immediate: true,
      handler(newValue) {
        this.setTimezone()
      },
    },
  },
  created() {
    this.initConnection()
    Bus.$on(Constants.EVENT_MONITOR_DB_CHANGED, this.setObjectMap)
  },
  beforeDestroy() {
    Bus.$off('server:event', this.sendMessageToServer)
    Bus.$off(Constants.EVENT_MONITOR_DB_CHANGED, this.setObjectMap)
    socketWorker.disconnect()
  },
  methods: {
    ...authMethods,
    async setObjectMap() {
      const objectMap = await objectDBWorker.getObjectsAsMap(
        {},
        ['ip', 'name', 'id', 'target', 'category', 'vendor', 'type'],
        'objectId'
      )
      socketWorker.setObjectMap(objectMap)
    },
    async setTimezone() {
      const timezone = this.timezone
      socketWorker.setTimezone(timezone)
    },
    async sendMessageToServer(payload) {
      if (!payload['event.type']) {
        return
      }
      if (this.SocketContext.connected) {
        let updatedPayload
        try {
          updatedPayload = await socketWorker.trimParams(payload || null)
        } catch (e) {
          updatedPayload = {}
        }
        updatedPayload = {
          ...updatedPayload,
          'event.context': {
            ...updatedPayload['event.context'],
            ...(!this.isMotadataUpdatingOrRestoring
              ? {
                  'session-id': this.sessionId,
                  'user.name': this.user.userName,
                }
              : {}),
          },
        }

        // if (process.env.NODE_ENV !== 'production') {
        if (
          CONSOLE_IGNORED_EVENTS.includes(updatedPayload['event.type']) ===
          false
        ) {
          // eslint-disable-next-line
          console.group(
            `%c  ${updatedPayload['event.type']} send at ${Date.now()}`,
            'background:#e72b25 ; padding: 1px; border-radius: 0 3px 3px 0;  color: #fff'
          )
          // eslint-disable-next-line
          console.log({
            ...updatedPayload,

            ...(!this.isMotadataUpdatingOrRestoring
              ? {
                  'session-id': this.sessionId,
                  'user.name': this.user.userName,
                }
              : {}),
          })
          // eslint-disable-next-line
          console.groupEnd()
        }
        // }
        socketWorker.sendToServer('server.event', {
          ...updatedPayload,
          ...(!this.isMotadataUpdatingOrRestoring
            ? {
                'session-id': this.sessionId,
                'user.name': this.user.userName,
              }
            : {}),
        })
      } else {
        this.ScreenBlockerContext.unblock()
      }
    },
    async initConnection() {
      await socketWorker.connect(
        `${process.env.VUE_APP_SOCKET_PATH}`,
        this.sessionId,
        workerFn(this.onConnected),
        workerFn(() => {
          this.SocketContext.setConnected(true)
        }),
        workerFn(() => {
          this.$emit('disconnected')
          this.SocketContext.setConnected(false)
        }),
        workerFn(this.handleIncomingEvent)
      )
      socketWorker.setExcludedEvents(this.SocketContext.excludedEvents || [])
      Bus.$off('server:event', this.sendMessageToServer)
      Bus.$on('server:event', this.sendMessageToServer)
    },
    onConnected() {
      this.$emit('connected')
      this.SocketContext.setConnected(true)
      Bus.$emit('socket:connected')
    },
    getInitialConfigs() {
      // fetch configs
      this.sendMessageToServer({
        'event.type': Constants.UI_CONFIG_EVENT,
        'event.context': {
          'session-id': this.sessionId,
        },
      })
    },
    handleIncomingEvent(eventData) {
      // const { eventType, eventContext } = Cbor.decode(eventData)
      let { eventType, eventContext } = eventData
      if (
        eventContext['event.type'] !== Constants.UI_EVENT_AGENT_DOWNLOAD_LOG &&
        (this.SocketContext.excludedEvents || []).includes(eventType)
      ) {
        return
      }
      const globalHandlers = GlobalEventHandlers(
        this.SocketContext.guidMap[eventType]
      )
      if (globalHandlers[eventType] && globalHandlers[eventType].handler) {
        globalHandlers[eventType].handler(eventContext, this)
        this.SocketContext.removeGuidForEvent(
          eventType,
          eventContext[Constants.UI_EVENT_UUID]
        )
      }

      if (
        eventType === Constants.USER_LOGOUT &&
        eventContext['user.id'] === this.user.id
      ) {
        this.logoutAndRedirectToLogin()
      }

      this.broadcastEventToBus(eventType, eventContext)
    },
    broadcastEventToBus(eventType, eventContext) {
      Queue.add(async () => {
        // if (process.env.NODE_ENV !== 'production') {
        if (CONSOLE_IGNORED_EVENTS.indexOf(eventType) === -1) {
          // eslint-disable-next-line
          console.group(
            `%c  ${eventType} received at ${Date.now()}`,
            'background:#099DD9 ; padding: 1px; border-radius: 0 3px 3px 0;  color: #fff'
          )
          // eslint-disable-next-line
          console.log(eventContext)
          // eslint-disable-next-line
          console.groupEnd()
        }
        // }

        Bus.$emit(eventType, eventContext)
        // @TODO remove this debuger once everything is working
        Bus.$emit('socket.debugger', {
          'event.type': eventType,
          'event.context': eventContext,
        })
      })
    },
    logoutAndRedirectToLogin() {
      this.logout().then(() => {
        this.$router.replace(
          this.$modules.getModuleRoute('auth', 'login', {
            query: { redirectFrom: this.$route.fullPath },
          })
        )
      })
    },
  },
  render() {
    return null
  },
}
</script>
