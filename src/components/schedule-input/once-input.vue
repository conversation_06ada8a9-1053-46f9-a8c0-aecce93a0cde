<template>
  <MRow v-if="!hideDateTime">
    <MCol :size="6">
      <FlotoFormItem label="Start Date" rules="required">
        <MDatePicker
          id="schedule-start-date"
          v-model="startDate"
          :show-time="false"
          :min-date="todaysDate"
          :allow-clear="false"
        />
      </FlotoFormItem>
    </MCol>
    <MCol :size="6">
      <FlotoFormItem id="hours" label="Hours" rules="required">
        <Timepicker
          id="schedule-time-picker"
          v-model="times"
          :external-options="timeOptions"
          :max-values="$attrs['use-single-selection'] ? 1 : undefined"
          :multiple="multiple"
        />
      </FlotoFormItem>
    </MCol>
  </MRow>
</template>

<script>
import Moment from 'moment'
import Timepicker from '../time-picker.vue'

export default {
  name: 'OnceForm',
  components: { Timepicker },
  model: { event: 'change' },
  props: {
    value: { type: Object, default: undefined },
    hideDateTime: { type: <PERSON>olean, default: false },
    timeOptions: { type: Array, default: undefined },
    multiple: { type: Boolean, default: false },
  },
  data() {
    this.todaysDate = Moment()
    return {}
  },
  computed: {
    startDate: {
      get() {
        return this.value.startDate
      },
      set(startDate) {
        this.$emit('change', { ...this.value, startDate })
      },
    },
    times: {
      get() {
        if (this.multiple) {
          return this.value.times
        } else {
          if (Array.isArray(this.value.times)) {
            return this.value.times.join(', ')
          } else {
            return this.value.times
          }
        }
      },
      set(times) {
        this.$emit('change', { ...this.value, times })
      },
    },
  },
}
</script>
