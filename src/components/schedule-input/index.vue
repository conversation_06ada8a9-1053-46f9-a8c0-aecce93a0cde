<template>
  <MRow>
    <MCol :size="12" class="mb-4">
      <!--#3621-Improvement:Case 4-->
      <FlotoFormItem label="Scheduler Type">
        <MRadioGroup
          id="schedule-time-line"
          v-model="scheduleType"
          :options="computedScheduleOptions"
          as-button
          @change="handleScheduleTypeChange"
        />
      </FlotoFormItem>
    </MCol>
    <MCol>
      <OnceInput
        v-if="['Once', 'Daily'].indexOf(scheduleType) >= 0"
        id="once-input"
        v-model="scheduleInfo"
        v-bind="$attrs"
        :multiple="!showOnlyOnce"
      />
      <WeeklyInput
        v-if="['Weekly'].indexOf(scheduleType) >= 0"
        id="weekly-input"
        v-model="scheduleInfo"
        v-bind="$attrs"
        :multiple="!showOnlyOnce"
      />
      <MonthlyInput
        v-if="['Monthly'].indexOf(scheduleType) >= 0"
        id="monthly-input"
        v-model="scheduleInfo"
        v-bind="$attrs"
        :multiple="!showOnlyOnce"
      />
    </MCol>
  </MRow>
</template>

<script>
import Moment from 'moment'
import OnceInput from './once-input.vue'
import WeeklyInput from './weekly-input.vue'
import MonthlyInput from './monthly-input.vue'

export default {
  name: 'ScheduleInput',
  components: { OnceInput, WeeklyInput, MonthlyInput },
  model: { event: 'change' },
  props: {
    value: { type: Object, default: undefined },
    excludedScheduleOptions: {
      type: Array,
      default() {
        return []
      },
    },
    showOnlyOnce: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    this.scheduleOptions = [
      { value: 'Once', text: 'Once' },
      { value: 'Daily', text: 'Daily' },
      { value: 'Weekly', text: 'Weekly' },
      { value: 'Monthly', text: 'Monthly' },
    ]
    return {}
  },
  computed: {
    scheduleType: {
      get() {
        return (this.value || {}).scheduleType
      },
      set(scheduleType) {
        this.$emit('change', { ...(this.value || {}), scheduleType })
      },
    },
    scheduleInfo: {
      get() {
        return (this.value || {}).scheduleInfo
      },
      set(scheduleInfo) {
        this.$emit('change', { ...(this.value || {}), scheduleInfo })
      },
    },

    computedScheduleOptions() {
      if (this.showOnlyOnce) {
        return this.scheduleOptions.filter((o) => o.value === 'Once')
      } else {
        return this.scheduleOptions.filter(
          (o) => !this.excludedScheduleOptions.includes(o.value)
        )
      }
    },
  },
  watch: {
    value: {
      immediate: true,
      handler: 'selectTodayDate',
    },
  },
  methods: {
    selectTodayDate() {
      if (this.scheduleInfo && !this.scheduleInfo.startDate) {
        this.scheduleInfo = {
          ...this.scheduleInfo,
          startDate: Moment().unix() * 1000,
        }
      }
    },
    handleScheduleTypeChange(scheduleType) {
      if (this.scheduleType !== scheduleType) {
        this.$emit('change', {
          scheduleType,
          scheduleInfo: {
            startDate: Moment().unix() * 1000,
          },
        })
      }
    },
  },
}
</script>
