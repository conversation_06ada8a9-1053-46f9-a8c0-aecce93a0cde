<template>
  <MRow>
    <MCol :size="12">
      <FlotoFormItem label="Days" rules="required">
        <FlotoDropdownPicker
          id="select-day-of-week"
          v-model="days"
          class="w-full multi-select-group"
          multiple
          searchable
          :max-values="$attrs['use-single-selection'] ? 1 : undefined"
          :options="options"
        />
      </FlotoFormItem>
    </MCol>
    <MCol v-if="!hideDateTime" :size="6">
      <FlotoFormItem label="Start Date" rules="required">
        <MDatePicker
          id="schedule-start-date"
          v-model="startDate"
          :show-time="false"
          :min-date="todaysDate"
          :allow-clear="false"
        />
      </FlotoFormItem>
    </MCol>
    <MCol v-if="!hideDateTime" :size="6">
      <FlotoFormItem label="Hours" rules="required">
        <Timepicker
          id="schedule-time-picker"
          v-model="times"
          :external-options="timeOptions"
          :max-values="$attrs['use-single-selection'] ? 1 : undefined"
          :multiple="multiple"
        />
      </FlotoFormItem>
    </MCol>
  </MRow>
</template>

<script>
import Moment from 'moment'
import Timepicker from '../time-picker.vue'

export default {
  name: 'OnceForm',
  components: { Timepicker },
  model: { event: 'change' },
  props: {
    value: { type: Object, default: undefined },
    hideDateTime: { type: Boolean, default: false },
    timeOptions: { type: Array, default: undefined },
    multiple: { type: Boolean, default: false },
  },
  data() {
    this.todaysDate = Moment()
    this.options = [
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
      'Sunday',
    ].map((d) => ({ key: d, text: d }))
    return {}
  },
  computed: {
    startDate: {
      get() {
        return this.value.startDate
      },
      set(startDate) {
        this.$emit('change', { ...this.value, startDate })
      },
    },
    times: {
      get() {
        return this.value.times
      },
      set(times) {
        this.$emit('change', { ...this.value, times })
      },
    },
    days: {
      get() {
        return this.value.days
      },
      set(days) {
        this.$emit('change', { ...this.value, days })
      },
    },
  },
}
</script>
