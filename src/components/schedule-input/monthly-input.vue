<template>
  <MRow>
    <MCol :size="12">
      <FlotoFormItem label="Months" rules="required">
        <FlotoDropdownPicker
          id="select-month"
          v-model="months"
          class="w-full multi-select-group"
          multiple
          searchable
          :max-values="$attrs['use-single-selection'] ? 1 : undefined"
          :options="options"
        />
      </FlotoFormItem>
    </MCol>
    <MCol :size="12">
      <FlotoFormItem label="Dates" rules="required">
        <FlotoDropdownPicker
          id="select-date"
          v-model="dates"
          class="w-full multi-select-group"
          multiple
          searchable
          :max-values="$attrs['use-single-selection'] ? 1 : undefined"
          :options="dateOptions"
        />
      </FlotoFormItem>
    </MCol>
    <MCol v-if="!hideDateTime" :size="6">
      <FlotoFormItem label="Start Date" rules="required">
        <MDatePicker
          id="select-schedule-start-date"
          v-model="startDate"
          :show-time="false"
          :min-date="todaysDate"
          :allow-clear="false"
        />
      </FlotoFormItem>
    </MCol>
    <MCol v-if="!hideDateTime" :size="6">
      <FlotoFormItem label="Hours" rules="required">
        <Timepicker
          id="select-schedule-time"
          v-model="times"
          :external-options="timeOptions"
          :max-values="$attrs['use-single-selection'] ? 1 : undefined"
          :multiple="multiple"
        />
      </FlotoFormItem>
    </MCol>
  </MRow>
</template>

<script>
import Range from 'lodash/range'
import Moment from 'moment'
import Timepicker from '../time-picker.vue'

export default {
  name: 'OnceForm',
  components: { Timepicker },
  model: { event: 'change' },
  props: {
    value: { type: Object, default: undefined },
    hideDateTime: { type: Boolean, default: false },
    timeOptions: { type: Array, default: undefined },
    multiple: { type: Boolean, default: false },
  },
  data() {
    this.todaysDate = Moment()
    this.options = Range(1, 13)
      .map((m) => Moment(m, 'M').format('MMMM'))
      .map((d) => ({ key: d, text: d }))
    this.dateOptions = Range(1, 32).map((d) => ({
      key: d,
      text: String(d),
      id: String(d),
    }))
    return {}
  },
  computed: {
    startDate: {
      get() {
        return this.value.startDate
      },
      set(startDate) {
        this.$emit('change', { ...this.value, startDate })
      },
    },
    times: {
      get() {
        return this.value.times
      },
      set(times) {
        this.$emit('change', { ...this.value, times })
      },
    },
    months: {
      get() {
        return this.value.months
      },
      set(months) {
        this.$emit('change', { ...this.value, months })
      },
    },
    dates: {
      get() {
        return this.value.dates
      },
      set(dates) {
        this.$emit('change', { ...this.value, dates })
      },
    },
  },
}
</script>
