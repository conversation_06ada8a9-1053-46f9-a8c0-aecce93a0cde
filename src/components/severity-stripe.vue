<template>
  <div class="severity-indicator" :class="severityColor"></div>
</template>

<script>
export default {
  name: 'SeverityStripe',
  props: {
    severity: {
      type: String,
      required: true,
    },
  },
  computed: {
    severityColor() {
      return (this.severity || '').toLowerCase()
    },
  },
}
</script>

<style scoped>
.severity-indicator {
  @apply mr-2;

  width: 3px;
  height: 100%;
  min-height: 3px;
  border-radius: 2px;

  &.critical {
    background: var(--secondary-red);
  }

  &.high {
    background: var(--secondary-orange);
  }

  &.medium {
    background: var(--secondary-yellow);
  }

  &.low {
    background: var(--secondary-green);
  }

  &.info {
    background: var(--primary);
  }
}
</style>
