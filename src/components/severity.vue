<template>
  <MTooltip
    :disabled="disableTooltip"
    style="display: flex; flex: unset; align-items: center"
  >
    <template v-slot:trigger>
      <div
        class="severity-dot-wrapper"
        :class="{ 'not-center': !center }"
        :title="disableTooltip ? tooltipText || capitalizeSeverity : undefined"
      >
        <div
          class="severity-dot-box severity-dot-shadow"
          :class="{ [smallSeverity]: true }"
        >
          <!-- <div
            v-if="smallSeverity === 'down'"
            class="flex flex-1 h-full w-full severity-dot items-center justify-center"
            :class="smallSeverity"
          >
            <div
              class="flex items-center justify-center"
              style="width: 80%; height: 80%; margin-left: 1px"
            >
              <svg
                class="h-full w-full"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 5.4 15.3"
              >
                <path
                  fill="var(--page-background-color)"
                  d="M2.7,11.79a1.75,1.75,0,1,0,1.75,1.75A1.75,1.75,0,0,0,2.7,11.79ZM4,0H1.21L1,0A1.25,1.25,0,0,0,0,1.48c.06.69.52,5.71.68,7.39s1,1.78,1.33,1.78H3.3A1.4,1.4,0,0,0,4.69,9.28c.08-.7.64-7.06.7-8.06S4.7,0,4,0Z"
                />
              </svg>
            </div>
          </div> -->
          <div
            class="severity-dot severity-dot-shadow"
            :class="smallSeverity"
          />
        </div>
        <span v-if="displayText" class="ml-1">{{
          tooltipText || capitalizeSeverity
        }}</span>
      </div>
    </template>
    {{ tooltipText || capitalizeSeverity }}
  </MTooltip>
</template>

<script>
import isIP from 'validator/lib/isIP'
import Bus from '@utils/emitter'
import Capitalize from 'lodash/capitalize'
import { severityDBWorker, objectDBWorker } from '@/src/workers'

export default {
  name: 'Severity',
  props: {
    objectId: { type: [Number, String], default: undefined },
    instance: { type: String, default: undefined },
    counter: { type: String, default: undefined },
    severity: { type: String, default: undefined },
    tooltipText: { type: String, default: undefined },
    disableTooltip: { type: Boolean, default: false },
    // eslint-disable-next-line
    center: { type: Boolean, default: true },
    displayText: { type: Boolean, default: false },
    // eslint-disable-next-line
    shadow: { type: Boolean, default: true },
  },
  data() {
    return {
      severityText: this.severity || 'UNKNOWN',
      resolvedObjectId: undefined,
    }
  },
  computed: {
    smallSeverity() {
      return (this.severityText || '').toLowerCase()
    },
    capitalizeSeverity() {
      return Capitalize(this.severityText)
    },
    monitorId() {
      return this.resolvedObjectId || this.objectId
    },
  },
  watch: {
    severity(newValue, oldValue) {
      if (newValue !== oldValue && newValue) {
        this.severityText = newValue
      }
    },
    objectId(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.resolveSeverity()
      }
    },
    counter(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.resolveSeverity()
      }
    },
    instance(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.resolveSeverity()
      }
    },
  },
  created() {
    if (this.objectId) {
      this.resolveSeverity()
    } else {
      this.severityText = this.severity
    }

    if (!this.severity) {
      this.bindLiveSeverityEvent()
    }

    if (this.instance || this.counter) {
      // this.bindLiveSeverityInstanceEvent()
    }
  },
  methods: {
    // bindLiveSeverityEvent() {
    //   Bus.$on(
    //     this.$constants.EVENT_SEVERITY_UPDATED,
    //     this.monitorSeverityUpdated
    //   )
    //   this.$once('hook:beforeDestroy', () => {
    //     Bus.$off(
    //       this.$constants.EVENT_SEVERITY_UPDATED,
    //       this.monitorSeverityUpdated
    //     )
    //   })
    // },
    bindLiveSeverityEvent() {
      // Bus.$on(
      //   this.$constants.EVENT_SEVERITY_UPDATED,
      //   this.monitorSeverityUpdated
      // )
      // this.$once('hook:beforeDestroy', () => {
      //   Bus.$off(
      //     this.$constants.EVENT_SEVERITY_UPDATED,
      //     this.monitorSeverityUpdated
      //   )
      // })
      Bus.$on(
        this.$constants.EVENT_SEVERITY_COUNTER_DB_CHANGED,
        this.resolveSeverity
      )
      this.$once('hook:beforeDestroy', () => {
        Bus.$off(
          this.$constants.EVENT_SEVERITY_COUNTER_DB_CHANGED,
          this.resolveSeverity
        )
      })
    },
    bindLiveSeverityInstanceEvent() {
      // const handler = (event) => {
      //   if (
      //     event.objectId === this.monitorId &&
      //     ((this.counter && event.counter === this.counter) ||
      //       (this.instance && event.instance === this.instance))
      //   ) {
      //     this.severityText = event.severity
      //   }
      // }
      // Bus.$on(this.$constants.EVENT_COUNTER_SEVERITY_UPDATED, handler)
      // Bus.$on(this.$constants.EVENT_COUNTER_SEVERITY_UPDATED, handler)
      this.$once('hook:beforeDestroy', () => {
        // Bus.$off(this.$constants.EVENT_COUNTER_SEVERITY_UPDATED, handler)
      })
    },
    monitorSeverityUpdated(id, severity) {
      if (this.objectId === id) {
        if (!this.counter && !this.instance) {
          this.severityText = severity
        }
      }
    },
    async resolveSeverity() {
      setTimeout(async () => {
        let severityObject
        if (isIP(String(this.objectId))) {
          const monitor = await objectDBWorker.getObjectByIP(this.objectId)
          if (monitor) {
            this.resolvedObjectId = monitor.id
          }
        } else {
          if (!/^\d+$/.test(this.objectId)) {
            const monitor = await objectDBWorker.getObjectByName(this.objectId)
            if (monitor) {
              this.resolvedObjectId = monitor.id
            }
          }
        }
        if (this.instance || this.counter || this.monitorId) {
          severityObject = await severityDBWorker.getSeverityByEntity(
            this.monitorId,
            null,
            // this.counter,
            this.instance
          )
          this.severityText = (severityObject || {}).severity || 'UNKNOWN'
          this.$emit('udpate:severity', this.severityText)
        }
      })
    },
  },
}
</script>
