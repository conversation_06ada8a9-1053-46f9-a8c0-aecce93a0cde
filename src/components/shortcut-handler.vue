<script>
import Mousetrap from 'mousetrap'
import Bus from '@utils/emitter'
import AVAILABLE_SHORTCUTS from '@src/shortcuts'

Mousetrap.prototype.stopCallback = function (e, element, combo) {
  // if popover is open and has container class "ignore-mousetrap" then stop
  // @TODO get dynamic ant prefix
  if (document.querySelector('.ant-popover-content .ignore-mousetrap')) {
    return true
  }
  // if the element has the class "mousetrap" then no need to stop
  if (
    (' ' + element.className + ' ').indexOf(' mousetrap ') > -1 ||
    element.closest('.mousetrap')
  ) {
    return false
  }

  // stop for input, select, and textarea
  return (
    element.tagName === 'INPUT' ||
    element.tagName === 'SELECT' ||
    element.tagName === 'TEXTAREA' ||
    (element.contentEditable && element.contentEditable === 'true')
  )
}

export default {
  name: 'ShortcutHandler',
  data() {
    this.boundShortcuts = []
    return {}
  },
  mounted() {
    this.bindAvailableShortcuts()
  },
  beforeDestroy() {
    this.unbindAllShortcuts()
  },
  methods: {
    bindAvailableShortcuts() {
      this.unbindAllShortcuts()
      const bindableShortcuts = AVAILABLE_SHORTCUTS
      this.boundShortcuts = bindableShortcuts
      this.boundShortcuts.map((s) => {
        Mousetrap.bind(s.key, (...args) => {
          // const e = args[0]
          // if (e.preventDefault) {
          //   e.preventDefault()
          // } else {
          //   // internet explorer
          //   e.returnValue = false
          // }
          Bus.$emit(s.command, ...[...args, s.command])
        })
      })
      this.bindAppLevelShortcuts()
    },
    bindAppLevelShortcuts() {
      // bind shortcuts which are for route navigations
      this.boundShortcuts
        .filter((s) => s.routeArgs)
        .forEach((shortcut) => {
          Bus.$on(shortcut.command, (e) => {
            e.preventDefault()
            e.stopPropagation()
            const route =
              typeof shortcut.routeArgs === 'function'
                ? shortcut.routeArgs(this.isPortal)
                : shortcut.routeArgs
            this.$router.push(this.$modules.getModuleRoute(...route))
          })
        })
    },
    unbindAllShortcuts() {
      this.boundShortcuts
        .filter((s) => s.routeArgs)
        .forEach((shortcut) => {
          Bus.$off(shortcut.command)
        })
      Mousetrap.reset()
    },
  },
  render() {
    return null
  },
}
</script>
