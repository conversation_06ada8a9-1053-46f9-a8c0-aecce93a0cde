<template>
  <div class="flex monitor-icon" :class="{ 'justify-center': center }">
    <MTooltip v-if="!disableTooltip">
      <template v-slot:trigger>
        <div
          class="items-center monitor-icon"
          :class="[isFitContents ? 'flex' : 'inline-flex']"
        >
          <div
            :style="{ width: width, height: width }"
            class="monitor-icon flex items-center"
          >
            <img :src="src" class="h-full w-full" :alt="type" />
          </div>
          <span v-if="displayText" class="ml-2">
            {{ text || type }}
          </span>
        </div>
      </template>
      {{ text || type }}
    </MTooltip>
    <div
      v-else
      class="items-center monitor-icon"
      :class="[isFitContents ? 'flex' : 'inline-flex']"
      :title="text || type"
    >
      <div
        :style="{ width: width, height: width }"
        class="monitor-icon flex items-center"
      >
        <img :src="src" class="h-full w-full" :alt="type" />
      </div>
      <span v-if="displayText" class="ml-2">
        {{ text || type }}
      </span>
    </div>
  </div>
</template>

<script>
import { UserPreferenceComputed } from '@state/modules/user-preference'
import getIcon from '@assets/icons/monitor-type-icons/monitor-type-icons'
import NoIcon from '@assets/icons/monitor-type-icons/icons/no-icon.svg?external'

export default {
  name: 'MonitorType',
  props: {
    text: { type: String, default: undefined },
    type: { type: String, default: undefined },
    width: { type: [Number, String], default: '24px' },
    displayText: { type: Boolean, default: false },
    disableTooltip: { type: Boolean, default: false },
    // eslint-disable-next-line
    center: { type: Boolean, default: true },
    isFitContents: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    ...UserPreferenceComputed,
    src() {
      if (this.type) {
        return getIcon(this.type, this.theme)
      }
      return NoIcon
    },
    lowerCasedType() {
      return (this.type || '').toLowerCase()
    },
  },
}
</script>
