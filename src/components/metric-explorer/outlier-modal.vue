<template>
  <MModal
    open
    :width="width"
    overlay-class-name="hide-footer"
    centered
    @cancel="onCancel"
  >
    <template v-slot:trigger>
      <span />
    </template>
    <template v-slot:title>
      <div class="flex items-center">
        <div class="flex-1">
          <slot name="title">
            <h4 class="mb-0 text-primary">
              {{ counter }} of {{ monitor.name }} ({{
                monitor.ip || monitor.target
              }})
            </h4>
          </slot>
        </div>
        <TimeRangePicker
          v-model="appliedTimeline"
          :hide-custom-time-range="false"
        />
        <MButton
          variant="transparent"
          :shadow="false"
          shape="circle"
          class="ml-2"
          @click="onCancel"
        >
          <MIcon name="times" class="text-neutral-light" />
        </MButton>
      </div>
    </template>
    <div style="height: 70vh; min-height: 250px" class="flex flex-col">
      <div class="flex items-center">
        <FlotoFormItem label="Algorithm" class="mr-4">
          <MRadioGroup
            v-model="algorithm"
            as-button
            :options="algorithmOptions"
          ></MRadioGroup>
        </FlotoFormItem>
        <MonitorProvider
          :search-params="monitorSearchParams"
          :excluded-ids="[objectId]"
        >
          <FlotoFormItem label="Select Target Monitors" class="mr-4 flex-1">
            <MonitorPicker v-model="targetObjectIds" multiple />
          </FlotoFormItem>
        </MonitorProvider>
        <MCheckbox v-model="overlayAlert">Overlay alert</MCheckbox>
      </div>
      <div class="flex flex-1 flex-col min-h-0">
        <MetricChart
          v-if="overlayOptions.targetObjectIds.length > 0"
          :title="`${counter} ${unit ? `(${unit})` : ''}`"
          :allow-close="false"
          :counter="counter"
          :object-id="objectId"
          :chart-options="chartOptions"
          chart-type="line"
          :instance="instance"
          :instance-type="instanceType"
          :overlay-options="overlayOptions"
          :unit="unit"
          :timeline="appliedTimeline"
          :mergable="false"
          :color="color"
          :event="$constants.UI_WIDGET_RESULT_EVENT"
          sync-group="compareSyncedChart"
          :series-key-builder="currentSeriesKeyBuilder"
          hide-counter-name
        >
          <template v-slot="slotProps">
            <slot name="default" v-bind="slotProps" />
          </template>
        </MetricChart>
        <div v-else class="flex flex-1 items-center justify-center">
          <h4 class="text-primary">Please select target Monitors</h4>
        </div>
      </div>
    </div>
  </MModal>
</template>

<script>
import MonitorProvider from '@components/data-provider/monitor-provider.vue'
import MonitorPicker from '@components/data-picker/monitor-picker.vue'
import CloneDeep from 'lodash/cloneDeep'
import { objectDBWorker } from '@/src/workers'
import MetricChart from '@components/metric-explorer/metric-chart.vue'
import TimeRangePicker from '@components/widgets/time-range-picker.vue'

export default {
  name: 'OutlierModal',
  components: {
    MetricChart,
    TimeRangePicker,
    MonitorProvider,
    MonitorPicker,
  },
  inject: { monitorContext: { default: { options: new Map() } } },
  props: {
    objectId: {
      type: Number,
      required: true,
    },
    counter: {
      type: String,
      required: true,
    },
    instanceType: {
      type: String,
      default: undefined,
    },
    instance: {
      type: String,
      default: undefined,
    },
    timeline: {
      type: Object,
      required: true,
    },
    chartOptions: {
      type: Object,
      default() {
        return {}
      },
    },
    color: {
      type: String,
      default: undefined,
    },
    unit: {
      type: String,
      default: undefined,
    },
  },
  data() {
    this.algorithmOptions = ['DBSCAN', 'MAD', 'SCALEDDBSCAN', 'SCALEDMAD'].map(
      (c) => ({
        text: c,
        value: c,
      })
    )
    return {
      width: window.innerWidth - 200,
      monitor: {},
      appliedTimeline: CloneDeep(this.timeline),
      overlayOptions: {
        type: 'Outlier',
        algorithm: 'DBSCAN',
        targetObjectIds: [],
        sourceObjectId: this.objectId,
        overlayAlert: false,
      },
    }
  },
  computed: {
    monitorSearchParams() {
      const monitor = this.monitor
      if (monitor.type) {
        return {
          type: monitor.type,
        }
      }
      return undefined
    },
    algorithm: {
      get() {
        return this.overlayOptions.algorithm
      },
      set(algorithm) {
        this.overlayOptions = {
          ...this.overlayOptions,
          algorithm,
        }
      },
    },
    targetObjectIds: {
      get() {
        return this.overlayOptions.targetObjectIds
      },
      set(targetObjectIds) {
        this.overlayOptions = {
          ...this.overlayOptions,
          targetObjectIds,
        }
      },
    },
    overlayAlert: {
      get() {
        return this.overlayOptions.overlayAlert
      },
      set(overlayAlert) {
        this.overlayOptions = {
          ...this.overlayOptions,
          overlayAlert,
        }
      },
    },
  },
  created() {
    this.getMonitor()
  },
  methods: {
    async getMonitor() {
      const monitor = await objectDBWorker.getObjectById(this.objectId)
      if (monitor) {
        this.monitor = Object.freeze(monitor)
      }
    },
    currentSeriesKeyBuilder(props) {
      return `anomaly-${props.objectId}-${props.counter}${
        props.instance ? `-${props.instance}` : ''
      }`
    },
    onCancel() {
      this.$emit('cancel')
    },
  },
}
</script>
