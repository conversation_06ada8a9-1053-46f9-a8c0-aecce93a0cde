<template>
  <div
    class="flex flex-col h-full relative chart-container metric-explorer-chart page-background-color pb-2"
    :class="{ 'has-actions': allowClose }"
  >
    <FlotoContentLoader :loading="loading">
      <div v-if="!hideCounterName" class="my-1 flex items-center">
        <h6 class="inline-flex items-center text-ellipsis mb-0">
          <span
            v-if="
              hasSeries &&
              !hasOverlaySeries &&
              mergedCounters.length === 0 &&
              mergable
            "
            class="cursor-move ml-2 relative"
            title="Merge Trend"
            draggable="true"
            :class="{
              'mr-2': !(monitor && monitor.id),
            }"
            @dragstart="dragStart"
            @dragend="dragEnd"
          >
            <MIcon name="merge-chart" size="lg" class="text-neutral" />
          </span>

          <FlotoLink
            v-if="monitor && monitor.id"
            :to="
              $modules.getModuleRoute('inventory', 'monitor-template', {
                params: { category: monitor.category, monitorId: monitor.id },
              })
            "
            :title="`${monitor.name}`"
            class="text-ellipsis mx-2"
          >
            <b class="text-ellipsis">{{ monitor.name }}</b>
          </FlotoLink>
          <MTag
            v-if="instance"
            :closable="false"
            rounded
            class="tag-primary text-ellipsis ml-2 cursor-auto"
            style="max-width: 200px"
            :title="instance"
            >{{ instance }}</MTag
          >
          <MTag
            v-if="error"
            :closable="false"
            rounded
            class="tag-red text-ellipsis ml-2 cursor-auto"
          >
            {{ error.code }}
            <ErrorInfo
              :error="error.message"
              classes="m-0"
              size="sm"
              type="secondary-red"
            />
          </MTag>

          <MTag
            v-if="!hideCounterName"
            rounded
            :class="{
              'ml-2 mt-1':
                !instance ||
                !(
                  hasSeries &&
                  !hasOverlaySeries &&
                  mergedCounters.length === 0 &&
                  mergable
                ),
            }"
            @close="handleRemoveChart"
          >
            <div
              class="rounded"
              style="
                position: relative;
                top: 3px;
                display: inline-block;
                width: 15px;
                height: 15px;
              "
              :style="{
                backgroundColor: color,
              }"
            />
            {{
              (overlayOptions || {}).type
                ? `${(overlayOptions || {}).type} (`
                : ''
            }}
            {{
              currentArithmeticOperation
                ? `${
                    DEFAULT_ARITHMETIC_OPERATION_OPTIONS_NAME_MAP[
                      currentArithmeticOperation
                    ] || ''
                  } (`
                : ''
            }}
            {{ title || `${dotCounterName}` }}
            {{ currentArithmeticOperation ? ')' : '' }}
            {{ (overlayOptions || {}).type ? ')' : '' }}
          </MTag>
        </h6>

        <MIcon
          v-if="sortable"
          name="arrows"
          title="Move trend up/down"
          size="lg"
          class="sort-handle ml-2 cursor-move relative text-neutral"
        />
        <div
          ref="mergedCountersRef"
          class="flex items-center relative mx-2 flex-1 flex-wrap"
          :class="{
            'justify-end': mergedCounters.length === 0,
            'justify-start': mergedCounters.length !== 0,
          }"
        >
          <MTag
            v-for="c in mergedCounters"
            :key="c.seriesKey"
            rounded
            :class="{
              'mt-1':
                !instance ||
                !(
                  hasSeries &&
                  !hasOverlaySeries &&
                  mergedCounters.length === 0 &&
                  mergable
                ),
            }"
            @close="handleRemoveCounter(c.seriesKey)"
          >
            <div
              class="rounded"
              style="
                position: relative;
                top: 3px;
                display: inline-block;
                width: 15px;
                height: 15px;
              "
              :style="{
                backgroundColor:
                  c.color ||
                  (MetricExplorerContext.data[c.seriesKey] &&
                    MetricExplorerContext.data[c.seriesKey].color),
              }"
            />
            {{ c.displayName || c.counter.replace(/~/g, '.') }}
          </MTag>
          <slot
            v-if="mergedCounters.length === 0"
            name="chart-actions"
            :counter="counter"
            :instance="instance"
            :instance-type="instanceType"
            :unit="currentUnit"
            :color="color"
            :object-id="objectId"
            :hasData="hasSeries"
          />

          <!-- <MButton
            v-if="allowClose || (chartSeries && chartSeries.length === 0)"
            class="remove-action-btn"
            size="small"
            variant="neutral-lighter"
            :rounded="false"
            shape="circle"
            title="Remove"
            @click="removeChart"
          >
            <MIcon name="times" />
          </MButton> -->
        </div>
      </div>
      <div ref="chartContainerRef" class="flex flex-1 min-h-0">
        <div
          v-if="hasSeries"
          ref="chartDivRef"
          class="flex-1 h-full"
          @drop="dropSeries"
          @dragover="allowDropSeries"
        >
          <ChartOptions
            v-if="height"
            :key="chartKey"
            mark-outside-range
            :stacked="stacked"
            :data="chartSeries"
            :tooltip-out-side="chartSeries && chartSeries.length > 1"
            :chart-type="chartType"
            :height="height"
            :unit="currentUnit"
            :alert-series="policySeries"
            range-series-name="Prediction Range"
            :is-anomaly-range="overlayOptions.type === 'Anomaly'"
            :range-series-color="color"
            :range-series="anomalySeries"
            :forecast-series="forecastSeries"
            :forecast-series-color="color"
            :use-instance-in-tooltip="false"
            :enable-legend="outlierSeries || hideCounterName ? false : true"
            :server-side-zoom="false"
            :y-axis-label-width="100"
            date-time
          >
            <template v-slot="{ options }">
              <Chart
                ref="chartRef"
                :options="options"
                :sync-group="
                  mergedCounters.length || hasOverlaySeries
                    ? undefined
                    : syncGroup
                "
              />
            </template>
          </ChartOptions>
          <template v-if="invalidDropMessage">
            <div
              class="chart-not-allowed-overlay"
              @drop="MetricExplorerContext.resetDraggingContext"
            />
            <div
              class="chart-not-allowed-text"
              @drop="MetricExplorerContext.resetDraggingContext"
            >
              <h3>{{ invalidDropMessage }}</h3>
            </div>
          </template>
        </div>
        <div
          v-if="showAggrigationVertical && hasSeries"
          class="flex flex-col mr-4 rounded justify-around text-center bg-neutral-lightest"
          style="
            width: 20%;
            max-width: 88px;
            margin-right: 10px;
            margin-left: 10px;
          "
        >
          <div class="px-4">
            <div class="text-neutral">Min:</div>
            <div
              class="font-600 text-ellipsis text-primary"
              :title="
                overview[`${dotCounterName}.min.formatted`] ||
                overview[`${dotCounterName}.min`]
              "
            >
              {{
                overview[`${dotCounterName}.min.formatted`] ||
                overview[`${dotCounterName}.min`]
              }}
            </div>
          </div>
          <div class="px-4">
            <div class="text-neutral">Max:</div>
            <div
              class="font-600 text-ellipsis text-primary"
              :title="
                overview[`${dotCounterName}.max.formatted`] ||
                overview[`${dotCounterName}.max`]
              "
            >
              {{
                overview[`${dotCounterName}.max.formatted`] ||
                overview[`${dotCounterName}.max`]
              }}
            </div>
          </div>
          <div class="px-4">
            <div class="text-neutral">Avg:</div>
            <div
              class="font-600 text-ellipsis text-primary"
              :title="
                overview[`${dotCounterName}.avg.formatted`] ||
                overview[`${dotCounterName}.avg`]
              "
            >
              {{
                overview[`${dotCounterName}.avg.formatted`] ||
                overview[`${dotCounterName}.avg`]
              }}
            </div>
          </div>
          <!-- <div class="px-4">
            <div class="text-neutral">
              Last:
            </div>
            <div class="font-bold">
              {{ overview[`${dotCounterName}.last`] }}{{ currentUnit }}
            </div>
          </div>-->
        </div>
        <slot
          v-if="hasSeries"
          name="vertical_series_info"
          :counter="counter"
          :color="color"
          :overview="overview"
          :object-id="objectId"
        />
        <FlotoNoData
          v-else
          hide-svg
          header-tag="h5"
          variant="neutral"
          icon="exclamation-triangle"
        />
      </div>
      <slot
        v-if="hasSeries"
        name="default"
        :counter="counter"
        :color="color"
        :overview="overview"
        :object-id="objectId"
      />
    </FlotoContentLoader>
  </div>
</template>

<script>
import UniqBy from 'lodash/uniqBy'
import Omit from 'lodash/omit'

import { chartWorker } from '@/src/workers'
import { generateId } from '@utils/id'
import Bus from '@utils/emitter'
import { UserPreferenceComputed } from '@state/modules/user-preference'
// import { convertTimeLineForServer } from '@components/widgets/helper'
import {
  WidgetTypeConstants,
  QUERY_TYPE_OPTIONS,
} from '@components/widgets/constants'
import { getAllowedUnit } from '@utils/unit-checker'
import ChartOptions from '@components/chart/options/chart-options.vue'
import Chart from '@components/chart/chart.vue'
import ErrorInfo from '@components/error-info.vue'
import WidgetContextBuilder from '@components/widgets/widget-context-builder'

import { DEFAULT_ARITHMETIC_OPERATION_OPTIONS_NAME_MAP } from '@components/widgets/helper'

export default {
  name: 'MetricChart',
  components: {
    Chart,
    ChartOptions,
    ErrorInfo,
  },
  inject: {
    counterContext: { default: { options: new Map() } },
    monitorContext: { default: { options: new Map() } },
    SocketContext: { default: {} },
    MetricExplorerContext: {
      default: {
        register() {
          throw new Error(
            'Please use drilldown chart inside MetricExplorerContext'
          )
        },
        remove() {
          throw new Error(
            'Please use drilldown chart inside MetricExplorerContext'
          )
        },
        data: {},
      },
    },
  },
  props: {
    sortable: {
      type: Boolean,
      default: false,
    },
    objectId: {
      type: Number,
      required: true,
    },
    counter: {
      type: String,
      required: true,
    },
    instanceType: {
      type: String,
      default: undefined,
    },
    instance: {
      type: String,
      default: undefined,
    },
    event: {
      type: String,
      required: true,
    },
    timeline: {
      type: Object,
      required: true,
    },
    chartOptions: {
      type: Object,
      default() {
        return {}
      },
    },
    chartType: {
      type: String,
      default() {
        return 'line'
      },
    },
    color: {
      type: String,
      default: undefined,
    },
    syncGroup: {
      type: String,
      default: undefined,
    },
    unit: {
      type: String,
      default() {
        if (this.counter.indexOf('percent') >= 0) {
          return '%'
        }
        return undefined
      },
    },
    title: {
      type: String,
      default: undefined,
    },
    allowClose: {
      type: Boolean,
      default: false,
    },
    showAggrigationVertical: {
      type: Boolean,
      default: false,
    },
    seriesKeyBuilder: {
      type: Function,
      default: undefined,
    },
    mergable: {
      type: Boolean,
      // eslint-disable-next-line
      default: true,
    },
    overlayOptions: {
      type: Object,
      default() {
        return {}
      },
    },
    rangeDifference: {
      type: Number,
      default: 0,
    },
    removeSeriesOnRemove: {
      type: Boolean,
      // eslint-disable-next-line
      default: true,
    },
    stacked: {
      type: Boolean,
      default: false,
    },
    currentArithmeticOperation: {
      type: String,
      default: undefined,
    },
    hideCounterName: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      chartGuid: generateId(),
      gaugeGuid: generateId(),
      overlayUUID: generateId(),
      chartKey: 1,
      loading: true,
      overview: {},
      height: undefined,
      currentUnit: undefined,
      mergedCounters: [],
      anomalySeries: undefined,
      forecastSeries: undefined,
      outlierSeries: undefined,
      policySeries: undefined,
      monitor: undefined,
      error: null,
      DEFAULT_ARITHMETIC_OPERATION_OPTIONS_NAME_MAP:
        DEFAULT_ARITHMETIC_OPERATION_OPTIONS_NAME_MAP,
    }
  },
  computed: {
    ...UserPreferenceComputed,
    hasOverlaySeries() {
      return Boolean(this.overlayOptions.type)
    },
    granularity() {
      return this.MetricExplorerContext.granularity
    },
    seriesKey() {
      if (this.seriesKeyBuilder) {
        return this.seriesKeyBuilder(this.$props)
      }
      return `${this.objectId}-${this.counter}${
        this.instance ? `-${this.instance}` : ''
      }`
    },
    chartSeries() {
      if (this.outlierSeries) {
        return this.outlierSeries
      }
      const series = this.MetricExplorerContext.data[this.seriesKey]
      if (!series) {
        return []
      }
      const mergedSeries = this.mergedCounters
        .map((c) => this.MetricExplorerContext.data[c.seriesKey])
        .filter(Boolean)

      const allSeries = [
        ...(Array.isArray(series) ? series : [series]),
        ...mergedSeries,
      ]
      if (mergedSeries.length) {
        return allSeries.map((c) => ({
          ...c,
          name: `${c.name} ${c.monitor ? `(${c.monitor.name})` : ''} ${
            c.instance ? `(${c.instance})` : ''
          }`,
        }))
      }
      return allSeries
    },
    hasSeries() {
      return Boolean(this.MetricExplorerContext.data[this.seriesKey])
    },
    counterName() {
      if (this.counterContext.options.has(this.counter)) {
        return this.counter
      }
      return this.counter.replace(/\.(min|max|last|avg)$/, '')
    },
    dotCounterName() {
      return this.counterName.replace(/[~^]/g, '.')
    },
    invalidDropMessage() {
      return (
        this.MetricExplorerContext.draggingContext &&
        this.MetricExplorerContext.draggingContext.invalidMessage &&
        this.MetricExplorerContext.draggingContext.invalidMessage.target ===
          this.counter &&
        this.MetricExplorerContext.draggingContext.invalidMessage.message
      )
    },
    chartWidget() {
      const builder = new WidgetContextBuilder()
      builder.addGroup('metric')
      if (this.overlayOptions.type) {
        builder.appendToGroup('aiops', { category: 'metric' })
      }
      if (this.granularity.queryType === QUERY_TYPE_OPTIONS.RAW) {
        builder.appendToGroup('metric', {
          additionalUntouchedRequestChunk: {
            'join.type': 'custom',
            'join.result': 'raw',
          },
        })
      }
      builder.addCounterToGroup({
        counter: this.counterName,
        aggrigateFn:
          this.granularity.queryType === QUERY_TYPE_OPTIONS.RAW &&
          !this.overlayOptions.type
            ? '__NONE__'
            : 'avg',
        statisticalFunc: this.currentArithmeticOperation,
      })
      builder.addEntities('Monitor', [this.objectId])
      if (this.overlayOptions.type === 'Forecast') {
        builder.setCategory(WidgetTypeConstants.FORECAST)
      } else if (this.overlayOptions.type === 'Anomaly') {
        builder.setCategory(WidgetTypeConstants.ANOMALY)
      } else {
        builder.setCategory(WidgetTypeConstants.CHART)
      }
      builder.setWidgetType(WidgetTypeConstants.LINE)
      builder.setTimeLine(this.timeline)
      builder.setGranularity({ ...Omit(this.granularity || {}, ['queryType']) })
      if (this.instanceType && this.instance) {
        builder.addInstance(`${this.instanceType}~instance.name`, this.instance)
      }

      builder.setWidgetProperties({
        legendEnabled: true,
      })

      return builder
    },
  },
  watch: {
    'MetricExplorerContext.data': function (newValue) {
      const keys = Object.keys(newValue).filter((key) => newValue[key])
      this.mergedCounters = this.mergedCounters.filter((c) =>
        keys.includes(c.seriesKey)
      )
    },
    mergedCounters(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.adjustChartHeight()

        if (this.$refs.chartRef) {
          this.$refs.chartRef.resetPointer()
        }
        if ((newValue || []).length === 0 && (oldValue || []).length > 0) {
          if (this.$refs.chartRef) {
            this.$refs.chartRef.showSeries()
          }
        }
      }
    },
    timeline(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.loading = true
        this.MetricExplorerContext.remove(this.seriesKey, true)
        this.requestChartData()
      }
    },
    'MetricExplorerContext.granularity'(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.loading = true
        this.MetricExplorerContext.remove(this.seriesKey, true)
        this.requestChartData()
      }
    },
    overlayOptions(newValue, oldValue) {
      if (newValue !== oldValue) {
        if (!newValue.type) {
          this.MetricExplorerContext.remove(this.seriesKey)
          this.requestChartData()
        }
      }
    },
    stacked(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.chartKey++
      }
    },
    'monitorContext.options'(newValue, oldValue) {
      if (newValue !== oldValue) {
        if (this.monitorContext.options.has(this.objectId)) {
          this.monitor = Object.freeze(
            this.monitorContext.options.get(this.objectId)
          )
        }
      }
    },
  },
  mounted() {
    if (this.objectId) {
      if (this.monitorContext.options.has(this.objectId)) {
        this.monitor = Object.freeze(
          this.monitorContext.options.get(this.objectId)
        )
      }
    }
    Bus.$on('socket:connected', this.requestChartData)

    this.$once('hook:beforeDestroy', () => {
      Bus.$off(this.event, this.onDataReceived)
      Bus.$off('socket:connected', this.startStreaming)
    })
    if (this.SocketContext.connected) {
      this.requestChartData()
    }
    Bus.$on(this.event, this.onDataReceived)

    this.height = parseInt(getComputedStyle(this.$el).height)
    // if (!this.showAggrigationVertical) {
    this.height = this.height - 40
    // }

    if (this.hideCounterName) {
      this.height = this.height - 25
    }
  },
  beforeDestroy() {
    if (this.removeSeriesOnRemove) {
      this.MetricExplorerContext.remove(this.seriesKey)
    }
  },
  methods: {
    dragEnd(event) {
      this.MetricExplorerContext.resetDraggingContext()
    },
    dragStart(event) {
      event.dataTransfer.effectAllowed = 'copy'
      this.MetricExplorerContext.setDraggingContext({
        type: 'series-merge',
        seriesKey: this.seriesKey,
        objectId: this.objectId,
        counter: this.counter,
        unit: this.currentUnit,
        rangeDifference: this.rangeDifference,
        color: this.color,
        ...(this.currentArithmeticOperation
          ? {
              displayName: `${
                this.currentArithmeticOperation
                  ? `${
                      DEFAULT_ARITHMETIC_OPERATION_OPTIONS_NAME_MAP[
                        this.currentArithmeticOperation
                      ] || ''
                    } (`
                  : ''
              } ${this.title || `${this.dotCounterName}`} ${
                this.currentArithmeticOperation ? ')' : ''
              }`,
            }
          : {}),
      })
      event.dataTransfer.setDragImage(this.$refs.chartDivRef, 0, 0)
    },
    allowDropSeries(ev) {
      if (['Anomaly', 'Forecast'].includes(this.overlayOptions?.type)) {
        return this.MetricExplorerContext.setInvalidMessage({
          target: this.counter,
          message: `Unable to merge trend with ${this.overlayOptions?.type}`,
        })
      }
      const mergedKeys = this.mergedCounters.map(({ seriesKey }) => seriesKey)
      if (
        this.MetricExplorerContext &&
        this.MetricExplorerContext.draggingContext &&
        this.MetricExplorerContext.draggingContext.data.seriesKey !==
          this.seriesKey &&
        mergedKeys.includes(
          this.MetricExplorerContext.draggingContext.data.seriesKey
        ) === false
      ) {
        if (this.currentUnit) {
          if (
            this.MetricExplorerContext.draggingContext.data.type ===
              'series-merge' &&
            this.currentUnit ===
              this.MetricExplorerContext.draggingContext.data.unit
          ) {
            ev.preventDefault()
          } else if (
            this.MetricExplorerContext.draggingContext.data.type ===
              'add-series-to-metric-explorer' &&
            this.currentUnit ===
              this.MetricExplorerContext.draggingContext.data.counter.unit
          ) {
            ev.preventDefault()
          } else {
            this.MetricExplorerContext.setInvalidMessage({
              target: this.counter,
              message: `Unable to merge trend with unit ${this.currentUnit}`,
            })
          }
        } else {
          ev.preventDefault()
        }
      }
    },
    dropSeries(ev) {
      if (
        this.MetricExplorerContext &&
        this.MetricExplorerContext.draggingContext
      ) {
        ev.preventDefault()
        if (
          this.MetricExplorerContext.draggingContext.data.type ===
          'series-merge'
        ) {
          const series = this.MetricExplorerContext.draggingContext.data
          if (!this.mergedCounters.includes(series.counter)) {
            this.mergedCounters = UniqBy(
              [...this.mergedCounters, series],
              'seriesKey'
            )

            this.MetricExplorerContext.mergeCountersToChart(
              this.mergedCounters,
              this.seriesKey
            )
          }
        } else if (
          this.MetricExplorerContext.draggingContext.data.type ===
          'add-series-to-metric-explorer'
        ) {
          const context =
            this.MetricExplorerContext.draggingContext.data.counter
          if (context.counter === this.counter) {
            return
          }
          this.$emit(
            'add-counter-to-explorer',
            this.MetricExplorerContext.draggingContext.data.counter
          )
          this.mergedCounters = UniqBy(
            [
              ...this.mergedCounters,
              {
                color: context.color,
                counter: context.key,
                objectId: context.objectId || this.objectId,
                rangeDifference: this.rangeDifference,
                seriesKey: `${context.objectId || this.objectId}-${
                  context.key
                }${context.instance ? `-${context.instance}` : ''}`,
                unit: context.unit,
              },
            ],
            'seriesKey'
          )

          this.MetricExplorerContext.mergeCountersToChart(
            this.mergedCounters,
            this.seriesKey
          )
        }
      }
      this.MetricExplorerContext.resetDraggingContext()
    },
    handleRemoveCounter(seriesKey) {
      this.mergedCounters = this.mergedCounters.filter(
        (c) => c.seriesKey !== seriesKey
      )
      this.MetricExplorerContext.mergeCountersToChart(
        this.mergedCounters,
        this.seriesKey
      )
    },
    requestGaugeData() {
      const builder = new WidgetContextBuilder()
      builder.addGroup('metric')
      builder.addCounterToGroup({
        counter: this.counterName,
        aggrigateFn: 'avg',
      })
      builder.addEntities('Monitor', [this.objectId])
      builder.setCategory(WidgetTypeConstants.CHART)
      builder.setWidgetType(WidgetTypeConstants.LINE)
      builder.setTimeLine(this.timeline)
      if (this.instanceType && this.instance) {
        builder.addInstance(`${this.instanceType}~instance.name`, this.instance)
      }
      builder.setCategory(WidgetTypeConstants.GRID)
      builder.addCounterToGroup({
        counter: this.counterName,
        aggrigateFn: 'min',
      })
      builder.addCounterToGroup({
        counter: this.counterName,
        aggrigateFn: 'max',
      })
      builder.addCounterToGroup({
        counter: this.counterName,
        aggrigateFn: 'last',
      })
      builder.addEntities('Monitor', [this.objectId])
      builder.addResultBy('monitor')

      // overview event
      Bus.$emit('server:event', {
        'event.type': this.event,
        'event.context': {
          [this.$constants.UI_EVENT_UUID]: this.gaugeGuid,
          id: -1,
          ...builder.generateWidgetDefinition(),
        },
      })
    },
    requestChartData(startLoading = true) {
      this.requestGaugeData()
      const series = this.MetricExplorerContext.data[this.seriesKey]
      if (series) {
        this.loading = false
        this.currentUnit =
          series.unit || this.unit || getAllowedUnit(series.counter)
        this.MetricExplorerContext.replaceData(this.seriesKey, {
          unit: this.currentUnit,
          color: this.color,
        })
        return
      }
      if (startLoading) {
        this.loading = true
      }

      Bus.$emit('server:event', {
        'event.type': this.event,
        'event.context': {
          [this.$constants.UI_EVENT_UUID]: this.chartGuid,
          id: -1,
          ...this.chartWidget.generateWidgetDefinition({
            ...(this.granularity.queryType === QUERY_TYPE_OPTIONS.RAW
              ? {
                  'join.type': 'custom',
                  'join.result': 'raw',
                }
              : {}),
          }),
          ...(['Forecast', 'Anomaly'].includes(this.overlayOptions.type)
            ? { 'visualization.result.type': 1 }
            : {}),
        },
      })
    },
    async onDataReceived({ result, ...rest }) {
      if (!result) {
        return
      }
      if (rest['visualization.category'] === WidgetTypeConstants.GRID) {
        if (rest[this.$constants.UI_EVENT_UUID] !== this.gaugeGuid) {
          return
        }
        this.overview =
          ((result[WidgetTypeConstants.GRID] || {}).data || [])[0] || {}

        return
      } else if (
        [
          WidgetTypeConstants.CHART,
          WidgetTypeConstants.FORECAST,
          WidgetTypeConstants.ANOMALY,
        ].includes(rest['visualization.category'])
      ) {
        if (rest[this.$constants.UI_EVENT_UUID] !== this.chartGuid) {
          return
        }
      } else {
        return
      }
      const seriesData = (
        (result[WidgetTypeConstants.CHART] || {}).series || []
      ).filter((s) => !s.seriesType)
      const series = seriesData[0]
      if (!series) {
        this.loading = false
        return
      }
      const color = this.color
      this.currentUnit =
        series.unit || this.unit || getAllowedUnit(series.counter)
      this.MetricExplorerContext.register(this.seriesKey, {
        name: this.dotCounterName,
        monitor: this.monitor,
        counter: series.counter,
        instance: this.instance,
        unit: series.unit || this.unit,
        rangeDifference: this.rangeDifference,
        ...(color ? { color } : {}),
        data: series.data,
        entity: this.objectId,
        formattedValues: series.formattedValues || [],
        mergedCounters: this.MetricExplorerContext.getMergeCountersData(
          this.seriesKey
        ),
        // zoomInfo: {
        //   individualZoom: true,
        //   params: {
        //     'entity.id': this.objectId,
        //     counter: this.counter,
        //   },
        //   serverEvent: this.event,
        // },
      })
      if (this.overlayOptions.type) {
        if (this.overlayOptions.type === 'Forecast') {
          const forecast = await chartWorker.getForecastSeries(
            ((result[WidgetTypeConstants.CHART] || {}).series || []).filter(
              (s) => s.seriesType === 'forecast'
            )[0]
          )
          this.forecastSeries = Object.freeze(forecast)
        } else if (this.overlayOptions.type === 'Anomaly') {
          const { range } = await chartWorker.buildAnomalyRangeSeries(
            ((result[WidgetTypeConstants.CHART] || {}).series || []).filter(
              (s) => s.seriesType === 'anomaly'
            )[0].data,
            `${this.counterName}.avg`,
            this.color
          )
          this.anomalySeries = Object.freeze(range)
        }
      }
      this.$nextTick(() => {
        this.loading = false
        this.$emit('series-registered')

        // this.mergedCounters = this.MetricExplorerContext.getMergeCountersData(
        //   this.seriesKey
        // )

        setTimeout(() => {
          this.mergedCounters = this.MetricExplorerContext.getMergeCountersData(
            this.seriesKey
          )
        }, 1000)
      })
    },
    removeChart() {
      this.$emit('remove')
      this.MetricExplorerContext.mergeCountersToChart([], this.seriesKey)
    },
    handleRemoveChart() {
      if (this.currentArithmeticOperation) {
        this.$emit('apply-chart-changes', {
          selectedArithmeticOperation: undefined,
        })
      } else if (this.overlayOptions?.type) {
        this.$emit('apply-chart-changes', {
          overlayOptions: undefined,
        })
      } else {
        this.removeChart()
      }
    },
    adjustChartHeight() {
      this.$nextTick(() => {
        const mergedCountersContainer = this.$refs.mergedCountersRef
        const chartContainer = this.$refs.chartContainerRef

        if (mergedCountersContainer && chartContainer) {
          const mergedCountersHeight = mergedCountersContainer.offsetHeight
          const containerHeight = this.$el.offsetHeight

          // Adjust chart height based on mergedCounters height
          const newChartHeight = containerHeight - mergedCountersHeight // Adjust padding/margin as needed
          if (newChartHeight > 0) {
            this.height = newChartHeight
          }
        }
      })
    },
  },
}
</script>
