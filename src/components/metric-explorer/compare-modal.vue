<template>
  <MModal
    open
    :width="width"
    overlay-class-name="scrollable-modal hide-footer compare-metric-modal"
    @cancel="onCancel"
  >
    <template v-slot:trigger>
      <span />
    </template>
    <template v-slot:title>
      <div class="flex items-center">
        <div class="flex-1">
          <slot name="title">
            <h4 class="mb-0 text-primary">
              {{ counter }} of {{ monitor.name }} ({{
                monitor.ip || monitor.target
              }})
            </h4>
          </slot>
        </div>
        <TimeRangePicker
          v-model="appliedTimeline"
          :hide-custom-time-range="false"
        />
        <MButton
          variant="neutral-lightest"
          :shadow="false"
          shape="circle"
          class="squared-button ml-2"
          @click="onCancel"
        >
          <MIcon name="times" class="text-neutral-light" />
        </MButton>
      </div>
    </template>
    <FlotoScrollView>
      <div style="height: 50%; min-height: 250px">
        <MetricChart
          :title="`Current ${counter} ${unit ? `(${unit})` : ''}`"
          :allow-close="false"
          :counter="counter"
          :object-id="objectId"
          :chart-options="chartOptions"
          chart-type="line"
          :instance="instance"
          :instance-type="instanceType"
          :overlay-options="overlayOptions"
          :unit="unit"
          :timeline="appliedTimeline"
          :mergable="false"
          :color="color"
          :event="$constants.UI_WIDGET_RESULT_EVENT"
          :range-difference="rangeDifference"
          sync-group="compareSyncedChart"
          :series-key-builder="currentSeriesKeyBuilder"
          hide-counter-name
        >
          <template v-slot:chart-actions>
            <span class="text-neutral">
              From:
              {{ getDate(appliedTimelineFormatted.startDate) }} |
              {{ appliedTimelineFormatted.startTime }} to
              {{ getDate(appliedTimelineFormatted.endDate) }} |
              {{ appliedTimelineFormatted.endTime }}
            </span>
          </template>
          <template v-slot="slotProps">
            <slot name="current-chart-info" v-bind="slotProps" />
          </template>
        </MetricChart>
      </div>
      <div style="height: 50%; min-height: 250px">
        <MetricChart
          :title="`Previous ${counter} ${unit ? `(${unit})` : ''}`"
          :allow-close="false"
          :counter="counter"
          :object-id="objectId"
          :chart-options="chartOptions"
          chart-type="line"
          :instance="instance"
          :instance-type="instanceType"
          :unit="unit"
          :overlay-options="overlayOptions"
          :timeline="previousTimeline"
          :range-difference="rangeDifference * -1"
          :mergable="false"
          :color="color"
          :event="$constants.UI_WIDGET_RESULT_EVENT"
          sync-group="compareSyncedChart"
          :series-key-builder="previousSeriesKeyBuilder"
          hide-counter-name
        >
          <template v-slot:chart-actions>
            <span class="text-neutral">
              From: {{ getDate(previousTimeline.startDate) }} |
              {{ previousTimeline.startTime }} to
              {{ getDate(previousTimeline.endDate) }} |
              {{ previousTimeline.endTime }}
            </span>
          </template>
          <template v-slot="slotProps">
            <slot name="previous-chart-info" v-bind="slotProps" />
          </template>
        </MetricChart>
      </div>
    </FlotoScrollView>
  </MModal>
</template>

<script>
import Moment from 'moment'
import CloneDeep from 'lodash/cloneDeep'
import { objectDBWorker } from '@/src/workers'
import MetricChart from '@components/metric-explorer/metric-chart.vue'
import TimeRangePicker from '@components/widgets/time-range-picker.vue'
import { getRange } from '@components/widgets/helper'
import { TIME_FORMAT, DATE_FORMAT } from '@components/widgets/constants'

export default {
  name: 'CompareModal',
  components: {
    MetricChart,
    TimeRangePicker,
  },
  props: {
    objectId: {
      type: Number,
      required: true,
    },
    counter: {
      type: String,
      required: true,
    },
    instanceType: {
      type: String,
      default: undefined,
    },
    instance: {
      type: String,
      default: undefined,
    },
    timeline: {
      type: Object,
      required: true,
    },
    chartOptions: {
      type: Object,
      default() {
        return {}
      },
    },
    color: {
      type: String,
      default: undefined,
    },
    unit: {
      type: String,
      default: undefined,
    },
    overlayOptions: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  data() {
    return {
      width: window.innerWidth - 200,
      monitor: {},
      appliedTimeline: CloneDeep(this.timeline),
    }
  },
  computed: {
    rangeDifference() {
      const previousTimeline = this.previousTimeline
      const appliedTimeline = this.appliedTimelineFormatted
      return appliedTimeline.startDate - previousTimeline.startDate
    },
    appliedTimelineFormatted() {
      if (this.appliedTimeline) {
        let range
        if (this.appliedTimeline.selectedKey === 'custom') {
          range = this.appliedTimeline
        } else {
          range = getRange(this.appliedTimeline.selectedKey)
        }
        return {
          ...this.appliedTimeline,
          ...range,
        }
      }
      return {}
    },
    previousTimeline() {
      if (this.appliedTimeline) {
        let range
        if (this.appliedTimeline.selectedKey === 'custom') {
          range = this.appliedTimeline
        } else {
          range = getRange(this.appliedTimeline.selectedKey)
        }
        const diff = range.endDate - range.startDate
        const startDate = range.startDate - diff
        return {
          selectedKey: 'custom',
          endDate: range.startDate,
          endTime: range.startTime,
          startDate,
          startTime: Moment.unix(startDate / 1000).format(TIME_FORMAT),
        }
      }
      return {}
    },
  },
  created() {
    this.getMonitor()
  },
  methods: {
    async getMonitor() {
      const monitor = await objectDBWorker.getObjectById(this.objectId)
      if (monitor) {
        this.monitor = Object.freeze(monitor)
      }
    },
    getDate(d) {
      return Moment.unix(d / 1000).format(DATE_FORMAT)
    },
    currentSeriesKeyBuilder(props) {
      return `compare-${props.objectId}-${props.counter}${
        props.instance ? `-${props.instance}` : ''
      }`
    },
    previousSeriesKeyBuilder(props) {
      return `compare-previous-${props.objectId}-${props.counter}${
        props.instance ? `-${props.instance}` : ''
      }`
    },
    onCancel() {
      this.$emit('cancel')
    },
  },
}
</script>
