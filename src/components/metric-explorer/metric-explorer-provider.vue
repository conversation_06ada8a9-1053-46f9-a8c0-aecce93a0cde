<script>
// import FindIndex from 'lodash/findIndex'
export default {
  name: 'MetricExplorerProvider',
  provide() {
    const MetricExplorerContext = {
      register: this.registerData,
      remove: this.removeData,
      removeAll: this.removeAll,
      replaceData: this.replaceData,
      requestCounterData: this.requestChartData,
      setDraggingContext: (item) => (this.draggingContext = { data: item }),
      setInvalidMessage: (invalidMessage) =>
        (this.draggingContext = { ...this.draggingContext, invalidMessage }),
      resetDraggingContext: () => (this.draggingContext = undefined),
      mergeCountersToChart: this.mergeCountersToChart,
      getMergeCountersData: this.getMergeCountersData,
      setGranularity: this.setGranularity,
      setMergeCountersData: this.setMergeCountersData,
      getAllMergeCountersMap: this.getAllMergeCountersMap,
    }
    Object.defineProperty(MetricExplorerContext, 'data', {
      enumerable: true,
      get: () => {
        return this.serieses
      },
    })
    Object.defineProperty(MetricExplorerContext, 'granularity', {
      enumerable: true,
      get: () => {
        return this.granularity
      },
    })
    Object.defineProperty(MetricExplorerContext, 'draggingContext', {
      enumerable: true,
      get: () => {
        return this.draggingContext
      },
    })
    return { MetricExplorerContext }
  },
  data() {
    return {
      serieses: {},
      mergedCounterMap: {},
      draggingContext: undefined,
      requestedCounters: {},
      granularity: { value: 5, unit: 'm' },
    }
  },
  beforeDestroy() {
    this.serieses = {}
  },
  methods: {
    setGranularity(newGranularity) {
      this.granularity = newGranularity
    },
    registerData(counter, data) {
      this.serieses = Object.freeze({
        ...this.serieses,
        [counter]: data,
      })
    },
    removeData(counter, isTimelineChange) {
      if (!isTimelineChange) {
        const mergedCounterMapKeys = Object.keys(this.mergedCounterMap)
        mergedCounterMapKeys.forEach((seriesKey) => {
          if (this.mergedCounterMap[seriesKey]) {
            this.mergedCounterMap[seriesKey].forEach((mergedCounter) => {
              if (mergedCounter.seriesKey === counter) {
                this.mergedCounterMap = Object.freeze({
                  ...this.mergedCounterMap,
                  [seriesKey]: this.mergedCounterMap[seriesKey].filter(
                    (data) => data.seriesKey !== counter
                  ),
                })
              }
            })
          }
        })
      }

      this.serieses = Object.freeze({
        ...this.serieses,
        [counter]: undefined,
      })
    },
    removeAll() {
      this.serieses = {}
    },
    replaceData(counter, data) {
      if (this.serieses[counter]) {
        this.serieses = Object.freeze({
          ...this.serieses,
          [counter]: {
            ...this.serieses[counter],
            ...data,
          },
        })
      }
    },
    mergeCountersToChart(mergedCounters, seriesKey) {
      this.mergedCounterMap = Object.freeze({
        ...this.mergedCounterMap,
        [seriesKey]: mergedCounters,
      })
    },
    getMergeCountersData(seriesKey) {
      if (this.mergedCounterMap[seriesKey]) {
        return this.mergedCounterMap[seriesKey]
      }
      return []
    },
    setMergeCountersData(context) {
      this.mergedCounterMap = context || {}
    },
    getAllMergeCountersMap() {
      return this.mergedCounterMap
    },
  },
  render() {
    return this.$scopedSlots.default()
  },
}
</script>
