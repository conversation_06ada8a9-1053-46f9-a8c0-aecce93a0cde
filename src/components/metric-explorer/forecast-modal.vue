<template>
  <MModal
    open
    :width="width"
    centered
    overlay-class-name="hide-footer"
    @cancel="onCancel"
  >
    <template v-slot:trigger>
      <span />
    </template>
    <template v-slot:title>
      <div class="flex items-center">
        <div class="flex-1">
          <slot name="title">
            <h4 class="mb-0 text-primary">
              {{ counter.replace(/[~^]/g, '.') }} of {{ monitor.name }} ({{
                monitor.ip || monitor.target
              }})
            </h4>
          </slot>
        </div>
        <TimeRangePicker
          v-model="appliedTimeline"
          :hide-custom-time-range="false"
        />
        <div>
          <SaveAsWidget :save-widget="handleSaveAsWidget">
            <template v-slot:trigger>
              <MButton
                class="mx-2 model-header-button primary"
                variant="primary"
                >Save as Widget</MButton
              >
            </template>
          </SaveAsWidget>
        </div>
        <MButton
          variant="neutral-lightest"
          :shadow="false"
          title="Stick Back"
          shape="circle"
          class="squared-button ml-2"
          @click="onApplyChartChanges"
        >
          <MIcon name="chart-sync" class="text-neutral-light" />
        </MButton>
        <MButton
          variant="neutral-lightest"
          :shadow="false"
          shape="circle"
          class="squared-button ml-2"
          @click="onCancel"
        >
          <MIcon name="times" class="text-neutral-light" />
        </MButton>
      </div>
    </template>
    <div style="height: 70vh; min-height: 250px" class="flex flex-col">
      <div class="flex items-center justify-between">
        <div>
          <!-- <FlotoFormItem label="Algorithm" class="mr-4">
          <MRadioGroup
            v-model="algorithm"
            as-button
            :options="algorithmOptions"
          ></MRadioGroup>
        </FlotoFormItem>
        <MCheckbox v-model="overlayAlert">Overlay alert</MCheckbox> -->
        </div>
      </div>
      <div class="flex flex-1 flex-col min-h-0">
        <MetricChart
          ref="metricChartReference"
          :title="`${counter} ${unit ? `(${unit})` : ''}`"
          :allow-close="false"
          :counter="counter"
          :object-id="objectId"
          :chart-options="chartOptions"
          chart-type="line"
          :instance="instance"
          :instance-type="instanceType"
          :overlay-options="overlayOptions"
          :unit="unit"
          :timeline="appliedTimeline"
          :mergable="false"
          :color="color"
          :event="$constants.UI_WIDGET_RESULT_EVENT"
          sync-group="compareSyncedChart"
          :series-key-builder="currentSeriesKeyBuilder"
          hide-counter-name
        >
          <template v-slot="slotProps">
            <slot name="default" v-bind="slotProps" />
          </template>
        </MetricChart>
      </div>
    </div>
  </MModal>
</template>

<script>
import CloneDeep from 'lodash/cloneDeep'
import { objectDBWorker } from '@/src/workers'
import MetricChart from '@components/metric-explorer/metric-chart.vue'
import TimeRangePicker from '@components/widgets/time-range-picker.vue'
import SaveAsWidget from '@components/widgets/save-as-widget.vue'
import { createWidgetApi } from '@components/widgets/widgets-api'

export default {
  name: 'ForecastModal',
  components: {
    MetricChart,
    TimeRangePicker,
    SaveAsWidget,
  },
  props: {
    objectId: {
      type: Number,
      required: true,
    },
    counter: {
      type: String,
      required: true,
    },
    instanceType: {
      type: String,
      default: undefined,
    },
    instance: {
      type: String,
      default: undefined,
    },
    timeline: {
      type: Object,
      required: true,
    },
    chartOptions: {
      type: Object,
      default() {
        return {}
      },
    },
    color: {
      type: String,
      default: undefined,
    },
    unit: {
      type: String,
      default: undefined,
    },
  },
  data() {
    this.algorithmOptions = ['Linear', 'Seasonal'].map((c) => ({
      text: c,
      value: c,
    }))
    return {
      width: window.innerWidth - 200,
      monitor: {},
      appliedTimeline: CloneDeep(this.timeline),
      overlayOptions: {
        type: 'Forecast',
        algorithm: 'Linear',
        overlayAlert: false,
      },
    }
  },
  computed: {
    algorithm: {
      get() {
        return this.overlayOptions.algorithm
      },
      set(algorithm) {
        this.overlayOptions = {
          ...this.overlayOptions,
          algorithm,
        }
      },
    },
    overlayAlert: {
      get() {
        return this.overlayOptions.overlayAlert
      },
      set(overlayAlert) {
        this.overlayOptions = {
          ...this.overlayOptions,
          overlayAlert,
        }
      },
    },
  },
  created() {
    this.getMonitor()
  },
  methods: {
    async getMonitor() {
      const monitor = await objectDBWorker.getObjectById(this.objectId)
      if (monitor) {
        this.monitor = Object.freeze(monitor)
      }
    },
    currentSeriesKeyBuilder(props) {
      return `anomaly-${props.objectId}-${props.counter}${
        props.instance ? `-${props.instance}` : ''
      }`
    },
    onCancel() {
      this.$emit('cancel')
    },
    handleSaveAsWidget(data) {
      return createWidgetApi({
        ...this.$refs.metricChartReference.chartWidget.getContext(),
        ...data,
      })
    },
    onApplyChartChanges() {
      this.$emit('apply-chart-changes', { overlayOptions: this.overlayOptions })

      this.onCancel()
    },
  },
}
</script>
