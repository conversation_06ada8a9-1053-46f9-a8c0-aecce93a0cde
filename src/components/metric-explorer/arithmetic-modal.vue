<template>
  <MModal
    open
    :width="width"
    centered
    overlay-class-name="hide-footer"
    @cancel="onCancel"
  >
    <template v-slot:trigger>
      <span />
    </template>
    <template v-slot:title>
      <div class="flex items-center">
        <div class="flex-1">
          <slot name="title">
            <h4 class="mb-0 text-primary">
              {{ counter.replace(/[~^]/g, '.') }} of {{ monitor.name }} ({{
                monitor.ip || monitor.target
              }}) ({{ selectedArithmeticOperationName }})
            </h4>
          </slot>
        </div>
        <TimeRangePicker
          v-model="appliedTimeline"
          :hide-custom-time-range="false"
        />
        <div>
          <SaveAsWidget :save-widget="handleSaveAsWidget">
            <template v-slot:trigger>
              <MButton
                class="mx-2 model-header-button primary"
                variant="primary"
              >
                <span>Save as Widget</span>
              </MButton>
            </template>
          </SaveAsWidget>
        </div>
        <MButton
          :shadow="false"
          class="squared-button mr-2"
          :rounded="false"
          variant="neutral-lightest"
          title="Stick Back"
          @click="onApplyChartChanges"
        >
          <MIcon name="chart-sync" />
        </MButton>
        <MButton
          :shadow="false"
          shape="circle"
          class="squared-button mr-2"
          variant="neutral-lightest"
          @click="onCancel"
        >
          <MIcon name="times" class="text-neutral-light" />
        </MButton>
      </div>
    </template>
    <div style="height: 70vh; min-height: 250px" class="flex flex-col">
      <div class="flex flex-1 flex-col min-h-0">
        <MetricChart
          ref="metricChartReference"
          :title="`${counter} ${unit ? `(${unit})` : ''}`"
          :allow-close="false"
          :counter="counter"
          :object-id="objectId"
          :chart-options="chartOptions"
          chart-type="line"
          :instance="instance"
          :instance-type="instanceType"
          :unit="unit"
          :timeline="appliedTimeline"
          :mergable="false"
          :color="color"
          :event="$constants.UI_WIDGET_RESULT_EVENT"
          sync-group="compareSyncedChart"
          :series-key-builder="currentSeriesKeyBuilder"
          :current-arithmetic-operation="selectedArithmeticOperation"
          hide-counter-name
        >
          <template v-slot="slotProps">
            <slot name="default" v-bind="slotProps" />
          </template>
        </MetricChart>
      </div>
    </div>
  </MModal>
</template>

<script>
import CloneDeep from 'lodash/cloneDeep'
import { objectDBWorker } from '@/src/workers'
import MetricChart from '@components/metric-explorer/metric-chart.vue'
import TimeRangePicker from '@components/widgets/time-range-picker.vue'
import SaveAsWidget from '@components/widgets/save-as-widget.vue'
import { createWidgetApi } from '@components/widgets/widgets-api'
import { DEFAULT_ARITHMETIC_OPERATION_OPTIONS_NAME_MAP } from '@components/widgets/helper'

export default {
  name: 'ArithmeticModal',
  components: {
    MetricChart,
    TimeRangePicker,
    SaveAsWidget,
  },
  props: {
    objectId: {
      type: Number,
      required: true,
    },
    counter: {
      type: String,
      required: true,
    },
    instanceType: {
      type: String,
      default: undefined,
    },
    instance: {
      type: String,
      default: undefined,
    },
    timeline: {
      type: Object,
      required: true,
    },
    chartOptions: {
      type: Object,
      default() {
        return {}
      },
    },
    color: {
      type: String,
      default: undefined,
    },
    unit: {
      type: String,
      default: undefined,
    },
    selectedArithmeticOperation: {
      type: String,
      default: undefined,
    },
  },
  data() {
    this.DEFAULT_ARITHMETIC_OPERATION_OPTIONS_NAME_MAP =
      DEFAULT_ARITHMETIC_OPERATION_OPTIONS_NAME_MAP

    return {
      width: window.innerWidth - 200,
      monitor: {},
      appliedTimeline: CloneDeep(this.timeline),
    }
  },
  computed: {
    selectedArithmeticOperationName() {
      return DEFAULT_ARITHMETIC_OPERATION_OPTIONS_NAME_MAP[
        this.selectedArithmeticOperation
      ]
    },
  },

  created() {
    this.getMonitor()
  },
  methods: {
    async getMonitor() {
      const monitor = await objectDBWorker.getObjectById(this.objectId)
      if (monitor) {
        this.monitor = Object.freeze(monitor)
      }
    },
    currentSeriesKeyBuilder(props) {
      return `arithmetic-${props.selectedArithmeticOperation}-${
        props.objectId
      }-${props.counter}${props.instance ? `-${props.instance}` : ''}`
    },
    onCancel() {
      this.$emit('cancel')
    },
    handleSaveAsWidget(data) {
      return createWidgetApi({
        ...this.$refs.metricChartReference.chartWidget.getContext(),
        ...data,
      })
    },
    onApplyChartChanges() {
      this.$emit('apply-chart-changes', {
        selectedArithmeticOperation: this.selectedArithmeticOperation,
      })

      this.onCancel()
    },
  },
}
</script>
