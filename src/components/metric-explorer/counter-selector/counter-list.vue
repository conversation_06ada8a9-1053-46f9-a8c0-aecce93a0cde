<template>
  <div class="flex flex-1 min-h-0 flex-col">
    <div class="mb-2">
      <MInput
        v-model="searchTerm"
        class="search-box"
        placeholder="Search"
        name="search"
        style="max-width: unset"
      >
        <template v-slot:prefix>
          <MIcon name="search" />
        </template>
        <template v-if="searchTerm" v-slot:suffix>
          <MIcon
            name="times-circle"
            class="text-neutral-light cursor-pointer"
            @click="searchTerm = undefined"
          />
        </template>
      </MInput>
    </div>
    <div class="flex flex-1 min-h-0 flex-col overflow-auto">
      <RecycleScroller
        :items="filteredList"
        :item-size="40"
        key-field="counterName"
      >
        <template v-slot="{ item }">
          <div style="height: 40px" class="item-container">
            <div class="py-1 flex flex-col h-full">
              <div class="flex items-center py-1 h-full px-1">
                <!-- <Severity
                  :key="item.counterName"
                  :object-id="objectId"
                  :instance="instance"
                  :counter="item.counterName"
                  class="mr-1"
                /> -->
                <span
                  v-if="canAddCounter"
                  variant="transparent"
                  class="cursor-pointer"
                  :shadow="false"
                  shape="circle"
                  @click="addCounter(item)"
                >
                  <MIcon name="plus-circle" class="text-neutral-light mr-1" />
                </span>
                <div
                  class="flex-1 min-w-0 text-ellipsis"
                  :title="item.counterName"
                >
                  <span class="text-neutral">
                    {{ item.counterName }}
                  </span>
                </div>
                <span
                  v-if="canAddCounter"
                  variant="transparent"
                  class="cursor-move p-3"
                  :shadow="false"
                  shape="circle"
                  draggable="true"
                  @dragstart="dragStart($event, item)"
                  @dragend="dragEnd"
                >
                  <MIcon name="drag-arrows" class="text-neutral-light" />
                </span>
              </div>
            </div>
          </div>
        </template>
      </RecycleScroller>
    </div>
  </div>
</template>

<script>
import UniqBy from 'lodash/uniqBy'
import Trim from 'lodash/trim'
import { getAllowedUnit } from '@utils/unit-checker'
// import Severity from '@components/severity.vue'

export default {
  name: 'CounterList',
  components: {
    // Severity,
  },
  inject: {
    MetricExplorerContext: {
      default: {
        register() {
          throw new Error(
            'Please use drilldown chart inside MetricExplorerContext'
          )
        },
        remove() {
          throw new Error(
            'Please use drilldown chart inside MetricExplorerContext'
          )
        },
        data: {},
      },
    },
  },
  props: {
    objectId: {
      type: Number,
      default: undefined,
    },
    list: {
      type: Array,
      default() {
        return []
      },
    },
    canAddCounter: {
      type: Boolean,
      default: false,
    },
    instanceType: {
      type: String,
      default: undefined,
    },
    instance: {
      type: String,
      default: undefined,
    },
    chartParams: {
      type: Object,
      default: undefined,
    },
  },
  data() {
    return {
      searchTerm: undefined,
    }
  },
  computed: {
    uniqList() {
      return UniqBy(this.list, 'counterName')
    },
    filteredList() {
      if (this.searchTerm) {
        return this.uniqList.filter(
          (item) => item.name.indexOf(Trim(this.searchTerm).toLowerCase()) >= 0
        )
      }
      return this.uniqList
    },
  },
  methods: {
    dragEnd(event) {
      this.MetricExplorerContext.resetDraggingContext()
    },
    addCounter(counter) {
      this.$emit('add-counter', {
        ...counter,
        ...this.chartParams,
        objectId: this.objectId,
        ...(this.instanceType ? { instanceType: this.instanceType } : {}),
        ...(this.instance ? { instance: this.instance } : {}),
        unit: getAllowedUnit(counter.counterName),
        counter: counter.counterName,
      })
    },
    dragStart(event, item) {
      event.dataTransfer.effectAllowed = 'copy'
      this.MetricExplorerContext.setDraggingContext({
        type: 'add-series-to-metric-explorer',
        counter: {
          ...item,
          ...(this.chartParams || {}),
          ...(this.instanceType ? { instanceType: this.instanceType } : {}),
          ...(this.instance ? { instance: this.instance } : {}),
          counter: item.counterName,
          unit: getAllowedUnit(item.counterName),
          objectId: this.objectId,
        },
      })
      event.dataTransfer.setDragImage(
        event.target.closest('.item-container'),
        0,
        0
      )
    },
  },
}
</script>
