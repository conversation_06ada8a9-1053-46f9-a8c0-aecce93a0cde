<template>
  <div class="flex flex-1 min-h-0 flex-col h-full">
    <template v-if="!forExecuteRunbook">
      <MTab v-if="allowInstanceSelection" v-model="tab" class="mb-2">
        <MTabPane key="metric" tab="Metric" />
        <MTabPane
          v-if="
            !defaultSelectedInstance && Object.keys(instanceCounters).length
          "
          key="instance"
          tab="Instance"
        />

        <MTabPane
          v-if="forMonitorTemplate && shodShowSavedView"
          key="saved-view"
          tab="Saved View"
        />
      </MTab>
    </template>

    <CounterList
      v-if="tab === 'metric' && !forExecuteRunbook"
      :list="plainCounters"
      :object-id="objectId"
      :chart-params="chartParams"
      :can-add-counter="canAddCounter"
      @add-counter="$emit('add-counter', $event)"
    />

    <MRow
      v-else-if="tab === 'saved-view'"
      class="min-h-0 flex-1 relative"
      :gutter="0"
    >
      <SavedMetricExplorersList
        for-device-template
        v-bind="$attrs"
        v-on="$listeners"
      />
    </MRow>

    <InstanceMetricProvider
      v-else
      ref="instanceMetricProvider"
      :instance="selectedInstance"
      :target="{ entities: [objectId], entityType: 'Monitor' }"
      group-type="metric"
    >
      <InstanceSelector
        :object-id="objectId"
        :can-add-counter="canAddCounter"
        :instance-counters="instanceCounters"
        :chart-params="chartParams"
        :category="category"
        :default-selected-instance-data="defaultSelectedInstanceData"
        :for-execute-runbook="forExecuteRunbook"
        @select-instance-type="handleSelectInstanceType"
        @add-counter="$emit('add-counter', $event)"
        @selected-instance="$emit('selected-instance', $event)"
      />
    </InstanceMetricProvider>
  </div>
</template>

<script>
import GroupBy from 'lodash/groupBy'
import CounterList from './counter-list.vue'
import InstanceSelector from './instance-selector.vue'
import InstanceMetricProvider from '@components/data-provider/instance-metric-provider.vue'

import SavedMetricExplorersList from '@modules/metric-explorer/components/saved-metric-explorers-list.vue'

export default {
  name: 'CounterSelector',
  components: {
    CounterList,
    InstanceSelector,
    InstanceMetricProvider,
    SavedMetricExplorersList,
  },
  inject: {
    counterContext: { default: { options: new Map() } },
    MetricExplorerSavedViewProvider: { default: { savedView: [] } },
  },
  props: {
    objectId: {
      type: Number,
      required: true,
    },
    canAddCounter: {
      type: Boolean,
      default: false,
    },
    allowInstanceSelection: {
      type: Boolean,
      default: false,
    },
    chartParams: {
      type: Object,
      default: undefined,
    },
    category: {
      type: String,
      default: undefined,
    },
    defaultSelectedInstance: {
      type: String,
      default: undefined,
    },
    defaultSelectedInstanceData: {
      type: Object,
      default: undefined,
    },
    forExecuteRunbook: {
      type: Boolean,
      default: false,
    },
    forMonitorTemplate: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      tab: 'metric',
      selectedInstance: this.defaultSelectedInstance,
    }
  },
  computed: {
    counterOptions() {
      const dataType = ['numeric']
      if (dataType) {
        return Array.from(this.counterContext.options.values()).filter((o) =>
          (o.dataType || []).includes('numeric')
        )
      }
      return Array.from(this.counterContext.options.values())
    },
    instanceCounters() {
      const groupedCounters = GroupBy(this.counterOptions, 'instanceType')

      const finalContext = {}

      Object.keys(groupedCounters).forEach((instanceKey) => {
        if (instanceKey !== 'undefined' && instanceKey) {
          finalContext[instanceKey] = groupedCounters[instanceKey]
        }
      })

      // return Object.keys(groupedCounters).reduce(
      //   (map, instanceKey) =>
      //     instanceKey !== 'undefined' && instanceKey
      //       ? { ...map, [instanceKey]: groupedCounters[instanceKey] }
      //       : map,
      //   {}
      // )

      return finalContext
    },
    plainCounters() {
      if (this.selectedInstance) {
        return this.instanceCounters[this.selectedInstance] || []
      }
      const instances = Object.keys(this.instanceCounters)
      return this.counterOptions.filter(
        (c) => !c.instanceType && instances.includes(c.counterName) === false
      )
    },
    shodShowSavedView() {
      return this.MetricExplorerSavedViewProvider?.savedView?.length
    },
  },
  watch: {
    tab(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.handleSelectInstanceType(undefined)

        if (
          newValue === 'instance' &&
          !this.selectedInstance &&
          this.defaultSelectedInstanceData
        ) {
          this.selectedInstance = this.defaultSelectedInstanceData.instanceType
        }
      }
    },
    shodShowSavedView(newValue, oldValue) {
      if (newValue !== oldValue) {
        if (!newValue) {
          this.tab = 'metric'
        }
      }
    },

    defaultSelectedInstanceData: {
      immediate: true,
      handler(newValue) {
        if (newValue) {
          this.tab = 'instance'
        }
      },
    },
  },
  mounted() {
    if (this.$route.query.instance) {
      const instance = JSON.parse(
        atob(decodeURIComponent(this.$route.query.instance))
      )
      this.tab = 'instance'
      this.$nextTick(() => {
        this.handleSelectInstanceType(instance.name)
      })
    } else if (this.defaultSelectedInstance) {
      this.handleSelectInstanceType(this.defaultSelectedInstance)
    }

    if (this.defaultSelectedInstanceData) {
      this.selectedInstance = this.defaultSelectedInstanceData?.instanceType
    }
  },
  methods: {
    handleSelectInstanceType(instanceType) {
      this.selectedInstance = instanceType
      if (this.$refs.instanceMetricProvider) {
        this.$refs.instanceMetricProvider.setSelectedCounter('dummy')
      }
    },
    setActiveTab(tab) {
      this.tab = tab
    },
  },
}
</script>
