<template>
  <div ref="dropdownContainerRef" class="flex flex-1 min-h-0 flex-col">
    <FlotoFormItem
      label="Instance Type"
      :rules="{ required: forExecuteRunbook }"
    >
      <FlotoDropdownPicker
        v-model="instanceType"
        :options="instanceTypeOptions"
        use-popover
        @change="$emit('select-instance-type', $event)"
      />
    </FlotoFormItem>
    <FlotoFormItem
      v-if="instanceType"
      label="Instances"
      :rules="{ required: forExecuteRunbook }"
    >
      <FlotoDropdownPicker
        v-model="instance"
        :multiple="forExecuteRunbook"
        use-popover
        :options="instanceOptions"
      />
    </FlotoFormItem>
    <template v-if="!forExecuteRunbook">
      <div v-if="instance">
        <MDivider class="m-0 text-primary-alt">
          <div>Metrics</div>
        </MDivider>
      </div>
      <CounterList
        v-if="instance"
        :list="counterList"
        :object-id="objectId"
        :can-add-counter="canAddCounter"
        :instance-type="instanceType"
        :chart-params="chartParams"
        :instance="instance"
        @add-counter="$emit('add-counter', $event)"
      />
    </template>
  </div>
</template>

<script>
import UniqBy from 'lodash/uniqBy'
import CounterList from './counter-list.vue'
import { getAllowedUnit } from '@/src/utils/unit-checker'

export default {
  name: 'InstanceSelector',
  components: {
    CounterList,
  },
  inject: {
    instanceMetricContext: { default: { options: { columns: [], data: [] } } },
  },
  props: {
    objectId: {
      type: Number,
      default: undefined,
    },
    canAddCounter: {
      type: Boolean,
      default: false,
    },
    instanceCounters: {
      type: Object,
      default() {
        return {}
      },
    },
    category: {
      type: String,
      default: undefined,
    },
    chartParams: {
      type: Object,
      default: undefined,
    },
    defaultSelectedInstanceData: {
      type: Object,
      default: undefined,
    },
    forExecuteRunbook: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    let instanceType
    if (this.category === this.$constants.SERVICE_CHECK) {
      instanceType = Object.keys(this.instanceCounters)[0]
    }
    if (instanceType) {
      this.$emit('select-instance-type', instanceType)
    }
    return {
      instanceType,
      instance: undefined,
    }
  },
  computed: {
    instanceTypeOptions() {
      return Object.keys(this.instanceCounters).map((instance) => ({
        key: instance,
        text: instance,
      }))
    },
    instanceOptions() {
      const instanceType = this.instanceType
      if (instanceType) {
        const options = this.instanceMetricContext.options.data
          .map((row) =>
            row[instanceType.toLowerCase()]
              ? {
                  value: row[instanceType.toLowerCase()],
                }
              : null
          )
          .filter(Boolean)
          .map((c) => ({
            counterName: c.value,
            key: c.value,
            text: c.value,
          }))
        return UniqBy(options, 'key')
      }
      return []
    },
    counterList() {
      return this.instanceCounters[this.instanceType] || []
    },
  },
  watch: {
    instanceType(newvalue, oldvalue) {
      if (newvalue !== oldvalue) {
        this.instance = undefined

        // if (this.defaultSelectedInstanceData) {
        //   this.instance = this.defaultSelectedInstanceData.instance
        // }
      }
    },
    instanceOptions(newValue) {
      if (newValue && newValue.length && !this.instance) {
        if (this.category === this.$constants.SERVICE_CHECK) {
          this.instance = newValue[0].counterName
        }
        if (this.defaultSelectedInstanceData) {
          this.instance = this.instanceOptions.find(
            (i) =>
              i.key.toLowerCase() ===
              this.defaultSelectedInstanceData?.instance?.toLowerCase()
          )?.key
        }
      }
    },
    defaultSelectedInstanceData: {
      // immediate: true,
      handler(newValue) {
        if (newValue) {
          this.instanceType = newValue.instanceType
          // this.instance = newValue.instance
          this.$emit('select-instance-type', this.instanceType)
        }
      },
    },
    instance(newValue) {
      if (newValue) {
        this.$emit('selected-instance', newValue)
      }
    },
  },
  mounted() {
    if (this.$route.query.instance) {
      const instance = JSON.parse(
        atob(decodeURIComponent(this.$route.query.instance))
      )
      this.instanceType = instance.name
      this.$nextTick(() => {
        this.instance = instance.value
      })
      this.$nextTick(() => {
        const counter = this.counterList.find(
          (c) => c.counterName === this.$route.query.counter
        )
        if (counter) {
          this.$emit('add-counter', {
            ...counter,
            ...this.chartParams,
            objectId: this.objectId,
            instanceType: this.instanceType,
            instance: this.instance,
            unit: getAllowedUnit(counter.counterName),
            counter: counter.counterName,
          })
        }
      })
    }
  },
  created() {
    if (this.defaultSelectedInstanceData) {
      this.instanceType = this.defaultSelectedInstanceData.instanceType
      this.instance = this.defaultSelectedInstanceData.instance

      this.$emit('select-instance-type', this.instanceType)
    }
  },
}
</script>
