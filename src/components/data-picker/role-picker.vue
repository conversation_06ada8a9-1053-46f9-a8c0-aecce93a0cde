<template>
  <SelectedItemPills v-if="disabled" :value="resolvedValue" v-bind="$attrs" />
  <span v-else class="role-name">
    <a
      v-if="allowCreate && hasRoleBaseAccess"
      id="create-new-role"
      class="text-right link-label"
      @click="openRoleSlider"
    >
      Create Role
    </a>
    <FlotoDropdownPicker
      :options="options"
      :value="value"
      v-bind="$attrs"
      :disabled="disabled"
      @change="handleChange"
      v-on="listeners"
    />

    <FlotoDrawerForm
      v-if="allowCreate"
      :open="openDrawer"
      width="50%"
      @submit="onCreateRoleSubmit"
      @cancel="hideDrawer"
      @reset="resetRoleForm"
    >
      <template v-slot:header> Create Role </template>
      <template v-slot:actions="{ submit, reset }">
        <MRow>
          <span class="mandatory"
            ><span class="text-secondary-red">*</span> fields are
            mandatory</span
          >
          <MCol class="text-right">
            <MButton
              id="create-role-btn"
              :loading="creatingRole"
              @click="submit"
            >
              Create Role
            </MButton>
            <MButton
              id="reset-role-btn"
              variant="default"
              class="ml-2"
              @click="reset"
            >
              Reset
            </MButton>
          </MCol>
        </MRow>
      </template>
      <RoleForm
        :value="roleEntity"
        :pre-applied-permissions="defaultPermissions"
        :update-fn="updateRoleEntity"
      />
    </FlotoDrawerForm>
  </span>
</template>

<script>
import { createRoleApi } from '@modules/settings/users-settings/roles-api'
import RoleForm from '@modules/settings/users-settings/components/role-form.vue'
import SelectedItemPills from '@components/dropdown-trigger/selected-item-pills.vue'
import { authComputed } from '@/src/state/modules/auth'

export default {
  name: 'RolePicker',
  components: { RoleForm, SelectedItemPills },
  inject: { roleContext: { default: { options: new Map() } } },
  model: {
    event: 'change',
  },
  props: {
    mandatory: { type: Boolean, default: false },
    value: { type: [Number, Array], default: undefined },
    allowCreate: { type: Boolean, default: false },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    this.defaultPermissions = [
      'my-account-settings:read',
      'my-account-settings:read-write',
      'dashboards:read',
      'widgets:read',
      'monitor-settings:read',
      'group-settings:read',
      'user-settings:read',
    ]
    return {
      openDrawer: null,
      creatingRole: false,
      roleEntity: {
        adminAccess: false,
      },
    }
  },
  computed: {
    ...authComputed,
    resolvedValue() {
      if (!this.disabled) {
        return
      }
      const optionsMap = this.roleContext.options
      const value = Array.isArray(this.value) ? this.value : [this.value]
      return value
        .filter(Boolean)
        .map((v) => (optionsMap.has(v) ? optionsMap.get(v).name : undefined))
    },
    options() {
      if (this.disabled) {
        return []
      }
      return Array.from(this.roleContext.options.values())
    },
    listeners() {
      const { change, ...listeners } = this.$listeners
      return listeners
    },
    hasRoleBaseAccess() {
      return this.hasLicensePermission(
        this.$constants.ROLE_BASE_ACCESS_LICENSE_PERMISSION
      )
    },
  },
  methods: {
    handleChange(value) {
      this.$emit('change', value)
    },
    openRoleSlider() {
      this.openDrawer = true
      this.roleEntity = {
        adminAccess: false,
        permissions: this.defaultPermissions,
      }
    },
    hideDrawer() {
      this.openDrawer = null
      this.roleEntity = {
        adminAccess: false,
      }
    },
    resetRoleForm() {
      this.roleEntity = {
        adminAccess: false,
        permissions: this.defaultPermissions,
      }
    },
    async onCreateRoleSubmit() {
      this.creatingRole = true
      try {
        const role = await createRoleApi(this.roleEntity)
        this.creatingRole = false
        this.$emit('change', role.id)
        this.openDrawer = null
        if (this.roleContext.add) {
          this.roleContext.add({
            key: role.id,
            name: role.roleName,
          })
        }
      } catch (e) {
        this.creatingRole = false
      }
    },
    updateRoleEntity(value) {
      this.roleEntity = {
        ...this.roleEntity,
        ...value,
      }
    },
  },
}
</script>
