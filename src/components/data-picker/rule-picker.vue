<template>
  <div
    v-if="useStrip"
    class="flex p-2"
    :class="{ 'cursor-pointer': allowView }"
    @click="onViewRule"
  >
    <SeverityStripe :severity="resolvedValue.ruleSeverity" />
    <span
      class="h-full mr-2 text-center text-sm flex items-center justify-center"
      >{{ countIndex }} {{ resolvedValue.name }}
    </span>

    <FlotoDrawer
      :open="showRuleFor !== null"
      :scrolled-content="false"
      @hide="showRuleFor = null"
    >
      <template v-if="showRuleFor" v-slot:title>
        <div class="flex min-w-0 h-full">
          <div style="min-height: 20px" class="flex">
            <SeverityStripe :severity="showRuleFor.ruleSeverity" />
          </div>

          <div v-if="showRuleFor">{{ showRuleFor.name }}</div>
        </div>
      </template>
      <ViewRule v-if="showRuleFor" :rule="showRuleFor" />
    </FlotoDrawer>
  </div>
  <SelectedItemPills
    v-else-if="disabled"
    :value="resolvedValue"
    v-bind="$attrs"
  />

  <FlotoDropdownPicker
    v-else
    :options="options"
    :value="value"
    :disabled="disabled"
    searchable
    :multiple="multiple"
    :disabled-options="disabledAssignedOptions ? defaultDisabledOptions : []"
    v-bind="$attrs"
    v-on="listeners"
    @change="changeSelection($event)"
  />
</template>

<script>
import SelectedItemPills from '@components/dropdown-trigger/selected-item-pills.vue'
import SeverityStripe from '@components/severity-stripe.vue'

import ViewRule from '@modules/settings/compliance-settings/components/view-rule.vue'

export default {
  name: 'RulePicker',
  components: { SelectedItemPills, SeverityStripe, ViewRule },
  inject: { ruleContext: { default: { options: new Map() } } },
  model: { event: 'change' },
  props: {
    value: { type: [Number, Array], default: undefined },
    multiple: { type: Boolean, default: false },
    disabled: {
      type: Boolean,
      default: false,
    },
    disabledAssignedOptions: {
      type: Boolean,
      default: false,
    },
    useStrip: {
      type: Boolean,
      default: false,
    },
    countIndex: {
      type: String,
      default: undefined,
    },
    allowView: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    this.defaultDisabledOptions = Object.freeze(this.value)
    return {
      openDrawer: null,
      showRuleFor: null,
    }
  },
  computed: {
    resolvedValue() {
      if (!this.disabled && !this.useStrip) {
        return
      }
      const optionsMap = this.ruleContext.options
      const value = Array.isArray(this.value) ? this.value : [this.value]

      if (this.useStrip) {
        if (optionsMap.has(this.value)) {
          return optionsMap.get(this.value)
        } else {
          return {}
        }
      }

      return value
        .filter(Boolean)
        .map((v) => (optionsMap.has(v) ? optionsMap.get(v).name : undefined))
    },

    options() {
      if (this.disabled) {
        return []
      }
      let items = Array.from(this.ruleContext.options.values())
      return items
    },
    listeners() {
      const { change, ...listeners } = this.$listeners
      return listeners
    },
  },
  methods: {
    changeSelection(e) {
      if (e.length <= 0) {
        this.$emit('change', this.defaultDisabledOptions)
      } else {
        this.$emit('change', e)
      }
    },
    onViewRule() {
      if (this.allowView) {
        this.showRuleFor = this.resolvedValue
      }
    },
  },
}
</script>
