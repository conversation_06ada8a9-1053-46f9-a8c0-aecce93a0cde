<template>
  <SelectedItemPills
    v-if="disabled && !asInput"
    :value="resolvedValue"
    v-bind="$attrs"
  />
  <FlotoDropdownPicker
    v-else
    :options="options"
    :value="value"
    v-bind="$attrs"
    :disabled="disabled"
    :searchable="searchable"
    :disabled-options="disabledOptions"
    :multiple="maxAllowedSelection !== 1"
    :as-input="asInput"
    @change="handleChange"
    v-on="listeners"
  />
</template>
<script>
import Uniq from 'lodash/uniq'
import SelectedItemPills from '@components/dropdown-trigger/selected-item-pills.vue'

export default {
  name: 'AgentPicker',
  components: { SelectedItemPills },
  inject: { agentContext: { default: { options: new Map() } } },
  model: {
    event: 'change',
  },
  props: {
    // eslint-disable-next-line
    searchable: { type: Boolean, default: true },
    value: { type: [Number, Array], default: undefined },
    maxAllowedSelection: { type: Number, default: 0 },
    multiple: { type: Boolean, default: false },
    asInput: { type: Boolean, default: false },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    resolvedValue() {
      if (!this.disabled) {
        return
      }
      const optionsMap = this.agentContext.options
      const value = Array.isArray(this.value) ? this.value : [this.value]
      return value
        .filter(Boolean)
        .map((v) => (optionsMap.has(v) ? optionsMap.get(v).name : undefined))
    },
    options() {
      // if (this.disabled) {
      //   return []
      // }
      return Array.from(this.agentContext.options.values())
    },
    disabledOptions() {
      const items = Array.from(this.agentContext.options.values())
      return items
        .filter((i) => i.state === this.$constants.STATE_DISABLE)
        .map(({ key }) => key)
    },
    listeners() {
      const { change, ...listeners } = this.$listeners
      return listeners
    },
  },
  methods: {
    handleChange(value) {
      if (this.multiple) {
        let uniqIds = Uniq(value)
        if (this.maxAllowedSelection) {
          value = uniqIds.slice(uniqIds.length - this.maxAllowedSelection)
        } else {
          value = uniqIds
        }
      }
      this.$emit('change', value)
    },
  },
}
</script>
