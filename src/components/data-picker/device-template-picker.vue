<template>
  <SelectedItemPills v-if="disabled" :value="resolvedValue" v-bind="$attrs" />
  <div v-else-if="useFormattedName">
    <MIcon
      :name="formatedVale ? 'file-alt' : 'document'"
      :class="{
        'text-primary-alt': formatedVale,
        'text-neutral-light': !formatedVale,
      }"
      class="mr-1"
    />
    <span
      :class="{
        'text-neutral-light': !formatedVale,
      }"
      >{{ formatedVale ? formatedVale : '[Not Assigned]' }}
    </span>
  </div>

  <div v-else-if="useGrid" class="flex flex-col flex-1 min-h-0" :gutter="0">
    <div class="mt-2">
      <MInput v-model="searchTerm" class="search-box" placeholder="Search">
        <template v-slot:prefix>
          <MIcon name="search" />
        </template>
        <template v-if="searchTerm" v-slot:suffix>
          <MIcon
            name="times-circle"
            class="text-neutral-light cursor-pointer"
            @click="searchTerm = undefined"
          />
        </template>
      </MInput>
    </div>
    <MGrid
      ref="gridRef"
      :data="options"
      :columns="columns"
      :search-term="searchTerm"
      selectable
      :max-allowed-selection="1"
      hide-selection-info
      :pre-selected-items="preSelectedItems"
      @selection-change="selectionChange"
    >
      <template v-slot:name="{ item }">
        <span class="text-primary"> {{ item.name }}</span>
      </template>

      <template v-slot:devices="{ item }">
        <UsedCounts
          class="text-center"
          :title="`Devices for ${item.name}`"
          :display-count="item.devices"
          :parent-resource-id="item.id"
          parent-resource-type="config-templates"
          :count-types="[
            {
              countType: 'Configuration',
              title: `Device Count`,
              resourceKey: 'Configuration',
            },
          ]"
        />
      </template>
    </MGrid>
  </div>

  <MRow v-else :gutter="0" class="w-full">
    <FlotoDropdownPicker
      :options="options"
      :value="templateId"
      searchable
      v-bind="$attrs"
      v-on="listeners"
      @change="$emit('change', $event)"
    />

    <MButton
      v-if="allowCreate"
      id="create-device-template-btn-id"
      outline
      class="text-right ml-4"
      @click="openCreateDeviceTemplateForm"
    >
      Create New Device Template
    </MButton>
  </MRow>
</template>

<script>
import SelectedItemPills from '@components/dropdown-trigger/selected-item-pills.vue'
import IsObject from 'lodash/isObject'
import UsedCounts from '@components/used-counts/used-counts.vue'

export default {
  name: 'DeviceTemplatePicker',
  components: {
    SelectedItemPills,
    UsedCounts,
  },
  inject: { deviceTemplateContext: { default: { options: new Map() } } },
  model: { event: 'change' },
  props: {
    value: { type: [Number, String, Array, Object], default: undefined },
    disabled: { type: Boolean, default: false },
    allowCreate: { type: Boolean, default: false },
    filters: {
      type: Object,
      default() {
        return {}
      },
    },
    useFormattedName: {
      type: Boolean,
      default: false,
    },
    useGrid: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    this.columns = [
      {
        key: 'name',
        name: 'Template',
        searchable: true,
        sortable: true,
      },
      {
        key: 'vendor',
        name: 'Vendor',
        searchable: true,
        sortable: true,
      },
      {
        key: 'os',
        name: 'OS Type',
        searchable: true,
        sortable: true,
      },
      {
        key: 'devices',
        name: 'Devices',
        searchable: true,
        sortable: true,
        align: 'center',
        width: '120px',
      },
    ]

    return {
      searchTerm: '',
      preSelectedItems: [this.value],
    }
  },
  computed: {
    resolvedValue() {
      const optionsMap = this.deviceTemplateContext.options
      const value = Array.isArray(this.templateId)
        ? this.templateId
        : [this.templateId]
      return value
        .filter(Boolean)
        .map((v) => (optionsMap.has(v) ? optionsMap.get(v).name : undefined))
    },
    formatedVale() {
      return (this.resolvedValue || [])[0]
    },
    listeners() {
      const { change, ...listeners } = this.$listeners
      return listeners
    },
    options() {
      let options = Array.from(this.deviceTemplateContext.options.values())
      if (this.disabled) {
        return []
      }
      if (Object.keys(this.filters).length) {
        Object.keys(this.filters).forEach((key) => {
          options = options.filter((o) => o[key] === this.filters[key])
        })
      }

      return options
    },

    templateId() {
      return IsObject(this.value) ? this.value.id : this.value
    },
  },
  methods: {
    openCreateDeviceTemplateForm() {
      this.$router.push(
        this.$modules.getModuleRoute('ncm-settings', 'create-device-template')
      )
    },
    selectionChange(ids) {
      this.$emit('change', ids[0])
    },
  },
}
</script>
