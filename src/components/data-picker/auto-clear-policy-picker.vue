<template>
  <FlotoDropdownPicker
    :options="options"
    :value="value"
    searchable
    v-bind="$attrs"
    v-on="listeners"
    @change="$emit('change', $event)"
  />
</template>

<script>
export default {
  name: 'AutoClearPolicyPicker',
  model: { event: 'change' },
  props: { value: { type: [Number], default: undefined } },
  computed: {
    listeners() {
      const { change, ...listeners } = this.$listeners
      return listeners
    },
    options() {
      return [
        { text: 'Never', key: 0, id: 'never' },
        { text: '30 Min', key: 30 * 60, id: '30min' },
        { text: '1 hour', key: 60 * 60, id: '1hour' },
        { text: '2 hour', key: 2 * 60 * 60, id: '2hour' },
        { text: '5 hour', key: 5 * 60 * 60, id: '5hour' },
        { text: '8 hour', key: 8 * 60 * 60, id: '8hour' },
        { text: '12 hour', key: 12 * 60 * 60, id: '12hour' },
        { text: '24 hour', key: 24 * 60 * 60, id: '24hour' },
      ]
    },
  },
}
</script>
