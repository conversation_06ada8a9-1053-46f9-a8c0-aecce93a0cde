<template>
  <MRow v-if="useTagFilter">
    <MCol :size="6">
      <FlotoFormItem label="Benchmark Filter by Tags">
        <FlotoDropdownPicker
          class="mt-1"
          :options="tagOptions"
          :value="value.tags"
          :disabled="disabled"
          searchable
          multiple
          :disabled-options="
            disabledAssignedOptions ? defaultDisabledOptions : []
          "
          v-bind="$attrs"
          v-on="listeners"
          @change="changeTags($event)"
        />
      </FlotoFormItem>
    </MCol>
    <MCol :size="6">
      <FlotoFormItem label="Benchmark" rules="required">
        <FlotoDropdownPicker
          :options="filteredOptions"
          :value="value.benchmark"
          :disabled="disabled"
          searchable
          :multiple="multiple"
          :disabled-options="
            disabledAssignedOptions ? defaultDisabledOptions : []
          "
          v-bind="$attrs"
          v-on="listeners"
          @change="changeBenchmark($event)"
        />
      </FlotoFormItem>
    </MCol>
  </MRow>
  <SelectedItemPills
    v-else-if="disabled"
    :value="resolvedValue"
    v-bind="$attrs"
  />

  <FlotoDropdownPicker
    v-else
    :options="options"
    :value="value"
    :disabled="disabled"
    searchable
    :multiple="multiple"
    :disabled-options="disabledAssignedOptions ? defaultDisabledOptions : []"
    v-bind="$attrs"
    v-on="listeners"
    @change="changeSelection($event)"
  />
</template>

<script>
import SelectedItemPills from '@components/dropdown-trigger/selected-item-pills.vue'
import Flatten from 'lodash/flatten'
import Uniq from 'lodash/uniq'

export default {
  name: 'BenchmarkPicker',
  components: { SelectedItemPills },
  inject: { benchmarkContext: { default: { options: new Map() } } },
  model: { event: 'change' },
  props: {
    value: { type: [Number, Array, Object], default: undefined },
    multiple: { type: Boolean, default: false },
    disabled: {
      type: Boolean,
      default: false,
    },
    disabledAssignedOptions: {
      type: Boolean,
      default: false,
    },
    useTagFilter: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    this.defaultDisabledOptions = Object.freeze(this.value)
    return {
      openDrawer: null,
    }
  },
  computed: {
    resolvedValue() {
      if (!this.disabled) {
        return
      }
      const optionsMap = this.benchmarkContext.options
      const value = Array.isArray(this.value) ? this.value : [this.value]
      return value
        .filter(Boolean)
        .map((v) => (optionsMap.has(v) ? optionsMap.get(v).name : undefined))
    },

    options() {
      if (this.disabled && !this.useTagFilter) {
        return []
      }
      let items = Array.from(this.benchmarkContext.options.values())
      return items
    },

    filteredOptions() {
      if (this.value?.tags?.length > 0) {
        return this.options.filter((benchmark) =>
          (benchmark?.tags || []).find((t) => this.value?.tags?.includes(t))
        )
      }

      return this.options
    },

    tagOptions() {
      let items = Uniq(
        Flatten(
          Array.from(this.benchmarkContext.options.values()).map((b) => b.tags)
        )
      )
        .filter(Boolean)
        .map((tag) => ({
          key: tag,
          name: tag,
        }))
      return items
    },

    listeners() {
      const { change, ...listeners } = this.$listeners
      return listeners
    },
  },
  methods: {
    changeSelection(e) {
      if (e.length <= 0) {
        this.$emit('change', this.defaultDisabledOptions)
      } else {
        this.$emit('change', e)
      }
    },
    changeBenchmark(benchmark) {
      this.$emit('change', {
        ...this.value,
        benchmark,
      })
    },
    changeTags(tags) {
      this.$emit('change', {
        ...this.value,
        tags,
        benchmark: undefined,
      })
    },
  },
}
</script>
