<template>
  <div
    class="flex flex-wrap items-center flex-1"
    :class="{ 'justify-around': !disableJustifyAround }"
  >
    <template v-if="disabled && tags.length === 0 && showEmpty">---</template>
    <FlotoForm
      v-if="isAddMode"
      :key="renderKeyWhenAlwaysTextMode"
      ref="form"
      :show-notification="false"
      layout="vertical"
      class="flex-grow flex flex-1 flex-shrink-0 no-padding-form"
      style="min-width: 120px"
      @submit="handleAddTag"
    >
      <div class="option-text relative flex-1" :class="{ 'w-full': fullWidth }">
        <MValidationProvider
          ref="tagTextField"
          slim
          :rules="validationRules"
          :name="title || tagType"
          mode="aggressive"
        >
          <MFormItem
            slot-scope="{
              errors,
              valid,
              validated,
              pending,
              pristine,
              failedRules,
            }"
            :error="errors[0]"
            :class="{
              'form-item-pristine': pristine,
              'has-required-validation':
                validated && !pending && hasRequiredValidation(failedRules),
              'has-info-tooltip': infoTooltip,
            }"
            :validate-status="
              validated ? (valid ? 'success' : 'error') : undefined
            "
          >
            <MInput
              v-model="tagValue"
              :disabled="disabled"
              class="w-full"
              :type="isNumber ? 'number' : undefined"
              :max="max"
              :min="min"
              :precision="0"
              :size="size"
              :loading="loading"
              :auto-focus="!alwaysTextMode"
              :placeholder="title || tagType"
              @pressEnter="handleEnter"
            />
            <FlotoDropdownPicker
              ref="userDropdownRef"
              :options="userOptions"
              class="w-full absolute inset-0"
              style="z-index: -1"
              :searchable="false"
              @change="handleUserSelected"
            >
              <template v-slot:trigger>
                <div class="absolute inset-0 pointer-events-none"></div>
              </template>
            </FlotoDropdownPicker>

            <FlotoDropdownPicker
              ref="handlarDropdownRef"
              :options="handlersOptions"
              class="w-full absolute inset-0"
              style="z-index: -1"
              :searchable="false"
              @change="handleHandlersSelected"
            >
              <template v-slot:trigger>
                <div class="absolute inset-0 pointer-events-none"></div>
              </template>
            </FlotoDropdownPicker>
          </MFormItem>
        </MValidationProvider>
      </div>
      <template v-slot:submit>
        <div class="action-bar flex">
          <MButton
            v-if="!disabled"
            v-show="(alwaysTextMode && tagValue) || !alwaysTextMode"
            type="submit"
            variant="transparent"
            shape="circle"
            :size="size"
            :shadow="false"
          >
            <MIcon name="check" size="lg" class="text-secondary-green" />
          </MButton>
          <MButton
            v-if="!alwaysTextMode"
            type="submit"
            variant="transparent"
            shape="circle"
            :size="size"
            :shadow="false"
            @click="isAddMode = false"
          >
            <MIcon name="times" size="lg" class="text-secondary-red" />
          </MButton>
        </div>
      </template>
    </FlotoForm>
    <MTag
      v-if="!isAddMode && (!disabled || preview)"
      :variant="type === 'email' ? 'default' : variant"
      :closable="false"
      class="relative flex items-center"
      @click="!disabled ? (isAddMode = true) : undefined"
    >
      <MIcon name="plus" :size="size" class="mr-1" />
      Add
      {{ title || tagType }}
    </MTag>

    <div class="w-full flex flex-wrap items-center mt-2">
      <template v-for="tag in tags">
        <MTooltip
          :key="tag"
          :disabled="tag.length <= 20"
          style="line-height: 15px"
        >
          <template v-slot:trigger>
            <MTag
              :key="tag"
              :variant="type === 'email' ? 'default' : variant"
              :rounded="type === 'email' ? true : rounded"
              :closable="!disabled"
              :confirmable="confirmBeforeRemove"
              :class="{ 'cursor-auto': disabled }"
              class="mb-2"
              @close="handleRemoveTag(tag)"
            >
              <template v-slot:confirm-title>
                <p>
                  {{
                    $message('confirm', {
                      message: $message('delete_resource', {
                        resource: title || 'Tag',
                      }),
                    })
                  }}?
                </p>
              </template>
              {{ `${tag.length > 20 ? `${tag.slice(0, 20)}...` : tag}` }}
            </MTag>
          </template>
          {{ tag }}
        </MTooltip>
      </template>
    </div>
  </div>
</template>

<script>
import Trim from 'lodash/trim'
import UniqBy from 'lodash/uniqBy'

export default {
  name: 'FlotoTagsPicker',
  inject: {
    UserProviderContext: { default: { options: [] } },
    HandlerProviderContext: { default: { options: [] } },
  },

  model: {
    event: 'change',
  },
  props: {
    preview: {
      type: Boolean,
      default: false,
    },
    isNumber: { type: Boolean, default: false },
    max: { type: Number, default: undefined },
    min: { type: Number, default: undefined },
    disabled: { type: Boolean, default: false },
    showEmpty: { type: Boolean, default: false },
    value: { type: Array, default: undefined },
    // eslint-disable-next-line
    rounded: { type: Boolean, default: true },
    confirmBeforeRemove: { type: Boolean, default: false },
    title: { type: String, default: undefined },
    alwaysTextMode: { type: Boolean, default: false },
    variant: {
      type: String,
      default: 'default',
    },
    fullWidth: { type: Boolean, default: false },
    infoTooltip: { type: String, default: undefined },
    // eslint-disable-next-line
    sm: { type: Boolean, default: true },
    disableJustifyAround: { type: Boolean, default: false },
    disabledTeamIntegration: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isAddMode: !!this.alwaysTextMode,
      tagValue: this.isNumber ? 1 : undefined,
      tagOptions: [],
      renderKeyWhenAlwaysTextMode: 1,
      loading: false,
      type: 'user',
    }
  },
  computed: {
    userOptions() {
      return this.UserProviderContext.options.map((i) => ({
        key: i.userName,
        label: i.userName,
      }))
    },
    handlersOptions() {
      if (this.disabledTeamIntegration) {
        return []
      }
      return UniqBy(
        this.HandlerProviderContext?.options?.map((i) => ({
          key: i.name,
          label: i.name,
          id: i.id,
        })),
        'key'
      )
    },
    currentMode() {
      return (this.tagValue || '').startsWith('@')
        ? 'user'
        : (this.tagValue || '').startsWith('/')
        ? 'handlers'
        : 'email'
    },
    validationRules() {
      const rules = {
        required: !this.alwaysTextMode,
      }
      if (this.currentMode === 'email') {
        rules.email = true
      }
      return rules
    },
    tagType() {
      if (this.currentMode === 'email') {
        return 'email_plural'
      }
      if (this.currentMode === 'handlers') {
        return 'handlers'
      }
      return this.type
    },
    tags() {
      return this.value || []
    },
    size() {
      if (this.sm) {
        return 'small'
      }
      return undefined
    },
  },
  watch: {
    tagValue(newValue) {
      if (newValue && newValue.startsWith('@')) {
        this.$refs.handlarDropdownRef.handleHide()

        this.$refs.userDropdownRef.handleShow()
        this.$refs.userDropdownRef.searchQuery = newValue.substr(1)
      } else if (
        newValue &&
        newValue.startsWith('/') &&
        this.$refs.handlarDropdownRef
      ) {
        this.$refs.userDropdownRef.handleHide()

        this.$refs.handlarDropdownRef.handleShow()
        this.$refs.handlarDropdownRef.searchQuery = newValue.substr(1)
      } else {
        this.$refs.userDropdownRef.handleHide()
        this.$refs.handlarDropdownRef.handleHide()
      }
    },
    isAddMode(newValue) {
      if (!newValue) {
        this.tagValue = undefined
      }
    },
  },
  created() {
    const handler = () => {
      this.tagValue = this.isNumber ? 1 : undefined
    }
    this.$root.$on('reset', handler)
    this.$once('hook:beforeDestroy', () => {
      this.$root.$off('reset', handler)
    })
  },
  methods: {
    hasRequiredValidation(failedRules) {
      return Object.keys(failedRules || {}).indexOf('required') >= 0
    },
    handleAddTag() {
      if (!this.tagValue) {
        return
      }

      if (['/', '@'].includes(this.tagValue)) {
        this.$refs.tagTextField.setErrors([
          `${
            this.currentMode === 'user'
              ? '@ -'
              : this.currentMode === 'handlers'
              ? '/ -'
              : ''
          } Invalid ${
            this.currentMode === 'user'
              ? 'Username'
              : this.currentMode === 'handlers'
              ? 'Handle Name'
              : 'Email'
          }`,
        ])
        return
      }
      if (this.isDuplicate(this.tagValue)) {
        this.$refs.tagTextField.setErrors([
          `${
            this.title || (this.type === 'user' ? 'User' : 'Email')
          } with the same value already exists`,
        ])
        return
      }
      const updatedValue = [...this.tags, this.tagValue].filter(
        (t) => Trim(t) !== ''
      )
      this.$emit('change', updatedValue)
      if (this.alwaysTextMode) {
        this.$nextTick(() => {
          this.tagValue = undefined
          this.renderKeyWhenAlwaysTextMode += 1
        })
      } else {
        this.isAddMode = false
      }
    },
    isDuplicate(tag) {
      const t = this.tags.find((t) => t === tag)
      if (t) {
        return true
      }
      return false
    },
    handleRemoveTag(tag) {
      this.$nextTick(() => {
        const updatedValue = this.tags.filter((t) => t !== tag)
        this.$emit('change', updatedValue)
      })
    },
    handleAutocompleteSelect(value) {
      this.tagValue = value
      this.handleAddTag()
    },
    handleEnter(e) {
      e.stopPropagation()
      // if (['user'].indexOf(this.type) >= 0) {
      //   this.$nextTick(() => this.handleAddTag())
      // }
    },
    handleUserSelected(user) {
      this.tagValue = `@${user}`
      if (['user'].indexOf(this.type) >= 0) {
        this.$nextTick(() => this.handleAddTag())
      }
    },

    handleHandlersSelected(handler) {
      this.tagValue = `${handler}`
      if (['handler'].indexOf(this.type) >= 0) {
        this.$nextTick(() => this.handleAddTag())
      }
    },
  },
}
</script>
