<template>
  <FlotoDropdownPicker
    :options="options"
    :value="value"
    v-bind="$attrs"
    hide-all-dropdown-options
    :disabled-options="allDisabledOptions"
    :allow-clear="allowClear"
    :disabled="disabled"
    show-help
    :items-to-show="25"
    @change="handleChange"
    v-on="listeners"
  >
    <template v-slot:helpbox>
      <div style="white-space: nowrap">
        <span class="text-ellipsis">
          Please refine your search for more metrics</span
        >
      </div>
    </template>

    <template v-slot:after-menu-text="{ item }">
      <span class="ml-2">
        <MIcon v-if="item.instanceType" name="table" size="sm" />
      </span>
    </template>
  </FlotoDropdownPicker>
</template>

<script>
import UniqBy from 'lodash/uniqBy'

export default {
  name: 'CounterPicker',
  inject: { counterContext: { default: { options: new Map() } } },
  model: {
    event: 'change',
  },
  props: {
    value: { type: [Number, Array, Object, String], default: undefined },
    dataType: {
      type: Array,
      default: undefined,
    },
    rootCounterType: {
      type: [String, Array],
      default: undefined,
    },
    rootAggrigateFn: {
      type: String,
      default: undefined,
    },
    instanceType: {
      type: String,
      default: undefined,
    },
    disabledOptions: {
      type: Array,
      default() {
        return []
      },
    },
    scalarOnly: {
      type: Boolean,
      default: false,
    },
    instanceOnly: {
      type: Boolean,
      default: false,
    },
    allowClear: {
      type: Boolean,
      // eslint-disable-next-line
      default: true,
    },
    statusOnlyCounters: {
      type: Boolean,
      default: false,
    },
    ignoreStatusFilter: {
      type: Boolean,
      default: false,
    },
    disabled: { type: Boolean, default: false },

    filterFn: {
      type: Function,
      default: undefined,
    },
  },
  computed: {
    allDisabledOptions() {
      return ['instance', 'instance.id'].concat(this.disabledOptions)
    },
    options() {
      // const dataType = this.dataType
      // const rootDataType = this.rootCounterType
      // const rootAggrValue = this.rootAggrigateFn
      let counterArr = Array.from(this.counterContext.options.values())
      if (this.instanceType) {
        counterArr = counterArr.filter(
          (v) => v.instanceType === this.instanceType
        )
      }
      if (this.instanceOnly) {
        let crr = counterArr.filter(
          (o) => o.isStatusCounter && Boolean(o.instanceType)
        )
        counterArr = UniqBy(
          crr.map((e) => ({
            ...e,
            name: e.instanceType,
            key: e.instanceType,
          })),
          'key'
        )
      }
      if (this.scalarOnly) {
        counterArr = counterArr.filter((c) => !c.instanceType)
      }
      // if (
      //   rootDataType &&
      //   (rootDataType || []).includes('numeric') &&
      //   rootAggrValue !== 'last'
      // ) {
      //   counterArr = counterArr.filter((o) =>
      //     (o.dataType || []).some((i) => rootDataType.includes(i))
      //   )
      // }
      const dataType = this.dataType
      if (dataType) {
        counterArr = counterArr.filter((o) =>
          (o.dataType || []).some((i) => dataType.includes(i))
        )
      }
      if (this.instanceOnly) {
        return counterArr
      }

      if (!this.ignoreStatusFilter) {
        if (this.statusOnlyCounters) {
          counterArr = counterArr.filter((c) => c.isStatusCounter)
        } else {
          counterArr = counterArr.filter((c) => !c.isStatusCounter)
        }
      }

      if (this.filterFn) {
        return this.filterFn(this.counterContext.options)
      }

      return counterArr.filter((i) => i.instanceType !== i.key)
    },
    listeners() {
      const { change, ...listeners } = this.$listeners
      return listeners
    },
  },
  watch: {
    'counterContext.options': {
      immediate: true,
      handler(newValue, oldValue) {
        if (newValue.size > 0 && this.value) {
          const value = this.value
          const item = Array.from(newValue.values()).find(
            (i) => i.key === value
          )

          if (item) this.$emit('change', item)
        }
      },
    },
  },
  mounted() {
    if (
      this.counterContext.hasLoader &&
      this.counterContext.options.size > 0 &&
      this.value
    ) {
      const value = this.value
      let item = Array.from(this.counterContext.options.values()).find(
        (i) => i.key === value
      )

      if (this.filterFn) {
        item = this.filterFn(this.counterContext.options).find(
          (i) => i.key === value
        )
      }

      this.$emit('change', item)
    }
  },
  methods: {
    handleChange(value) {
      const item = this.options.find((i) => i.key === value)
      this.$emit('change', item)
    },
  },
}
</script>
