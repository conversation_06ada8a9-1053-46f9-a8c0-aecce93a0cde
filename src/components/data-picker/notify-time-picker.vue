<template>
  <FlotoDropdownPicker
    :options="options"
    :value="value"
    searchable
    v-bind="$attrs"
    v-on="listeners"
    @change="$emit('change', $event)"
  />
</template>

<script>
export default {
  name: 'NotifyTimePicker',
  model: { event: 'change' },
  props: { value: { type: [Number], default: undefined } },
  computed: {
    listeners() {
      const { change, ...listeners } = this.$listeners
      return listeners
    },
    options() {
      return [
        { text: '30 sec', key: 30, id: '30sec' },
        { text: '1 min', key: 60, id: '1min' },
        { text: '5 min', key: 5 * 60, id: '5min' },
        { text: '10 min', key: 10 * 60, id: '10min' },
        { text: '15 min', key: 15 * 60, id: '15min' },
        { text: '30 min', key: 30 * 60, id: '30min' },
        { text: '60 min', key: 60 * 60, id: '60min' },
      ]
    },
  },
}
</script>
