<template>
  <SelectedItemPills v-if="disabled" :value="resolvedValue" v-bind="$attrs" />
  <FlotoDropdownPicker
    v-else
    :options="options"
    :value="value"
    v-bind="$attrs"
    :disabled="disabled"
    :searchable="searchable"
    v-on="$listeners"
  />
</template>

<script>
import SelectedItemPills from '@components/dropdown-trigger/selected-item-pills.vue'

export default {
  name: 'LdapServerPicker',
  components: {
    SelectedItemPills,
  },
  inject: { ldapServerContext: { default: { options: new Map() } } },
  props: {
    // eslint-disable-next-line
    searchable: { type: Boolean, default: true },
    value: { type: [Number, Array], default: undefined },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    resolvedValue() {
      if (!this.disabled) {
        return
      }
      const optionsMap = this.ldapServerContext.options
      const value = Array.isArray(this.value) ? this.value : [this.value]
      return value
        .filter(Boolean)
        .map((v) => (optionsMap.has(v) ? optionsMap.get(v).name : undefined))
    },
    options() {
      if (this.disabled) {
        return []
      }
      return Array.from(this.ldapServerContext.options.values())
    },
  },
}
</script>
