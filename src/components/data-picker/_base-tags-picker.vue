<template>
  <div
    class="flex flex-wrap items-center"
    :class="{ 'justify-around': !disableJustifyAround }"
  >
    <template v-for="tag in tags">
      <MTooltip :key="tag" :disabled="tag.length <= 20">
        <template v-slot:trigger>
          <MTag
            :key="tag"
            :variant="type === 'email' ? 'default' : variant"
            class="mb-2"
            :rounded="type === 'email' ? true : rounded"
            :closable="!disabled"
            :confirmable="confirmBeforeRemove"
            :class="{ 'cursor-auto': disabled }"
            @close="handleRemoveTag(tag)"
          >
            <template v-slot:confirm-title>
              <p>
                {{
                  $message('confirm', {
                    message: $message('delete_resource', {
                      resource: title || 'Tag',
                    }),
                  })
                }}?
              </p>
            </template>
            {{ `${tag.length > 20 ? `${tag.slice(0, 20)}...` : tag}` }}
          </MTag>
        </template>
        {{ tag }}
      </MTooltip>
    </template>
    <template v-if="disabled && tags.length === 0 && showEmpty">---</template>
    <FlotoForm
      v-if="isAddMode"
      :key="renderKeyWhenAlwaysTextMode"
      ref="form"
      :show-notification="false"
      layout="vertical"
      class="flex-grow flex"
      @submit="handleAddTag"
    >
      <div class="option-text" :class="{ 'w-full': fullWidth }">
        <MValidationProvider
          ref="tagTextField"
          slim
          :rules="validationRules"
          :name="title || tagType"
          mode="aggressive"
        >
          <MFormItem
            slot-scope="{
              errors,
              valid,
              validated,
              pending,
              pristine,
              failedRules,
            }"
            :error="errors[0]"
            :class="{
              'form-item-pristine': pristine,
              'has-required-validation':
                validated && !pending && hasRequiredValidation(failedRules),
              'has-info-tooltip': infoTooltip,
            }"
            :validate-status="
              validated ? (valid ? 'success' : 'error') : undefined
            "
          >
            <MInput
              v-model="tagValue"
              :disabled="disabled"
              class="w-full"
              :type="isNumber ? 'number' : undefined"
              :max="max"
              :min="min"
              :precision="0"
              :size="size"
              :loading="loading"
              :auto-focus="!alwaysTextMode"
              :placeholder="title || tagType"
              @pressEnter="handleEnter"
            />
          </MFormItem>
        </MValidationProvider>
      </div>
      <template v-slot:submit>
        <div class="action-bar flex">
          <MButton
            v-if="!disabled"
            v-show="(alwaysTextMode && tagValue) || !alwaysTextMode"
            type="submit"
            variant="transparent"
            shape="circle"
            :size="size"
            :shadow="false"
          >
            <MIcon name="check" size="lg" class="text-secondary-green" />
          </MButton>
          <MButton
            v-if="!alwaysTextMode"
            type="submit"
            variant="transparent"
            shape="circle"
            :size="size"
            :shadow="false"
            @click="isAddMode = false"
          >
            <MIcon name="times" size="lg" class="text-secondary-red" />
          </MButton>
        </div>
      </template>
    </FlotoForm>
    <MTag
      v-if="!isAddMode && (!disabled || preview)"
      :variant="type === 'email' ? 'default' : variant"
      :closable="false"
      class="relative flex items-center"
      @click="!disabled ? (isAddMode = true) : undefined"
    >
      <MIcon name="plus" :size="size" class="mr-1" />
      Add
      {{ title || tagType }}
    </MTag>
  </div>
</template>

<script>
import Trim from 'lodash/trim'
export default {
  name: 'FlotoTagsPicker',
  model: {
    event: 'change',
  },
  props: {
    preview: {
      type: Boolean,
      default: false,
    },
    isNumber: { type: Boolean, default: false },
    max: { type: Number, default: undefined },
    min: { type: Number, default: undefined },
    disabled: { type: Boolean, default: false },
    showEmpty: { type: Boolean, default: false },
    value: { type: Array, default: undefined },
    // eslint-disable-next-line
    rounded: { type: Boolean, default: true },
    confirmBeforeRemove: { type: Boolean, default: false },
    type: { type: String, default: 'tag' },
    title: { type: String, default: undefined },
    alwaysTextMode: { type: Boolean, default: false },
    variant: {
      type: String,
      default: 'default',
    },
    fullWidth: { type: Boolean, default: false },
    infoTooltip: { type: String, default: undefined },
    // eslint-disable-next-line
    sm: { type: Boolean, default: true },
    disableJustifyAround: { type: Boolean, default: false },
  },
  data() {
    return {
      isAddMode: !!this.alwaysTextMode,
      tagValue: this.isNumber ? 1 : undefined,
      tagOptions: [],
      renderKeyWhenAlwaysTextMode: 1,
      loading: false,
    }
  },
  computed: {
    validationRules() {
      const rules = {
        required: !this.alwaysTextMode,
      }
      if (this.type === 'email') {
        rules.email = true
      }
      if (this.type === 'domain') {
        rules.domain = true
      }
      if (this.type === 'ip') {
        rules.ip_only = true
      }
      if (this.type === 'tag') {
        rules.max = 50
      }
      if (this.type === 'port') {
        rules.port = true
      }
      if (this.type === 'ip_range') {
        rules.ip_range = true
      }
      if (this.type === 'ip_or_ip_range') {
        rules.ip_or_ip_range = true
      }
      if (this.type === 'mobile_number') {
        rules.numeric = true
        rules.min = 8
        rules.max = 12
        rules.mobileNumber = true
      }
      if (this.type === 'custom_ip_or_ip_range') {
        rules.custom_ip_or_ip_range = true
      }
      return rules
    },
    tagType() {
      if (this.type === 'email') {
        return 'email_plural'
      }
      return this.type
    },
    tags() {
      return this.value || []
    },
    size() {
      if (this.sm) {
        return 'small'
      }
      return undefined
    },
  },
  watch: {
    isAddMode(newValue) {
      if (!newValue) {
        this.tagValue = undefined
      }
    },
  },
  created() {
    const handler = () => {
      this.tagValue = this.isNumber ? 1 : undefined
    }
    this.$root.$on('reset', handler)
    this.$once('hook:beforeDestroy', () => {
      this.$root.$off('reset', handler)
    })
  },
  methods: {
    hasRequiredValidation(failedRules) {
      return Object.keys(failedRules || {}).indexOf('required') >= 0
    },
    handleAddTag() {
      if (!this.tagValue) {
        return
      }
      if (this.isDuplicate(this.tagValue)) {
        this.$refs.tagTextField.setErrors([
          `${
            this.title || (this.type === 'tag' ? 'Tag' : 'Email')
          } with the same value already exists`,
        ])
        return
      }
      const updatedValue = [...this.tags, this.tagValue].filter(
        (t) => Trim(t) !== ''
      )
      this.$emit('change', updatedValue)
      if (this.alwaysTextMode) {
        this.$nextTick(() => {
          this.tagValue = undefined
          this.renderKeyWhenAlwaysTextMode += 1
        })
      } else {
        this.isAddMode = false
      }
    },
    isDuplicate(tag) {
      const t = this.tags.find((t) => t === tag)
      if (t) {
        return true
      }
      return false
    },
    handleRemoveTag(tag) {
      this.$nextTick(() => {
        const updatedValue = this.tags.filter((t) => t !== tag)
        this.$emit('change', updatedValue)
      })
    },
    handleAutocompleteSelect(value) {
      this.tagValue = value
      this.handleAddTag()
    },
    handleEnter(e) {
      e.stopPropagation()
      if (['tag'].indexOf(this.type) >= 0) {
        this.$nextTick(() => this.handleAddTag())
      }
    },
  },
}
</script>
