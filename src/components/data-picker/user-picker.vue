<template>
  <SelectedItemPills v-if="disabled" :value="resolvedValue" v-bind="$attrs" />
  <span v-else-if="!isGridSelector" class="role-name">
    <FlotoDropdownPicker
      :options="options"
      :value="value"
      v-bind="$attrs"
      :disabled="disabled"
      :disabled-options="disabledOptions"
      @change="handleChange"
      v-on="listeners"
    />
  </span>

  <span v-else>
    <FlotoDropdownGridSelector
      :options="options"
      :value="value"
      v-bind="$attrs"
      :disabled="disabled"
      :disabled-options="disabledOptions"
      :columns="columns"
      @change="handleChange"
      v-on="listeners"
    />
  </span>
</template>

<script>
import SelectedItemPills from '@components/dropdown-trigger/selected-item-pills.vue'
import { authComputed } from '@/src/state/modules/auth'

export default {
  name: 'UserPicker',
  components: { SelectedItemPills },
  inject: { UserProviderContext: { default: { options: new Map() } } },
  model: {
    event: 'change',
  },
  props: {
    mandatory: { type: Boolean, default: false },
    value: { type: [Number, Array], default: undefined },
    allowCreate: { type: Boolean, default: false },
    disabled: {
      type: Boolean,
      default: false,
    },
    disabledDefaultUser: {
      type: Boolean,
      default: false,
    },

    isGridSelector: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    this.columns = [
      {
        key: 'userName',
        name: 'Name',
        searchable: true,
        sortable: true,
      },
      {
        key: 'role',
        name: 'Role',
        searchable: true,
        sortable: true,
      },
      {
        key: 'userType',
        name: 'Type',
        searchable: true,
        sortable: true,
      },
      {
        key: 'groups',
        name: 'Group',
        searchable: true,
        sortable: true,
      },
    ]
    return {}
  },
  computed: {
    ...authComputed,
    resolvedValue() {
      if (!this.disabled) {
        return
      }
      const optionsMap = this.UserProviderContext.options
      const value = Array.isArray(this.value) ? this.value : [this.value]
      return value.filter(Boolean).map((v) => {
        if (Array.isArray(optionsMap)) {
          return optionsMap.find((o) => o.id === v)?.name
        }

        return optionsMap.has(v) ? optionsMap.get(v).name : undefined
      })
    },
    options() {
      if (this.disabled) {
        return []
      }
      return Array.from(this.UserProviderContext.options.values())
    },
    listeners() {
      const { change, ...listeners } = this.$listeners
      return listeners
    },
    disabledOptions() {
      return this.disabledDefaultUser
        ? this.options.filter((u) => u.type === '0').map((u) => u.id)
        : []
    },
  },
  methods: {
    handleChange(value) {
      this.$emit('change', value)
    },
  },
}
</script>
