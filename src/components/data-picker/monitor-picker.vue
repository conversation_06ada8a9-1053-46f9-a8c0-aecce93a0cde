<template>
  <SelectedItemPills v-if="disabled" :value="resolvedValue" v-bind="$attrs" />
  <FlotoDropdownPicker
    v-else
    ref="dropdownPickerRef"
    :options="finalOptions"
    :value="value"
    v-bind="$attrs"
    :disabled="disabled"
    :searchable="false"
    :multiple="multiple"
    remove-clear-btn
    overlay-class-name="picker-overlay grid-dropdown"
    :fixed-position="useBodyContainer"
    :get-popup-container="
      useBodyContainer ? getMonitorPickerContainer : undefined
    "
    avoid-keyboard-navigation
    @change="$emit('change', $event)"
    v-on="listeners"
  >
    <div class="mt-4 flex flex-col flex-1 min-h-0 px-4">
      <MonitorSelection
        :key="renderKey"
        :use-padding="false"
        :virtual-scroll="true"
        :max-allowed-selection="maxAllowedSelections || multiple ? 0 : 1"
        :value="
          multiple
            ? value
            : value
            ? Array.isArray(value)
              ? value
              : [value]
            : undefined
        "
        :fetch-fn="fetchMonitors"
        :filters="filters"
        :for-ncm-device="$attrs['for-ncm-device']"
        :default-mode="$attrs['default-mode']"
        @change="handleSelection"
      />
    </div>
  </FlotoDropdownPicker>
</template>

<script>
import UniqBy from 'lodash/uniqBy'
import MonitorSelection from '@components/item-selection/monitor-selection.vue'
import SelectedItemPills from '@components/dropdown-trigger/selected-item-pills.vue'

export default {
  name: 'MonitorPicker',
  components: { MonitorSelection, SelectedItemPills },
  inject: { monitorContext: { default: { options: new Map() } } },
  model: {
    event: 'change',
  },
  props: {
    options: { type: Array, default: undefined },
    useBodyContainer: { type: Boolean, default: false },
    multiple: { type: Boolean, default: false },
    filters: { type: [Array], default: undefined },
    // eslint-disable-next-line
    searchable: { type: Boolean, default: true },
    value: { type: [Number, Array, String], default: undefined },
    disabled: {
      type: Boolean,
      default: false,
    },
    maxAllowedSelections: {
      type: Number,
      default: 0,
    },
    excludedOptions: {
      type: Array,
      default: undefined,
    },
  },
  data() {
    return { renderKey: 1 }
  },
  computed: {
    resolvedValue() {
      if (!this.disabled) {
        return
      }
      const optionsMap = this.monitorContext.options
      const value = Array.isArray(this.value) ? this.value : [this.value]
      return value
        .filter(Boolean)
        .map((v) => (optionsMap.has(v) ? optionsMap.get(v).name : undefined))
    },
    finalOptions() {
      if (this.disabled) {
        return
      }
      return this.options || Array.from(this.monitorContext.options.values())
    },
    listeners() {
      const { change, ...listeners } = this.$listeners
      return listeners
    },
  },
  watch: {
    'monitorContext.options': function () {
      this.renderKey++
    },
  },
  methods: {
    getMonitorPickerContainer() {
      return 'viewport'
    },
    fetchMonitors() {
      if (this.excludedOptions) {
        const ids = this.excludedOptions
        return Promise.resolve(
          UniqBy(
            this.finalOptions.filter(({ id }) => ids.includes(id) === false),
            'key'
          )
        )
      }
      return Promise.resolve(this.finalOptions)
    },
    handleSelection(event) {
      this.$emit('change', this.multiple ? event : event[0])
      if (!this.multiple) {
        if (this.$refs.dropdownPickerRef) {
          this.$refs.dropdownPickerRef.handleHide()
        }
      }
    },
  },
}
</script>
