<template>
  <FlotoDropdownPicker
    :value="value"
    :options="options"
    multiple
    allow-clear
    @change="handleChange"
  >
  </FlotoDropdownPicker>
</template>

<script>
import { AvailableReportCategories } from '@modules/report/helpers/report'
import { hardCodedCountersByReportCategory } from '@components/filters/helper'

export default {
  name: 'FilterCounterPicker',
  inject: { counterContext: { default: { options: new Map() } } },
  model: {
    event: 'change',
  },
  props: {
    value: { type: Array, default: undefined },
    reportCategory: { type: String, default: undefined },
    selectedCounter: {
      type: Object,
      default: undefined,
    },
  },

  computed: {
    options() {
      if (
        this.selectedCounter &&
        this.reportCategory &&
        this.reportCategory === AvailableReportCategories.AVAILABILITY
      ) {
        let prefix

        if (this.selectedCounter?.key === 'monitor') {
          prefix = 'monitor.'
        } else {
          prefix = `${this.selectedCounter?.key}~`
        }

        return (
          hardCodedCountersByReportCategory[
            `${this.reportCategory}-without-agg`
          ] || []
        ).map((c) => {
          const counterRowName = `${prefix || ''}${c}`
          return {
            key: counterRowName,
            text: counterRowName.replace('~', '.'),
          }
        })
      }
      return []
    },
  },
  methods: {
    handleChange(value) {
      this.$emit('change', value)
    },
  },
}
</script>
