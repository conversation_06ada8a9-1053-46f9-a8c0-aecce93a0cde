<template>
  <SelectedItemPills v-if="disabled" tags :value="value" v-bind="$attrs" />
  <FlotoDropdownPicker
    v-else-if="asDropdown"
    :value="value"
    :options="tagOptions"
    as-input
    searchable
    multiple
    allow-clear
    v-bind="$attrs"
    @change="handleChange"
  />
  <FlotoDropdownPicker
    v-else-if="usePopover"
    class="inline-flex"
    use-popover
    multiple
    :as-input="false"
    placeemnt="bottomRight"
    :value="value"
    :options="uniqueTags"
    :disabled-options="disabledOptions"
    @change="handleChange"
  >
    <template v-slot:trigger="{ toggle }">
      <MButton
        id="btn-tag-inventory"
        title="Tags"
        :shadow="false"
        class="squared-button mr-2"
        :rounded="false"
        variant="neutral-lightest"
        @click="toggle"
      >
        <MIcon name="tag" class="mr-2" />
      </MButton>
    </template>
  </FlotoDropdownPicker>
  <MSelect
    v-else
    mode="tags"
    class="loose-tags-input"
    :options="tagOptions"
    placeholder="Add Tags"
    :value="value"
    @change="handleChange"
  >
    <template v-slot:clearIcon>
      <MIcon name="times-circle" />
    </template>
  </MSelect>
</template>

<script>
import Uniq from 'lodash/uniq'
import Trim from 'lodash/trim'
import UniqBy from 'lodash/uniqBy'

import SelectedItemPills from '@components/dropdown-trigger/selected-item-pills.vue'
export default {
  name: 'ObejctTagPicker',
  components: {
    SelectedItemPills,
  },
  inject: { tagContext: { default: { options: new Map() } } },

  model: {
    event: 'change',
  },
  props: {
    disabled: { type: Boolean, default: false },
    value: {
      type: Array,
      default() {
        return []
      },
    },
    // eslint-disable-next-line
    sm: { type: Boolean, default: true },
    asDropdown: { type: Boolean, default: false },
    usePopover: { type: Boolean, default: false },
    disabledOptions: { type: Array, default: undefined },
  },
  computed: {
    tagOptions() {
      return Array.from(this.tagContext.options.values())
    },
    uniqueTags() {
      return UniqBy(this.tagOptions, 'key')
    },
  },
  methods: {
    handleChange(event) {
      if (this.asDropdown || this.usePopover) {
        this.$emit(
          'change',
          Uniq(event || []).filter((i) => Trim(i))
        )
      } else {
        this.$emit(
          'change',
          Uniq((event || []).map((i) => i.toLowerCase())).filter((i) => Trim(i))
        )
      }
    },
  },
}
</script>
