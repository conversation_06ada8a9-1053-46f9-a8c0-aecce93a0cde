<template>
  <SelectedItemPills v-if="disabled" :value="resolvedValue" v-bind="$attrs" />
  <span v-else>
    <FlotoDropdownPicker
      :options="options"
      :value="value"
      v-bind="$attrs"
      :disabled="disabled"
      @change="handleChange"
      v-on="listeners"
    />
  </span>
</template>

<script>
import SelectedItemPills from '@components/dropdown-trigger/selected-item-pills.vue'

export default {
  name: 'ActiveIntegrationPicker',
  components: { SelectedItemPills },
  inject: { activeIntegrationContext: { default: { options: new Map() } } },
  model: {
    event: 'change',
  },
  props: {
    mandatory: { type: Boolean, default: false },
    value: { type: [Number, Array], default: undefined },
    allowCreate: { type: Boolean, default: false },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {}
  },
  computed: {
    resolvedValue() {
      if (!this.disabled) {
        return
      }
      const optionsMap = this.activeIntegrationContext.options
      const value = Array.isArray(this.value) ? this.value : [this.value]
      return value
        .filter(Boolean)
        .map((v) => (optionsMap.has(v) ? optionsMap.get(v).name : undefined))
    },
    options() {
      if (this.disabled) {
        return []
      }
      return Array.from(this.activeIntegrationContext.options.values())
    },
    listeners() {
      const { change, ...listeners } = this.$listeners
      return listeners
    },
  },
  methods: {
    handleChange(value) {
      this.$emit('change', value)
    },
  },
}
</script>
