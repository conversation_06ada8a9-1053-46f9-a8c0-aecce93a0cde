<template>
  <div
    v-if="disabled && !multiple && !asInput"
    class="flex items-center"
    name="monitor-type"
  >
    <template v-if="value">
      <MonitorType
        :disable-tooltip="displayName"
        :type="value"
        :width="iconWidth"
      />
      <span v-if="displayName" class="ml-2">{{ value }}</span>
    </template>
    <template v-else>{{ $attrs.placeholder }}</template>
  </div>
  <SelectedItemPills
    v-else-if="disabled && (multiple || asInput)"
    :value="resolvedValue"
    v-bind="$attrs"
  />
  <FlotoDropdownPicker
    v-else
    :options="options"
    :value="value"
    :multiple="multiple"
    :disabled="disabled"
    :as-input="asInput"
    :item-size="40"
    class="monitor-type-picker"
    v-bind="$attrs"
    v-on="listeners"
    @change="$emit('change', $event)"
  >
    <template v-if="!multiple && !asInput" v-slot:trigger="{ toggle }">
      <div
        class="flex items-center"
        :class="{ 'cursor-pointer': !disabled }"
        name="monitor-type"
        @click="toggle"
      >
        <template v-if="value">
          <MonitorType
            :disable-tooltip="displayName || disableTooltip"
            :type="value"
            :width="iconWidth"
          />
          <span v-if="displayName" class="ml-2">{{ value }}</span>
        </template>
        <template v-else>{{ placeholder }}</template>
        <MIcon
          v-if="!disabled"
          name="chevron-down"
          class="text-neutral-light ml-2"
        />
      </div>
    </template>
    <template v-slot:before-menu-text="{ item: optionItem }">
      <MonitorType
        :type="optionItem.text"
        class="mx-1 inline-flex"
        disable-tooltip
        :width="iconWidth"
      />
    </template>
  </FlotoDropdownPicker>
</template>

<script>
import SelectedItemPills from '@components/dropdown-trigger/selected-item-pills.vue'
import MonitorType from '@components/monitor-type.vue'

export default {
  name: 'MetricTypePicker',
  components: { MonitorType, SelectedItemPills },
  inject: { metricTypeContext: { default: { options: new Map() } } },
  model: { event: 'change' },
  props: {
    value: { type: [String, Number], default: undefined },
    type: { type: String, default: 'custom' },
    iconWidth: { type: [String, Number], default: '25px' },
    disabled: { type: Boolean, default: false },
    asInput: { type: Boolean, default: false },
    multiple: { type: Boolean, default: false },
    displayName: { type: Boolean, default: false },
  },
  data() {
    return {}
  },
  computed: {
    resolvedValue() {
      if (!this.disabled) {
        return
      }
      const value = Array.isArray(this.value) ? this.value : [this.value]
      return value
    },
    options() {
      if (this.disabled) {
        return []
      }
      return Array.from(this.metricTypeContext.options.values()) || []
    },
    listeners() {
      const { change, ...listeners } = this.$listeners
      return listeners
    },
  },
}
</script>
