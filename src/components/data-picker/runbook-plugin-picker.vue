<template>
  <span v-if="textOnly"> {{ resolvedValue }} </span>
  <SelectedItemPills
    v-else-if="disabled"
    :value="resolvedValue"
    v-bind="$attrs"
  />

  <FlotoDropdownPicker
    v-else
    ref="dropdownRef"
    :options="options"
    :value="value"
    :disabled="disabled"
    :searchable="searchable"
    v-bind="$attrs"
    v-on="listeners"
    @change="$emit('change', $event)"
  >
    <slot :options="options" :handleSelect="handleItemSelected" />
  </FlotoDropdownPicker>
</template>

<script>
import SelectedItemPills from '@components/dropdown-trigger/selected-item-pills.vue'

export default {
  name: 'RunbookPluginPicker',
  components: {
    SelectedItemPills,
  },
  inject: { runbookPluginContext: { default: { options: new Map() } } },
  model: { event: 'change' },
  props: {
    value: { type: [Number, Array], default: undefined },
    disabled: { type: Boolean, default: false },
    // eslint-disable-next-line
    searchable: { type: Boolean, default: true },
    textOnly: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    resolvedValue() {
      if (!this.disabled) {
        return
      }
      const optionsMap = this.runbookPluginContext.options
      const value = Array.isArray(this.value) ? this.value : [this.value]

      const resolvedMap = value
        .filter(Boolean)
        .map((v) => (optionsMap.has(v) ? optionsMap.get(v).text : undefined))

      if (resolvedMap && Array.isArray(resolvedMap) && this.textOnly) {
        return resolvedMap.join(',')
      }
      return resolvedMap
    },
    listeners() {
      const { change, ...listeners } = this.$listeners
      return listeners
    },
    options() {
      if (this.disabled) {
        return []
      }
      let items = Array.from(this.runbookPluginContext.options.values())
      return items
    },
  },
  methods: {
    handleItemSelected(items) {
      this.$emit('change', items)
      if (this.$refs.dropdownRef) {
        this.$refs.dropdownRef.handleHide()
      }
    },
  },
}
</script>
