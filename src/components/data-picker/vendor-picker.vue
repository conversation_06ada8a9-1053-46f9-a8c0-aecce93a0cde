<template>
  <SelectedItemPills v-if="disabled" :value="resolvedValue" v-bind="$attrs" />
  <FlotoDropdownPicker
    v-else
    :options="options"
    :value="value"
    searchable
    v-bind="$attrs"
    v-on="listeners"
    @change="$emit('change', $event)"
  />
</template>

<script>
import SelectedItemPills from '@components/dropdown-trigger/selected-item-pills.vue'

export default {
  name: 'VendorPicker',
  components: {
    SelectedItemPills,
  },
  inject: { vendorContext: { default: { options: new Map() } } },
  model: { event: 'change' },
  props: {
    value: { type: [Number, String, Array], default: undefined },
    disabled: { type: Boolean, default: false },
  },
  computed: {
    resolvedValue() {
      if (!this.disabled) {
        return
      }
      const optionsMap = this.vendorContext.options
      const value = Array.isArray(this.value) ? this.value : [this.value]
      return value
        .filter(Boolean)
        .map((v) => (optionsMap.has(v) ? optionsMap.get(v).name : undefined))
    },
    listeners() {
      const { change, ...listeners } = this.$listeners
      return listeners
    },
    options() {
      if (this.disabled) {
        return []
      }
      return Array.from(this.vendorContext.options.values())
    },
  },
}
</script>
