<template>
  <SelectedItemPills v-if="disabled" :value="resolvedValue" v-bind="$attrs" />
  <span v-else-if="!isGridSelector" class="role-name">
    <FlotoDropdownPicker
      :options="options"
      :value="value"
      v-bind="$attrs"
      :disabled="disabled"
      :disabled-options="disabledOptions"
      @change="handleChange"
      v-on="listeners"
    />
  </span>

  <span v-else>
    <ActiveIntegrationProvider>
      <FlotoDropdownGridSelector
        :options="options"
        :value="value"
        v-bind="$attrs"
        :disabled="disabled"
        :excluded-options="disabledOptions"
        :columns="columns"
        @change="handleChange"
        v-on="listeners"
      />
    </ActiveIntegrationProvider>
  </span>
</template>

<script>
import SelectedItemPills from '@components/dropdown-trigger/selected-item-pills.vue'
import ActiveIntegrationProvider from '@/src/components/data-provider/active-integration-provider.vue'

export default {
  name: 'IntegrationProfilePicker',
  components: { SelectedItemPills, ActiveIntegrationProvider },
  inject: {
    IntegrationProfileProviderContext: { default: { options: new Map() } },
  },
  model: {
    event: 'change',
  },
  props: {
    value: { type: [Number, Array], default: undefined },
    disabled: {
      type: Boolean,
      default: false,
    },

    isGridSelector: {
      type: Boolean,
      default: false,
    },
    disabledOptions: {
      type: Array,
      default: undefined,
    },
  },
  data() {
    this.columns = [
      {
        key: 'name',
        name: 'Integration Profile Name',
        searchable: true,
        sortable: true,
      },
      {
        key: 'description',
        name: 'Description',
        searchable: true,
        sortable: true,
      },
      {
        key: 'integrationType',
        name: 'Integration Type',
        searchable: true,
        sortable: true,
      },
    ]
    return {}
  },
  computed: {
    resolvedValue() {
      if (!this.disabled) {
        return
      }
      const optionsMap = this.IntegrationProfileProviderContext.options
      const value = Array.isArray(this.value) ? this.value : [this.value]
      return value.filter(Boolean).map((v) => {
        if (Array.isArray(optionsMap)) {
          return optionsMap.find((o) => o.id === v)?.name
        }

        return optionsMap.has(v) ? optionsMap.get(v).name : undefined
      })
    },
    options() {
      if (this.disabled) {
        return []
      }
      return Array.from(this.IntegrationProfileProviderContext.options.values())
    },
    listeners() {
      const { change, ...listeners } = this.$listeners
      return listeners
    },
  },
  methods: {
    handleChange(value) {
      this.$emit('change', value)
    },
  },
}
</script>
