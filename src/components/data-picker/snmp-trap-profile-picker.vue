<template>
  <SelectedItemPills v-if="disabled" :value="resolvedValue" v-bind="$attrs" />
  <FlotoDropdownPicker
    v-else-if="!isGridSelector"
    :options="options"
    :value="value"
    v-bind="$attrs"
    :disabled="disabled"
    :searchable="searchable"
    @change="handleChange"
    v-on="listeners"
  />

  <FlotoDropdownGridSelector
    v-else
    :options="options"
    :value="value"
    v-bind="$attrs"
    :disabled="disabled"
    :searchable="searchable"
    :columns="columns"
    @change="handleChange"
    v-on="listeners"
  />
</template>

<script>
import SelectedItemPills from '@components/dropdown-trigger/selected-item-pills.vue'

export default {
  name: 'SNMPTrapProfilePicker',
  components: { SelectedItemPills },
  inject: { trapProfilesContext: { default: { options: new Map() } } },
  model: {
    event: 'change',
  },
  props: {
    value: { type: [Number, Array, Object], default: undefined },
    disabled: {
      type: Boolean,
      default: false,
    },
    // eslint-disable-next-line
    searchable: { type: Boolean, default: true },
    isGridSelector: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    this.columns = [
      {
        key: 'name',
        name: 'snmp trap profile',
        searchable: true,
        sortable: true,
      },
      {
        key: 'oid',
        name: 'snmp oid',
        searchable: true,
        sortable: true,
      },
    ]
    return {}
  },
  computed: {
    resolvedValue() {
      if (!this.disabled) {
        return
      }
      const optionsMap = this.trapProfilesContext.options
      const value = Array.isArray(this.value) ? this.value : [this.value]
      return value
        .filter(Boolean)
        .map((v) => (optionsMap.has(v) ? optionsMap.get(v).name : undefined))
    },
    options() {
      if (this.disabled) {
        return []
      }
      return Array.from(this.trapProfilesContext.options.values())
    },
    listeners() {
      const { change, ...listeners } = this.$listeners
      return listeners
    },
  },
  methods: {
    handleChange(value) {
      this.$emit('change', value)
    },
  },
}
</script>
