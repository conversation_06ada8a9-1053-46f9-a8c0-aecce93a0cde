<template>
  <SelectedItemPills v-if="disabled" :value="resolvedValue" v-bind="$attrs" />
  <FlotoDropdownPicker
    v-else-if="!isGridSelector"
    :options="options"
    :value="value"
    v-bind="$attrs"
    :disabled="disabled"
    :searchable="searchable"
    @change="handleChange"
    v-on="listeners"
  />

  <FlotoDropdownGridSelector
    v-else
    :options="options"
    :value="value"
    v-bind="$attrs"
    :disabled="disabled"
    :searchable="searchable"
    :columns="columns"
    @change="handleChange"
    v-on="listeners"
  />
</template>

<script>
import SelectedItemPills from '@components/dropdown-trigger/selected-item-pills.vue'

export default {
  name: 'NcmMonitorPicker',
  components: { SelectedItemPills },
  inject: { ncmMonitorContext: { default: { options: new Map() } } },
  model: {
    event: 'change',
  },
  props: {
    value: { type: [Number, Array, Object], default: undefined },
    disabled: {
      type: Boolean,
      default: false,
    },
    // eslint-disable-next-line
    searchable: { type: Boolean, default: true },
    isGridSelector: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    this.columns = [
      {
        key: 'device',
        name: 'Device',
        searchable: true,
        sortable: true,
      },
      {
        key: 'ip',
        name: 'IP',
        searchable: true,
        sortable: true,
        width: '120px',
      },

      {
        key: 'type',
        name: 'Type',
        searchable: true,
        sortable: true,
        width: '120px',
        cellRender: 'type',
      },
      {
        key: 'groups',
        name: 'Groups',
        contextKey: 'groupContext',
        searchable: true,
        sortable: true,
        searchKey: 'groupsDisplay',
        sortKey: 'groupsDisplay',
        cellRender: 'groups',
      },
      {
        key: 'vendor',
        name: 'vendor',
        searchable: true,
        sortable: true,
      },
    ]
    return {}
  },
  computed: {
    resolvedValue() {
      if (!this.disabled) {
        return
      }
      const optionsMap = this.ncmMonitorContext.options
      const value = Array.isArray(this.value) ? this.value : [this.value]
      return value
        .filter(Boolean)
        .map((v) => (optionsMap.has(v) ? optionsMap.get(v).name : undefined))
    },
    options() {
      if (this.disabled) {
        return []
      }
      return Array.from(this.ncmMonitorContext.options.values())
    },
    listeners() {
      const { change, ...listeners } = this.$listeners
      return listeners
    },
  },
  methods: {
    handleChange(value) {
      this.$emit('change', value)
    },
  },
}
</script>
