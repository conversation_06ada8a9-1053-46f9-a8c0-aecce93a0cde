<template>
  <SelectedItemPills v-if="disabled" :value="resolvedValue" v-bind="$attrs" />
  <FlotoDropdownPicker
    v-else
    :options="options"
    :value="value"
    :multiple="multiple"
    :disabled="disabled"
    :as-input="asInput"
    :item-size="40"
    class="monitor-type-picker"
    v-bind="$attrs"
    v-on="listeners"
    @change="$emit('change', $event)"
  >
    <template v-if="!multiple && !asInput" v-slot:trigger="{ toggle }">
      <div
        class="flex items-center"
        :class="{ 'cursor-pointer': !disabled }"
        name="monitor-type"
        @click="toggle"
      >
        <template v-if="value">
          <MonitorType
            :disable-tooltip="displayName || disableTooltip"
            :type="value"
            :width="iconWidth"
          />
          <span v-if="displayName" class="ml-2">{{ value }}</span>
        </template>
        <template v-else>{{ placeholder }}</template>
        <MIcon
          v-if="!disabled"
          name="chevron-down"
          class="text-neutral-light ml-2"
        />
      </div>
    </template>
    <template v-slot:before-menu-text="{ item: optionItem }">
      <MonitorType
        :type="optionItem.name"
        class="mx-1 inline-flex"
        :width="iconWidth"
        disable-tooltip
      />
    </template>
  </FlotoDropdownPicker>
</template>

<script>
import MonitorType from '@components/monitor-type.vue'
import SelectedItemPills from '@components/dropdown-trigger/selected-item-pills.vue'

export default {
  name: 'MonitorTypePicker',
  components: { MonitorType, SelectedItemPills },
  inject: { monitorTypeContext: { default: { options: [] } } },
  model: { event: 'change' },
  props: {
    value: { type: [String, Array], default: undefined },
    multiple: { type: Boolean, default: false },
    displayName: { type: Boolean, default: false },
    disableTooltip: { type: Boolean, default: false },
    iconWidth: { type: [String, Number], default: '20px' },
    disabled: { type: Boolean, default: false },
    asInput: { type: Boolean, default: false },
  },
  computed: {
    listeners() {
      const { change, ...listeners } = this.$listeners
      return listeners
    },
    resolvedValue() {
      if (!this.disabled) {
        return
      }
      const optionsMap = this.monitorTypeContext.options
      const value = Array.isArray(this.value) ? this.value : [this.value]
      return value
        .filter(Boolean)
        .map((v) => (optionsMap.has(v) ? optionsMap.get(v).name : undefined))
    },
    options() {
      if (this.disabled) {
        return
      }
      return Array.from(this.monitorTypeContext.options.values())
    },
  },
}
</script>
