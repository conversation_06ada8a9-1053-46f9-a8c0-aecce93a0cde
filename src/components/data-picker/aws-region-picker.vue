<template>
  <SelectedItemPills
    v-if="disabled"
    :value="resolvedValue"
    v-bind="$attrs"
    :wrap="'wrap' in $attrs ? $attrs.wrap : true"
  />
  <span v-else class="create-group">
    <a
      v-if="allowCreate"
      id="create-new-group"
      class="text-right link-label"
      @click="openAwsRegionSlider"
    >
      Create Aws Region
    </a>
    <HierarchyPicker
      ref="HierarchyPickerRef"
      :value="value"
      name-property="groupName"
      :multiple="multiple"
      :disabled="disabled"
      :included-options="includedGroupsByName"
      v-bind="$attrs"
      :options="awsRegionsList"
      @change="handleChange"
    >
      <template v-slot:before-scroll-view>
        <div v-if="multiple && (awsRegionsList || []).length" class="w-full">
          <span style="margin-left: 42px">
            <MCheckbox
              :checked="isAllAwsRegionSelected"
              @change="selectAllAwsRegion"
              >Select All
            </MCheckbox>
          </span>

          <MDivider class="mb-0 mt-2" />
        </div>
      </template>
    </HierarchyPicker>

    <FlotoDrawerForm
      v-if="allowCreate"
      :open="openDrawer"
      @submit="onCreateGroupSubmit"
      @cancel="hideDrawer"
      @reset="resetGroupForm"
    >
      <template v-slot:header> Create Aws Region </template>
      <template v-slot:actions="{ submit, reset }">
        <MRow :gutter="0">
          <span class="mandatory"
            ><span class="text-secondary-red">*</span> fields are
            mandatory</span
          >
          <MCol class="text-right">
            <span>
              <MButton
                id="reset-group-btn"
                variant="default"
                class="mr-2"
                @click="reset"
              >
                Reset
              </MButton>
            </span>
            <MButton
              id="submit-group-btn"
              :loading="creatingGroup"
              @click="submit"
            >
              Create Aws Region
            </MButton>
          </MCol>
        </MRow>
      </template>
    </FlotoDrawerForm>
  </span>
</template>

<script>
import SelectedItemPills from '@components/dropdown-trigger/selected-item-pills.vue'
import HierarchyPicker from './hierarchy-picker.vue'

export default {
  name: 'AwsRegionPicker',
  components: { HierarchyPicker, SelectedItemPills },
  inject: { regionContext: { default: { awsRegionsList: new Map() } } },
  model: {
    event: 'change',
  },
  props: {
    isEditMode: { type: Boolean, default: false },
    value: { type: [Number, Array, Object], default: undefined },
    multiple: { type: Boolean, default: false },
    allowCreate: { type: Boolean, default: false },
    disabled: {
      type: Boolean,
      default: false,
    },
    includedGroupsByName: {
      type: Array,
      default: undefined,
    },
    showCustomOnly: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      openDrawer: null,
      groupEntity: {},
      creatingGroup: false,
    }
  },

  computed: {
    resolvedValue() {
      if (!this.disabled) {
        return
      }
      const optionsMap = this.regionContext.awsRegionsList
      const value = Array.isArray(this.value) ? this.value : [this.value]
      return value
        .map((v) => (optionsMap.has(v) ? optionsMap.get(v).name : undefined))
        .filter(Boolean)
    },
    awsRegionsList() {
      if (this.disabled) {
        return []
      }

      return Array.from(this.regionContext.awsRegionsList.values()).map((r) => {
        return {
          ...(!r.parentId ? { disabled: true } : {}),
          ...r,
        }
      })
    },
    listeners() {
      const { change, ...listeners } = this.$listeners
      return listeners
    },
    includedGroupsOptionsByName() {
      const options = this.awsRegionsList
      if (this.includedGroupsByName) {
        options.filter((group) =>
          this.includedGroupsByName.includes(group.groupName || group.name)
        )
      }
      if (this.showCustomOnly) {
        options.filter((g) => g.isCustom)
      }

      return options
    },

    // isAllAwsRegionSelected() {
    //   if (this.showCustomOnly) {
    //     return (
    //       Array.isArray(this.value) &&
    //       this.awsRegionsList.filter((g) => !g.disabled).length ===
    //         this.value.length
    //     )
    //   }
    //   return Array.isArray(this.value)
    //     ? this.includedGroupsOptionsByName.length === this.value.length
    //     : false
    // },
    isAllAwsRegionSelected() {
      return (
        Array.isArray(this.value) &&
        this.awsRegionsList.filter((g) => !g.disabled).length ===
          this.value.length
      )
    },
  },
  created() {
    if (!this.isEditMode) {
      this.$emit(
        'change',
        this.awsRegionsList
          .filter((region) => region.parentId)
          .map((r) => r.key)
      )
    }
  },
  methods: {
    openAwsRegionSlider() {
      this.openDrawer = true
    },
    hideDrawer() {
      this.openDrawer = null
      this.groupEntity = {}
    },
    resetGroupForm() {
      this.groupEntity = {}
    },
    handleChange(change) {
      if (change) {
        if (this.multiple) {
          this.$emit('change', change)
        } else {
          this.$emit('change', change.key)
        }
      }
    },

    // selectAllAwsRegion(event) {
    //   let allAwsRegions = this.awsRegionsList
    //   if (this.showCustomOnly) {
    //     allAwsRegions = allAwsRegions.filter((g) => !g.disabled)
    //   }

    //   this.$emit('change', event ? allAwsRegions.map((g) => g.key) : [])
    // },

    selectAllAwsRegion(event) {
      let allAwsRegions = this.awsRegionsList

      allAwsRegions = allAwsRegions.filter((g) => !g.disabled)
      // this.$emit('change', event ? allAwsRegions.map((g) => g.key) : [])

      if (!this.isAllAwsRegionSelected) {
        const allAwsChildRegions = allAwsRegions.map((g) => g.key)

        if (this.$refs.HierarchyPickerRef) {
          this.$refs.HierarchyPickerRef.handleChange(allAwsChildRegions)
        }

        this.$emit('change', allAwsChildRegions)
      } else {
        if (this.$refs.HierarchyPickerRef) {
          this.$refs.HierarchyPickerRef.handleChange([])
        }
      }
    },

    addGroupsRecursive(parentIdMap, parentId, result = []) {
      // Check if parent ID exists in the map
      if (!parentIdMap.has(parentId)) {
        return result
      }

      const group = parentIdMap.get(parentId)
      // Add the current group to the result
      result.push(group)

      // Check if parent ID is falsyvalue (indicating root group)
      if (!group.parentId) {
        return result
      }

      // Recursively add parent groups
      return this.addGroupsRecursive(parentIdMap, group.parentId, result)
    },
  },
}
</script>
