<template>
  <SelectedItemPills v-if="disabled" :value="resolvedValue" v-bind="$attrs" />
  <div v-else class="flex">
    <FlotoDropdownPicker
      id="external-storage-profiel-picker-id"
      :options="options"
      :value="value"
      v-bind="$attrs"
      :disabled="disabled"
      :searchable="searchable"
      :multiple="multiple"
      @change="handleChange"
      v-on="listeners"
    />

    <MPermissionChecker
      :permission="$constants.SYSTEM_SETTINGS_CREATE_PERMISSION"
    >
      <MButton
        v-if="allowCreate"
        id="create-storage-profiel-btn-id"
        outline
        class="text-right ml-4"
        @click="openStorageProfileSlider"
      >
        Create Storage Profile
      </MButton>
    </MPermissionChecker>

    <FlotoDrawerForm
      v-if="allowCreate"
      id="create-storage-profiel-id"
      :open="openDrawer"
      @reset="resetCredential"
      @submit="onCreateStorageProfileSubmit"
      @cancel="hideDrawer"
    >
      <template v-slot:header> Create Storage Profile </template>
      <template v-slot:actions="{ submit, reset, validate }">
        <span class="mandatory mt-5"
          ><span class="text-secondary-red">*</span> fields are mandatory</span
        >

        <MButton id="reset-btn" variant="default" @click="reset">
          Reset
        </MButton>

        <ExternalStorageFormTest
          ref="testref"
          :context="storageProfileEntry"
          :processing="creatingStorageProfile"
          :validate="validate"
          :submit="submit"
        />
      </template>
      <ExternalStorageForm
        :value="storageProfileEntry"
        @form-dirty="formDirty"
      />
    </FlotoDrawerForm>
  </div>
</template>

<script>
import ExternalStorageForm from '@modules/settings/system-settings/components/external-storage-form.vue'
import { createExternalStorageApi } from '@modules/settings/system-settings/external-storage-profile-api'
import SelectedItemPills from '@components/dropdown-trigger/selected-item-pills.vue'

import ExternalStorageFormTest from '@modules/settings/system-settings/components/external-storage-form-test.vue'

export default {
  name: 'StorageProfilePicker',
  components: {
    ExternalStorageForm,
    SelectedItemPills,
    ExternalStorageFormTest,
  },
  inject: {
    storageProfileContext: { default: { options: new Map() } },
  },
  model: {
    event: 'change',
  },
  props: {
    value: { type: [Number, Array], default: undefined },
    multiple: { type: Boolean, default: false },
    defaultFormData: {
      type: Object,
      default() {
        return {}
      },
    },

    allowCreate: { type: Boolean, default: false },
    disabled: {
      type: Boolean,
      default: false,
    },
    // eslint-disable-next-line
    searchable: { type: Boolean, default: true },
  },
  data() {
    return {
      creatingStorageProfile: false,
      openDrawer: null,
      storageProfileEntry: { ...this.defaultFormData },
    }
  },
  computed: {
    resolvedValue() {
      if (!this.disabled) {
        return
      }
      const optionsMap = this.storageProfileContext.options
      const value = Array.isArray(this.value) ? this.value : [this.value]
      return value
        .filter(Boolean)
        .map((v) => (optionsMap.has(v) ? optionsMap.get(v).name : undefined))
    },
    options() {
      if (this.disabled) {
        return
      }

      const options = Array.from(this.storageProfileContext.options.values())

      return options
    },
    listeners() {
      const { change, ...listeners } = this.$listeners
      return listeners
    },
  },
  methods: {
    handleChange(value) {
      this.$emit('change', value)
    },
    openStorageProfileSlider() {
      this.openDrawer = true
      this.storageProfileEntry = { ...this.defaultFormData }
    },
    hideDrawer() {
      this.formDirty()
      this.openDrawer = null
      this.storageProfileEntry = {}
    },
    resetCredential() {
      this.storageProfileEntry = { ...this.defaultFormData }
    },
    async onCreateStorageProfileSubmit() {
      this.creatingStorageProfile = true
      try {
        const profile = await createExternalStorageApi(this.storageProfileEntry)
        this.creatingStorageProfile = false
        this.openDrawer = null
        if (this.storageProfileContext.add) {
          this.storageProfileContext.add({
            key: profile.id,
            name: profile.name,
            destination: profile.destination,
          })
        }
        if (this.multiple) {
          this.$emit('change', [...(this.value || []), profile.id])
        } else {
          this.$emit('change', profile.id)
        }
      } catch (e) {
        this.creatingStorageProfile = false
      }

      this.formDirty()
    },
    formDirty() {
      if (this.$refs.testref) {
        this.$refs.testref.formDirty()
      }
    },
  },
}
</script>
