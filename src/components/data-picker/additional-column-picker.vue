<template>
  <FlotoDropdownPicker
    :options="options"
    :value="value"
    v-bind="$attrs"
    :disabled="disabled"
    :searchable="searchable"
    @change="$emit('change', $event)"
    v-on="listeners"
  />
</template>

<script>
export default {
  name: 'AddiitionalColumnPicker',

  inject: { additionalColumnsContext: { default: { options: new Map() } } },
  model: { event: 'change' },
  props: {
    // eslint-disable-next-line
    searchable: { type: Boolean, default: true },
    value: { type: [Number, Array], default: undefined },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    listeners() {
      const { change, ...listeners } = this.$listeners
      return listeners
    },

    options() {
      if (this.disabled) {
        return []
      }
      return Array.from(this.additionalColumnsContext.options.values())
    },
  },
}
</script>
