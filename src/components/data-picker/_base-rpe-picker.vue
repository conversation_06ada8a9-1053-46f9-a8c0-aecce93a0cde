<template>
  <SelectedItemPills v-if="disabled" :value="resolvedValue" v-bind="$attrs" />
  <FlotoDropdownPicker
    v-else
    :options="options"
    :value="value"
    v-bind="$attrs"
    :disabled="disabled"
    :searchable="searchable"
    @change="handleChange"
    v-on="listeners"
  >
    <template v-slot:before-menu-text="{ item }">
      <div class="inline mx-1">
        <MStatusArrow :status="item.status" size="lg" />
      </div>
    </template>
  </FlotoDropdownPicker>
</template>

<script>
import SelectedItemPills from '@components/dropdown-trigger/selected-item-pills.vue'

export default {
  name: 'RpePicker',
  components: { SelectedItemPills },
  inject: { rpeContext: { default: { options: new Map() } } },
  model: {
    event: 'change',
  },
  props: {
    value: { type: [Number, Array, Object], default: undefined },
    disabled: {
      type: Boolean,
      default: false,
    },
    // eslint-disable-next-line
    searchable: { type: Boolean, default: true },
  },
  computed: {
    resolvedValue() {
      if (!this.disabled) {
        return
      }
      const optionsMap = this.rpeContext.options
      const value = Array.isArray(this.value) ? this.value : [this.value]
      return value
        .filter(Boolean)
        .map((v) => (optionsMap.has(v) ? optionsMap.get(v).name : undefined))
    },
    options() {
      if (this.disabled) {
        return []
      }
      return Array.from(this.rpeContext.options.values())
    },
    listeners() {
      const { change, ...listeners } = this.$listeners
      return listeners
    },
  },
  methods: {
    handleChange(value) {
      this.$emit('change', value)
    },
  },
}
</script>
