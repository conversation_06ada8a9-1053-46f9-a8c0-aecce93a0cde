<template>
  <SelectedItemPills v-if="disabled" :value="resolvedValue" v-bind="$attrs" />
  <FlotoDropdownPicker
    v-else
    :options="options"
    :value="value"
    v-bind="$attrs"
    :disabled="disabled"
    :searchable="searchable"
    as-input
    @change="handleChange"
    v-on="listeners"
  />
</template>

<script>
import SelectedItemPills from '@components/dropdown-trigger/selected-item-pills.vue'

export default {
  name: 'MonitoringFieldsPicker',
  components: {
    SelectedItemPills,
  },
  inject: { monitoringFieldsContext: { default: { options: new Map() } } },
  model: {
    event: 'change',
  },
  props: {
    value: { type: [Number, Array, String], default: undefined },
    disabled: {
      type: <PERSON>olean,
      default: false,
    },
    // eslint-disable-next-line
    searchable: { type: Boolean, default: true },
    exclude: { type: Array, default: undefined },
  },
  computed: {
    resolvedValue() {
      if (!this.disabled) {
        return
      }
      const optionsMap = this.monitoringFieldsContext.options
      const value = Array.isArray(this.value) ? this.value : [this.value]
      return value
        .filter(Boolean)
        .map((v) => (optionsMap.has(v) ? optionsMap.get(v).name : undefined))
    },
    options() {
      const exclude = this.exclude

      if (exclude) {
        return Array.from(this.monitoringFieldsContext.options.values()).filter(
          (o) => exclude.indexOf(o.key) === -1 || this.value === o.key
        )
      }

      return Array.from(this.monitoringFieldsContext.options.values())
    },
    listeners() {
      const { change, ...listeners } = this.$listeners
      return listeners
    },
  },
  methods: {
    handleChange(value) {
      this.$emit('change', value)
    },
  },
}
</script>
