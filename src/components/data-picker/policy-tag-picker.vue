<template>
  <SelectedItemPills v-if="disabled" :value="resolvedValue" v-bind="$attrs" />
</template>

<script>
import Flatten from 'lodash/flatten'
import SelectedItemPills from '@components/dropdown-trigger/selected-item-pills.vue'

export default {
  name: 'PolicyTagPicker',
  components: {
    SelectedItemPills,
  },
  inject: { policyContext: { default: { options: new Map() } } },
  props: {
    // eslint-disable-next-line
    searchable: { type: Boolean, default: true },
    value: { type: [Number, Array], default: undefined },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    resolvedValue() {
      if (!this.disabled) {
        return
      }
      const optionsMap = this.policyContext.options
      const value = Array.isArray(this.value) ? this.value : [this.value]
      return Flatten(
        value
          .filter(Boolean)
          .map((v) => (optionsMap.has(v) ? optionsMap.get(v).tags : undefined))
      )
    },
    options() {
      if (this.disabled) {
        return []
      }
      return Array.from(this.policyContext.options.values()).map((i) => ({
        key: i.key,
        name: i.tags,
      }))
    },
  },
}
</script>
