<template>
  <SelectedItemPills v-if="disabled" :value="resolvedValue" v-bind="$attrs" />
  <FlotoDropdownPicker
    v-else
    :options="options"
    :value="value"
    :disabled="disabled"
    searchable
    :multiple="multiple"
    :disabled-options="disabledAssignedOptions ? defaultDisabledOptions : []"
    v-bind="$attrs"
    v-on="listeners"
    @change="changeSelection($event)"
  />
</template>

<script>
import SelectedItemPills from '@components/dropdown-trigger/selected-item-pills.vue'

export default {
  name: 'LogParserPicker',
  components: { SelectedItemPills },
  inject: { logParserContext: { default: { options: new Map() } } },
  model: { event: 'change' },
  props: {
    value: { type: [Number, Array], default: undefined },
    multiple: { type: Boolean, default: false },
    disabled: {
      type: Boolean,
      default: false,
    },
    disabledAssignedOptions: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    this.defaultDisabledOptions = Object.freeze(this.value)
    return {
      openDrawer: null,
    }
  },
  computed: {
    resolvedValue() {
      if (!this.disabled) {
        return
      }
      const optionsMap = this.logParserContext.options
      const value = Array.isArray(this.value) ? this.value : [this.value]
      return value
        .filter(Boolean)
        .map((v) => (optionsMap.has(v) ? optionsMap.get(v).name : undefined))
    },

    options() {
      if (this.disabled) {
        return []
      }
      let items = Array.from(this.logParserContext.options.values())
      return items
    },
    listeners() {
      const { change, ...listeners } = this.$listeners
      return listeners
    },
  },
  methods: {
    changeSelection(e) {
      if (e.length <= 0) {
        this.$emit('change', this.defaultDisabledOptions)
      } else {
        this.$emit('change', e)
      }
    },
  },
}
</script>
