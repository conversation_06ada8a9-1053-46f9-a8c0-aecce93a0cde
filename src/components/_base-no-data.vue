<template>
  <MRow :gutter="0" class="items-center justify-center flex-1">
    <MCol :size="12">
      <MRow v-if="!hideSvg" :gutter="0" class="justify-center">
        <MCol :size="svgColSize">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            viewBox="0 0 308.28 290.97"
          >
            <defs>
              <linearGradient
                id="a"
                x1="-217.1"
                y1="-222.71"
                x2="-218.43"
                y2="-224.32"
                gradientTransform="translate(48278.37 49526.74) scale(221.23)"
                gradientUnits="userSpaceOnUse"
              >
                <stop offset="0" stop-color="#189cd7" />
                <stop offset="1" stop-color="#ff0076" />
              </linearGradient>
              <linearGradient
                id="b"
                x1="-210.68"
                y1="-222.35"
                x2="-210.05"
                y2="-219.31"
                gradientTransform="translate(5879.82 6056.74) scale(26.56)"
                gradientUnits="userSpaceOnUse"
              >
                <stop offset="0" stop-color="#ffeb53" />
                <stop offset="0.19" stop-color="#ffb45b" />
                <stop offset="0.48" stop-color="#ff6766" />
                <stop offset="0.73" stop-color="#ff2f6e" />
                <stop offset="0.9" stop-color="#ff0d74" />
                <stop offset="1" stop-color="#ff0076" />
              </linearGradient>
              <linearGradient
                id="c"
                x1="-186.39"
                y1="-217.38"
                x2="-176.22"
                y2="-201"
                gradientTransform="translate(1115.22 1286.55) scale(5.86)"
                gradientUnits="userSpaceOnUse"
              >
                <stop offset="0" stop-color="#4aebff" />
                <stop offset="0.42" stop-color="#3cbcff" />
                <stop offset="0.8" stop-color="#329aff" />
                <stop offset="1" stop-color="#2f8dff" />
              </linearGradient>
              <linearGradient
                id="d"
                x1="-185.58"
                y1="-216.07"
                x2="-175.41"
                y2="-199.69"
                gradientTransform="translate(1098.1 1286.55) scale(5.86)"
                xlink:href="#c"
              />
              <linearGradient
                id="e"
                x1="-184.77"
                y1="-214.76"
                x2="-174.6"
                y2="-198.38"
                gradientTransform="translate(1080.98 1286.55) scale(5.86)"
                xlink:href="#c"
              />
              <linearGradient
                id="f"
                x1="-183.95"
                y1="-213.45"
                x2="-173.78"
                y2="-197.07"
                gradientTransform="translate(1063.86 1286.55) scale(5.86)"
                xlink:href="#c"
              />
            </defs>
            <circle cx="157.64" cy="145.15" r="110.62" fill="url(#a)" />
            <path
              d="M229.27,272.76A144.42,144.42,0,0,1,52.9,241.38"
              transform="translate(0 -0.01)"
              fill="none"
              stroke="#7b8fa5"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              opacity="0.25"
            />
            <path
              d="M62.41,40.24A144.31,144.31,0,0,1,298.31,190.7"
              transform="translate(0 -0.01)"
              fill="none"
              stroke="#7b8fa5"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              opacity="0.25"
            />
            <g opacity="0.25">
              <path
                d="M281.77,110.52A125.43,125.43,0,0,1,89.91,248.76"
                transform="translate(0 -0.01)"
                fill="none"
                stroke="#7b8fa5"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
              />
              <path
                d="M35.47,145.2A125.74,125.74,0,0,1,260,67.76"
                transform="translate(0 -0.01)"
                fill="none"
                stroke="#7b8fa5"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
              />
            </g>
            <path
              d="M295,189.94a13.28,13.28,0,1,0,13.28,13.28h0A13.27,13.27,0,0,0,295,189.94Zm0,23.26a10,10,0,1,1,10-10h0A10,10,0,0,1,295,213.2Z"
              transform="translate(0 -0.01)"
              fill="url(#b)"
            />
            <path
              d="M268.05,62.81A13.89,13.89,0,1,0,281.94,76.7h0a13.87,13.87,0,0,0-13.88-13.88Zm0,24.33a10.45,10.45,0,1,1,10.44-10.46h0A10.45,10.45,0,0,1,268.05,87.14Z"
              transform="translate(0 -0.01)"
              fill="#802fe0"
            />
            <circle cx="246.81" cy="239.2" r="8.72" fill="#802fe0" />
            <path
              d="M54.3,66.11a2.93,2.93,0,1,1,2.93-2.93h0a2.93,2.93,0,0,1-2.93,2.93Z"
              transform="translate(0 -0.01)"
              fill="url(#c)"
            />
            <path
              d="M37.17,66.11a2.93,2.93,0,1,1,2.93-2.93,2.93,2.93,0,0,1-2.93,2.93Z"
              transform="translate(0 -0.01)"
              fill="url(#d)"
            />
            <path
              d="M20.05,66.11A2.93,2.93,0,1,1,23,63.18h0a2.93,2.93,0,0,1-2.93,2.93Z"
              transform="translate(0 -0.01)"
              fill="url(#e)"
            />
            <path
              d="M2.93,66.11a2.93,2.93,0,1,1,2.93-2.93h0a2.93,2.93,0,0,1-2.93,2.93Z"
              transform="translate(0 -0.01)"
              fill="url(#f)"
            />
            <path
              d="M142,195.13H104a4,4,0,0,1-4-4v-89a4,4,0,0,1,4-4h42a4,4,0,0,1,4,4v6h46a8,8,0,0,1,8,8v14h3a8,8,0,0,1,8,8v49a8,8,0,0,1-8,8Z"
              transform="translate(0 -0.01)"
              fill="#fff"
              opacity="0.2"
              style="isolation: isolate"
            />
            <path
              d="M215.27,157.79a1.75,1.75,0,0,0-1.75,1.75V188.1a4.12,4.12,0,0,1-4.1,4.1H111.27a7.63,7.63,0,0,0,1.2-4.1V184a1.76,1.76,0,0,0-3.51,0v4.1a4.09,4.09,0,1,1-8.18,0h0V102.73a4.09,4.09,0,0,1,4.09-4.09h5.4a1.76,1.76,0,0,0,0-3.51h-5.4a7.62,7.62,0,0,0-7.6,7.6V188.1a7.6,7.6,0,0,0,7.6,7.6H209.42a7.6,7.6,0,0,0,7.6-7.6h0V159.54A1.75,1.75,0,0,0,215.27,157.79Z"
              transform="translate(0 -0.01)"
              fill="#fff"
            />
            <path
              d="M209.42,129.28h-4.09v-4.34a1.76,1.76,0,0,0-3.51-.27,1.22,1.22,0,0,0,0,.27h0v4.36H116.57a7.6,7.6,0,0,0-7.6,7.6h0V176a1.75,1.75,0,0,0,3.5,0h0V136.88a4.1,4.1,0,0,1,4.1-4.09h92.85a4.1,4.1,0,0,1,4.1,4.09v14.48a1.75,1.75,0,0,0,3.5,0h0V136.88A7.61,7.61,0,0,0,209.42,129.28Z"
              transform="translate(0 -0.01)"
              fill="#fff"
            />
            <path
              d="M118.46,98.64h26.17a4.1,4.1,0,0,1,4.1,4.09v4.68a2.92,2.92,0,0,0,2.92,2.92h46.08a4.11,4.11,0,0,1,4.09,4.1v2.3a1.76,1.76,0,0,0,3.51,0h0v-2.3a7.62,7.62,0,0,0-7.6-7.6H152.24v-4.1a7.62,7.62,0,0,0-7.6-7.6H118.46a1.76,1.76,0,1,0-.27,3.51A1.22,1.22,0,0,0,118.46,98.64Z"
              transform="translate(0 -0.01)"
              fill="#fff"
            />
            <path
              d="M104,195.13a4,4,0,0,1-4-4v-89a4,4,0,0,1,4-4h42a4,4,0,0,1,4,4v6h46a8,8,0,0,1,8,8v14h3a8.08,8.08,0,0,1,3.87,1H117a6,6,0,0,0-6,6v58Z"
              transform="translate(0 -0.01)"
              fill="#fff"
              opacity="0.3"
              style="isolation: isolate"
            />
            <path
              d="M27,199.46a144.32,144.32,0,0,1-5.74-90.34"
              transform="translate(0 -0.01)"
              fill="none"
              stroke="#7b8fa5"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              opacity="0.25"
            />
            <path
              d="M14.71,236A8.72,8.72,0,1,0,28,246.56,24.21,24.21,0,0,1,14.71,236Z"
              transform="translate(0 -0.01)"
              fill="#802fe0"
            />
            <circle
              cx="35.43"
              cy="223.55"
              r="24.17"
              fill="none"
              stroke="#802fe0"
              stroke-miterlimit="10"
            />
          </svg>
        </MCol>
      </MRow>
      <MRow :gutter="0">
        <MCol v-if="!hideMessage" :size="12">
          <component
            :is="$attrs.size === 'small' ? 'h5' : headerTag"
            class="text-center font-normal flex justify-center items-center"
            :class="textForVariant"
          >
            <MIcon v-if="icon" :name="icon || 'info-circle'" class="mr-2" />
            {{ message || 'No data found' }}
          </component>
        </MCol>
      </MRow>
    </MCol>
  </MRow>
</template>

<script>
export default {
  name: 'FlotoNoData',
  props: {
    icon: { type: [String, Boolean], default: undefined },
    message: { type: String, default: undefined },
    variant: { type: String, default: undefined },
    hideSvg: { type: Boolean, default: false },
    svgColSize: { type: Number, default: 2 },
    hideMessage: { type: Boolean, default: false },
    headerTag: { type: String, default: 'h1' },
  },
  computed: {
    textForVariant() {
      if (this.variant === 'error') {
        return 'text-secondary-red'
      }
      if (this.variant === 'success') {
        return 'text-secondary-green'
      }
      if (this.variant === 'neutral') {
        return 'text-neutral-light'
      }
      return 'text-primary'
    },
  },
}
</script>
