<script>
import { authComputed } from '@state/modules/auth'

export default {
  name: '<PERSON>er<PERSON><PERSON><PERSON><PERSON>',
  props: {
    permission: { type: [String, Array], required: true },
    handleRedirection: { type: Boolean, default: false },
    checkAll: { type: Boolean, default: false },
  },
  data() {
    return {
      isPermissionVerified: false,
    }
  },
  computed: {
    ...authComputed,
  },
  watch: {
    permissions: {
      handler() {
        this.verifyPermission()
      },
    },
    checkAll: 'verifyPermission',
  },
  created() {
    this.verifyPermission()
  },
  methods: {
    verifyPermission() {
      if (this.hasPermission(this.permission, this.checkAll)) {
        this.isPermissionVerified = true
      } else {
        this.isPermissionVerified = false
        if (this.handleRedirection) {
          this.$router.replace({ name: 'unauthorized' })
        }
        this.$emit('unauthorized')
      }
    },
  },
  render(h) {
    if (this.isPermissionVerified) {
      return this.$scopedSlots.default && this.$scopedSlots.default()
    }
    return null
  },
}
</script>
