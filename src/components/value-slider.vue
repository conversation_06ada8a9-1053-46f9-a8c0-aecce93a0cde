<template>
  <ASlider
    v-model="innerValue"
    :range="range"
    :step="step"
    :default-value="defaultValue"
    :max="max"
    :min="min"
  />
</template>

<script>
import Slider from 'ant-design-vue/es/slider'
import 'ant-design-vue/es/slider/style'

export default {
  name: 'ValueSlider',
  components: { ASlider: Slider },
  model: { event: 'change' },
  props: {
    range: { type: Boolean, default: false },
    width: {
      type: [String, Number],
      default: '45%',
    },
    height: {
      type: [String, Number],
      default: 180,
    },
    step: {
      type: [Number, String],
      default: 0.01,
    },
    defaultValue: {
      type: [Number, Array],
      default() {
        return undefined
      },
    },
    max: {
      type: [Number, String],
      default: 100,
    },
    min: {
      type: [Number, String],
      default: 0,
    },
    value: {
      type: [Number, Array],
      default: undefined,
    },
  },
  computed: {
    innerValue: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('change', value)
      },
    },
  },
}
</script>
