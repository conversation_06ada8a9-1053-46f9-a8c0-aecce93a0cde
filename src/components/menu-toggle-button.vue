<template>
  <div class="menu-container" @click="$emit('click', $event)">
    <MIcon
      :name="visible ? 'chevron-left' : 'chevron-right'"
      class="excluded-header-icon"
      :class="{ 'menu-visible': visible }"
    />
  </div>
</template>

<script>
export default {
  name: 'MenuToggleButton',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
}
</script>

<style lang="less" scoped>
div.menu-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 25px;
  height: 25px;
  font-size: @text-sm;
  color: var(--left-menu-arrow-color);
  cursor: pointer;
  border: 1px solid var(--border-color);
  border-radius: 50%;

  .menu-visible {
    margin-right: 2px;
  }
}
</style>
