<template>
  <MUpload
    v-if="mode !== 'dropper'"
    :key="renderCount"
    :action="url"
    :multiple="multiple"
    name="file"
    :open-file-dialog-on-click="!disabled"
    :show-upload-list="false"
    v-bind="attrs"
    :before-upload="handleFileUpload"
    @remove="handleFileRemove"
    @change="handleFileStatusChange"
    v-on="listeners"
  >
    <slot v-if="(!disabled && !onlyList) || preview">
      <div class="file-input-container" :class="{ disabled: disabled }">
        <div class="flex-1 min-w-0 flex flex-col h-full">
          <div class="flex-1 file-name-text flex items-center">
            <MIcon name="paper-clip" class="mr-1" />
            <span class="flex-1 text-ellipsis">
              {{ fileName || 'Select File' }}
            </span>
            <MButton
              v-if="uploadingfileList.length > 0 && hasSuccess"
              size="small"
              class="flex items-center justify-center"
              variant="transparent"
              :shadow="false"
              shape="circle"
              @click.stop.prevent="handleResetFiles"
            >
              <MIcon name="times" />
            </MButton>
          </div>
          <div
            v-if="progressInPercent !== undefined || hasError"
            class="progress"
          >
            <div
              class="progress-line"
              :class="{ 'has-error': hasError, 'has-success': hasSuccess }"
              :style="{ width: hasError ? '100%' : `${progressInPercent}%` }"
            />
          </div>
        </div>
        <div class="right-side-btn">
          {{ buttonText }}
        </div>
        <!-- <a v-if="asLink" class="text-neutral">
          <MIcon name="paper-clip" class="mr-2" /> {{ buttonText }}
        </a>
        <MButton v-else>
          {{ buttonText }}
        </MButton> -->
      </div>
    </slot>
  </MUpload>
  <AUploadDragger
    v-else
    :key="renderCount"
    :action="url"
    :multiple="multiple"
    class="mt-3"
    name="files"
    v-bind="attrs"
    :default-file-list="fileList"
    :before-upload="handleFileUpload"
    @remove="handleFileRemove"
    @change="handleFileStatusChange"
  >
    <slot>
      <div class="my-4">
        <p class="ant-upload-drag-icon">
          <MIcon name="monitor-export" class="text-neutral-light" />
        </p>
      </div>
      <p class="ant-upload-text">Click or drag file to this area to upload</p>
    </slot>
  </AUploadDragger>
</template>

<script>
import Pick from 'lodash/pick'
import AUploadDragger from 'ant-design-vue/es/upload/Dragger'
import { bytesToSize } from '../filters/bytes'
import { authComputed } from '../state/modules/auth'

export default {
  name: 'FileDropper',
  components: { AUploadDragger },
  model: { event: 'change' },
  props: {
    mode: { type: String, default: 'dropper' },
    disabled: { type: Boolean, default: false },
    asLink: {
      type: Boolean,
      default: false,
    },
    onlyList: { type: Boolean, default: false },
    buttonText: {
      type: String,
      required: false,
      default() {
        return 'Attach files'
      },
    },
    value: {
      type: [Array],
      default: () => [],
    },
    url: {
      type: String,
      default() {
        return `/upload`
      },
    },
    maxFiles: { type: Number, default: undefined },
    multiple: { type: Boolean, default: false },
    allowedExtensions: {
      type: Array,
      default: undefined,
    },
    maxAllowedFileSize: {
      type: Number,
      default: undefined,
    },
    shouldEmitFullResponse: {
      type: Boolean,
      default: false,
    },
    preview: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      renderCount: 1,
      uploadingfileList: [],
    }
  },
  computed: {
    ...authComputed,
    hasSuccess() {
      return (
        this.uploadingfileList.filter((f) => f.status === 'done').length ===
        this.uploadingfileList.length
      )
    },
    hasError() {
      return this.uploadingfileList.find((f) => f.status === 'error')
    },
    progressInPercent() {
      const totalFiles = this.uploadingfileList.length
      if (totalFiles > 1) {
        const uploadedFiles = this.uploadingfileList.filter(
          (f) => f.status === 'done'
        ).length
        return (uploadedFiles / totalFiles) * 100
      } else if (totalFiles > 0) {
        return this.uploadingfileList[0].percent
      }
      return undefined
    },
    fileName() {
      const files = this.uploadingfileList.map(({ name }) => name)
      if (files.length > 1) {
        return `${files[0]} (+${files.slice(1).length - 1})`
      } else if (this.fileList.length > 0) {
        if (this.fileList.length > 1) {
          return `${this.fileList[0].name} (+${
            this.fileList.slice(1).length - 1
          })`
        }
        return `${this.fileList[0].name}`
      }
      return files[0]
    },
    fileList() {
      if ((this.value || []).length) {
        return this.value.map((a) => {
          return {
            uid: a.result,
            name: a.name,
            result: a.result,
            status: 'done',
            url: `/download?id=${a.result}&file.name=${a.name}`,
          }
        })
      }
      return []
    },
    attrs() {
      const attrs = {
        ...this.$attrs,
      }
      return attrs
    },
    listeners() {
      const { change, ...listeners } = this.$listeners
      return listeners
    },
  },
  watch: {
    value(newValue, oldValue) {
      if (newValue !== oldValue) {
        if (this.maxFiles === 1) {
          this.uploadingfileList = []
        }
        this.renderCount++
      }
    },
  },
  methods: {
    handleFileStatusChange({ file, fileList }) {
      const files = fileList.filter((f) => f.status !== 'removed')
      this.$emit('file-list-updated', files)
      this.uploadingfileList = files
      if (file.status === 'error') {
        this.$emit('upload-done')
        return
      }

      if (file.status === 'removed') {
        this.$emit('remove', file)
      }
      if (file.response && file.status === 'done') {
        fileList.forEach((f) => {
          if (f.response) {
            f.url = `/download?id=${f.response.result}&file.name=${f.response['file.name']}`
          }
        })
        setTimeout(() => {
          let validfileList = [
            ...(this.value || []),
            {
              name: file.name,
              ...Pick(file.response, ['result']),
              ...(this.shouldEmitFullResponse ? file.response || {} : {}),
            },
          ]
          let shouldRerender = false
          if (this.maxFiles && validfileList.length > this.maxFiles) {
            validfileList = validfileList.reverse().slice(0, this.maxFiles)
            shouldRerender = true
          }
          this.$emit('change', validfileList)
          this.$emit('upload-done')
          if (shouldRerender) {
            setTimeout(() => this.renderCount++, 200)
          }
        }, 450)
      }
    },
    handleFileRemove(file) {
      let newList
      const refFileName = file.result || (file.response || {}).result
      if (!refFileName) {
        return
      }
      newList = (this.value || []).filter((f) => f.result !== refFileName)
      this.$emit('change', newList)
      return true
    },
    handleResetFiles() {
      this.uploadingfileList = []
      this.$emit('change', [])
      this.renderCount++
    },
    handleFileUpload(file) {
      let isValidFile = true
      if (this.maxAllowedFileSize) {
        const maxFileSize = this.maxAllowedFileSize * 1000 * 1024
        if (file.size > maxFileSize) {
          this.$errorNotification({
            message: 'File Too large',
            description: `Maximum ${
              this.maxAllowedFileSize ? bytesToSize(maxFileSize) : `5 MB`
            } file is allowed`,
          })
          isValidFile = false
        }
      }
      const ext = file.name.split('.').pop()
      if (
        this.allowedExtensions &&
        this.allowedExtensions.indexOf(ext) === -1
      ) {
        this.$errorNotification({
          message: 'Unsupported file type',
          description: `Only ${this.allowedExtensions.join(', ')} are allowed`,
        })
        isValidFile = false
      }
      if (!isValidFile) {
        const e = new Error('Unsupported file!')
        e._handled = true
        return Promise.reject(e)
      }
      this.$emit('upload-start')
      return true
    },
  },
}
</script>

<style lang="less" scoped>
.file-input-container {
  @apply flex items-center;

  height: @input-height-base;
  padding: 0;
  color: @input-placeholder-color;
  cursor: pointer;
  border: 1px solid var(--border-color);
  border-radius: @btn-radius;

  &.disabled {
    cursor: not-allowed;
  }

  .file-name-text {
    padding-left: 4px;
  }

  .progress {
    display: flex;
    align-items: center;
    height: 3px;
    background: var(--code-tag-background-color);

    .progress-line {
      height: 2px;
      background: var(--primary);

      &.has-error {
        background: var(--secondary-red);
      }

      &.has-success {
        background: var(--secondary-green);
      }
    }
  }
}

.right-side-btn {
  @apply inline-flex bg-primary h-full items-center justify-center px-2;

  color: white;
  border-top-right-radius: @btn-radius;
  border-bottom-right-radius: @btn-radius;
}
</style>
