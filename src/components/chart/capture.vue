<template>
  <div class="flex flex-1 min-h-0 flex-col min-w-0">
    <slot :capture="capture" />
    <MModal
      v-if="showShareScreen"
      :open="showShareScreen"
      width="85%"
      overlay-class-name="share-modal hide-footer"
    >
      <template v-slot:title>
        <div class="flex justify-between items-center">
          <h3 class="m-0">Share Widget</h3>
          <div class="inline-flex items-center">
            <ColorPicker v-model="markerColor" class="mx-1" hide-arrow />
            <FlotoDropdownPicker
              v-model="markerWidth"
              :options="markerWidthOptions"
              :searchable="false"
              :as-input="false"
            >
              <template v-slot:trigger>
                <MButton
                  :shadow="false"
                  class="squared-button mr-2"
                  :rounded="false"
                  variant="neutral-lightest"
                >
                  <MIcon name="pencil" />
                </MButton>
              </template>
            </FlotoDropdownPicker>
            <MButton
              :shadow="false"
              class="squared-button mr-2"
              :rounded="false"
              variant="neutral-lightest"
              title="Clear"
              @click="resetCanvas"
            >
              <MIcon name="eraser" />
            </MButton>
            <!-- <MButton
              variant="transparent"
              shape="circle"
              title="Send As An Email"
              @click="isFormMinimized = false"
            >
              <MIcon name="paper-clip" />
            </MButton> -->
            <MButton
              :shadow="false"
              class="squared-button mr-2"
              :rounded="false"
              variant="neutral-lightest"
              @click="handleClose"
            >
              <MIcon name="times" />
            </MButton>
          </div>
        </div>
      </template>
      <div class="flex flex-col min-h-0">
        <div
          class="flex bordered p-1 rounded justify-center items-center bg-neutral-lightest mb-2 flex-1 min-h-0"
        >
          <CanvasDrawBoard
            v-if="!loading"
            ref="canvasBoardRef"
            :image="image"
            :stroke-color="markerColor"
            :stroke-width="markerWidth"
            :height="boundingRect.height"
            :width="boundingRect.width"
          />
          <!-- <h2 v-else class="m-0">Loading...</h2> -->
          <FlotoContentLoader v-else class="m-0" loading />
        </div>
        <FlotoForm
          submit-text="Share"
          :loading="processing || loading"
          allow-reset
          reset-text="Reset"
          class="flip-buttons"
          @reset="reset"
          @submit="handleShareCapture"
        >
          <UserProvider>
            <HandlerProvider ref="handlerProviderRef">
              <FlotoFormItem rules="required" label="Share With">
                <UserOrEmailPicker
                  v-model="formData.notifyTo"
                  name="parameter-name"
                  title="@User or Email or /Handle"
                  always-text-mode
                  rules="required"
                  disable-justify-around
                />
              </FlotoFormItem>
            </HandlerProvider>
          </UserProvider>

          <FlotoFormItem
            v-model="formData.message"
            rules="required"
            label="Message"
            type="textarea"
            :rows="3"
          />
        </FlotoForm>
      </div>
      <template v-slot:footer>
        <span />
      </template>
    </MModal>
  </div>
</template>
<script>
import html2canvas from 'html2canvas'
import Omit from 'lodash/omit'
import Moment from 'moment'

import api from '@api'
import ColorPicker from '@components/color-picker.vue'
import CanvasDrawBoard from './canvas-draw-board.vue'
import { authComputed } from '@/src/state/modules/auth'
// import { wrapSocketEventToPromise } from '@/src/utils/socket-event-as-api'
import Constants from '@constants'
import Bus from '@utils/emitter'
import UserOrEmailPicker from '@components/data-picker/user-or-email-picker.vue'
import HandlerProvider from '@/src/components/data-provider/handler-provider.vue'
import UserProvider from '@/src/components/data-provider/user-provider.vue'

export default {
  name: 'Capture',

  components: {
    CanvasDrawBoard,
    ColorPicker,
    UserOrEmailPicker,
    HandlerProvider,
    UserProvider,
  },
  inject: {
    HandlerProviderContext: { default: { options: [] } },
  },
  props: {
    minWidth: {
      type: Number,
      default: 100,
    },
    minHeight: {
      type: Number,
      default: 100,
    },
    widget: {
      type: Object,
      default: undefined,
    },
  },
  data() {
    this.markerWidthOptions = [
      { key: 2, text: 'Thin' },
      { key: 5, text: 'Medium' },
      { key: 8, text: 'Thick' },
    ]
    this.formSpace = {
      width: 500,
      height: 300,
    }
    return {
      image: null,
      showShareScreen: false,
      processing: false,
      loading: true,
      boundingRect: {},
      formPosition: null,
      markerWidth: 2,
      markerColor: '#f04e3e',
      formData: {
        users: undefined,
        message: undefined,
      },
      isFormMinimized: true,
    }
  },
  computed: {
    ...authComputed,

    containerStyle() {
      return Omit(this.boundingRect, [
        'width',
        'bottom',
        'left',
        'right',
        'height',
      ])
    },
    containerClasses() {
      if (['left', 'right'].includes(this.formPosition)) {
        return []
      } else {
        return ['flex-col']
      }
    },
    formStyle() {
      // const position = this.formPosition
      // const boundingRect = this.boundingRect
      const formSpace = this.formSpace
      // const gap = 10
      // const goesOutSide =
      //   parseInt(boundingRect.top) + formSpace.height > window.innerHeight
      return {
        width: `${formSpace.width}px`,
        height: `${formSpace.height}px`,
        top: `calc(50% - ${formSpace.height / 2}px)`,
        left: `calc(50% - ${formSpace.width / 2}px)`,
        // ...(position === 'left'
        //   ? {
        //       left: `${
        //         parseInt(boundingRect.left) - (formSpace.width + gap)
        //       }px`,
        //       ...(goesOutSide ? { bottom: 0 } : {}),
        //     }
        //   : {}),
        // ...(position === 'right'
        //   ? {
        //       left: `${parseInt(boundingRect.right) + gap}px`,
        //       ...(goesOutSide ? { bottom: 0 } : {}),
        //     }
        //   : {}),
        // ...(position === 'top'
        //   ? {
        //       top: `${parseInt(boundingRect.top) - (formSpace.height + gap)}px`,
        //       left: boundingRect.left,
        //     }
        //   : {}),
        // ...(position === 'bottom'
        //   ? {
        //       top: `${parseInt(boundingRect.bottom) + gap}px`,
        //       left: boundingRect.left,
        //     }
        //   : {}),
      }
    },
  },
  methods: {
    reset() {
      // this.image = null
      // this.showShareScreen = false
      this.boundingRect = {}
      this.formData = {
        users: undefined,
        message: undefined,
      }
      this.resetCanvas()
    },
    handleClose() {
      this.image = null
      this.showShareScreen = false
      this.boundingRect = {}
      this.formData = {
        users: undefined,
        message: undefined,
      }
    },
    calculateFormPosition(rect) {
      const leftAvailableSpace = rect.left
      const topAvailableSpace = rect.top
      const formSpace = this.formSpace
      if (leftAvailableSpace > formSpace.width) {
        return 'left'
      } else if (
        window.innerWidth - leftAvailableSpace - rect.width >
        formSpace.width
      ) {
        return 'right'
      } else if (window.innerHeight - topAvailableSpace > formSpace.height) {
        return 'bottom'
      } else {
        return 'top'
      }
    },
    capture() {
      this.$el.scrollIntoView({
        block: 'center',
        inline: 'center',
        behavior: 'smooth',
      })
      this.showShareScreen = true
      this.loading = true
      this.processing = false
      setTimeout(() => {
        this.$nextTick(async () => {
          const rect = Omit(this.$el.getBoundingClientRect().toJSON())
          if (rect.width < this.minWidth) {
            rect.width = this.minWidth
          }
          if (rect.height < this.minHeight) {
            rect.height = this.minHeight
          }
          this.formPosition = this.calculateFormPosition(rect)
          Object.keys(Omit(rect, ['x', 'y'])).forEach(
            (key) => (rect[key] = `${rect[key]}px`)
          )
          this.boundingRect = rect
          const canvas = await html2canvas(this.$el, {
            scale: 1,
            useCORS: false,
          })
          this.image = canvas.toDataURL()
          this.loading = false
        })
      }, 400)
    },
    handleShareCapture() {
      this.processing = true
      setTimeout(() => {
        const image = this.$refs.canvasBoardRef.getImage()
        return api
          .getNewClient()
          .post(
            '/upload-image',
            { file: image },
            {
              notify: false,
              headers: { Authorization: `Bearer ${this.accessToken}` },
            }
          )
          .then(({ data }) => {
            let handlerMap = {}

            if (this.$refs.handlerProviderRef) {
              handlerMap = this.$refs.handlerProviderRef.gethandlerMaps()
            }

            Bus.$emit('server:event', {
              'event.type': Constants.UI_ACTION_WIDGET_SHARE,
              'event.context': {
                filename: data['file.name'],
                recipients: this.formData.notifyTo.map((r) => {
                  const isHandles = r.startsWith('/')
                  const recipient = r.startsWith('@') ? r.slice(1) : r

                  let HandlerContext

                  if (isHandles) {
                    HandlerContext = handlerMap[r]
                  }

                  const type = r.startsWith('@')
                    ? 'user'
                    : isHandles
                    ? 'channel'
                    : 'email'

                  return {
                    type,
                    recipient,

                    ...(isHandles
                      ? {
                          id: isHandles ? HandlerContext.id : undefined,

                          integration: isHandles
                            ? HandlerContext.integrationType
                            : undefined,
                        }
                      : {}),
                  }
                }),
                message: this.formData.message,
                'user.name': this.user.userName,
                ...(this.widget
                  ? {
                      'widget.id': this.widget?.id,
                      'visualization.name': this.widget?.name,
                    }
                  : {}),
                type: this.widget ? 'Widget' : 'Metric Explorer',
                Timestamp: Moment().unix() * 1000,
              },
            })
            // api.post(`/misc/widget-share`, {
            //   filename: data['file.name'],
            //   recipients: this.formData.users,
            //   message: this.formData.message,
            //   'user.name': this.user.userName,
            // })
            this.handleClose()
            this.reset()
            this.$successNotification({
              message: 'Successful',
              description: `Shared successfully`,
            })
            this.processing = false
          })
      }, 1000)
    },
    resetCanvas() {
      if (this.$refs.canvasBoardRef) {
        this.$refs.canvasBoardRef.reset()
      }
    },
    getIdByHandlerContextByName(name, options) {
      return this.HandlerProviderContext?.options?.find((i) => i.name === name)
    },
  },
}
</script>

<style lang="less" scoped>
.screenshot-share-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  background: var(--screenshot-share-overlay-bg);

  &.loaded {
    backdrop-filter: blur(4px);
  }
}

.image-container {
  position: fixed;
  z-index: 101;
}

.form-container {
  position: fixed;
  z-index: 101;
  background: var(--screenshot-share-form-bg);
}

.main-container {
  position: fixed;
  z-index: 100;
  display: flex;
  width: 100vw;
  height: 100vh;
}

.button-bar {
  position: absolute;
  top: -35px;
  right: 0;
  display: flex;
  width: auto;
  height: 30px;
  background: var(--page-background-color);
  border-radius: 0.25rem;
}
</style>
