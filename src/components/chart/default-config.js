// import Color from 'color'
import {
  handleLoadEvent,
  renderLegendElements,
  handleAfterSeriesAnimateEvent,
} from './chart-events'

export default {
  title: {
    text: null, // widgetName || '',
    align: 'left',
    style: {
      color: 'var(--page-text-color)',
      fontSize: '14px',
    },
  },
  chart: {
    type: 'line',
    plotBorderColor: 'var(--border-color)',
    backgroundColor: 'transparent',
    style: {
      fontFamily: 'var(--chart-font-family)',
    },
    reflow: false,
    skipClone: true,
    resetZoomButton: {
      theme: {
        fill: 'var(--primary)',
        stroke: 'var(--primary)',
        r: 4,
        style: {
          color: 'var(--primary-button-text)',
        },
        states: {
          hover: {
            fill: 'var(--primary-button-hover-bg)',
            style: {
              color: 'var(--primary-button-hover-text)',
            },
          },
        },
      },
    },
    events: {
      load: handleLoadEvent,
      redraw: renderLegendElements,
      // render: handleRenderEvent,
    },
  },
  boost: {
    useGPUTranslations: true,
    // Chart-level boost when there are more than 5 series in the chart
    seriesThreshold: 11,
  },
  colors: [
    '#099dd9',
    '#89c540',
    '#f5bc18',
    '#f58518',
    '#f45b5b',
    '#8d3abc',
    '#8085E9',
    '#3279be',
    '#90ef7f',
    '#434348',
    '#f7a35c',
    '#f15c80',
    '#e4d354',
    '#2b908f',
    '#f04e3e',
    '#91e8e1',
  ],
  xAxis: {
    // dateTimeLabelFormats: {
    //   millisecond: '%I:%M:%S.%L %P',
    //   second: '%I:%M:%S %P',
    //   minute: '%I:%M %P',
    //   hour: '%I:%M %P',
    //   day: '%e %b',
    //   week: '%e %b',
    //   month: "%b '%y",
    //   year: '%Y',
    // },
    lineColor: 'var(--bottom-line-color)',
    tickLength: 0,
    labels: {
      style: { color: 'var(--page-text-color)', fontSize: '0.65rem' },
    },
    title: {
      style: { color: 'var(--page-text-color)' },
    },
    gridLineColor: 'var(--chart-grid-line-color)',
  },
  yAxis: {
    // min: 0,
    reversedStacks: false,
    labels: {
      style: { color: 'var(--page-text-color)', fontSize: '0.65rem' },
    },
    title: {
      style: { color: 'var(--page-text-color)' },
    },
    gridLineColor: 'var(--chart-grid-line-color)',
  },
  tooltip: {
    useHTML: true,
    crosshairs: true,
    headerFormat: `{point.key}`,
    backgroundColor: 'var(--chart-tooltip-background)',
    style: { color: 'var(--page-text-color)' },
  },
  legend: {
    align: 'center',
    symbolWidth: 11,
    symbolHeight: 11,
    symbolRadius: 1,
    itemStyle: {
      color: 'var(--chart-legend-color)',
      fontWeight: 'normal',
      opacity: '1',
      fontSize: '0.65rem',
    },
    itemHoverStyle: {
      color: 'var(--page-text-color)',
      fontWeight: 'normal',
      opacity: '1',
    },
    itemHiddenStyle: {
      opacity: '0.5',
    },
    navigation: {
      activeColor: 'var(--primary)',
      inactiveColor: 'var(--faded-page-background-color)',
      style: {
        color: 'var(--page-text-color)',
      },
    },
  },
  plotOptions: {
    series: {
      boostThreshold: 3000,
      borderWidth: 0,
      // borderColor: 'var(--chart-border-color)',
      animation: {
        duration: 300,
        easing: 'linear',
      },
      connectNulls: true,
      dataLabels: {
        color: 'var(--page-text-color)',
        shadow: false,
        style: {
          fontWeight: 'normal',
          textOutline: 'none',
        },
      },
      marker: {
        enabled: false,
        symbol: 'circle',
        fillColor: 'white',
        lineColor: null,
        lineWidth: 2,
        radius: 3,
        states: {
          hover: {
            enabled: true,
          },
        },
      },
      states: {
        hover: {
          lineWidthPlus: 0,
        },
      },
      events: {
        afterAnimate: handleAfterSeriesAnimateEvent,
      },
    },
    bar: {
      // pointWidth: 15,
      maxPointWidth: 50,
    },
    column: {
      // pointWidth: 15,
      maxPointWidth: 50,
    },
  },
  credits: {
    enabled: false,
  },
  exporting: {
    enabled: false,
  },
  noData: {
    style: {
      fontSize: '15px',
      color: 'var(--text-neutral-ligher)',
    },
  },
  lang: {
    noData: 'No data found',
  },
}
