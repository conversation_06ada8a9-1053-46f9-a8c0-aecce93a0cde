import Color from 'color'
import Moment from 'moment'
import Trim from 'lodash/trim'
import Constants from '@constants'
import {
  DATE_FORMAT,
  TIME_FORMAT,
  WidgetTypeConstants,
} from '@components/widgets/constants'
import { wrapSocketEventToPromise } from '@/src/utils/socket-event-as-api'
import NumberFormat from '@src/filters/number-format'
import Bus from '@utils/emitter'

function displayTotalInCenter() {
  const centerPieSeries = this.series.find((s) => s.options.displayTextInCenter)
  const useCenerTextHtml = this.series.find((s) => s.options.useCenerTextHtml)
  if (centerPieSeries) {
    const series = this.series[0]
    const textInfo = series.options.centerText || {
      fontSize: 24,
    }
    const seriesCenter = series.center
    if (series.total <= 0 || !seriesCenter) {
      return
    }
    const chart = this
    const x = seriesCenter[0] + chart.plotLeft
    const y = seriesCenter[1] + chart.plotTop - textInfo.fontSize
    const text = useCenerTextHtml
      ? textInfo.title
      : `<div class='font-600' style="text-align: center; font-size: ${
          textInfo.fontSize
        }px"><span class='text-lg font-normal'>${
          textInfo.title || 'Total'
        }</span><br /><span   class="cursor-pointer" title=${
          textInfo.value ? textInfo.value.apply(series) : series.total
        }>${
          textInfo.value
            ? textInfo.value.apply(series)
            : NumberFormat(series.total)
        }</span></div>`
    const fontMetrics = chart.renderer.fontMetrics(textInfo.fontSize)

    if (!this.customTitle) {
      this.customTitle = chart.renderer
        .text(text, null, null, true)
        .css({
          transform: 'translate(-50%)',
          fontSize: `${textInfo.fontSize}px`,
          fontFamily: "'Poppins', sans-serif",
          zIndex: -1,
        })
        .add()
    }

    this.customTitle.attr({
      x,
      y: y + fontMetrics.f / 2,
    })
  }
}

function applyAnomalyRangeBoundary() {
  const anomalySeries = this.series.find((s) => s.options.isAnomalySeries)
  if (anomalySeries && anomalySeries.data.length > 0) {
    const areaSeries = this.series.find((s) => s.type === 'areasplinerange')

    const renderer = areaSeries.group.renderer
    const existingGroup = renderer.boxWrapper.element.getElementById(
      anomalySeries.options.guid
    )
    if (existingGroup) {
      existingGroup.remove()
    }
    const group = renderer.g().add()
    group.element.id = anomalySeries.options.guid

    const clipPath = renderer.createElement('mask')

    clipPath.element.id = `mask-${anomalySeries.options.guid}`
    renderer
      .createElement('rect')
      .attr('height', this.clipBox.height)
      .attr('width', this.clipBox.width)
      .attr('x', this.clipBox.x)
      .attr('y', this.clipBox.y)
      .attr('fill', 'white')
      .add(clipPath)
    renderer
      .createElement('path')
      .attr('fill', 'black')
      .attr('d', areaSeries.area.element.getAttribute('d'))
      .add(clipPath)

    clipPath.add(group)
    anomalySeries.group.element.setAttribute(
      'mask',
      `url(#mask-${anomalySeries.options.guid})`
    )
    anomalySeries.group.element.childNodes[0].setAttribute(
      'mask',
      `url(#mask-${anomalySeries.options.guid})`
    )
  }
}

export function handleAfterSeriesAnimateEvent() {
  displayTotalInCenter.apply(this.chart)
}

export function handleRenderEvent() {
  applyAnomalyRangeBoundary.apply(this)
}

export function fillSeriesColorWithGradient() {
  this.series.forEach((series) => {
    if (
      this.options.chart.type === 'areaspline' ||
      series.options.type === 'areaspline'
    ) {
      let color
      if (/^(var\()?--/.test(series.color)) {
        const style = getComputedStyle(document.body)
        color = Trim(
          style.getPropertyValue(
            Trim(series.color.replace(/(var\()?(.*)/, '$2'), ')')
          )
        )
      } else {
        color = series.color
      }
      if (color && color !== '') {
        series.update(
          {
            fillColor: {
              linearGradient: {
                x1: 0,
                y1: 0,
                x2: 0,
                y2: 1,
              },
              stops: [
                [0, Color(color).alpha(1).toString()],
                [1, Color(color).alpha(0).toString()],
              ],
            },
          },
          true
        )
      }
    }
  })
}

export function handleLoadEvent() {
  fillSeriesColorWithGradient.call(this)
  const min = this.xAxis[0].getExtremes().min
  const max = this.xAxis[0].getExtremes().max
  this.userOptions.initialTimeRange = { min, max }
  renderLegendElements.call(this)
}

export function renderLegendElements() {
  if (this.legend) {
    this.legend.destroy()
  }
  // distance between 2 elements
  let itemDistance = this.legend.options.itemDistance
  // the biggest element
  let maxItemWidth = this.legend.maxItemWidth
  // make the width of the legend in the size of 2 largest elements + distance
  let nextLegendWidth = maxItemWidth * 2 + itemDistance
  // container width
  let boxWidth = this.plotBox.width
  // if the length of the 2 largest elements + the distance between them is less than the width of 			container, we make 1 row, else set legend width 2 max elements + distance between
  if (boxWidth < nextLegendWidth) {
    this.legend.options.width = maxItemWidth
  } else {
    this.legend.options.width = nextLegendWidth
  }

  displayTotalInCenter.apply(this)

  this.render()
}

function defaultZoomFn(e, start, end, zoomInfo, seriesArr) {
  let timeline
  if (typeof e.min === 'undefined' && typeof e.max === 'undefined') {
    const defaultTimeline =
      e.target.chart.series[0].userOptions.zoomInfo.defaultTimeline
    timeline = defaultTimeline
  } else {
    if (e.max >= e.dataMax || e.min <= e.dataMin) {
      start = e.target.chart.userOptions.initialTimeRange.min
      end = e.target.chart.userOptions.initialTimeRange.max
    }
    const startInstance = Moment(start)
    const endInstance = Moment(end)
    timeline = {
      'relative.timeline': 'custom',
      'from.date': startInstance.utc().format(DATE_FORMAT),
      'from.time': `${startInstance.utc().format(TIME_FORMAT)}`,
      'to.date': endInstance.utc().format(DATE_FORMAT),
      'to.time': `${endInstance.utc().format(TIME_FORMAT)}`,
    }
  }
  return wrapSocketEventToPromise(
    zoomInfo.serverEvent || Constants.UI_WIDGET_RESULT_EVENT,
    {
      ...(zoomInfo.params || {}),
      'visualization.timeline': timeline,
    },
    true
  ).then(({ result }) => {
    const series = (result[WidgetTypeConstants.CHART] || {}).series || []
    series.forEach((s) => {
      const seriesToUpdate = seriesArr.find(
        (d) =>
          d.userOptions.entity === s.entity &&
          d.userOptions.counter === s.counter &&
          d.userOptions.instance === s.instance
      )
      if (seriesToUpdate) {
        const data = s.data
        seriesToUpdate.update({ data })
      }
    })
  })
}

export function setExtremes(e) {
  const { chart } = e.target
  if (['zoom', 'syncExtremes'].includes(e.trigger)) {
    if (chart.userOptions.disableAutoFetchServerSideZoomData) {
      let start = Math.round(e.min)
      let end = Math.round(e.max)
      if (e.max >= e.dataMax || e.min <= e.dataMin) {
        start = e.target.chart.userOptions.initialTimeRange.min
        end = e.target.chart.userOptions.initialTimeRange.max
      }
      const startInstance = Moment(start)
      const endInstance = Moment(end)
      const timeline = {
        'relative.timeline': 'custom',
        'from.date': startInstance.utc().format(DATE_FORMAT),
        'from.time': `${startInstance.utc().format(TIME_FORMAT)}`,
        'to.date': endInstance.utc().format(DATE_FORMAT),
        'to.time': `${endInstance.utc().format(TIME_FORMAT)}`,
      }
      Bus.$emit('chart:server:zoom', {
        guid: chart.userOptions.guid,
        timeline,
      })
    } else {
      if (chart.userOptions.serverSideZoom) {
        let zoomInfo
        let zoomableSeries = chart.series.filter((s) => {
          const internalZoomInfo = s.userOptions.zoomInfo
          if (!zoomInfo && internalZoomInfo) {
            zoomInfo = Object.assign({}, internalZoomInfo)
          }
          return zoomInfo && zoomInfo.individualZoom
        })
        if (zoomableSeries.length === 0) {
          zoomableSeries = [chart.series[0]]
        }
        let startTime = Math.round(e.min)
        let endTime = Math.round(e.max)
        chart.showLoading('Loading data...')
        Promise.all(
          zoomableSeries.map((series) => {
            const fn =
              series.userOptions.onZoom ||
              chart.userOptions.onZoom ||
              defaultZoomFn
            return fn(e, startTime, endTime, zoomInfo || {}, chart.series).then(
              (result) => {
                return Promise.resolve(result)
              }
            )
          })
        ).finally(() => {
          setTimeout(() => {
            chart.hideLoading()
          }, 100)
        })
      }
    }
  }
}
