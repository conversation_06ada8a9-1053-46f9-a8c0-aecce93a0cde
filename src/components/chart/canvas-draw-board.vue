<template>
  <div class="drawing-board w-full overflow-auto text-center h-full">
    <canvas
      ref="drawing-board"
      class="drawing-board__canvas"
      :style="{ cursor: 'crosshair' }"
      :width="`${parseFloat(width)}px`"
      :height="`${parseFloat(height)}px`"
      @mousedown="handleMouseDown"
      @mouseup="handleMouseUp"
      @mousemove="handleMouseMove"
    ></canvas>
  </div>
</template>

<script>
const data = () => ({
  ref: null,
  current: {
    x: 0,
    y: 0,
  },
  previous: {
    x: 0,
    y: 0,
  },
  mouseDown: false,
})
const props = {
  msg: String,
  image: {
    type: String,
    default: undefined,
  },
  width: {
    default: 800,
    type: [String, Number],
  },
  height: {
    default: 800,
    type: [String, Number],
  },
  strokeColor: {
    default: '#f04e3e',
    type: String,
  },
  strokeWidth: {
    default: 2,
    type: Number,
  },
}
const computed = {
  currentMouse() {
    const rect = this.ref.getBoundingClientRect()
    const pos = {
      x: this.current.x - rect.left,
      y: this.current.y - rect.top,
    }
    return pos
  },
  ctx() {
    return this.ref.getContext('2d')
  },
}
const methods = {
  startDrawing() {
    if (this.mouseDown) {
      // this.ctx.clearRect(0, 0, parseFloat(this.width), parseFloat(this.height))
      this.ctx.lineTo(this.currentMouse.x, this.currentMouse.y)
      this.ctx.strokeStyle = this.strokeColor
      this.ctx.lineWidth = this.strokeWidth
      this.ctx.stroke()
    }
  },
  handleMouseMove(event) {
    this.current = {
      x: event.pageX,
      y: event.pageY,
    }
    this.startDrawing(event)
  },
  handleMouseDown(event) {
    this.mouseDown = true
    this.current = {
      x: event.pageX,
      y: event.pageY,
    }
    this.ctx.beginPath()
    this.ctx.moveTo(this.currentMouse.x, this.currentMouse.y)
  },
  handleMouseUp() {
    this.mouseDown = false
  },
  drawImageScaled(img, ctx) {
    var canvas = ctx.canvas
    var hRatio = canvas.width / img.width
    var vRatio = canvas.height / img.height
    var ratio = Math.min(hRatio, vRatio)
    var centerShiftX = (canvas.width - img.width * ratio) / 2
    var centerShiftY = (canvas.height - img.height * ratio) / 2
    ctx.clearRect(0, 0, canvas.width, canvas.height)
    ctx.drawImage(
      img,
      0,
      0,
      img.width,
      img.height,
      centerShiftX,
      centerShiftY,
      img.width * ratio,
      img.height * ratio
    )
  },
  reset() {
    this.ctx.clearRect(0, 0, parseFloat(this.width), parseFloat(this.height))
    var image = new Image()
    var _that = this
    image.onload = function () {
      _that.ref.width = this.naturalWidth
      _that.ref.height = this.naturalHeight
      _that.ctx.clearRect(0, 0, _that.ref.width, _that.ref.height)
      _that.ctx.drawImage(this, 0, 0)
      // _that.drawImageScaled(this, _that.ctx)
    }
    image.src = this.image
  },
  getImage() {
    return this.ref.toDataURL()
  },
}
export default {
  name: 'DrawingBoard',
  props,
  data,
  computed,
  mounted() {
    this.ref = this.$refs['drawing-board']
    this.ctx.translate(0.5, 0.5)
    this.ctx.imageSmoothingEnabled = false
    this.reset()
  },
  methods,
}
</script>
