<script>
import Merge from 'lodash/merge'
import Moment from 'moment'
import DefaultConfig from '@components/chart/default-config'

export default {
  name: 'SparklineOptions',
  props: {
    type: {
      type: String,
      default: 'line',
    },
    data: {
      type: Array,
      default() {
        return []
      },
    },
    timezone: {
      type: String,
      default: undefined,
    },
    dateFormat: {
      type: String,
      default: undefined,
    },
    minValue: {
      type: Number,
      default: 0,
    },
    color: {
      type: String,
      default: '#099dd9',
    },
  },
  computed: {
    options() {
      const _that = this
      return Object.freeze(
        Merge({}, DefaultConfig, {
          chart: {
            type: this.type,
            backgroundColor: null,
            borderWidth: 0,
            margin: [2, 0, 2, 0],
            height: 30,
            style: {
              overflow: 'visible',
              fontFamily: 'var(--chart-font-family)',
            },
            // small optimalization, saves 1-2 ms each sparkline
            skipClone: true,
          },
          // colors: ['#099dd9'],
          title: {
            text: '',
          },
          series: this.data.map((i) => ({
            ...i,
            pointStart: 1,
            color: this.color,
          })),
          xAxis: {
            visible: false,
            gridLineWidth: 0,
            labels: {
              enabled: false,
            },
            title: {
              text: null,
            },
            startOnTick: false,
            endOnTick: false,
            tickPositions: [],
          },
          yAxis: {
            endOnTick: false,
            startOnTick: false,
            gridLineWidth: 0,
            labels: {
              enabled: false,
            },
            title: {
              text: null,
            },
            tickPositions: [0],
            min: this.minValue,
          },
          legend: {
            enabled: false,
          },
          tooltip: {
            hideDelay: 0,
            outside: true,
            shared: true,
            useHTML: true,
            borderWidth: 1,
            borderColor: 'var(--border-color)',
            className: 'shadow-lg hc-tooltip-bg sparkline-tooltip',
            animation: false,
            borderRadius: 10,
            backgroundColor: 'var(--chart-tooltip-background)',
            fontWeight: 'normal',
            formatter: function () {
              const momentInstance = Moment.tz(
                this.x,
                _that.timezone || 'UTC'
              ).tz('UTC')
              const x = momentInstance.isValid()
                ? momentInstance.format(_that.dateFormat)
                : this.x || this.point.name
              let tooltip = `<div class="flex flex-col" style="max-width: 50vw;flex-shrink: 0"><div style="font-size: 11px" class="font-bold">${
                x || ''
              }</div>`
              this.points.map((point) => {
                const series = point.series
                let tooltipBody
                const p = point.point || point
                let formattedValue
                let counterName = series.userOptions.counterRawName.replace(
                  /[~^]/g,
                  '.'
                )
                let seriesName = `${counterName}`
                const seriesOptions = this.points[0]
                  ? this.points[0].series.options
                  : undefined

                let legend = `<svg height="8" width="8" class="inline-flex  flex-shrink-0 mr-1">
               <rect width="8" height="8" fill="${point.color}" class="highcharts-point"></rect>
                </svg>`
                if (
                  seriesOptions &&
                  seriesOptions.formattedValues &&
                  seriesOptions.formattedValues.length
                ) {
                  formattedValue = seriesOptions.formattedValues[p.index]
                }

                tooltipBody = `
                  <div class="flex flex-col w-full min-w-0 min-h-0 " style="max-width: 50vw; font-size:11px">
                    <div class="flex justify-between items-center">
                      <div class="flex h-full items-center">
                        <div class="flex items-center">
                          ${legend}
                        </div>
                      </div>
                      <div class="min-w-0 mr-auto  text-ellipsis">

                         ${(seriesName || '').toString()}

                      </div>
                      <div class="flex flex-shrink-0 justify-end ml-6" style="flex-shrink: 0">
                        <span class="value font-bold shrink-0 text-sm" style="flex-shrink: 0">
                          ${((formattedValue || this.y) === 0
                            ? 0
                            : formattedValue || this.y || ''
                          ).toString()}
                        </span>
                      </div>
                    </div>
                  </div>
                  `
                tooltip += `${tooltipBody}`
              })

              return `${tooltip}</div>`
            },
          },
          getTooltipContainer() {
            if (document.fullscreen || document.fullscreenElement) {
              return _that.$el.closest('.widget-view')
            }
            return null
          },
          destroyTooltipOnHide: true,
          plotOptions: {
            series: {
              animation: false,
              lineWidth: 1,
              shadow: false,
              connectNulls: true,
              states: {
                hover: {
                  lineWidth: 1,
                },
              },
              marker: {
                radius: 1,
                states: {
                  hover: {
                    radius: 2,
                  },
                },
              },
              fillOpacity: 0.25,
            },
            column: {
              negativeColor: 'var(--secondary-red)',
              borderColor: 'var(--primary-alt)',
              pointWidth: 10,
            },
          },
        })
      )
    },
  },
  render() {
    const options = this.options
    return this.$scopedSlots.default({
      options,
    })
  },
}
</script>
