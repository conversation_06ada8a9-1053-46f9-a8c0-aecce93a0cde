<script>
import Omit from 'lodash/omit'
import Color from 'color'
import NumberFormat from '@src/filters/number-format'
import { SEVERITY_MAP, colorMap } from '@data/monitor'
import Capitalize from 'lodash/capitalize'
import Merge from 'lodash/merge'
import { fillSeriesColorWithGradient } from '../chart-events'
import { UserPreferenceComputed } from '@state/modules/user-preference'
import getChartCommonOptions from './chart-common-options'
import { WidgetTypeConstants, FontSizeMap } from '@components/widgets/constants'
import { extractAvailabilityCounterType } from '@components/widgets/result-builder'
import {
  findAnomalyZones,
  calculatePlotBendAndPlotLines,
} from './chart-helpers'

export default {
  name: 'ChartOptions',
  inheritAttrs: false,
  props: {
    widgetType: { type: String, default: 'line' },
    height: { type: Number, default: undefined },
    isAvailabilityChart: { type: Boolean, default: false },
    vertical: { type: Boolean, default: false },
    stacked: { type: Boolean, default: false },
    dateTime: { type: Boolean, default: false },
    categories: { type: Array, default: undefined },
    chartType: { type: String, default: 'line' },
    guid: { type: String, default: undefined },
    unit: { type: String, default: undefined },
    enableLegend: { type: Boolean, default: false },
    maxY: { type: Number, default: undefined },
    hideXAxis: { type: Boolean, default: false },
    forPrint: { type: Boolean, default: false },
    legendOptions: {
      type: Object,
      default() {
        return {}
      },
    },
    // eslint-disable-next-line
    useInstanceInTooltip: { type: Boolean, default: true },
    data: {
      type: [Array, Object],
      required: true,
    },
    additionalSeries: {
      type: [Array, Object],
      default() {
        return []
      },
    },
    widget: {
      type: Object,
      default() {
        return {
          widgetProperties: {},
        }
      },
    },
    // eslint-disable-next-line
    allowDecimal: { type: Boolean, default: true },
    rangeSeries: { type: [Array, Object], default: undefined },
    alertSeries: { type: [Array, Object], default: undefined },
    forecastSeries: { type: [Object], default: undefined },
    rangeSeriesName: { type: String, default: undefined },
    isAnomalyRange: { type: Boolean, default: false },
    rangeSeriesColor: { type: String, default: undefined },
    forecastSeriesColor: { type: String, default: undefined },
    pieInnerSize: { type: String, default: undefined },
    markOutsideRange: { type: Boolean, default: false },
    disableSharedTooltip: {
      type: Boolean,
      default: false,
    },
    tooltipOutSide: {
      type: Boolean,
      default: false,
    },
    // eslint-disable-next-line
    serverSideZoom: { type: Boolean, default: false },
    disableAutoFetchServerSideZoomData: { type: Boolean, default: false },
    disableInitialAnimation: {
      type: Boolean,
      default: false,
    },
    yAxisLabelWidth: {
      type: Number,
      default: undefined,
    },
    seriesColor: {
      type: String,
      default: undefined,
    },
    shouldIgnoreObjectIdSeries: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    ...UserPreferenceComputed,
    appliedChartType() {
      const widgetType = this.widgetType
      if (
        [
          WidgetTypeConstants.STACKED_VERTICAL_BAR,
          WidgetTypeConstants.VERTICAL_BAR,
          WidgetTypeConstants.STACKED_HORIZONTAL_BAR,
          WidgetTypeConstants.HORIZONTAL_BAR,
          WidgetTypeConstants.HORIZONTAL,
        ].indexOf(widgetType) >= 0
      ) {
        return this.vertical ? 'column' : 'bar'
      } else if (widgetType === WidgetTypeConstants.STACKED_AREA) {
        return 'areaspline'
      } else if (widgetType === WidgetTypeConstants.SANKEY) {
        return 'sankey'
      } else if (widgetType === WidgetTypeConstants.STACKED_LINE) {
        return 'spline'
      } else if (
        [
          WidgetTypeConstants.PIE,
          WidgetTypeConstants.APPLICATION_TODAY_AVAILABILITY,
        ].includes(widgetType)
      ) {
        return 'pie'
      } else if (widgetType === WidgetTypeConstants.AREA) {
        return 'areaspline'
      } else if (widgetType === WidgetTypeConstants.LINE) {
        return 'spline'
      }
      if (this.chartType === 'line') {
        return 'spline'
      } else if (this.chartType === 'area') {
        return 'areaspline'
      }
      return this.chartType
    },
    isAvailabilityPie() {
      return (
        this.appliedChartType === 'pie' && this.isAvailabilityChart // &&
        // this.widget.category === WidgetTypeConstants.GAUGE
      )
    },
    options() {
      const isStacked = this.stacked
      const categories = this.categories
      const height = this.height
      const dateTime = this.dateTime
      const widgetProperties =
        this.widget.category === WidgetTypeConstants.GAUGE
          ? this.widget.widgetProperties || {}
          : (this.widget.widgetProperties || {}).styleSetting || {}
      const chartOptions = widgetProperties.chartOptions || {}
      const unit = this.unit
      let rangeSeries = this.rangeSeries
      const alertSeries = this.alertSeries
      const allowDecimal = this.allowDecimal
      const enableLegend = this.enableLegend
      const hideXAxis = this.hideXAxis
      const forecastSeries = this.forecastSeries
      const seriesColor = this.seriesColor
      const shouldIgnoreObjectIdSeries = this.shouldIgnoreObjectIdSeries

      const { plotBands, plotLines } = calculatePlotBendAndPlotLines(
        this.widget?.widgetProperties?.styleSetting?.markerProperty
      )

      let data = this.data || []

      const stacks = (this.widget.widgetProperties || {}).stacks

      if (stacks) {
        data = data.map((series) => {
          const stack = stacks.find((stack) =>
            stack.counters.includes(series.counter)
          )
          if (stack) {
            return {
              ...series,
              stack: stack.group,
            }
          }
          return series
        })
      }

      if (this.isAvailabilityChart) {
        if (this.appliedChartType === 'pie') {
          data = data.map((series) => {
            return {
              flipCounterAndSeriesName: true,
              ...series,
              data: (series.data || [])
                .filter((d) => d && d.name)
                .map((d) => ({
                  ...d,
                  name: Capitalize(
                    extractAvailabilityCounterType(d.name || '')
                  ),
                  color:
                    d.color ||
                    `var(--severity-${extractAvailabilityCounterType(
                      d.name || ''
                    ).toLowerCase()})`,
                })),
            }
          })
        } else {
          data = data.map((series) => {
            const counter = extractAvailabilityCounterType(
              series.severity ||
                series.entity ||
                series.counter ||
                series.name ||
                ''
            )
            return {
              ...series,
              color: `var(--severity-${counter.toLowerCase()})`,
              ...((series.counter || series.name).indexOf('percentage') >= 0
                ? { unit: '%' }
                : {}),
              name: Capitalize(counter || series.name || ''),
            }
            // series.borderColor = `var(--severity-${counter.toLowerCase()})`
            // series.borderWidth = 2
          })
        }
      }
      if (alertSeries) {
        if (data.length) {
          const severity = Object.keys(SEVERITY_MAP)
          data = [
            {
              ...data[0],
              turboThreshold: 0,
              data: data[0].data.map((point) => {
                if (alertSeries[point[0]]) {
                  const maxSeverity = severity.find(
                    (s) =>
                      alertSeries[point[0]] &&
                      alertSeries[point[0]][s] &&
                      alertSeries[point[0]][s].count > 0
                  )
                  return {
                    x: point[0],
                    y: point[1],
                    ...(maxSeverity
                      ? {
                          policy: alertSeries[point[0]],

                          marker: {
                            enabled: true,
                            symbol:
                              maxSeverity === 'CLEAR'
                                ? `circle`
                                : `url(data:image/svg+xml,%3Csvg width='512' height='512' viewBox='0 0 512 512' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M313.698 73.6253L475.573 369.917C487.916 391.044 488.185 416.181 476.108 437.306C464.033 458.431 442.296 471 417.875 471H94.1256C69.704 471 47.9677 458.431 35.8925 437.306C23.8154 416.181 24.0846 391.044 36.4274 369.917L198.302 73.6253C210.647 53.301 232.116 41 256 41C279.884 41 301.353 53.301 313.698 73.6253Z' fill='${Color(
                                    colorMap[maxSeverity.toLowerCase()]
                                  ).rgb()}'/%3E%3Cpath d='M255.836 417.517C241.042 417.517 229 405.518 229 390.776C229 376.034 241.042 364.035 255.836 364.035C270.63 364.035 282.672 376.034 282.672 390.776C282.672 405.518 270.63 417.517 255.836 417.517Z' fill='white'/%3E%3Cpath d='M255.836 337.293C270.63 337.293 282.672 325.294 282.672 310.552V176.846C282.672 162.104 270.63 150.104 255.836 150.104C241.042 150.104 229 162.104 229 176.846V310.552C229 325.294 241.042 337.293 255.836 337.293Z' fill='white'/%3E%3C/svg%3E%0A)`,
                            height: 15,
                            width: 15,

                            ...(maxSeverity === 'CLEAR'
                              ? {
                                  fillColor: '#89C540',
                                  radius: 4,
                                  lineColor: '#89C540',
                                }
                              : {}),
                            // fillColor: 'white',
                            // radius: 4,
                            // lineWidth: 2,
                            // lineColor: `var(--severity-${maxSeverity.toLowerCase()})`,
                          },
                        }
                      : {}),
                  }
                }
                return point
              }),
            },
            ...data.slice(1),
          ]
        }
      }

      if (seriesColor) {
        data = data.map((series) => {
          return {
            ...series,
            color: seriesColor,
          }
        })
      }
      if (forecastSeries) {
        data = [
          {
            ...data[0],
            data: ((data[0] || {}).data || []).concat(
              forecastSeries.futurePoints || []
            ),
          },
        ]
        rangeSeries = forecastSeries.range
      }

      if (shouldIgnoreObjectIdSeries) {
        const counter = this.widget?.groups?.[0]?.counters?.[0]?.counter?.key
        data = data.filter((s) => `${counter}.${s.aggr}` === s.counterRawName)
      }
      const options = getChartCommonOptions(
        [
          ...(this.isAnomalyRange
            ? data.map((d) => ({
                ...d,
                zoneAxis: 'x',
                zones: findAnomalyZones(rangeSeries, d.data),
              }))
            : data),
          ...(Array.isArray(this.additionalSeries)
            ? this.additionalSeries
            : [this.additionalSeries]),
          ...(rangeSeries
            ? [
                {
                  counter: (data[0] || {}).counter,
                  name: this.rangeSeriesName || 'Prediction Range',
                  type: 'areasplinerange',
                  lineWidth: 0,
                  linkedTo: ':previous',
                  color: this.rangeSeriesColor || `#099dd9`,
                  fillOpacity: 0.3,
                  zIndex: 0,
                  marker: {
                    enabled: false,
                    symbol: null,
                  },
                  data: rangeSeries,
                },
              ]
            : []),
        ],
        widgetProperties,
        {
          hasDateTime: dateTime,
          timezone: this.timezone,
          dateFormat: this.dateFormat,
          serverSideZoom: this.serverSideZoom,
          guid: this.guid,
          disableAutoFetchServerSideZoomData:
            this.disableAutoFetchServerSideZoomData,
          alertSeries: this.alertSeries,
          unit: this.unit,
          useInstanceInTooltip: this.useInstanceInTooltip,
          sharedTooltip: !this.disableSharedTooltip,
          outsideTooltip: this.tooltipOutSide,
          yAxisLabelWidth: this.yAxisLabelWidth,
        }
      )

      const _this = this

      const appliedOptions = Merge(
        options,
        // {
        //   ...(widgetProperties.verticalLegend
        //     ? {
        //         legend: {
        //           ...options.legend,
        //           layout: 'vertical',
        //         },
        //       }
        //     : {
        //         legend: {
        //           ...options.legend,
        //           layout: (options.legend || {}).layout
        //             ? (options.legend || {}).layout
        //             : 'horizontal',
        //         },
        //       }),
        // },
        {
          chart: {
            type: this.appliedChartType,
            ...(dateTime ? { zoomType: 'x' } : {}),
            ...(height ? { height } : {}),
          },
          ...(enableLegend
            ? {
                legend: {
                  enabled: true,
                  verticalAlign: 'bottom',
                  ...this.legendOptions,
                },
              }
            : {}),
          xAxis: {
            ...(categories && !dateTime
              ? {
                  categories,
                }
              : {}),
            ...(dateTime
              ? {
                  type: 'datetime',
                }
              : {}),
            ...(!isNaN(widgetProperties.rotation)
              ? {
                  labels: {
                    rotation: +widgetProperties.rotation,
                  },
                }
              : {}),
            ...(hideXAxis
              ? {
                  visible: false,
                }
              : {}),
            ...(forecastSeries && forecastSeries.futurePoints.length > 0
              ? {
                  plotLines: [
                    {
                      width: 1,
                      color: 'var(--secondary-red-light)',
                      value: forecastSeries.futurePoints[0][0],
                      zIndex: 3,
                    },
                  ],
                }
              : {}),
          },
          yAxis: {
            ...(unit
              ? {
                  labels: {
                    format: `{value} ${unit || ''}`,
                  },
                }
              : {}),
            ...(this.maxY
              ? {
                  max: this.maxY,
                  endOnTick: true,
                }
              : {}),
            ...(allowDecimal
              ? {
                  allowDecimal: allowDecimal,
                }
              : {}),
            ...(Array.isArray(plotBands) ? { plotBands } : {}),
            ...(Array.isArray(plotLines) ? { plotLines } : {}),
          },
          plotOptions: {
            series: {
              lineWidth: widgetProperties.lineWidth || 2,
              centerInCategory: true,
              ...(isStacked && !rangeSeries
                ? { stacking: 'normal', connectNulls: false }
                : {}),
              ...(['bar', 'column'].includes(this.appliedChartType)
                ? { minPointLength: 0 }
                : {}),
              events: {
                click: function (event) {
                  event.preventDefault()
                  let map = {}
                  let instanceMap = {}
                  if (this.chart.xAxis[0].categories) {
                    // this.chart.series[0].userOptions.ids.reduce(
                    //   (prev, id) => ({
                    //     ...prev,
                    //     [id]: this.chart.series[0].userOptions.counter.replace(
                    //       /\.(min|max|avg|sum|count|last)$/,
                    //       ''
                    //     ),
                    //   }),
                    //   {}
                    // )
                    this.chart?.series?.[0]?.userOptions?.ids?.forEach((id) => {
                      map[id] =
                        this.chart.series[0].userOptions.counter.replace(
                          /\.(min|max|avg|sum|count|last)$/,
                          ''
                        )
                    })

                    const categories = this.chart.xAxis[0].categories
                    const instanceType =
                      _this.widget?.groups?.[0]?.counters?.[0]?.counter
                        ?.instanceType

                    const currentSeries =
                      this.chart?.series?.[0]?.userOptions?.resultByResolver?.[
                        event.point.index
                      ] || {}

                    const instanceIndex = categories.includes(
                      `${currentSeries[instanceType]}: ${
                        currentSeries[
                          Object.keys(currentSeries)[
                            Object.keys(currentSeries).findIndex(
                              (s) => s !== instanceType
                            )
                          ]
                        ]
                      }`
                    )
                      ? Object.keys(currentSeries).findIndex(
                          (s) => s !== instanceType
                        )
                      : Object.keys(currentSeries).findIndex(
                          (s) => s === instanceType
                        )

                    const monitorIndex = instanceIndex === 0 ? 1 : 0

                    const currentSeriesKeys = Object.keys(currentSeries)
                    if (currentSeries) {
                      instanceMap[
                        `${currentSeries[currentSeriesKeys[instanceIndex]]}${
                          _this.$constants.SEPARATOR_WITH_SLASH
                        }${
                          currentSeries.monitor ||
                          currentSeries[currentSeriesKeys[monitorIndex]]
                        }`
                      ] =
                        currentSeries.monitor ||
                        currentSeries[currentSeriesKeys[monitorIndex]]
                    }
                  } else {
                    // map = this.chart.series.reduce(
                    //   (prev, item, index) => ({
                    //     ...prev,
                    //     [item.userOptions.id || index]:
                    //       item.userOptions.counterRawName.replace(
                    //         /\.(min|max|avg|sum|count|last)$/,
                    //         ''
                    //       ),
                    //   }),
                    //   {}
                    // )

                    this.chart.series.forEach((item, index) => {
                      map[item.userOptions.id || index] =
                        item.userOptions.counterRawName.replace(
                          /\.(min|max|avg|sum|count|last)$/,
                          ''
                        )

                      let instanceName

                      if (item.userOptions.instance) {
                        const resultByResolverValues = Object.values(
                          item?.userOptions?.resultByResolver || {}
                        )

                        if (resultByResolverValues?.length) {
                          const instanceIndex = resultByResolverValues
                            .map((key) => key.toLowerCase())
                            .indexOf(item?.userOptions?.instance)

                          if (instanceIndex >= 0) {
                            const resultByResolverkeys = Object.keys(
                              item?.userOptions?.resultByResolver || {}
                            )
                            instanceName =
                              item?.userOptions.resultByResolver[
                                resultByResolverkeys[instanceIndex]
                              ]
                          }
                        }

                        instanceMap[
                          `${
                            instanceName || item?.userOptions?.instance || index
                          }${_this.$constants.SEPARATOR_WITH_SLASH}${
                            item?.userOptions?.monitor
                          }`
                        ] = item?.userOptions?.monitor
                      }
                    })
                  }
                  if (map) {
                    _this.$emit('drilldown', map, event, instanceMap)
                  }
                },
              },
              point: {
                events: {
                  mouseOver: function (event) {
                    if (
                      Array.isArray(event.target.series.options.isOutOfRange)
                    ) {
                      if (
                        event.target.series.options.isOutOfRange[
                          event.target.index
                        ]
                      ) {
                        event.target.update({ color: 'var(--secondary-red)' })
                      } else {
                        event.target.update({
                          color: event.target.series.color,
                        })
                      }
                    }
                  },
                },
              },
            },
            areasplinerange: {
              marker: {
                enabled: true,
              },
            },
            pie: {
              allowPointSelect: true,
              cursor: 'pointer',
              ...(this.isAvailabilityPie || this.disableInitialAnimation
                ? { animation: false }
                : {}),
              ...(this.pieInnerSize || this.isAvailabilityPie
                ? { innerSize: this.pieInnerSize || '70%' }
                : {}),
              dataLabels: {
                enabled: widgetProperties.pieDataLabelsEnabled,
                formatter: function () {
                  const index = this.point.index
                  const formattedValues =
                    this.point.series.userOptions.formattedValues

                  const name = (this.point.name || '').substring(
                    0,
                    Math.min((this.point.name || '').length, 50)
                  )

                  const labelValue = `${
                    name.length === (this.point.name || '').length
                      ? this.point.name
                      : name + '...'
                  }`

                  if (formattedValues && formattedValues.length) {
                    return `${labelValue}: ${formattedValues[index]}`
                  }
                  return `${labelValue}: ${this.point.y}`
                },
                rotation: isNaN(widgetProperties.rotation)
                  ? 0
                  : widgetProperties.rotation,
                style: {
                  fontWeight: 'normal',
                  fontFamily: 'Poppins',
                  fontSize: FontSizeMap['small'],
                  ...(widgetProperties.ellipsisEnabled
                    ? {
                        whiteSpace: 'nowrap',
                        textOverflow: 'ellipsis',
                      }
                    : {}),
                },
              },
              showInLegend: true,
              events: {
                click(event) {
                  // eslint-disable-next-line
                  event.preventDefault()
                  let map
                  let instanceMap = {}

                  if (
                    this.chart.xAxis[0].categories &&
                    this.chart.series[0].userOptions.ids
                  ) {
                    map = this.chart.series[0].userOptions.ids.reduce(
                      (prev, id) => ({
                        ...prev,
                        [id]: this.chart.series[0].userOptions.counter.replace(
                          /\.(min|max|avg|sum|count|last)$/,
                          ''
                        ),
                      }),
                      {}
                    )

                    const categories = this.chart.xAxis[0].categories
                    const instanceType =
                      _this.widget?.groups?.[0]?.counters?.[0]?.counter
                        ?.instanceType

                    const currentSeries =
                      this.chart.series[0].userOptions.resultByResolver[
                        event.point.index
                      ] || {}

                    const instanceIndex = categories.includes(
                      `${currentSeries[instanceType]}: ${
                        currentSeries[
                          Object.keys(currentSeries)[
                            Object.keys(currentSeries).findIndex(
                              (s) => s !== instanceType
                            )
                          ]
                        ]
                      }`
                    )
                      ? Object.keys(currentSeries).findIndex(
                          (s) => s !== instanceType
                        )
                      : Object.keys(currentSeries).findIndex(
                          (s) => s === instanceType
                        )

                    const monitorIndex = instanceIndex === 0 ? 1 : 0

                    const currentSeriesKeys = Object.keys(currentSeries)
                    if (currentSeries) {
                      instanceMap[
                        `${currentSeries[currentSeriesKeys[instanceIndex]]}${
                          _this.$constants.SEPARATOR_WITH_SLASH
                        }${
                          currentSeries.monitor ||
                          currentSeries[currentSeriesKeys[monitorIndex]]
                        }`
                      ] =
                        currentSeries.monitor ||
                        currentSeries[currentSeriesKeys[monitorIndex]]
                    }
                  } else {
                    map = this.chart.series.reduce(
                      (prev, item, index) => ({
                        ...prev,
                        [item?.userOptions?.id || index]:
                          item?.userOptions?.counterRawName?.replace(
                            /\.(min|max|avg|sum|count|last)$/,
                            ''
                          ),
                      }),
                      {}
                    )

                    instanceMap = this.chart.series.reduce(
                      (prev, item, index) => {
                        let instanceName

                        if (item?.userOptions?.instance) {
                          const resultByResolverValues = Object.values(
                            item?.userOptions?.resultByResolver || {}
                          )

                          if (resultByResolverValues?.length) {
                            const instanceIndex = resultByResolverValues
                              .map((key) => key.toLowerCase())
                              .indexOf(item?.userOptions?.instance)

                            if (instanceIndex >= 0) {
                              const resultByResolverkeys = Object.keys(
                                item?.userOptions?.resultByResolver || {}
                              )
                              instanceName =
                                item?.userOptions?.resultByResolver[
                                  resultByResolverkeys[instanceIndex]
                                ]
                            }
                          }

                          instanceMap[
                            `${
                              instanceName ||
                              item?.userOptions?.instance ||
                              index
                            }${_this.$constants.SEPARATOR_WITH_SLASH}${
                              item?.userOptions?.monitor
                            }`
                          ] = item?.userOptions?.monitor
                        }

                        return {
                          ...prev,
                          [item?.userOptions?.id || index]:
                            item?.userOptions?.counterRawName?.replace(
                              /\.(min|max|avg|sum|count|last)$/,
                              ''
                            ),
                        }
                      },

                      {}
                    )
                  }
                  if (map) {
                    _this.$emit('drilldown', map, event, instanceMap)
                  }
                },
              },
            },
          },
          ...(rangeSeries || alertSeries
            ? {
                tooltip: {
                  crosshairs: true,
                  shared: true,
                },
              }
            : {}),
        },
        {
          ...(this.appliedChartType === 'pie'
            ? {
                legend: {
                  align: 'right',
                  layout: 'vertical',
                  itemMarginTop: 8,
                  itemMarginBottom: 8,
                  verticalAlign: 'middle',
                  itemWidth: 200,
                  labelFormatter() {
                    const index = this.index
                    const formattedValues =
                      this.series.userOptions.formattedValues
                    if (formattedValues && formattedValues.length) {
                      return `${this.name}: ${formattedValues[index]}`
                    }
                    return `${this.name}: ${NumberFormat(this.y)}`
                  },
                  ...this.legendOptions,
                },
              }
            : {}),
        }
      )

      return Merge(
        appliedOptions,
        Omit(chartOptions, ['stacks']),
        this.forPrint
          ? {
              plotOptions: {
                series: {
                  animation: null,
                },
              },
              chart: {
                events: {
                  load: function () {
                    fillSeriesColorWithGradient.call(this)
                    _this.$emit('rendered', this.data)
                  },
                  redraw: (e) => {
                    _this.$emit('rendered', this.data)
                  },
                },
              },
              xAxis: {
                labels: {
                  style: {
                    color: 'var(--page-text-color)',
                    fontSize: '1rem',
                  },
                },
              },
              yAxis: {
                labels: {
                  style: {
                    color: 'var(--page-text-color)',
                    fontSize: '1rem',
                  },
                },
              },
              legend: {
                itemStyle: {
                  fontSize: '1.2rem',
                },
              },
            }
          : {}
      )
    },
  },
  render() {
    return this.$scopedSlots.default({
      options: this.options,
    })
  },
}
</script>
