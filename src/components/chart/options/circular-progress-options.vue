<script>
import Merge from 'lodash/merge'
import DefaultConfig from '@components/chart/default-config'
import NumberFormatter from '@src/filters/number-format'

export default {
  name: 'CircularProgressOptions',
  props: {
    title: {
      type: String,
      default: undefined,
    },
    height: {
      type: Number,
      default: undefined,
    },
    width: {
      type: Number,
      default: undefined,
    },
    color: {
      type: String,
      default: 'var(--primary)',
    },
    max: {
      type: Number,
      default: 100,
    },
    data: {
      type: Object,
      default() {
        return {}
      },
    },
    suffix: {
      type: String,
      default: '%',
    },
    prefix: {
      type: String,
      default: '',
    },
  },
  computed: {
    options() {
      const data = this.data
      const radius = '100%'
      const innerRadius = '88%'
      return Merge({}, DefaultConfig, {
        chart: {
          type: 'solidgauge',
          height: this.height,
          width: this.width,
        },
        title: {
          text: null,
          align: 'center',
        },
        exporting: {
          enabled: false,
        },

        tooltip: {
          enabled: false,
          crosshairs: false,
        },
        colors: [this.color],
        pane: {
          startAngle: 0,
          endAngle: 360,
          background: {
            backgroundColor: 'var(--neutral-lighter)',
            innerRadius: innerRadius,
            outerRadius: radius,
            borderWidth: 0,
          },
          backgroundColor: 'var(--neutral-lighter)',
          size: '100%',
        },
        yAxis: {
          lineWidth: 0,
          tickWidth: 0,
          minorTickInterval: null,
          title: {
            text: this.title,
            y: -45,
          },
          labels: {
            enabled: false,
          },
          min: 0,
          max: this.max,
          // labels: {
          //   y: 0,
          // },
        },
        plotOptions: {
          solidgauge: {
            dataLabels: {
              enabled: true,
              borderWidth: 0,
              format: `
              <div>
                ${
                  this.prefix
                    ? `<span style="font-size: 0.8rem;">${this.prefix}</span>`
                    : ''
                }
                <span style="font-size: 1.5rem;">${
                  data.formattedValue
                    ? `${data.formattedValue.replace(
                        /^(\d+(\.\d+)?)\s?(\w+)$/,
                        '$1'
                      )}`
                    : `${
                        data.suffix || data.prefix
                          ? `{y}`
                          : NumberFormatter(data.value)
                      }`
                }</span>
                ${
                  this.suffix
                    ? `<span style="font-size: 0.8rem;">${this.suffix}</span>`
                    : ''
                }
              </div>`,
            },
            linecap: 'round',
            stickyTracking: false,
            rounded: true,
          },
        },
        series: [
          {
            name: data.name,
            data: [{ y: data.value, radius, innerRadius }],
            size: '110%',
          },
        ],
      })
    },
  },
  render() {
    const options = this.options
    return this.$scopedSlots.default({
      options,
    })
  },
}
</script>
