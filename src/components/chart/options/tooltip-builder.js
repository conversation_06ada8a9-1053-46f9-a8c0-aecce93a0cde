import Moment from 'moment'
import Uniq from 'lodash/uniq'
import Template from 'lodash/template'
import { SEVERITY_MAP } from '@data/monitor'
import { getAllowedUnit } from '@utils/unit-checker'
import applyUnit from '@utils/unit-applier'
import NumberFormat from '@src/filters/number-format'

export default class TooltipBuilder {
  _points = []
  _context = null
  _options = {}
  _allPointCounters = []
  _baseCounter = null

  constructor(options, context) {
    this._context = context
    this._options = options

    this.__getCounterPoints()
  }

  __getCounterPoints() {
    this._points = this._context.points
      ? this._context.points
      : [this._context.point]
    let allPointCounters = []
    this._points.forEach((point) => {
      if (point.series && point.series.userOptions.counter) {
        this._baseCounter = point.series.userOptions.counter
        allPointCounters.push(point.series.userOptions.counter)
      }
    })
    this._allPointCounters = Uniq(allPointCounters)
  }

  __getDateTimeHtml() {
    const momentInstance = this._options.hasDateTime
      ? Moment.tz(this._context.x, this._options.timezone || 'UTC').tz('UTC')
      : {
          isValid() {
            return false
          },
        }
    let x = momentInstance.isValid()
      ? momentInstance.format(this._options.dateFormat)
      : this._context.x !== undefined
      ? this._context.x
      : this._context.series.userOptions.flipCounterAndSeriesName
      ? this._context.series.name
      : this._context.point.name
    let tooltipHeader = ''

    if (
      this._context?.series?.userOptions?.resultByResolver?.[0]?.[
        'system.process'
      ]
    ) {
      x = x?.split('|')?.[0]?.replace('(', '| ')
    } else if (
      this._context?.points?.[0]?.series?.userOptions?.resultByResolver?.[0]?.[
        'system.process'
      ]
    ) {
      x = x?.split('|')?.[0]?.replace('(', '| ')
    }

    // add date time on header only if multiple points
    tooltipHeader +=
      momentInstance.isValid() || (x && String(x).indexOf('highchart') === -1)
        ? `<div style="font-size: 11px" class="font-bold">${x}</div>`
        : ''

    return tooltipHeader
  }

  _buildAlertWithRangeTooltip(y, policy, unit, counter) {
    const severity = Object.keys(SEVERITY_MAP)
    return severity
      .filter((p) => policy[p])
      .map(
        (p) => `
        <div class="flex items-center">
          <div class="severity-dot-wrapper mr-1">
            <div class="severity-dot-shadow">
              <div class="severity-dot ${p.toLowerCase()}" title="${p}"></div>
            </div>
          </div>
          Value&nbsp;|&nbsp;Avg&nbsp;|&nbsp;Count&nbsp;:&nbsp;
          <b class="text ${p.toLowerCase()}">
          ${counter ? applyUnit(counter, policy[p].value) : policy[p].value}
          </b>&nbsp;|&nbsp;
          <b>${counter ? applyUnit(counter, y) : y}</b>&nbsp;|&nbsp;
          <b>${NumberFormat(policy[p].count)}</b>
        </div>
    `
      )
      .join('')
  }

  __getRangeHtml(point, value) {
    return `<span class="value font-bold">${point.from} - ${point.to} : ${value}</span>`
  }

  __getLowHighHtml(point, counter) {
    return `<span class="value font-bold">${
      counter ? applyUnit(counter, point.low) : NumberFormat(point.low)
    } - ${
      counter ? applyUnit(counter, point.high) : NumberFormat(point.high)
    }</span>`
  }

  __getDurationHtml(point, value) {
    return `<span class="value font-bold">${value} </span><div>(${point.duration})</div>`
  }

  __getDefaultValueHtml(value) {
    return `<span class="value font-bold shrink-0	${
      this._allPointCounters.length === 1 ? 'text-sm' : 'text-sm'
    }" style="flex-shrink: 0">${value}</span>`
  }

  __getSinglePointHtml(point) {
    const anomalySeries = point.series.chart.series.find(
      (s) => s.userOptions.isAnomalySeries
    )

    const series = point.series
    const pointIndex = point.index
    let isOutOfRangePoint = false
    if (anomalySeries && point.series.type !== 'areasplinerange') {
      isOutOfRangePoint = anomalySeries
        ? anomalySeries.userOptions.isOutOfRange[pointIndex]
        : false
      if (isOutOfRangePoint) {
        series.markerGroup.element
          .querySelector('path:first-child')
          .setAttribute('fill', anomalySeries.color)
        series.markerGroup.element
          .querySelector('path:last-child')
          .setAttribute('stroke', anomalySeries.color)
      } else {
        series.markerGroup.element
          .querySelector('path:first-child')
          .setAttribute('fill', series.color)
        series.markerGroup.element
          .querySelector('path:last-child')
          .setAttribute('stroke', series.color)
      }
    }
    let legend = `<svg height="8" width="8" class="inline-flex flex-shrink-0 mr-1">
      <rect width="8" height="8" fill="${
        isOutOfRangePoint ? anomalySeries.userOptions.color : point.color
      }" class="highcharts-point"></rect>
      </svg>`
    let rawValue = point.y
    let counter = point.series.userOptions.counter || this._baseCounter || ''
    counter = counter.replace(/[~^]/g, '.')
    if (point.series.type === 'sankey') {
      if (point.isNode) {
        rawValue = point.sum
      } else {
        rawValue = point.weight
      }
    }
    let value =
      counter && getAllowedUnit(counter)
        ? applyUnit(counter, rawValue)
        : `${NumberFormat(rawValue)}${
            this._options.unit ? ` ${this._options.unit}` : ''
          }`
    if (
      !counter &&
      (point.series.userOptions.formattedValues || []).length > 0
    ) {
      value = point.series.userOptions.formattedValues[point.index]
    }
    let valueHtml = ''
    let instance = this._options.useInstanceInTooltip
      ? series.options.instance
      : null
    let counterName = series.userOptions.flipCounterAndSeriesName
      ? point.name
      : series.name
    let seriesName = `${counterName} ${
      instance && series.name.indexOf(instance) === -1 ? `(${instance}) ` : ''
    }`
    if (series.userOptions['system.process']) {
      seriesName = series.name.split('|')[0].replace('(', '| ')
    }
    if (point.from && point.to) {
      valueHtml = this.__getRangeHtml(point, value)
    } else if (point.low !== undefined && point.high !== undefined) {
      valueHtml = this.__getLowHighHtml(point, counter)
    } else {
      if (point.duration) {
        valueHtml = this.__getDurationHtml(point, value)
      } else {
        valueHtml = this.__getDefaultValueHtml(value)
      }
      if (point && point.options && point.options.policy) {
        // html += this._buildAlertWithRangeTooltip(
        //   point.y,
        //   point.options.policy,
        //   getAllowedUnit(counter),
        //   counter
        // )
      }
    }
    // if (this._points.length === 1) {
    //   seriesName += `${this.__getDateTimeHtml()}`
    // }
    return Template(
      `
      <div class="flex justify-between w-full items-center">
        <div class="flex h-full ${
          // this._points.length === 1 &&
          ((this._points[0] || {}).series || {}).type !== 'pie'
            ? 'self-start'
            : 'items-center'
        }" ${
        // this._points.length === 1 &&
        ((this._points[0] || {}).series || {}).type !== 'pie'
          ? 'style="margin-top: 6px"'
          : ''
      }>
          <div class="flex items-center">
            <%= legend %>
          </div>
        </div>
        <div class="min-w-0 mr-auto">
          <%= seriesName %>
        </div>
        <div class="flex-shrink-0 justify-end flex ml-6">
          <%= value %>
        </div>
      </div>
    `
    )({
      legend,
      seriesName,
      value: valueHtml,
    })
  }

  __getTooltipBody() {
    let tooltipBody = `<div class="flex flex-col min-w-0 min-h-0 " style="max-width: 50vw; font-size:11px">
      ${this._points
        .filter((point) => {
          if (point.series.type !== 'sankey') {
            return point.y !== null
          }
          return true
        })
        .map((point) => this.__getSinglePointHtml(point.point || point))
        .join('')}
      </div>`

    return tooltipBody
  }

  html() {
    let tooltipHeader = ''
    let tooltipBody = this.__getTooltipBody()
    if (this._points.length >= 1) {
      tooltipHeader = `<div class="flex flex-col" style="max-width: 50vw">${this.__getDateTimeHtml()}</div>`
    }
    // else {
    //   tooltipBody += this.__getDateTimeHtml()
    // }

    return `${
      ((this._points[0] || {}).series || {}).type !== 'pie'
        ? `${tooltipHeader}${tooltipBody}`
        : `${tooltipHeader}${tooltipBody}`
    }`
  }
}
