export function findAnomalyZones(intervals = [], values) {
  let validColor = '#099dd9'
  let anomalyColor = '#c84235'
  let zones = []
  let prev = null
  let anomalyType = null // 1 for above Confidence interval. -1 for below
  for (let i = 0; i < (values || []).length; i++) {
    let interval = intervals[i] || []
    let value = values[i]
    let zone = { value: value[0] }

    // point is an anomaly
    if (value[1] < interval[1]) {
      anomalyType = -1
      zone.color = anomalyColor
    } else if (value[1] > interval[2]) {
      anomalyType = 1
      zone.color = anomalyColor
    } else {
      zone.color = validColor
    }

    // Push prev zone if colors should be different
    // Update prev zone
    if (prev != null && prev.color !== zone.color) {
      const interIdx = anomalyType === 1 ? 2 : 1
      let { m: m1, b: b1 } = findSlopeAndYIntercept(
        [intervals[i - 1][0], intervals[i - 1][interIdx]],
        [interval[0], [interval[interIdx]]]
      )
      let { m: m2, b: b2 } = findSlopeAndYIntercept(values[i - 1], value)
      let { x } = findLineIntercept(m1, b1, m2, b2)

      prev.value = x
      zones.push(prev)
    }
    prev = zone
  }

  zones.push({ value: Date.UTC(9999), color: validColor })
  return zones
}

function findSlopeAndYIntercept(p1, p2) {
  const m = (p2[1] - p1[1]) / (p2[0] - p1[0])
  const b = p1[1] - m * p1[0]
  return { m, b }
}

function findLineIntercept(m1, b1, m2, b2) {
  let x = (b2 - b1) / (m1 - m2)
  let y = m1 * x + b1
  return { x, y }
}

export function calculatePlotBendAndPlotLines(markerProperty = []) {
  const plotLines = markerProperty
    .filter((p) => p.markerType === 'line')
    .map((pl) => {
      const isValid = pl.markerColor && pl.markerLineType

      if (isValid) {
        return {
          color: `var(--severity-${pl.markerColor.toLowerCase()})`,
          width: pl.markerLineType === 'solid.bold' ? 2 : 1,
          value: +pl.markerThreshold,
          zIndex: 3,
          dashStyle: pl.markerLineType,
          label: {
            text: pl.markerLabel,
            style: {
              color: `var(--severity-${pl.markerColor.toLowerCase()})`,
            },
          },
        }
      }
    })
    .filter(Boolean)
  const plotBands = markerProperty
    .filter((p) => p.markerType !== 'line')
    .map((pl) => {
      const isValid = pl.markerColor && pl.markerType && pl.markerLineType

      if (isValid) {
        return {
          color: `var(--severity-${pl.markerColor.toLowerCase()}-lightest)`,
          label: {
            text: pl.markerLabel,
            style: {
              color: `var(--severity-${pl.markerColor.toLowerCase()})`,
            },
          },
          to: pl.start,
          from: pl.end,
          key: pl.markerColor.toLowerCase(),
          dashStyle: pl.markerLineType,
          markerColor: `var(--severity-${pl.markerColor.toLowerCase()})`,
          zIndex: 3,
        }
      }
    })
    .filter(Boolean)

  const allPlotLines = plotLines
    .concat(
      plotBands.map((pb) => {
        return [
          {
            color: pb.markerColor,
            width: pb.dashStyle === 'solid.bold' ? 2 : 1,
            value: +pb.to,
            zIndex: 3,
            dashStyle: pb.dashStyle,
          },
          {
            color: pb.markerColor,
            value: +pb.from,
            width: pb.dashStyle === 'solid.bold' ? 2 : 1,
            dashStyle: pb.dashStyle,
            zIndex: 3,
          },
        ]
      })
    )
    .flat()

  return { plotLines: allPlotLines, plotBands }
}
