<script>
import Reverse from 'lodash/reverse'
import Color from 'color'
import Merge from 'lodash/merge'
import getChartCommonOptions from './chart-common-options'

export default {
  name: 'RadarChartOptions',
  props: {
    unit: {
      type: String,
      default: undefined,
    },
    data: {
      type: Object,
      default() {
        return {}
      },
    },
    min: {
      type: Number,
      default: 0,
    },
    max: {
      type: Number,
      default: 0,
    },
    yAxisLabels: {
      type: Boolean,
      default: false,
    },
    widget: {
      type: Object,
      default() {
        return {
          widgetProperties: {},
        }
      },
    },
    ranges: {
      type: Array,
      default() {
        return [
          {
            max: -67,
            min: -120,
            color: 'var(--secondary-red)',
            radius: '100%',
            name: 'Weak',
          },
          {
            max: -55,
            min: -66,
            color: 'var(--secondary-yellow)',
            radius: '85%',
            name: 'Good',
          },
          {
            max: 0,
            min: -54,
            color: 'var(--secondary-green)',
            radius: '65%',
            name: 'Strong',
          },
        ]
      },
    },
  },
  computed: {
    options() {
      const splittedSeries = this.splitSeries(this.data, this.ranges)
      const options = getChartCommonOptions(
        splittedSeries,
        this.widget.widgetProperties,
        {
          hasDateTime: false,
          unit: this.unit,
        }
      )

      const _that = this

      const finalOptions = Merge({}, options, {
        chart: {
          polar: true,
          type: 'scatter',
        },
        xAxis: {
          tickmarkPlacement: 'on',
          lineWidth: 0,
          gridLineWidth: 0,
          labels: {
            enabled: false,
          },
        },
        pane: [
          {
            size: '90%',
            center: ['50%', '50%'],
            endAngle: 360,
            background: [
              {
                outerRadius: '110%',
                shape: 'circle',
                backgroundColor: {
                  radialGradient: {
                    cx: 0.5,
                    cy: 0.5,
                    r: 0.5,
                  },
                  stops: [
                    [0, Color('#89c540').alpha(0.8).toString()],
                    [1, Color('#89c540').alpha(0).toString()],
                  ],
                },
                borderWidth: 0,
              },
              ...Reverse(this.ranges).map((r, index, all) => ({
                size: '90%',
                outerRadius: r.radius,
                innerRadius: all[index + 1] ? all[index + 1].radius : '0%',
                backgroundColor: null,
                borderWidth: 2,
                borderColor: r.color,
              })),
            ],
          },
        ],
        yAxis: {
          // min: this.min,
          // max: this.max,
          gridLineWidth: 0,
          reversedStacks: false,
          reversed: true,
          labels: {
            enabled: this.yAxisLabels,
            format: `{text} ${this.unit}`,
          },
        },
        tooltip: {
          shared: false,
          crosshairs: false,
          formatter() {
            return `<p style="font-weight: 500">${
              this.series.userOptions.clients[this.point.index]
            }</p>
            ${this.point.y} ${_that.unit}`
          },
        },
        plotOptions: {
          series: {
            stacking: 'normal',
            marker: {
              enabled: true,
              symbol: 'circle',
              fillColor: null,
              lineColor: null,
              lineWidth: 2,
              radius: 4,
              states: {
                hover: {
                  enabled: true,
                  lineWidthPlus: 1,
                },
              },
            },
          },
        },
      })
      return finalOptions
    },
  },
  methods: {
    splitSeries(series, ranges) {
      const points = series.data.map((p, index) => ({
        y: p,
        client: series.clients[index],
      }))
      const map = {}
      ranges.forEach((range) => {
        map[range.name] = []
      })
      points.forEach((point) => {
        const rangeToAdd = (
          ranges.find((r) => point.y >= r.min && point.y <= r.max) || {}
        ).name
        if (rangeToAdd) {
          map[rangeToAdd].push(point)
          const remainingRanges = Object.keys(map).filter(
            (rangeName) => rangeName !== rangeToAdd
          )
          remainingRanges.forEach((r) => {
            map[r].push(null)
          })
        }
      })
      return Object.keys(map).map((name, index) => ({
        name,
        color: 'var(--secondary-red-light)',
        // color: (ranges.find((r) => r.name === name) || {}).color,
        data: map[name].map((p) => (p ? p.y : p)),
        clients: map[name].map((p) => (p ? p.client : p)),
      }))
    },
  },
  render() {
    return this.$scopedSlots.default({
      options: this.options,
    })
  },
}
</script>
