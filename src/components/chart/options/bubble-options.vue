<script>
import Merge from 'lodash/merge'
import NumberFormat from '@src/filters/number-format'
// import getChartCommonOptions from './chart-common-options'

export default {
  name: 'BubbleOptions',
  props: {
    height: { type: Number, default: undefined },
    // eslint-disable-next-line
    useInstanceInTooltip: { type: Boolean, default: true },
    data: {
      type: [Array, Object],
      required: true,
    },
    drilldown: {
      type: [Array, Object],
      default: undefined,
    },
    additionalSeries: {
      type: [Array, Object],
      default() {
        return []
      },
    },
    chartOptions: {
      type: Object,
      default: undefined,
    },
  },
  computed: {
    options() {
      const _this = this
      const height = this.height
      const chartOptions = this.chartOptions || {}
      // let data = this.data || []
      // const options = getChartCommonOptions(
      //   [
      //     ...data,
      //     ...(Array.isArray(this.additionalSeries)
      //       ? this.additionalSeries
      //       : [this.additionalSeries]),
      //   ],
      //   {},
      //   {
      //     useInstanceInTooltip: this.useInstanceInTooltip,
      //     omitTooltipFormatter: true,
      //   }
      // )

      const appliedOptions = {
        title: {
          text: null, // widgetName || '',
          align: 'left',
          style: {
            color: 'var(--page-text-color)',
            fontSize: '14px',
          },
        },
        chart: {
          type: 'packedbubble',
          backgroundColor: 'var(--page-background-color)',
          style: {
            fontFamily: "'Poppins', sans-serif",
          },
          ...(height ? { height } : {}),
        },
        legend: {
          align: 'center',
          symbolWidth: 11,
          symbolHeight: 11,
          symbolRadius: 1,
          itemStyle: {
            color: 'var(--page-text-color)',
            fontWeight: 'normal',
            opacity: '1',
            fontSize: '10px', // FontSizeMap['small'],
          },
          itemHoverStyle: {
            color: 'var(--page-text-color)',
            fontWeight: 'normal',
            opacity: '1',
          },
          itemHiddenStyle: {
            opacity: '0.5',
          },
        },
        plotOptions: {
          packedbubble: {
            cursor: 'pointer',
            maxSize: '90%',
            draggable: false,
            layoutAlgorithm: {
              // gravitationalConstant: 0.1,
              splitSeries: true,
              seriesInteraction: false,
              dragBetweenSeries: false,
              enableSimulation: false,
              parentNodeLimit: true,
              // integration: 'verlet',
              // turboThreshold: 100000,
            },
            dataLabels: {
              enabled: true,
              className: 'bubbleLabelText',
              format: '{point.name}',
              filter: {
                property: 'y',
                operator: '>',
                value: 5,
              },
              style: {
                color: 'var(--page-text-color)',
                textOutline: 'none',
              },
            },
            events: {
              click: function (e) {
                if (
                  e.point.options &&
                  e.point.options.resourceType === 'event.source'
                ) {
                  _this.$emit('source-click', {
                    source: {
                      name: e.point.options.name,
                      id: e.point.options.resourceId,
                      type: e.point.options.resourceType,
                    },
                    plugin: {
                      name: e.point.series.options.name,
                      type: e.point.series.options.resourceType,
                      id: e.point.series.options.resourceId,
                    },
                  })
                }
              },
            },
          },
        },
        tooltip: {
          useHTML: true,
          borderWidth: 1,
          borderColor: 'var(--border-color)',
          className: 'shadow-lg hc-tooltip-bg',
          animation: false,
          borderRadius: 10,
          formatter: function (tooltip) {
            const html = tooltip.defaultFormatter.call(this, tooltip)
            if (this.point.y !== null && html[1]) {
              html[1] = `${this.point.options.name}: ${NumberFormat(
                this.point.y
              )}`
            }
            return html
          },
          backgroundColor: 'var(--chart-tooltip-background)',
          style: { color: 'var(--page-text-color)' },
          // shared: true,
          hideDelay: 100,
        },
        series: this.data,
        credits: {
          enabled: false,
        },
        exporting: {
          enabled: false,
        },
        noData: {
          style: {
            fontSize: '15px',
            color: 'var(--text-neutral-ligher)',
          },
        },
        drilldown: {
          activeDataLabelStyle: {
            color: 'white',
            textDecoration: 'none',
          },
          breadcrumbs: {
            buttonTheme: {
              fill: 'transparent',
              padding: 0,
              stroke: 'transparent',
              'stroke-width': 1,
              style: {
                color: 'var(--page-text-color)',
                fontWeight: '600',
              },
            },
            floating: true,
            position: {
              align: 'left',
            },
          },
          // drillUpButton: {
          //   relativeTo: 'spacingBox',
          //   position: {
          //     y: 0,
          //     x: 0,
          //   },
          //   theme: {
          //     fill: 'var(--page-background-color)',
          //     color: 'var(--page-text-color)',
          //     'stroke-width': 1,
          //     stroke: 'var(--border-color)',
          //     r: 4,
          //     style: {
          //       color: 'var(--page-text-color)',
          //     },
          //     states: {
          //       active: {
          //         color: 'var(--page-text-color)',
          //       },
          //       hover: {
          //         fill: 'var(--primary)',
          //       },
          //       select: {
          //         stroke: 'var(--page-text-color)',
          //         fill: 'var(--primary)',
          //       },
          //     },
          //   },
          // },
          ...(this.drilldown ? { series: this.drilldown } : {}),
        },
        colors: [
          '#043f57',
          '#374f1a',
          '#624b0a',
          '#62350a',
          '#622424',
          '#38174b',
          '#33355d',
          '#14304c',
          '#3a6033',
          '#1b1b1d',
          '#634125',
          '#602533',
          '#5b5422',
          '#113a39',
          '#601f19',
          '#3a5d5a',
        ],
      }

      return Merge(appliedOptions, chartOptions)
    },
  },
  render() {
    return this.$scopedSlots.default({
      options: this.options,
    })
  },
}
</script>
