<script>
import Merge from 'lodash/merge'
import getChartCommonOptions from './chart-common-options'
import { UserPreferenceComputed } from '@state/modules/user-preference'

function highlightSankeyPath(point, state) {
  if (point.isNode) {
    point.linksFrom.forEach(function (l) {
      l.setState(state)
    })
    point.linksTo.forEach(function (l) {
      l.setState(state)
    })
  } else {
    const rowId = point.options.rowId
    point.series.data.forEach((l) => {
      const r = l.options.rowId
      if (r === rowId) {
        l.setState(state)
        // if (state === 'hover') {
        //   l.originalColor = ['#a5bad0', '#455a63'].includes(l.color)
        //     ? l.color
        //     : l.originalColor
        //   l.update({ color: l.fromNode.color })
        // } else {
        //   console.log(`setting  from edieg ${state}`)
        //   l.update({
        //     color: ['#a5bad0', '#455a63'].includes(l.color)
        //       ? l.color
        //       : l.originalColor,
        //   })
        // }
      }
    })
  }
}

export default {
  name: 'SankeyOptions',
  props: {
    height: { type: Number, default: undefined },
    // eslint-disable-next-line
    useInstanceInTooltip: { type: Boolean, default: true },
    data: {
      type: [Array, Object],
      required: true,
    },
    additionalSeries: {
      type: [Array, Object],
      default() {
        return []
      },
    },
    widget: {
      type: Object,
      default() {
        return {
          widgetProperties: {},
        }
      },
    },
  },
  computed: {
    ...UserPreferenceComputed,
    options() {
      const height = this.height
      const chartOptions = this.widget.widgetProperties.chartOptions || {}
      let data = this.data || []
      const color = this.theme === 'black' ? '#787878' : '#a5bad0'
      const options = getChartCommonOptions(
        [
          ...(Array.isArray(data) ? data : [data]).map((d) => ({
            ...d,
            keys: (d.keys || []).concat('color'),
            data: (d.data || []).map((i) => [...i, color]),
          })),
          ...(Array.isArray(this.additionalSeries)
            ? this.additionalSeries
            : [this.additionalSeries]),
        ],
        this.widget.widgetProperties,
        {
          unit: this.unit,
          useInstanceInTooltip: this.useInstanceInTooltip,
        }
      )

      const appliedOptions = Merge(options, {
        chart: {
          type: 'sankey',
          ...(height ? { height } : {}),
        },
        tooltip: {
          crosshairs: false,
        },
        plotOptions: {
          sankey: {
            point: {
              events: {
                mouseOver: function () {
                  highlightSankeyPath(this, 'hover')
                },
                mouseOut: function () {
                  highlightSankeyPath(this, '')
                },
              },
            },
            dataLabels: {
              style: {
                fontWeight: 'normal',
                color: 'var(--page-text-color)',
                textOutline: 'none',
              },
              align: 'left',
              x: 20,
            },
          },
        },
      })

      return Merge(appliedOptions, chartOptions)
    },
  },
  render() {
    return this.$scopedSlots.default({
      options: this.options,
    })
  },
}
</script>
