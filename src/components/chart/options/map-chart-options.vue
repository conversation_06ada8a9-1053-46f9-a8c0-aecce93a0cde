<script>
import Merge from 'lodash/merge'

import {
  WidgetTypeConstants,
  MAP_SERIES_TYPE,
} from '@components/widgets/constants'

export default {
  name: 'MapChartOptions',
  props: {
    data: {
      type: [Array, Object],
      required: true,
    },
    widgetType: { type: String, default: 'map' },
    widget: {
      type: Object,
      default() {
        return {
          widgetProperties: {},
        }
      },
    },
  },
  data() {
    return {
      worldMap: require('@highcharts/map-collection/custom/world.topo.json'),
    }
  },

  computed: {
    isTopN() {
      return this.widget.category === WidgetTypeConstants.TOPN
    },

    options() {
      const _this = this

      return Merge(
        {
          ...(this.widgetType === WidgetTypeConstants.MAP_VIEW
            ? {
                chart: {
                  backgroundColor: null,
                  type: 'map',
                  map: this.worldMap,
                },
                mapView: {
                  projection: {
                    name: '<PERSON>',
                  },
                },

                series: [
                  {
                    nullColor: `var( --chart-null-color)`,
                    borderColor: `var(--page-background-color)`,
                    showInLegend: false,
                  },

                  ...Object.keys(this.data.data || {}).map((s) => {
                    return {
                      type: s,
                      data: this.data.data[s],
                    }
                  }),
                ],
                plotOptions: {
                  mappoint: {
                    shadow: false,
                    marker: {
                      fillColor: 'var(--primary)',
                      states: {
                        hover: {
                          fillColor: 'var(--primary)', // New color on hover
                        },
                      },
                    },
                    dataLabels: {
                      enabled: true,
                      style: {
                        color: `var(--page-text-color)`,
                        textOutline: 'none', // Removes the stroke-width or text outline
                      },
                      shadow: false,
                      borderWidth: 0,
                    },
                  },
                  mapbubble: {
                    // dataLabels: {
                    //   enabled: true,
                    //   style: {
                    //     color: `var(--page-text-color)`,
                    //     textOutline: 'none', // Removes the stroke-width or text outline
                    //   },
                    //   shadow: false,
                    //   borderWidth: 0,
                    //   formatter: function (p) {
                    //     return this.point.name
                    //   },
                    // },
                    color: 'var(--tag-bg)',
                    joinBy: ['iso-a2', 'code2'],
                    minSize: 4,
                    maxSize: '12%',
                    marker: {
                      symbol: 'circle',
                      states: {
                        hover: {
                          radiusPlus: 1,
                        },
                      },
                      point: {},
                    },

                    // events: {
                    //   click: (e) => {
                    //     console.log('Map point clicked:', e.point.name)
                    //   },
                    // },
                  },
                  mapline: {
                    // dataLabels: {
                    //   enabled: true,
                    //   style: {
                    //     color: `var(--page-text-color)`,
                    //     textOutline: 'none', // Removes the stroke-width or text outline
                    //     fontSize: '10px',
                    //     // fontWeight: 'normal',
                    //   },
                    //   shadow: false,
                    //   borderWidth: 0,
                    //   formatter: function (p) {
                    //     return `${this.point?.options?.source}-${this.point?.options?.destination}`
                    //   },
                    //   align: 'left',
                    //   position: 'left',
                    //   verticalAlign: 'top',
                    // },
                    color: 'var(--map-line-color)',
                    width: 2, // Set the line width of the connections
                    dashStyle: 'ShortDash', // You can also set other properties like dashed lines
                    lineWidth: 2,
                    lineJoin: 'round',
                    className: 'animated-line',
                    states: {
                      hover: {
                        color: 'var(--primary)', // New color on hover
                        dashStyle: 'ShortDash', // New dash style on hover
                        lineWidth: 3, // Optionally change line width on hover
                        // className: 'animated-line',
                      },
                    },
                    clip: true,
                    // color: 'var(--neutral-light)',
                  },
                },
                legend: {
                  enabled: false,
                },

                mapNavigation: {
                  enabled: true,
                  enableButtons: true,
                  buttonOptions: {
                    verticalAlign: 'top',
                    align: 'right',
                    alignTo: 'spacingBox',
                  },
                },
              }
            : {}),
          ...(this.widgetType === WidgetTypeConstants.TREE_VIEW
            ? {
                plotOptions: {
                  treemap: {
                    colorByPoint: true,
                  },
                  series: {
                    point: {
                      events: {
                        click: function (e) {
                          _this.$emit('drilldown', e)
                        },
                      },
                    },
                  },
                },
                series: [
                  {
                    borderWidth: 0,
                    dataLabels: {
                      enabled: true,
                      style: {
                        color: `white`,
                        textOutline: 'none', // Removes the stroke-width or text outline
                      },
                      shadow: false,
                      borderWidth: 0,

                      ...(this.isTopN
                        ? {
                            formatter: function () {
                              // Format value with percentage
                              const value = this.point.formattedValue
                              const valueDisplay = value

                              // Truncate name if too long (over 15 characters)
                              const name = this.point.name || ''
                              const maxLength = 100
                              const truncatedName =
                                name.length > maxLength
                                  ? name.substring(0, maxLength) + '...'
                                  : name

                              // Make value larger for better visibility
                              return `<div style="text-align: center">
                                  <div style="font-size: 24px; font-weight: bold; margin-bottom: 5px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 100%; margin: 0 auto;" title="${valueDisplay}">${valueDisplay}</div>
                                  <div style="font-size: 12px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 90%; margin: 0 auto;" title="${name}">${truncatedName}</div>
                                </div>`
                            },
                            useHTML: true,
                          }
                        : {}),
                    },
                    type: 'treemap',
                    layoutAlgorithm: 'squarified',
                    alternateStartingDirection: true,
                    levels: [
                      {
                        level: 1,
                        layoutAlgorithm: 'squarified',
                        dataLabels: {
                          enabled: true,
                          align: 'left',
                          verticalAlign: 'top',
                          ...(this.isTopN
                            ? { verticalAlign: 'middle', align: 'center' }
                            : {}),
                          style: {
                            fontSize: '14px',
                            fontWeight: 'normal',
                          },
                        },
                      },
                    ],
                    data: (
                      this.data.data[MAP_SERIES_TYPE.MAP_BUBBLE] ||
                      this.data.data[MAP_SERIES_TYPE.MAP_POINT] ||
                      []
                    ).map((d) => ({
                      ...d,
                      color: undefined,
                    })),
                  },
                ],
                colors: [
                  '#043f57',
                  '#374f1a',
                  '#624b0a',
                  '#62350a',
                  '#622424',
                  '#38174b',
                  '#33355d',
                  '#14304c',
                  '#3a6033',
                  // '#1b1b1d',
                  '#634125',
                  '#602533',
                  '#5b5422',
                  '#113a39',
                  '#601f19',
                  '#3a5d5a',
                ],
              }
            : {}),
          ...(this.widgetType === WidgetTypeConstants.ONLINE_MAP
            ? {
                leafletSeries: (
                  this.data.data[MAP_SERIES_TYPE.MAP_BUBBLE] ||
                  this.data.data[MAP_SERIES_TYPE.MAP_POINT] ||
                  []
                ).map((p) => ({
                  ...p,
                  coordinates: p.geometry.coordinates,
                })),
              }
            : {}),
        },
        {
          chart: {
            backgroundColor: null,
            borderWidth: 0,
            margin: [2, 0, 2, 0],

            style: {
              overflow: 'visible',
              fontFamily: 'var(--chart-font-family)',
            },
          },
          title: {
            text: '',
          },
          credits: {
            enabled: false,
          },
          tooltip: {
            hideDelay: 0,
            outside: true,
            shared: true,
            useHTML: true,
            borderColor: 'var(--border-color)',
            className: 'shadow-lg hc-tooltip-bg',
            backgroundColor: 'var(--chart-tooltip-background)',
            style: { color: 'var(--page-text-color)' },
            animation: false,
            borderRadius: 10,
            fontWeight: 'normal',
            formatter: function () {
              let tooltip = `<div style="overflow-wrap: break-word; " class="text-sm"><b> <span style="font-size: 11px">${
                this.point.name || ''
              }</span><br />`
              Object.keys(this.point?.options || {}).map((key, index) => {
                if (
                  ![
                    'name',
                    'value',
                    'z',
                    'geometry',
                    '_i',
                    'color',
                    'className',
                    'code2',
                    'untouchedData',
                    'formattedValue',
                  ].includes(key)
                ) {
                  tooltip += `<div class=""> <span style="font-size: 11px; font-weight:300">${key}</span>  <span class="value font-bold shrink-0 ml-6">${this.point[key]}</span></div>
                  `
                }
              })
              return `${tooltip}</div>`
            },
          },
        }
      )
    },
  },
  render() {
    return this.$scopedSlots.default({
      options: this.options,
    })
  },
}
</script>
