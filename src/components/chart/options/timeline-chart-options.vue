<script>
import Moment from 'moment'
import Uniq from 'lodash/uniq'
import buildConfig from '@components/chart/build-chart-config'
import Duration from '@src/filters/duration'
import Merge from 'lodash/merge'
import { UserPreferenceComputed } from '@state/modules/user-preference'

export default {
  name: 'TimelineChartOptions',
  props: {
    data: {
      type: [Array, Object],
      required: true,
    },
    widget: {
      type: Object,
      default() {
        return {
          widgetProperties: {},
        }
      },
    },
  },
  computed: {
    ...UserPreferenceComputed,
    options() {
      const dateFormat = this.dateFormat
      const timezone = this.timezone
      let gridLineColor

      const status = Uniq(this.data.map((d) => d.status))
      if (status.length === 1) {
        gridLineColor = `var(--severity-${status[0].toLowerCase()})`
      }
      return Merge(
        buildConfig({
          timezone: timezone,
          chart: {
            zoomType: 'x',
            type: 'timeline',
          },
          xAxis: {
            type: 'datetime',
            visible: false,
            gridLineWidth: 3,
            ...(gridLineColor ? { gridLineColor } : {}),
          },
          yAxis: {
            gridLineWidth: 3,
            ...(gridLineColor ? { gridLineColor } : {}),
            title: null,
            labels: {
              enabled: false,
            },
          },
          legend: {
            enabled: false,
          },
          tooltip: {
            crosshairs: false,
            formatter() {
              const momentInstance = Moment.tz(
                Moment.unix(this.x / 1000).format('YYYY-MM-DD HH:mm:ss'),
                timezone
              )
              const x = momentInstance.tz('UTC').format(dateFormat)
              const options = this.point.options
              return `${options.resourceName} entered into status ${options.status} at ${x}`
            },
          },
          plotOptions: {
            timeline: {
              dataLabels: {
                connectorWidth: 2,
                allowOverlap: false,
                backgroundColor: 'var(--page-background-color)',
                borderColor: 'var(--border-color)',
                color: 'var(--page-text-color)',
                formatter() {
                  const point = this.point
                  const momentInstance = Moment.tz(
                    Moment.unix(point.x / 1000).format('YYYY-MM-DD HH:mm:ss'),
                    timezone
                  )
                  const x = momentInstance.tz('UTC').format(dateFormat)
                  const options = this.point.options
                  return `<span style="color:${
                    point.color
                  }">● </span><span style="font-weight: bold;" >
                  ${x}</span><br/>Status: ${point.label} for ${Duration(
                    options.duration
                  )}`
                },
              },
              marker: {
                symbol: 'circle',
                enabled: true,
                radius: 5,
                fillColor: undefined,
              },
            },
          },
          series: [
            {
              data: [
                ...this.data.map((d) => ({
                  ...d,
                  dataLabels: {
                    connectorColor: `var(--severity-${d.status.toLowerCase()})`,
                  },
                })),
              ],
            },
          ],
        })
      )
    },
  },
  render() {
    return this.$scopedSlots.default({
      options: this.options,
    })
  },
}
</script>
