import Uniq from 'lodash/uniq'
import Duration from '@src/filters/duration'
import NumberFormat from '@src/filters/number-format'
import buildConfig from '@components/chart/build-chart-config'
import bytesPerSecToSize, { bytesToSize } from '@src/filters/bytes'
import { getAllowedUnit } from '@utils/unit-checker'
import TooltipBuilder from './tooltip-builder'

function buildYAxis(title, widgetProperty, unit, options = {}) {
  let formatterFn = {
    formatter() {
      return NumberFormat(this.value, 2)
    },

    ...(['percent'].includes(unit)
      ? {
          formatter: function () {
            return `${this.value}%`
          },
        }
      : {}),

    ...(['ms', 's'].includes(unit)
      ? {
          formatter: function () {
            return `${this.value < 0 ? '-' : ''}${Duration(
              this.value,
              unit === 'ms' ? 'milliseconds' : 'seconds',
              false,
              undefined,
              true
            )}`
          },
        }
      : {}),

    ...(['bytes', 'bps'].includes(unit)
      ? {
          formatter: function () {
            if (this.value === 0) {
              return 0
            }
            return unit === 'bps'
              ? bytesPerSecToSize(this.value)
              : bytesToSize(this.value)
          },
        }
      : {}),
  }
  return {
    title: {
      text: title,
      style: { color: 'var(--page-text-color)', fontWeight: '500' },
    },
    labels: {
      ...(options.yAxisLabelWidth
        ? {
            useHTML: true,
            formatter: function () {
              // Wrap the label in a div with a fixed width
              return `<div style="width: ${
                options.yAxisLabelWidth
              }px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; text-align: center;">${formatterFn.formatter.apply(
                this
              )}</div>`
            },
          }
        : formatterFn),
      // rotation: isNaN(labelProperty.rotation)
      //   ? 0
      //   : labelProperty.rotation,
      style: {
        ...(widgetProperty.ellipsisEnabled
          ? {
              whiteSpace: 'nowrap',
              textOverflow: 'ellipsis',
            }
          : {}),
        color: 'var(--page-text-color)',
      },
    },
    // ...(uniqUnits.length === 1 && ['bytes', 'bps'].includes(uniqUnits[0])
    //   ? {
    //       tickInterval: 1024,
    //     }
    //   : {}),
    reversedStacks: false,
    gridLineColor: 'var(--chart-grid-line-color)',
    ...(['percent'].includes(unit) ? { endOnTick: true } : {}),
    ...options,
  }
}

// function buildLegendSymbol(outerHtml, height = 15, width = 15) {
//   return `<svg width='${width}' height='${height}' style="position: relative;top: 2px">${outerHtml}</svg>`
// }

export default function getWidgetChartCommonOptions(
  data,
  widgetProperties,
  options
) {
  const units = data.map((series) => getAllowedUnit(series.counter))
  const hasUnknownUnits = units.filter((u) => !u).length > 0
  const uniqUnits = hasUnknownUnits ? [] : Uniq(units)
  let yAxis
  if (uniqUnits.length > 1 && uniqUnits.length <= 2) {
    const unitYAxisMap = {}
    yAxis = uniqUnits.map((unit, index) => {
      unitYAxisMap[unit] = index
      const prop = index === 0 ? 'yAxisTitle' : 'zAxisTitle'
      return buildYAxis(
        widgetProperties[prop] !== undefined
          ? widgetProperties[prop]
          : undefined,
        widgetProperties,
        unit,
        {
          yAxisLabelWidth: options.yAxisLabelWidth,
          ...(index > 0 ? { opposite: true } : {}),
        }
      )
    })
    data = data.map((series) => {
      const unit = getAllowedUnit(series.counter)
      return {
        ...series,
        yAxis: unitYAxisMap[unit],
      }
    })
  } else {
    yAxis = buildYAxis(
      widgetProperties.yAxisTitle !== undefined
        ? widgetProperties.yAxisTitle || ''
        : undefined,
      widgetProperties,
      uniqUnits.length === 1 ? uniqUnits[0] : undefined,
      {
        yAxisLabelWidth: options.yAxisLabelWidth,
      }
    )
  }

  return buildConfig({
    guid: options.guid,
    disableAutoFetchServerSideZoomData:
      options.disableAutoFetchServerSideZoomData,
    timezone: options.timezone,
    title: {
      text: null, // widgetName || '',
      align: 'center',
    },
    series: data,
    legend: {
      enabled: widgetProperties.legendEnabled,
    },
    xAxis: {
      tickLength: 0,
      title: {
        text: widgetProperties.xAxisTitle || '',
      },
      labels: {
        rotation: isNaN(widgetProperties.rotation)
          ? 0
          : widgetProperties.rotation,
        style: {
          ...(widgetProperties.ellipsisEnabled
            ? {
                whiteSpace: 'nowrap',
                textOverflow: 'ellipsis',
              }
            : {}),
        },
      },
    },
    yAxis,
    tooltip: {
      borderWidth: 1,
      borderColor: 'var(--border-color)',
      className: 'shadow-lg hc-tooltip-bg',
      animation: false,
      borderRadius: 10,
      ...(options.outsideTooltip ? { outside: options.outsideTooltip } : {}),
      valueSuffix:
        options.unit || uniqUnits.length > 0 ? uniqUnits[0] : undefined,
      ...(data.length > 0 && options.sharedTooltip ? { shared: true } : {}),
      ...(options.omitTooltipFormatter
        ? {}
        : {
            formatter: function () {
              var tooltip = new TooltipBuilder(options, this)
              return tooltip.html()
            },
          }),
    },
    plotOptions: {
      ...(options.plotOptions || {}),
    },
    ...(options.serverSideZoom && options.hasDateTime
      ? { serverSideZoom: 10000 }
      : {}),
  })
}
