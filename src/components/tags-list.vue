<template>
  <div v-if="(value || []).length" class="flex-1 min-w-0 flex flex-wrap">
    <template v-if="(value || []).length <= maxLength">
      <MTag
        v-for="tag in value"
        :key="tag"
        variant="primary"
        :closable="false"
        class="mr-2 cursor-auto inline-block text-ellipsis min-w-0 application-item tag-primary my-1"
      >
        {{ tag }}
      </MTag>
    </template>
    <MPopover
      v-else
      trigger="hover"
      placement="bottomRight"
      transition-name="slide-up"
      overlay-class-name="readable-content-overlay"
    >
      <template v-slot:trigger>
        <MTag
          :closable="false"
          variant="primary"
          class="application-item tag-primary"
        >
          {{ value.length }}
        </MTag>
      </template>
      <div class="list">
        <div
          v-for="(item, index) in value"
          :key="`${item}-${index}`"
          :title="item"
          v-text="item"
        />
      </div>
    </MPopover>
  </div>
</template>

<script>
export default {
  name: 'TagsList',
  props: {
    value: { type: [Array, Object], default: undefined },
    maxLength: { type: Number, default: 2 },
  },
}
</script>

<style lang="less" scoped>
.application-item {
  border-radius: 10px;
}

.list {
  div {
    &:not(:last-child) {
      border-bottom: 1px solid var(--border-color);
    }

    @apply py-2;
  }
}
</style>
