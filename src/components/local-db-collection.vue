<script>
import Debounce from 'lodash/debounce'
import { metricDBWorker, objectDBWorker } from '@/src/workers'
import { ConfigMethods } from '@state/modules/config'
import Bus from '@utils/emitter'
import { authComputed, authMethods } from '@state/modules/auth'

const dbStores = {
  objects: objectDBWorker,
  metric: metricDBWorker,
}

const responseProperties = {
  objects: 'objects',
  metric: 'metric.database',
}

export default {
  name: 'LocalDbCollection',
  props: {
    store: {
      type: String,
      default: undefined,
    },
    getEvent: {
      type: String,
      default: undefined,
    },
    publishEvent: {
      type: String,
      default: undefined,
    },
    changeEvent: {
      type: String,
      default: undefined,
    },
    heartbeatEvent: {
      type: String,
      default: undefined,
    },
    entity: {
      type: String,
      required: true,
    },
    handleMetricChangeEvents: {
      type: Boolean,
      default: false,
    },
    disableDataAsking: {
      type: Boolean,
      default: false,
    },
    initEventName: {
      type: String,
      default: undefined,
    },
  },
  computed: {
    ...authComputed,
  },
  mounted() {
    this.triggerPublishGenericEvent = Debounce(
      this.triggerPublishGenericEventRaw,
      1000
    )
    if (this.getEvent) {
      Bus.$on(this.getEvent, this.receivedAllRecords)
    }
    // if (this.changeEvent) {
    //   Bus.$on(this.changeEvent, this.handleChange)
    // }
    let interval

    if (this.$route.name !== 'report.render') {
      interval = setInterval(() => {
        this.askForData()
      }, 30000)
    }

    this.$once('hook:beforeDestroy', () => {
      if (this.getEvent) {
        Bus.$off(this.getEvent, this.receivedAllRecords)
      }
      // if (this.changeEvent) {
      //   Bus.$off(this.changeEvent, this.handleChange)
      // }

      if (interval) {
        clearInterval(interval)
      }
      // Bus.$off('socket:connected', this.initialDataAsking)
    })
    // Bus.$on('socket:connected', this.initialDataAsking)
    this.initialDataAsking()
  },
  methods: {
    ...ConfigMethods,
    ...authMethods,
    triggerMetricTypeSet(event) {
      this.setMetricTypes(event)
    },
    triggerLicenseTypeSet(event) {
      this.setLicenseType(event)
    },
    initialDataAsking() {
      if (this.disableDataAsking) {
        return
      }
      Bus.$emit('server:event', {
        'event.type': this.initEventName,
        'event.context': {
          'session-id': this.sessionId,
        },
      })
    },
    askForData() {
      if (this.disableDataAsking) {
        return
      }
      Bus.$emit('server:event', {
        'event.type': this.heartbeatEvent,
        'event.context': {
          'session-id': this.sessionId,
        },
      })
    },
    handleChange(event) {
      this.askForData()
    },
    triggerPublishGenericEventRaw() {
      if (this.publishEvent) {
        Bus.$emit(this.publishEvent)
      }
    },
    async receivedAllRecords(records) {
      const responseKey = responseProperties[this.store]
      if (!responseKey) {
        throw new Error(`Response key for store ${this.store} is not defined`)
      }
      const store = dbStores[this.store]
      if (!store) {
        throw new Error(`Store with name ${this.store} is not defined`)
      }
      const allRecords = records[responseKey] || []
      if (this.store === 'objects') {
        await store.clearHierarchyCache()
      }
      await store.addBulk(allRecords, true, true)
      this.triggerPublishGenericEvent()
      if (this.handleMetricChangeEvents) {
        this.triggerMetricTypeSet(records)
      }
      this.triggerLicenseTypeSet(records)
      setTimeout(() => {
        this.$emit('db-filled')
      }, 1000)
    },
    async addItem(item) {
      const store = dbStores[this.store]
      if (!store) {
        throw new Error(`Store with name ${this.store} is not defined`)
      }
      await store.add(item, true)
      this.triggerPublishGenericEvent()
    },
    async deleteItem(item) {
      const store = dbStores[this.store]
      if (!store) {
        throw new Error(`Store with name ${this.store} is not defined`)
      }
      await store.remove(item.id)
      this.triggerPublishGenericEvent()
      Bus.$emit('monitor.deleted', item)
    },
    async updateItem(object) {
      const store = dbStores[this.store]
      if (!store) {
        throw new Error(`Store with name ${this.store} is not defined`)
      }
      await store.update(object, true)
      this.triggerPublishGenericEvent()
    },
  },
  render() {
    return null
  },
}
</script>
