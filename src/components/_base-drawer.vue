<template>
  <MDrawer
    ref="drawer"
    :width="width"
    :mask-closable="false"
    v-bind="$attrs"
    v-on="listeners"
    @hide="handlevisibleChange(false)"
    @show="handlevisibleChange(true)"
  >
    <template v-slot:trigger="slotData">
      <slot name="trigger" v-bind="slotData"><span /></slot>
    </template>
    <template v-slot:title>
      <slot name="title-row">
        <h5 class="mb-0 text-ellipsis pr-6 text-primary">
          <slot name="title" />
        </h5>
      </slot>
    </template>
    <div class="flex h-100 flex-col">
      <div
        class="h-100 -mx-4 flex flex-col spacer-mt-popup"
        style="padding: 5px 15px 0"
        :style="{ paddingBottom: $scopedSlots.actions ? '65px' : '0' }"
      >
        <FlotoScrollView v-if="scrolledContent">
          <div class="w-full flex-1 flex flex-col px-4">
            <slot />
          </div>
        </FlotoScrollView>
        <slot v-else />
      </div>
    </div>
    <div v-if="$scopedSlots.actions" class="actions">
      <slot name="actions" :hide="hide" />
    </div>
  </MDrawer>
</template>

<script>
export default {
  name: 'FlotoDrawer',
  props: {
    open: { type: Boolean, default: false },
    width: { type: [String, Number], default: '40%' },
    usePadding: { type: Boolean, default: false },
    // eslint-disable-next-line
    scrolledContent: { type: Boolean, default: true },
  },
  computed: {
    listeners() {
      const { hide, show, ...listeners } = this.$listeners
      return listeners
    },
  },
  watch: {
    open: {
      immediate: true,
      handler(newValue) {
        if (this.$refs.drawer) {
          if (newValue) {
            this.show()
          } else {
            this.hide()
          }
        }
      },
    },
  },
  mounted() {
    if (this.open) {
      this.show()
    }
  },
  beforeDestroy() {
    this.hide()
  },
  methods: {
    hide() {
      if (this.$refs.drawer) {
        this.$refs.drawer.hide()
      }
    },
    show() {
      if (this.$refs.drawer) {
        this.$refs.drawer.show()
      }
    },
    handlevisibleChange(isVisible) {
      if (!isVisible) {
        setTimeout(() => this.$emit('hide'), 500)
      } else {
        this.$emit('show')
      }
    },
  },
}
</script>
