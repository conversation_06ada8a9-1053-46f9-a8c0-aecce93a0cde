<script>
import Omit from 'lodash/omit'
import Clone<PERSON>eep from 'lodash/cloneDeep'
import {
  UserPreferenceComputed,
  UserPreferenceMethods,
} from '@state/modules/user-preference'

export default {
  name: 'MPersistedColumns',
  model: { event: 'change' },
  props: {
    moduleKey: {
      type: String,
      required: true,
    },
    availableColumns: {
      type: Array,
      default() {
        return []
      },
    },
    defaultValue: {
      type: Array,
      default: undefined,
    },
    value: {
      type: Array,
      default: undefined,
    },
    disabled: {
      type: Boolean,
      default: false,
    },

    notifyInitialLoad: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    this.columnMap = {}
    this.defaultColumns = CloneDeep(this.defaultValue)
    return {
      columns: this.value || this.defaultValue,
    }
  },
  computed: {
    ...UserPreferenceComputed,
  },
  watch: {
    moduleKey: 'fixColumnsFromVuex',
    availableColumns: 'fixColumnsFromVuex',
  },
  created() {
    this.fixColumnsFromVuex(this.notifyInitialLoad)
  },
  methods: {
    ...UserPreferenceMethods,
    fixColumnsFromVuex(notifyInitialLoad) {
      const persistedColumns = this.persistedColumnsForModule(this.moduleKey)
      if (persistedColumns && persistedColumns.length) {
        this.columnMap = {}
        this.availableColumns.forEach((c) => {
          this.columnMap[c.key] = c
        })
        const persistedColumnsMap = {}

        // persistedColumns.reduce(
        //   (prev, c) => ({ ...prev, [c.key]: c }),
        //   {}
        // )

        persistedColumns.forEach((c) => {
          persistedColumnsMap[c.key] = c
        })

        this.columns = this.availableColumns
          .map((c) => ({
            ...c,
            ...(persistedColumnsMap[c.key]
              ? Omit(persistedColumnsMap[c.key], ['sortable', 'searchable'])
              : {}),
          }))
          .map((c) => ({ ...c, hidden: c.hidden || false }))

        if (notifyInitialLoad === true) {
          this.$emit('loaded', persistedColumns)
        }
        this.$emit('change', this.columns)
      } else {
        this.columns = Object.freeze(this.defaultColumns)
        this.$emit('change', this.columns)
      }
    },
    resetColumns() {
      return this.updateColumnsForModule({
        moduleKey: this.moduleKey,
        columns: undefined,
      }).then(() => this.fixColumnsFromVuex(this.moduleKey))
    },
    setColumns(columns) {
      const columnMap = {}
      let updatedColumns
      this.columns.forEach((c) => {
        columnMap[c.key] = c
      })
      if ((!Array.isArray(columns) || columns.length === 0) && !this.disabled) {
        return this.resetColumns()
      } else if (columns.length < this.columns.length) {
        updatedColumns = this.columns.map((column) => {
          const updatedContext = (columns || []).find(
            (c) => c.key === column.key
          )
          if (updatedContext) {
            return {
              key: updatedContext.key || updatedContext.field,
              name: updatedContext.title || updatedContext.name,
              ...updatedContext,
              ...Omit(updatedContext, ['context']),
            }
          }
          return {
            key: column.key || column.field,
            name: column.title || column.name,
            ...columnMap[column.key],
            ...Omit(column, ['context']),
          }
        })
      } else {
        updatedColumns = columns.map((column) => ({
          key: column.key || column.field,
          name: column.title || column.name,
          ...columnMap[column.key],
          ...Omit(column, ['context']),
        }))
      }

      this.columns = Object.freeze(CloneDeep(updatedColumns))
      this.$emit('change', this.columns)

      if (this.disabled) {
        return
      }
      // fire vuex action to persist in user preference
      this.updateColumnsForModule({
        moduleKey: this.moduleKey,
        columns: updatedColumns,
      })
    },
  },
  render() {
    return this.$scopedSlots.default({
      columns: this.columns,
      setColumns: this.setColumns,
    })
  },
}
</script>
