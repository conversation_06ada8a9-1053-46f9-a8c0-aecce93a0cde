<template>
  <Transition name="slideTop">
    <div
      v-if="visible && notification"
      :key="`${notification.message}`"
      class="network-state-checker text-center"
    >
      <div class="inline-flex vue-grid-item pl-2 items-center">
        {{ notification.message }}
        <MButton class="mx-2" size="small" @click="redirect">
          Update Now
        </MButton>

        <MButton
          size="small"
          class="flex items-center justify-center"
          variant="transparent"
          :shadow="false"
          shape="circle"
          @click.stop.prevent="closeNotification"
        >
          <MIcon name="times" />
        </MButton>
      </div>
    </div>
  </Transition>
</template>

<script>
import Bus from '@utils/emitter'

export default {
  name: 'LicenseExpireNotification',
  data() {
    return {
      visible: false,
      notification: undefined,
      width: 0,
    }
  },

  created() {
    Bus.$on(
      this.$constants.UI_NOTIFICATION_LICENSE_EXPIRE,
      this.showNotification
    )
  },

  beforeDestroy() {
    Bus.$off(
      this.$constants.UI_NOTIFICATION_LICENSE_EXPIRE,
      this.showNotification
    )
  },

  methods: {
    closeNotification() {
      this.visible = false
      this.notification = undefined
    },

    showNotification(event) {
      this.visible = true

      this.buildNotification(event)
    },

    buildNotification(event) {
      if (event['remaining.days'] < 0) {
        this.notification = {
          message: `Your license has expired!`,
        }
      } else if (event['remaining.days'] === 0) {
        this.notification = {
          message: `Your license is expiring today!`,
        }
      } else {
        this.notification = {
          message: `You have ${event['remaining.days']} days left in your evaluation!`,
        }
      }
    },
    redirect() {
      this.$router.push(this.$modules.getModuleRoute('my-account', 'license'))
      this.closeNotification()
    },
  },
}
</script>

<style lang="less" scoped>
.network-state-checker {
  position: fixed;
  top: 6px;
  z-index: ***********;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  pointer-events: none;

  > div {
    padding: 0.5rem;
    pointer-events: all;
    background-color: var(--dashboard-background);
    border-radius: 4px;

    @apply rounded shadow-lg;
  }
}
</style>
