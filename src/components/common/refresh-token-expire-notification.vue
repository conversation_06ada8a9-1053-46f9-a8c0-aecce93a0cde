<template>
  <Transition name="slideTop">
    <div
      v-if="visible && notification"
      ref="notification"
      :key="`${notification.message}`"
      class="network-state-checker text-center"
    >
      <div class="flex vue-grid-item pl-2 items-center">
        <div class="flex" :title="tooltip" v-html="notification.message"></div>
        <MButton class="mx-2" size="small" @click="redirect">
          Update Now
        </MButton>

        <MButton
          size="small"
          class="flex items-center justify-center"
          variant="transparent"
          :shadow="false"
          shape="circle"
          @click.stop.prevent="closeNotification"
        >
          <MIcon name="times" />
        </MButton>
      </div>
    </div>
  </Transition>
</template>

<script>
import Bus from '@utils/emitter'

export default {
  name: 'LicenseExpireNotification',
  data() {
    return {
      visible: false,
      notification: undefined,
      width: 0,
    }
  },
  computed: {
    tooltip() {
      return `${this.notification?.message || ''}`.replace(/<[^>]+>/g, '')
    },
  },

  created() {
    Bus.$on(
      this.$constants.UI_NOTIFICATION_REFRESH_TOKEN_EXPIRE,
      this.showNotification
    )
  },

  beforeDestroy() {
    Bus.$off(
      this.$constants.UI_NOTIFICATION_REFRESH_TOKEN_EXPIRE,
      this.showNotification
    )
  },

  methods: {
    closeNotification() {
      this.visible = false
      this.notification = undefined
    },

    showNotification(event) {
      this.visible = true

      this.buildNotification(event)
    },

    buildNotification(event) {
      this.notification = {
        message: `The refresh token associated with your <span class="text-ellipsis mx-1" style="max-width:200px">${event['credential.profile.name']}</span> credential profile has expired.`,
        id: event.id,
      }
    },
    redirect() {
      this.$router.push(
        this.$modules.getModuleRoute(
          'network-discovery',
          'credential-profiles',
          {
            params: { id: this.notification.id },
          }
        )
      )
      this.closeNotification()
    },
  },
}
</script>

<style lang="less" scoped>
.network-state-checker {
  position: fixed;
  top: 6px;
  z-index: 99999999999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  pointer-events: none;

  > div {
    padding: 0.5rem;
    pointer-events: all;
    background-color: var(--dashboard-background);
    border-radius: 4px;

    @apply rounded shadow-lg;
  }
}
</style>
