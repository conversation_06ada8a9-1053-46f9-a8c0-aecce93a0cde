<template>
  <FlotoFormItem>
    <div class="flex flex-col">
      <div class="flex justify-between">
        <input
          v-for="(digit, index) in otp"
          :key="index"
          ref="otpInputs"
          v-model="otp[index]"
          type="text"
          class="otp-input"
          :class="{ 'error-border': showError && !digit }"
          :placeholder="isFocused[index] ? '' : '0'"
          maxlength="1"
          @focus="setFocus(index, true)"
          @blur="setFocus(index, false)"
          @input="handleInput(index, $event)"
          @keydown.backspace="handleBackspace(index, $event)"
          @paste="handlePaste"
        />
      </div>

      <span v-if="showError" class="text-secondary-red text-xs mt-1"
        >Invalid TOTP</span
      >
    </div>
  </FlotoFormItem>
</template>

<script>
export default {
  name: 'OTPInput',
  props: {
    length: {
      type: Number,
      default: 6, // Default OTP length
    },
  },
  data() {
    return {
      otp: new Array(this.length).fill(''),
      isFocused: new Array(this.length).fill(false),
      showError: false, // Flag for showing error
    }
  },
  mounted() {
    this.$nextTick(() => {
      if (this.$refs.otpInputs[0]) {
        this.$refs.otpInputs[0].focus()
      }
    })
  },

  methods: {
    handleInput(index, event) {
      const value = event.target.value.replace(/\D/g, '') // Only allow numbers
      this.$set(this.otp, index, value)

      if (value && index < this.length - 1) {
        this.$refs.otpInputs[index + 1].focus() // Move to next input
      }

      this.emitOtp()
    },
    handleBackspace(index, event) {
      if (!this.otp[index] && index > 0) {
        this.$refs.otpInputs[index - 1].focus() // Move to previous input
      }
    },
    handlePaste(event) {
      const pasteData = event.clipboardData
        .getData('text')
        .replace(/\D/g, '')
        .slice(0, this.length)
      if (pasteData.length > 0) {
        this.showError = false // Reset error on paste

        this.otp = pasteData.split('')
        this.$nextTick(() => {
          const lastIndex = pasteData.length - 1
          this.$refs.otpInputs[lastIndex]?.focus()
        })
      }
      this.emitOtp()
      event.preventDefault()
    },
    emitOtp() {
      this.$emit('input', this.otp.join(''))
    },
    onfocusInput(index) {
      this.isFocused[index] = true
    },

    validateOtp() {
      return new Promise((resolve, reject) => {
        if (this.otp.includes('')) {
          this.showError = true // Show red border if any field is empty
          resolve(false) // Reject the promise
        } else {
          this.showError = false
          const otpCode = this.otp.join('')
          resolve(otpCode) // Resolve with the OTP
        }
      })
    },
    setFocus(index, status) {
      this.$set(this.isFocused, index, status) // Ensures reactivity
    },
  },
}
</script>

<style scoped>
.otp-container {
  display: flex;
  gap: 8px;
}

.otp-input {
  width: 50px;
  height: 60px;
  font-size: 26px;
  font-weight: 600;
  text-align: center;
  border: 2px solid #d1d5db; /* Gray border */
  border-radius: 8px;
  outline: none;
  transition: border-color 0.2s;
}

.otp-input:focus {
  border-color: var(--primary); /* Blue border on focus */
}

.error-border {
  border: 1px solid var(--secondary-red); /* Gray border */
}
</style>
