<template>
  <Transition name="slideTop">
    <div
      v-if="visible && notification"
      :key="`${notification.message}`"
      class="network-state-checker text-center"
      :class="{
        'login-notification': forLoginPage,
      }"
    >
      <div class="inline-flex vue-grid-item pl-2 items-center">
        <template v-if="!forLoginPage">
          {{ notification.message }}
          <MButton class="mx-2" size="small" @click="redirect">
            Update Now
          </MButton>
        </template>
        <div v-else class="inline-flex items-center text-sm">
          <MIcon name="exclamation-circle" size="lg" class="mx-2 font-medium" />

          Your Password has expired!
          <a class="text-white mx-1" @click.prevent="$emit('reset-password')">
            <b>Click Here</b>
          </a>
          to reset.
        </div>

        <MButton
          size="small"
          class="flex items-center justify-center"
          :class="{
            'mx-2 ': forLoginPage,
          }"
          variant="transparent"
          :shadow="false"
          shape="circle"
          :style="{
            color: forLoginPage ? 'var(--white-regular) !important' : 'unset',
          }"
          @click.stop.prevent="closeNotification"
        >
          <MIcon name="times" :size="forLoginPage ? 'lg' : undefined" />
        </MButton>
      </div>
    </div>
  </Transition>
</template>

<script>
import Bus from '@utils/emitter'

export default {
  name: 'PasswordExpireNotification',

  props: {
    forLoginPage: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      visible: false,
      notification: undefined,
      width: 0,
    }
  },

  created() {
    Bus.$on(
      this.$constants.UI_NOTIFICATION_PASSWORD_EXPIRY,
      this.showNotification
    )
  },

  beforeDestroy() {
    Bus.$off(
      this.$constants.UI_NOTIFICATION_PASSWORD_EXPIRY,
      this.showNotification
    )
  },

  methods: {
    closeNotification() {
      this.visible = false
      this.notification = undefined
    },

    showNotification(event) {
      this.visible = true

      this.buildNotification(event)
    },

    buildNotification(event) {
      if (event['remaining.days'] < 0) {
        this.notification = {
          message: `Your password has expired!`,
        }
      } else if (event['remaining.days'] === 0) {
        this.notification = {
          message: `Your password is expiring today!`,
        }
      } else {
        this.notification = {
          message: `Your Password will expire in ${event['remaining.days']} days`,
        }
      }
    },
    redirect() {
      this.$router.push('/settings')
      this.closeNotification()
    },
  },
}
</script>

<style lang="less" scoped>
.network-state-checker {
  position: fixed;
  top: 6px;
  z-index: 99999999999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  pointer-events: none;

  > div {
    padding: 0.5rem;
    pointer-events: all;
    background-color: var(--dashboard-background);
    border-radius: 4px;

    @apply rounded shadow-lg;
  }
}

.login-notification {
  border: none !important;

  > div {
    color: var(--white-regular);
    background: var(--severity-critical) !important;
    border: none !important;
    border-radius: 4px;
  }
}
</style>
