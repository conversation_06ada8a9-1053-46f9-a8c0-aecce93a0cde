<template>
  <div
    v-if="notification && notification.length && visible"
    class="notification-container"
  >
    <div
      v-for="(notificationMessage, index) in notification"
      :key="`${notificationMessage['policy.name']}-${
        notificationMessage['entity.id']
      }-${notificationMessage['policy.id']}-${
        notificationMessage['instance'] ? notificationMessage['instance'] : ''
      }-${notificationMessage['severity']}
      }`"
      ref="notification"
      class="custom-notification vue-grid-item shadow-lg text-ellipsis"
      :style="{
        position: 'absolute',
        top: `${(notification.length - index) * 3 + 2}px`,
        padding: '0px 8px',
        width: `${index * 14 + 350}px`,
      }"
    >
      <div class="custom-notification-content">
        <div class="notification-body w-full">
          <!-- Notification Header -->
          <div
            v-if="notificationMessage"
            class="notification-header bold severity-lightest flex w-full"
          >
            <div class="mt-1">
              <Severity :severity="notificationMessage.severity" class="mr-2" />
            </div>
            <div class="w-full">
              <div class="flex-col">
                <div
                  class="text-ellipsis overflow-hidden whitespace-no-wrap"
                  style="max-width: 210px; font-size: 12px"
                >
                  <a>
                    {{ notificationMessage['policy.name'] }}
                  </a>
                </div>

                <!-- Notification Message -->
                <div class="notification-details-container w-full">
                  <div
                    v-if="isTrapPolicy && lastnotificationContext"
                    class="notification-details w-full text-ellipsis"
                    style="max-width: 240px; font-size: 11px"
                    :title="lastnotificationContext['event.source']"
                  >
                    {{ lastnotificationContext['event.source'] }}
                  </div>
                  <div
                    v-if="lastnotification && lastnotificationContext"
                    class="notification-details w-full text-ellipsis"
                    style="max-width: 240px; font-size: 11px"
                    :title="
                      lastnotificationContext['instance']
                        ? lastnotificationContext['instance']
                        : ''
                    "
                  >
                    {{ entityDetails }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Action Button -->
          <div class="notification-action items-center">
            <MTag
              class="used-count-pill ant-tag cursor-auto items-center rounded my-2 text-ellipsis"
              :closable="false"
              style="max-width: 60px"
            >
              {{ notificationMessage['policy.type'] }}
            </MTag>
            <MButton
              size="small"
              class="flex items-center justify-center mt-1"
              variant="transparent"
              :shadow="false"
              shape="circle"
              @click.stop.prevent="closeNotification(notificationMessage)"
            >
              <MIcon name="times" />
            </MButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Bus from '@utils/emitter'
import Severity from '@components/severity'

import { objectDBWorker } from '@/src/workers'
export default {
  name: 'AlertNotification',
  components: { Severity },
  data() {
    return {
      visible: false,
      notification: [],
      width: 0,
      entityDetails: '',
      lastnotification: undefined,
      lastnotificationContext: undefined,
    }
  },

  computed: {
    isTrapPolicy() {
      return (
        this.lastnotificationContext?.['policy.type']?.toLowerCase() === 'trap'
      )
    },
  },

  watch: {
    notification(newValue, oldValue) {
      if (newValue !== oldValue && newValue) {
        this.updateNotificationWidth()
      }
    },
    visible(newValue, oldValue) {
      if (newValue !== oldValue && newValue) {
        this.updateNotificationWidth()
        // setInterval(() => {
        //   this.visible = false
        // }, 10000)
      }
    },
  },
  created() {
    Bus.$on(this.$constants.UI_ALERT_NOTIFICATIONS, this.showNotification)
  },

  mounted() {
    this.updateNotificationWidth()
    window.addEventListener('resize', this.updateNotificationWidth)
  },

  beforeDestroy() {
    window.removeEventListener('resize', this.updateNotificationWidth)
    Bus.$off(this.$constants.UI_ALERT_NOTIFICATIONS, this.showNotification)
  },

  methods: {
    async fetchEntityDetails() {
      this.entityDetails = await this.getEntityDetailsById()
    },
    async getEntityDetailsById() {
      let message = this.notification[this.notification?.length - 1]

      const resolveMonitor = await objectDBWorker.getObjectById(
        message['entity.id']
      )

      this.lastnotification = resolveMonitor

      this.lastnotificationContext =
        this.notification[this.notification?.length - 1]

      if (this.lastnotification?.['category'] === this.$constants.CLOUD) {
        return `${this.lastnotification['type']} - ${this.lastnotification['target']}`
      } else {
        return `${this.lastnotification?.['host']}(${
          this.lastnotification?.['ip']
        })${
          this.lastnotificationContext['instance']
            ? ' - ' + this.lastnotificationContext['instance']
            : ''
        }`
      }
    },
    headerClass(notificationMessage) {
      if (notificationMessage) {
        return {
          [notificationMessage['policy.name']?.toLowerCase()]: true,
          [`${notificationMessage['policy.name']?.toLowerCase()}-text`]: true,
        }
      }
      return {}
    },
    closeNotification() {
      this.visible = false
      this.notification = undefined
    },
    updateNotificationWidth() {
      const notification = this.$refs.notification
      if (notification) {
        const halfScreenWidth = window.innerWidth / 2
        notification.style.width = `${Math.min(halfScreenWidth, 800)}px` // Maximum width of 400px
        this.width = halfScreenWidth
      }
    },
    showNotification(event) {
      this.visible = true
      this.buildNotification(event)

      const lastNotificationItem = event.result.at(-1) || {}

      this.lastNotificationUniqKey = `${lastNotificationItem['policy.name']}-${
        lastNotificationItem['entity.id']
      }-${lastNotificationItem['policy.id']}-${
        lastNotificationItem['instance'] ? lastNotificationItem['instance'] : ''
      }-${lastNotificationItem['severity']}
      }`

      this.autoCloseNotification(event)
    },

    buildNotification(event) {
      this.notification = event.result.slice(-3)
      this.fetchEntityDetails()
    },
    autoCloseNotification(event) {
      const lastNotification = event.result.at(-1) || {}
      const lastNotificationUniqKey = `${lastNotification['policy.name']}-${
        lastNotification['entity.id']
      }-${lastNotification['policy.id']}-${
        lastNotification['instance'] ? lastNotification['instance'] : ''
      }-${lastNotification['severity']}
      }`
      setTimeout(() => {
        if (lastNotificationUniqKey === this.lastNotificationUniqKey) {
          this.visible = false
        }
      }, 10000)
    },
  },
}
</script>

<style lang="less" scoped>
.custom-notification {
  position: fixed;
  top: 4px;
  right: 50%;
  left: 50%;
  z-index: 1000;
  display: flex;
  // align-items: center;
  justify-content: space-between;
  width: 800px;

  /* width: calc(100% - 32px); 100% of the viewport width minus padding */

  min-width: 350px;
  background: var(--alert-notification-popup-background-color);
  border: 1px solid var(--alert-notification-popup-border-color);
  // background: var(--notification-dropdown-background);
  // white-space: nowrap;

  /* padding: 8px; */
  // background-color: var(--timerange-background-color);

  /* color: #ffffff; */
  border-radius: 4px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.1);
  transform: translateX(-50%);
  // @apply shadow-lg;
}

.custom-notification-content {
  display: flex;
  // align-self: center;
  // justify-content: center;
  width: 100%;

  .notification-body {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    /* font-size: 14px; */
    overflow: hidden;

    .notification-header {
      display: flex;
      // align-items: center;
      // justify-content: center;
      padding: 4px;
      font-size: 14px;
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;

      &.down {
        background: var(--severity-down-lightest) !important;
      }

      &.down-text {
        color: var(--severity-down) !important;
      }

      &.clear,
      &.up {
        background: var(--severity-clear-lightest) !important;
      }

      &.clear-text,
      &.up-text {
        color: var(--severity-clear) !important;
      }

      &.critical {
        background: var(--severity-critical-lightest) !important;
      }

      &.critical-text {
        color: var(--severity-critical) !important;
      }

      &.major {
        background: var(--severity-major-lightest) !important;
      }

      &.major-text {
        color: var(--severity-major) !important;
      }

      &.warning {
        background: var(--severity-warning-lightest) !important;
      }

      &.warning-text {
        color: var(--severity-warning) !important;
      }

      &.bold {
        font-weight: 600;
      }

      &.success {
        background: var(--secondary-green);
      }

      &.error {
        background: var(--secondary-red);
      }

      &.neutral-lighter {
        background: var(--neutral-lighter);
      }

      &.info {
        background: var(--severity-maintenance-lightest);
      }

      &.info-text {
        color: var(--primary-alt);
      }

      &.attention {
        background: var(--primary-alt);
      }
    }

    .notification-message {
      display: flex;
      font-size: 11px;

      // align-items: center;
      // padding: 8px;
    }
  }

  .notification-action {
    display: flex;
    // align-items: center;
    // padding: 8px;
  }

  .notification-container {
    position: fixed;
    right: 10px;
    bottom: 10px;
    z-index: 9999;
    display: flex;
    flex-direction: column-reverse;
    align-items: flex-start;
    max-height: 5vh;
    overflow: hidden;
  }

  .notification-details {
    font-size: 11px;
  }
}
</style>
