<template>
  <div
    v-if="visible && notification"
    :key="`${notification.header}-${notification.message}-${width}`"
    ref="notification"
    class="custom-notification vue-grid-item"
  >
    <div class="custom-notification-content">
      <div class="notification-body">
        <div
          v-if="notification && notification.header"
          :key="notification.header"
          class="notification-header bold severity-lightest"
          :class="headerClass"
        >
          {{ notification.header }}
        </div>
        <div
          v-if="notification && notification.message"
          :key="notification.message"
          class="notification-message"
        >
          {{ notification.message }}
        </div>
      </div>

      <div class="notification-action">
        <MButton
          size="small"
          class="flex items-center justify-center"
          variant="transparent"
          :shadow="false"
          shape="circle"
          @click.stop.prevent="closeNotification"
        >
          <MIcon name="times" />
        </MButton>
      </div>
    </div>
  </div>
</template>

<script>
import Bus from '@utils/emitter'
import Capitalize from 'lodash/capitalize'

export default {
  name: 'CustomNotification',
  data() {
    return {
      visible: false,
      notification: undefined,
      width: 0,
    }
  },
  computed: {
    headerClass() {
      if (this.notification) {
        return {
          [(this.notification?.header).toLowerCase()]: true,
          [`${(this.notification?.header).toLowerCase()}-text`]: true,
        }
      }
      return {}
    },
  },
  watch: {
    notification(newValue, oldValue) {
      if (newValue !== oldValue && newValue) {
        this.updateNotificationWidth()
      }
    },
    visible(newValue, oldValue) {
      if (newValue !== oldValue && newValue) {
        this.updateNotificationWidth()
      }
    },
  },
  created() {
    Bus.$on(
      this.$constants.UI_NOTIFICATION_DISK_UTILIZATION_EXCEED,
      this.showNotification
    )
  },

  mounted() {
    this.updateNotificationWidth()
    window.addEventListener('resize', this.updateNotificationWidth)
  },

  beforeDestroy() {
    window.removeEventListener('resize', this.updateNotificationWidth)
    Bus.$off(
      this.$constants.UI_NOTIFICATION_DISK_UTILIZATION_EXCEED,
      this.showNotification
    )
  },

  methods: {
    closeNotification() {
      this.visible = false
      this.notification = undefined
    },
    updateNotificationWidth() {
      const notification = this.$refs.notification
      if (notification) {
        const halfScreenWidth = window.innerWidth / 2
        notification.style.width = `${Math.min(halfScreenWidth, 800)}px` // Maximum width of 400px
        this.width = halfScreenWidth
      }
    },
    showNotification(event) {
      this.visible = true

      this.buildNotification(event)
    },

    buildNotification(event) {
      this.notification = {
        header: Capitalize((event?.severity || '').toLowerCase()),
        message: event.message,
      }
    },
  },
}
</script>

<style lang="less" scoped>
.custom-notification {
  position: fixed;
  top: 8px;
  right: 50%;
  left: 50%;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 800px;

  /* width: calc(100% - 32px); 100% of the viewport width minus padding */

  min-width: 500px;
  // white-space: nowrap;

  /* padding: 8px; */
  background-color: var(--dashboard-background);

  /* color: #ffffff; */
  border-radius: 4px;
  transform: translateX(-50%);
}

.custom-notification-content {
  display: flex;
  align-self: center;
  justify-content: center;
  width: 100%;

  .notification-body {
    display: flex;
    width: 95%;

    /* font-size: 14px; */
    overflow: hidden;

    .notification-header {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 8px;
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;

      &.down {
        background: var(--severity-down-lightest) !important;
      }

      &.down-text {
        color: var(--severity-down) !important;
      }

      &.clear,
      &.up {
        background: var(--severity-clear-lightest) !important;
      }

      &.clear-text,
      &.up-text {
        color: var(--severity-clear) !important;
      }

      &.critical {
        background: var(--severity-critical-lightest) !important;
      }

      &.critical-text {
        color: var(--severity-critical) !important;
      }

      &.major {
        background: var(--severity-major-lightest) !important;
      }

      &.major-text {
        color: var(--severity-major) !important;
      }

      &.warning {
        background: var(--severity-warning-lightest) !important;
      }

      &.warning-text {
        color: var(--severity-warning) !important;
      }

      &.bold {
        font-weight: 600;
      }

      &.success {
        background: var(--secondary-green);
      }

      &.error {
        background: var(--secondary-red);
      }

      &.neutral-lighter {
        background: var(--neutral-lighter);
      }

      &.info {
        background: var(--severity-maintenance-lightest);
      }

      &.info-text {
        color: var(--primary-alt);
      }

      &.attention {
        background: var(--primary-alt);
      }
    }

    .notification-message {
      display: flex;
      align-items: center;
      padding: 8px;
    }
  }

  .notification-action {
    display: flex;
    align-items: center;
    padding: 8px;
  }
}
</style>
