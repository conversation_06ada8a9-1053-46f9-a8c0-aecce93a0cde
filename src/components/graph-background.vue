<template>
  <div
    class="graph-bg flex flex-1 flex-col"
    :class="{ 'no-background': disabled }"
  >
    <slot />
  </div>
</template>

<script>
export default {
  name: 'GraphBackground',
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
  },
}
</script>

<style lang="less" scoped>
.graph-bg {
  background-color: var(--code-tag-background-color);
  background-image: url('../assets/images/graph-background.png');
  background-repeat: repeat;

  &.no-background {
    background: none;
  }
}
</style>
