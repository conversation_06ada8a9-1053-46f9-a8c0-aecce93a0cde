<template>
  <MTooltip
    v-if="error"
    id="error-message"
    overlay-class-name="readable-content-overlay"
  >
    <template v-slot:trigger>
      <MIcon
        name="info-circle"
        :size="size"
        class="cursor-pointer"
        :class="[{ [`text-${type}`]: true }, classes]"
      />
    </template>
    <div style="white-space: preline">
      {{ error }}
    </div>
  </MTooltip>
</template>

<script>
export default {
  name: 'ErrorInfo',
  props: {
    error: { type: [String, Boolean], default: undefined },
    size: { type: String, default: undefined },
    type: { type: String, default: 'primary' },
    classes: { type: String, default: 'ml-1' },
  },
}
</script>
