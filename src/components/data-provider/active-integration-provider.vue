<script>
import Api from '@api'
import Bus from '@utils/emitter'
import Constants from '@constants'

export default {
  name: 'ActiveIntegrationProvider',
  provide() {
    const activeIntegrationContext = {
      add: this.add,
    }
    Object.defineProperty(activeIntegrationContext, 'options', {
      enumerable: true,
      get: () => {
        return this.options
      },
    })
    return { activeIntegrationContext }
  },
  data() {
    return {
      options: new Map(),
    }
  },
  created() {
    this.fetchRoles()
  },
  methods: {
    add(role) {
      const map = new Map(Array.from(this.options))
      map.set(role.key, role)
      this.options = map
    },
    fetchRoles() {
      Api.get('/settings/integrations', {
        params: {
          filter: {
            active: 'yes',
          },
        },
      }).then((data) => {
        const map = new Map()
        const result = data.result || []
        result.forEach((item) => {
          map.set(item[Constants.ID_PROPERTY], {
            key: item[Constants.ID_PROPERTY],
            name: item['integration.type'],
          })
        })
        this.options = map
        Bus.$emit('provider:fetched')
      })
    },
  },
  render() {
    return this.$scopedSlots.default()
  },
}
</script>
