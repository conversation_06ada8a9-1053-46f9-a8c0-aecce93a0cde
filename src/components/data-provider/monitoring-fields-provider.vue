<script>
import Api from '@api'
import Bus from '@utils/emitter'
import Constants from '@constants'

export default {
  name: 'MonitoringFieldsProvider',
  provide() {
    const monitoringFieldsContext = {}
    Object.defineProperty(monitoringFieldsContext, 'options', {
      enumerable: true,
      get: () => {
        return this.options
      },
    })
    return { monitoringFieldsContext }
  },
  props: { useId: { type: Boolean, default: false } },
  data() {
    return {
      options: new Map(),
    }
  },
  created() {
    this.fetchMonitoringFields()
  },
  methods: {
    fetchMonitoringFields() {
      Api.get('/settings/custom-monitoring-fields').then((data) => {
        const map = new Map()
        const result = data.result || []
        result.forEach((item) => {
          map.set(item[Constants.ID_PROPERTY], {
            key: item[Constants.ID_PROPERTY],
            name: item['custom.monitoring.field.name'],
          })
        })
        this.options = map
        Bus.$emit('provider:fetched')
      })
    },
  },
  render() {
    return this.$scopedSlots.default()
  },
}
</script>
