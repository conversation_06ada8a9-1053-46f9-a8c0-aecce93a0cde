<script>
import Api from '@api'
import Constants from '@constants'

export default {
  name: 'LogParserProvider',
  provide() {
    const logParserContext = { add: this.add }
    Object.defineProperty(logParserContext, 'options', {
      enumerable: true,
      get: () => {
        return this.options
      },
    })
    return { logParserContext }
  },

  data() {
    return {
      options: new Map(),
    }
  },
  created() {
    this.getAllLogParsers()
  },
  methods: {
    add(logParser) {
      const map = new Map(Array.from(this.options))
      map.set(logParser.key, logParser)
      this.options = map
    },
    getAllLogParsers() {
      Api.get('/settings/log-parsers').then((data) => {
        const map = new Map()
        const result = data.result || []
        result.forEach((item) => {
          map.set(item[Constants.ID_PROPERTY], {
            key: item[Constants.ID_PROPERTY],
            name: item['log.parser.name'],
          })
        })
        this.options = map
        this.$emit('loaded', result)
      })
    },
  },
  render() {
    return this.$scopedSlots.default()
  },
}
</script>
