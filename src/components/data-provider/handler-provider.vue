<script>
// import api from '@api'

import Constants from '@constants'
import { getAllIntegrationProfileApi } from '@modules/settings/integration/integration-profile-api'

import { INTEGRATION_PROFILE_MODE_TYPE } from '@modules/settings/integration/helpers/integration-profile'

export default {
  name: 'HandlerProvider',
  provide() {
    const HandlerProviderContext = {}
    Object.defineProperty(HandlerProviderContext, 'options', {
      enumerable: true,
      get: () => {
        return this.options
      },
    })
    return { HandlerProviderContext }
  },

  data() {
    return {
      options: [],
    }
  },
  created() {
    this.fetchHandler()
  },
  methods: {
    fetchHandler() {
      return getAllIntegrationProfileApi(
        INTEGRATION_PROFILE_MODE_TYPE.NOTIFICATION
      ).then((data) => {
        this.options = data.map((h) => ({
          key: h[Constants.ID_PROPERTY],
          name: h['handleName'],
          integrationType: h.integrationType,
          id: h[Constants.ID_PROPERTY],
        }))
      })
    },

    gethandlerMaps() {
      let result = {}
      for (let curr of this.options) {
        result[curr.name] = curr
      }
      return result
    },
  },
  render() {
    return this.$scopedSlots.default()
  },
}
</script>
