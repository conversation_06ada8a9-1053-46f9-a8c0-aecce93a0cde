<script>
import { getPolicyActionsApi } from '@/src/modules/settings/policy-settings/policy-api'

export default {
  name: 'RunbookPluginProvider',
  provide() {
    const runbookPluginContext = {}
    Object.defineProperty(runbookPluginContext, 'options', {
      enumerable: true,
      get: () => {
        return this.options
      },
    })
    return { runbookPluginContext }
  },
  props: {
    showLogColltionOnly: {
      type: Boolean,
      default: false,
    },
    filterFn: {
      type: Function,
      default: undefined,
    },
  },
  data() {
    return {
      options: new Map(),
    }
  },
  created() {
    this.fetch()
  },
  methods: {
    fetch() {
      const map = new Map()
      getPolicyActionsApi().then((data) => {
        data.forEach((item) => {
          if (this.filterFn) {
            if (this.filterFn(item)) {
              map.set(item.key, item)
            }
            return
          }
          if (this.showLogColltionOnly) {
            if (item.runbookCategory === 'Log Collection') {
              map.set(item.key, item)
            } else {
              return
            }
          }

          map.set(item.key, item)
        })
        this.options = Object.freeze(map)
      })
    },
    getOption(id) {
      if (this.options.has(id)) {
        return this.options.get(id)
      }
      return undefined
    },
  },
  render() {
    return this.$scopedSlots.default()
  },
}
</script>
