<script>
import GroupBy from 'lodash/groupBy'
import Uniq from 'lodash/uniq'
import { generateId } from '@utils/id'
import Bus from '@utils/emitter'
import { isUnitConvertible } from '@utils/unit-checker'
import applyUnit from '@utils/unit-applier'
import { WidgetTypeConstants } from '@components/widgets/constants'
import { buildWidgetContext, makeCounter } from '@utils/socket-event-as-api'
import buildWidgetResult from '@components/widgets/result-builder'
import { sortedSeriesData } from '@components/widgets/helper'
import { getAllPoliciesApi } from '@modules/settings/policy-settings/policy-api.js'
import { authComputed } from '@state/modules/auth'

export default {
  name: 'MonitorAlertCountProvider',

  provide() {
    const policyGridContext = {
      requestData: this.requestData,
      resetData: this.resetData,
      updateFilteredSevertiy: this.updateFilteredSevertiy,
      resetFilter: this.resetFilter,
    }
    Object.defineProperty(policyGridContext, 'data', {
      enumerable: true,
      get: () => {
        return this.data
      },
    })
    Object.defineProperty(policyGridContext, 'policyWidget', {
      enumerable: true,
      get: () => {
        return this.policyWidget
      },
    })
    Object.defineProperty(policyGridContext, 'filteredSevertiy', {
      enumerable: true,
      get: () => {
        return this.filteredSevertiy
      },
    })
    return { policyGridContext }
  },
  props: {
    selectedTarget: {
      type: Object,
      default: undefined,
    },
  },
  data() {
    return {
      guid: generateId(),
      loading: false,
      data: undefined,
      filteredSevertiy: [],
    }
  },
  computed: {
    ...authComputed,
    convertGridResultToGauge() {
      const groupBySeverity = GroupBy((this.data || {}).rows || [], 'severity')

      return {
        categories: Object.keys(groupBySeverity),
        series: [
          {
            data: sortedSeriesData(
              Object.keys(groupBySeverity).map((key) => {
                return {
                  name: key,
                  y: (groupBySeverity[key] || []).length,
                }
              })
            ),
          },
        ],
      }
    },
    policyWidget() {
      let counters = [
        'severity',
        'policy.name',
        'instance',
        'policy.type',
        'policy.id',
        'metric',
        'value',
        'object.id',
        'duration',
        'policy.first.trigger.tick',
        'policy.threshold',
      ]
      return buildWidgetContext({
        groupCategory: 'metric',
        groupType: 'policy.stream',
        timeline: {
          selectedKey: 'today',
        },
        widgetType: WidgetTypeConstants.ACTIVE_ALERT,
        category: WidgetTypeConstants.GRID,
        counters: counters.map((c) =>
          makeCounter(
            c,
            'last',
            'Monitor',
            Array.isArray((this.selectedTarget || {}).id)
              ? (this.selectedTarget || {}).id
              : [(this.selectedTarget || {}).id]
          )
        ),
      })
        .appendToGroup('policy.stream', {
          target: {
            entityType: 'monitor',
            entities: Array.isArray((this.selectedTarget || {}).id)
              ? (this.selectedTarget || {}).id
              : [(this.selectedTarget || {}).id],
          },
        })
        .setWidgetProperties({
          searchable: true,
          columnSettings: [
            {
              name: 'policy.name',
              displayName: 'Policy Name',
              type: 'alert',
              orderIndex: 1,
              style: {
                'width.percent': 10,
                'color.conditions': [],
              },
            },
            {
              name: 'policy.type',
              displayName: 'Policy Type',
              hidden: true,
              orderIndex: 2,
            },
            {
              name: 'object.id',
              displayName: 'Monitor',
              hidden: true,
              selectable: false,
              type: 'monitor',
              orderIndex: 3,
            },
            {
              name: 'instance',
              displayName: 'Instance',
              selectable: false,
              orderIndex: 4,
            },
            {
              name: 'severity',
              hidden: true,
              selectable: false,
              orderIndex: 5,
            },
            {
              name: 'metric',
              displayName: 'Metric',
              disable: false,
              orderIndex: 6,
            },
            {
              name: 'value',
              displayName: ' Value',
              disable: false,
              orderIndex: 7,
            },
            {
              name: 'duration',
              displayName: 'Duration',
              disable: false,
              orderIndex: 8,
            },
            {
              name: 'policy.id',
              displayName: 'Policy Id',
              hidden: true,
              disable: false,
              orderIndex: 9,
            },
          ],
        })
    },
  },
  watch: {
    selectedTarget: {
      handler(newValue, oldValue) {
        if (
          (newValue || {}).id &&
          ((newValue || {}).id !== (oldValue || {}).id ||
            (newValue || {}).resourceType !== (oldValue || {}).resourceType)
        ) {
          if (
            !Array.isArray(this.selectedTarget?.id) ||
            this.selectedTarget?.id?.length
          ) {
            this.resetData()
            this.requestData()
          }
        }
      },
      immediate: true,
    },
  },
  created() {
    Bus.$on(this.$constants.UI_WIDGET_RESULT_EVENT, this.handleReceiveData)
    this.$once('hook:beforeDestroy', () => {
      Bus.$off(this.$constants.UI_WIDGET_RESULT_EVENT, this.handleReceiveData)
    })
  },
  methods: {
    async handleReceiveData(response) {
      if (response[this.$constants.UI_EVENT_UUID] !== this.guid) {
        return
      }
      let result = await buildWidgetResult(
        this.policyWidget.getContext(),
        response
      )

      this.data = Object.freeze(await this.transformRowData(result))
      this.loading = false
    },
    requestData() {
      if (
        this.selectedTarget &&
        this.hasPermission(this.$constants.ALERT_READ_PERMISSION)
      ) {
        this.loading = true
        const widget = this.policyWidget.generateWidgetDefinition()
        widget.id = -1
        widget[this.$constants.UI_EVENT_UUID] = this.guid
        Bus.$emit('server:event', {
          'event.type': this.$constants.UI_WIDGET_RESULT_EVENT,
          'event.context': {
            ...widget,
            ...(this.selectedTarget.instance
              ? { instance: this.selectedTarget.instance }
              : {}),
            ...(this.$route.params.application ||
            this.selectedTarget.application
              ? {
                  application:
                    this.$route.params.application ||
                    this.selectedTarget.application,
                }
              : {}),
            ...(this.$route.params.vm
              ? { instance: this.$route.params.vm }
              : {}),

            ...(this.selectedTarget.interface
              ? { instance: this.selectedTarget.interface }
              : {}),

            ...(this.$route.params.ap
              ? { instance: this.$route.params.ap }
              : {}),
          },
        })
      }
    },
    resetData() {
      this.data = undefined
      this.resetFilter()
    },
    resetFilter() {
      this.filteredSevertiy = []
    },
    updateFilteredSevertiy(severity, active) {
      if (active) {
        this.filteredSevertiy = Uniq([...this.filteredSevertiy, severity])
      } else {
        this.filteredSevertiy = this.filteredSevertiy.filter(
          (s) => s !== severity
        )
      }
    },
    async transformRowData(result) {
      let policyData = await getAllPoliciesApi(true)

      return {
        ...result,
        rows: (result.rows || []).map((row) => {
          const policySeverity = policyData?.find(
            (policy) => policy.id === row['policy_id']
          )?.severity
          const value =
            isUnitConvertible(row['metric']) &&
            /^\d+(.\d+)?$/.test(row['value'])
              ? applyUnit(row.metric, +row['value'])
              : row['value']
          return {
            ...row,
            counterRawName: row.metric,
            metric: (row.metric || '').replace(/[~^]/g, '.'),
            value,
            policySeverity,
          }
        }),
      }
    },
  },
  render() {
    return this.$scopedSlots.default({
      convertedData: this.convertGridResultToGauge,
      data: this.data,
    })
  },
}
</script>
