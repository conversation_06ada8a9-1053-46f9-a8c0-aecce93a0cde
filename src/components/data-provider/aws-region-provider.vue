<script>
import awsRegions from '@src/statics/aws-regions'

export default {
  name: 'AwsRegionProvider',
  provide() {
    const regionContext = {
      refresh: this.fetchRegions,
    }
    Object.defineProperty(regionContext, 'awsRegionsList', {
      enumerable: true,
      get: () => {
        return this.awsRegionsList
      },
    })
    return { regionContext }
  },
  data() {
    return {
      loading: true,
      awsRegionsList: new Map(),
    }
  },
  created() {
    this.fetchRegions()
  },
  methods: {
    fetchRegions() {
      const map = new Map()
      awsRegions.forEach((option) => {
        map.set(option.key, option.value)
      })
      this.awsRegionsList = map
    },
  },
  render() {
    return this.$scopedSlots.default({
      loading: this.loading,
    })
  },
}
</script>
