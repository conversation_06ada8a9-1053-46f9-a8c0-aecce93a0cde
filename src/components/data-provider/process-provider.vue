<script>
import Api from '@api'
import Constants from '@constants'

export default {
  name: 'ProcessProvider',
  provide() {
    const processContext = {}
    Object.defineProperty(processContext, 'options', {
      enumerable: true,
      get: () => {
        return this.options
      },
    })
    return { processContext }
  },
  data() {
    return {
      options: new Map(),
    }
  },
  created() {
    this.fetchVendors()
  },
  methods: {
    fetchVendors() {
      Api.get('/settings/system-processes').then((data) => {
        const map = new Map()
        const result = data.result || []
        result.forEach((item) => {
          map.set(item[Constants.ID_PROPERTY], {
            key: item[Constants.ID_PROPERTY],
            name: item['system.process'],
          })
        })
        this.options = map
      })
    },
  },
  render() {
    return this.$scopedSlots.default()
  },
}
</script>
