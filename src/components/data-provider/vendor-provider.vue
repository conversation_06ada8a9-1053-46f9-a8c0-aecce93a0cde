<script>
import Api from '@api'

export default {
  name: 'VendorProvider',
  provide() {
    const vendorContext = {}
    Object.defineProperty(vendorContext, 'options', {
      enumerable: true,
      get: () => {
        return this.options
      },
    })
    return { vendorContext }
  },
  props: {
    // eslint-disable-next-line
    autoFetch: { type: Boolean, default: true },
    defaultOptions: {
      type: Array,
      default() {
        return []
      },
    },
    url: { type: String, default: '/settings/snmp-device-catalogs/vendors' },
  },
  data() {
    return {
      options: new Map(),
    }
  },
  created() {
    if (this.autoFetch) {
      this.fetchVendors()
    } else {
      this.$watch('defaultOptions', (newValue) => {
        this.options = Object.freeze(newValue)
      })
    }
  },
  methods: {
    fetchVendors() {
      Api.get(this.url).then((data) => {
        const map = new Map()
        const result = data.result || []
        result.forEach((item) => {
          map.set(item, { key: item, name: item })
        })
        this.options = map
      })
    },
  },
  render() {
    return this.$scopedSlots.default()
  },
}
</script>
