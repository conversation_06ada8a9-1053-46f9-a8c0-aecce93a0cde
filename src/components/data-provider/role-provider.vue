<script>
import Api from '@api'
import Bus from '@utils/emitter'
import Constants from '@constants'

export default {
  name: 'Role<PERSON>rovider',
  provide() {
    const roleContext = {
      add: this.add,
    }
    Object.defineProperty(roleContext, 'options', {
      enumerable: true,
      get: () => {
        return this.options
      },
    })
    return { roleContext }
  },
  data() {
    return {
      options: new Map(),
    }
  },
  created() {
    this.fetchRoles()
  },
  methods: {
    add(role) {
      const map = new Map(Array.from(this.options))
      map.set(role.key, role)
      this.options = map
    },
    fetchRoles() {
      Api.get('/settings/user-roles').then((data) => {
        const map = new Map()
        const result = data.result || []
        result.forEach((item) => {
          map.set(item[Constants.ID_PROPERTY], {
            key: item[Constants.ID_PROPERTY],
            name: item['user.role.name'],
          })
        })
        this.options = map
        Bus.$emit('provider:fetched')
      })
    },
  },
  render() {
    return this.$scopedSlots.default()
  },
}
</script>
