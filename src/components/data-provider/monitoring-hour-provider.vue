<script>
import Api from '@api'
import Bus from '@utils/emitter'
import Constants from '@constants'

export default {
  name: 'MonitoringHourProvider',
  provide() {
    const monitoringHourContext = {}
    Object.defineProperty(monitoringHourContext, 'options', {
      enumerable: true,
      get: () => {
        return this.options
      },
    })
    return { monitoringHourContext }
  },
  data() {
    return {
      options: new Map(),
    }
  },
  created() {
    this.fetchRemotePollers()
  },
  methods: {
    fetchRemotePollers() {
      Api.get('/settings/business-hours').then((data) => {
        const map = new Map()
        const result = data.result || []
        result.forEach((item) => {
          map.set(item[Constants.ID_PROPERTY], {
            key: item[Constants.ID_PROPERTY],
            name: item['business.hour.name'],
          })
        })
        this.options = map
        Bus.$emit('provider:fetched')
      })
    },
  },
  render() {
    return this.$scopedSlots.default()
  },
}
</script>
