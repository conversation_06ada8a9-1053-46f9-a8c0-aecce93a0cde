<script>
import Debounce from 'lodash/debounce'
import Bus from '@utils/emitter'
import { generateId } from '@utils/id'
import { convertTimeLineForServer } from '@/src/components/widgets/helper'
import { WidgetTypeConstants } from '@components/widgets/constants'

export default {
  name: 'IncrementalResultProvider',
  inject: { SocketContext: { default: {} } },

  props: {
    timeline: {
      type: Object,
      default() {
        return {
          selectedKey: 'today',
        }
      },
    },
    serversideWidgetDefination: {
      type: Object,
      required: true,
    },
    fullResponse: {
      type: Boolean,
      default: false,
    },
    defaultGuid: {
      type: String,
      default: undefined,
    },
    disableAutoFetching: {
      type: Boolean,
      default: false,
    },
    reportCategory: {
      type: String,
      default: undefined,
    },
    serverParams: {
      type: Object,
      default() {
        return {}
      },
    },
    disabledStreamingTimer: {
      type: Boolean,
      default: false,
    },
    isPreview: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    this.data = {
      notifications: [],
    }
    return {
      isPaused: false,
      guid: this.defaultGuid || generateId(),
      streaming: false,
      loading: true,
      queryProgress: null,
      currentBatch: 1,
      gridData: [],
    }
  },

  created() {
    this.requestData = Debounce(this.requestDataRaw, 1000, { traling: true })
    Bus.$on('socket:connected', this.requestData)
    if (this.SocketContext.connected && !this.defaultGuid) {
      this.requestData()
    }

    Bus.$on(this.$constants.UI_WIDGET_RESULT_EVENT, this.handleDataReceived)

    this.$once('hook:beforeDestroy', () => {
      // if (!this.defaultGuid) {
      this.stopHeartbeat()
      // }
      Bus.$off('socket:connected', this.requestData)
      this.abortQuery()

      Bus.$off(this.$constants.UI_WIDGET_RESULT_EVENT, this.handleDataReceived)
    })
  },
  methods: {
    requestDataRaw(sendRequest = true) {
      this.guid = this.defaultGuid || generateId()
      this.abortQuery()
      this.isPaused = false
      this.loading = true
      this.error = null
      this.queryProgress = null
      this.gridData = []
      this.currentBatch = 1

      if (sendRequest) {
        this.startIncrementalRequest()
      }
    },
    async startIncrementalRequest(queryId = null, subQueryId, timerange) {
      const widget = this.serversideWidgetDefination

      Bus.$emit('server:event', {
        'event.type': this.$constants.UI_WIDGET_RESULT_EVENT,
        'event.context': {
          ...widget,
          ...this.serverParams,
          ...(queryId
            ? { 'query.id': queryId, 'sub.query.id': subQueryId }
            : {}),
          [this.$constants.UI_EVENT_UUID]: this.guid,

          ...(timerange
            ? {
                'visualization.timeline': convertTimeLineForServer(timerange),
              }
            : {}),

          ...(this.isPreview ? { id: -1 } : {}),
        },
      })
    },

    async handleDataReceived(response) {
      let patchData
      if (response[this.$constants.UI_EVENT_UUID] !== this.guid) {
        return
      }
      this.__parentQueryId = response.result.queryMeta.parentQueryId
      this.__parentSubQueryId = response['sub.query.id']
      this.queryProgress = response.result.queryMeta.progress

      patchData = this.fullResponse
        ? response
        : ((response.result || {})[WidgetTypeConstants.GRID] || {}).data || []

      this.$emit('patchRecived', patchData)
      if (response.result.error) {
        this.error = response.result.error
        if (response.result.queryMeta.progress >= 100) {
          this.loading = false
          this.stopHeartbeat()
          return
        }
      }

      if (this.currentBatch === 1 && !this.defaultGuid) {
        this.sendActiveSessionEvent()
        // start active session event
        this.startHeartbeat()
        // start session heartbeat
      }

      if (!this.disableAutoFetching) {
        if (response.result.queryMeta.progress < 100) {
          setTimeout(() => {
            this.$nextTick(() => {
              this.requestNextBatch()
            })
          }, 100)
        }
      }
    },

    getProgress() {
      return this.queryProgress
    },

    async requestNextBatch(timerange) {
      if (this.isPaused) {
        return
      }
      if (this.queryProgress >= 100) {
        return
      }
      this.currentBatch++
      this.startIncrementalRequest(
        this.__parentQueryId,
        this.__parentSubQueryId,
        timerange
      )
    },

    sendActiveSessionEvent() {
      if (
        !this.__parentQueryId ||
        this.reportCategory ||
        this.disabledStreamingTimer
      ) {
        return
      }
      Bus.$emit('server:event', {
        'event.type': this.$constants.UI_WIDGET_ACTIVE_SESSION,
        'event.context': {
          'query.id': this.__parentQueryId,
          [this.$constants.UI_EVENT_UUID]: this.guid,
          'event.context': {
            ...(this.serverParams || {}),
            ...(this.timeline
              ? {
                  'visualization.timeline': convertTimeLineForServer(
                    this.timeline
                  ),
                }
              : {}),
          },
        },
      })
    },
    abortQuery() {
      if (this.__parentQueryId) {
        this.queryProgress = null
        this.isPaused = true
        Bus.$emit('server:event', {
          'event.type': this.$constants.UI_WIDGET_ABORT_EVENT,
          'event.context': {
            'query.id': this.__parentQueryId,
          },
        })
      }
    },

    resetData() {},
    startHeartbeat() {
      if (this.reportCategory || this.disabledStreamingTimer) {
        return
      }
      this.stopHeartbeat()
      this.__streamingTimer = setInterval(this.sendActiveSessionEvent, 10000)
    },
    stopHeartbeat() {
      if (this.__streamingTimer) {
        clearInterval(this.__streamingTimer)
        this.__streamingTimer = null
      }
    },
    setGuid(guid) {
      this.guid = guid
    },
  },

  render(h) {
    return (
      this.$scopedSlots &&
      this.$scopedSlots.default &&
      this.$scopedSlots.default({
        requestData: this.requestData,
        abortQuery: this.abortQuery,
        guid: this.guid,
        parentQueryId: this.__parentQueryId,
      })
    )
  },
}
</script>
