<script>
import Bus from '@utils/emitter'
import Constants from '@constants'
import { fetchAllLdapServerApi } from '@modules/settings/users-settings/ldap-server-api'

export default {
  name: 'LdapServerProvider',
  provide() {
    const ldapServerContext = {
      add: this.add,
    }
    Object.defineProperty(ldapServerContext, 'options', {
      enumerable: true,
      get: () => {
        return this.options
      },
    })
    return { ldapServerContext }
  },
  data() {
    return {
      options: new Map(),
    }
  },
  created() {
    this.fetchRoles()
  },
  methods: {
    fetchRoles() {
      return fetchAllLdapServerApi().then((data) => {
        const map = new Map()
        const result = data || []
        result.forEach((item) => {
          map.set(item[Constants.ID_PROPERTY], {
            key: item[Constants.ID_PROPERTY],
            name: item['ldapServerHost'],
          })
        })
        this.options = map
        Bus.$emit('provider:fetched')
      })
    },

    getOptions() {
      return this.options
    },
  },
  render() {
    return this.$scopedSlots.default()
  },
}
</script>
