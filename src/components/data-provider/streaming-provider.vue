<script>
import Bus from '@utils/emitter'
import { generateId } from '@utils/id'

export default {
  name: 'StreamingProvider',
  inject: { SocketContext: { defualt: {} } },
  props: {
    type: {
      type: String,
      required: true,
    },
    disableAutoStart: {
      type: Boolean,
      default: false,
    },
    params: {
      type: Object,
      default() {
        return {}
      },
    },
    replaceExistingData: {
      type: Boolean,
      // eslint-disable-next-line
      default: true,
    },
    eventInterval: {
      type: Number,
      default: 5000,
    },
    ignoreUuid: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    this.data = {
      notifications: [],
    }
    return {
      guid: generateId(),
      streaming: false,
    }
  },
  watch: {
    params: {
      deep: true,
      handler() {
        if (this.disableAutoStart) {
          return
        }
        this.restart()
      },
    },
  },
  created() {
    Bus.$on('socket:connected', this.startStreaming)
    Bus.$on(
      this.$constants.UI_EVENT_STREAMING_NOTIFICATION,
      this.handleNotification
    )
    if (!this.disableAutoStart) {
      this.startStreaming()
    }
    this.startHeartBeat()
  },
  beforeDestroy() {
    Bus.$off('socket:connected', this.startStreaming)
    Bus.$off(
      this.$constants.UI_EVENT_STREAMING_NOTIFICATION,
      this.handleNotification
    )

    this.stopStreaming()
  },
  methods: {
    restart() {
      this.stopStreaming()
      setTimeout(() => {
        this.startStreaming()
        this.startHeartBeat()
      }, 500)
    },
    handleNotification(item) {
      if (
        this.ignoreUuid === false &&
        item[this.$constants.UI_EVENT_UUID] !== this.guid
      ) {
        return
      }
      if (!this.streaming) {
        return
      }
      if (item['streaming.type'] !== this.type) {
        return
      }
      if (Array.isArray(item)) {
        item = item.map((i) => ({ ...i, guid: generateId() }))
      } else {
        item.guid = generateId()
      }
      if (this.replaceExistingData) {
        this.data = Object.freeze({
          notifications: [...(Array.isArray(item) ? item : [item])],
        })
      } else {
        this.data = Object.freeze({
          notifications: [
            ...this.data.notifications,
            ...(Array.isArray(item) ? item : [item]),
          ],
        })
      }
      this.$emit('notification', item)
      this.$emit('updated', this.data)
    },
    sendHeartBeat() {
      Bus.$emit('server:event', {
        'event.type': this.$constants.UI_EVENT_STREAMING_START,
        'event.context': {
          'streaming.type': this.type,
          [this.$constants.UI_EVENT_UUID]: this.guid,
          ...this.params,
        },
      })
    },
    startStreaming() {
      this.streaming = true
      this.sendHeartBeat()
    },
    stopStreaming() {
      if (this.__eventInterval) {
        clearInterval(this.__eventInterval)
      }
      this.streaming = false
      Bus.$emit('server:event', {
        'event.type': this.$constants.UI_EVENT_STREAMING_STOP,
        'event.context': {
          [this.$constants.UI_EVENT_UUID]: this.guid,
          'streaming.type': this.type,
        },
      })
    },
    startHeartBeat() {
      if (this.eventInterval) {
        this.__eventInterval = setInterval(
          this.sendHeartBeat,
          this.eventInterval
        )
      }
    },
  },
  render(h) {
    return (
      this.$scopedSlots &&
      this.$scopedSlots.default &&
      this.$scopedSlots.default({
        isStreaming: this.streaming,
        guid: this.guid,
        start: this.startStreaming,
        stop: this.stopStreaming,
        restart: this.restart,
      })
    )
  },
}
</script>
