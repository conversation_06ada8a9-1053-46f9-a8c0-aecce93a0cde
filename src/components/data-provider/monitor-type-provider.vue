<script>
import SortBy from 'lodash/sortBy'
import { ConfigComputed } from '@state/modules/config'
import { getObjectTypes } from '../../modules/settings/plugin-library/object-type-api'

export default {
  name: 'MonitorTypeProvider',
  provide() {
    const monitorTypeContext = {}
    Object.defineProperty(monitorTypeContext, 'options', {
      enumerable: true,
      get: () => {
        return this.options
      },
    })
    return { monitorTypeContext }
  },
  props: {
    deviceTypes: {
      type: [Array],
      default() {
        return ['Network']
      },
    },
    includeOnly: {
      type: [Array],
      default: undefined,
    },
    exclude: {
      type: [Array],
      default: undefined,
    },
    ignoreAvailableFilter: { type: Boolean, default: false },
  },
  data() {
    this.allTypes = []
    return {
      options: new Map(),
    }
  },
  computed: {
    ...ConfigComputed,
  },
  watch: {
    deviceTypes() {
      this.setOptions()
    },
    availableMonitorTypes: 'setOptions',
    ignoreAvailableFilter(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.setOptions()
      }
    },
  },
  created() {
    this.fetchMonitorTypes()
  },
  methods: {
    setOptions() {
      let options = []
      const allTypeMaps = this.allTypes
      const includeOnly = this.includeOnly
      const exclude = this.exclude
      const ignoreAvailableFilter = this.ignoreAvailableFilter
      const availableMonitorTypes = this.availableMonitorTypes || []
      options = SortBy(
        [
          ...options,
          ...allTypeMaps
            .filter((type) => {
              if (includeOnly && includeOnly.indexOf(type) === -1) {
                return false
              }
              if (exclude && exclude.indexOf(type) >= 0) {
                return false
              }
              if (ignoreAvailableFilter) {
                return true
              }
              return availableMonitorTypes.indexOf(type) >= 0
            })
            .map((type) => ({ key: type, name: type })),
        ],
        'name'
      )
      const map = new Map()
      options.forEach((o) => {
        map.set(o.key, o)
      })
      this.options = map
    },
    fetchMonitorTypes() {
      getObjectTypes(
        this.deviceTypes.length
          ? { key: 'object.category', value: this.deviceTypes }
          : undefined
      ).then((data) => {
        this.allTypes = Array.isArray(data)
          ? Object.freeze(data)
          : Object.freeze(data.types)

        this.setOptions()
      })
    },
  },
  render() {
    return this.$scopedSlots.default()
  },
}
</script>
