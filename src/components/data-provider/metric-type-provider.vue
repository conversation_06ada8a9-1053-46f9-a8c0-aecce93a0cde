<script>
import { ConfigComputed } from '@state/modules/config'
import { getObjectTypes } from '@modules/settings/plugin-library/object-type-api'

export default {
  name: 'MetricTypeProvider',
  provide() {
    const metricTypeContext = {}
    Object.defineProperty(metricTypeContext, 'options', {
      enumerable: true,
      get: () => {
        return this.options
      },
    })
    return { metricTypeContext }
  },
  props: {
    ignoreAvailableFilter: { type: <PERSON><PERSON><PERSON>, default: false },
    credentialProfileProtocol: { type: Array, default: () => [] },
  },
  data() {
    return {
      options: new Map(),
    }
  },
  computed: {
    ...ConfigComputed,
  },
  watch: {
    availableMetricTypes: 'getOptions',
    ignoreAvailableFilter(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.getOptions()
      }
    },
    credentialProfileProtocol: {
      handler: 'getOptions',
      deep: true,
    },
  },
  created() {
    this.getOptions()
  },
  methods: {
    async getOptions() {
      const map = new Map()
      let protocols = [
        ...(Array.isArray(this.credentialProfileProtocol)
          ? this.credentialProfileProtocol
          : [this.credentialProfileProtocol]),
      ]
      if (protocols.includes(this.$constants.JDBC)) {
        protocols = [...protocols]
      }
      // if (protocols.includes(this.$constants.HTTP_HTTPS)) {
      //   protocols = [
      //     ...protocols.filter((f) => f !== this.$constants.HTTP_HTTPS),
      //     'HTTP_HTTPS',
      //   ]
      // }
      const response = await getObjectTypes(
        protocols.length
          ? {
              key: 'protocol',
              value: protocols,
            }
          : undefined
      )

      const types = (
        Array.isArray(response) ? response : response.types || []
      ).sort()
      types.forEach((type) => {
        const category = type

        if (
          this.ignoreAvailableFilter ||
          this.availableMetricTypes.indexOf(type) >= 0
        ) {
          map.set(category.toLowerCase(), { key: type, text: type })
        }
      })
      this.options = map
    },
  },
  render() {
    return this.$scopedSlots.default()
  },
}
</script>
