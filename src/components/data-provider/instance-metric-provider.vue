<script>
import Debounce from 'lodash/debounce'
import isEqual from 'lodash/isEqual'
import UniqBy from 'lodash/uniqBy'
import { WidgetTypeConstants } from '@components/widgets/constants'
import {
  getWidgetResponseApi,
  makeCounter,
  buildWidgetContext,
} from '@utils/socket-event-as-api'

export default {
  name: 'InstanceMetricProvider',
  inject: { counterContext: { default: { options: new Map() } } },
  provide() {
    const instanceMetricContext = {
      setSelectedCounter: this.setSelectedCounter,
    }
    Object.defineProperty(instanceMetricContext, 'options', {
      enumerable: true,
      get: () => {
        return this.filterOptions
      },
    })

    return { instanceMetricContext }
  },
  props: {
    instance: {
      type: String,
      default: undefined,
    },
    target: {
      type: Object,
      default: undefined,
    },
    groupType: {
      type: String,
      required: true,
    },
    timeline: {
      type: Object,
      default() {
        return {
          selectedKey: 'today',
        }
      },
    },
    shouldFetchAllCounters: {
      type: Boolean,
      default: false,
    },

    fetchCounterFn: {
      type: Function,
      default: undefined,
    },
  },
  data() {
    return {
      filterOptions: {
        columns: [],
        data: [],
      },
      selectedCounter: null,

      counterOptions: new Map(),
    }
  },
  watch: {
    instance(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.fillColumns()
        // this.fetchFilterDrawerDataDebounced()
      }
    },
    target(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.fetchFilterDrawerDataDebounced()
      }
    },
    timeline(newValue, oldValue) {
      if (!isEqual(newValue || {}, oldValue || {})) {
        this.fetchFilterDrawerDataDebounced()
      }
    },
    selectedCounter(newValue, oldValue) {
      if (newValue && !isEqual(newValue || {}, oldValue || {})) {
        this.fetchFilterDrawerDataDebounced()
      }
    },
    counterOptions() {
      if (this.fetchCounterFn && this.shouldFetchAllCounters) {
        this.fillColumns()
      }
    },
  },
  async created() {
    this.fillColumns()

    this.fetchFilterDrawerDataDebounced = Debounce(
      this.fetchFilterDrawerData,
      1000,
      {
        trailing: true,
        leading: false,
      }
    )

    if (this.shouldFetchAllCounters) {
      if (this.fetchCounterFn) {
        this.counterOptions = await this.fetchCounterFn()
      }
    }
  },
  methods: {
    setSelectedCounter(counter) {
      this.selectedCounter = counter
    },
    async fillColumns() {
      let counters
      if (!this.instance) {
        this.filterOptions = {
          columns: [],
          data: Object.freeze([]),
        }
        return
      }

      if (this.shouldFetchAllCounters) {
        if (this.fetchCounterFn) {
          counters = this.counterOptions
        }
      } else {
        counters = this.counterContext.options
      }

      counters = Array.from(counters.values()).filter(
        (c) =>
          c.instanceType === this.instance &&
          c.dataType.includes('string') &&
          c.key !== this.instance
      )
      if (counters.length === 0) {
        counters = [
          {
            key: `${this.instance}~instance.name`,
            name: `${this.instance}~instance.name`,
          },
        ]
      }
      this.filterOptions = {
        columns: Object.freeze(
          counters.map((c) => ({
            key: c?.key,
            text: c?.name?.replace('.last', ''),
          }))
        ),
        data: Object.freeze([]),
      }
    },
    fetchFilterDrawerData() {
      if (!this.instance || !this.selectedCounter) {
        return
      }
      this.filterOptions = {
        ...this.filterOptions,
        data: [],
      }
      let counters = Array.from(this.counterContext.options.values()).filter(
        (c) => c.key === this.selectedCounter
      )
      if (counters.length === 0) {
        counters = [
          {
            key: `${this.instance}~instance.name`,
          },
        ]
      }
      const target = this.target || {}
      getWidgetResponseApi(
        buildWidgetContext({
          groupType: this.groupType,
          timeline: this.timeline,
          counters: counters.map((c) =>
            makeCounter(c.key, 'last', target.entityType, target.entities)
          ),
          resultBy: this.instance,
        }).generateWidgetDefinition(),
        { fullResponse: true, ignoreTimeout: true }
      ).then(this.handleDataReceived)
    },
    handleDataReceived(data) {
      const rows = (data.result[WidgetTypeConstants.GRID] || {}).data || []
      const columns =
        (data.result[WidgetTypeConstants.GRID] || {}).rawColumns || []
      this.filterOptions = {
        columns: Object.freeze(
          UniqBy(
            [
              ...this.filterOptions.columns,

              ...columns.map((c) => ({
                key: c.rawName,
                text: c.name.replace('.last', ''),
              })),
            ],
            'key'
          )
        ),
        data: Object.freeze(rows),
      }
      this.$emit('data', this.filterOptions)
    },
  },
  render() {
    return this.$scopedSlots.default && this.$scopedSlots.default()
  },
}
</script>
