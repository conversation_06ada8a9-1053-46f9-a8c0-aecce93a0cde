<script>
import Api from '@api'
import Bus from '@utils/emitter'
import Constants from '@constants'
export default {
  name: 'SNMPTraoProfileProvider',
  provide() {
    const trapProfilesContext = {}
    Object.defineProperty(trapProfilesContext, 'options', {
      enumerable: true,
      get: () => {
        return this.options
      },
    })
    return { trapProfilesContext }
  },

  data() {
    return {
      options: new Map(),
    }
  },
  created() {
    this.fetchSNMPTrapProfiles()
  },
  methods: {
    fetchSNMPTrapProfiles() {
      Api.get('/settings/snmp-trap-profiles').then((data) => {
        let result = (data.result || []).map((r) => ({
          id: r[Constants.ID_PROPERTY],
          key: r[Constants.ID_PROPERTY],
          name: r['snmp.trap.profile.name'],
          oid: r['snmp.trap.profile.oid'],
        }))
        const map = new Map()
        result.forEach((result) => map.set(result.key, result))
        this.options = map
        Bus.$emit('provider:fetched')
      })
    },
  },
  render() {
    return this.$scopedSlots.default()
  },
}
</script>
