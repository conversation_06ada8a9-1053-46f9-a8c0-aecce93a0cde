<script>
import { getAllTagsApi } from '@modules/settings/monitoring/monitors-api'

export default {
  name: 'ObjectTagProvider',
  provide() {
    const tagContext = {}
    Object.defineProperty(tagContext, 'options', {
      enumerable: true,
      get: () => {
        return this.options
      },
    })
    return { tagContext }
  },
  props: {
    type: {
      type: String,
      default: 'metric',
    },
    counter: {
      type: Object,
      default: undefined,
    },
    ignoreWatch: {
      type: Boolean,
      default: false,
    },
    useKeyValuePairOnly: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      options: new Map(),
    }
  },
  watch: {
    counter(newValue, oldValue) {
      if (newValue?.key !== oldValue?.key && !this.ignoreWatch) {
        this.getAvailableTags()
      }
    },
  },
  created() {
    this.getAvailableTags()
  },
  methods: {
    async getAvailableTags(counter) {
      return getAllTagsApi(false, counter?.key || this.counter?.key).then(
        (data) => {
          const map = new Map()
          data.forEach((i, index) => {
            if (this.useKeyValuePairOnly) {
              if (i.isKeyValueTag) {
                map.set(index, {
                  ...i,
                  key: i.splitedKey,
                  label: i.splitedKey,
                  text: i.splitedKey,
                  value: i.splitedKey,
                })
              }
              return
            }
            map.set(index, {
              ...i,
              key: i.tag,
              label: i.tag,
              text: i.tag,
              value: i.tag,
            })
          })
          this.options = map
          this.$emit('tag-loaded', this.options)

          return this.options
        }
      )
    },
  },
  render() {
    return this.$scopedSlots.default()
  },
}
</script>
