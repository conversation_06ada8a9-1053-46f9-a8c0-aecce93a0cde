<script>
import Api from '@api'
import IsEqual from 'lodash/isEqual'
import Bus from '@utils/emitter'
import { transformCredentialProfileForDropdown } from '@modules/settings/network-discovery/helpers/credential-profile'

export default {
  name: 'CredentialProvider',
  provide() {
    const credentialContext = {
      add: this.add,
    }
    Object.defineProperty(credentialContext, 'options', {
      enumerable: true,
      get: () => {
        return this.options
      },
    })
    return { credentialContext }
  },
  props: {
    searchParams: {
      type: Object,
      default() {
        return {}
      },
    },
    filterFn: {
      type: Function,
      default: undefined,
    },
  },
  data() {
    return {
      options: new Map(),
    }
  },
  watch: {
    searchParams(newValue, oldValue) {
      if (!IsEqual(newValue, oldValue)) {
        this.fetchCredentials()
      }
    },
  },
  created() {
    this.fetchCredentials()
  },
  methods: {
    add(option) {
      const map = new Map(Array.from(this.options))
      map.set(option.key, option)
      this.options = map
      this.$emit('loaded', this.options)
    },
    fetchCredentials() {
      const v = this.searchParams.value
      Api.get(
        '/settings/credential-profiles',
        v
          ? {
              params: {
                filter: this.searchParams,
              },
            }
          : {}
      ).then((data) => {
        let result = data.result || []

        result = result
          .filter((item) => {
            if (!v) {
              return true
            }
            return Array.isArray(v)
              ? v.indexOf(item['credential.profile.protocol']) >= 0
              : v === item['credential.profile.protocol'] ||
                  item['credential.profile.protocol']
                    .toLowerCase()
                    .indexOf(v.toLowerCase()) >= 0
          })
          .map(transformCredentialProfileForDropdown)

        if (this.filterFn) {
          result = result.filter(this.filterFn)
        }

        const map = new Map()
        result.forEach((result) => map.set(result.key, result))
        this.options = map

        this.$emit('loaded', this.options)
        Bus.$emit('provider:fetched')
      })
    },
  },
  render() {
    return this.$scopedSlots.default()
  },
}
</script>
