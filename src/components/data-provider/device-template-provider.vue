<script>
import Api from '@api'
import Constants from '@constants'

export default {
  name: 'DeviceTemplateProvider',
  provide() {
    const deviceTemplateContext = {}
    Object.defineProperty(deviceTemplateContext, 'options', {
      enumerable: true,
      get: () => {
        return this.options
      },
    })
    return { deviceTemplateContext }
  },
  props: {
    // eslint-disable-next-line
    autoFetch: { type: Boolean, default: true },
    defaultOptions: {
      type: Array,
      default() {
        return []
      },
    },
  },
  data() {
    return {
      options: new Map(),
    }
  },
  created() {
    if (this.autoFetch) {
      this.fetchDeviceTemplate()
    } else {
      this.$watch('defaultOptions', (newValue) => {
        this.options = Object.freeze(newValue)
      })
    }
  },
  methods: {
    fetchDeviceTemplate() {
      Api.get('/settings/config-templates').then((data) => {
        const map = new Map()
        const result = data.result || []
        result.forEach((item) => {
          map.set(item[Constants.ID_PROPERTY], {
            id: item[Constants.ID_PROPERTY],
            key: item[Constants.ID_PROPERTY],
            name: item['config.template.name'],
            vendor: item['config.template.vendor'],
            os: item['config.template.os.type'],
            devices: item['count'],
          })
        })
        this.options = map
        this.$emit('loaded', result)
      })
    },
  },
  render() {
    return this.$scopedSlots.default()
  },
}
</script>
