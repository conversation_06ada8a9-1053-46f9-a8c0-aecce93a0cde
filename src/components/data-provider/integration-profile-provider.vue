<script>
import Constants from '@constants'
import { getAllIntegrationProfileApi } from '@modules/settings/integration/integration-profile-api'
import { INTEGRATION_PROFILE_MODE_TYPE } from '@modules/settings/integration/helpers/integration-profile'

export default {
  name: 'IntegrationProfileProvier',
  provide() {
    const IntegrationProfileProviderContext = {}
    Object.defineProperty(IntegrationProfileProviderContext, 'options', {
      enumerable: true,
      get: () => {
        return this.options
      },
    })
    return { IntegrationProfileProviderContext }
  },
  props: {
    transformDataForPicker: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      options: [],
    }
  },
  created() {
    this.fetchIntegrationProfile()
  },
  methods: {
    fetchIntegrationProfile() {
      getAllIntegrationProfileApi(INTEGRATION_PROFILE_MODE_TYPE.INCIDENT).then(
        (data) => {
          this.options = Object.freeze(
            data.map((integrationProfile) => ({
              id: integrationProfile[Constants.ID_PROPERTY],
              key: integrationProfile[Constants.ID_PROPERTY],
              name: integrationProfile.name,
              description: integrationProfile.description,
              integrationType: integrationProfile.integrationType,
            }))
          )
        }
      )
    },
  },
  render() {
    return this.$scopedSlots.default()
  },
}
</script>
