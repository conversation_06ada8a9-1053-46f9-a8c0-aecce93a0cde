<script>
// import Api from '@api'
import Bus from '@utils/emitter'
// import Constants from '@constants'

import { getNcmObjectsApi } from '@modules/ncm/ncm-api'

export default {
  name: 'NcmMonitorProvider',
  provide() {
    const ncmMonitorContext = {
      add: this.add,
    }
    Object.defineProperty(ncmMonitorContext, 'options', {
      enumerable: true,
      get: () => {
        return this.options
      },
    })
    return { ncmMonitorContext }
  },
  data() {
    return {
      options: new Map(),
    }
  },
  created() {
    this.fetchRoles()
  },
  methods: {
    fetchRoles() {
      return getNcmObjectsApi().then((data) => {
        const map = new Map()
        const result = data || []
        result.forEach((item) => {
          map.set(item['objectId'], {
            id: item['objectId'],
            key: item['objectId'],
            device: item['device'],
            ip: item['ip'],
            type: item['deviceType'],
            groups: item['groups'],
            vendor: item['vendor'],
            name: item['device'],
          })
        })
        this.options = map
        Bus.$emit('provider:fetched')
      })
    },
  },
  render() {
    return this.$scopedSlots.default()
  },
}
</script>
