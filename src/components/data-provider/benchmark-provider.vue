<script>
import Api from '@api'
import Bus from '@utils/emitter'
import Constants from '@constants'

export default {
  name: 'BenchmarkProvider',
  provide() {
    const benchmarkContext = {
      add: this.add,
    }
    Object.defineProperty(benchmarkContext, 'options', {
      enumerable: true,
      get: () => {
        return this.options
      },
    })
    return { benchmarkContext }
  },
  data() {
    return {
      options: new Map(),
    }
  },
  created() {
    this.fetchBenchmarks()
  },
  methods: {
    add(benchmark) {
      const map = new Map(Array.from(this.options))
      map.set(benchmark.key, benchmark)
      this.options = map
    },
    fetchBenchmarks() {
      Api.get('/settings/compliance-benchmarks').then((data) => {
        const map = new Map()
        const result = data.result || []
        result.forEach((item) => {
          map.set(item[Constants.ID_PROPERTY], {
            key: item[Constants.ID_PROPERTY],
            name: item['compliance.benchmark.name'],
            tags: item['compliance.benchmark.tags'],
          })
        })
        this.options = map
        Bus.$emit('provider:fetched')
      })
    },
  },
  render() {
    return this.$scopedSlots.default()
  },
}
</script>
