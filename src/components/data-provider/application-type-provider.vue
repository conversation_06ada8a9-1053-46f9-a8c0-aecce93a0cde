<script>
import ApplicationTypes from '@src/statics/application-types.js'

export default {
  name: 'ApplicationTypeProvider',
  provide() {
    const applicationTypeContext = {}
    Object.defineProperty(applicationTypeContext, 'options', {
      enumerable: true,
      get: () => {
        return this.options
      },
    })
    return { applicationTypeContext }
  },
  data() {
    return {
      options: new Map(),
    }
  },
  created() {
    this.fetchApplicationTypes()
  },
  methods: {
    fetchApplicationTypes() {
      const map = new Map()
      Object.keys(ApplicationTypes).forEach((key) => {
        map.set(key, {
          key: key,
          name: key,
          protocol: ApplicationTypes[key],
        })
      })
      this.options = map
    },
  },
  render() {
    return this.$scopedSlots.default()
  },
}
</script>
