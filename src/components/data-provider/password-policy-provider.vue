<script>
import Pick from 'lodash/pick'
import { fetchPasswordPolicyApi } from '@modules/settings/users-settings/password-policy-api'

export default {
  name: 'PasswordPolicyProvider',
  provide() {
    const passwordPolicyContext = {
      availablePolicies: this.availablePolicies,
      policyTextMap: this.policyTextMap,
    }
    Object.defineProperty(passwordPolicyContext, 'policies', {
      enumerable: true,
      get: () => {
        return this.policies
      },
    })
    return { passwordPolicyContext }
  },
  props: {
    usePolicy: { type: Boolean, default: false },
  },
  data() {
    this.availablePolicies = [
      'passwordPolicyPasswordMinimumLength',
      'passwordPolicySpecialCharacterMandatory',
      'passwordPolicyNumberMandatory',
      'passwordPolicyLowercaseMandatory',
      'passwordPolicyUppercaseMandatory',
    ]
    this.policyTextMap = {
      passwordPolicyPasswordMinimumLength:
        'Password must be of minimum length ',
      passwordPolicySpecialCharacterMandatory:
        'Password must contain at least a special character',
      passwordPolicyNumberMandatory:
        'Password must contain at least a number character',
      passwordPolicyLowercaseMandatory:
        'Password must contain at least a lower case character',
      passwordPolicyUppercaseMandatory:
        'Password must contain at least a upper case character',
    }
    return {
      policies: {},
    }
  },
  created() {
    this.fetchPolicies()
  },
  methods: {
    fetchPolicies() {
      fetchPasswordPolicyApi().then((data) => {
        let policyData = {}
        Object.keys(Pick(data, this.availablePolicies)).forEach((policy) => {
          if (data[policy]) {
            policyData[policy] = data[policy]
          }
        })
        this.policies = Object.keys(policyData).length ? policyData : null
      })
    },
  },
  render() {
    return this.$scopedSlots.default()
  },
}
</script>
