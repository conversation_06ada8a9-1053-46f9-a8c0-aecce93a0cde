<script>
import Bus from '@utils/emitter'
import Constants from '@constants'
import { authComputed } from '@state/modules/auth'

import { fetchNetRoutesApi } from '@modules/settings/monitoring/netroute-settings-api'

export default {
  name: 'NetrouteProvider',
  provide() {
    const netRouteServerContext = {
      add: this.add,
    }
    Object.defineProperty(netRouteServerContext, 'options', {
      enumerable: true,
      get: () => {
        return this.options
      },
    })
    return { netRouteServerContext }
  },
  data() {
    return {
      options: new Map(),
    }
  },
  computed: {
    ...authComputed,
  },
  created() {
    this.fetchNetroutes()
  },
  methods: {
    fetchNetroutes() {
      if (
        !this.hasPermission(this.$constants.NETROUTE_SETTINGS_READ_PERMISSION)
      ) {
        return
      }
      return fetchNetRoutesApi().then((data) => {
        const map = new Map()
        const result = data || []
        result.forEach((item) => {
          map.set(item[Constants.ID_PROPERTY], {
            key: item[Constants.ID_PROPERTY],
            name: item['name'],
            tags: item.tags,
          })
        })
        this.options = map
        this.$emit('fetched', this.options)
        Bus.$emit('provider:fetched')
      })
    },
  },
  render() {
    return this.$scopedSlots.default()
  },
}
</script>
