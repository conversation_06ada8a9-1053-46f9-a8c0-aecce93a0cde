<script>
import Constants from '@constants'

import { fetchExternalStoragesApi } from '@modules/settings/system-settings/external-storage-profile-api'
export default {
  name: 'StorageProfileProvider',
  provide() {
    const storageProfileContext = {
      add: this.add,
    }
    Object.defineProperty(storageProfileContext, 'options', {
      enumerable: true,
      get: () => {
        return this.options
      },
    })
    return { storageProfileContext }
  },
  props: {
    // eslint-disable-next-line
    autoFetch: { type: Boolean, default: true },
    defaultOptions: {
      type: Array,
      default() {
        return []
      },
    },
  },
  data() {
    return {
      options: new Map(),
    }
  },
  created() {
    if (this.autoFetch) {
      this.fetchExternalStorageProfiles()
    } else {
      this.$watch('defaultOptions', (newValue) => {
        this.options = Object.freeze(newValue)
      })
    }
  },
  methods: {
    add(option) {
      const map = new Map(Array.from(this.options))
      map.set(option.key, option)
      this.options = map
    },
    fetchExternalStorageProfiles() {
      fetchExternalStoragesApi().then((data) => {
        const map = new Map()
        const result = data || []
        result.forEach((item) => {
          map.set(item[Constants.ID_PROPERTY], {
            key: item[Constants.ID_PROPERTY],
            name: item['name'],
          })
        })
        this.options = map
        this.$emit('loaded', result)
      })
    },
  },
  render() {
    return this.$scopedSlots.default()
  },
}
</script>
