<script>
import Api from '@api'
import Intersection from 'lodash/intersection'
import Uniq from 'lodash/uniq'
import isEqual from 'lodash/isEqual'
import { WidgetTypeConstants } from '../widgets/constants'
import { AvailableReportCategories } from '@modules/report/helpers/report'

const DataTypeMap = {
  0: 'string',
  1: 'numeric',
  2: 'numeric',
  3: 'any',
}

export default {
  name: 'CounterProvider',
  provide() {
    const counterContext = {
      refresh: this.fetchCounters,
    }
    Object.defineProperty(counterContext, 'options', {
      enumerable: true,
      get: () => {
        return this.options
      },
    })
    Object.defineProperty(counterContext, 'hasLoader', {
      enumerable: true,
      get: () => {
        return this.showLoader
      },
    })
    return { counterContext }
  },

  props: {
    searchParams: {
      type: Object,
      default: undefined,
    },
    widgetCategory: {
      type: String,
      default: undefined,
    },
    fetchFn: {
      type: Function,
      default: undefined,
    },
    dataType: {
      type: Array,
      default: undefined,
    },
    showLoader: {
      type: Boolean,
      default: false,
    },
    shouldIncludeMotadataAgentCounters: {
      type: Boolean,
      default: false,
    },
    reportCategory: {
      type: String,
      default: undefined,
    },
    ignoreShadowCounters: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      options: new Map(),
      loading: true,
    }
  },
  computed: {
    isAvailabilityWidget() {
      return this.searchParams['visualization.group.type'] === 'availability'
    },
  },

  watch: {
    searchParams(newValue, oldValue) {
      if (
        !isEqual(
          JSON.parse(JSON.stringify(newValue || {})),
          JSON.parse(JSON.stringify(oldValue || {}))
        )
      ) {
        this.fetchCounters()
      }
    },
    // dataType(newValue) {
    //   this.buildMap({
    //     result: Array.from(this.options.values()).reduce(
    //       (prev, item) => ({ ...prev, [item.key]: item }),
    //       {}
    //     ),
    //   })
    // },
  },
  created() {
    this.fetchCounters()
  },
  methods: {
    fetchCounters() {
      if (
        [WidgetTypeConstants.ACTIVE_ALERT].includes(this.widgetCategory) ||
        (this.reportCategory &&
          [
            // AvailableReportCategories.INVENTORY,
            // AvailableReportCategories.AVAILABILITY,
            AvailableReportCategories.ACTIVE_ALERTS,
            // AvailableReportCategories.AVAILABILITY_ALERT,
            AvailableReportCategories.METRIC_ALERT,
          ].includes(this.reportCategory)) ||
        ([WidgetTypeConstants.HEATMAP].includes(this.widgetCategory) &&
          this.isAvailabilityWidget)
      ) {
        const map = new Map()
        const counters = [
          'Monitor',
          'Application',
          'Interface',
          'VM',
          'Access point',
          'Process',
          'WAN Link',
          'SDN Tunnel',
          'SDN TLOC',
          'SDN Interface',
          // 'Meraki Interface',
          // 'Containers',
          // 'Link',
          // 'Container',
        ]
        counters.forEach((key) => {
          map.set(key, {
            key:
              key === 'Process'
                ? 'system.process'
                : key === 'WAN Link'
                ? 'ipsla'
                : key === 'SDN Tunnel'
                ? 'cisco.vedge.tunnel'
                : key === 'SDN TLOC'
                ? 'cisco.vedge.tloc'
                : key === 'SDN Interface'
                ? 'cisco.vedge.interface'
                : // : key === 'Meraki Interface'
                  // ? 'cisco.meraki.interface'
                  key.replace(/\^/g, '.').toLowerCase(),
            counterName: key.replace(/[~^]/g, '.'),
            name: key.replace(/[~^]/g, '.'),
            dataType: ['string'],
            metricPlugins: [],

            ...(![
              'Monitor',
              'Application',
              'WAN Link',
              'SDN Tunnel',
              'SDN TLOC',
              'SDN Interface',
            ].includes(key)
              ? {
                  isInstanceLevelCounter: true,
                }
              : {}),
          })
        })
        this.options = map
        this.$emit('counters', this.options)
        this.loading = false
        this.$nextTick(() => {
          this.$emit('loaded', this.options)
        })
        return
      }
      //  else if (
      //   this.reportCategory &&
      //   [AvailableReportCategories.AVAILABILITY_FLAP_SUMMARY].includes(
      //     this.reportCategory
      //   )
      // ) {
      //   const map = new Map()
      //   const counters = [
      //     'Monitor',
      //     'system.process',
      //     'system.service',
      //     'network.service',
      //     'esxi.vm',
      //     'hyperv.vm',
      //     'nutanix.vm',
      //     'aruba.wireless.access.point',
      //     'cisco.wireless.access.point',
      //     'ruckus.wireless.access.point',
      //     'citrix.xen.vm',
      //     'ipsla',
      //     'interface',
      //   ]
      //   counters.forEach((key) => {
      //     map.set(key, {
      //       key: key.replace(/\^/g, '.').toLowerCase(),
      //       counterName: key.replace(/[~^]/g, '.'),
      //       name: key.replace(/[~^]/g, '.'),
      //       dataType: ['string'],
      //       metricPlugins: [],
      //     })
      //   })
      //   this.options = map
      //   this.$emit('counters', this.options)
      //   this.loading = false
      //   this.$nextTick(() => {
      //     this.$emit('loaded', this.options)
      //   })
      //   return
      // }

      if (
        this.searchParams !== undefined &&
        this.searchParams['visualization.group.type'] !== undefined &&
        this.searchParams['visualization.group.type'].indexOf('netroute') === -1
      ) {
        this.loading = true
        Api.get('/misc/column-mappers', {
          params: {
            filter: {
              ...this.searchParams,
            },
          },
        }).then((data) => {
          this.buildMap(data)
        })
      } else {
        this.options = new Map()
      }
    },
    buildMap(data = {}, ignorSetOptions) {
      // const dataType = this.dataType
      let result = data.result
      const groupType = this.searchParams['visualization.group.type']

      if (
        this.reportCategory &&
        [AvailableReportCategories.AVAILABILITY_FLAP_SUMMARY].includes(
          this.reportCategory
        )
      ) {
        this.buildStatusFlapCounters(result)

        return
      }
      if (['flow', 'log'].includes(groupType)) {
        result = {}
        // where log or flow we check counters by 500000 event id for flow

        // result = Object.keys(result).reduce((prev, key) => {
        //   const item = {}
        //   if (groupType === 'flow') {
        //     if (result[key]['mapper.plugin.ids'].includes(500000)) {
        //       item[key] = result[key]
        //     }
        //   } else if (groupType === 'log') {
        //     if (
        //       Intersection(
        //         [490000, 500000, 500001, 500002, 500003, 500004],
        //         result[key]['mapper.plugin.ids']
        //       ).length === 0 ||
        //       result[key]['mapper.plugin.ids'].length > 1
        //     ) {
        //       item[key] = result[key]
        //     }
        //   }
        //   return {
        //     ...prev,
        //     ...item,
        //   }
        // }, {})

        Object.keys(data.result || {}).forEach((key) => {
          if (groupType === 'flow') {
            if (data.result[key]['mapper.plugin.ids'].includes(500000)) {
              result[key] = data.result[key]
            }
          } else if (groupType === 'log') {
            if (
              Intersection(
                [490000, 500000, 500001, 500002, 500003, 500004],
                data.result[key]['mapper.plugin.ids']
              ).length === 0 ||
              data.result[key]['mapper.plugin.ids'].length > 1
            ) {
              result[key] = data.result[key]
            }
          }
        })
      }

      const map = new Map()
      Object.entries(result).map(([key, value]) => {
        if ((value['mapper.data.categories'] || []).length === 0) {
          return
        }

        if (
          this.ignoreShadowCounters &&
          value['mapper.shadow.counter'] === 'yes'
        ) {
          return
        }

        // if (
        //   [WidgetTypeConstants.EVENT_HISTORY].includes(this.widgetCategory) &&
        //   key === 'message'
        // ) {
        //   return
        // }

        // NOTE - remove motadata agent counters
        if (
          /^motadata/i.test(key) &&
          !this.shouldIncludeMotadataAgentCounters
        ) {
          return
        }
        // @TODO remove this corelated metric check later
        if (
          ('correlated.metric' in value &&
            value['correlated.metric'] === 'no') ||
          !('correlated.metric' in value)
        ) {
          // set instanceType on instance it self
          if (value.hasOwnProperty('mapper.instance')) {
            if (map.has(value['mapper.instance'])) {
              const v = map.get(value['mapper.instance'])
              v.instanceType = value['mapper.instance']
              map.set(value['mapper.instance'], v)
            }
          }
          map.set(key, {
            key: key.replace(/\^/g, '.'),
            counterName: key.replace(/[~^]/g, '.'),
            name: key.replace(/[~^]/g, '.'),
            metricPlugins: value['mapper.plugin.ids'],
            ...(value.hasOwnProperty('mapper.event.category')
              ? {
                  eventCategory: value['mapper.event.category'],
                }
              : {}),
            ...(value.hasOwnProperty('mapper.instance')
              ? {
                  instanceType: value['mapper.instance'],
                }
              : {}),
            isStatusCounter: value['mapper.status'] === 'yes',
            dataType: Uniq(
              value['mapper.data.categories'].map(
                (type) => DataTypeMap[type] || 'numeric'
              )
            ),
          })
        }
      })

      if (ignorSetOptions) {
        return map
      }
      this.options = map

      this.$emit('counters', this.options)
      this.loading = false
      this.$nextTick(() => {
        this.$emit('loaded', this.options)
      })
    },
    fetchCountersWithParams(searchParams = {}) {
      return Api.get('/misc/column-mappers', {
        params: {
          filter: {
            ...searchParams,
          },
        },
      }).then((data) => {
        return this.buildMap(data, true)
      })
    },
    buildStatusFlapCounters(result) {
      const map = new Map()
      const counters = Object.keys(result)
      counters.forEach((key) => {
        map.set(key, {
          key: key.replace(/\^/g, '.').toLowerCase(),
          counterName: key.replace(/[~^]/g, '.'),
          name: key.replace(/[~^]/g, '.'),
          dataType: ['string'],
          metricPlugins: [],
        })
      })
      this.options = map
      this.$emit('counters', this.options)
      this.loading = false
      this.$nextTick(() => {
        this.$emit('loaded', this.options)
      })
    },
  },
  render(h) {
    if (this.showLoader) {
      if (this.loading) {
        return h('div', { class: 'flex flex-1 items-center justify-center' }, [
          h('MLoader'),
        ])
      }
    }
    return this.$scopedSlots.default({ loading: this.loading })
  },
}
</script>
