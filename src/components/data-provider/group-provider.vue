<script>
import Api from '@api'
import { transformGroupRecursive } from '@modules/settings/group-settings/helpers/group'
import Bus from '@utils/emitter'

export default {
  name: 'GroupProvider',
  provide() {
    const groupContext = {
      refresh: this.fetchGroups,
    }
    Object.defineProperty(groupContext, 'options', {
      enumerable: true,
      get: () => {
        return this.options
      },
    })
    return { groupContext }
  },
  props: { maxLevel: { type: Number, default: 0 } },
  data() {
    return {
      options: new Map(),
      loading: true,
    }
  },
  created() {
    this.fetchGroups()
  },
  methods: {
    add(group) {
      const map = new Map(Array.from(this.options))
      map.set(group.key, group)
      this.options = map
    },
    fetchGroups() {
      this.loading = true
      Api.get('/settings/groups').then((data) => {
        const flatOptions = transformGroupRecursive(data.result, this.maxLevel)
        const map = new Map()
        flatOptions.forEach((option) => {
          map.set(option.key, option)
        })
        this.options = map
        this.loading = false
        this.$emit('data-fetched')
        Bus.$emit('provider:fetched')
      })
    },
  },
  render() {
    return this.$scopedSlots.default({
      loading: this.loading,
    })
  },
}
</script>
