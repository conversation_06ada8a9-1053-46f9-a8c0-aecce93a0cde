<script>
export default {
  name: 'AddiitionalColumnProvider',
  provide() {
    const additionalColumnsContext = {
      refresh: this.fetchGroups,
    }
    Object.defineProperty(additionalColumnsContext, 'options', {
      enumerable: true,
      get: () => {
        return this.options
      },
    })
    return { additionalColumnsContext }
  },
  props: {
    searchParams: {
      type: Object,
      default: undefined,
    },
    reportCategory: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      options: new Map(),
      loading: true,
    }
  },
  computed: {
    counterKey() {
      return this.searchParams?.counter?.key
    },
  },
  watch: {
    counterKey() {
      this.fetchColumns()
    },
  },
  created() {
    this.fetchColumns()
  },
  methods: {
    fetchColumns() {
      const map = new Map()

      let flatOptions = []

      if (this.counterKey) {
        if (this.counterKey === 'monitor') {
          flatOptions = [{ key: 'monitor', name: 'Monitor' }]
        } else if (this.counterKey === 'interface') {
          flatOptions = [
            { key: 'monitor', name: 'Monitor' },
            { key: 'monitor1', name: 'Monitor1' },
          ]
        } else if (this.counterKey === 'vm') {
          flatOptions = [
            { key: 'monitor', name: 'Monitor' },
            { key: 'monitor2', name: 'Monitor2' },
          ]
        } else if (this.counterKey === 'access point') {
          flatOptions = [
            { key: 'monitor', name: 'Monitor' },
            { key: 'monitor3', name: 'Monitor3' },
          ]
        } else if (this.counterKey === 'process') {
          flatOptions = [
            { key: 'monitor', name: 'Monitor' },
            { key: 'monitor4', name: 'Monitor4' },
          ]
        }
      }
      flatOptions.forEach((option) => {
        map.set(option.key, option)
      })
      this.options = map
      this.loading = false
      this.$emit('data-fetched')
    },
  },
  render() {
    return this.$scopedSlots.default({
      loading: this.loading,
    })
  },
}
</script>
