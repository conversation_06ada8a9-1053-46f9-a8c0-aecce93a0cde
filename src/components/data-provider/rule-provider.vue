<script>
import Bus from '@utils/emitter'
import Constants from '@constants'

import { fetchRulesApi } from '@modules/settings/compliance-settings/rules-api'

export default {
  name: 'RuleProvider',
  provide() {
    const ruleContext = {}
    Object.defineProperty(ruleContext, 'options', {
      enumerable: true,
      get: () => {
        return this.options
      },
    })
    return { ruleContext }
  },
  data() {
    return {
      options: new Map(),
    }
  },
  created() {
    this.fetchRules()
  },
  methods: {
    fetchRules() {
      return fetchRulesApi().then((data) => {
        const map = new Map()
        const result = data || []
        result.forEach((item) => {
          map.set(item[Constants.ID_PROPERTY], {
            key: item[Constants.ID_PROPERTY],
            name: item['name'],
            ...item,
          })
        })
        this.options = map
        Bus.$emit('provider:fetched')
      })
    },
  },
  render() {
    return this.$scopedSlots.default()
  },
}
</script>
