<script>
import Api from '@api'
import { transformRemotePollerForDropdown } from '@modules/settings/system-settings/helpers/remote-poller'
import Bus from '@utils/emitter'

export default {
  name: 'RpeProvider',
  provide() {
    const rpeContext = {}
    Object.defineProperty(rpeContext, 'options', {
      enumerable: true,
      get: () => {
        return this.options
      },
    })
    return { rpeContext }
  },
  props: {
    remoteEventTypeFilter: {
      type: [String, Array],
      default: undefined,
    },
  },
  data() {
    return {
      options: new Map(),
    }
  },
  created() {
    this.fetchRemotePollers()
  },
  methods: {
    fetchRemotePollers() {
      Api.get('/settings/remote-event-processors').then((data) => {
        let result = (data.result || []).map(transformRemotePollerForDropdown)

        const filterArray = Array.isArray(this.remoteEventTypeFilter)
          ? this.remoteEventTypeFilter
          : [this.remoteEventTypeFilter]
        const map = new Map()
        result.forEach((result) => {
          if (
            this.remoteEventTypeFilter &&
            !filterArray
              .map((i) => i.toLowerCase())
              .includes((result.rpeType || '').toLowerCase())
          ) {
            return
          }

          if (
            this.remoteEventTypeFilter &&
            (result.rpeType || '').toLowerCase() === 'app' &&
            (result.mode || '').toLowerCase() === 'failover'
          ) {
            return
          }

          map.set(result.key, result)
        })
        this.options = map
        Bus.$emit('provider:fetched')
      })
    },
  },
  render() {
    return this.$scopedSlots.default()
  },
}
</script>
