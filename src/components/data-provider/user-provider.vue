<script>
import Constants from '@constants'
import { buildAvatarUrl } from '@data/avatar'
import { fetchUsersApi } from '@modules/settings/users-settings/users-api'

export default {
  name: 'UserProvider',
  provide() {
    const UserProviderContext = {}
    Object.defineProperty(UserProviderContext, 'options', {
      enumerable: true,
      get: () => {
        return this.options
      },
    })
    return { UserProviderContext }
  },
  props: {
    transformDataForPicker: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      options: [],
    }
  },
  created() {
    this.fetchUsers()
  },
  methods: {
    fetchUsers() {
      fetchUsersApi((user) => ({
        id: user[Constants.ID_PROPERTY],
        name: `${user['user.first.name'] || ''} ${
          user['user.last.name'] || ''
        }`,
        userName: user['user.name'],
        avatarUrl: user['user.avatar']
          ? buildAvatarUrl(user['user.avatar'])
          : '/default-user.png',

        ...(this.transformDataForPicker
          ? {
              key: user[Constants.ID_PROPERTY],
              name: user['user.name'],
              type: user._type,
              groups: user['user.groups'],
              userType: user['user.type'],
              role: user['user.role'],
            }
          : {}),
      })).then((data) => {
        this.options = Object.freeze(data)
      })
    },
  },
  render() {
    return this.$scopedSlots.default()
  },
}
</script>
