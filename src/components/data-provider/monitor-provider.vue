<script>
import Bus from '@utils/emitter'
import { objectDBWorker } from '@/src/workers'
import { getMonitorsApi } from '@modules/settings/monitoring/monitors-api'
import Constants from '@constants/index'

export default {
  name: 'MonitorProvider',
  provide() {
    const monitorContext = {}
    Object.defineProperty(monitorContext, 'options', {
      enumerable: true,
      get: () => {
        return this.showArchivedObjectes
          ? this.archivedObjectesMap
          : this.options
      },
    })
    return { monitorContext }
  },
  props: {
    searchParams: {
      type: Object,
      default: undefined,
    },
    fetchFn: {
      type: Function,
      default: undefined,
    },
    useIp: {
      type: Boolean,
      default: false,
    },
    useApi: {
      type: Boolean,
      default: false,
    },
    excludedIds: {
      type: Array,
      default: undefined,
    },
    excludedTypes: {
      type: Array,
      default: undefined,
    },
    fetchArchivedObjectes: {
      type: Boolean,
      default: false,
    },
    showArchivedMonitors: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      options: new Map(),
      archivedObjectesMap: new Map(),
    }
  },
  watch: {
    searchParams() {
      this.fetchMonitors()
    },
    excludedId() {
      this.fetchMonitors()
    },
    showArchivedMonitors() {
      this.fetchMonitors()
    },
  },

  created() {
    this.fetchMonitors()

    if (!this.useIp) {
      Bus.$on(this.$constants.EVENT_MONITOR_DB_CHANGED, this.fetchMonitors)
      this.$once('hook:beforeDestroy', () => {
        Bus.$off(this.$constants.EVENT_MONITOR_DB_CHANGED, this.fetchMonitors)
      })
    }
  },

  methods: {
    fetchMonitors() {
      if (!this.searchParams) {
        return
      }

      const p = this.useApi
        ? this.fetchFn
          ? this.fetchFn(this.searchParams)
          : getMonitorsApi(
              this.searchParams ? { params: this.searchParams } : {},
              true,
              true
            )
        : objectDBWorker.getObjects(
            this.searchParams,
            false,
            this.showArchivedMonitors
          )
      const useIp = this.useIp
      p.then((data) => {
        const map = new Map()
        const result = data || []
        const excludedId = this.excludedIds
        const excludedTypes = this.excludedTypes
        result.forEach((i) => {
          if (excludedId && excludedId.includes(useIp ? i.ip : i.id)) {
            return true
          }
          if (excludedTypes && excludedTypes.includes(i.type)) {
            return true
          }
          map.set(i.id, {
            key: useIp ? i.ip : i.id,
            id: useIp ? i.ip : i.id,
            text: useIp ? i.ip : i.name,
            name: useIp ? i.ip : i.name,
            type: i.type,
            ip: i.ip,
            category: i.category,
            target: i.target,
            groups: i.groups,
            severity: i.severity,
            isAgent: i.isAgent ? Constants.AGENT : undefined,
          })
        })
        this.options = map
      })
    },
  },
  render() {
    return this.$scopedSlots.default()
  },
}
</script>
