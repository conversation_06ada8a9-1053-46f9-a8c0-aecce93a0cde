<script>
import Constants from '@constants'
import Bus from '@utils/emitter'

import {
  getAllPoliciesApi,
  getAllLogPoliciesApi,
  getAllFlowPoliciesApi,
  getAllTrapPoliciesApi,
  getAllNetroutePoliciesApi,
} from '@modules/settings/policy-settings/policy-api.js'

export default {
  name: 'PolicyProvider',
  provide() {
    const policyContext = {}
    Object.defineProperty(policyContext, 'options', {
      enumerable: true,
      get: () => {
        return this.options
      },
    })
    return { policyContext }
  },
  props: {
    type: {
      type: String,
      default: 'metric',
    },
  },
  data() {
    return {
      options: new Map(),
    }
  },
  watch: {
    type(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.fetchPolicies()
      }
    },
  },
  created() {
    this.fetchPolicies()
  },
  methods: {
    async fetchPolicies() {
      const fn =
        (this.type || '').toLowerCase() === 'metric'
          ? getAllPoliciesApi()
          : (this.type || '').toLowerCase() === 'log'
          ? getAllLogPoliciesApi()
          : (this.type || '').toLowerCase() === 'flow'
          ? getAllFlowPoliciesApi()
          : (this.type || '').toLowerCase() === 'netroute'
          ? getAllNetroutePoliciesApi()
          : getAllTrapPoliciesApi()
      await fn.then((data) => {
        const map = new Map()
        const result = data || []
        result.forEach((item) => {
          map.set(item[Constants.ID_PROPERTY], {
            key: item[Constants.ID_PROPERTY],
            name: item['name'],
            tags: item['tag'] || [],
            metric: item['metric'],
          })
        })
        this.options = map
        Bus.$emit('provider:fetched')
      })
    },
  },
  render() {
    return this.$scopedSlots.default()
  },
}
</script>
