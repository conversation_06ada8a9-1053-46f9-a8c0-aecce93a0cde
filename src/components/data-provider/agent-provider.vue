<script>
import api from '@api'
import Bus from '@utils/emitter'
import Constants from '@constants'

export default {
  name: 'AgentProvider',
  provide() {
    const agentContext = {}
    Object.defineProperty(agentContext, 'options', {
      enumerable: true,
      get: () => {
        return this.options
      },
    })
    return { agentContext }
  },
  props: {
    allowedTypes: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      options: new Map(),
    }
  },
  created() {
    this.fetchAgents()
  },
  methods: {
    fetchAgents() {
      api.get(`/settings/agents`).then((data) => {
        const map = new Map()
        const result = data.result || []
        result.forEach((i) => {
          if (this.allowedTypes.length > 0) {
            if (this.allowedTypes.includes(i['object.type'])) {
              map.set(i[Constants.ID_PROPERTY], {
                key: i[Constants.ID_PROPERTY],
                name: i['object.name'],
                state: i['object.state'],
                type: i['object.type'],
              })
            }
          } else {
            map.set(i[Constants.ID_PROPERTY], {
              key: i[Constants.ID_PROPERTY],
              name: i['object.name'],
              state: i['object.state'],
            })
          }
        })
        this.options = map
        Bus.$emit('provider:fetched')
      })
    },
  },
  render() {
    return this.$scopedSlots.default()
  },
}
</script>
