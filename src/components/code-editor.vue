<template>
  <codemirror
    :value="innerValue"
    class="plugin-code-editor"
    :options="options"
    @input="innerValue = $event"
  />
</template>

<script>
import { codemirror } from 'vue-codemirror'
import 'codemirror/addon/display/placeholder'
import 'codemirror/mode/python/python.js'
import 'codemirror/mode/shell/shell.js'
import 'codemirror/mode/powershell/powershell.js'
import 'codemirror/mode/sql/sql.js'
import 'codemirror/mode/javascript/javascript.js'
import 'codemirror/mode/clike/clike.js'
import 'codemirror/mode/go/go.js'
import { UserPreferenceComputed } from '@state/modules/user-preference'

export default {
  name: 'CodeEditor',
  components: { codemirror },
  model: { event: 'change' },
  props: {
    value: { type: String, default: undefined },
    mode: { type: String, default: 'text/x-python' },
    disabled: { type: Boolean, default: false },
  },
  computed: {
    ...UserPreferenceComputed,
    options() {
      return {
        mode: this.mode,
        styleActiveLine: true,
        readOnly: this.disabled ? 'nocursor' : false,
        lineNumbers: true,
        line: true,
        foldGutter: true,
        matchBrackets: true,
        ...(this.theme === 'black' ? { theme: 'monokai' } : {}),
      }
    },
    innerValue: {
      get() {
        return this.value || ''
      },
      set(value) {
        this.$emit('change', value)
      },
    },
  },
}
</script>

<style lang="less">
@import '~codemirror/lib/codemirror.css';
@import '~codemirror/theme/monokai.css';

// stylelint-disable-next-line
.CodeMirror {
  font-family: 'IAWriter Duospace', sans-serif;
}

.plugin-code-editor {
  // stylelint-disable-next-line
  .CodeMirror {
    height: 450px;
    font-size: @text-regular;
    color: var(--page-text-color);
    background: var(--page-background-color);
    border: 1px solid var(--border-color);

    &-placeholder {
      font-size: @text-sm !important;
      color: var(--input-placeholder-color) !important;
    }

    &-gutters {
      background: var(--page-background-color);
      border-color: var(--border-color);
    }

    &-cursor {
      border-color: var(--page-text-color);
    }

    &-scroll {
      margin-right: 0;
    }

    &-scrollbar-filler {
      background: transparent;
    }
  }

  &.command-line-args {
    // stylelint-disable-next-line
    .CodeMirror {
      height: 150px;
    }
  }
}
</style>
