<template>
  <MValidationProvider
    ref="provider"
    slim
    :rules="rules"
    :name="validationLabel || label"
    :debounce="200"
    :mode="mode"
    :immediate="immediate"
    :vid="vid"
  >
    <div
      slot-scope="{ errors, invalid, valid, validated, pending, ...slotData }"
      :class="{
        'form-item-pristine': slotData.pristine,
        'has-required-validation':
          validated && !pending && hasRequiredValidation(slotData.failedRules),
        'has-info-tooltip': infoTooltip,
      }"
    >
      <MFormItem
        :error="
          slotData.pristine
            ? help || undefined
            : invalid && !hideErrorMessage
            ? errors[0]
            : help || ''
        "
        :required="required"
        :validate-status="
          validated && !pending ? (valid ? 'success' : 'error') : ''
        "
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
      >
        <template v-if="label" v-slot:label>
          <div class="inline-flex items-center min-w-0">
            {{ label }}
            <MTooltip v-if="infoTooltip">
              <template v-slot:trigger>
                <MIcon
                  name="info-circle"
                  class="ml-1 text-primary-alt text-sm"
                  style="position: relative; top: -1px"
                  :style="required ? { right: '-16px' } : {}"
                />
              </template>
              <div v-html="infoTooltip" />
            </MTooltip>
          </div>
        </template>
        <slot v-bind="slotData">
          <slot name="before-input" />
          <MInput
            ref="input"
            v-model="inputValue"
            v-bind="$attrs"
            :type="inputType || type"
            @blur="$emit('blur', $event)"
            v-on="listeners"
          >
            <template
              v-for="(_, name) in $scopedSlots"
              v-slot:[name]="nestedSlot"
            >
              <slot :name="name" v-bind="nestedSlot" />
            </template>
            <slot name="input-children" />
          </MInput>
        </slot>
      </MFormItem>
    </div>
  </MValidationProvider>
</template>

<script>
export default {
  name: 'FlotoFormItem',
  model: {
    event: 'update',
  },
  props: {
    help: { type: String, default: undefined },
    infoTooltip: { type: String, default: undefined },
    value: { type: [Array, Object, String, Number], default: undefined },
    rules: { type: [Object, String], default: '' },
    label: { type: String, required: false, default: '' },
    validationLabel: { type: String, default: undefined },
    vid: { type: String, default: undefined },
    mode: { type: String, default: 'eager' },
    immediate: { type: Boolean, default: false },
    inputType: { type: String, default: undefined },
    type: { type: String, default: undefined },
    hideErrorMessage: { type: Boolean, default: false },
    wrapperCol: {
      type: Object,
      default() {
        return { xxl: 10, xl: 9, lg: 8 }
      },
    },
    labelCol: {
      type: Object,
      default() {
        return { xxl: 2, xl: 3, lg: 4 }
      },
    },
  },
  computed: {
    required() {
      const rules = this.rules
      if (typeof rules === 'string') {
        return /required(?!_if)/.test(rules)
      }
      if (rules.required) {
        return true
      }
      return false
    },
    listeners() {
      const { update, input, change, blur, ...listeners } = this.$listeners
      return listeners
    },
    inputValue: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('update', value)
      },
    },
  },
  methods: {
    hasRequiredValidation(failedRules) {
      return Object.keys(failedRules || {}).indexOf('required') >= 0
    },
    setValue(value) {
      this.inputValue = value
    },
    addError(error) {
      this.$refs.provider.setErrors(Array.isArray(error) ? error : [error])
    },
    focus() {
      if (this.$refs.input) {
        this.$refs.input.focus()
      }
    },
    validate() {
      return this.$refs.provider.validate()
    },
  },
}
</script>
