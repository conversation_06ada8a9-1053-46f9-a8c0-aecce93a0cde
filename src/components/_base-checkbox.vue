<template functional>
  <label
    class="ant-checkbox-wrapper"
    :class="{
      'ant-checkbox-wrapper-checked': props.checked,
      'ant-checkbox-wrapper-disabled': props.disabled,
      'ant-checkbox-indeterminate': props.indeterminate,
    }"
    v-on="listeners.click ? { click: listeners.click } : {}"
  >
    <span
      class="ant-checkbox"
      :class="{
        'ant-checkbox-checked': props.checked,
        'ant-checkbox-disabled': props.disabled,
      }"
    >
      <input
        type="checkbox"
        :disabled="props.disabled"
        class="ant-checkbox-input"
        @change="
          listeners.change ? listeners.change($event.target.checked) : undefined
        "
      />
      <span class="ant-checkbox-inner" />
    </span>
    <span v-if="children">
      <slot />
    </span>
  </label>
</template>

<script>
export default {
  name: 'MCheckbox',
  model: {
    prop: 'checked',
    event: 'change',
  },
  props: {
    value: {
      type: [<PERSON>olean, String],
      default: undefined,
    },
    checked: {
      type: <PERSON><PERSON><PERSON>,
      default: false,
    },
    disabled: {
      type: <PERSON><PERSON><PERSON>,
      default: false,
    },
    indeterminate: {
      type: <PERSON><PERSON>an,
      default: false,
    },
  },
}
</script>
