<script>
import Omit from 'lodash/omit'
import CloneDeep from 'lodash/cloneDeep'

const DEFAULT_EXCLUDED_EVENTS = [
  // 'ui.notification.task.manager',
  // 'ui.event.log.statistics',
  // 'ui.event.metric.poll.progress',
  // 'ui.notification.agent',
]

export default {
  name: 'SocketContext',
  provide() {
    const SocketContext = {
      addExcludedEvents: this.addExcludedEvents,
      removeExcludedEvents: this.removeExcludedEvents,
      resetExcludedEvents: this.resetExcludedEvents,
      setConnected: this.setConnected,
      addGuidForEvent: this.addGuidForEvent,
      removeGuidForEvent: this.removeGuidForEvent,
    }
    Object.defineProperty(SocketContext, 'excludedEvents', {
      enumerable: true,
      get: () => {
        return this.excludedEvents
      },
    })
    Object.defineProperty(SocketContext, 'connected', {
      enumerable: true,
      get: () => {
        return this.isConnected
      },
    })
    Object.defineProperty(SocketContext, 'guidMap', {
      enumerable: true,
      get: () => {
        return this.guidMap
      },
    })
    return { SocketContext }
  },
  data() {
    return {
      excludedEvents: CloneDeep(DEFAULT_EXCLUDED_EVENTS),
      isConnected: undefined,
      guidMap: {},
    }
  },
  methods: {
    addGuidForEvent(event, guid) {
      const guidMap = this.guidMap
      const map = { ...guidMap, [event]: [...(guidMap[event] || []), guid] }
      this.guidMap = Object.freeze(map)
    },
    removeGuidForEvent(event, guid) {
      const guidMap = this.guidMap
      const updatedGuids = (guidMap[event] || []).filter((i) => i !== guid)
      const map =
        updatedGuids.length > 0
          ? {
              ...guidMap,
              [event]: updatedGuids,
            }
          : Omit(guidMap, [event])
      this.guidMap = Object.freeze(map)
    },
    setConnected(value) {
      this.isConnected = value
    },
    addExcludedEvents(event) {
      this.excludedEvents = [...this.excludedEvents, event]
    },
    removeExcludedEvents(event) {
      this.excludedEvents = this.excludedEvents.filter((i) => i !== event)
    },
    resetExcludedEvents() {
      this.excludedEvents = CloneDeep(DEFAULT_EXCLUDED_EVENTS)
    },
  },
  render() {
    return this.$scopedSlots.default && this.$scopedSlots.default()
  },
}
</script>
