<template>
  <FlotoContentLoader :loading="loading">
    <div
      class="flex flex-col min-w-0 w-full min-h-0 pr-0"
      :class="{ 'pl-2': usePadding }"
      style="flex: 1"
    >
      <div class="mt-4 flex flex-1 flex-col min-h-0 flex-col mar-t-spacer-s">
        <div class="slide-toggle">
          <slot
            name="add-controls"
            :filter="filterResults"
            :searchTerm="searchTerm"
            :editingItem="editingItem"
            :resetFilter="resetFilter"
            :create="() => showForm()"
            :items="items"
          />
        </div>
        <MValidationObserver slim>
          <template v-slot="{ validate }">
            <MGrid
              ref="gridRef"
              :data="items"
              :search-term="searchTerm"
              :columns="gridColumns"
              v-bind="$attrs"
              v-on="$listeners"
            >
              <template
                v-for="(_, name) in gridSlots"
                v-slot:[name]="nestedSlot"
              >
                <slot
                  :name="name"
                  v-bind="nestedSlot"
                  :edit="() => showForm(nestedSlot.item)"
                  :activateItem="(item) => activateItem(item)"
                  :editingItem="editingItem"
                  :cancelEdit="hideForm"
                  :update="
                    (payload) => {
                      if (inlineEditing) {
                        validate().then((validationResponse) => {
                          if (validationResponse) {
                            updateItem(payload)
                          }
                        })
                      } else {
                        updateItem(payload)
                      }
                    }
                  "
                />
              </template>
            </MGrid>
          </template>
        </MValidationObserver>
      </div>
      <!-- default form as drawer -->
      <FlotoDrawerForm
        v-if="!inlineEditing"
        ref="drawerFormRef"
        :width="formWidth"
        :open="editingItem !== null"
        :default-item="defaultItem"
        v-bind="drawerProps"
        @cancel="editingItem = null"
        @submit="handleFormSubmit(editingItem)"
      >
        <template v-if="Boolean(editingItem)" v-slot:header>
          <slot name="form-header" :item="editingItem"></slot>
        </template>
        <slot
          v-if="Boolean(editingItem)"
          name="form-items"
          :item="editingItem"
          :resetForm="resetForm"
        ></slot>
        <template
          v-if="Boolean(editingItem)"
          v-slot:actions="{ hide, submit, ...formSlotData }"
        >
          <slot
            name="form-actions"
            :processing="processing"
            :resetForm="(payload) => resetForm(payload, formSlotData.reset)"
            :item="editingItem"
            :updateItem="(payload) => resetForm({ ...editingItem, ...payload })"
            :cancel="hide"
            :submit="submit"
            v-bind="formSlotData"
          />
        </template>
      </FlotoDrawerForm>
      <!-- Delete Confirm Modal -->
      <FlotoConfirmModal
        v-if="showConfirmDeleteModalFor !== null"
        open
        no-icon-shadow
        @confirm="handleDeleteConfirmation"
        @hide="showConfirmDeleteModalFor = null"
      >
        <template v-slot:icon>
          <slot name="confirm-delete-icon">
            <MIcon name="trash-alt" size="2x" class="text-secondary-red" />
          </slot>
        </template>
        <template v-slot:message>
          <slot name="confirm-delete-message" :item="showConfirmDeleteModalFor">
            {{
              $message('confirm', {
                message: $message('delete_resource', {
                  resource: (resourceName || '').toLowerCase(),
                }),
              })
            }}?
          </slot>
        </template>
      </FlotoConfirmModal>
      <!-- Delete Failed Modal -->
      <LinkedRecordsDetailModal
        v-if="isLinkedRecordsDetailModalVisible"
        :data="linkedRecordsDetailData"
        @hide="isLinkedRecordsDetailModalVisible = false"
      >
        <template v-slot:title>
          <div class="flex items-center">
            <div class="flex-1">
              <slot name="title">
                <h4 id="header-id" class="mb-0 text-secondary-red">
                  Usage Details
                </h4>
              </slot>
            </div>
            <MButton
              id="close-used-count"
              variant="transparent"
              :shadow="false"
              shape="circle"
              @click="isLinkedRecordsDetailModalVisible = false"
            >
              <MIcon name="times" class="text-neutral-light" />
            </MButton>
          </div>
        </template>
      </LinkedRecordsDetailModal>

      <!-- default slot -->
      <slot
        :activateItem="activateItem"
        :activeItem="activeItem"
        :editingItem="editingItem"
        :resetActiveItem="resetActiveItem"
      >
      </slot>
    </div>
  </FlotoContentLoader>
</template>

<script>
import Bus from '@utils/emitter'
import Find from 'lodash/find'
import Omit from 'lodash/omit'
import { generateId } from '@utils/id'
import { arrayWorker } from '@/src/workers'
import CloneDeep from 'lodash/cloneDeep'
import { UserPreferenceComputed } from '@state/modules/user-preference'
import LinkedRecordsDetailModal from './linked-records-detail-modal.vue'

export default {
  name: 'FlotoPaginatedCrud',
  components: {
    LinkedRecordsDetailModal,
  },
  provide() {
    return {
      triggerDeleteConfirmModal: this.triggerConfirmDeleteModal,
    }
  },
  props: {
    fetchFn: { type: Function, default: undefined },
    createFn: { type: Function, default: undefined },
    deleteFn: { type: Function, default: undefined },
    updateFn: { type: Function, default: undefined },
    inlineEditing: { type: Boolean, default: false },
    defaultItem: {
      type: Object,
      default: undefined,
    },
    formWidth: { type: [Number, String], default: '40%' },
    resourceName: { type: String, default: undefined },
    getEditItem: { type: Function, default: undefined },
    // eslint-disable-next-line
    usePadding: { type: Boolean, default: true },
    columns: {
      type: Array,
      default() {
        return []
      },
    },
    drawerProps: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  data() {
    return {
      loading: true,
      items: [],
      searchTerm: undefined,
      processing: false,
      editingItem: null,
      showConfirmDeleteModalFor: null,
      activeItem: null,
      error: null,
      isLinkedRecordsDetailModalVisible: false,
      linkedRecordsDetailData: undefined,
    }
  },
  computed: {
    ...UserPreferenceComputed,
    gridColumns() {
      const slotKeys = Object.keys(this.$scopedSlots)
      return this.columns.map((c) => ({
        ...c,
        ...(slotKeys.indexOf(c.key) >= 0 ? { cellRender: c.key } : {}),
      }))
    },
    gridSlots() {
      return Omit(this.$scopedSlots, ['add-controls'])
    },
  },
  created() {
    this.take = this.pageSize
    this.fetchData()
    Bus.$on('setup:hide-form-drawer', this.hideForm)
  },
  beforeDestroy() {
    Bus.$off('setup:hide-form-drawer', this.hideForm)
    this.activeItem = null
    this.hideForm()
  },
  methods: {
    async getFilteredData() {
      if (this.$refs.gridRef) {
        let filteredData = await this.$refs.gridRef.getCurrentData()
        return filteredData
      }
      return []
    },
    getContextData() {
      if (this.$refs.gridRef) {
        return this.$refs.gridRef.getContextData()
      }
      return {}
    },
    getData() {
      return this.items
    },
    refresh() {
      if (this.$refs.gridRef) {
        this.$refs.gridRef.resetSkip()
      }
      this.fetchData()
    },
    filterResults(searchTerm) {
      this.searchTerm = searchTerm
    },
    resetFilter() {
      this.searchTerm = undefined
    },
    getItem(id) {
      return arrayWorker.findById(this.items, id)
    },
    getItemSync(id) {
      return Find(this.items, { id })
    },
    getItems(ids) {
      return arrayWorker.getItemsByIds(this.items, ids)
    },
    activateItem(item) {
      this.activeItem = item
    },
    resetActiveItem() {
      this.activeItem = null
    },
    updateItem(item) {
      this.handleFormSubmit(item)
    },
    setItems(items) {
      if (Array.isArray(items)) {
        this.items = Object.freeze(items)
      }
    },
    handleAddNewItem(item) {
      this.items = Object.freeze([...this.items, item])
    },
    async handleUpdateItem(item) {
      const itemIndex = await arrayWorker.findIndex(this.items, { id: item.id })
      if (itemIndex >= 0) {
        this.items = Object.freeze([
          ...this.items.slice(0, itemIndex),
          item,
          ...this.items.slice(itemIndex + 1),
        ])
      } else {
        this.items = Object.freeze([...this.items, item])
      }
    },
    async replaceItem(item, oldItem) {
      const index = await arrayWorker.findIndex(this.items, {
        id: oldItem ? oldItem.id : item.id,
      })
      if (index !== -1) {
        this.items = Object.freeze([
          ...this.items.slice(0, index),
          item,
          ...this.items.slice(index + 1),
        ])
      }
    },
    handleFormSubmit(updatedItem) {
      this.processing = true
      if (updatedItem.id && `${updatedItem.id}`.indexOf('-') === -1) {
        return this.updateFn(updatedItem)
          .then(async (data) => {
            this.hideForm()
            await this.handleUpdateItem(data)
            return data
          })
          .finally(() => (this.processing = false))
      } else {
        return this.createFn(updatedItem)
          .then(async (data) => {
            if (
              this.inlineEditing &&
              `${this.editingItem.id}`.indexOf('-') !== -1
            ) {
              await this.replaceItem(data, this.editingItem)
            } else {
              await this.handleUpdateItem(data)
            }
            this.hideForm()
            return data
          })
          .finally(() => (this.processing = false))
      }
    },
    async removeItem(item) {
      const items = await arrayWorker.excludeFromList(this.items, [item.id])
      this.items = Object.freeze(items)
    },
    showForm(item) {
      const currentEditingItem = this.editingItem
      if (currentEditingItem && `${currentEditingItem.id}`.indexOf('-') >= 0) {
        this.removeItem(currentEditingItem)
      }
      if (item && this.getEditItem) {
        this.getEditItem(item).then((data) => {
          this.editingItem = CloneDeep({ ...item, ...data })
        })
      } else {
        const defaultItem = this.defaultItem
        this.editingItem = CloneDeep({
          ...(this.inlineEditing ? { id: generateId() } : {}),
          ...(item || defaultItem || {}),
        })
      }

      if (this.inlineEditing && !item) {
        this.$refs.gridRef.resetSkip()
        this.items = Object.freeze([CloneDeep(this.editingItem), ...this.items])
      }
    },
    hideForm() {
      if (
        this.inlineEditing &&
        this.editingItem &&
        `${this.editingItem.id}`.indexOf('-') !== -1
      ) {
        this.removeItem(this.editingItem)
      }
      this.editingItem = null
    },
    setSelection(ids) {
      this.$refs.gridRef.setSelection(ids)
    },
    resetList() {
      this.$refs.gridRef.resetSelection()
      this.$refs.gridRef.resetSkip()
      this.fetchData()
    },
    fetchData() {
      return this.fetchFn().then((response) => {
        this.items = Object.freeze(response)
        this.loading = false
      })
    },
    handleDeleteConfirmation() {
      const deletingResource = this.showConfirmDeleteModalFor
      this.deleteItem(deletingResource).then(() => {
        this.showConfirmDeleteModalFor = null
      })
    },
    triggerConfirmDeleteModal(item) {
      this.showConfirmDeleteModalFor = item
    },
    deleteItem(item) {
      if (this.deleteFn) {
        return this.deleteFn(item)
          .then(async (data) => {
            await this.removeItem(item)
            if (this.items.length === 0) {
              this.$emit('empty-list')
            }
            return data
          })
          .catch((e) => {
            this.linkedRecordsDetailData = e.response.data
            this.isLinkedRecordsDetailModalVisible = e.response.status === 400
            throw e
          })
      } else {
        throw new Error('delete function is missing')
      }
    },
    async resetForm(item, resetFn) {
      let plainItem
      if (item) {
        plainItem = item
      } else {
        plainItem = await arrayWorker.findById(this.items, this.editingItem.id)
      }
      if ('toJSON' in plainItem) {
        plainItem = item.toJSON()
      }
      this.editingItem = { ...this.defaultItem, ...plainItem }
      if (resetFn) {
        resetFn()
      }
    },
    validateDrawerForm() {
      return this.$refs.drawerFormRef.validate()
    },
  },
}
</script>
