<template>
  <div class="flex flex-col min-h-0 flex-1">
    <div v-if="selectedIds.length && !hideSelectionInfo" class="mt-3">
      <MTag
        variant="default"
        rounded
        @close="onHeaderSelectionChange(false, 'all')"
      >
        <span class="font-500"> {{ selectedIds.length }} items selected </span>
      </MTag>
    </div>
    <div class="flex flex-col min-h-0 flex-1">
      <Transition name="placeholder">
        <FlotoContentLoader v-if="processingData" loading />
        <Grid
          v-else
          :key="key"
          :style="
            hasTopMargin ? { height: 'calc(100% - 15px)' } : { height: '100%' }
          "
          :data-items="dataItems"
          data-item-key="id"
          :columns="currentColumns"
          :total="totalCount || dataItems.total"
          :row-height="paging ? undefined : rowHeight"
          :skip="externalSkip !== undefined ? externalSkip : skip"
          :take="externalTake !== undefined ? externalTake : take"
          :resizable="true"
          :reorderable="true"
          :sortable="sortable"
          :sort="sort"
          :pageable="paging ? pageable : undefined"
          :group="defaultGroup ? group : undefined"
          v-bind="gridAttrs"
          @sortchange="handleSortChange"
          @columnreorder="handleColumnReorder"
          @columnresize="handleColumnResize"
          @datastatechange="handleDataStateChange"
          v-on="gridListeners"
        >
          <template v-slot:selectAllCheckbox>
            <template v-if="maxAllowedSelection === 0 && currentPageIds.length">
              <SelectAll
                :max-allowed-selection="maxAllowedSelection"
                :selected-items="selectedIds"
                :items="currentPageIds"
                :toggle-select-all="onHeaderSelectionChange"
                :page-info="pageInfo"
                :resource-name="resourceName"
              />
            </template>
            <span v-else />
          </template>
          <template v-slot:selectionCheckbox="{ props }">
            <CustomCell
              :field="props.field"
              :expanded="props.expanded"
              :row-type="props.rowType"
              :level="props.level"
              :column-index="props.columnIndex"
              :columns-count="props.columnsCount"
              :data-item="props.dataItem"
            >
              <MCheckbox
                :checked="selectedIds.indexOf(props.dataItem.id) >= 0"
                :disabled="
                  (disablePreSelectedItem &&
                    preSelectedItems.indexOf(props.dataItem.id) >= 0) ||
                  selectionDisabledItems.indexOf(props.dataItem.id) >= 0
                "
                @change="
                  onSelectionChange({
                    dataItem: props.dataItem,
                  })
                "
              />
            </CustomCell>
          </template>
          <template v-for="(_, name) in validSlots" v-slot:[name]="slotData">
            <CustomCell
              v-if="name !== $attrs.detail"
              :key="name"
              :field="slotData.props.field"
              :class-name="slotData.props.className"
              :expanded="slotData.props.expanded"
              :row-type="slotData.props.rowType"
              :level="slotData.props.level"
              :column-index="slotData.props.columnIndex"
              :columns-count="slotData.props.columnsCount"
              :data-item="slotData.props.dataItem"
              @toggle-expand="
                onExpandChange({
                  dataItem: slotData.props.dataItem,
                  value: !slotData.props.dataItem.expanded,
                })
              "
            >
              <slot
                :name="name"
                v-bind="slotData"
                :item="slotData.props.dataItem"
                :expanded="slotData.props.expanded"
                :toggleExpand="
                  () =>
                    onExpandChange({
                      dataItem: slotData.props.dataItem,
                      value: !slotData.props.dataItem.expanded,
                    })
                "
              >
                {{ slotData.props.dataItem[slotData.props.field] }}
              </slot>
            </CustomCell>
            <slot
              v-else
              :name="name"
              v-bind="slotData"
              :item="slotData.props.dataItem"
              :toggleExpand="
                () =>
                  onExpandChange({
                    dataItem: slotData.props.dataItem,
                    value: !slotData.props.dataItem.expanded,
                  })
              "
            >
              {{ slotData.props.dataItem[slotData.props.field] }}
            </slot>
          </template>
        </Grid>
      </Transition>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import Pick from 'lodash/pick'
import Uniq from 'lodash/uniq'
import Trim from 'lodash/trim'
import CloneDeep from 'lodash/cloneDeep'
import UniqBy from 'lodash/uniqBy'
import LodashFilter from 'lodash/filter'
import LodashSortBy from 'lodash/sortBy'
import Throttle from 'lodash/throttle'
import FindIndex from 'lodash/findIndex'

import '@src/design/kendo/kendo.scss'
import { SORT_DISABLED_COLUMNS } from './helpers'
import { Grid } from '@progress/kendo-vue-grid'
import { gridWorker, arrayWorker } from '@/src/workers'
import Bus from '@utils/emitter'
import { UserPreferenceComputed } from '@state/modules/user-preference'
import CustomCell from './custom-cell.vue'
import SelectAll from './select-all.vue'

import {
  ACTION_ORDER_INDEX,
  MIN_COL_WIDTH,
} from '@components/widgets/constants'

const DEFAULT_PAGE_SIZE = 20

export default {
  name: 'MGrid',
  components: {
    Grid,
    CustomCell,
    SelectAll,
  },
  inject: {
    monitorContext: { default: { options: new Map() } },
    groupContext: { default: { options: new Map() } },
    roleContext: { default: { options: new Map() } },
    rpeContext: { default: { options: new Map() } },
    trapProfilesContext: { default: { options: new Map() } },
    agentContext: { default: { options: new Map() } },
    credentialContext: { default: { options: new Map() } },
    policyContext: { default: { options: new Map() } },
    runbookPluginContext: { default: { options: new Map() } },
    ldapServerContext: { default: { options: new Map() } },
    netRouteServerContext: { default: { options: new Map() } },
  },
  props: {
    columns: { type: Array, required: true },
    // eslint-disable-next-line
    hasTopMargin: { type: Boolean, default: true },
    hideSelectionInfo: { type: Boolean, required: false },
    defaultPageSize: { type: Number, default: DEFAULT_PAGE_SIZE },
    expandable: { type: Boolean, default: false },
    defaultSort: { type: [String, Array], default: undefined },
    filters: { type: [Object, Array], default: undefined },
    searchTerm: { type: String, default: undefined },
    selectable: { type: Boolean, default: false },
    rowHeight: { type: Number, default: 40 },
    maxAllowedSelection: { type: Number, default: 0 },
    resourceName: { type: String, default: undefined },
    getResizeWidthInPercent: { type: Boolean, default: false },
    preSelectedItems: {
      type: Array,
      default() {
        return []
      },
    },
    selectionDisabledItems: {
      type: Array,
      default() {
        return []
      },
    },
    disablePreSelectedItem: { type: Boolean, default: false },
    defaultGroup: { type: Array, default: undefined },
    // eslint-disable-next-line
    paging: { type: Boolean, default: true },
    data: {
      type: Array,
      default() {
        return []
      },
    },
    useExpandKey: { type: Boolean, default: false },
    debounceTime: { type: Number, default: 700 },
    totalCount: { type: Number, default: undefined },
    externalTake: { type: Number, default: undefined },
    externalSkip: { type: Number, default: undefined },
  },
  data() {
    this.sortable = {
      allowUnsort: false,
      mode: 'single',
    }
    this.pageable = {
      buttonCount: 5,
      info: true,
      type: 'numeric',
      pageSizes: [10, 20, 50, 100],
      previousNext: true,
      messages: {
        display: `Showing {0}-{1} from {2} data ${this.resourceName}`,
      },
    }
    this.isLoadEventSent = false
    return {
      processingData: true,
      currentPageIds: [],
      dataItems: {
        data: [],
        length: 0,
      },
      currentColumns: [],
      group: this.defaultGroup,
      skip: 0,
      take: this.defaultPageSize,
      sort: this.defaultSort
        ? typeof this.defaultSort === 'string'
          ? [{ field: this.defaultSort.replace('-', ''), dir: 'asc' }]
          : CloneDeep(this.defaultSort)
        : undefined,
      selectedIds: CloneDeep(this.preSelectedItems),
      key: 1,
    }
  },
  computed: {
    ...UserPreferenceComputed,
    contextColumns() {
      return (this.columns || [])
        .filter((c) => c.contextKey)
        .map((c) =>
          Pick(c, [
            'key',
            'name',
            'contextKey',
            'searchable',
            'searchKey',
            'sortKey',
          ])
        )
    },
    validSlots() {
      const slots = [
        ...this.columns.map(({ cellRender, key }) => cellRender || key),
        ...this.columns.map(({ headerCell }) => headerCell),
      ].filter(Boolean)
      if (this.$attrs.detail && typeof this.$attrs.string) {
        slots.push(this.$attrs.detail)
      }
      if (slots.length) {
        return Pick(this.$scopedSlots, slots)
      }
      return []
    },
    gridAttrs() {
      return {
        ...this.$attrs,
        ...(this.expandable ? { expandField: 'expanded' } : {}),
        ...(this.defaultGroup
          ? { expandField: 'expanded', groupable: true }
          : {}),
        ...(this.defaultGroup
          ? {
              expandField: 'expanded',
              groupable: true,
            }
          : { dataItems: this.items }),
        ...(this.selectable ? { selectedField: 'selected' } : {}),
      }
    },
    gridListeners() {
      return {
        ...(this.selectable
          ? {
              // selectionchange: this.onSelectionChange,
              headerselectionchange: this.onHeaderSelectionChange,
            }
          : {}),
        ...(this.defaultGroup
          ? {
              expandchange: this.onExpandChange,
            }
          : {}),
      }
    },
    currentFilters() {
      let filter
      if (
        (this.searchTerm || this.filters) &&
        this.columns &&
        this.columns.length
      ) {
        // when searchTerm is defined
        if ((this.searchTerm || '').length) {
          const keysToUse = this.columns
            .filter((column) => column.searchable)
            .map((c) => c.searchKey || c.key)
          filter = {
            logic: 'or',
            filters: keysToUse.map((key) => ({
              field: key,
              operator: 'contains',
              value: `${Trim(this.searchTerm)}`,
            })),
          }
        }
        // when filters are defined
        if (this.filters) {
          filter = {
            logic: 'and',
            filters: filter ? [filter, ...this.filters] : [...this.filters],
          }
        }
      }
      return filter
    },
    appliedSort() {
      let sort = this.sort ? CloneDeep(this.sort) : undefined
      if (sort && sort.length) {
        const sortField = sort[0].field
        const column = this.columns.find((c) => c.key === sortField)
        if (column && column.sortKey) {
          sort[0].field = column.sortKey
        }
      }
      return sort
    },
    pageInfo() {
      return {
        total: this.dataItems.total,
        current: Math.ceil(this.skip / this.take),
        pageSize: this.take,
      }
    },
  },
  watch: {
    columns: {
      handler: 'buildColumns',
    },
    data: {
      handler(newValue, oldValue) {
        if (newValue !== oldValue) {
          if (this.totalCount) {
            this.dataItems = {
              data: newValue,
              total: this.totalCount,
            }
          } else {
            this.setItems()
          }
        }
      },
    },
    searchTerm(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.skip = 0
        this.setItems()
      }
    },
    filters(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.skip = 0
        this.resetSelection()
        this.setItems()
      }
    },
  },
  created() {
    this.setItems = Throttle(this.setItemsRaw, this.debounceTime, {
      trailing: true,
    })
    const handler = () => {
      if (this.data.length) {
        this.setItems()
      }
    }
    Bus.$on('provider:fetched', handler)
    this.$once('hook:beforeDestroy', () => {
      Bus.$off('provider:fetched', handler)
    })
    this.take = this.pageSize
    this.setItems()
  },
  mounted() {
    this.buildColumns()
  },
  methods: {
    getCurrentData() {
      return this.dataItems?.data ? this.dataItems.data : []
    },
    addClassToScrollContainer() {
      if (this.$el.querySelector('.k-grid-content.k-virtual-content')) {
        this.$el
          .querySelector('.k-grid-content.k-virtual-content')
          .classList.add('__panel')
      }
    },
    getContextData() {
      const contextData = {}
      const contextColumns = this.contextColumns
      contextColumns.forEach(({ contextKey }) => {
        contextData[contextKey] = this[contextKey].options
      })
      return contextData
    },
    async getFilteredData(skip, take, group) {
      const currentFilters = this.currentFilters
      const sort = this.appliedSort
      const contextData = {}
      const contextColumns = this.contextColumns
      contextColumns.forEach(({ contextKey }) => {
        contextData[contextKey] = this[contextKey].options
      })
      let processedData = await gridWorker.processList(this.data, {
        ...(this.externalSkip !== undefined ? { skip: 0 } : { skip }),
        ...(this.externalTake !== undefined
          ? { take: this.externalTake }
          : { take: this.paging ? take : undefined }),
        ...(currentFilters ? { filter: currentFilters } : {}),
        ...(sort ? { sort } : {}),
        ...(group ? { group } : {}),
        columns: contextColumns,
        contextData,
      })
      return processedData
    },
    resetSelection() {
      this.selectedIds = CloneDeep(this.preSelectedItems)
      this.$emit('selection-change', this.selectedIds)
    },
    setSelection(ids = []) {
      this.selectedIds = CloneDeep(Uniq([...this.preSelectedItems, ...ids]))
      this.$emit('selection-change', this.selectedIds)
    },
    resetSkip() {
      this.skip = 0
      this.setItems()
    },
    handleSortChange(event) {
      this.sort = event.sort
      this.setItems()
    },
    async setItemsRaw() {
      let processedData = await this.getFilteredData(
        this.skip,
        this.take,
        this.group
      )
      let currentPageIds
      currentPageIds = processedData.data.map(({ id }) => id)
      if (this.group) {
        const ungroupedItems = await this.getFilteredData(this.skip, this.take)
        currentPageIds = ungroupedItems.data.map(({ id }) => id)
        processedData.data = processedData.data.map((d) => ({
          ...(this.dataItems.data.find((ed) => ed.value === d.value) || {}),
          ...d,
        }))
      }
      this.dataItems = processedData
      this.processingData = false
      if (this.selectable) {
        this.currentPageIds = Object.freeze(currentPageIds)
      }
      this.$emit('current-page-ids', currentPageIds)
      this.$nextTick(() => {
        setTimeout(() => {
          this.$emit('loaded')
          if (!this.isLoadEventSent) {
            this.isLoadEventSent = true
            this.$emit('loaded-once')
          }
          this.addClassToScrollContainer()
        }, 400)
      })
    },
    getPercentageInPixel(percentage) {
      return Math.round((this.$el.offsetWidth / 100) * percentage)
    },
    getPixelInPercentage(pixel) {
      return (pixel * 100) / this.$el.offsetWidth
    },
    getWidth(width) {
      if (width) {
        if (/%$/.test(width)) {
          return this.getPercentageInPixel(parseFloat(width))
        }
        return parseInt(width)
      }
      return undefined
    },
    buildColumns() {
      this.currentColumns = [
        ...(this.expandable
          ? [
              {
                field: 'expandableField',
                width: '0%',
              },
            ]
          : []),
        ...(this.selectable
          ? [
              {
                field: 'selected',
                width: '50px',
                cell: 'selectionCheckbox',
                headerCell: 'selectAllCheckbox',
                reorderable: false,
                groupable: false,
                resizable: false,
              },
            ]
          : []),
        ...this.columns.map(this.columnTranslate),
      ]
    },
    columnTranslate(item) {
      const isActionColumn = ['action', 'actions'].includes(item.key)

      const minWidth =
        this.getWidth(item.minWidth) ||
        Math.min(this.getWidth(item.width) || MIN_COL_WIDTH, MIN_COL_WIDTH)

      return {
        field: item.key,
        title: item.name,
        ...(item.minWidth
          ? { minResizableWidth: this.getWidth(item.minWidth) }
          : { minResizableWidth: 50 }),
        ...(item.width ? { width: this.getWidth(item.width) } : {}),
        groupable: !!item.groupable,
        hidden: !!item.hidden,
        sortable:
          SORT_DISABLED_COLUMNS.indexOf(item.key) >= 0
            ? false
            : !!item.sortable,
        ...(item.orderIndex ? { orderIndex: item.orderIndex } : {}),
        reorderable: 'reorderable' in item ? item.reorderable : true,
        resizable: 'resizable' in item ? item.resizable : true,
        className: `text-${item.align} ${item.className || ''} ${
          item.useEllipsis === false ? '' : 'text-ellipsis'
        }`,
        ...(this.$scopedSlots[item.key] ? { cell: item.key } : {}),
        ...(item.headerCell ? { headerCell: item.headerCell } : {}),
        ...(item.cellRender ? { cell: item.cellRender } : {}),
        ...(item.headerClassName
          ? { headerClassName: `${item.headerClassName || ''} text-ellipsis` }
          : {}),
        ...(item.align
          ? {
              headerClassName: `${item.headerClassName || ''} text-${
                item.align
              } text-ellipsis`,
              className: `text-${item.align} text-ellipsis ${
                item.className || ''
              }`,
            }
          : {}),
        context: Object.freeze({
          ...item,
          ...(isActionColumn
            ? { orderIndex: ACTION_ORDER_INDEX, reorderable: false }
            : {}),

          ...(minWidth ? { minWidth: `${minWidth}px` } : {}),
        }),
        ...(isActionColumn
          ? { orderIndex: ACTION_ORDER_INDEX, reorderable: false }
          : {}),

        ...(minWidth ? { minResizableWidth: minWidth } : {}),
      }
    },
    async onSelectionChange({ dataItem, selected }) {
      const id = dataItem.id
      let uniqIds = []
      const isSelected = this.selectedIds.indexOf(id) >= 0
      if (!isSelected) {
        uniqIds = Uniq([...this.selectedIds, id])
      } else {
        uniqIds = await arrayWorker.exclude(this.selectedIds, [id])
      }
      if (this.maxAllowedSelection) {
        if (uniqIds.length > this.maxAllowedSelection) {
          this.selectedIds = uniqIds.slice(
            uniqIds.length - this.maxAllowedSelection
          )
        } else {
          this.selectedIds = uniqIds
        }
      } else {
        this.selectedIds = uniqIds
      }
      this.$emit('selection-change', this.selectedIds)
    },
    async onHeaderSelectionChange(selected, selectionSize) {
      if (!this.paging) {
        selectionSize = 'all'
      }
      let selectedIds = [...this.selectedIds]
      const currentPageIds = this.currentPageIds
      if (selected) {
        if (selectionSize === 'page') {
          selectedIds = [...selectedIds, ...currentPageIds]
        } else {
          const allData = await this.getFilteredData()
          selectedIds = await arrayWorker.getPropertyFromList(
            allData.data,
            'id'
          )
        }
      } else {
        let idsToExcludeFromUnselection = this.disablePreSelectedItem
          ? this.preSelectedItems
          : []
        if (selectionSize === 'page') {
          const otherPageIds = await arrayWorker.exclude(
            selectedIds,
            currentPageIds
          )
          idsToExcludeFromUnselection = [
            ...idsToExcludeFromUnselection,
            ...otherPageIds,
          ]
        }
        selectedIds = await arrayWorker.keep(
          selectedIds,
          idsToExcludeFromUnselection
        )
      }
      let uniqIds = Uniq(selectedIds)
      const selectionDisabledItems = this.selectionDisabledItems || []
      uniqIds = await arrayWorker.exclude(
        Uniq(selectedIds),
        selectionDisabledItems
      )
      if (this.maxAllowedSelection) {
        this.selectedIds = uniqIds.slice(
          uniqIds.length - this.maxAllowedSelection
        )
      } else {
        this.selectedIds = uniqIds
      }
      this.$emit('selection-change', this.selectedIds)
    },
    handleDataStateChange(event) {
      const dataState = event.data
      if (this.defaultGroup) {
        this.group =
          dataState.group && Object.keys(dataState.group).length
            ? UniqBy(dataState.group, 'field')
            : dataState.group
      }
      this.skip = dataState.skip
      this.take = dataState.take
      if (this.$listeners['data-state-change']) {
        this.$emit('data-state-change', event)
      } else {
        this.setItems()
      }
    },
    onExpandChange(event) {
      Vue.set(event.dataItem, 'expanded', event.value)
      if (this.useExpandKey) {
        this.key++
      }
    },
    handleColumnReorder(options) {
      this.currentColumns = options.columns

      const actionColumnIndex = FindIndex(options.columns || [], (c) =>
        ['actions', 'action'].includes(c?.context?.key)
      )

      if (actionColumnIndex >= 0) {
        this.currentColumns = this.currentColumns
          .filter((c) => !['actions', 'action'].includes(c?.context?.key))
          .concat([
            {
              ...this.currentColumns[actionColumnIndex],
              orderIndex: ACTION_ORDER_INDEX,
            },
          ])
      }

      const excluded = ['expandableField', 'selected']
      const updatedColumns = LodashFilter(
        LodashSortBy(this.currentColumns, 'orderIndex'),
        ({ field }) => excluded.includes(field) === false
      )
      this.$emit(
        'column-change',
        updatedColumns.map((c) => ({ ...c.context, orderIndex: c.orderIndex }))
      )
    },
    handleColumnResize(event) {
      if (event.end === false) {
        return
      }
      const excluded = ['expandableField', 'selected']
      const usePercentageOnly = this.getResizeWidthInPercent
      const updatedColumns = [
        ...event.columns.slice(0, event.index),
        { ...event.columns[event.index] },
        ...event.columns.slice(event.index + 1),
      ].filter((c) => excluded.includes(c.field) === false)
      this.$emit(
        'column-change',
        updatedColumns.map((c) => ({
          ...c.context,
          width: usePercentageOnly
            ? c.width && `${this.getPixelInPercentage(parseInt(c.width))}%`
            : c.width && /%$/.test(c.width)
            ? `${this.getPixelInPercentage(parseInt(c.width))}%`
            : c.width,
        }))
      )
    },
    async getFilteredDataWithoutTake() {
      const filteredData = await this.getFilteredData()
      return filteredData
    },
  },
}
</script>
