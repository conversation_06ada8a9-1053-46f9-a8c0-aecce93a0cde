<script>
import Pick from 'lodash/pick'
import Trim from 'lodash/trim'
import CloneDeep from 'lodash/cloneDeep'
import Throttle from 'lodash/throttle'
import { gridWorker } from '@/src/workers'

const DEFAULT_PAGE_SIZE = 20

export default {
  name: 'RenderLessSearching',
  props: {
    data: {
      type: Array,
      default() {
        return []
      },
    },
    columns: { type: Array, required: true },
    defaultSort: { type: [String, Array], default: undefined },
    searchTerm: { type: String, default: undefined },
    debounceTime: { type: Number, default: 700 },
    filters: { type: [Object, Array], default: undefined },
    externalSkip: { type: Number, default: undefined },
    // eslint-disable-next-line
    paging: { type: Boolean, default: true },
    defaultPageSize: { type: Number, default: DEFAULT_PAGE_SIZE },
    defaultGroup: { type: Array, default: undefined },
    totalCount: { type: Number, default: undefined },
  },
  data() {
    this.isLoadEventSent = false

    return {
      processingData: true,
      dataItems: {
        data: [],
        length: 0,
      },
      sort: this.defaultSort
        ? typeof this.defaultSort === 'string'
          ? [{ field: this.defaultSort.replace('-', ''), dir: 'asc' }]
          : CloneDeep(this.defaultSort)
        : undefined,
      group: this.defaultGroup,
      skip: 0,
      take: this.defaultPageSize,
    }
  },
  computed: {
    currentFilters() {
      let filter
      if (
        (this.searchTerm || this.filters) &&
        this.columns &&
        this.columns.length
      ) {
        // when searchTerm is defined
        if ((this.searchTerm || '').length) {
          const keysToUse = this.columns
            .filter((column) => column.searchable)
            .map((c) => c.searchKey || c.key)
          filter = {
            logic: 'or',
            filters: keysToUse.map((key) => ({
              field: key,
              operator: 'contains',
              value: `${Trim(this.searchTerm)}`,
            })),
          }
        }
        // when filters are defined
        if (this.filters) {
          filter = {
            logic: 'and',
            filters: filter ? [filter, ...this.filters] : [...this.filters],
          }
        }
      }
      return filter
    },
    appliedSort() {
      let sort = this.sort ? CloneDeep(this.sort) : undefined
      if (sort && sort.length) {
        const sortField = sort[0].field
        const column = this.columns.find((c) => c.key === sortField)
        if (column && column.sortKey) {
          sort[0].field = column.sortKey
        }
      }
      return sort
    },
    contextColumns() {
      return (this.columns || [])
        .filter((c) => c.contextKey)
        .map((c) =>
          Pick(c, [
            'key',
            'name',
            'contextKey',
            'searchable',
            'searchKey',
            'sortKey',
          ])
        )
    },
  },
  watch: {
    searchTerm(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.setItems()
      }
    },
    data: {
      handler(newValue, oldValue) {
        if (newValue !== oldValue) {
          if (this.totalCount) {
            this.dataItems = {
              data: newValue,
              total: this.totalCount,
            }
          } else {
            this.setItems()
          }
        }
      },
    },
  },
  created() {
    this.setItems = Throttle(this.setItemsRaw, this.debounceTime, {
      trailing: true,
    })
    this.take = this.pageSize
    this.setItems()
  },

  methods: {
    async getFilteredData(skip, take, group) {
      const currentFilters = this.currentFilters
      const sort = this.appliedSort
      const contextData = {}
      const contextColumns = this.contextColumns
      contextColumns.forEach(({ contextKey }) => {
        contextData[contextKey] = this[contextKey].options
      })
      let processedData = await gridWorker.processList(this.data, {
        ...(this.externalSkip !== undefined ? { skip: 0 } : { skip }),
        ...(this.externalTake !== undefined
          ? { take: this.externalTake }
          : { take: this.paging ? take : undefined }),
        ...(currentFilters ? { filter: currentFilters } : {}),
        ...(sort ? { sort } : {}),
        ...(group ? { group } : {}),
        columns: contextColumns,
        contextData,
      })
      return processedData
    },
    async setItemsRaw() {
      this.processingData = true
      let processedData = await this.getFilteredData(
        this.skip,
        this.take,
        this.group
      )
      // let currentPageIds
      // currentPageIds = processedData.data.map(({ id }) => id)
      if (this.group) {
        // const ungroupedItems = await this.getFilteredData(this.skip, this.take)
        // currentPageIds = ungroupedItems.data.map(({ id }) => id)
        processedData.data = processedData.data.map((d) => ({
          ...(this.dataItems.data.find((ed) => ed.value === d.value) || {}),
          ...d,
        }))
      }
      this.dataItems = processedData
      this.processingData = false
      this.$nextTick(() => {
        setTimeout(() => {
          this.$emit('loaded')
          if (!this.isLoadEventSent) {
            this.isLoadEventSent = true
            this.$emit('loaded-once')
          }
        }, 400)
      })
    },
  },
  render() {
    return this.$scopedSlots.default({
      dataItems: this.dataItems,
      processingData: this.processingData,
    })
  },
}
</script>
