<template functional>
  <td
    v-if="
      props.field !== undefined &&
      props.rowType !== 'groupHeader' &&
      props.rowType !== 'pivotGroupHeader'
    "
    :class="{
      [props.className]: true,
      'has-detail-row-open': props.dataItem.expanded,
    }"
  >
    <slot />
  </td>
  <td
    v-else-if="
      (props.columnIndex === undefined ||
        props.level === undefined ||
        props.columnIndex < props.level ||
        props.columnsCount === undefined ||
        (props.rowType !== 'groupHeader' &&
          props.rowType !== 'pivotGroupHeader') ||
        props.dataItem[props.field] === undefined) &&
      props.dataItem[props.field]
    "
    :key="`g${props.columnIndex}`"
    class="k-group-cell"
    :class="{
      'has-detail-row-open': props.dataItem.expanded,
    }"
  />
  <td
    v-else-if="props.columnIndex <= props.level"
    key="g-colspan"
    :colSpan="props.columnsCount - props.columnIndex"
    :class="{
      [props.className]: true,
      'has-detail-row-open': props.dataItem.expanded,
    }"
  >
    <p class="k-reset">
      <a
        tabIndex="-1"
        href="#"
        :class="props.expanded ? 'k-i-collapse k-icon' : 'k-i-expand k-icon'"
        @click.prevent="
          listeners['toggle-expand']({
            dataItem: props.dataItem,
            value: !props.expanded,
          })
        "
      />
      {{ props.dataItem[props.field] }}
    </p>
  </td>
  <td v-else class="hidden"> Extra </td>
</template>

<script>
export default {
  name: 'CustomCell',
  props: {
    field: { type: String, required: true },
    dataItem: { type: Object, required: true },
    format: { type: String, default: undefined },
    className: { type: String, default: undefined },
    columnIndex: { type: Number, default: undefined },
    columnsCount: { type: Number, default: undefined },
    rowType: { type: String, default: undefined },
    level: { type: Number, default: undefined },
    expanded: { type: Boolean, default: false },
    editor: { type: String, default: undefined },
  },
  // computed: {
  //   renderArrow() {
  //     var returnValue =
  //       this.columnIndex === undefined ||
  //       this.level === undefined ||
  //       this.columnIndex < this.level ||
  //       this.columnsCount === undefined ||
  //       this.rowType !== 'groupHeader' ||
  //       this.dataItem[this.field] === undefined
  //     return returnValue && this.dataItem[this.field]
  //   },
  //   renderCell() {
  //     return this.field !== undefined && this.rowType !== 'groupHeader'
  //   },
  // },
  // methods: {
  //   onClick(e) {
  //     this.$emit('toggle-expand', {
  //       dataItem: this.dataItem,
  //       value: !this.expanded,
  //     })
  //   },
  // },
}
</script>
