<template>
  <FlotoForm
    ref="form"
    layout="vertical"
    @reset="$emit('reset')"
    @submit="handleSave"
  >
    <template v-slot="slotData">
      <FlotoDrawer
        ref="drawer"
        :width="width"
        v-bind="$attrs"
        :scrolled-content="scrolledContent"
        @show="$emit('show')"
        @hide="handleCancel"
      >
        <template v-slot:trigger="triggerSlotData">
          <slot name="trigger" v-bind="triggerSlotData" />
        </template>
        <template v-slot:title>
          <slot name="header"></slot>
        </template>
        <template v-slot:title-row>
          <slot name="header-row"></slot>
        </template>
        <slot :submit="submit" />
        <template v-slot:actions="{ hide: hideDrawer }">
          <slot
            name="actions"
            v-bind="slotData"
            :hide="hideDrawer"
            :submit="submit"
          ></slot>
        </template>
      </FlotoDrawer>
    </template>
    <template v-slot:submit>
      <span />
    </template>
  </FlotoForm>
</template>

<script>
export default {
  name: 'FlotoDrawerForm',
  props: {
    open: { type: Boolean, default: false },
    width: { type: [String, Number], default: '40%' },
    // eslint-disable-next-line
    scrolledContent: { type: Boolean, default: true },
  },
  watch: {
    open: {
      immediate: true,
      handler(newValue) {
        if (this.$refs.drawer) {
          if (newValue) {
            this.$refs.drawer.show()
          } else {
            this.$refs.drawer.hide()
          }
        }
      },
    },
  },
  mounted() {
    if (this.open) {
      this.$refs.drawer.show()
    }
  },
  beforeDestroy() {
    if (this.$refs.drawer) {
      this.$refs.drawer.hide()
    }
  },
  methods: {
    hide() {
      this.$refs.drawer.hide()
    },
    submit(...args) {
      this.$refs.form.submit(...args)
    },
    handleSave(...args) {
      this.$emit('submit', ...args)
    },
    handleCancel() {
      this.$emit('cancel')
    },
    validate() {
      return this.$refs.form.validate()
    },
  },
}
</script>
