<template>
  <MModal
    open
    :width="1000"
    overlay-class-name="confirm-modal floto-confirm-modal scrollable-modal hide-footer restrict-width"
    @hide="$emit('hide')"
  >
    <template v-slot:trigger>
      <span />
    </template>
    <template v-slot:title>
      <slot name="title" />
    </template>

    <div>
      <MRow :gutter="0">
        <MCol>
          <MInput v-model="searchTerm" class="search-box" placeholder="Search">
            <template v-slot:prefix>
              <MIcon name="search" />
            </template>
            <template v-if="searchTerm" v-slot:suffix>
              <MIcon
                name="times-circle"
                class="text-neutral-light cursor-pointer"
                @click="searchTerm = undefined"
              />
            </template>
          </MInput>
        </MCol>
      </MRow>
    </div>

    <VirtualTable
      :use-padding="false"
      :search-term="searchTerm"
      :default-sort="defaultSort"
      :columns="columns"
      :data="gridRows"
    />
    <template v-slot:footer>
      <span />
    </template>
  </MModal>
</template>

<script>
import Flatten from 'lodash/flatten'
import { generateId } from '@utils/id'
import VirtualTable from '@components/crud/virtual-table.vue'

const ENTITY_MAP = {
  apps: 'Application',
}

export default {
  name: 'LinkedRecordsDetailModal',
  components: {
    VirtualTable,
  },
  props: {
    data: { type: Object, default: undefined },
    bulkDeleteColumns: { type: Object, default: undefined },
  },
  data() {
    return {
      searchTerm: undefined,
      gridRows: [],
      columns: [
        {
          key: 'useIn',
          name: 'Entity Type',
          searchable: true,
          sortable: true,
        },
        {
          key: 'value',
          name: 'Entity Name',
          searchable: true,
          sortable: true,
        },
      ],
      defaultSort: 'useIn',
    }
  },
  created() {
    this.generateData()
  },
  methods: {
    generateData() {
      if (this.bulkDeleteColumns) {
        this.columns = this.bulkDeleteColumns
        const result = Flatten(
          (this.data.result || {}).map((val) => ({
            id: generateId(),
            name: val['name'],
            entity: val['entity'],
            type: val['type'],
          }))
        )
        this.gridRows = Object.freeze(result)
        return
      }
      const response = this.data.result || {}
      const result = Flatten(
        Object.keys(response).map((item) =>
          response[item].map((val) => ({
            id: generateId(),
            useIn: ENTITY_MAP[item] || item,
            value: val,
          }))
        )
      )
      this.gridRows = Object.freeze(result)
    },
  },
}
</script>
