<template>
  <div ref="gridContainerRef" class="flex flex-1 min-h-0">
    <FlotoContentLoader :loading="processing">
      <Grid
        :key="gridKey"
        :class="{ 'non-relative-td': $attrs['is-alert-report'] }"
        :style="{ height: height ? `${height}px` : `calc(${100}% - 15px)` }"
        :data-items="items"
        :data-item-key="dataItemKey"
        :columns="currentColumns"
        :total="_list.length"
        :row-height="rowHeight"
        :resizable="resizable"
        :reorderable="reorderable"
        :skip="skip"
        :page-size="take"
        :sortable="sortable"
        :sort="sort"
        :scrollable="'virtual'"
        @sortchange="handleSortChange"
        @pagechange="pageChange"
        @columnreorder="handleColumnReorder"
        @columnresize="handleColumnResize"
      >
        <template v-slot:selectAllCheckbox>
          <MCheckbox
            v-if="maxAllowedSelection === 0 && items.length"
            :checked="areAllSelected"
            @change="onHeaderSelectionChange(areAllSelected ? false : true)"
          />
          <span v-else />
        </template>
        <template v-slot:selectionCheckbox="{ props }">
          <CustomCell
            :field="props.field"
            :expanded="props.expanded"
            :row-type="props.rowType"
            :level="props.level"
            :class-name="props.className"
            :column-index="props.columnIndex"
            :columns-count="props.columnsCount"
            :data-item="props.dataItem"
          >
            <MCheckbox
              :checked="selectedIds.indexOf(props.dataItem.id) >= 0"
              :disabled="
                disablePreSelectedItem &&
                preSelectedItems.indexOf(props.dataItem.id) >= 0
              "
              @change="
                onSelectionChange({
                  dataItem: props.dataItem,
                  selected: $event,
                })
              "
            />
          </CustomCell>
        </template>
        <template v-for="(_, name) in validSlots" v-slot:[name]="slotData">
          <CustomCell
            v-if="!headerValidSlots.includes(name)"
            :key="name"
            :field="slotData.props.field"
            :class-name="slotData.props.className"
            :expanded="slotData.props.expanded"
            :row-type="slotData.props.rowType"
            :level="slotData.props.level"
            :column-index="slotData.props.columnIndex"
            :columns-count="slotData.props.columnsCount"
            :data-item="slotData.props.dataItem"
          >
            <slot
              :name="name"
              v-bind="slotData"
              :item="slotData.props.dataItem || {}"
            >
              {{ (slotData.props.dataItem || {})[slotData.props.field] }}
            </slot>
          </CustomCell>

          <span v-else :key="name" class="w-full flex">
            <slot :name="name" v-bind="slotData" :item="slotData.props || {}">
              {{ slotData.props.title || {} }}
            </slot>
          </span>
        </template>
      </Grid>
    </FlotoContentLoader>
  </div>
</template>

<script>
import Pick from 'lodash/pick'
import Uniq from 'lodash/uniq'
import LodashFilter from 'lodash/filter'
import LodashSortBy from 'lodash/sortBy'
import CloneDeep from 'lodash/cloneDeep'
import Debounce from 'lodash/debounce'
// import Throttle from 'lodash/throttle'
import { Grid } from '@progress/kendo-vue-grid'
import Trim from 'lodash/trim'
import Bus from '@utils/emitter'
import { SORT_DISABLED_COLUMNS } from './helpers'
import { gridWorker, arrayWorker } from '@/src/workers'
import CustomCell from './custom-cell.vue'

export default {
  name: 'VirtualTable',
  components: {
    Grid,
    CustomCell,
  },
  inject: {
    groupContext: { default: { options: new Map() } },
    rpeContext: { default: { options: new Map() } },
    credentialContext: { default: { options: new Map() } },
    agentContext: { default: { options: new Map() } },
  },
  props: {
    searchTerm: { type: String, default: undefined },
    rowHeight: { type: Number, default: 40 },
    buffer: { type: Number, default: 20 },
    columns: {
      type: Array,
      required: true,
    },
    defaultSort: { type: String, default: undefined },
    selectable: { type: Boolean, default: false },
    maxAllowedSelection: { type: Number, default: 0 },
    filters: { type: [Array], default: undefined },
    preSelectedItems: {
      type: Array,
      default() {
        return []
      },
    },
    // eslint-disable-next-line
    reorderable: { type: Boolean, default: true },
    // eslint-disable-next-line
    resizable: { type: Boolean, default: true },
    getResizeWidthInPercent: { type: Boolean, default: false },
    disablePreSelectedItem: { type: Boolean, default: false },
    data: {
      type: Array,
      default() {
        return []
      },
    },
    debounceTime: {
      type: Number,
      default: 750,
    },
    dataItemKey: {
      type: String,
      default: 'id',
    },
  },
  data() {
    this.sortable = {
      allowUnsort: false,
      mode: 'single',
    }
    this.$_processedDataReady = false
    this._list = []
    return {
      listIds: [],
      gridKey: 1,
      processing: true,
      height: undefined,
      sort: this.defaultSort
        ? [
            {
              field: this.defaultSort.replace('-', ''),
              dir: /^-/.test(this.defaultSort) ? 'desc' : 'asc',
            },
          ]
        : undefined,
      currentColumns: [],
      items: [],
      take: 0,
      skip: 0,
      selectedIds: Object.freeze(CloneDeep(this.preSelectedItems)),
    }
  },
  computed: {
    contextColumns() {
      return this.columns
        .filter((c) => c.contextKey)
        .map((c) =>
          Pick(c, [
            'key',
            'name',
            'contextKey',
            'searchable',
            'searchKey',
            'sortKey',
          ])
        )
    },
    validSlots() {
      const slots = [
        ...this.columns.map(({ cellRender, key }) => cellRender || key),
        ...this.columns.map(({ headerCell }) => headerCell),
      ].filter(Boolean)
      if (slots.length) {
        return Pick(this.$scopedSlots, slots)
      }
      return {}
    },

    headerValidSlots() {
      const slots = [
        ...this.columns.map(({ headerCell }) => headerCell),
      ].filter(Boolean)

      if (slots.length) {
        return slots
      }
      return []
    },

    areAllSelected() {
      const selectedIds = this.selectedIds
      if (selectedIds.length) {
        return this.listIds.every((v) => selectedIds.includes(v))
      }
      return false
    },
    appliedSort() {
      let sort = this.sort ? CloneDeep(this.sort) : undefined
      if (sort && sort.length) {
        const sortField = sort[0].field
        const column = this.columns.find((c) => c.key === sortField)
        if (column && column.sortKey) {
          sort[0].field = column.sortKey
        }
      }
      return sort
    },
    currentFilters() {
      let filter
      if (
        (this.searchTerm || this.filters) &&
        this.columns &&
        this.columns.length
      ) {
        // when searchTerm is defined
        if ((this.searchTerm || '').length) {
          const keysToUse = this.columns
            .filter((column) => column.searchable)
            .map((c) => c.searchKey || c.key)
          filter = {
            logic: 'or',
            filters: keysToUse.map((key) => ({
              field: key,
              operator: 'contains',
              value: `${Trim(this.searchTerm)}`,
            })),
          }
        }
        // when filters are defined
        if (this.filters) {
          filter = {
            logic: 'and',
            filters: filter ? [filter, ...this.filters] : [...this.filters],
          }
        }
      }
      return filter
    },
  },
  watch: {
    defaultSort(newValue) {
      if (newValue) {
        this.handleSortChange({
          sort: [
            {
              field: newValue.replace('-', ''),
              dir: /^-/.test(newValue) ? 'desc' : 'asc',
            },
          ],
        })
      }
    },
    columns: {
      handler: 'buildColumns',
    },
    preSelectedItems: {
      handler(newValue) {
        this.selectedIds = newValue
      },
    },
    searchTerm: {
      handler() {
        this.skip = 0
        this.debouncedBuildList()
      },
    },
    filters: {
      handler() {
        this.skip = 0
        this.resetSelection()
        this.buildList()
      },
    },
    data: {
      handler() {
        this.calculateHeight()
        this.buildList()
      },
    },
  },
  created() {
    const handler = () => {
      if (this.data.length) {
        this.buildList()
      }
    }
    Bus.$on('provider:fetched', handler)
    this.$once('hook:beforeDestroy', () => {
      Bus.$off('provider:fetched', handler)
    })
  },
  mounted() {
    this.buildColumns()
    this.calculateHeight()
    this.buildList()
    this.debouncedBuildList = Debounce(this.buildList, this.debounceTime, {
      trailing: true,
    })
    // resize observer
    this.$_resizeObserver = new ResizeObserver((entries) => {
      this.calculateHeight()
      this.buildColumns()
    })
    this.$_resizeObserver.observe(this.$refs.gridContainerRef)
  },
  beforeDestroy() {
    if (this.$_resizeObserver) {
      this.$_resizeObserver.disconnect()
      this.$_resizeObserver = null
    }
  },
  activated() {
    this.gridKey++
  },
  methods: {
    resetSelection() {
      this.selectedIds = CloneDeep(this.preSelectedItems)
    },
    calculateHeight() {
      if (this.$refs.gridContainerRef) {
        const height = this.$refs.gridContainerRef.clientHeight - 15
        const perPage = Math.ceil(height / this.rowHeight + this.buffer)
        this.take = Math.min(perPage, this.data.length)
        this.height = height
      }
    },
    async buildSlice() {
      if (!this.$_processedDataReady) {
        return
      }
      this.items = Object.freeze(
        this._list.slice(this.skip, this.skip + this.take)
      )
      setTimeout(() => {
        if (this.$el) {
          Array.prototype.forEach.call(
            this.$el.querySelectorAll('.report-group-header'),
            function (el) {
              el.parentNode.style.position = 'unset'
            }
          )
        }
      }, 700)
    },
    async buildList() {
      const currentFilters = this.currentFilters
      const sort = this.appliedSort
      const contextData = {}
      const contextColumns = this.contextColumns
      contextColumns.forEach(({ contextKey }) => {
        contextData[contextKey] = this[contextKey].options
      })
      const data = await gridWorker.processList(
        this.data,
        {
          ...(currentFilters ? { filter: currentFilters } : {}),
          ...(sort ? { sort } : {}),
          columns: contextColumns,
          contextData,
        },
        true
      )
      this._list = data.data
      this.$_processedDataReady = true
      this.processing = false
      this.buildSlice()
      this.listIds = await arrayWorker.getPropertyFromList(this._list, 'id')
    },
    handleSortChange(event) {
      this.sort = event.sort
      this.buildList()
    },
    handleColumnReorder(options) {
      this.currentColumns = options.columns
      const updatedColumns = LodashFilter(
        LodashSortBy(this.currentColumns, 'orderIndex'),
        ({ field }) => field !== 'selected'
      )
      this.$emit(
        'column-change',
        updatedColumns.map((c) => ({
          ...c.context,
          orderIndex: c.orderIndex + 1,
        }))
      )
    },
    handleColumnResize(event) {
      if (event.end === false) {
        return
      }
      const usePercentageOnly = this.getResizeWidthInPercent
      const updatedColumns = [
        ...event.columns.slice(0, event.index),
        { ...event.columns[event.index] },
        ...event.columns.slice(event.index + 1),
      ].filter((c) => c.field !== 'selected')
      this.$emit(
        'column-change',
        updatedColumns.map((c) => ({
          ...c.context,
          ...(c.orderIndex ? { orderIndex: c.orderIndex } : {}),
          width: usePercentageOnly
            ? c.width && `${this.getPixelInPercentage(parseInt(c.width))}%`
            : c.width && /%$/.test(c.width)
            ? `${this.getPixelInPercentage(parseInt(c.width))}%`
            : c.width,
        }))
      )
    },
    pageChange(event) {
      this.skip = event.page.skip
      this.buildSlice()
    },
    getPercentageInPixel(percentage) {
      return Math.round((this.$el.offsetWidth / 100) * percentage)
    },
    getPixelInPercentage(pixel) {
      return (pixel * 100) / this.$el.offsetWidth
    },
    getWidth(width) {
      if (width) {
        if (/%$/.test(width)) {
          return this.getPercentageInPixel(parseFloat(width))
        }
        return parseInt(width)
      }
      return undefined
    },
    buildColumns() {
      this.currentColumns = Object.freeze([
        ...(this.selectable
          ? [
              {
                field: 'selected',
                width: '50px',
                cell: 'selectionCheckbox',
                headerCell: 'selectAllCheckbox',
                reorderable: false,
                groupable: false,
                resizable: false,
              },
            ]
          : []),
        ...this.columns.map(this.columnTranslate),
      ])
    },
    columnTranslate(item) {
      return {
        field: item.key,
        title: item.name,
        ...(item.minWidth
          ? { minResizableWidth: this.getWidth(item.minWidth) }
          : { minResizableWidth: '50px' }),
        ...(item.width ? { width: this.getWidth(item.width) } : {}),
        groupable: !!item.groupable,
        hidden: !!item.hidden,
        sortable:
          SORT_DISABLED_COLUMNS.indexOf(item.key) >= 0
            ? false
            : !!item.sortable,
        ...(item.orderIndex ? { orderIndex: item.orderIndex } : {}),
        reorderable: 'reorderable' in item ? item.reorderable : true,
        resizable: 'resizable' in item ? item.resizable : true,
        className: `text-${item.align} text-ellipsis`,
        ...(this.$scopedSlots[item.key] ? { cell: item.key } : {}),
        ...(item.cellRender ? { cell: item.cellRender } : {}),
        ...(item.headerCell ? { headerCell: item.headerCell } : {}),
        ...(item.headerClassName
          ? { headerClassName: `${item.headerClassName || ''} text-ellipsis` }
          : {}),
        ...(item.align
          ? {
              headerClassName: `${item.headerClassName || ''} text-${
                item.align
              } text-ellipsis`,
              className: `text-${item.align} text-ellipsis`,
            }
          : {}),
        context: Object.freeze(item),
      }
    },
    async onHeaderSelectionChange(isChecked) {
      if (isChecked) {
        let ids = await arrayWorker.getPropertyFromList(this._list, 'id')
        const disablePreSelectedItem = this.disablePreSelectedItem
        if (disablePreSelectedItem) {
          ids = Uniq([...ids, ...this.preSelectedItems])
        }
        this.selectedIds = ids
      } else {
        const preSelectedItems = this.preSelectedItems
        const disablePreSelectedItem = this.disablePreSelectedItem
        if (disablePreSelectedItem) {
          this.selectedIds = await arrayWorker.keep(
            this.selectedIds,
            preSelectedItems
          )
        } else {
          this.selectedIds = []
        }
      }
      this.$emit('selection-change', this.selectedIds)
    },
    async onSelectionChange({ dataItem, selected }) {
      const id = dataItem.id
      let uniqIds = []
      const isSelected = this.selectedIds.indexOf(id) >= 0
      if (!isSelected) {
        uniqIds = Uniq([...this.selectedIds, id])
      } else {
        uniqIds = await arrayWorker.exclude(this.selectedIds, [id])
      }
      if (this.maxAllowedSelection) {
        this.selectedIds = uniqIds.slice(
          uniqIds.length - this.maxAllowedSelection
        )
      } else {
        this.selectedIds = uniqIds
      }
      this.$emit('selection-change', this.selectedIds)
    },
    getContextData() {
      const contextData = {}
      const contextColumns = this.contextColumns
      contextColumns.forEach(({ contextKey }) => {
        contextData[contextKey] = this[contextKey].options
      })
      return contextData
    },
  },
}
</script>
