<template>
  <FlotoDropdownPicker
    v-if="pageInfo.total > 0"
    :searchable="false"
    :options="selectionOptions"
    placement="bottom-start"
    :disabled="pageInfo.total <= pageInfo.pageSize"
    overlay-class-name="auto-size-dropdown picker-overlay"
    @change="handleSelectionChange"
  >
    <template v-slot:trigger="{ toggle }">
      <div class="flex items-center" style="margin-top: 1px">
        <MCheckbox
          v-if="maxAllowedSelection === 0"
          :key="pageInfo.current"
          :checked="isCurrentPageSelected"
          @change="handleChecked(isCurrentPageSelected ? false : true)"
          @click.stop="handleClick"
        />
        <MIcon
          v-if="pageInfo.total > pageInfo.pageSize"
          name="chevron-down"
          class="cursor-pointer ml-1 text-neutral"
          size="sm"
          @click="toggle"
        />
      </div>
    </template>
  </FlotoDropdownPicker>
</template>

<script>
export default {
  name: 'SelectAll',
  props: {
    maxAllowedSelection: { type: Number, default: undefined },
    toggleSelectAll: { type: Function, required: true },
    pageInfo: { type: Object, required: true },
    selectedItems: { type: Array, required: true },
    items: { type: Array, required: true },
    resourceName: { type: String, default: 'Records' },
  },
  data() {
    return {}
  },
  computed: {
    selectionOptions() {
      const isCurrentPageSelected = this.isCurrentPageSelected
      const allSelected = this.selectedItems.length === this.pageInfo.total
      return [
        {
          key: isCurrentPageSelected || allSelected ? 'unselect-page' : 'page',
          text: `${
            isCurrentPageSelected || allSelected ? 'Unselect' : 'Select'
          } Page`,
        },
        {
          key: allSelected ? 'unselect-all' : 'all',
          text: `${allSelected ? 'Unselect' : 'Select'} All`,
        },
      ]
    },
    // isIndeterminate() {
    //   const selectedItems = this.selectedItems
    //   if (
    //     selectedItems.length === 0 ||
    //     selectedItems.length === this.pageInfo.total
    //   ) {
    //     return false
    //   }
    //   const pageItemIds = this.items.map(({ id }) => id)
    //   return pageItemIds.some((v) => selectedItems.includes(v))
    // },
    isCurrentPageSelected() {
      const selectedItems = this.selectedItems
      if (selectedItems.length === 0) {
        return false
      }
      const pageItemIds = this.items
      return pageItemIds.every((v) => selectedItems.includes(v))
    },
  },
  methods: {
    handleClick(e) {},
    handleChecked($event) {
      // const selectedItems = this.selectedItems
      let isChecked = $event
      if (this.isCurrentPageSelected && isChecked) {
        isChecked = false
      }
      // if (selectedItems.length >= this.pageInfo.total) {
      //   return this.toggleSelectAll(isChecked, 'all')
      // }
      this.toggleSelectAll(isChecked, 'page')
    },
    handleSelectionChange(value) {
      let isChecked = true
      if (value.indexOf('-') >= 0) {
        const parts = value.split('-')
        isChecked = false
        value = parts[1]
      }
      this.toggleSelectAll(isChecked, value)
    },
  },
}
</script>
