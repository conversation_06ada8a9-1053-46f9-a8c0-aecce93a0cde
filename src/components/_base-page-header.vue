<template>
  <MRow :gutter="0" class="min-w-0" :class="{ 'header-has-divider': false }">
    <MCol class="flex min-w-0">
      <MRow :gutter="0" class="w-full flex-no-wrap items-center min-w-0">
        <MCol
          class="items-center flex py-2 min-w-0"
          :class="{
            'main-title-panel': mainHeader,
            'main-inner-title-panel': !mainHeader,
          }"
        >
          <slot name="back-button">
            <FlotoBackButton
              v-if="backLink"
              :to="backLink"
              class="text-neutral-light"
            />
          </slot>
          <h4
            class="flex items-center mb-0 font-normal flex-1 text-primary-alt header-primery-inner-color min-w-0"
          >
            <slot name="before-title"></slot>
            <div class="flex-1 min-w-0">
              <div class="w-full items-center">
                <div class="mx-2 flex items-center min-w-0">
                  <div class="min-w-0 text-ellipsis font-500">
                    {{ title }}
                  </div>
                  <slot name="title" />
                </div>
                <slot name="title-append"></slot>
              </div>
            </div>
            <slot name="after-title"></slot>
          </h4>
          <slot />
        </MCol>
      </MRow>
    </MCol>
    <slot name="additional-rows" />
    <!--<MCol v-if="useDivider" :size="12">
    </MCol>-->
  </MRow>
</template>

<script>
export default {
  name: 'FlotoPageHeader',
  props: {
    backLink: { type: [String, Object], default: undefined },
    title: { type: String, default: undefined },
    mainHeader: { type: Boolean, default: false },
    useDivider: { type: Boolean, default: false },
  },
}
</script>
