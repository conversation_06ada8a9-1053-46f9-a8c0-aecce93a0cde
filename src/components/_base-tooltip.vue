<template>
  <div v-if="disabled" class="inline-block">
    <slot name="trigger" />
  </div>
  <VTippy
    v-else
    :lazy="true"
    :interactive="true"
    class="inline-block"
    :arrow="false"
    animation="shift-toward"
    :append-to="container"
  >
    <template v-slot:trigger>
      <slot name="trigger" />
    </template>
    <div :class="overlayClassName">
      <div class="ant-tooltip-content">
        <div class="ant-tooltip-inner">
          <div class="ant-tooltip-inner-content" :class="overlayClassName">
            <slot />
          </div>
        </div>
      </div>
    </div>
  </VTippy>
</template>

<script>
export default {
  name: 'MTooltip',
  props: {
    placement: {
      type: String,
      default: 'top-start',
    },
    disabled: { type: Boolean, default: false },
    trigger: { type: String, default: 'hover focus' },
    overlayClassName: { type: String, default: undefined },
    getPopupContainer: {
      type: Function,
      default: undefined,
    },
  },
  methods: {
    container() {
      const element = this.getPopupContainer
        ? this.getPopupContainer()
        : this.$el.closest('.__panel')
      if (element) {
        return element
      }
      return document.body
    },
  },
}
</script>

<style lang="less">
.tippy-tooltip,
.tippy-backdrop {
  background-color: var(--tooltip-background-color);

  &.tippy-tooltip {
    padding: 0;
    color: var(--tooltip-text-color);
    background: var(--tooltip-background-color);
    border: var(--border-color);
    box-shadow: var(--tooltip-box-shadow);

    .@{ant-prefix}-tooltip {
      &-arrow {
        display: none;
      }

      &-inner {
        color: var(--tooltip-text-color);
        background: transparent;
        border: none;
        box-shadow: none;
      }
    }
  }
}
</style>
