<template>
  <div class="flex flex-col ml-6 min-h-0 flex-1 h-100">
    <div class="flex flex-col min-h-0 flex-1 justify-center">
      <div
        v-if="!hideImage"
        class="flex-1 min-h-0 text-center"
        style="max-height: 500px"
      >
        <ImageSvg class="h-full h-full" style="max-height: 500px" />
      </div>
      <div>
        <component :is="tag" class="text-center text-secondary-red my-2">
          {{ message }}
        </component>
        <div class="flex flex-col items-center">
          <MButton class="my-2" variant="default" @click="showModal = true">
            View Error
          </MButton>
          <MButton class="my-2" @click="$emit('remove-error')"> Retry </MButton>
        </div>
      </div>
    </div>
    <ErrorModal
      v-if="showModal"
      :error="error"
      :component="component"
      :reason="reason"
      @cancel="showModal = false"
    />
  </div>
</template>

<script>
import ImageSvg from './500.svg'
import ErrorModal from './error-modal.vue'

export default {
  name: 'ErrorShower',
  components: { ErrorModal, ImageSvg },
  props: {
    tag: {
      type: String,
      default: 'h4',
    },
    message: {
      type: String,
      default: 'Something went wrong! Please try again.',
    },
    hideImage: { type: Boolean, default: false },
    error: { type: [Object, Error], required: true },
    component: { type: Object, default: undefined },
    reason: { type: String, default: undefined },
  },
  data() {
    return {
      showModal: false,
    }
  },
}
</script>
