<script>
export default {
  name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
  inheritAttrs: false,
  data() {
    return {
      error: null,
      component: null,
      reason: null,
    }
  },
  errorCaptured(error, component, reason) {
    if (error.isAxiosError) {
      if (
        error.config.method.toLowerCase() === 'get' &&
        error.config.handle404
      ) {
        this.$router.replace({ name: '404' })
        return false
      }
      if (
        ['post', 'put', 'delete'].indexOf(error.config.method.toLowerCase()) >=
        0
      ) {
        return false
      }
    }
    this.error = error
    this.component = component
    this.reason = reason
    this.$emit('error', this.error)
    return false
  },
  methods: {
    removeError() {
      this.error = null
      this.component = null
      this.reason = null
    },
  },
  render(h) {
    if (this.error) {
      return h('ErrorShower', {
        props: {
          error: this.error,
          component: this.component,
          reason: this.reason,
          ...this.$attrs,
        },
        on: {
          'remove-error': this.removeError,
        },
      })
    }
    return this.$scopedSlots.default && this.$scopedSlots.default()
  },
}
</script>
