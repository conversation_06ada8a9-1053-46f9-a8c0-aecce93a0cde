<template>
  <MModal
    :open="open"
    :width="1020"
    overlay-class-name="scrollable-modal hide-footer floto-confirm-modal error-modal"
    @cancel="$emit('cancel')"
  >
    <template v-slot:trigger>
      <span />
    </template>
    <template v-slot:title>
      <div class="flex items-center">
        <div class="flex-1">
          <h4 class="mb-0 text-primary"> Error Information: {{ reason }} </h4>
        </div>
        <MButton
          variant="transparent"
          :shadow="false"
          shape="circle"
          @click="handleClose"
        >
          <MIcon name="times" class="text-neutral-light" />
        </MButton>
      </div>
    </template>
    <div class="flex flex-col min-h-0 h-100">
      <MTab v-model="tab">
        <MTabPane key="ui" tab="UI" />
        <MTabPane v-if="hasBackendError" key="backend" tab="Server" />
      </MTab>
      <div class="flex-1 flex flex-col min-h-0">
        <FlotoScrollView>
          <div v-if="tab === 'ui'">
            <div class="text-secondary-red">{{ reason }}</div>
            <div class="text-secondary-red">{{ error }}</div>
            <div class="text-secondary-red mb-4">
              Component: {{ component._name }}
            </div>
            <pre class="p-4 text-secondary-red" v-text="error.stack" />
          </div>
          <div v-if="tab === 'backend'">
            <div
              class="text-secondary-red"
              v-text="error.response.data.message"
            />
            <pre
              class="p-4 text-secondary-red"
              v-text="error.response.data.error"
            />
          </div>
        </FlotoScrollView>
      </div>
    </div>
    <template v-slot:footer>
      <span />
    </template>
  </MModal>
</template>

<script>
export default {
  name: 'ErrorModal',
  props: {
    error: { type: [Object, Error], required: true },
    component: { type: Object, required: true },
    reason: { type: String, required: true },
  },
  data() {
    return {
      open: true,
      tab: 'ui',
    }
  },
  computed: {
    hasBackendError() {
      const response = this.error.response
        ? this.error.response.data
        : undefined
      if (response) {
        return true
      }
      return false
    },
  },
  methods: {
    handleClose() {
      this.open = false
      setTimeout(() => {
        this.$emit('cancel')
      }, 400)
    },
  },
}
</script>

<style lang="less">
.error-modal {
  .@{ant-prefix}-modal-content {
    max-width: unset !important;
  }
}
</style>
