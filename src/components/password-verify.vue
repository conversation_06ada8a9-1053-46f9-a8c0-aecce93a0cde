<template>
  <div class="flex flex-1 min-h-0">
    <slot />
    <MModal
      :open="open"
      :width="520"
      :mask-closable="false"
      overlay-class-name="confirm-modal"
      @hide="hide"
    >
      <template v-slot:title>
        <h5 class="text-primary my-2">Enter your password</h5>
      </template>
      <MRow :gutter="0">
        <MCol :size="12" class="flex flex-col">
          <div
            v-if="error"
            class="mb-4 bg-secondary-red text-white px-2 py-1 rounded"
          >
            {{ error }}
          </div>
          <FlotoForm @submit="submitPasswordForm">
            <FlotoFormItem :value="username" label="Username" disabled />
            <PasswordInput
              v-model="password"
              auto-focus
              label="Password"
              rules="required"
            />
            <template v-slot:submit="{ submit }">
              <MRow>
                <MCol class="text-right">
                  <MButton :loading="processing" @click="submit">
                    Verify Password
                  </MButton>
                  <MButton class="ml-2" variant="default" @click="hide">
                    Cancel
                  </MButton>
                </MCol>
              </MRow>
            </template>
          </FlotoForm>
        </MCol>
      </MRow>

      <template v-slot:footer>
        <slot name="footer">
          <span />
        </slot>
      </template>
    </MModal>
  </div>
</template>

<script>
import { authComputed } from '@state/modules/auth'
import { verifyPasswordApi } from '@modules/auth/auth-api'
import PasswordInput from '@components/password-input.vue'

export default {
  name: 'PasswordVerify',
  components: { PasswordInput },
  provide() {
    const userAuthContext = {
      verifyPassword: this.verifyPassword,
    }
    return { userAuthContext }
  },
  data() {
    return {
      open: false,
      processing: false,
      password: '',
      error: null,
    }
  },
  computed: {
    ...authComputed,
    username() {
      return (this.user || {}).userName
    },
  },
  methods: {
    resetForm() {
      this.processing = false
      this.error = null
      this.password = ''
    },
    hide() {
      this.open = false
      setTimeout(() => {
        this.$emit('hide', 400)
      })
    },
    verifyPassword() {
      return new Promise((resolve, reject) => {
        this.resetForm()
        this.open = true
        this.resolve = resolve
        this.reject = reject
      })
    },
    submitPasswordForm() {
      this.error = null
      this.processing = true
      verifyPasswordApi(this.password, this.refreshToken)
        .then((data) => {
          this.hide()
          this.resolve()
        })
        .catch((e) => {
          this.error =
            ((e.response || {}).data || {}).message || 'Invalid credentials.'
        })
        .finally(() => (this.processing = false))
    },
  },
}
</script>
