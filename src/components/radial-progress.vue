<template>
  <div
    ref="containerRef"
    class="h-full w-full flex flex-col justify-center items-center"
  >
    <Progress
      v-if="diameter"
      progress-type="circle"
      :width="value"
      :radial-width="diameter"
      :trail-color="innerStrokeColor"
      :stroke-color="color"
      :stroke-width="strokeWidth"
      :stroke-linecap="strokeLinecap"
      :segment="segment"
      show-info
    >
      <template v-slot:format="slotData">
        <slot v-bind="slotData" />
      </template>
    </Progress>
    <slot v-if="diameter" name="bottom-header"></slot>
  </div>
</template>

<script>
import Progress from '@components/progress.vue'
export default {
  name: 'RadialProgress',
  components: {
    Progress,
  },
  inheritAttrs: false,
  props: {
    value: {
      type: Number,
      default: 0,
    },
    strokeWidth: {
      type: Number,
      default: undefined,
    },
    innerStrokeColor: {
      type: String,
      default: 'var(--gauge-base-color)',
    },
    color: {
      type: String,
      default: 'var(--primary)',
    },
    strokeLinecap: {
      type: String,
      default: 'round',
    },
    segment: {
      type: Number,
      default: 0,
    },
  },

  data() {
    return {
      diameter: undefined,
    }
  },

  mounted() {
    this.$_resizeObserver = new ResizeObserver(() => {
      this.diameter = Math.min(
        this.$refs.containerRef.offsetWidth,
        this.$refs.containerRef.offsetHeight
      )
    })

    this.$_resizeObserver.observe(this.$refs.containerRef)

    this.$once('hook:beforeDestroy', () => {
      if (this.$_resizeObserver) {
        this.$_resizeObserver.disconnect()
        this.$_resizeObserver = undefined
      }
    })

    this.diameter = Math.min(
      this.$refs.containerRef.offsetWidth,
      this.$refs.containerRef.offsetHeight
    )
  },
}
</script>
