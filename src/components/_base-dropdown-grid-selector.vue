<template>
  <FlotoDropdownPicker
    ref="dropdownPickerRef"
    :options="finalOptions"
    :value="value"
    v-bind="$attrs"
    :disabled="disabled"
    :searchable="false"
    :multiple="multiple"
    remove-clear-btn
    overlay-class-name="picker-overlay grid-dropdown"
    :get-popup-container="
      useBodyContainer ? getMonitorPickerContainer : undefined
    "
    avoid-keyboard-navigation
    @change="$emit('change', $event)"
    v-on="listeners"
  >
    <div class="mt-4 flex flex-col flex-1 min-h-0 px-4">
      <FlotoContentLoader :loading="loading">
        <div class="flex flex-col flex-1 min-h-0 min-w-0">
          <MRow :gutter="0">
            <MCol>
              <MInput
                id="assign-monitor-search"
                v-model="searchTerm"
                auto-focus
                class="search-box dropdown-search"
                placeholder="Search"
              >
                <template v-slot:prefix>
                  <MIcon name="search" />
                </template>
                <template v-if="searchTerm" v-slot:suffix>
                  <MIcon
                    name="times-circle"
                    class="text-neutral-light cursor-pointer"
                    @click="searchTerm = undefined"
                  />
                </template>
              </MInput>

              <MBadge
                :count="selectedItems.length"
                class="mx-2 primary-bg"
                @click="showSelected = !showSelected"
              />
              <a
                v-if="selectedItems.length && listMode !== 'selected'"
                @click="listMode = 'selected'"
              >
                View Selected
              </a>
              <a v-if="listMode !== 'all'" @click="listMode = 'all'">
                View All
              </a>
              <span
                v-if="selectedItems.length && !disablePreSelectedItem"
                class="mx-1"
              >
                |
              </span>
              <a
                v-if="selectedItems.length && !disablePreSelectedItem"
                @click="$emit('change', [])"
              >
                Clear Selected
              </a>
            </MCol>
          </MRow>

          <VirtualTable
            :columns="columns"
            :search-term="searchTerm"
            :data="data"
            default-sort="-name"
            selectable
            :disable-pre-selected-item="disablePreSelectedItem"
            :max-allowed-selection="maxAllowedSelections"
            :pre-selected-items="selectedItems"
            :filters="filters"
            v-bind="$attrs"
            @selection-change="handleSelection"
          >
            <template v-slot:groups="{ item }">
              <GroupPicker :value="item.groups" disabled />
            </template>
            <template v-slot:role="{ item }">
              <RolePicker :value="item.role" disabled />
            </template>
            <template v-slot:integrationType="{ item }">
              <ActiveIntegrationPicker :value="item.integrationType" disabled />
            </template>
            <template v-slot:type="{ item }">
              <div class="flex min-w-0 overflow-hidden">
                <div class="mr-2">
                  <MonitorType disable-tooltip :type="item.type" />
                </div>
                <div>
                  <MonitorType
                    v-if="item.isAgent"
                    disable-tooltip
                    :type="$constants.AGENT"
                  />
                </div>
              </div>
            </template>
          </VirtualTable>
        </div>
      </FlotoContentLoader>
    </div>
  </FlotoDropdownPicker>
</template>

<script>
import Throttle from 'lodash/throttle'
import VirtualTable from '@components/crud/virtual-table.vue'
import RolePicker from '@components/data-picker/role-picker.vue'
import ActiveIntegrationPicker from '@components/data-picker/active-integration-picker.vue'
import MonitorType from '@components/monitor-type.vue'

import { arrayWorker } from '@/src/workers'

export default {
  name: 'FlotoDropdownGridSelector',
  components: {
    VirtualTable,
    RolePicker,
    ActiveIntegrationPicker,
    MonitorType,
  },
  props: {
    defaultValue: { type: [Array], default: undefined },
    defaultMode: { type: String, default: 'all' },
    options: { type: Array, default: undefined },
    useBodyContainer: { type: Boolean, default: false },
    multiple: { type: Boolean, default: false },
    filters: { type: [Array], default: undefined },
    // eslint-disable-next-line
    searchable: { type: Boolean, default: true },
    value: {
      type: [Number, Array, String],
      default() {
        return []
      },
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    maxAllowedSelections: {
      type: Number,
      default: 0,
    },
    excludedOptions: {
      type: Array,
      default: undefined,
    },
    disablePreSelectedItem: { type: Boolean, default: false },

    columns: {
      type: Array,
      required: true,
    },
  },
  data() {
    let mode = this.defaultMode
    if (this.maxAllowedSelection !== 1 && this.value.length) {
      mode = 'selected'
    }
    return {
      loading: true,
      renderKey: 1,
      searchTerm: undefined,
      data: [],
      storedData: [],
      listMode: mode,
    }
  },
  computed: {
    listeners() {
      const { change, ...listeners } = this.$listeners
      return listeners
    },
    selectedItems() {
      return this.defaultValue || this.multiple
        ? this.value
        : this.value
        ? Array.isArray(this.value)
          ? this.value
          : [this.value]
        : undefined
    },
    finalOptions() {
      if (this.disabled) {
        return
      }

      let options = this.options

      if (this.excludedOptions) {
        const ids = this.excludedOptions

        options = options.filter(({ id }) => ids.includes(id) === false)
      }

      return options
    },
  },
  watch: {
    options: function () {
      this.renderKey++
    },
    storedData() {
      this.buildData()
    },
    listMode() {
      this.buildData()
    },
    value() {
      this.buildData()
    },
    finalOptions() {
      this.buildData()
    },
  },
  created() {
    this.buildData = Throttle(this.buildDataRaw, 750)
    this.buildData()
  },

  methods: {
    getMonitorPickerContainer() {
      return 'viewport'
    },
    async buildDataRaw() {
      const listMode = this.listMode
      const value = this.selectedItems
      const data = this.finalOptions
      this.data = data
      this.loading = false
      if (listMode === 'selected') {
        if (value.length === 0) {
          this.setListMode('all')
        }
        if (value.length > 0) {
          this.data = await arrayWorker.getItemsByIds(
            data,
            Array.isArray(this.value) ? this.value : [this.value]
          )
          return
        }
      } else if (this.listMode === 'unselected') {
        this.data = await arrayWorker.excludeFromList(data, this.value)
        return
      }
      this.data = data
    },
    setListMode(mode) {
      this.listMode = mode
    },

    handleSelection(event) {
      this.$emit('change', this.multiple ? event : event[0])
      if (!this.multiple) {
        if (this.$refs.dropdownPickerRef) {
          this.$refs.dropdownPickerRef.handleHide()
        }
      }
    },
  },
}
</script>
