<template>
  <div class="flex flex-col">
    <h5 class="text-primary-alt">{{ title }}</h5>
    <ul class="sample-download-list">
      <li v-for="file in list" :key="file.name">
        <a target="_blank" :href="file.link">
          {{ file.name }}
          <MIcon name="download" class="text-primary-alt ml-2" size="lg" />
        </a>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'SampleScriptList',
  props: {
    list: { type: Array, required: true },
    title: {
      type: String,
      default: 'Sample Parsing Script',
    },
  },
}
</script>
