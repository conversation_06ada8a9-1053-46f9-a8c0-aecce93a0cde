<template>
  <MModal
    ref="modal"
    centered
    :width="width"
    v-bind="$attrs"
    :mask-closable="false"
    :overlay-class-name="`confirm-modal ${
      variant === 'error' ? 'floto-confirm-modal' : variant
    }`"
    @success="$emit('confirm')"
    @hide="$emit('hide')"
  >
    <MRow :gutter="0">
      <MCol :size="12" class="flex">
        <MCol
          v-if="hideIcon !== true"
          :size="2"
          class="icon-container"
          :class="{ 'without-shadow': iconShadow === false }"
        >
          <slot name="icon"></slot>
        </MCol>
        <MCol
          :size="hideIcon ? 12 : 10"
          class="flex flex-col"
          :class="{ 'pop-content': !hideIcon }"
        >
          <MRow>
            <MCol :size="12" class="mb-3">
              <slot name="message"></slot>
            </MCol>
            <slot name="action-container">
              <MCol :size="12" class="action-container">
                <slot name="cancel-action" :handler="cancel">
                  <MButton
                    id="confirm-no"
                    variant="default"
                    @click.stop="cancel"
                  >
                    {{ cancelText }}
                  </MButton>
                </slot>

                <slot name="confirm-action" :handler="success">
                  <MButton
                    id="confirm-yes"
                    :variant="variant"
                    :outline="variant === 'error'"
                    @click.stop="success"
                  >
                    {{ successText }}
                  </MButton>
                </slot>
              </MCol>
            </slot>
          </MRow>
        </MCol>
      </MCol>
    </MRow>
    <template v-slot:footer>
      <slot name="footer">
        <span />
      </slot>
    </template>
  </MModal>
</template>

<script>
export default {
  name: 'FlotoConfirmModal',
  inheritAttrs: false,
  props: {
    hideIcon: { type: Boolean, default: false },
    open: { type: Boolean, default: false },
    iconShadow: { type: Boolean, default: false },
    variant: { type: String, default: 'error' },
    width: { type: [Number, String], default: 450 },
    successText: { type: String, default: 'Yes' },
    cancelText: { type: String, default: 'No' },
  },
  watch: {
    open: {
      handler: 'toggleModal',
    },
  },
  mounted() {
    this.toggleModal(this.open)
  },
  methods: {
    toggleModal(newValue) {
      if (newValue) {
        this.openModal()
      } else {
        this.closeModal()
      }
    },
    openModal() {
      if (this.$refs.modal) {
        this.$refs.modal.show()
      }
    },
    closeModal() {
      if (this.$refs.modal) {
        this.$refs.modal.hide()
      }
    },
    success() {
      this.$emit('confirm')
      this.$nextTick(() => {
        this.closeModal()
      })
    },
    cancel() {
      this.closeModal()
      this.$emit('hide')
    },
  },
}
</script>

<style lang="less">
.floto-confirm-modal {
  .@{ant-prefix}-modal-content {
    max-width: 500px;
  }

  .icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    margin-right: 1rem;
    border-radius: 50%;
    box-shadow: 2px 2px 30px -4px rgba(0, 0, 0, 0.6);

    &.without-shadow {
      box-shadow: none;
    }
  }
  .@{ant-prefix}-modal-header {
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
  }
}
</style>
