<template>
  <SortableList
    :value="value"
    :child-key="childKey"
    :disabled="!sortable"
    :class="{
      'sortable-list': level === 1,
      'sortable-list-child': level !== 1,
      'wide-gap': levelMargin > 20,
    }"
    :group-name="`sortable-${level}`"
    :sort="false"
    ghost-class="test"
    chosen-class="test"
    :allow-drop="level <= maxLevel"
    filter=".not-draggable"
    :level="level"
    v-bind="$attrs"
    @add="$emit('add', $event, parent)"
    @remove="$emit('remove', $event, parent)"
    @select-item="$emit('select-item', $event)"
  >
    <template v-slot:item="{ item }">
      <slot
        name="item"
        :item="item"
        :toggle="() => handleItemExpand(item)"
        :level="level"
        :parent="parent"
      >
        {{ item.name }}
      </slot>
      <NestedSortableList
        v-if="
          (expandedKeys.includes(item.key || item.id) ||
            (item[childKey] || []).length === 0) &&
          level < maxLevel
        "
        :value="item[childKey] || []"
        :max-level="maxLevel"
        :expanded-keys="expandedKeys"
        :sortable="sortable"
        :style="{ paddingLeft: `${levelMargin}px` }"
        :level="level + 1"
        :level-margin="levelMargin"
        :parent="item"
        :child-key="childKey"
        v-bind="$attrs"
        v-on="listeners"
        @select-item="$emit('select-item', $event)"
      >
        <template v-slot:item="childSlotData">
          <slot
            name="item"
            v-bind="childSlotData"
            :toggle="() => handleItemExpand(childSlotData.item)"
          >
            {{ childSlotData.item.name }}
          </slot>
        </template>
      </NestedSortableList>
    </template>
  </SortableList>
</template>

<script>
import Omit from 'lodash/omit'
import SortableList from './sortable-list.vue'

export default {
  name: 'NestedSortableList',
  components: { SortableList },
  props: {
    sortable: { type: Boolean, required: true },
    value: { type: Array, required: true },
    childKey: { type: String, default: 'children' },
    maxLevel: { type: Number, default: 3 },
    level: { type: Number, default: 1 },
    levelMargin: { type: Number, default: 20 },
    parent: { type: Object, default: undefined },
    expandedKeys: {
      type: Array,
      default() {
        return []
      },
    },
  },
  computed: {
    listeners() {
      return Omit(this.$listeners, ['change', 'select-item'])
    },
  },
  methods: {
    handleItemExpand(item) {
      const index = this.expandedKeys.indexOf(item.key || item.id)
      if (index === -1) {
        this.$emit('toggle-expand', [...this.expandedKeys, item.key || item.id])
      } else {
        this.$emit('toggle-expand', [
          ...this.expandedKeys.slice(0, index),
          ...this.expandedKeys.slice(index + 1),
        ])
      }
    },
  },
}
</script>
