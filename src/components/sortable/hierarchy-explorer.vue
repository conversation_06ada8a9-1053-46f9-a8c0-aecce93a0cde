<template>
  <div class="flex flex-col hierarchy-explorer">
    <NestedSortableList
      :value="data"
      :child-key="childKey"
      :sortable="sortable"
      :max-level="maxLevel"
      v-bind="$attrs"
      :expanded-keys="expandedKeys"
      v-on="listeners"
      @select-item="handleEmitValue"
      @toggle-expand="expandedKeys = $event"
    >
      <template v-slot:item="{ item, toggle, parent, level }">
        <div
          class="hierarchy-item flex items-center"
          :data-level="level"
          :class="{
            'cursor-move': sortable && item.draggable !== false,
            'not-draggable': item.draggable === false,
            'mb-3': useMargin,
          }"
        >
          <div
            class="title-container flex flex-1 items-center min-w-0"
            :class="{
              'with-bg': useBg,
              seperator: useSeparator,
              active: activeItem && activeItem.id === item.id,
            }"
          >
            <div
              class="mr-2 ml-1 cursor-pointer text-neutral-light font-bold"
              :class="{
                invisible: (item[childKey] || []).length === 0,
                hidden: removeExpandIcon,
              }"
              size="lg"
              @click.stop="toggle"
            >
              <MIcon
                :name="`chevron-${
                  expandedKeys.indexOf(item.key || item.id) >= 0
                    ? 'down'
                    : 'right'
                }`"
              />
            </div>
            <slot
              :item="item"
              :level="level"
              :toggle="toggle"
              :parent="parent"
              :toggleExpand="toggle"
            />
          </div>
        </div>
      </template>
    </NestedSortableList>
  </div>
</template>

<script>
import Omit from 'lodash/omit'
import CloneDeep from 'lodash/cloneDeep'
import NestedSortableList from '@components/sortable/nested-sortable-list.vue'

export default {
  name: 'HierarchyExplorer',
  components: { NestedSortableList },
  model: {
    prop: 'activeItem',
    event: 'change',
  },
  props: {
    sortable: { type: Boolean, default: false },
    // eslint-disable-next-line
    useBg: { type: Boolean, default: true },
    // eslint-disable-next-line
    useMargin: { type: Boolean, default: true },
    canAdd: { type: Boolean, default: false },
    childKey: { type: String, default: 'children' },
    activeItem: { type: Object, default: undefined },
    useSeparator: { type: Boolean, default: false },
    hierarchy: { type: Array, required: true },
    maxLevel: { type: Number, default: 5 },
    removeExpandIcon: { type: Boolean, default: false },
    defaultExpandedKeys: {
      type: Array,
      default() {
        return []
      },
    },
  },
  data() {
    return {
      data: CloneDeep(this.hierarchy),
      expandedKeys: [],
    }
  },
  computed: {
    listeners() {
      return Omit(this.$listeners, ['toggle-expand', 'click'])
    },
  },
  watch: {
    defaultExpandedKeys: {
      immediate: true,
      handler(newValue) {
        this.expandedKeys = newValue
      },
    },
    hierarchy(newValue) {
      this.data = CloneDeep(newValue)
    },
  },
  methods: {
    toggleExpand(key) {
      const index = this.expandedKeys.indexOf(key)
      if (index === -1) {
        this.expandedKeys = [...this.expandedKeys, key]
      } else {
        this.expandedKeys = [
          ...this.expandedKeys.slice(0, index),
          ...this.expandedKeys.slice(index + 1),
        ]
      }
    },
    handleEmitValue(item) {
      this.$emit('change', item)
      const activeItem = this.activeItem
      if (activeItem && activeItem.id === item.id) {
        this.toggleExpand(item.id)
        return
      }
      if (!this.expandedKeys.includes(item.id)) {
        this.expandedKeys = [...this.expandedKeys, item.id]
      }
    },
  },
}
</script>
