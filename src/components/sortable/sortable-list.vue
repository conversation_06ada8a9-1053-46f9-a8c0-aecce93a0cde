<template>
  <Draggable
    v-if="!disabled"
    :value="value"
    :group="{ name: groupName, put: putFn }"
    :animation="150"
    :fallback-on-body="true"
    :swap-threshold="0.65"
    :sort="sort"
    :ghost-class="ghostClass"
    :disabled="disabled"
    :chosen-class="chosenClass"
    :tag="tag"
    v-bind="$attrs"
    @change="handleChange"
    @start="handleDragStart"
    @end="handleDragEnd"
  >
    <slot>
      <li
        v-for="item in value"
        :id="`${item.name}-group`"
        :key="item.id || item.guid || item.key"
        class="sortable-item"
        :class="{ 'not-draggable': item.draggable === false }"
        @click.stop="$emit('select-item', item)"
      >
        <slot :item="item" name="item" />
      </li>
    </slot>
  </Draggable>
  <ul v-else>
    <slot>
      <li
        v-for="item in value"
        :key="item.id || item.guid || item.key"
        class="sortable-item"
        @click.stop="$emit('select-item', item)"
      >
        <slot :item="item" name="item" />
      </li>
    </slot>
  </ul>
</template>

<script>
import CloneDeep from 'lodash/cloneDeep'
import Draggable from 'vuedraggable'
import { move } from '@utils/arr'

export default {
  name: 'SortableList',
  components: { Draggable },
  model: { event: 'change' },
  props: {
    disabled: { type: Boolean, default: false },
    sort: { type: Boolean, default: false },
    value: { type: Array, required: true },
    groupName: { type: String, default: 'sortable' },
    ghostClass: { type: String, default: undefined },
    chosenClass: { type: String, default: undefined },
    tag: { type: String, default: 'ul' },
    level: { type: Number, default: 1 },
    // eslint-disable-next-line
    allowDrop: { type: Boolean, default: true },
  },
  methods: {
    putFn() {
      if (this.allowDrop) {
        return true
      }
      return false
    },
    handleChange(data) {
      if (data.moved) {
        const newIndex = data.moved.newIndex
        const oldIndex = data.moved.oldIndex
        const newData = move(CloneDeep(this.value), oldIndex, newIndex)
        const updatedData = newData.slice(
          Math.min(oldIndex, newIndex),
          Math.max(newIndex, oldIndex) + 1
        )
        this.$emit('change', newData)
        this.$emit('update', {
          items: newData,
          updatedItems: updatedData,
          oldData: this.value,
        })
      }
      if (data.added) {
        this.$emit('add', {
          item: data.added.element,
          index: data.added.newIndex,
        })
      }
      if (data.removed) {
        this.$nextTick(() => {
          this.$emit('remove', {
            item: data.removed.element,
            index: data.removed.oldIndex,
          })
        })
      }
    },
    handleDragStart($event) {
      this.$emit('drag-start', $event)
    },
    handleDragEnd($event) {
      this.$emit('drag-end', $event)
    },
  },
}
</script>

<style lang="less">
ul.sortable-list {
  padding: 0;
  margin: 0;
  list-style: none;
}
</style>
