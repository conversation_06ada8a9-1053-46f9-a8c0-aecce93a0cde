<template>
  <div class="flex flex-1 flex-col min-h-0 overflow-auto hierarchy-explorer">
    <RecycleScroller
      ref="virtualListRef"
      class="flex-1"
      :items="nodes"
      :key-field="keyField"
      :item-size="rowHeight"
      @visible="selectInitialValue"
    >
      <template v-slot="{ item }">
        <div :style="{ height: `${rowHeight}px` }" class="py-1 flex flex-1">
          <TreeNodeWrapper
            :node="item"
            :tree="tree"
            :state="item.state"
            @click="handleSelectItem(item)"
          >
            <slot :item="item" :tree="tree">
              {{ item.name }}
            </slot>
          </TreeNodeWrapper>
        </div>
      </template>
    </RecycleScroller>
  </div>
</template>

<script>
import Debounce from 'lodash/debounce'
import InfiniteTree from 'infinite-tree'
// import Bus from '@utils/emitter'
import { objectDBWorker } from '@/src/workers'
import { SEVERITY_MAP } from '@data/monitor'
import TreeNodeWrapper from './tree-node-wrapper.vue'

export default {
  name: 'InfiniteTree',
  components: {
    TreeNodeWrapper,
  },
  model: { event: 'change' },
  props: {
    keyField: {
      type: String,
      default: 'key',
    },
    openIds: {
      type: Array,
      default() {
        return []
      },
    },
    data: {
      type: [Array],
      default() {
        return []
      },
    },
    rowHeight: {
      type: Number,
      default: 35,
    },
    value: {
      type: Object,
      default: undefined,
    },
    searchTerm: {
      type: String,
      default: undefined,
    },
    filterFn: {
      type: Function,
      default: undefined,
    },
  },
  data() {
    return {
      nodes: [],
    }
  },
  watch: {
    data(newValue) {
      if (this.tree) {
        this.tree.loadData(newValue)
        setTimeout(() => {
          this.selectInitialValue()
        }, 200)
      }
    },
    value(newValue, oldValue) {
      if (newValue !== oldValue) {
        if (newValue && newValue.id && newValue.resourceType) {
          const selectedNode = this.tree.getSelectedNode()
          if (selectedNode) {
            if (
              selectedNode.resourceId === newValue.id &&
              selectedNode.resourceType === newValue.resourceType
            ) {
              if (
                ['application', 'vm', 'access.point'].includes(
                  newValue.resourceType
                )
              ) {
                if (newValue.name === selectedNode.name) {
                  return
                }
              } else {
                return
              }
            }
          }
          setTimeout(() => {
            this.selectInitialValue()
          }, 50)
        }
      }
    },
    searchTerm(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.filter()
      }
    },
    openIds(newValue, oldValue) {
      if (newValue !== oldValue) {
        if (this.openIds.length) {
          this.openIds.forEach((id) => {
            const node = this.tree.getNodeById(id)
            if (node) {
              this.tree.openNode(node)
            }
          })
        }
      }
    },
  },
  created() {
    this.filter = Debounce(this.filterRaw, 750, { trailing: true })
    const options = { data: this.data, ...this.$attrs }

    options.rowRenderer = () => ''

    this.tree = new InfiniteTree(options)

    if (this.openIds.length) {
      this.openIds.forEach((id) => {
        const node = this.tree.getNodeById(id)
        if (node) {
          this.tree.openNode(node)
        }
      })
    }

    // Filters nodes.
    // https://github.com/cheton/infinite-tree/wiki/Functions:-Tree#filterpredicate-options
    const treeFilter = this.tree.filter.bind(this.tree)
    this.tree.filter = (...args) => {
      treeFilter(...args)
      const virtualList = this.$refs.virtualListRef
      if (virtualList) {
        virtualList.$forceUpdate()
        virtualList.scrollToPosition(0)
        virtualList.updateVisibleItems()
      }
    }

    // Unfilter nodes.
    // https://github.com/cheton/infinite-tree/wiki/Functions:-Tree#unfilter
    const treeUnfilter = this.tree.unfilter.bind(this.tree)
    this.tree.unfilter = (...args) => {
      treeUnfilter(...args)
      const virtualList = this.$refs.virtualListRef
      if (virtualList) {
        virtualList.$forceUpdate()
        virtualList.scrollToPosition(0)
        virtualList.updateVisibleItems()
      }
    }

    // Sets the current scroll position to this node.
    // @param {Node} node The Node object.
    // @return {boolean} Returns true on success, false otherwise.
    this.tree.scrollToNode = (node) => {
      const virtualList = this.$refs.virtualListRef

      if (!this.tree || !virtualList) {
        return false
      }

      const nodeIndex = this.tree.nodes.indexOf(node)
      if (nodeIndex < 0) {
        return false
      }

      virtualList.scrollToItem(nodeIndex)

      return true
    }

    // Gets (or sets) the current vertical position of the scroll bar.
    // @param {number} [value] If the value is specified, indicates the new position to set the scroll bar to.
    // @return {number} Returns the vertical scroll position.
    this.tree.scrollTop = (value) => {
      const virtualList = this.$refs.virtualListRef

      if (!this.tree || !virtualList) {
        return
      }

      if (value !== undefined) {
        virtualList.scrollToPosition(Number(value))
      } else {
        virtualList.scrollToPosition(0)
      }

      return virtualList.getScroll().start
    }

    // Updates the tree.
    this.tree.update = () => {
      this.tree.emit('contentWillUpdate')
      if (this.searchTerm) {
        const nodes = this.tree.nodes.filter((node) => {
          return node.state.filtered !== false
        })
        this.nodes = nodes
      } else {
        this.nodes = this.tree.nodes.map((n) => n)
      }
      this.tree.emit('contentDidUpdate')
    }
    this.nodes = this.tree.nodes

    this.tree.on('selectNode', (node) => {
      const virtualList = this.$refs.virtualListRef

      if (!this.tree || !virtualList) {
        return
      }
      virtualList.$forceUpdate()
    })

    // Bus.$on(this.$constants.EVENT_SEVERITY_UPDATED, this.monitorSeverityUpdated)
    // Bus.$on(
    //   this.$constants.EVENT_COUNTER_SEVERITY_UPDATED,
    //   this.instanceSeverityUpdated
    // )

    this.selectInitialValue = Debounce(this.selectInitialValueRaw, 100, {
      trailing: true,
    })
  },

  beforeDestroy() {
    // Bus.$off(
    //   this.$constants.EVENT_SEVERITY_UPDATED,
    //   this.monitorSeverityUpdated
    // )
    // Bus.$off(
    //   this.$constants.EVENT_COUNTER_SEVERITY_UPDATED,
    //   this.instanceSeverityUpdated
    // )
    this.tree.destroy()
    this.tree = null
  },

  methods: {
    selectInitialValueRaw() {
      setTimeout(() => {
        if (this.value && this.value.id) {
          const tree = this.tree
          if (this.value.resourceType === 'monitor') {
            const id = this.value.id
            const nodes = Object.keys(tree.nodeTable.data)
              .filter(
                (nodeId) =>
                  tree.nodeTable.data[nodeId].resourceId === id &&
                  tree.nodeTable.data[nodeId].resourceType === 'monitor'
              )
              .map((nodeId) => tree.nodeTable.data[nodeId])
            if (nodes && nodes.length > 0) {
              let node
              if (this.value.key) {
                node = nodes.find((n) => n.key === this.value.key) || nodes[0]
              } else {
                node = nodes[0]
              }
              if (
                !tree.getSelectedNode() ||
                (tree.getSelectedNode() &&
                  tree.getSelectedNode().id !== node.id)
              ) {
                tree.selectNode(node)
              }
              tree.scrollToNode(node)
            }
          } else if (
            ['application', 'vm', 'access.point'].includes(
              this.value.resourceType
            )
          ) {
            const id = this.value.id
            const resourceType = this.value.resourceType
            const application = this.value.name
            const nodes = Object.keys(tree.nodeTable.data)
              .filter(
                (nodeId) =>
                  tree.nodeTable.data[nodeId].resourceId === id &&
                  tree.nodeTable.data[nodeId].name === application &&
                  tree.nodeTable.data[nodeId].resourceType === resourceType
              )
              .map((nodeId) => tree.nodeTable.data[nodeId])
            if (nodes && nodes.length > 0) {
              let node
              if (this.value.key) {
                node = nodes.find((n) => n.key === this.value.key) || nodes[0]
              } else {
                node = nodes[0]
              }
              if (
                !tree.getSelectedNode() ||
                (tree.getSelectedNode() &&
                  tree.getSelectedNode().id !== node.id)
              ) {
                tree.selectNode(node)
              }
              tree.scrollToNode(node)
            }
          } else {
            const selectedNode = tree.getNodeById(this.value.id)
            if (selectedNode) {
              if (
                !tree.getSelectedNode() ||
                (tree.getSelectedNode() &&
                  tree.getSelectedNode().id !== selectedNode.id)
              ) {
                tree.selectNode(selectedNode)
              }
              tree.scrollToNode(selectedNode)
            }
          }
        }
      }, 400)
    },
    handleSelectItem(item) {
      this.$emit('change', item, this.tree)
    },
    async monitorSeverityUpdated(monitorId, severity) {
      const tree = this.tree
      const nodes = Object.keys(this.tree.nodeTable.data)
        .filter((nodeId) => {
          return (
            tree.nodeTable.data[nodeId].resourceId === monitorId &&
            !tree.nodeTable.data[nodeId].instance &&
            tree.nodeTable.data[nodeId].parent.resourceId !== monitorId
          )
        })
        .map((nodeId) => tree.nodeTable.data[nodeId])
      if (!nodes.length) {
        return
      }

      nodes.forEach(async (node) => {
        tree.updateNode(node, {
          severity,
          severityNumber: SEVERITY_MAP[severity],
        })
        let parent = node.getParent()
        while (parent.id) {
          const maxSeverity = await objectDBWorker.getMaxSeverityFromObjects(
            parent.getChildren()
          )
          tree.updateNode(parent, maxSeverity)
          parent = parent.getParent()
        }
      })
    },
    async instanceSeverityUpdated(payload) {
      if (!payload) {
        return
      }
      const tree = this.tree
      const nodes = Object.keys(this.tree.nodeTable.data)
        .filter(
          (nodeId) =>
            tree.nodeTable.data[nodeId].resourceId === payload.objectId &&
            payload.instance === tree.nodeTable.data[nodeId].instance
        )
        .map((nodeId) => tree.nodeTable.data[nodeId])
      if (!nodes.length) {
        return
      }

      nodes.forEach(async (node) => {
        tree.updateNode(node, {
          severity: payload.severity,
          severityNumber: SEVERITY_MAP[payload.severity],
        })

        let parent = node.getParent()
        while (parent.id && parent.resourceType !== 'monitor') {
          const maxSeverity = await objectDBWorker.getMaxSeverityFromObjects([
            ...parent.getChildren(),
          ])
          tree.updateNode(parent, maxSeverity)
          parent = parent.getParent()
        }
      })
    },
    filterRaw() {
      if (!this.tree) {
        return
      }
      const term = this.searchTerm
      if ((term || '').length) {
        this.tree.filter(
          this.filterFn ? (node) => this.filterFn(node, term) : term,
          {
            filterPath: 'name',
            caseSensitive: false,
            exactMatch: false,
            includeAncestors: true,
            includeDescendants: true,
          }
        )
      } else {
        this.tree.unfilter()
      }
    },
  },
}
</script>
