<template>
  <div
    class="flex flex-1 items-center h-full hierarchy-item relative"
    :style="style"
    v-on="$listeners"
  >
    <div
      class="title-container flex flex-1 items-center min-w-0"
      :class="{ active: selected }"
    >
      <slot />
    </div>
    <!-- <span
      v-if="node.state.depth > 0"
      class="vertical-line"
      :style="{
        left: `0`,
        top: '-15px',
        bottom: '50%',
      }"
    />
    <span
      v-if="node.state.depth > 0"
      class="horizontal-line"
      :style="{
        width: `${marginLevel}px`,
        left: 0,
      }"
    /> -->
  </div>
</template>

<script>
export default {
  name: 'TreeNode',
  props: {
    selected: {
      type: Boolean,
      default: false,
    },
    node: {
      type: Object,
      required: true,
    },
    tree: {
      type: Object,
      required: true,
    },
    marginLevel: {
      type: Number,
      default: 20,
    },
  },
  computed: {
    style() {
      return {
        marginLeft: `${this.depth * this.marginLevel}px`,
      }
    },
    depth() {
      return this.node.state.depth
    },
  },
}
</script>

<style lang="less" scoped>
.horizontal-line,
.vertical-line {
  position: absolute;
  background: var(--border-color);
}

.horizontal-line {
  height: 1px;
}

.vertical-line {
  width: 1px;
}
</style>
