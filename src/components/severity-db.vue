<template>
  <div />
</template>

<script>
import { severityDBWorker } from '@/src/workers'
import Bus from '@utils/emitter'
import { generateId } from '../utils/id'

export default {
  name: 'SeverityDb',
  data() {
    return {
      guid: generateId(),
    }
  },
  mounted() {
    this.fillInitialSeverity()
  },
  methods: {
    fillInitialSeverity() {
      const handleAllSeverityReceived = async (data) => {
        await this.fillSeverityInDb(data)
        setTimeout(() => this.$emit('severity-received'), 400)
      }
      Bus.$on('severity.query', handleAllSeverityReceived)
      Bus.$emit('server:event', {
        'event.type': 'severity.query',
        'event.context': {
          [this.$constants.UI_EVENT_UUID]: this.guid,
        },
      })
      this.$once('hook:beforeDestroy', () => {
        Bus.$off('severity.query', handleAllSeverityReceived)
      })
    },
    async fillSeverityInDb(result) {
      let allSeverities = Object.keys(result['object.severity'] || {}).map(
        (key) => ({
          id: +key,
          entity: +key,
          severity: result['object.severity'][key],
        })
      )
      // const instanceSeverities = Object.keys(
      //   result['instance.severity'] || {}
      // ).map((key) => ({
      //   'policy.key': key,
      //   id: +key.split('``||``')[0],
      //   entity: +key.split('``||``')[0],
      //   instance: key.split('``||``')[1],
      //   severity: result['instance.severity'][key],
      // }))
      // const applicationSeverities = Object.keys(
      //   result['application.severity'] || {}
      // ).map((key) => ({
      //   'policy.key': key,
      //   id: +key.split('``||``')[0],
      //   entity: +key.split('``||``')[0],
      //   instance: key.split('``||``')[1],
      //   severity: result['application.severity'][key],
      // }))
      // let allSeverities = [
      //   ...objectSeverities,
      // ...instanceSeverities,
      // ...applicationSeverities,
      // ]
      if (allSeverities.length > 0) {
        await severityDBWorker.updateBulk(allSeverities, true, true)
        // for (let item of allSeverities) {
        //   if (item.instance || item.counter) {
        //     Bus.$emit(this.$constants.EVENT_COUNTER_SEVERITY_UPDATED, {
        //       objectId: item.id,
        //       instance: item.instance,
        //       severity: item.severity,
        //     })
        //   } else {
        //     Bus.$emit(
        //       this.$constants.EVENT_SEVERITY_UPDATED,
        //       item.id,
        //       item.severity
        //     )
        //   }
        // }
        Bus.$emit(
          this.$constants.EVENT_SEVERITY_COUNTER_DB_CHANGED,
          result['object.severity'] || {}
        )
        allSeverities = null
        // objectSeverities = null
        // instanceSeverities = null
        // applicationSeverities = null
        result = null
      }
    },
  },
}
</script>
