<template>
  <div class="flex" :class="{ 'flex-col': !horizontal }">
    <div v-for="(currentItem, index) in value" :key="currentItem.key">
      <slot
        :item="currentItem"
        :index="index"
        :isFirstItem="index === 0"
        :isLastItem="index === value.length - 1"
        :total="value.length"
        :remove="() => removeItem(currentItem.key)"
        :add="addItem"
        :canAdd="maxItems ? value.length < maxItems : true"
        :disabled="disabled"
        :update="(payload) => updateItem(payload, currentItem.key)"
      >
        Please Provide item to render
      </slot>
    </div>
    <slot v-if="value.length === 0 && !disabled" name="add-item" :add="addItem">
      <div>
        <MButton outline @click="addItem(itemTemplate)">
          <MIcon v-if="showIcon" name="plus-circle" /> {{ addBtnText }}
        </MButton>
      </div>
    </slot>
  </div>
</template>

<script>
import CloneDeep from 'lodash/cloneDeep'
import FindIndex from 'lodash/findIndex'
import { generateId } from '@utils/id'

export default {
  name: 'MultipleFormItem',
  model: { event: 'change' },
  props: {
    disabled: { type: Boolean, default: false },
    value: {
      type: [Array, String],
      default() {
        return []
      },
    },
    // eslint-disable-next-line
    showIcon: { type: Boolean, default: true },
    addBtnText: { type: String, default: 'Add' },
    maxItems: { type: Number, default: undefined },
    itemTemplate: { type: [Object, String, Number, Array], default: undefined },
    horizontal: { type: Boolean, default: false },
  },
  methods: {
    addItem(payload = {}) {
      if (this.maxItems && this.maxItems <= this.value.length) {
        return
      }
      this.$emit('change', [
        ...this.value,
        {
          key: generateId(),
          ...CloneDeep(this.itemTemplate || {}),
          ...CloneDeep(payload),
        },
      ])
    },
    removeItem(key) {
      this.$emit(
        'change',
        this.value.filter((i) => key !== i.key)
      )
    },
    updateItem(value, key) {
      const index = FindIndex(this.value, { key })
      if (index !== -1) {
        this.$emit('change', [
          ...this.value.slice(0, index),
          { ...this.value[index], ...value },
          ...this.value.slice(index + 1),
        ])
      }
    },
  },
}
</script>
