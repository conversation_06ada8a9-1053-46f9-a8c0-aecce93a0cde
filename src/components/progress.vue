<template>
  <AProgress
    :percent="width"
    :show-info="showInfo"
    :size="size"
    :status="type === 'error' ? 'exception' : type"
    :type="progressType"
    :width="radialWidth"
    :success-percent="segment"
    v-bind="$attrs"
  >
    <!-- :success-percent="segment" -->
    <template v-for="(_, name) in $scopedSlots" v-slot:[name]="nestedSlot">
      <slot :name="name" v-bind="nestedSlot || {}" />
    </template>
  </AProgress>
</template>

<script>
import Progress from 'ant-design-vue/es/progress'
import 'ant-design-vue/es/progress/style'

export default {
  name: 'Progress',
  components: { AProgress: Progress },
  props: {
    size: { type: String, default: 'small' },
    width: { type: Number, default: 0 },
    showInfo: { type: Boolean, default: false },
    type: { type: String, default: 'success' },
    progressType: { type: String, default: 'line' },
    radialWidth: {
      type: Number,
      default: undefined,
    },
    segment: {
      type: Number,
      default: 0,
    },
  },
}
</script>

<style lang="less">
.@{ant-prefix}-progress-large {
  .@{ant-prefix}-progress-bg {
    height: 12px !important;
  }
}
</style>
