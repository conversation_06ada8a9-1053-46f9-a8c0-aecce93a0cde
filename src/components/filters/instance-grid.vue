<template>
  <FlotoDrawerForm
    :open="isDrawerOpen"
    width="60%"
    :wrap-style="{ zIndex: 1056 }"
    @submit="handleSubmit"
    @cancel="isDrawerOpen = false"
    @reset="handleReset"
  >
    <template v-slot:header>Select Instance</template>
    <template v-slot:trigger>
      <slot name="trigger" :open="() => (isDrawerOpen = true)">
        <a class="flex mt-3" @click="isDrawerOpen = true">
          {{ selectedValue.length > 0 ? 'Selected' : 'Select' }}
          Instance
          {{ selectedValue[0] }}
          <template v-if="selectedValue.length > 1"
            >(+{{ selectedValue.length - 1 }})</template
          >
        </a>
      </slot>
    </template>

    <div class="flex flex-col flex-1 min-h-0 min-w-0">
      <MRow :gutter="0">
        <MCol>
          <MInput
            id="assign-monitor-search"
            v-model="searchTerm"
            class="search-box"
            placeholder="Search"
          >
            <template v-slot:prefix>
              <MIcon name="search" />
            </template>
            <template v-if="searchTerm" v-slot:suffix>
              <MIcon
                name="times-circle"
                class="text-neutral-light cursor-pointer"
                @click="searchTerm = undefined"
              />
            </template>
          </MInput>
        </MCol>
        <MCol class="text-right">
          <ColumnSelector
            v-model="columns"
            :columns="availableColumns"
            :hide-reset-option="true"
          />
        </MCol>
      </MRow>
      <VirtualTable
        :columns="columns"
        :search-term="searchTerm"
        :data="rowData"
        data-item-key="guid"
        :paging="false"
        selectable
        :pre-selected-items="selectedValue"
        @selection-change="selectedValue = $event"
      >
        <template v-slot:monitor="{ item }">
          <MonitorName :value="item.monitor" :row="item" />
        </template>
        <template v-slot:status="{ item, props }">
          <MStatusTag :status="item[props.field]" />
        </template>
      </VirtualTable>
    </div>

    <template v-slot:actions="{ submit, reset }">
      <MButton id="reset-btn" variant="default" class="mr-2" @click="reset"
        >Reset</MButton
      >
      <MButton id="submit-btn" @click="submit">Save</MButton>
    </template>
  </FlotoDrawerForm>
</template>

<script>
import Uniq from 'lodash/uniq'
import CloneDeep from 'lodash/cloneDeep'
import FindIndex from 'lodash/findIndex'
import VirtualTable from '@components/crud/virtual-table.vue'
import MonitorName from '@components/widgets/views/grid/view-more/monitor-name.vue'
import ColumnSelector from '@components/column-selector.vue'
import { generateId } from '@utils/id'

export default {
  name: 'InstanceGrid',
  components: {
    VirtualTable,
    MonitorName,
    ColumnSelector,
  },
  model: { event: 'change' },
  props: {
    keyField: {
      type: String,
      default: undefined,
    },
    data: {
      type: Object,
      required: true,
    },
    value: {
      type: Array,
      default: undefined,
    },
    operand: {
      type: String,
      required: true,
    },
  },
  data() {
    const columns = this.data.columns.map((c) => ({
      key: c.key.replace(/[~\.^]/g, '_'),
      name: c.text,
      searchable: true,
      sortable: true,
      ...(c.text.indexOf('.status') > 0 ? { cellRender: 'status' } : {}),
    }))
    const firstElementIndex = FindIndex(columns, {
      key: this.operand.replace(/[~\.^]/g, '_'),
    })
    const disabledColumns = [columns[firstElementIndex].key, 'monitor']
    const gridColumns = [
      columns[firstElementIndex],
      ...columns.slice(0, firstElementIndex),
      ...columns.slice(firstElementIndex + 1),
    ].map((c, index) => ({
      ...c,
      hidden: index > 5,
      disable: disabledColumns.includes(c.key),
    }))
    return {
      isDrawerOpen: false,
      searchTerm: undefined,
      selectedValue: [],
      columns: gridColumns,
      availableColumns: Object.freeze(CloneDeep(gridColumns)),
    }
  },
  computed: {
    rowData() {
      const keyField = this.keyField
      return this.data.data.map((data) => {
        let obj = {}
        Object.keys(data).forEach((c) => {
          obj[c.replace(/\./g, '_')] = data[c]
        })
        if (keyField) {
          obj.id = `${data[keyField]}`
        }
        obj.guid = generateId()
        return obj
      })
    },
  },
  watch: {
    value: {
      immediate: true,
      handler(newValue) {
        if (newValue) {
          const operand = this.operand
          const values = Uniq(
            this.rowData
              .filter(
                (data) =>
                  newValue.indexOf(data[operand.replace(/\./g, '_')]) >= 0
              )
              .map((item) => item.id)
          )
          this.selectedValue = CloneDeep(values)
        }
      },
    },
  },
  methods: {
    getResolvedSelectedValue() {
      const selectedValue = this.selectedValue
      const operand = this.operand.replace(/\./g, '_')
      return Uniq(
        this.rowData
          .filter((data) => selectedValue.indexOf(data.id) >= 0)
          .map((item) => item[operand])
      )
    },
    handleSubmit() {
      this.isDrawerOpen = false
      setTimeout(() => {
        this.$emit('change', this.getResolvedSelectedValue())
      }, 400)
    },
    handleReset() {
      this.selectedValue = Uniq([...(this.value || [])])
    },
  },
}
</script>
