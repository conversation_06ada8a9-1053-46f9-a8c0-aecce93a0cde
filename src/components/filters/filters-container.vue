<template>
  <MPopover
    :open="isPopoverOpen"
    overlay-class-name="pre-post-filter-dropdown picker-overlay has-arrow"
    placement="bottomLeft"
    :get-popup-container="$attrs['get-popup-container']"
  >
    <template v-slot:trigger>
      <slot name="trigger" :open="handlePopoverOpen">
        <div
          class="flex flex-1 flex-wrap min-w-0"
          @click.stop="handlePopoverOpen"
        >
          <FilterTrigger
            :filters="value"
            :placeholder="placeholder"
            :disabled="disabled"
          />
        </div>
      </slot>
    </template>
    <div ref="filterContainer" class="h-full flex flex-1 flex-col relative">
      <MButton
        size="small"
        class="absolute"
        variant="neutral-lighter"
        :rounded="false"
        shape="circle"
        title="Remove"
        style="top: 10px; right: 10px; z-index: 2"
        @click.stop="isPopoverOpen = false"
      >
        <MIcon name="times" />
      </MButton>
      <MTab :value="currentTab" class="mb-2" @change="changeTab">
        <MTabPane
          v-for="tab in tabs"
          :key="tab.key"
          :tab="tab.text"
          force-render
        />
      </MTab>
      <FlotoForm
        v-if="isPopoverOpen"
        ref="formref"
        class="flex flex-col flex-1 min-h-0"
        @submit="handleSubmit"
        @reset="handleReset"
      >
        <MonitorTypeProvider
          ignore-available-filter
          :device-types="deviceTypes"
        >
          <div class="flex flex-col min-h-0 overflow-auto flex-1">
            <Filters
              :key="currentTab"
              v-model="formData[currentTab]"
              :max-groups="maxGroups"
              :selected-counters="selectedCounters"
              :filter-type="currentTab"
              :use-instance-grid="useInstanceGrid"
              :selected-unique-counter-keys="selectedUniqueCounterKeys"
              v-bind="$attrs"
            />
          </div>
        </MonitorTypeProvider>
        <template v-slot:submit="{ submit, reset }">
          <div class="text-right my-2">
            <MButton variant="default" class="mr-2" @click="reset">
              Reset
            </MButton>
            <MButton variant="error" outlined @click="clearFilters">
              Clear
            </MButton>
            <MButton class="ml-2" @click="submit">Apply</MButton>
          </div>
        </template>
      </FlotoForm>
    </div>
  </MPopover>
</template>

<script>
import CloneDeep from 'lodash/cloneDeep'
import Flatten from 'lodash/flatten'
import Uniq from 'lodash/uniq'

import MonitorTypeProvider from '@components/data-provider/monitor-type-provider'
import FilterTrigger from './filter-trigger.vue'
import Filters from './filters.vue'
import { FILTER_CONDITION_DEFAULT_DATA } from '../widgets/constants'

export default {
  name: 'FiltersContainer',
  components: {
    Filters,
    FilterTrigger,
    MonitorTypeProvider,
  },
  inheritAttrs: false,
  model: { event: 'change' },
  props: {
    maxPreGroups: {
      type: Number,
      default: 3,
    },
    maxPostGroups: {
      type: Number,
      default: 1,
    },
    selectedCounters: {
      type: Array,
      default() {
        return []
      },
    },
    useInstanceGrid: {
      type: Boolean,
      default: false,
    },
    value: {
      type: Object,
      default: undefined,
    },
    excludedTabs: {
      type: Array,
      default() {
        return []
      },
    },
    placeholder: {
      type: String,
      default: 'Search',
    },
    useUndefinedOnClear: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      currentTab: 'pre',
      formData: {},
      isPopoverOpen: false,
    }
  },
  computed: {
    deviceTypes() {
      return [
        this.$constants.SERVER,
        this.$constants.CLOUD,
        this.$constants.NETWORK,
        this.$constants.SDN,
        this.$constants.SERVICE_CHECK,
        this.$constants.VIRTUALIZATION,
        this.$constants.WIRELESS,
        this.$constants.OTHER,
        this.$constants.HYPERCONVERGED_INFRASTRUCTURE,
      ]
    },
    tabs() {
      const excludedTabs = this.excludedTabs
      return [
        { key: 'pre', text: 'Pre Filters' },
        { key: 'post', text: 'Post Filters' },
      ].filter((d) => excludedTabs.includes(d.key) === false)
    },
    maxGroups() {
      if (this.currentTab === 'pre') {
        return this.maxPreGroups
      }
      return this.maxPostGroups
    },
    selectedUniqueCounterKeys() {
      return Uniq(
        Flatten(this.formData?.pre?.groups?.map((g) => g.conditions) || []).map(
          (c) => c.operand
        )
      ).filter(Boolean)
    },
    hasFirstOprandSelected() {
      return this.formData?.[this.currentTab]?.groups?.[0]?.conditions?.[0]
        ?.operand
    },
    haseMultipleGroupSelected() {
      return this.formData?.[this.currentTab]?.groups?.length > 1
    },
  },
  created() {
    // const clickHandler = (e) => {
    //   if (
    //     this.$refs.filterContainer &&
    //     !this.$refs.filterContainer.contains(e.target)
    //   ) {
    //     this.isPopoverOpen = false
    //   }
    // }
    // document.addEventListener('click', clickHandler)
    // this.$once('hook:beforeDestroy', () => {
    //   document.removeEventListener('click', clickHandler)
    // })
  },
  methods: {
    handlePopoverOpen() {
      if (this.disabled) {
        return
      }
      this.formData = CloneDeep(
        this.value || {
          pre: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
          post: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
        }
      )
      this.isPopoverOpen = true
    },
    handleSubmit() {
      this.isPopoverOpen = false
      this.$emit('change', this.formData)
    },
    handleReset() {
      if (this.currentTab === 'pre') {
        this.formData = CloneDeep({
          ...this.formData,
          pre: this.value?.pre?.groups[0]?.conditions[0]?.operator
            ? this.value.pre
            : CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
        })
      } else {
        this.formData = CloneDeep({
          ...this.formData,
          post: this.value?.post?.groups[0]?.conditions[0]?.operator
            ? this.value.post
            : CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
        })
      }
    },
    clearFilters() {
      this.formData = CloneDeep({
        pre: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
        post: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
      })
      if (this.useUndefinedOnClear) {
        this.$emit('change', undefined)
      } else {
        this.$emit('change', {
          pre: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
          post: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
        })
      }
      this.isPopoverOpen = false
    },
    changeTab(event) {
      if (this.hasFirstOprandSelected || this.haseMultipleGroupSelected) {
        this.$refs.formref.validate().then((validate) => {
          if (validate) {
            this.currentTab = event
          }
        })
      } else {
        this.currentTab = event
      }
    },
  },
}
</script>
