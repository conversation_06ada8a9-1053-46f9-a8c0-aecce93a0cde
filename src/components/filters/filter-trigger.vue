<template>
  <div
    v-if="!textOnly"
    class="input-container flex flex-1 search-bar-content w-full overflow-hidden"
    :class="{ 'cursor-pointer': !disabled }"
  >
    <span
      class="inline-flex items-center jusitify-center px-2 h-full filter-icon mr-2 rounded text-neutral-light"
    >
      <MIcon name="filter" />
    </span>
    <div
      v-if="preGroups.length || postGroups.length"
      class="flex flex-1 flex-wrap text-input min-w-0"
      :class="{ 'cursor-pointer': !disabled }"
      :title="tooltip"
    >
      <div class="text-ellipsis" v-html="groups" />
      <!-- <strong v-if="postGroups.length" class="seperator mx-2" />
      <div v-if="postGroups.length" v-html="postGroups" /> -->
    </div>
    <div v-else class="flex items-center text-neutral-light">
      {{ placeholder }}
    </div>
  </div>
  <div v-else class="search-bar-content" v-html="groups" />
</template>
<script>
import Trim from 'lodash/trim'
import { OPERATOR_MAP } from '@components/widgets/constants'

export default {
  name: 'FilterTrigger',
  inject: { groupContext: { default: { options: new Map() } } },
  props: {
    filters: {
      type: Object,
      default() {
        return {
          pre: {},
          post: {},
        }
      },
    },
    textOnly: { type: Boolean, default: false },
    placeholder: {
      type: String,
      default: 'Search',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    preFilters() {
      return this.filters.pre
    },
    postFilters() {
      return this.filters.post
    },
    preGroups() {
      const preFilters = this.preFilters || {}
      const groups = preFilters.groups || []
      if (!groups.length) {
        return ''
      }
      const conditions = groups[0].conditions || []
      if (
        !conditions.length ||
        !(conditions[0].operator && conditions[0].operand)
      ) {
        return ''
      }
      const appliedGroupCondition = preFilters.condition

      //  number of group count is less do not affact memory
      return groups.reduce(
        (result, group, index) =>
          `${result} ${
            index !== 0 && index < groups.length
              ? `<strong>${appliedGroupCondition}</strong>`
              : ''
          } ${groups.length > 1 ? '(' : ''}${this.getGroupText(
            group
            // groups.length > 1
          )}${groups.length > 1 ? ')' : ''}`,
        ''
      )
    },
    postGroups() {
      const postFilters = this.postFilters || {}
      const groups = postFilters.groups || []
      if (!groups.length) {
        return ''
      }
      const conditions = groups[0].conditions || []
      if (
        !conditions.length ||
        !(
          conditions[0].value !== undefined &&
          conditions[0].operator &&
          conditions[0].operand
        )
      ) {
        return ''
      }
      const appliedGroupCondition = postFilters.condition

      //  number of group count is less do not affact memory
      return groups.reduce(
        (result, group, index) =>
          `${result} ${
            index !== 0 && index < groups.length
              ? `<strong>${appliedGroupCondition}</strong>`
              : ''
          } ${groups.length > 1 ? '(' : ''}${this.getGroupText(
            group
            // groups.length > 1
          )}${groups.length > 1 ? ')' : ''}`,
        ''
      )
    },
    tooltip() {
      return `${this.preGroups} ${this.postGroups.length ? '|' : ''} ${
        this.postGroups
      }`.replace(/<[^>]+>/g, '')
    },
    groups() {
      let text = `${this.preGroups}${
        this.postGroups.length
          ? '<strong  class="seperator mx-2 text-xl">|</strong>'
          : ''
      }${this.postGroups.length ? this.postGroups : ''}`
      if (text.length <= 0) {
        this.$emit('toggle', false)
      } else {
        this.$emit('toggle', true)
      }
      return text
    },
  },
  methods: {
    getGroupText(group, useGroupBracket = true) {
      const appliedGroupCondition = (group.condition || 'and').toUpperCase()
      const groupInclusion = `<strong>${(
        group.inclusion || 'include'
      ).toUpperCase()}</strong> `
      return `${useGroupBracket ? groupInclusion : ''}${group.conditions.reduce(
        (result, condition, index) =>
          `${result} ${
            index !== 0 && index < group.conditions.length
              ? `<strong>${appliedGroupCondition}</strong>`
              : ''
          } ${condition.operand.replace(/[~^]/g, '.')} <b>${
            OPERATOR_MAP[condition.operator]
          } ${condition.aggregation || ''} ${
            condition.thresholdOrBaseline || ''
          }</b> ${this.getConditionValue(condition)}`,
        ''
      )}`
    },
    getConditionValue(condition) {
      if (condition.operator === 'between') {
        return `${condition.value} - ${condition.toValue}`
      }
      if (condition.operand === 'Group') {
        return condition.value.map((id) => {
          if (this.groupContext.options.has(id)) {
            const name = Trim(this.groupContext.options.get(id).name)
            if (/\s/.test(name)) {
              return `"${name}"`
            }
            return name
          }
          return id
        })
      } else if (['in', 'not in'].includes(condition.operator)) {
        return (
          Array.isArray(condition.value)
            ? condition.value
            : (condition.value || '').toString().split(',')
        )
          .map((i) => (/\s/.test(i) ? `"${i}"` : i))
          .join(',')
      }
      return /\s/.test(Trim(condition.value))
        ? `"${Trim(
            condition.value === undefined ||
              condition.value === null ||
              condition.value === ''
              ? 'Any'
              : condition.value
          )}"`
        : Trim(
            condition.value === undefined ||
              condition.value === null ||
              condition.value === ''
              ? 'Any'
              : condition.value
          )
    },
  },
}
</script>

<style lang="less" scoped>
.seperator {
  width: 2px;
  height: 20px;
  background: var(--primary);
}

.input-container {
  align-items: center;
  height: @input-height-base;
  padding: 0 @input-padding-horizontal-base;
  padding-left: 0;
  background: var(--dropdown-btn-background);
  border: 1px solid var(--border-color);
  border-radius: @btn-radius;
  transition: border-color 0.5s ease;

  .filter-icon {
    background: var(--dropdown-btn-background);
    border-right: 1px solid var(--border-color);
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
}
</style>
