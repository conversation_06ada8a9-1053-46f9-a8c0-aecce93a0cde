<template>
  <div
    class="flex flex-col flex-1 bordered rounded relative"
    :class="{ 'mt-6': maxGroups > 1 }"
    :style="maxGroups > 1 ? { paddingTop: '20px' } : {}"
  >
    <div
      v-if="maxGroups > 1"
      class="flex items-center my-1 mx-2 absolute"
      style="top: -22px; background: transparent; backdrop-filter: blur(50px)"
    >
      <div style="max-width: 100px">
        <FlotoDropdownPicker
          v-model="condition"
          :options="conditionOptions"
          :searchable="false"
        />
      </div>
      <span class="mx-2">Group(s) matching</span>
    </div>
    <MultipleFormItems
      id="add-log-condition-btn"
      v-model="groups"
      add-btn-text="Add Condition"
      :max-items="maxGroups"
      :show-icon="false"
      :item-template="defaultItemTemplate"
    >
      <template
        v-slot="{ item, update, remove, isLastItem, canAdd, add, total, index }"
      >
        <FilterGroup
          :value="item"
          :can-add="isLastItem && canAdd"
          :can-remove="total > 1"
          :is-post-filter="isPostFilter"
          :selected-counters="selectedCounters"
          :group-index="index"
          v-bind="$attrs"
          @change="update($event)"
          @remove="remove($event)"
          @add="add"
        />
        <MDivider v-if="!isLastItem" class="my-2" />
      </template>
    </MultipleFormItems>
  </div>
</template>

<script>
import Omit from 'lodash/omit'
import MultipleFormItems from '@components/multiple-form-items.vue'
import { FILTER_CONDITION_DEFAULT_DATA } from '../widgets/constants'
import FilterGroup from './filter-group.vue'

export default {
  name: 'Filters',
  components: {
    FilterGroup,
    MultipleFormItems,
  },
  inheritAttrs: false,
  model: { event: 'change' },
  props: {
    maxGroups: {
      type: Number,
      default: 3,
    },
    value: {
      type: Object,
      default: undefined,
    },
    filterType: {
      type: String,
      default: 'pre',
    },
    selectedCounters: {
      type: Array,
      default() {
        return []
      },
    },
  },
  data() {
    this.conditionOptions = [
      { key: 'and', text: 'All' },
      { key: 'or', text: 'Any' },
    ]
    this.defaultItemTemplate = Omit(FILTER_CONDITION_DEFAULT_DATA.groups[0], [
      'key',
    ])
    return {}
  },
  computed: {
    isPostFilter() {
      return this.filterType === 'post'
    },
    condition: {
      get() {
        return (this.value || {}).condition
      },
      set(condition) {
        this.$emit('change', { ...(this.value || {}), condition })
      },
    },
    groups: {
      get() {
        return (this.value || {}).groups
      },
      set(groups) {
        this.$emit('change', { ...(this.value || {}), groups })
      },
    },
  },
}
</script>
