<template>
  <div class="flex flex-col px-2 py-1 my-2 rounded relative group-container">
    <div class="flex items-center my-1">
      <div style="max-width: 100px">
        <FlotoDropdownPicker
          v-model="inclusion"
          :searchable="false"
          :options="inclusionOptions"
        />
      </div>
      <span class="mx-2">Group matching</span>
      <div style="max-width: 100px">
        <FlotoDropdownPicker
          v-model="condition"
          :options="conditionOptions"
          :searchable="false"
        />
      </div>
      <span class="mx-2">Criterias</span>
    </div>
    <div class="flex flex-col">
      <MultipleFormItems
        id="add-log-condition-btn"
        v-model="conditions"
        add-btn-text="Add Condition"
        :max-items="maxConditions"
        :show-icon="false"
      >
        <template
          v-slot="{
            item,
            update,
            remove,
            isLastItem,
            canAdd: innerCanAdd,
            add,
            total,
            index,
          }"
        >
          <FilterCondition
            :condition="item"
            :operand-options="counterOptions"
            :can-add="isLastItem && innerCanAdd"
            :can-remove="total > 1"
            :possible-column-options="possibleColumnOptions"
            :operand-data-type-map="counterDataTypeMap"
            :instance-grid-key-field="instanceGridKeyField"
            :selected-counters="selectedCounters"
            :is-post-filter="isPostFilter"
            :operand-field-type-map="fieldTypeMap"
            :item-index="`${index}${groupIndex}`"
            v-bind="$attrs"
            @change="update($event)"
            @remove="remove($event)"
            @add="add"
          />
        </template>
      </MultipleFormItems>
    </div>
    <div class="flex">
      <a v-if="canAdd" @click.stop="$emit('add')">
        <MIcon name="plus" />Add New Group
      </a>
    </div>
    <MButton
      v-if="canRemove"
      size="small"
      class="remove-action-btn absolute"
      variant="neutral-lighter"
      :rounded="false"
      shape="circle"
      title="Remove"
      @click.stop="$emit('remove')"
    >
      <MIcon name="times" />
    </MButton>
  </div>
</template>

<script>
import UniqBy from 'lodash/uniqBy'
import MultipleFormItems from '@components/multiple-form-items.vue'
import FilterCondition from './filter-condition.vue'
import { AvailableReportCategories } from '@modules/report/helpers/report'
import { hardCodedCountersByReportCategory } from './helper'

export default {
  name: 'FilterGroup',
  components: {
    FilterCondition,
    MultipleFormItems,
  },
  inject: {
    instanceMetricContext: { default: { options: { columns: [], data: [] } } },
    counterContext: { default: { options: new Map() } },
  },
  inheritAttrs: false,
  model: { event: 'change' },
  props: {
    value: {
      type: Object,
      default: undefined,
    },
    maxConditions: {
      type: Number,
      default: 3,
    },
    isPostFilter: {
      type: Boolean,
      default: false,
    },
    postFilterCounters: {
      type: Array,
      default() {
        return []
      },
    },
    canAdd: {
      type: Boolean,
      default: false,
    },
    canRemove: {
      type: Boolean,
      default: false,
    },
    selectedCounters: {
      type: Array,
      default() {
        return []
      },
    },
    allCounters: {
      type: Array,
      default() {
        return []
      },
    },
    preExtraOptions: {
      type: Array,
      default() {
        return []
      },
    },
    excludedOperandOption: {
      type: Array,
      default() {
        return [
          'source.plugin',
          'source.host',
          'monitor',
          'object.type',
          'object.vendor',
          'object.ip',
        ]
      },
    },
    groupIndex: {
      type: Number,
      required: true,
    },
    reportCategory: {
      type: String,
      default: undefined,
    },
  },
  computed: {
    instanceGridKeyField() {
      if (this.isPostFilter) {
        return undefined
      }
      const instanceCounters = new Set()
      const counterOptions = this.counterContext.options
      Array.from(counterOptions.values())
        .filter((c) => c.instanceType)
        .forEach((c) => instanceCounters.add(c.instanceType))
      const instanceColumn = this.possibleColumnOptions.columns.find((c) =>
        instanceCounters.has(c.key)
      )
      if (instanceColumn) {
        return instanceColumn.key
      }
      return undefined
    },
    counterDataTypeMap() {
      const map = {}
      const counterPickerContext = this.counterContext.options
      if (this.isPostFilter) {
        this.counterOptions.forEach((column) => {
          if (counterPickerContext.has(column.key)) {
            map[column.key] = counterPickerContext.get(column.key).dataType
          } else {
            // default data type numeric always
            map[column.key] = ['numeric']
          }
        })
      } else if (this.allCounters.length > 0) {
        Array.from(counterPickerContext.values()).forEach((c) => {
          map[c.key] = c.dataType
        })
      } else {
        this.possibleColumnOptions.columns.forEach((column) => {
          if (counterPickerContext.has(column.key)) {
            map[column.key] = counterPickerContext.get(column.key).dataType
          } else {
            // default data type numeric always
            map[column.key] = ['string']
          }
        })
      }
      const newKey = map[this.value?.conditions[0]?.operand]
      if (!newKey) {
        map[this.value?.conditions[0]?.operand] = ['string']
      }
      return map
    },
    fieldTypeMap() {
      const map = {}
      const counterPickerContext = this.counterContext.options
      this.counterOptions.forEach((column) => {
        if (counterPickerContext.has(column.key)) {
          const fieldType = counterPickerContext.get(column.key).fieldType
          if (fieldType) {
            map[column.key] = fieldType
          }
        }
      })
      return map
    },
    counterOptions() {
      const excludedOptions = this.excludedOperandOption
      if (this.isPostFilter) {
        if (
          this.reportCategory &&
          this.reportCategory === AvailableReportCategories.AVAILABILITY
        ) {
          let prefix

          if (this.selectedCounters?.[0]?.key === 'monitor') {
            prefix = 'monitor.'
          }

          if (this.selectedCounters?.[0]?.instanceType) {
            prefix = `${this.selectedCounters?.[0]?.instanceType}~`
          }

          return (
            hardCodedCountersByReportCategory[this.reportCategory] || []
          ).map((c) => {
            const counterRowName = `${prefix || ''}${c}`
            return {
              key: counterRowName,
              text: counterRowName.replace('~', '.'),
            }
          })
        }
        return this.selectedCounters.map((m) => ({
          key: `${m.key}${m.aggrigateFn ? `.${m.aggrigateFn}` : ''}`,
          text: `${m.counterName || m.key}${
            m.aggrigateFn ? `.${m.aggrigateFn}` : ''
          }`,
        }))
      } else if (this.allCounters.length > 0) {
        return this.preExtraOptions
          .concat(this.allCounters)
          .filter((d) => excludedOptions.includes(d.key) === false)
      }
      return this.preExtraOptions
        .concat(UniqBy(this.possibleColumnOptions.columns, 'key'))
        .filter((d) => excludedOptions.includes(d.key) === false)
    },
    possibleColumnOptions() {
      return this.instanceMetricContext.options
    },
    conditionOptions() {
      return [
        { key: 'and', text: 'All' },
        { key: 'or', text: 'Any' },
      ]
    },
    inclusionOptions() {
      return [
        { key: 'include', text: 'Include' },
        { key: 'exclude', text: 'Exclude' },
      ]
    },
    condition: {
      get() {
        return (this.value || {}).condition
      },
      set(condition) {
        this.$emit('change', { ...(this.value || {}), condition })
      },
    },
    inclusion: {
      get() {
        return (this.value || {}).inclusion
      },
      set(inclusion) {
        this.$emit('change', { ...(this.value || {}), inclusion })
      },
    },
    conditions: {
      get() {
        return (this.value || {}).conditions || []
      },
      set(conditions) {
        this.$emit('change', { ...(this.value || {}), conditions })
      },
    },
  },
}
</script>

<style lang="less" scoped>
.group-container {
  .remove-action-btn {
    top: 10px;
    right: 10px;
    z-index: 2;
    cursor: pointer;
    opacity: 0;
    transition: transform opacity 0.5s linear;
    transform: translateY(-20px);
  }

  &:hover {
    .remove-action-btn {
      opacity: 1;
      transform: translateY(0);
    }
  }
}
</style>
