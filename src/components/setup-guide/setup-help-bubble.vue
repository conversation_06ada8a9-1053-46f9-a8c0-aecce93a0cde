<template>
  <Transition name="placeholder" appear>
    <DraggableElement v-if="showBubble">
      <div
        class="bubble-container text-white"
        :style="{
          '--h': `${isMinimized ? 50 : 250}px`,
          '--v': `${isMinimized ? 100 : 300}px`,
        }"
      >
        <div
          class="flex flex-1 min-h-0"
          :class="{
            'justify-center': isMinimized,
            'items-center': isMinimized,
          }"
        >
          <template v-if="!isMinimized">
            <div class="icon">
              <MIcon
                :name="setupGuideContext.currentStep.helpBubble.icon"
                size="2x"
              />
            </div>
            <div class="flex-1 min-w-0 flex flex-col">
              <h3 class="mb-0 font-bold text-white">{{
                setupGuideContext.currentStep.helpBubble.title
              }}</h3>
              <div class="description w-full min-h-0 overflow-auto">{{
                setupGuideContext.currentStep.helpBubble.description
              }}</div>
              <div class="mt-1 text-underline w-full">{{
                setupGuideContext.currentStep.route.text
              }}</div>
            </div>
          </template>
          <h5 v-else class="m-0 text-white">Product Setup Guides</h5>

          <MButton
            variant="transparent"
            :shadow="false"
            shape="circle"
            @click="isMinimized = !isMinimized"
          >
            <MIcon
              :name="isMinimized ? 'window-restore' : 'minimize'"
              @click="isMinimized = !isMinimized"
            />
          </MButton>
        </div>
        <div v-if="!isMinimized" class="mt-6 flex items-center justify-between">
          <div>
            Done?
            <a @click.prevent="backToGuide">
              <span class="text-white text-underline">Click here to skip</span>
            </a>
          </div>
          <div>
            <MButton @click="backToGuide">Back to Guide</MButton>
          </div>
        </div>
      </div>
    </DraggableElement>
  </Transition>
</template>

<script>
import Bus from '@utils/emitter'
import { availableSections as metricSections } from '@modules/product-setup/helper/metric-guide'
import DraggableElement from '../draggable-element.vue'

const availableSections = {
  metric: metricSections,
}

export default {
  name: 'SetupHelpBubbule',
  components: { DraggableElement },
  inject: { setupGuideContext: { default: {} } },
  data() {
    return {
      showBubble: false,
      isMinimized: false,
    }
  },
  watch: {
    $route(newValue, oldValue) {
      setTimeout(() => {
        this.checkRouteQuery()
      }, 400)
    },
  },
  mounted() {
    this.checkRouteQuery()
  },
  methods: {
    checkRouteQuery() {
      const setup = this.$route.query._setup
      if (setup) {
        try {
          const decodedSetup = JSON.parse(atob(decodeURIComponent(setup)))
          if (decodedSetup.__setupguide) {
            if (this.setupGuideContext.sections.length === 0) {
              if (
                decodedSetup.guide &&
                decodedSetup.section &&
                decodedSetup.step
              ) {
                this.setupGuideContext.init(
                  decodedSetup.guide,
                  availableSections[decodedSetup.guide] || []
                )
                this.setupGuideContext.goToSection(decodedSetup.section)
                this.setupGuideContext.goToStep(decodedSetup.step)
              }
            }
            setTimeout(() => {
              if (this.setupGuideContext.currentStep.helpBubble) {
                this.showBubble = true
              }
            })
          } else {
            this.showBubble = false
          }
        } catch (e) {
          this.showBubble = false
        }
      } else {
        this.showBubble = false
      }
    },
    hideFormDrawer() {
      Bus.$emit('setup:hide-form-drawer')
    },
    backToGuide() {
      this.showBubble = false
      this.setupGuideContext.moveToNextStep()
      this.hideFormDrawer()
      setTimeout(() => {
        if (
          this.setupGuideContext.isCurrentGuideCompleted &&
          this.$route.name === 'product-setup'
        ) {
          return
        }
        this.$router.push(
          this.$modules.getModuleRoute(
            'product-setup',
            this.setupGuideContext.isCurrentGuideCompleted
              ? undefined
              : this.setupGuideContext.guideType
          )
        )
      }, 400)
    },
  },
}
</script>

<style lang="less" scoped>
.bubble-container {
  --h: 250px; /* height  */
  --v: 300px; /* cut */

  position: fixed;
  top: calc(100vh - var(--v));
  z-index: 99999;
  display: flex;
  flex-direction: column;
  max-width: 450px;
  height: calc(var(--h));
  cursor: move;
  background: #114e80;

  @apply rounded shadow-md p-4;

  .description {
    color: #7b8fa5;
  }

  .icon {
    background: rgb(255 255 255 / 10%);

    @apply rounded mr-2 p-2 mb-auto;
  }

  .text-underline {
    text-decoration: underline;
  }
}
</style>
