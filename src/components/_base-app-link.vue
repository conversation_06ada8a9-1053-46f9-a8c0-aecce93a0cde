<template>
  <RouterLink v-if="!asButton" v-bind="$attrs" v-on="$listeners">
    <slot />
  </RouterLink>
  <MButton v-else v-bind="$attrs" v-on="$listeners" @click="handlePushRoute">
    <slot />
  </MButton>
</template>

<script>
export default {
  name: 'FlotoLink',
  inheritAttrs: false,
  props: {
    asButton: { type: Boolean, default: false },
  },
  methods: {
    handlePushRoute() {
      const href = this.$router.resolve(this.$attrs.to).href
      if (href !== this.$route.path) {
        this.$router.push(this.$attrs.to)
      }
    },
  },
}
</script>
