<template>
  <div
    class="flex notification"
    :class="`${(notification.severity || '').toLowerCase()}-notification ${
      isEventPolicy ? 'h-full' : ''
    }`"
  >
    <div
      class="flex min-w-0 ml-2"
      :class="{
        'flex-col': size === 'dropdown',
        'justify-between items-center flex-1 pr-4': size === 'page',
      }"
    >
      <!-- <AlertDrilldown :alert="notification" :field="notification['policy']" traget-blank /> -->
      <a
        v-if="size === 'dropdown'"
        target="_blank"
        class="text-ellipsis cursor-pointer text-sm"
        :title="notification['policy.name']"
        @click="navigateToDetail(notification, 'overview')"
        v-text="notification['policy.name']"
      />
      <div
        v-else-if="size === 'page'"
        class="text-ellipsis text-primary text-sm"
        :title="notification['policy.name']"
        v-text="notification['policy.name']"
      />
      <div
        style="flex: no-wrap; max-width: 530px"
        class="text-ellipsis text-sm"
      >
        {{
          notification['object.category'] === $constants.CLOUD
            ? notification['object.type'] + ' - ' + notification['object.name']
            : `${notification['object.name'] || ''} ${
                notification['object.ip']
                  ? `( ${notification['object.ip']} )`
                  : ''
              } ${
                notification['instance'] ? ` - ${notification['instance']}` : ''
              }`
        }}
        {{ isEventPolicy ? notification['event.source'] : '' }}
      </div>
      <small v-if="size === 'dropdown'" class="text-neutral cursor-pointer">
        {{ notification['event.timestamp'] | datetime }}
      </small>
      <small
        v-else-if="size === 'page'"
        class="text-ellipsis"
        style="flex-shrink: 0"
      >
        {{ notification['event.timestamp'] | datetime }}
      </small>
    </div>
    <MTag
      class="used-count-pill ant-tag cursor-auto inline-flex items-center rounded"
      :closable="false"
    >
      {{ notification['policy.type'] }}
    </MTag>
  </div>
</template>
<script>
import {
  transformAlert,
  transformLogFlowStreamData,
} from '@modules/alert/helpers/alert-helper'

import { WidgetTypeConstants } from '@/src/components/widgets/constants'
export default {
  name: 'AlertNotificationItem',
  props: {
    isNew: {
      type: Boolean,
      default: false,
    },
    notification: {
      type: Object,
      required: true,
    },
    size: {
      type: String,
      default: 'dropdown',
    },
  },
  computed: {
    iconClass() {
      const type = this.type
      if (type === 'error') {
        return 'text-secondary-red'
      } else if (type === 'warning') {
        return 'text-secondary-yellow'
      } else if (type === 'info') {
        return 'text-neutral'
      } else if (type === 'clear') {
        return 'text-secondary-green'
      }
      return undefined
    },
    icon() {
      const type = this.type
      if (type === 'error') {
        return 'times-circle'
      } else if (type === 'warning') {
        return 'exclamation-triangle'
      } else if (type === 'info') {
        return 'exclamation-circle'
      } else if (type === 'clear') {
        return 'check-circle'
      }
      return 'question'
    },
    type() {
      return (this.notification.type || 'Error').toLowerCase()
    },

    isEventPolicy() {
      return ['log', 'flow', 'trap'].includes(
        this.notification['policy.type']?.toLowerCase()
      )
    },
  },

  methods: {
    async navigateToDetail(item, tab) {
      if (
        ['log', 'flow', 'trap'].includes(
          this.notification['policy.type']?.toLowerCase()
        )
      ) {
        transformLogFlowStreamData(
          this.notification['policy.type']?.toLowerCase(),
          {
            result: {
              [WidgetTypeConstants.GRID]: {
                data: [
                  {
                    ...(this.notification || {}),
                  },
                ],
              },
            },
          }
        ).then((data) => {
          const alert = data[0]

          const param = {
            ...alert,
            groupCategory: this.notification['policy.type']?.toLowerCase(),
            view: 'overview',
          }

          const query = encodeURIComponent(
            btoa(
              JSON.stringify({
                selectedKey: 'today',
              })
            )
          )

          const routePath = this.$modules.getModuleRoute('alert', 'detail', {
            params: {
              ...this.$route.params,
              category: this.notification['policy.type']?.toLowerCase(),
              tab: tab || 'overview',
              uuid: encodeURIComponent(btoa(JSON.stringify(param))),
            },
            query: {
              t: query,
            },
          })

          const fullPath = this.$router.resolve(routePath).href

          window.open(fullPath, '_blank')

          this.$emit('navigate')
        })
      } else {
        this.$emit('navigate')

        const alert = await transformAlert(item)

        const param = {
          ...alert,
          view: 'overview',
        }

        const query = encodeURIComponent(
          btoa(
            JSON.stringify({
              selectedKey: 'today',
            })
          )
        )

        const routePath = this.$modules.getModuleRoute('alert', 'detail', {
          params: {
            ...this.$route.params,
            category: alert['category'],
            tab: tab || 'overview',
            uuid: encodeURIComponent(btoa(JSON.stringify(param))),
          },
          query: {
            t: query,
          },
        })

        const fullPath = this.$router.resolve(routePath).href

        // Open the URL in a new tab
        window.open(fullPath, '_blank')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.notification {
  align-items: center;
  // background: var(--page-background-color);
  border-left: 3px solid transparent;
  // border-radius: 4px;
  // @apply shadow-md  h-full flex items-center;

  &.error-notification {
    border-color: var(--secondary-red);
  }

  &.warning-notification {
    border-color: var(--secondary-yellow);
  }

  &.info-notification {
    border-color: var(--neutral-regular);
  }

  &.clear-notification {
    border-color: var(--secondary-green);
  }

  &.down-notification {
    border-color: var(--severity-down);
  }

  &.unreachable-notification {
    border-color: var(--severity-unreachable);
  }

  &.critical-notification {
    border-color: var(--severity-critical);
  }

  &.major-notification {
    border-color: var(--severity-major);
  }

  .used-count-pill {
    @apply mx-2 my-1 px-2 ml-auto;
  }

  .text-sm {
    font-size: 12px !important;
  }

  small {
    font-size: 11px !important;
    color: var(--notification-dropdown-datetime-color) !important;
  }
}
</style>
