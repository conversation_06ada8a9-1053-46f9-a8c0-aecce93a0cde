<template>
  <svg
    id="Layer_1"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
    x="0px"
    y="0px"
    viewBox="0 0 200 180"
    style="enable-background: new 0 0 200 180"
    xml:space="preserve"
  >
    <rect x="42" y="-379" class="st0" fill="none" width="116" height="938" />
    <g id="Mask_Group_2" transform="translate(-53)">
      <g transform="matrix(1, 0, 0, 1, 53, 0)">
        <linearGradient
          id="Ellipse_728-2_1_"
          gradientUnits="userSpaceOnUse"
          x1="-297.2444"
          y1="394.3487"
          x2="-295.9104"
          y2="393.0217"
          gradientTransform="matrix(148 0 0 -148 44002 58346)"
        >
          <stop offset="0" style="stop-color: #89c540" />
          <stop offset="1" style="stop-color: #099dd9" />
        </linearGradient>
        <circle
          id="Ellipse_728-2"
          class="mask-group-1"
          cx="118"
          cy="90"
          r="74"
        />
      </g>
      <g transform="matrix(1, 0, 0, 1, 53, 0)">
        <linearGradient
          id="Ellipse_729-2_1_"
          gradientUnits="userSpaceOnUse"
          x1="-294.9074"
          y1="399.1977"
          x2="-293.5734"
          y2="397.8707"
          gradientTransform="matrix(68 0 0 -68 20046 27214)"
        >
          <stop offset="0" style="stop-color: #f5bc18" />
          <stop offset="1" style="stop-color: #f58518" />
        </linearGradient>
        <circle
          id="Ellipse_729-2"
          class="mask-group-2"
          cx="65"
          cy="50"
          r="34"
        />
      </g>
    </g>
  </svg>
</template>

<script>
export default {
  name: 'MaskGroup',
}
</script>

<style lang="less" scoped>
// stylelint-disable-next-line
.mask-group-1 {
  opacity: 0.5;
  fill: url('#Ellipse_728-2_1_');
  enable-background: new;
}
// stylelint-disable-next-line
.mask-group-2 {
  opacity: 0.5;
  fill: url('#Ellipse_729-2_1_');
  enable-background: new;
}
</style>
