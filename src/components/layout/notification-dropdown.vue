<template>
  <MPopover
    ref="popoverRef"
    placement="bottomRight"
    overlay-class-name="notification-dropdown-panel"
    @hide="handleHidePoppver"
  >
    <template v-slot:trigger="{ toggle }">
      <MBadge :count="alertCount" class="notification-badge">
        <MButton
          variant="transparent"
          title="Notification"
          shape="circle"
          @click="clickNotificationIcon(toggle)"
        >
          <MIcon name="bell" size="lg" />
        </MButton>
      </MBadge>
    </template>
    <div class="flex flex-1 flex-col notification-dropdown-content">
      <div class="header flex justify-between items-center px-2 py-2">
        <h6 class="m-0">
          {{ 'Alerts & Notifications' }}
        </h6>
      </div>
      <div class="notification-content flex flex-1 flex-col px-2 min-h-0">
        <MTab v-model="notificationType">
          <MTabPane
            key="alerts"
            :tab="`Alerts (${(alertData || []).length})`"
          />
          <MTabPane
            key="system"
            :tab="`System Notification (${(systemData || []).length})`"
          />
        </MTab>
        <div
          v-if="!loading"
          :key="notificationType"
          class="flex flex-1 flex-col min-h-0 overflow-y-auto py-2"
        >
          <RecycleScroller
            v-if="
              notificationType === 'system'
                ? systemData.length
                : reversedAlertData.length
            "
            :items="
              notificationType === 'system' ? systemData : reversedAlertData
            "
            key-field="id"
            :item-size="notificationType === 'system' ? 50 : 70"
          >
            <template v-slot="{ item, index }">
              <div
                class="py-2 notification-bar"
                :style="{
                  height: notificationType === 'system' ? ' 52px' : '70px',
                  borderBottom:
                    '1px solid var(--notification-dropdown-border-color)',
                }"
              >
                <NotificationItem
                  v-if="notificationType === 'system'"
                  :notification="item"
                  :is-new="index < count"
                  @change="navigateToAll"
                />
                <AlertNotificationItem
                  v-else
                  :notification="item"
                  :is-new="index < count"
                  @navigate="hide"
                />
              </div>
            </template>
          </RecycleScroller>
          <FlotoNoData v-else />
        </div>
        <FlotoContentLoader v-else loading />
      </div>
      <div
        class="footer flex justify-items-center"
        style="align-items: center; justify-content: center"
      >
        <span>
          <a @click="navigateToAll({ type: notificationType })"> View All </a>
          <span class="mx-1"> | </span>
          <a @click="clearNotification"> Clear All </a>
        </span></div
      >
    </div>
  </MPopover>
</template>

<script>
import Bus from '@utils/emitter'
import Debounce from 'lodash/debounce'
import { WidgetTypeConstants } from '@components/widgets/constants'
import NotificationItem from './notification-item.vue'
import AlertNotificationItem from './alert-notification-item.vue'
import { authComputed } from '@state/modules/auth'
import {
  fetchNotificationDataApi,
  // fetchNotificationCountApi,
  transformNotificationForList,
} from '@modules/notifications/notifications-api.js'
import { generateId } from '@utils/id'

export default {
  name: 'NotificationsDropdown',
  components: {
    NotificationItem,
    AlertNotificationItem,
  },
  inject: { SocketContext: { default: {} } },
  data() {
    return {
      count: 0,
      alertCount: 0,
      notificationType: 'alerts',
      systemData: [],
      alertData: [],
      fragmentedData: [],
      queryProgress: null,
      currentBatch: 1,
      error: null,
      isPaused: false,
      guid: generateId(),
      loading: true,
    }
  },
  computed: {
    ...authComputed,
    reversedAlertData() {
      return [...this.alertData].reverse()
    },
  },
  created() {
    this.requestDataRaw = Debounce(this.startRequestData, 1000, {
      traling: true,
    })
    Bus.$on(this.$constants.NOTIFICATION_COUNT, this.handleCountUpdated)
    Bus.$on(this.$constants.NOTIFICATION_CLEAR_COUNT, this.handleCountClear)

    Bus.$on(this.$constants.ALERT_NOTIFICATION, this.handleAlertsIdReceived)
    Bus.$on(this.$constants.ALERT_NOTIFICATION_CLEAR, this.handleCountClear)

    Bus.$on(this.$constants.ALERT_NOTIFICATION_COUNT, this.handleCountAlerts)

    this.$once('hook:beforeDestroy', () => {
      this.abortQuery()
      this.stopHeartbeat()
      Bus.$off(this.$constants.NOTIFICATION_COUNT, this.handleCountUpdated)
      Bus.$off(this.$constants.NOTIFICATION_CLEAR_COUNT, this.handleCountClear)

      Bus.$off(this.$constants.ALERT_NOTIFICATION, this.handleAlertsIdReceived)
      Bus.$off(this.$constants.ALERT_NOTIFICATION_CLEAR, this.handleCountClear)

      Bus.$off(this.$constants.ALERT_NOTIFICATION_COUNT, this.handleCountAlerts)
    })
    // this.fetchNotificationCount()
  },
  methods: {
    // fetchNotificationCount() {
    //   // fetchNotificationCountApi(this.user.id).then((data) => {
    //   //   this.count = data
    //   // })
    // },
    handleCountAlerts(payload) {
      this.alertCount = payload?.count ? payload.count : 0
    },
    handleCountUpdated(payload, raw) {
      const count = payload[this.user.id] ? payload[this.user.id] : 0
      if (count === 0) {
        setTimeout(() => {
          this.count = count
        }, 700)
      } else {
        this.count = count
      }
    },
    handleNotificationReceived(payload) {
      this.systemData = Object.freeze(
        payload.filter((e) => e.notificationType === 'system')
      )
      // this.alertData = Object.freeze(
      //   payload.filter((e) => e.notificationType !== 'system')
      // )
    },
    clickNotificationIcon(toggle) {
      toggle()
      this.requestDataRaw()
    },
    hide() {
      if (this.$refs.popoverRef) {
        this.$refs.popoverRef.hide()
      }
    },
    navigateToAll(item) {
      this.hide()
      if (item.type === 'alerts') {
        this.$router.push(
          this.$modules.getModuleRoute('alert', 'dashboard', undefined, {
            params: {
              tab: item.type ? item.type : 'system',
            },
          })
        )
      } else {
        this.$router.push(
          this.$modules.getModuleRoute('notifications', undefined, {
            params: {
              tab: item.notificationType ? item.notificationType : 'system',
            },
          })
        )
      }
    },
    startRequestData() {
      this.abortQuery()
      this.loading = true
      this.error = null
      this.queryProgress = null
      this.fragmentedData = []
      this.isPaused = false
      this.currentBatch = 1
      this.systemData = []
      this.alertData = []
      this.startNotificationIncremental()
      this.sendRequestForAlertIds()
    },
    sendRequestForAlertIds() {
      Bus.$emit('server:event', {
        'event.type': this.$constants.ALERT_NOTIFICATION,
        'event.context': {
          'user.id': this.user.id,
        },
      })
    },
    handleAlertsIdReceived(context) {
      if (!context || !Array.isArray(context.result)) {
        return
      }

      if (context.status !== 'fail') {
        this.alertData = (context.result || []).map((a) => ({
          ...a,
          id: generateId(),
        }))
        this.loading = false
      }
    },

    async startNotificationIncremental(queryId = null) {
      const customFilters = {
        id: this.user.id,
        limit: 100,
      }

      fetchNotificationDataApi(
        { selectedKey: 'today' },
        customFilters,
        queryId,
        this.guid
      ).then((data) => {
        this.handleDataReceived(data)
      })
    },
    clearNotification() {
      if (this.notificationType === 'system') {
        this.systemData = []
        this.fragmentedData = []
      } else {
        this.alertData = []
      }

      Bus.$emit('server:event', {
        'event.type':
          this.notificationType === 'system'
            ? this.$constants.NOTIFICATION_CLEAR_COUNT
            : this.$constants.ALERT_NOTIFICATION_CLEAR,
        'event.context': {
          'user.id': this.user.id,
        },
      })
    },

    handleDataReceived(response) {
      this.__parentQueryId = response.result.queryMeta.parentQueryId
      this.queryProgress = response.result.queryMeta.progress

      if (response[this.$constants.UI_EVENT_UUID] !== this.guid) {
        return
      }
      this.fragmentedData = [
        ...(this.fragmentedData || []),
        ...(
          ((response.result || {})[WidgetTypeConstants.GRID] || {}).data || []
        )
          .map(transformNotificationForList)
          .filter((notification) => notification.title !== ''),
      ]
      this.handleNotificationReceived(this.fragmentedData)
      setTimeout(() => {
        this.loading = false
      }, 0)
      if (response.result.error) {
        this.error = response.result.error
        if (response.result.queryMeta.progress >= 100) {
          this.stopHeartbeat()
          return
        }
      }

      if (response.result.queryMeta.progress >= 100) {
        return
      }
      if (this.currentBatch === 1) {
        this.sendActiveSessionEvent()
        // start active session event
        this.scheduleUpdate()
        // ask first 50 records
      }
      if (response.result.queryMeta.progress < 100) {
        if (this.isPaused) {
          return
        }
        setTimeout(() => {
          this.$nextTick(() => {
            this.requestNextBatch()
          })
        }, 100)
      }
    },

    async requestNextBatch() {
      this.currentBatch++
      this.startNotificationIncremental(this.__parentQueryId)
    },
    abortQuery() {
      if (this.__parentQueryId) {
        this.queryProgress = null
        this.isPaused = true
        Bus.$emit('server:event', {
          'event.type': this.$constants.UI_WIDGET_ABORT_EVENT,
          'event.context': {
            'query.id': this.__parentQueryId,
          },
        })
      }
    },
    sendActiveSessionEvent() {
      if (!this.__parentQueryId) {
        return
      }
      Bus.$emit('server:event', {
        'event.type': this.$constants.UI_WIDGET_ACTIVE_SESSION,
        'event.context': {
          'query.id': this.__parentQueryId,
          [this.$constants.UI_EVENT_UUID]: this.guid,
        },
      })
    },
    scheduleUpdate() {
      this.stopHeartbeat()
      this.__streamingTimer = setInterval(this.sendActiveSessionEvent, 10000)
    },

    stopHeartbeat() {
      if (this.__streamingTimer) {
        clearInterval(this.__streamingTimer)
        this.__streamingTimer = null
      }
    },
    handleHidePoppver() {
      this.stopHeartbeat()
      this.abortQuery()
    },
    handleCountClear(event) {
      this.count = event.result
    },
  },
}
</script>

<style lang="less">
@notification-panel-border-width: 10px;

.notification-badge {
  .@{ant-prefix}-badge-count {
    top: 5px;
    right: 5px;
    height: 15px;
    padding: 0 5px;
    font-size: 0.7rem;
    line-height: 15px;
    color: white;
    background: var(--secondary-red);
    .@{ant-prefix}-scroll-number-only,
    p {
      height: 15px;
    }
  }
}

.notification-dropdown-panel {
  .@{ant-prefix}-popover-arrow {
    display: none;
  }
  .@{ant-prefix}-popover-inner-content {
    display: flex;
    flex-direction: colummn;
    width: 600px;
    height: 65vh;
    padding: 0;
    background: var(--notification-dropdown-background);
    border-radius: @notification-panel-border-width;
  }

  .@{ant-prefix}-popover-inner {
    border-radius: @notification-panel-border-width;
  }

  .notification-dropdown-content {
    .header {
      background: var(--notification-dropdown-header);
      border-top-left-radius: @notification-panel-border-width;
      border-top-right-radius: @notification-panel-border-width;
    }

    .footer {
      height: 36px;
      background: var(--notification-dropdown-footer);
      border-bottom-right-radius: @notification-panel-border-width;
      border-bottom-left-radius: @notification-panel-border-width;
    }
  }
}

.notification-bar {
  &:hover {
    background: var(--notification-dropdown-background-hover) !important;
  }
}
</style>
