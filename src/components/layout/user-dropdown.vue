<template>
  <MPopover
    v-if="user"
    ref="popoverMenu"
    :destroy-tooltip-on-hide="false"
    trigger="click"
    placement="bottomRight"
    overlay-class-name="headerUserMenu header-user-dropmenu"
  >
    <a id="user-avatar" slot="trigger" href="javascript:;">
      <FlotoUserAvatar
        size="large"
        class="rounded-full cursor-pointer"
        :avatar="user.avatarUrl"
      />
    </a>
    <div class="page-background-color flex flex-col rounded">
      <div class="bg-neutral-lightest rounded">
        <div class="flex flex-no-wrap w-full min-w-0 py-4 px-4 border-bot">
          <div>
            <FlotoUserAvatar
              :size="50"
              class="rounded-full cursor-auto avatar-photo-box"
              :avatar="user.avatarUrl"
            />
          </div>
          <div class="flex-1 min-w-0">
            <div class="mb-0 pl-4 user-name-title text-ellipsis">
              {{ user.name }}
            </div>
            <div
              class="mb-0 pl-4 user-name-sub-title text-ellipsis text-neutral-light"
            >
              {{ userRole.name }}
            </div>
          </div>
        </div>
      </div>
      <div>
        <ul class="user-menu px-4 py-2" @click="hide">
          <li>
            <FlotoLink
              :to="$modules.getModuleRoute('my-account', 'my-profile')"
            >
              <MIcon name="my-profile" size="lg" class="mr-1" /> My Profile
            </FlotoLink>
          </li>
          <li v-if="userRole.name === 'admin'">
            <FlotoLink :to="$modules.getModuleRoute('product-setup')">
              <MIcon name="tour" size="lg" class="mr-1" />
              Product Setup Guide
            </FlotoLink>
          </li>
          <li>
            <a
              href="https://docs.motadata.com/motadata-aiops-docs/"
              target="_blank"
              class="help-support"
            >
              <MIcon name="help" size="lg" class="mr-1" /> Documentation
            </a>
          </li>
          <li class="divider" />
          <li class="logout">
            <a hrem="javascript:;" class="help-support" @click="performLogout">
              Logout
            </a>
          </li>
        </ul>
      </div>
    </div>
  </MPopover>
</template>

<script>
import { authMethods, authComputed } from '@state/modules/auth/'
export default {
  name: 'UserDropdown',
  props: {
    portalMode: { type: Boolean, default: false },
  },
  computed: {
    ...authComputed,
  },
  methods: {
    ...authMethods,
    hide() {
      this.$refs.popoverMenu.hide()
    },
    performLogout() {
      this.logout()
      this.$modules.replace('auth')
    },
    goToSettings() {
      this.$router.push(
        this.$modules.getModuleRoute('my-account', 'my-profile')
      )
      this.hide()
    },
  },
}
</script>
