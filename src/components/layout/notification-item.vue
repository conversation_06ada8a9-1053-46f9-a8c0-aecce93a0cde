<template>
  <div class="flex notification" :class="`${type}-notification`">
    <div
      class="flex min-w-0 ml-2"
      :class="{
        'flex-col': size === 'dropdown',
        'justify-between items-center flex-1 pr-4': size === 'page',
      }"
    >
      <div
        v-if="size === 'dropdown'"
        class="text-ellipsis cursor-pointer text-sm"
        :title="notification.title"
        @click="$emit('change', notification)"
        v-text="notification.title"
      />
      <div
        v-else-if="size === 'page'"
        class="text-ellipsis text-primary text-sm"
        :title="notification.title"
        v-text="notification.title"
      />

      <small v-if="size === 'dropdown'" class="text-neutral cursor-pointer">
        {{ notification.dateTime | datetime }}
      </small>
      <small
        v-else-if="size === 'page'"
        class="text-ellipsis"
        style="flex-shrink: 0"
      >
        {{ notification.dateTime | datetime }}
      </small>
    </div>
  </div>
</template>

<script>
export default {
  name: 'NotificationItem',
  props: {
    isNew: {
      type: Boolean,
      default: false,
    },
    notification: {
      type: Object,
      required: true,
    },
    size: {
      type: String,
      default: 'dropdown',
    },
  },
  computed: {
    iconClass() {
      const type = this.type
      if (type === 'error') {
        return 'text-secondary-red'
      } else if (type === 'warning') {
        return 'text-secondary-yellow'
      } else if (type === 'info') {
        return 'text-neutral'
      } else if (type === 'clear') {
        return 'text-secondary-green'
      }
      return undefined
    },
    icon() {
      const type = this.type
      if (type === 'error') {
        return 'times-circle'
      } else if (type === 'warning') {
        return 'exclamation-triangle'
      } else if (type === 'info') {
        return 'exclamation-circle'
      } else if (type === 'clear') {
        return 'check-circle'
      }
      return 'question'
    },
    type() {
      return (this.notification.type || 'Error').toLowerCase()
    },
  },
}
</script>

<style lang="less" scoped>
.notification {
  // background: var(--page-background-color);
  border-left: 3px solid transparent;
  // border-radius: 4px;

  // @apply shadow-md  h-full flex items-center;

  &.error-notification {
    border-color: var(--secondary-red);
  }

  &.warning-notification {
    border-color: var(--secondary-yellow);
  }

  &.info-notification {
    border-color: var(--neutral-regular);
  }

  &.clear-notification {
    border-color: var(--secondary-green);
  }

  .new {
    color: var(--secondary-green);
    background: rgba(137, 197, 64, 0.2);

    @apply mx-2 my-1 px-2 ml-auto;
  }

  .text-sm {
    font-size: 12px !important;
  }

  small {
    font-size: 11px !important;
    color: var(--notification-dropdown-datetime-color);
  }
}
</style>
