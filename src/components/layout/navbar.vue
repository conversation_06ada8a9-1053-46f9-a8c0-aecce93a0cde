<template>
  <MLayoutSider
    class="mainNavbar nav-bar-panel pt-1 border-right"
    :trigger="null"
    collapsible
    :collapsed="!pinned"
    :collapsed-width="collapsedWidth"
    :width="fullWidth"
    @mouseover="handleMouseOver"
    @mouseout="handleMouseOut"
  >
    <MMenu class="mainMenu" theme="dark" :selected-keys="selectedItem">
      <MMenuItem class="expand-collapse-trigger">
        <!-- <a hrem="javascript:;" @click="$emit('change', !pinned)">
          <MIcon name="bars" type="fas" size="lg" />
        </a>
        <span>{{ pinned ? 'Collapse' : 'Expand' }}</span> -->
        <a
          class="ai-ops-logo"
          style="display: flex; align-items: center"
          :style="!pinned ? { justifyContent: 'center' } : {}"
        >
          <img
            src="@assets/images/logo/motadata.png"
            height="30"
            width="30"
            class="anticon"
          />
          <span class="page-text-color">AIOps</span>
        </a>
      </MMenuItem>
      <template v-for="menu in menuItems">
        <MMenuItem
          v-if="menu.permissions ? hasPermission(menu.permissions) : true"
          :id="menu.key"
          :key="menu.key"
        >
          <FlotoLink :to="menu.link">
            <MIcon :name="menu.icon" size="lg" :style="menu.iconStyle" />
            <span>
              {{ menu.name }}
              <MTag
                v-if="menu.isBeta"
                :closable="false"
                class="cursor-auto mx-2 tag-primary items-center justify-center p-1 inline-flex"
                variant="primary"
              >
                BETA
              </MTag>
            </span>
          </FlotoLink>
        </MMenuItem>
      </template>
    </MMenu>
    <div class="flex items-center">
      <!-- <div v-show="!pinned" class="flex flex-col justify-start">
        <span class="ai-ops-text">AIOps</span>
        <img
          src="@assets/images/logo/motadata.png"
          height="30"
          width="30"
          class="self-center mb-4"
        />
      </div> -->
      <div class="flex-1 relative">
        <div class="mask-group">
          <MaskGroup />
        </div>
      </div>
    </div>
  </MLayoutSider>
</template>

<script>
import FindIndex from 'lodash/findIndex'
import { authComputed } from '@state/modules/auth'
import MaskGroup from './mask-group.vue'

export default {
  name: 'FlotoNavBar',
  components: { MaskGroup },
  model: {
    event: 'change',
  },
  props: {
    portalMode: { type: Boolean, default: false },
  },
  data() {
    return {
      zIndex: 99,
      pinned: false,
      collapsedWidth: 65,
      fullWidth: 170,
      selectedItem: [],
      menuItems: [
        {
          key: 'dashboard',
          name: 'Dashboards',
          link: this.$modules.getModuleRoute('dashboard'),
          icon: 'dashboard',
          permissions: [this.$constants.DASHBOARD_SETTINGS_READ_PERMISSION],
        },
        {
          key: 'inventory',
          name: 'Monitors',
          link: this.$modules.getModuleRoute('inventory'),
          icon: 'navbar-monitor',
          permissions: [this.$constants.INVENTORY_READ_PERMISSION],
        },
        // {
        //   key: 'apm',
        //   name: 'APM',
        //   link: this.$modules.getModuleRoute('apm'),
        //   icon: 'apm',
        // },
        {
          key: 'metric-explorer',
          name: 'Metric Explorer',
          link: this.$modules.getModuleRoute('metric-explorer'),
          icon: 'metric-explorer',
          permissions: [this.$constants.METRIC_EXPLORER_READ_PERMISSION],
        },
        {
          key: 'netroute',
          name: 'NetRoute',
          link: this.$modules.getModuleRoute('netroute'),
          icon: 'netroute',
          isBeta: true,
          permissions: [this.$constants.NETROUTE_EXPLORER_READ_PERMISSION],
        },
        {
          key: 'log',
          name: 'Log Explorer',
          link: this.$modules.getModuleRoute('log'),
          icon: 'log',
          permissions: [this.$constants.LOG_EXPLORER_READ_PERMISSION],
        },
        {
          key: 'flow',
          name: 'Flow Explorer',
          link: this.$modules.getModuleRoute('flow'),
          icon: 'flow',
          permissions: [this.$constants.FLOW_EXPLORER_READ_PERMISSION],
        },
        {
          key: 'alert',
          name: 'Alerts',
          link: this.$modules.getModuleRoute('alert'),
          icon: 'alert',
          permissions: [this.$constants.ALERT_READ_PERMISSION],
        },
        {
          key: 'topology',
          name: 'Topology',
          link: this.$modules.getModuleRoute('topology', '', {
            params: { tab: 'network' },
          }),
          icon: 'topology',
          permissions: [this.$constants.TOPOLOGY_READ_PERMISSION],
        },
        {
          key: 'ncm',
          name: 'NCM',
          link: this.$modules.getModuleRoute('ncm', ''),
          icon: 'ncm',
          permissions: [this.$constants.NCM_READ_PERMISSION],
        },
        {
          key: 'trap-viewer',
          name: 'Trap Explorer',
          link: this.$modules.getModuleRoute('trap-viewer'),
          icon: 'trap-viewer',
          permissions: [this.$constants.TRAP_EXPLORER_READ_PERMISSION],
        },
        {
          key: 'reports',
          name: 'Reports',
          link: this.$modules.getModuleRoute('report'),
          icon: 'report',
          permissions: [this.$constants.REPORT_READ_PERMISSION],
        },
        {
          key: 'audit',
          name: 'Audits',
          link: this.$modules.getModuleRoute('audit'),
          icon: 'audit',
          permissions: [this.$constants.AUDIT_SETTINGS_READ_PERMISSION],
        },
        {
          key: 'settings',
          name: 'Settings',
          link: this.$modules.getModuleRoute('settings'),
          icon: 'settings',
          permissions: [
            this.$constants.MY_ACCOUNT_READ_PERMISSION,
            this.$constants.USER_SETTINGS_READ_PERMISSION,
            this.$constants.SYSTEM_SETTINGS_READ_PERMISSION,
            this.$constants.POLICY_SETTINGS_READ_PERMISSION,
            this.$constants.DISCOVERY_SETTINGS_READ_PERMISSION,
            this.$constants.MONITOR_SETTINGS_READ_PERMISSION,
            this.$constants.SNMP_TRAP_READ_PERMISSION,
            this.$constants.LOG_SETTINGS_READ_PERMISSION,
            this.$constants.FLOW_SETTINGS_READ_PERMISSION,
            this.$constants.PLUGIN_LIBRARY_SETTINGS_READ_PERMISSION,
            this.$constants.AIOPS_SETTINGS_READ_PERMISSION,
            this.$constants.NCM_READ_PERMISSION,
            this.$constants.INTEGRATION_READ_PERMISSION,
          ],
        },
      ],
    }
  },
  computed: {
    ...authComputed,
  },
  watch: {
    $route: {
      immediate: true,
      handler(newValue) {
        this.$nextTick(() => {
          if (/^\/settings/.test(newValue.path)) {
            this.selectedItem = ['settings']
          } else {
            if (newValue.name) {
              this.selectedItem = [newValue.name.split('.')[0]]
            }
          }
        })
      },
    },
  },
  created() {
    if (!this.hasPermission(this.$constants.USER_SETTINGS_READ_PERMISSION)) {
      const menuItem = this.menuItems.find((m) => m.key === 'settings')
      menuItem.link = this.$modules.getModuleRoute('my-account')
      const index = FindIndex(this.menuItems, (m) => m.key === 'settings')
      this.menuItems = [
        ...this.menuItems.slice(0, index),
        menuItem,
        ...this.menuItems.slice(index + 1),
      ]
    }
    if (!this.hasPermission(this.$constants.AUDIT_SETTINGS_READ_PERMISSION)) {
      const menuItems = this.menuItems.filter((m) => m.key !== 'audit')
      this.menuItems = menuItems
    }
  },
  methods: {
    handleMouseOver() {
      document.querySelector('.nav-bar-panel').style.zIndex = 1053
      requestAnimationFrame(() => {
        this.pinned = true
      })
    },
    handleMouseOut() {
      document.querySelector('.nav-bar-panel').style.zIndex = 99
      requestAnimationFrame(() => {
        this.pinned = false
      })
    },
  },
}
</script>
