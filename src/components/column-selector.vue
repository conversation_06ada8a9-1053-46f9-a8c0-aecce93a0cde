<template>
  <FlotoDropdownPicker
    v-model="selectedKeys"
    multiple
    :as-input="false"
    class="inline-flex"
    :options="availableColumns"
    placeemnt="bottomRight"
    use-popover
    remove-clear-btn
    @hide="updateColumns"
  >
    <template v-slot:trigger="{ toggle, isOpen }">
      <slot name="trigger" :toggle="toggle" :isOpen="isOpen">
        <MButton
          id="btn-show-hide-columns"
          :shadow="false"
          :rounded="false"
          :variant="isOpen ? 'neutral-lighter' : 'neutral-lightest'"
          class="squared-button mr-2"
          @click="toggle"
        >
          <MIcon name="eye" />
        </MButton>
      </slot>
    </template>
    <template v-if="!hideResetOption" v-slot:after-menu>
      <div
        class="my-2 px-2 flex justify-between items-center text-neutral-light border-top"
        style="cursor: pointer"
        @click="resetColumns"
      >
        <MIcon name="unlock-alt" class="mt-2" />
        <span class="text-ellipsis ml-2 mt-2">Reset Column Preference</span>
      </div>
    </template>
  </FlotoDropdownPicker>
</template>

<script>
export default {
  name: 'ColumnSelector',
  model: { event: 'change' },
  props: {
    hideResetOption: { type: Boolean, default: false },
    value: {
      type: Array,
      default() {
        return []
      },
    },
    columns: {
      type: Array,
      default() {
        return []
      },
    },
    maxAllowedSelection: {
      type: Number,
      default: undefined,
    },
  },
  data() {
    return {
      selectedKeys: this.columns
        .filter((v) => v.hidden !== true)
        .map((v) => v.key),
    }
  },
  computed: {
    availableColumns() {
      return this.columns.filter(
        (c) =>
          ['actions', 'action'].includes(c.key) === false &&
          c.selectable !== false
      )
    },
  },
  watch: {
    value: {
      immediate: true,
      handler(newValue) {
        if (newValue && newValue.length) {
          this.selectedKeys = newValue
            .filter((v) => v.hidden !== true)
            .map((v) => v.key)
        }
      },
    },
  },
  methods: {
    updateColumns() {
      if (this.maxAllowedSelection) {
        if (this.selectedKeys.length > this.maxAllowedSelection) {
          this.$errorNotification({
            message: 'Error',
            description:
              'The allowed column limit has been reached. Please remove a selected column to proceed.',
          })

          return
        }
      }

      const selectedColumns = this.columns.map((c) => ({
        ...c,
        hidden: this.selectedKeys.indexOf(c.key) === -1,
      }))
      this.$emit('change', selectedColumns)
    },
    resetColumns() {
      this.$emit('change')
    },
  },
}
</script>
