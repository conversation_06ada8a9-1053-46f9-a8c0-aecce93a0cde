<template>
  <div class="flex-1 history-flow-panel min-h-0 flex flex-col">
    <slot name="title"></slot>
    <ul v-if="items.length" class="flex flex-col flex-1 min-h-0">
      <RecycleScroller
        :items="items"
        :item-size="60"
        :key-field="useGuidAsKey ? 'guid' : undefined"
      >
        <template v-slot="{ item }">
          <li
            style="height: 60px"
            :class="{ 'cursor-pointer': $listeners.click }"
            @click="$emit('click', item)"
          >
            <div
              class="date-time"
              :class="{
                [`${(item.severity || '').toLowerCase()}-color`]:
                  !ignoreSeverity,
                'no-highlight': ignoreSeverity,
              }"
            >
              <span class="date mt-1">{{
                item.date | datetime('MM/DD/YYYY')
              }}</span>
              <span class="time">{{ item.time | datetime('hh:mm:ss A') }}</span>
            </div>
            <div
              class="bg flex-1 min-w-0 flex items-center item-content rounded"
              :class="{
                'no-highlight': true,
                'active-item':
                  activeItem &&
                  (useGuidAsKey
                    ? activeItem.guid === item.guid
                    : activeItem.id === item.id),
              }"
              :title="item.text"
            >
              <!-- <span class="severity-icon">
                <Severity
                  v-if="!ignoreSeverity"
                  :disable-tooltip="true"
                  :severity="item.severity"
                  class="text-left"
                />
              </span> -->

              <div
                class="h-full w-full overflow-y-scroll flex-1 min-w-0 flex items-center"
              >
                <span
                  class="text"
                  :class="{
                    [(item.severity || '').toLowerCase()]: !ignoreSeverity,
                    'no-highlight': ignoreSeverity,
                  }"
                  style="max-height: 44px; overflow-wrap: break-word"
                >
                  {{ item.text }}
                </span>
              </div>
              <MButton
                v-if="item.incidentDetails"
                class="squared-button"
                variant="neutral-lightest"
                @click="showIncidentDetailsItem = item"
              >
                <MIcon name="ticket-id" />
              </MButton>
              <slot name="actions" :item="item"></slot>
            </div>
          </li>
        </template>
      </RecycleScroller>
      <IncidentDetailsDrawer
        v-if="showIncidentDetailsItem !== null"
        :incident-item="showIncidentDetailsItem"
        @hide="showIncidentDetailsItem = null"
      />
    </ul>

    <FlotoNoData
      v-else
      hide-svg
      header-tag="h5"
      icon="exclamation-triangle"
      variant="neutral"
      message="No history found for selected timeline"
    />
  </div>
</template>

<script>
// import Severity from '@components/severity.vue'
import IncidentDetailsDrawer from '@modules/alert/components/stream/incident-details-drawer.vue'

export default {
  name: 'Timeline',
  components: {
    // Severity,
    IncidentDetailsDrawer,
  },
  props: {
    items: {
      type: Array,
      default() {
        return []
      },
    },
    ignoreSeverity: {
      type: Boolean,
      default: false,
    },
    activeItem: {
      type: [String, Number, Object],
      default: undefined,
    },
    useGuidAsKey: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      showIncidentDetailsItem: null,
    }
  },
}
</script>

<style lang="less">
.history-flow-panel {
  & ul {
    & li {
      .hover-action {
        visibility: hidden;
      }

      &:hover {
        .hover-action {
          visibility: visible;
        }
      }
    }
  }
}
</style>

<style lang="less" scoped>
.history-flow-panel {
  float: left;
  width: 100%;

  & ul {
    padding: 0;
    margin: 0;
    overflow: auto;
    list-style: none;

    @apply mb-2;

    & li {
      display: flex;
      width: 100%;
      padding-bottom: 10px;
      overflow: hidden;

      .hover-action {
        visibility: hidden;
      }

      &:hover {
        .hover-action {
          visibility: visible;
        }
      }

      &.active-item {
        background: red !important;
      }

      &:last-child {
        // padding-bottom: 0;
        &::before {
          height: 40px;
        }
      }

      & div.date-time {
        position: relative;
        display: flex;
        flex-direction: column;
        width: 120px;
        padding-right: 35px;
        margin-right: 10px;
        text-align: right;
        opacity: 0.7;

        &::after {
          position: absolute;
          top: 17px;
          right: 10px;
          float: left;
          width: 11px;
          height: 11px;
          content: '';
          background: var(--page-background-color);
          border: solid 3px var(--neutral-light);
          border-radius: 150px;
        }

        &::before {
          position: absolute;
          top: 0;
          right: 15px;
          width: 1px;
          height: 250px;
          content: '';
          background: var(--neutral-light);
        }

        &.down-color {
          &::after {
            border-color: var(--severity-down);
          }
        }

        &.unreachable-color {
          &::after {
            border-color: var(--severity-unreachable);
          }
        }

        &.critical-color {
          &::after {
            border-color: var(--severity-critical);
          }
        }

        &.major-color {
          &::after {
            border-color: var(--severity-major);
          }
        }

        &.warning-color {
          &::after {
            border-color: var(--severity-warning);
          }
        }

        &.clear-color {
          &::after {
            border-color: var(--severity-clear);
          }
        }

        &.maintenance-color::after {
          &::after {
            border-color: var(--severity-maintenance);
          }
        }

        &.unknown-color {
          &::after {
            border-color: var(--severity-unknown);
          }
        }

        &.disable-color {
          &::after {
            border-color: var(--severity-disable);
          }
        }

        &.none-color {
          &::after {
            border-color: var(--severity-none);
          }
        }

        &.no-highlight-color {
          &::after {
            border-color: var(--grid-header-bg);
          }
        }
      }

      & .dot-line {
        position: relative;
        display: none;
        float: left;
        width: 7%;
        text-indent: -10000px;
      }

      & .item-content {
        position: relative;
        padding: 8px 10px;
        padding-left: 10px;
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;

        & .text {
          float: left;
          color: var(--page-text-color);

          &.no-highlight {
            padding-left: 0;
          }
        }

        & .severity-icon {
          position: absolute;
          float: left;
          margin-top: 2px;
          margin-right: 10px;
        }

        &::after {
          position: absolute;
          top: 25px;
          right: 100%;
          width: 0;
          height: 0;
          margin-top: -10px;
          pointer-events: none;
          content: '';
          border: solid transparent;
          border-width: 10px;
          border-right-color: transparent;
        }

        &.down {
          &::after {
            border-right-color: var(--severity-down-lighter);
          }
        }

        &.unreachable {
          &::after {
            border-right-color: var(--severity-unreachable-lighter);
          }
        }

        &.critical {
          &::after {
            border-right-color: var(--severity-critical-lighter);
          }
        }

        &.major {
          &::after {
            border-right-color: var(--severity-major-lighter);
          }
        }

        &.warning {
          &::after {
            border-right-color: var(--severity-major-lighter);
          }
        }

        &.clear {
          &::after {
            border-right-color: var(--severity-clear-lighter);
          }
        }

        &.maintenance {
          &::after {
            border-right-color: var(--severity-maintenance-lighter);
          }
        }

        &.unknown {
          &::after {
            border-right-color: var(--severity-unknown-lighter);
          }
        }

        &.disable {
          &::after {
            border-right-color: var(--severity-disable-lighter);
          }
        }

        &.none {
          &::after {
            border-right-color: var(--severity-none-lighter);
          }
        }

        &.no-highlight {
          background: var(--grid-header-bg);

          &::after {
            border-right-color: var(--grid-header-bg);
          }
        }

        &.active-item {
          background: fade(#3279be, 20) !important;

          &::after {
            border-right-color: fade(#3279be, 20);
          }
        }
      }
    }
  }
}
</style>
