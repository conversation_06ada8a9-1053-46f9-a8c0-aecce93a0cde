<script>
import Bus from '@utils/emitter'
import exportScrollableElement, {
  getScrollableElement,
} from '@utils/capture-scrollable-element'

export default {
  name: 'ScrollableExport',
  inject: ['ScreenBlockerContext'],
  props: {
    useQueue: {
      type: <PERSON>olean,
      default: false,
    },
  },
  methods: {
    async capture(htmlElement, fileName, message, options) {
      const captureHandler = async () => {
        document.querySelectorAll('.create-widget-btn').forEach((i) => {
          i.style.display = 'none'
        })
        this.ScreenBlockerContext.block(message || 'Exporting...')
        await exportScrollableElement(htmlElement, fileName, options)
        this.ScreenBlockerContext.unblock()
        document.querySelectorAll('.create-widget-btn').forEach((i) => {
          i.style.display = 'block'
        })
      }
      if (this.useQueue) {
        Bus.$once('current:widget:queue', async (queue) => {
          if (queue) {
            await queue.add(captureHandler)
          } else {
            captureHandler()
          }
        })
        Bus.$emit('widgets:export')
      } else {
        captureHandler()
      }
    },
    async getScrollableElement(htmlElement, fileName, message, options) {
      const captureHandler = async () => {
        let image
        document.querySelectorAll('.create-widget-btn').forEach((i) => {
          i.style.display = 'none'
        })
        this.ScreenBlockerContext.block(message || 'Exporting...')
        image = await getScrollableElement(htmlElement, fileName, options)

        this.ScreenBlockerContext.unblock()
        document.querySelectorAll('.create-widget-btn').forEach((i) => {
          i.style.display = 'block'
        })

        return image
      }

      return captureHandler()
    },
  },
  render() {
    return this.$scopedSlots.default({
      convertToImage: this.capture,
    })
  },
}
</script>
