<template>
  <FlotoContentLoader :loading="loading">
    <div v-if="counters.length" class="flex flex-col min-w-0">
      <SectionItemRow
        v-for="item in counters"
        :key="item.counter"
        :title="item.counter"
        :value="item.formatted || item.value"
      />
    </div>
    <FlotoNoData v-else />
  </FlotoContentLoader>
</template>

<script>
import Bus from '@utils/emitter'
import { isUnitConvertible } from '@utils/unit-checker'
import applyUnit from '@utils/unit-applier'
import { generateId } from '@utils/id'
import {
  FILTER_CONDITION_DEFAULT_DATA,
  WidgetTypeConstants,
} from '@components/widgets/constants'
import WidgetContextBuilder from '@components/widgets/widget-context-builder'
import SectionItemRow from './section-item-row.vue'
import { getVmType } from '@/src/utils/vm-map'

const MONITOR_EXCLUDED_COUNTER_NAMES = ['monitor', 'entity.id']
const WIRELESS_EXCLUDED_COUNTER_NAMES = ['object.vendor']

export default {
  name: 'RawCounterSection',
  components: {
    SectionItemRow,
  },
  inheritAttrs: false,
  props: {
    monitor: {
      type: Object,
      required: true,
    },
    vm: {
      type: String,
      default: undefined,
    },
    application: {
      type: String,
      default: undefined,
    },
    section: {
      type: Object,
      required: true,
    },
    isNetworkDevice: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      uuid: generateId(),
      loading: true,
      counters: [],
    }
  },
  created() {
    Bus.$on(this.$constants.UI_WIDGET_RESULT_EVENT, this.handleDataReceived)
    this.$once('hook:beforeDestroy', () => {
      Bus.$off(this.$constants.UI_WIDGET_RESULT_EVENT, this.handleDataReceived)
    })
    this.getData()
  },
  methods: {
    handleDataReceived(data) {
      if (!data) {
        return
      }
      if (data[this.$constants.UI_EVENT_UUID] !== this.uuid) {
        return
      }
      if (((data.result || {})[WidgetTypeConstants.GRID] || {}).data) {
        this.addCounters(
          ((data.result || {})[WidgetTypeConstants.GRID] || {}).data[0]
        )
      } else {
        this.loading = false
      }
    },
    addCounters(data) {
      this.counters = Object.freeze(
        Object.keys(data || {})
          .map((counter) => {
            if (
              !this.isNetworkDevice &&
              WIRELESS_EXCLUDED_COUNTER_NAMES.includes(counter) &&
              ![
                this.$constants.NETAPP,
                this.$constants.HPE_STOREONCE,
                this.$constants.HPE_PRIMERA,
                this.$constants.HPE_3PAR,
                this.$constants.DELL_EMC_UNITY,
              ].includes(this.monitor.type)
            ) {
              return false
            }
            if (MONITOR_EXCLUDED_COUNTER_NAMES.includes(counter)) {
              return false
            }
            if (/\.formatted/.test(counter)) {
              return false
            }
            let c = {
              counter: counter.replace(/\.last/, ''),
              value: data[counter],
            }
            if (isUnitConvertible(counter)) {
              c[`formatted`] = applyUnit(counter, data[counter])
            }
            return c
          })
          .filter(Boolean)
      )
      this.loading = false
    },
    getData() {
      this.loading = true
      if (this.monitor && this.section.counters.length > 0) {
        const context = new WidgetContextBuilder()
        context.addGroup('metric')
        context.setCategory(WidgetTypeConstants.GRID)
        context.setWidgetType(WidgetTypeConstants.GRID)
        context.setTimeLine({ selectedKey: 'today' })
        this.section.counters.forEach((counter) => {
          context.addCounterToGroup({
            counter,
            aggrigateFn: 'last',
            entityType: 'Monitor',
            entities: [+(this.monitor.resourceId || this.monitor.id)],
          })
        })
        if (this.vm) {
          const vmType = getVmType(this.monitor.type)
          context.addPreFilterGroup({
            ...FILTER_CONDITION_DEFAULT_DATA.groups[0],
            conditions: [
              {
                operand: vmType,
                operator: '=',
                value: this.vm,
              },
            ],
          })
        }
        context.addResultBy('monitor')
        Bus.$emit('server:event', {
          'event.type': this.$constants.UI_WIDGET_RESULT_EVENT,
          'event.context': {
            ...context.generateWidgetDefinition(),
            id: -1,
            [this.$constants.UI_EVENT_UUID]: this.uuid,
          },
        })
      } else {
        this.loading = false
      }
    },
  },
}
</script>
