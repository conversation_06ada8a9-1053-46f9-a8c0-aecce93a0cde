<template>
  <RpeProvider>
    <div class="flex flex-col min-w-0">
      <SectionItemRow
        v-if="vm || application || ap"
        :title="vm ? 'VM' : ap ? 'Access Point' : 'Application'"
        :value="vm || application || ap"
      >
        <FlotoLink
          v-if="showTemplateLink"
          class="text-ellipsis"
          :title="vm || application || ap"
          :to="route"
          target="_blank"
        >
          {{ vm || application || ap }}
        </FlotoLink>
      </SectionItemRow>
      <SectionItemRow
        title="Name"
        :value="
          monitor.category === $constants.SERVICE_CHECK
            ? monitor.serviceCheckName
            : monitor.name || monitor.target
        "
      >
        <FlotoLink
          v-if="showTemplateLink"
          class="text-ellipsis"
          :title="
            monitor.category === $constants.SERVICE_CHECK
              ? monitor.serviceCheckName
              : monitor.name || monitor.target
          "
          :to="monitorRoute"
          target="_blank"
        >
          {{
            monitor.category === $constants.SERVICE_CHECK
              ? monitor.serviceCheckName
              : monitor.name || monitor.target
          }}
        </FlotoLink>

        <template v-else>
          {{
            monitor.category === $constants.SERVICE_CHECK
              ? monitor.serviceCheckName
              : monitor.name || monitor.target
          }}</template
        >
      </SectionItemRow>
      <SectionItemRow
        v-if="monitor.host"
        title="Host Name"
        :value="monitor.host"
      />

      <SectionItemRow title="IP" :value="monitor.ip || '-'" />
      <SectionItemRow
        v-if="monitor.category === $constants.NETWORK"
        title="OID"
        :value="monitor.oid"
      />
      <SectionItemRow title="Type">
        <MonitorType :type="monitor.type" :center="false" />
      </SectionItemRow>
      <SectionItemRow title="Group">
        <GroupPicker disabled :value="monitor.groups" />
      </SectionItemRow>
      <SectionItemRow title="Motadata Collector">
        <RpePicker disabled :value="monitor.rpe" />
      </SectionItemRow>
    </div>
  </RpeProvider>
</template>

<script>
import MonitorType from '@components/monitor-type.vue'
import RpeProvider from '@components/data-provider/rpe-provider.vue'
import SectionItemRow from './section-item-row.vue'

export default {
  name: 'MonitorInfoSection',
  components: {
    SectionItemRow,
    RpeProvider,
    MonitorType,
  },
  inheritAttrs: false,
  props: {
    showTemplateLink: {
      type: Boolean,
      default: false,
    },
    monitor: {
      type: Object,
      required: true,
    },
    application: {
      type: String,
      default: undefined,
    },
    vm: {
      type: String,
      default: undefined,
    },
    ap: {
      type: String,
      default: undefined,
    },
  },
  computed: {
    route() {
      const data = this.monitor
      if (!data) {
        return undefined
      }
      if (this.application) {
        return this.$modules.getModuleRoute('inventory', 'application-tab', {
          params: {
            monitorId: data.id,
            applicationName: this.application,
          },
        })
      }
      if (this.vm) {
        return this.$modules.getModuleRoute('inventory', 'vm-template', {
          params: {
            monitorId: data.id,
            vm: this.vm,
            category: this.$constants.VIRTUALIZATION,
          },
        })
      }
      if (this.ap) {
        return this.$modules.getModuleRoute('inventory', 'ap-template', {
          params: {
            monitorId: data.id,
            ap: this.ap,
            category: this.$constants.NETWORK,
          },
        })
      }
      return this.$modules.getModuleRoute('inventory', 'monitor-template', {
        params: { category: data.category, monitorId: data.id },
      })
    },
    monitorRoute() {
      const data = this.monitor
      if (!data) {
        return undefined
      }

      return this.$modules.getModuleRoute('inventory', 'monitor-template', {
        params: { category: data.category, monitorId: data.id },
      })
    },
  },
}
</script>
