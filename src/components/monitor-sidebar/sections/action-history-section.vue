<template>
  <RunbookPluginProvider>
    <IncrementalResultProvider
      ref="resultProviderRef"
      :serverside-widget-defination="widetDef"
      disabled-streaming-timer
      @patchRecived="patchRecived"
    >
      <FlotoContentLoader :loading="loading" class="h-full w-full">
        <div class="min-h-0 flex flex-col flex-1 w-full">
          <div class="m-2 flex justify-between mt-3">
            <MInput
              v-model="searchTerm"
              class="search-box"
              placeholder="Search"
            >
              <template v-slot:prefix>
                <MIcon name="search" />
              </template>
              <template v-if="searchTerm" v-slot:suffix>
                <MIcon
                  name="times-circle"
                  class="text-neutral-light cursor-pointer"
                  @click="searchTerm = undefined"
                />
              </template>
            </MInput>
          </div>
          <!-- <div v-if="showFilter && !fullscreen" class="px-4 my-2 pr-6">
          <Filters
            v-model="filters"
            @change="applyFilter"
            @hide="showFilter = !showFilter"
          />
        </div> -->

          <div class="flex-1 overflow-auto">
            <MGrid
              class="min-w-0 h-full umt-0"
              :columns="columns"
              :data="rows"
              :paging="true"
              :search-term="searchTerm"
            >
              <template v-slot:operation="{ item }">
                <MButton
                  variant="transparent"
                  :shadow="false"
                  :rounded="false"
                  class="p-0"
                  @click="showItemForLastPerfomAction = item"
                >
                  <span class="text-primary">{{ item.operation }}</span>
                </MButton>
              </template>

              <template v-slot:timestamp="{ item }">
                {{ Math.round(item.timestamp / 1000) | datetime }}
              </template>

              <template v-slot:runbookId="{ item }">
                <RunbookPluginPicker
                  ref="RunbookPluginPickerRef"
                  disabled
                  :value="item.runbookId"
                  text-only
                />
              </template>

              <template v-slot:status="{ item }">
                <NCMCredentialStatus
                  :status="item.status"
                  use-status-map
                  use-pill
                />
              </template>

              <template v-slot:viewResult="{ item }">
                <a v-if="item" id="view-result" @click="openViewResult(item)">
                  View Result
                </a>
              </template>
            </MGrid>
          </div>
        </div>
      </FlotoContentLoader>
    </IncrementalResultProvider>
  </RunbookPluginProvider>
</template>

<script>
import {
  // getNcmDetails,
  generateRunbookWidgetDefinition,
  transformRunbookResult,
} from '@modules/settings/plugin-library/helpers/runbook-plugin'

import NCMCredentialStatus from '@modules/settings/ncm-settings/components/ncm-credential-status.vue'
import IncrementalResultProvider from '@components/data-provider/incremental-result-provider.vue'
import RunbookPluginProvider from '@/src/components/data-provider/runbook-plugin-provider.vue'

import RunbookPluginPicker from '@components/data-picker/runbook-plugin-picker.vue'

export default {
  name: 'ActionHistorySection',
  components: {
    NCMCredentialStatus,
    IncrementalResultProvider,
    RunbookPluginProvider,
    RunbookPluginPicker,
  },
  props: {
    monitor: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      rows: [],
      loading: true,
      searchTerm: undefined,
    }
  },
  computed: {
    columns() {
      return [
        {
          key: 'runbookIdArray',
          name: 'Action',
          searchable: true,
          sortable: true,
          contextKey: 'runbookPluginContext',
          cellRender: 'runbookId',
          searchKey: 'runbookDisplay',
          sortKey: 'runbookDisplay',
        },
        {
          key: 'status',
          name: 'STATUS',
          searchable: true,
          sortable: true,
        },
        {
          key: 'timestamp',
          name: 'Timestamp',
          searchable: true,
          sortable: true,
        },

        {
          key: 'userName',
          name: 'Executed By',
          searchable: true,
          sortable: true,
        },
        {
          key: 'viewResult',
          name: 'Result',
          searchable: true,
          sortable: true,
        },
      ]
    },

    widetDef() {
      return generateRunbookWidgetDefinition(this.monitor.id)
    },
  },

  methods: {
    patchRecived(data) {
      this.loading = false
      this.rows = [
        ...this.rows,
        ...(data || []).map((row) => transformRunbookResult(row)),
      ]
    },

    requestIncrementalData() {
      this.loading = true
      this.rows = []

      if (this.$refs.resultProviderRef) {
        this.$refs.resultProviderRef.requestData()
      }
    },
    openViewResult(item) {
      this.$emit('show-action-result', {
        ...item,
        ignoreAutoFetch: true,
      })
    },
  },
}
</script>
