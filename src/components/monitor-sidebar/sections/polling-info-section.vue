<template>
  <FlotoContentLoader :loading="loading" class="h-full">
    <MonitorPollTable v-if="metricPollTimes.length" :groups="metricPollTimes" />
    <FlotoNoData
      v-else
      hide-svg
      header-tag="h5"
      icon="exclamation-triangle"
      variant="neutral"
    />
  </FlotoContentLoader>
</template>

<script>
import { generateId } from '@utils/id'
import Bus from '@utils/emitter'
import MonitorPollTable from '../monitor-poll-table.vue'

export default {
  name: 'PolllingInfoSection',
  components: { MonitorPollTable },
  props: {
    monitor: {
      type: Object,
      required: true,
    },
    application: {
      type: String,
      default: undefined,
    },
    vm: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      loading: true,
      metricPollTimes: [],
      uuid: generateId(),
    }
  },
  created() {
    Bus.$on(this.$constants.UI_EVENT_LAST_POLL_TIME, this.handleReceiveLastPoll)
    this.askForData()
  },
  beforeDestroy() {
    Bus.$off(
      this.$constants.UI_EVENT_LAST_POLL_TIME,
      this.handleReceiveLastPoll
    )
  },
  methods: {
    askForData() {
      Bus.$emit('server:event', {
        'event.type': this.$constants.UI_EVENT_LAST_POLL_TIME,
        'event.context': {
          [this.$constants.UI_EVENT_UUID]: this.uuid,
          id:
            this.monitor.monitorId ||
            this.monitor.resourceId ||
            this.monitor.id,
          ...(this.application ? { application: this.application } : {}),
          ...(this.vm ? { vm: this.vm } : {}),
        },
      })
    },
    handleReceiveLastPoll(e) {
      if (e[this.$constants.UI_EVENT_UUID] === this.uuid) {
        if (
          e.result &&
          e.result.id ===
            (this.monitor.monitorId ||
              this.monitor.resourceId ||
              this.monitor.id)
        ) {
          this.lastPollTime = e.result['event.timestamp']
          const excludedKeys = ['event.timestamp', 'id']
          this.metricPollTimes = Object.keys(e.result)
            .filter((key) => excludedKeys.includes(key) === false)
            .map((key) => ({
              metricGroup: key,
              timestamp: e.result[key],
              id: `${e.result[key]}-${key}`,
            }))
        }
        this.loading = false
      }
    },
  },
}
</script>
