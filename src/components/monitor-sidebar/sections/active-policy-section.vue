<template>
  <MGrid
    v-if="rows.length"
    class="min-w-0"
    :columns="columns"
    :data="rows"
    :paging="true"
  >
    <template v-slot:policy_name="{ item }">
      <div class="flex items-center w-full">
        <Severity
          disable-tooltip
          :severity="item.severity"
          :center="false"
          class="mr-1"
        />
        <AlertDrilldown :alert="item" :field="item.policy_name" traget-blank />
      </div>
    </template>

    <template v-slot:instance="{ item }">
      <span class="m-0" :title="item.instance">{{ item.instance }}</span>
    </template>

    <template v-slot:value="{ item }">
      <MStatusTag
        v-if="(item.metric || '').includes('status')"
        :status="item.value"
      />
      <MTag
        v-else
        :closable="false"
        rounded
        class="tag-primary text-ellipsis cursor-auto"
        style="max-width: 200px"
        :title="item.value"
        >{{ item.value }}</MTag
      >
    </template>
  </MGrid>

  <FlotoNoData v-else />
</template>

<script>
import Severity from '@components/severity.vue'
import AlertDrilldown from '@components/widgets/views/grid/view-more/alert-drilldown.vue'
import { severityDBWorker } from '@/src/workers'
export default {
  name: 'ActivePolicy',
  components: {
    // MonitorAlertCountProvider,
    Severity,
    AlertDrilldown,
  },
  inject: { policyGridContext: { default: { data: {} } } },
  props: {
    monitor: {
      type: Object,
      required: true,
    },
  },
  data() {
    this.columns = [
      {
        key: 'policy_name',
        name: 'Policy Name',
        searchable: true,
        sortable: true,
      },
      {
        key: 'instance',
        name: 'Instance',
        searchable: true,
        sortable: true,
      },
      {
        key: 'value',
        name: 'Value',
        searchable: true,
        sortable: true,
      },
    ]
    return {
      severityText: undefined,
    }
  },
  computed: {
    rows() {
      return (((this.policyGridContext || {}).data || {}).rows || []).filter(
        (row) => (row.severity || '').toLowerCase() !== 'clear'
      )
    },
    widgetParams() {
      return {
        resourceType: 'Monitor',
        id: this.monitor.id,
      }
    },
  },
  created() {
    this.getMonitorSeverity()
  },
  methods: {
    async getMonitorSeverity() {
      let severityObject = await severityDBWorker.getSeverityByEntity(
        this.monitor.id,
        null,
        // this.counter,
        this.monitor.instance
      )

      this.severityText = (severityObject || {}).severity || 'UNKNOWN'
    },
  },
}
</script>
