<template>
  <div class="flex flex-col min-w-0">
    <!-- <SectionItemRow title="System Tags">
      <LooseTags :value="monitor.systemTags" disabled />
    </SectionItemRow> -->
    <SectionItemRow title="Tags">
      <LooseTags :value="monitor.tags || monitor.systemTags" disabled />
    </SectionItemRow>
  </div>
</template>

<script>
import LooseTags from '@components/loose-tags.vue'
import SectionItemRow from './section-item-row.vue'

export default {
  name: 'TagInfoSection',
  components: {
    SectionItemRow,
    LooseTags,
  },
  inheritAttrs: false,
  props: {
    monitor: {
      type: Object,
      required: true,
    },
  },
}
</script>
