<template>
  <div class="flex min-w-0 items-center py-2">
    <div
      class="w-1/3 min-w-0 text-ellipsis px-2 font-500"
      style="flex-shrink: 0; color: var(--neutral-regular)"
      :title="title"
    >
      {{ title }}
    </div>
    <div
      class="w-2/3 min-w-0 px-2 h-full text-ellipsis"
      style="flex-shrink: 0"
      :title="value || '---'"
    >
      <slot>
        {{ value || '---' }}
      </slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SectionItemRow',
  props: {
    title: {
      type: String,
      default: undefined,
    },
    value: {
      type: [String, Array, Number],
      default: undefined,
    },
  },
}
</script>
