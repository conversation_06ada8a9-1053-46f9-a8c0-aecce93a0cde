<template>
  <div v-if="status" class="flex flex-wrap items-center">
    <MStatusTag class="mr-2" :status="status" />
  </div>
</template>

<script>
import { severityDBWorker } from '@/src/workers'
import Bus from '@utils/emitter'

export default {
  name: 'MonitorStatus',
  props: {
    monitor: {
      type: Object,
      required: true,
    },
    application: {
      type: String,
      default: undefined,
    },
    vm: {
      type: String,
      default: undefined,
    },
    ap: {
      type: String,
      default: undefined,
    },
  },

  data() {
    this.vmTypeMapper = {
      'vmware esxi': 'esxi',
      'hyper-v': 'hyperv',
      // 'hyper-v cluster': 'hyperv.cluster',
      'citrix xen': 'citrix.xen',
    }
    this.apTypeMapper = {
      'aruba wireless': 'aruba.wireless.access.point',
      'cisco wireless': 'cisco.wireless.access.point',
      'ruckus wireless': 'ruckus.wireless.access.point',
    }
    return {
      status: null,
    }
  },
  computed: {
    instance() {
      return this.application || this.vm || this.ap
    },
    instanceValue() {
      if (this.instance) {
        if (this.vm) {
          return this.vm
        } else if (this.application) {
          return this.monitor.appProcesses[this.application].name
        } else {
          return this.ap
        }
      }

      return null
    },
    // params() {
    //   return {
    //     entities: [
    //       this.monitor.monitorId || this.monitor.resourceId || this.monitor.id,
    //     ],
    //     ...(this.vm
    //       ? {
    //           'instance.type': `${
    //             this.vmTypeMapper[this.monitor.type.toLowerCase()]
    //           }.vm`,
    //           instance: this.vm,
    //         }
    //       : {}),
    //     ...(this.application
    //       ? {
    //           'instance.type': this.monitor.appProcesses[this.application].type,
    //           instance: this.monitor.appProcesses[this.application].name,
    //         }
    //       : {}),
    //     ...(this.ap
    //       ? {
    //           'instance.type': `${
    //             this.apTypeMapper[this.monitor.type.toLowerCase()]
    //           }`,
    //           instance: this.ap,
    //         }
    //       : {}),
    //   }
    // },
  },
  created() {
    if (this.monitor.id) {
      this.resolveStatus()
    }
    this.bindLiveStatusEvent()
    if (this.instance) {
      this.bindLiveStatusInstanceEvent()
    }
  },
  methods: {
    bindLiveStatusEvent() {
      Bus.$on(this.$constants.EVENT_STATUS_DB_CHANGED, this.monitorStatusUpdate)
      this.$once('hook:beforeDestroy', () => {
        Bus.$off(
          this.$constants.EVENT_STATUS_DB_CHANGED,
          this.monitorStatusUpdate
        )
      })
    },
    bindLiveStatusInstanceEvent() {
      const handler = (event) => {
        if (!event) {
          return this.resolveStatus()
        }
        if (
          event.objectId === this.monitor.id &&
          this.instanceValue &&
          event.instance === this.instanceValue
        ) {
          this.status = event.status
        }
      }
      Bus.$on(this.$constants.EVENT_STATUS_COUNTER_DB_CHANGED, handler)
      this.$once('hook:beforeDestroy', () => {
        Bus.$off(this.$constants.EVENT_STATUS_COUNTER_DB_CHANGED, handler)
      })
    },
    monitorStatusUpdate(id, status) {
      if (this.monitor.id === id) {
        if (!this.instance) {
          this.status = status
        }
      }
    },
    async resolveStatus() {
      let severityObject
      if (this.instance || this.monitor.id) {
        severityObject = await severityDBWorker.getSeverityByEntity(
          this.monitor.id,
          null,
          // this.counter,
          this.instanceValue
        )
        this.status = (severityObject || {}).status || 'UNKNOWN'
        this.$emit('udpate:severity', this.status)
      }
    },
  },
}
</script>
