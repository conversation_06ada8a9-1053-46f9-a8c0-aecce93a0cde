<template>
  <div class="flex flex-1 min-h-0 flex-col">
    <div class="font-500 m-2">{{ title }}</div>
    <div class="flex flex-1 min-h-0">
      <WidgetPreview :widget="widget" hide-title tooltip-out-side />
    </div>
  </div>
</template>

<script>
import { WidgetTypeConstants } from '@components/widgets/constants'
import WidgetPreview from '@components/widgets/preview.vue'
import WidgetContextBuilder from '@components/widgets/widget-context-builder'

export default {
  name: 'Metric<PERSON>hart',
  components: {
    WidgetPreview,
  },
  props: {
    title: {
      type: String,
      default: undefined,
    },
    enableLegend: {
      type: Boolean,
      default: false,
    },
    color: {
      type: String,
      default: undefined,
    },
    counter: {
      type: String,
      required: true,
    },
    monitorId: {
      type: Number,
      required: true,
    },
    instance: {
      type: String,
      default: undefined,
    },
    instanceType: {
      type: String,
      default: undefined,
    },
  },
  computed: {
    widget() {
      const builder = new WidgetContextBuilder()
      builder.addGroup('metric')
      builder.addCounterToGroup({
        counter: this.counter,
        aggrigateFn: 'avg',
        entityType: 'Monitor',
        entities: [this.monitorId],
      })
      builder.setCategory(WidgetTypeConstants.CHART)
      builder.setWidgetType(WidgetTypeConstants.LINE)
      builder.setTimeLine({
        selectedKey: '-1h',
      })
      let widgetProperties = {
        styleSetting: {
          chartOptions: {
            chart: {
              marginRight: 0,
            },
          },
        },
      }
      if (this.color) {
        widgetProperties.styleSetting.chartOptions = {
          ...widgetProperties.styleSetting.chartOptions,
          colors: [this.color],
        }
      }
      if (this.enableLegend) {
        widgetProperties.styleSetting = {
          ...widgetProperties.styleSetting,
          legendEnabled: true,
        }
      }
      builder.setWidgetProperties(widgetProperties)
      if (this.instanceType && this.instance) {
        builder.addInstance(this.instanceType, this.instance)
      }
      return builder.getContext()
    },
  },
}
</script>
