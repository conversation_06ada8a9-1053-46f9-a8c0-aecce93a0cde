import Constants from '@constants'

export const MONITOR_SIDEBAR_METRICS = {
  // SERVER Category
  [Constants.WINDOWS]: [
    {
      title: 'CPU (%)',
      counter: 'system.cpu.percent',
    },
    {
      title: 'Memory (%)',
      counter: 'system.memory.used.percent',
    },
    {
      title: 'Disk (%)',
      counter: 'system.disk.used.percent',
    },
    {
      title: 'Network',
      counter: 'system.network.bytes.per.sec',
    },
  ],
  [Constants.LINUX]: [
    {
      title: 'CPU (%)',
      counter: 'system.cpu.percent',
    },
    {
      title: 'Memory (%)',
      counter: 'system.memory.used.percent',
    },
    {
      title: 'Disk (%)',
      counter: 'system.disk.used.percent',
    },
    {
      title: 'Network',
      counter: 'system.network.bytes.per.sec',
    },
  ],
  [Constants.HP_UX]: [
    {
      title: 'CPU (%)',
      counter: 'system.cpu.percent',
    },
    {
      title: 'Memory (%)',
      counter: 'system.memory.used.percent',
    },
    {
      title: 'Disk (%)',
      counter: 'system.disk.used.percent',
    },
    {
      title: 'Network',
      counter: 'system.network.bytes.per.sec',
    },
  ],
  [Constants.IBM_AIX]: [
    {
      title: 'CPU (%)',
      counter: 'system.cpu.percent',
    },
    {
      title: 'Memory (%)',
      counter: 'system.memory.used.percent',
    },
    {
      title: 'Disk (%)',
      counter: 'system.disk.used.percent',
    },
    {
      title: 'Network',
      counter: 'system.network.bytes.per.sec',
    },
  ],
  [Constants.SOLARIS]: [
    {
      title: 'CPU (%)',
      counter: 'system.cpu.percent',
    },
    {
      title: 'Memory (%)',
      counter: 'system.memory.used.percent',
    },
    {
      title: 'Disk (%)',
      counter: 'system.disk.used.percent',
    },
    {
      title: 'Network',
      counter: 'system.network.bytes.per.sec',
    },
  ],
  // CLOUD Category
  [Constants.AMAZON_DYNAMO_DB]: [
    {
      title: 'Table Size',
      counter: 'aws.dynamodb.table.size.bytes',
    },
    {
      title: 'Throttle Requests',
      counter: 'aws.dynamodb.throttled.requests',
    },
    {
      title: 'User Errors',
      counter: 'aws.dynamodb.user.errors',
    },
    {
      title: 'Query Errors',
      counter: 'aws.dynamodb.query.system.errors',
    },
  ],
  [Constants.AMAZON_EBS]: [
    {
      title: 'Read Ops',
      counter: 'aws.ebs.volume.read.ops.per.sec',
    },
    {
      title: 'Write Ops',
      counter: 'aws.ebs.volume.write.ops.per.sec',
    },
    {
      title: 'Burst Balance (%)',
      counter: 'aws.ebs.volume.burst.balance.percent',
    },
    {
      title: 'Throughput (%)',
      counter: 'aws.ebs.volume.throughput.percent',
    },
  ],
  [Constants.AMAZON_EC2]: [
    {
      title: 'CPU (%)',
      counter: 'aws.ec2.cpu.percent',
    },
    {
      title: 'Disk I/O',
      counter: 'aws.ec2.disk.io.bytes.per.sec',
    },
    {
      title: 'Network',
      counter: 'aws.ec2.network.bytes.per.sec',
    },
    {
      title: 'Disk Ops',
      counter: 'aws.ec2.disk.io.ops.per.sec',
    },
  ],
  [Constants.AMAZON_RDS]: [
    {
      title: 'CPU (%)',
      counter: 'aws.rds.cpu.percent',
    },
    {
      title: 'Connections',
      counter: 'aws.rds.database.connections',
    },
    {
      title: 'Disk Latency',
      counter: 'aws.rds.disk.io.latency.ms',
    },
    {
      title: 'Network',
      counter: 'aws.rds.network.traffic.bytes.per.sec',
    },
  ],
  [Constants.AMAZON_S3]: [
    {
      title: 'Bucket Size',
      counter: 'aws.s3.bucket.bytes',
    },
    {
      title: 'Downloaded',
      counter: 'aws.s3.bucket.downloaded.bytes',
    },
    {
      title: 'Uploaded',
      counter: 'aws.s3.bucket.uploaded.bytes',
    },
    {
      title: 'Latency',
      counter: 'aws.s3.bucket.request.latency.ms',
    },
  ],
  [Constants.AMAZON_SNS]: [
    {
      title: 'Messages (per sec)',
      counter: 'aws.sns.published.messages.per.sec',
    },
    {
      title: 'Delivered Notifications',
      counter: 'aws.sns.delivered.notifications',
    },
    {
      title: 'Published',
      counter: 'aws.sns.published.bytes',
    },
    {
      title: 'SMS Usage Cost',
      counter: 'aws.sns.sms.usage.cost',
    },
  ],
  [Constants.AMAZON_CLOUD_FRONT]: [
    {
      title: 'Requests (per sec)',
      counter: 'aws.cloudfront.requests.per.sec',
    },
    {
      title: 'Latency',
      counter: 'aws.cloudfront.origin.latency.ms',
    },
    {
      title: 'Cache Hit Rate (%)',
      counter: 'aws.cloudfront.cache.hit.ratio.percent',
    },
    {
      title: 'Downloaded',
      counter: 'aws.cloudfront.downloaded.bytes.per.sec',
    },
  ],
  [Constants.AWS_AUTO_SCALING]: [
    {
      title: 'Desired Capacity',
      counter: 'aws.autoscaling.group.desired.capacity',
    },
    {
      title: 'Total Instances',
      counter: 'aws.autoscaling.group.instances',
    },
    {
      title: 'In Service',
      counter: 'aws.autoscaling.group.in.service.instances',
    },
    {
      title: 'Pending',
      counter: 'aws.autoscaling.group.pending.instances',
    },
  ],
  [Constants.AWS_LAMBDA]: [
    {
      title: 'Invocations',
      counter: 'aws.lambda.invocations',
    },
    {
      title: 'Errors',
      counter: 'aws.lambda.errors',
    },
    {
      title: 'Duration',
      counter: 'aws.lambda.duration.ms',
    },
    {
      title: 'Memory Usage',
      counter: 'aws.lambda.memory.size.bytes',
    },
  ],
  [Constants.AWS_ELASTIC_BEANSTALK]: [
    {
      title: '4xx Requests',
      counter: 'aws.elasticbeanstalk.application.4xx.requests',
    },
    {
      title: 'Latency',
      counter: 'aws.elasticbeanstalk.application.p99.latency.seconds',
    },
    {
      title: 'Requests',
      counter: 'aws.elasticbeanstalk.application.requests',
    },
    {
      title: 'CPU User (%)',
      counter: 'aws.elasticbeanstalk.cpu.user.percent',
    },
  ],

  [`${Constants.AWS_ELB}-application`]: [
    {
      title: '2XX Responses',
      counter: 'aws.elb.2xx.responses',
    },
    {
      title: 'Response Time',
      counter: 'aws.elb.target.response.time.ms',
    },
    {
      title: 'Request Rate',
      counter: 'aws.elb.requests.rate',
    },
    {
      title: '4XX Responses',
      counter: 'aws.elb.4xx.responses',
    },
  ],

  [`${Constants.AWS_ELB}-network`]: [
    {
      title: 'Processed Bytes',
      counter: 'aws.elb.processed.bytes.rate',
    },
    {
      title: 'Active Flows',
      counter: 'aws.elb.active.flows',
    },
    {
      title: 'Client Negotiation Errors',
      counter: 'aws.elb.client.tls.negotiation.errors',
    },
    {
      title: 'Target Negotiation Errors',
      counter: 'aws.elb.target.tls.negotiation.errors',
    },
  ],

  [Constants.AMAZON_SQS]: [
    {
      title: 'Sent Messages',
      counter: 'aws.sqs.sent.messages',
    },
    {
      title: 'Received Messages',
      counter: 'aws.sqs.received.messages',
    },
    {
      title: 'Delayed Messages',
      counter: 'aws.sqs.delayed.messages',
    },
    {
      title: 'Sent',
      counter: 'aws.sqs.sent.bytes',
    },
  ],
  [Constants.AZURE_SQL_DATABASE]: [
    {
      title: 'CPU (%)',
      counter: 'azure.sql.database.cpu.percent',
    },
    {
      title: 'I/O (%)',
      counter: 'azure.sql.database.data.io.percent',
    },
    {
      title: 'Session Utilization (%)',
      counter: 'azure.sql.database.session.utilization.percent',
    },
    {
      title: 'Process Utilization (%)',
      counter: 'azure.sql.database.sql.server.process.core.utilization.percent',
    },
  ],
  [Constants.AZURE_COSMOS_DB]: [
    {
      title: 'Data Usage',
      counter: 'azure.cosmos.db.data.usage.bytes',
    },
    {
      title: 'Requests (per sec)',
      counter: 'azure.cosmos.db.requests.per.sec',
    },
    {
      title: 'Availability (%)',
      counter: 'azure.cosmos.db.service.availability.percent',
    },
    {
      title: 'Index Usage',
      counter: 'azure.cosmos.db.index.usage.bytes',
    },
  ],
  [Constants.AZURE_STORAGE]: [
    {
      title: 'Availability (%)',
      counter: 'azure.storage.availability.percent',
    },
    {
      title: 'Transactions',
      counter: 'azure.storage.transactions',
    },
    {
      title: 'Blobs',
      counter: 'azure.storage.blobs',
    },
    {
      title: 'Queue',
      counter: 'azure.storage.queues',
    },
  ],
  [Constants.AZURE_VM]: [
    {
      title: 'CPU (%)',
      counter: 'azure.vm.cpu.percent',
    },
    {
      title: 'Disk I/O',
      counter: 'azure.vm.disk.io.bytes.per.sec',
    },
    {
      title: 'Memory',
      counter: 'azure.vm.memory.used.bytes',
    },
    {
      title: 'Network',
      counter: 'azure.vm.network.traffic.bytes.per.sec',
    },
  ],
  [Constants.AZURE_WEB_APP]: [
    {
      title: 'Requests',
      counter: 'azure.webapp.requests.per.sec',
    },
    {
      title: 'CPU Time (Sec)',
      counter: 'azure.webapp.cpu.time.seconds',
    },
    {
      title: 'Connections',
      counter: 'azure.webapp.connections',
    },
    {
      title: '5XX Errors',
      counter: 'azure.webapp.HTTP5xx.requests',
    },
  ],
  [Constants.AZURE_SERVICE_BUS]: [
    {
      title: 'Active Connections',
      counter: 'azure.psb.active.connections',
    },
    {
      title: 'Active Messages',
      counter: 'azure.psb.active.messages',
    },
    {
      title: 'Errors',
      counter: 'azure.psb.server.errors',
    },
    {
      title: 'Successful Requests',
      counter: 'azure.psb.successful.requests',
    },
  ],
  [Constants.AZURE_APPLICATION_GATEWAY]: [
    {
      title: 'Active Connections',
      counter: 'azure.application.gateway.active.connections',
    },
    {
      title: 'CPU (%)',
      counter: 'azure.application.gateway.cpu.percent',
    },
    {
      title: 'Throughput',
      counter: 'azure.application.gateway.throughput.bytes.per.sec',
    },
    {
      title: 'Requests (per sec)',
      counter: 'azure.application.gateway.requests.per.sec',
    },
  ],
  [Constants.AZURE_FUNCTION]: [
    {
      title: 'Connections',
      counter: 'azure.function.connections',
    },
    {
      title: 'Memory',
      counter: 'azure.function.memory.used.bytes',
    },
    {
      title: 'Queued Requests',
      counter: 'azure.function.request.queued.requests',
    },
    {
      title: '5XX Requests',
      counter: 'azure.function.5xx.requests',
    },
  ],
  [Constants.AZURE_LOAD_BALANCER]: [
    {
      title: 'Availability',
      counter: 'azure.loadbalancer.availability',
    },
    {
      title: 'SNAT Connections',
      counter: 'azure.loadbalancer.snat.connections',
    },
    {
      title: 'Packets',
      counter: 'azure.loadbalancer.packets.per.sec',
    },
    {
      title: 'Traffic',
      counter: 'azure.loadbalancer.bytes.per.sec',
    },
  ],
  [Constants.AZURE_VM_SCALE_SET]: [
    {
      title: 'CPU (%)',
      counter: 'azure.vmscaleset.cpu.percent',
    },
    {
      title: 'Network (IN)',
      counter: 'azure.vmscaleset.network.in.bytes.per.sec',
    },
    {
      title: 'Network (OUT)',
      counter: 'azure.vmscaleset.network.out.bytes.per.sec',
    },
    {
      title: 'CPU Consumed Credits',
      counter: 'azure.vmscaleset.cpu.consumed.credits',
    },
  ],
  [Constants.AZURE_CDN]: [
    {
      title: 'Requests',
      counter: 'azure.cdn.requests',
    },
    {
      title: 'Response Rate',
      counter: 'azure.cdn.response.bytes.per.sec',
    },
    {
      title: 'Byte Hit Ratio',
      counter: 'azure.cdn.byte.hit.ratio.percent',
    },
    {
      title: 'Latency',
      counter: 'azure.cdn.latency.ms',
    },
  ],
  [Constants.AZURE_POSTGRESQL_SERVER]: [
    {
      title: 'CPU (%)',
      counter: 'azure.postgresql.server.cpu.percent',
    },
    {
      title: 'Failed Connections',
      counter: 'azure.postgresql.server.failed.connections',
    },
    {
      title: 'Active Connections',
      counter: 'azure.postgresql.server.active.connections',
    },
    {
      title: 'Storage Used Bytes',
      counter: 'azure.postgresql.server.storage.used.bytes',
    },
  ],
  [Constants.AZURE_MYSQL_SERVER]: [
    {
      title: 'CPU (%)',
      counter: 'azure.mysql.server.cpu.percent',
    },
    {
      title: 'IO (%)',
      counter: 'azure.mysql.server.io.percent',
    },
    {
      title: 'Active Connections',
      counter: 'azure.mysql.server.active.connections',
    },
    {
      title: 'Storage Used Bytes',
      counter: 'azure.mysql.server.data.storage.used.bytes',
    },
  ],
  [Constants.ONEDRIVE]: [
    {
      title: 'Active Files',
      counter: 'onedrive.active.files',
    },
    {
      title: 'File Activity',
      counter: 'onedrive.files',
    },
  ],
  [Constants.EXCHANGE_ONLINE]: [
    {
      title: 'Active Mailboxes',
      counter: 'exchange.online.mailboxes',
    },
    {
      title: 'Used Storage',
      counter: 'exchange.online.mailbox.used.size.bytes',
    },
  ],
  [Constants.SHAREPOINT_ONLINE]: [
    {
      title: 'Active Sites',
      counter: 'sharepoint.online.site~active.files',
    },
    {
      title: 'Active Users',
      counter: 'sharepoint.online.active.users',
    },
    {
      title: 'Total Files',
      counter: 'sharepoint.online.site~files',
    },
    {
      title: 'Used Storage',
      counter: 'sharepoint.online.site~storage.used.bytes',
    },
  ],
  [Constants.MICROSOFT_TEAMS]: [
    {
      title: 'Calls',
      counter: 'teams.calls',
    },
    {
      title: 'Messages',
      counter: 'teams.chat.messages',
    },
  ],

  // VIRTUALIZATION Category
  [Constants.HYPER_V]: [
    {
      title: 'CPU (%)',
      counter: 'hyperv.cpu.percent',
    },
    {
      title: 'Committed Memory',
      counter: 'hyperv.memory.committed.bytes',
    },
    {
      title: 'Disk I/O',
      counter: 'hyperv.disk.io.ops.per.sec',
    },
    {
      title: 'Network',
      counter: 'hyperv.network.bytes.per.sec',
    },
  ],
  [`${Constants.HYPER_V}-vm`]: [
    {
      title: 'CPU (%)',
      counter: 'hyperv.cpu.percent',
    },
    {
      title: 'Memory (%)',
      counter: 'hyperv.vm~memory.free.bytes',
    },
    {
      title: 'Network',
      counter: 'hyperv.vm~network.bytes.per.sec',
    },
  ],
  [Constants.HYPER_V_CLUSTER]: [
    {
      title: 'Disk Used',
      counter: 'hyperv.cluster.disk.used.bytes',
    },
    {
      title: 'Memory Used',
      counter: 'hyperv.cluster.memory.used.bytes',
    },
    {
      title: 'Virtual Machines',
      counter: 'hyperv.cluster.virtual.machines',
    },
    {
      title: 'CPU Cores',
      counter: 'hyperv.cluster.cpu.cores',
    },
  ],
  [Constants.VCENTER]: [
    {
      title: 'CPU Used',
      counter: 'vcenter.cpu.used.hz',
    },
    {
      title: 'Memory Used',
      counter: 'vcenter.memory.used.bytes',
    },
    {
      title: 'Datacenter Memory (%)',
      counter: 'vcenter.datacenter~memory.used.percent',
    },
    {
      title: 'Resource Pools',
      counter: 'vcenter.resource.pools',
    },
  ],
  [Constants.VMWARE_ESXI]: [
    {
      title: 'CPU Used',
      counter: 'esxi.cpu.used.hz',
    },
    {
      title: 'Active Memory',
      counter: 'esxi.active.memory.bytes',
    },
    {
      title: 'Storage Used',
      counter: 'esxi.disk.used.bytes',
    },
    {
      title: 'Network',
      counter: 'esxi.network.bytes.per.sec',
    },
  ],
  [`${Constants.VMWARE_ESXI}-vm`]: [
    {
      title: 'CPU (%)',
      counter: 'esxi.vm~cpu.percent',
    },
    {
      title: 'Memory (%)',
      counter: 'esxi.vm~memory.used.percent',
    },
    {
      title: 'Storage (%)',
      counter: 'esxi.vm~disk.used.percent',
    },
  ],
  [Constants.CITRIX_XEN]: [
    {
      title: 'CPU (%)',
      counter: 'citrix.xen.cpu.percent',
    },
    {
      title: 'Memory (%)',
      counter: 'citrix.xen.memory.used.percent',
    },
    {
      title: 'Storage Used (%)',
      counter: 'citrix.xen.disk.used.percent',
    },
    {
      title: 'API Memory Used',
      counter: 'citrix.xen.api.memory.live.bytes',
    },
  ],
  [`${Constants.CITRIX_XEN}-vm`]: [
    {
      title: 'CPU (%)',
      counter: 'citrix.xen.vm.cpu.percent',
    },
    {
      title: 'Memory (%)',
      counter: 'citrix.xen.vm.memory.used.percent',
    },
    {
      title: 'Network',
      counter: 'citrix.xen.vm.network.bytes.per.sec',
    },
  ],

  // network category
  [Constants.SWITCH]: [
    {
      title: 'CPU (%)',
      counter: 'system.cpu.percent',
    },
    {
      title: 'Memory (%)',
      counter: 'system.memory.used.percent',
    },
    {
      title: 'Packet Loss (%)',
      counter: 'ping.packet.lost.percent',
    },
    {
      title: 'Latency',
      counter: 'ping.latency.ms',
    },
  ],
  [Constants.FIREWALL]: [
    {
      title: 'CPU (%)',
      counter: 'system.cpu.percent',
    },
    {
      title: 'Memory (%)',
      counter: 'system.memory.used.percent',
    },

    {
      title: 'Latency',
      counter: 'ping.latency.ms',
    },
  ],
  [Constants.ROUTER]: [
    {
      title: 'CPU (%)',
      counter: 'system.cpu.percent',
    },
    {
      title: 'Memory (%)',
      counter: 'system.memory.used.percent',
    },
    {
      title: 'Packet Loss (%)',
      counter: 'ping.packet.lost.percent',
    },
    {
      title: 'Latency',
      counter: 'ping.latency.ms',
    },
  ],
  [Constants.CISCO_VEDGE]: [
    { title: 'CPU (%)', counter: 'cisco.vedge.cpu.percent' },
    { title: 'Memory (%)', counter: 'cisco.vedge.memory.used.percent' },
  ],
  [Constants.CISCO_VMANAGE]: [
    { title: 'CPU (%)', counter: 'cisco.vmanage.cpu.percent' },
    { title: 'Memory (%)', counter: 'cisco.vmanage.memory.used.percent' },
  ],
  [Constants.CISCO_VSMART]: [
    { title: 'CPU (%)', counter: 'cisco.vsmart.cpu.percent' },
    { title: 'Memory (%)', counter: 'cisco.vsmart.memory.used.percent' },
  ],
  [Constants.CISCO_VBOND]: [
    { title: 'CPU (%)', counter: 'cisco.vbond.cpu.percent' },
    { title: 'Memory (%)', counter: 'cisco.vbond.memory.used.percent' },
  ],
}
