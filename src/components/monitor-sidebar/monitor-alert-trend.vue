<template>
  <FlotoContentLoader :loading="loading">
    <ChartView
      :data="data"
      :widget="widget.getContext()"
      :disable-server-zoom="true"
      availability-colors
      for-template
    />
  </FlotoContentLoader>
</template>

<script>
import Bus from '@utils/emitter'
import WidgetContextBuilder from '@components/widgets/widget-context-builder'
import { WidgetTypeConstants } from '@components/widgets/constants'
import { generateId } from '@utils/id'
import buildWidgetResult from '@components/widgets/result-builder'
import ChartView from '@components/widgets/views/chart-view.vue'

export default {
  name: 'MonitorAlertTrend',
  components: {
    ChartView,
  },
  props: {
    monitorId: {
      type: Number,
      required: true,
    },
    timeline: {
      type: Object,
      default() {
        return {
          selectedKey: '-1h',
        }
      },
    },
    instance: {
      type: String,
      default: undefined,
    },
    instanceType: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      data: {},
      loading: true,
      guid: generateId(),
    }
  },
  created() {
    const builder = new WidgetContextBuilder()
    builder.addGroup('alert')
    builder.appendToGroup('alert', { category: 'metric' })
    builder.setCategory(WidgetTypeConstants.CHART)
    builder.setWidgetType(WidgetTypeConstants.STACKED_VERTICAL_BAR)
    builder.setTimeline(this.timeline)
    builder.addCounterToGroup({
      counter: 'severity',
      aggrigateFn: 'count',
    })
    builder.setWidgetProperties({
      legendEnabled: true,
      chartOptions: {
        yAxis: {
          allowDecimals: false,
        },
      },
    })
    builder.addEntities('Monitor', [+this.monitorId])
    builder.addResultBy(['severity'])
    if (this.instanceType && this.instance) {
      builder.addInstance(this.instanceType, this.instance)
    }
    this.widget = builder

    Bus.$on('socket:connected', this.requestData)
    Bus.$on(this.$constants.UI_WIDGET_RESULT_EVENT, this.handleReceiveData)
    this.$once('hook:beforeDestroy', () => {
      Bus.$off(this.$constants.UI_WIDGET_RESULT_EVENT, this.handleReceiveData)
      Bus.$off('socket:connected', this.requestData)
    })

    this.requestData()
  },
  methods: {
    requestData() {
      this.loading = true
      const widget = this.widget.generateWidgetDefinition()
      widget.id = -1
      widget[this.$constants.UI_EVENT_UUID] = this.guid
      Bus.$emit('server:event', {
        'event.type': this.$constants.UI_WIDGET_RESULT_EVENT,
        'event.context': widget,
      })
    },
    async handleReceiveData(response) {
      if (response[this.$constants.UI_EVENT_UUID] !== this.guid) {
        return
      }
      const result = await buildWidgetResult(this.widget.getContext(), response)

      this.data = Object.freeze(result)
      this.loading = false
    },
  },
}
</script>
