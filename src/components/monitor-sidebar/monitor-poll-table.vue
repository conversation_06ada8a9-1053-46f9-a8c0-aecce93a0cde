<template>
  <MGrid class="min-w-0" :columns="columns" :data="groups" :paging="false">
    <template v-slot:timestamp="{ item }">
      {{ item.timestamp | datetime }}
    </template>
  </MGrid>
</template>

<script>
export default {
  name: 'MonitorPollTable',
  props: {
    groups: {
      type: Array,
      default() {
        return []
      },
    },
  },
  data() {
    this.columns = [
      {
        key: 'metricGroup',
        name: 'Metric Group',
        searchable: true,
        sortable: true,
      },
      {
        key: 'timestamp',
        name: 'Last Poll Time',
        searchable: true,
        sortable: true,
      },
    ]
    return {}
  },
}
</script>
