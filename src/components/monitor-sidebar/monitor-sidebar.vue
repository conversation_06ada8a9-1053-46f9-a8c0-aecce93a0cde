<template>
  <div>
    <GroupProvider>
      <div
        v-if="!useDrawer"
        :key="monitor.id"
        class="flex flex-col flex-1 min-h-0 min-w-0 cursor-auto absolute pl-2 h-full overflow-auto border-left"
        :style="currentStyle"
      >
        <MRow class="min-h-0" :gutter="0">
          <MCol class="flex justify-between items-start min-w-0 mt-2">
            <!-- <h5 class="text-primary-alt m-0 flex min-w-0 items-center"> -->
            <!-- <Severity
                :object-id="Number(monitor.resourceId) || Number(monitor.id)"
                :instance="application || vm || ap"
                class="mr-2"
              /> -->
            <!-- <MonitorType
                :type="application ? application : vm ? 'vm' : monitor.type"
                class="mr-2"
                is-fit-contents
              /> -->
            <!-- <span class="min-w-0 text-ellipsis">
                {{
                  application
                    ? `${application} (${
                        sidebarMonitor.host ||
                        sidebarMonitor.ip ||
                        sidebarMonitor.target ||
                        (monitor.parentMonitor || data.object).name
                      })`
                    : vm
                    ? `${vm} (${
                        sidebarMonitor.host ||
                        sidebarMonitor.ip ||
                        sidebarMonitor.target ||
                        (monitor.parentMonitor || data.object).name
                      })`
                    : ap
                    ? `${ap} (${
                        sidebarMonitor.host ||
                        sidebarMonitor.ip ||
                        sidebarMonitor.target
                      })`
                    : monitor.name
                }}
              </span> -->
            <!-- </h5> -->

            <MRow :gutter="0" class="w-full flex items-center w-11/12">
              <MButton
                shape="circle"
                class="squared-button mr-2"
                variant="neutral-lightest"
              >
                <MonitorType
                  :type="
                    application
                      ? application
                      : vm
                      ? $constants.VIRTUAL_MACHINE
                      : monitor.type
                  "
                  disable-tooltip
                  :center="false"
                  class="p-1"
                />
              </MButton>

              <div class="flex flex-col w-11/12">
                <div class="flex items-center">
                  <Severity
                    :object-id="
                      Number(monitor.resourceId) || Number(monitor.id)
                    "
                    :instance="application || vm || ap"
                    class="mr-2"
                  />

                  <h5
                    class="text-ellipsis mb-0 mr-2 page-text-color"
                    style="
                      max-width: 500px;
                      color: var(--page-text-color) !important;
                    "
                    :title="
                      application
                        ? `${application} (${monitor.name || monitor.host})`
                        : vm
                        ? `${vm} (${monitor.name || monitor.host})`
                        : ap
                        ? `${ap} (${monitor.name || monitor.host})`
                        : monitor.name ||
                          monitor.serviceCheckTarget ||
                          monitor.target ||
                          monitor.host
                    "
                  >
                    {{
                      application
                        ? `${application} (${monitor.name || monitor.host})`
                        : vm
                        ? `${vm} (${monitor.name || monitor.host})`
                        : ap
                        ? `${ap} (${monitor.name || monitor.host})`
                        : monitor.name ||
                          monitor.serviceCheckTarget ||
                          monitor.target ||
                          monitor.host
                    }}
                  </h5>
                  <span class="text-neutral-light font-500 text-xs mt-1">
                    {{
                      monitor.host
                        ? ` | ${
                            monitor.category === $constants.SERVICE_CHECK
                              ? monitor.target
                              : instanceIp ||
                                monitor.ip ||
                                monitor.host ||
                                monitor.target ||
                                monitor.serviceCheckTarget
                          } | `
                        : ''
                    }}
                    {{
                      `${monitor.host ? '' : '|'} ${
                        monitor.vendor || monitor.type
                      } `
                    }}
                  </span>
                </div>
                <div
                  class="inline-flex flex-grow-0 min-w-0 flex-shrink-0 w-full"
                >
                  <div class="group-picker-container">
                    <GroupPicker
                      :value="monitor.groups"
                      disabled
                      :wrap="false"
                      style="flex: none"
                    />
                  </div>
                  <div class="loose-tags-container">
                    <LooseTags :value="monitor.tags" disabled />
                  </div>
                </div>
              </div>
            </MRow>
            <MButton
              :shadow="false"
              variant="transparent"
              @click="$emit('close')"
            >
              <MIcon name="times" size="lg" />
            </MButton>
          </MCol>
          <MDivider class="my-1" />
        </MRow>
        <div class="min-h-0 flex-1">
          <FlotoContentLoader :loading="loading" class="h-full">
            <!-- <FlotoScrollView> -->
            <div v-if="data" class="flex flex-col h-full">
              <MonitorStringCounters
                v-if="
                  (monitor.parentMonitor || {}).id
                    ? monitor.parentMonitor
                    : data.object
                "
                :key="data.id"
                :monitor="
                  (monitor.parentMonitor || {}).id
                    ? monitor.parentMonitor
                    : data.object
                "
                show-last-poll
                :application-name="application"
                hide-groups
                show-template-link
                :vm="vm"
                :hide-metrics="hideMetrics"
                :ap="ap"
                for-monitor-sidebar
                @show-action-result="showActionResultForItem = $event"
              />
            </div>
            <!-- </FlotoScrollView> -->
          </FlotoContentLoader>
        </div>
      </div>

      <FlotoDrawer
        v-else
        :open="Boolean(monitor) && isOpen && useDrawer"
        :scrolled-content="false"
        @hide="handleDrawerHide"
      >
        <template v-if="monitor" v-slot:title>
          <MRow class="min-h-0" :gutter="0">
            <MCol class="flex justify-between items-start min-w-0 mt-2">
              <MRow :gutter="0" class="w-full flex items-center w-11/12">
                <MButton
                  shape="circle"
                  class="squared-button mr-2"
                  variant="neutral-lightest"
                >
                  <MonitorType
                    :type="monitor['type']"
                    disable-tooltip
                    :center="false"
                    class="p-1"
                  />
                </MButton>

                <div class="flex flex-col w-11/12">
                  <div class="flex items-center">
                    <Severity
                      :object-id="
                        Number(monitor.resourceId) || Number(monitor.id)
                      "
                      :instance="application || vm || ap"
                      class="mr-2"
                    />
                    <h5
                      class="text-ellipsis mb-0 mr-2 page-text-color"
                      style="
                        max-width: 500px;
                        color: var(--page-text-color) !important;
                      "
                      :title="
                        application
                          ? `${application} (${monitor.name || monitor.host})`
                          : vm
                          ? `${vm} (${monitor.name || monitor.host})`
                          : ap
                          ? `${ap} (${monitor.name || monitor.host})`
                          : monitor.name ||
                            monitor.serviceCheckTarget ||
                            monitor.target ||
                            monitor.host
                      "
                    >
                      {{
                        application
                          ? `${application} (${monitor.name || monitor.host})`
                          : vm
                          ? `${vm} (${monitor.name || monitor.host})`
                          : ap
                          ? `${ap} (${monitor.name || monitor.host})`
                          : monitor.name ||
                            monitor.serviceCheckTarget ||
                            monitor.target ||
                            monitor.host
                      }}
                    </h5>
                    <span class="text-neutral-light font-500 text-xs mt-1">
                      {{
                        monitor.host
                          ? ` | ${
                              monitor.category === $constants.SERVICE_CHECK
                                ? monitor.target
                                : instanceIp ||
                                  monitor.ip ||
                                  monitor.host ||
                                  monitor.target ||
                                  monitor.serviceCheckTarget
                            } | `
                          : ''
                      }}
                      {{
                        `${monitor.host ? '' : '|'} ${
                          monitor.vendor || monitor.type
                        } `
                      }}
                    </span>
                  </div>
                  <div class="inline-flex flex-grow-0 min-w-0 flex-shrink-0">
                    <GroupPicker
                      :value="monitor.groups"
                      disabled
                      :wrap="false"
                      style="flex: none"
                    />
                    <LooseTags :value="monitor.tags" disabled />
                  </div>
                </div>
              </MRow>
            </MCol>
          </MRow>
        </template>
        <div v-if="data" class="flex flex-col w-full h-full">
          <MonitorStringCounters
            v-if="
              (monitor.parentMonitor || {}).id
                ? monitor.parentMonitor
                : data.object
            "
            :key="data.id"
            :monitor="
              (monitor.parentMonitor || {}).id
                ? monitor.parentMonitor
                : data.object
            "
            show-last-poll
            :application-name="application"
            hide-groups
            show-template-link
            :vm="vm"
            :hide-metrics="hideMetrics"
            :ap="ap"
            @show-action-result="showActionResultForItem = $event"
          />
        </div>
      </FlotoDrawer>
    </GroupProvider>
    <ActionResult
      v-if="showActionResultForItem !== null"
      :item="showActionResultForItem"
      :open="showActionResultForItem !== null"
      @close="showActionResultForItem = null"
    />
  </div>
</template>

<script>
import { UserPreferenceComputed } from '@state/modules/user-preference/helpers'
import { objectDBWorker } from '@/src/workers'
import GroupProvider from '@components/data-provider/group-provider.vue'
import MonitorType from '@components/monitor-type.vue'
import LooseTags from '@components/loose-tags.vue'

import Severity from '@components/severity.vue'
import { getVmType } from '@utils/vm-map'
// import MonitorAlertTrend from './monitor-alert-trend.vue'

import MonitorStringCounters from './monitor-string-counters.vue'

import ActionResult from '@modules/settings/plugin-library/components/action-result.vue'

export default {
  name: 'MonitorSidebar',
  components: {
    GroupProvider,
    MonitorType,
    MonitorStringCounters,
    // MonitorAlertTrend,
    Severity,
    ActionResult,
    LooseTags,
  },
  props: {
    monitor: {
      type: Object,
      required: true,
    },
    topOffset: {
      type: Number,
      default: 0,
    },
    rightOffset: {
      type: Number,
      default: 0,
    },
    hideMetrics: {
      type: Boolean,
      default: false,
    },
    useDrawer: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    this.defaultOpenPanels = ['alert_trend', 'metrics']
    return {
      data: undefined,
      loading: true,
      application: undefined,
      vm: undefined,
      hasPanels: false,
      ap: undefined,
      isOpen: false,
      showActionResultForItem: null,
    }
  },
  computed: {
    ...UserPreferenceComputed,
    stringCounterMonitor() {
      return {
        id: this.monitor.monitorId,
      }
    },
    metricChartFilter() {
      if (this.vm && this.data.type) {
        return {
          instanceType: getVmType(this.data.type) || 'vm',
          instance: this.vm,
        }
      }
      return undefined
    },
    currentStyle() {
      return {
        top: `0px`,
        right: `0px`,
        zIndex: 100,
        width: '750px',
        willChange: 'transform',
        transition: 'transform 0.1s linear',
        borderWidth: '2px',
        background: 'var(--drawer-background-color)',
        boxShadow: `-6px 0px 10px -5px ${
          this.theme === 'black' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.2)'
        }`,
        ...(this.topOffset || this.rightOffset
          ? {
              transform: `translate(${this.rightOffset}px, ${this.topOffset}px)`,
            }
          : {}),
      }
    },
    sidebarMonitor() {
      return this.monitor || {}
    },
    instanceIp() {
      const ip = this.monitor?.instanceIpMap?.find(
        (i) => i.instance === (this.vm || this.ap)
      )?.['instance.ip']

      if (ip && ip !== '') {
        return ip
      }
      return undefined
    },
  },
  watch: {
    'monitor.id'(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.hasPanels = false
        this.$nextTick(this.getNodeSidebarData)
      }
    },
    monitor: {
      immediate: true,
      handler(newValue) {
        if (newValue) {
          this.isOpen = true
        }
      },
    },
  },
  created() {
    this.getNodeSidebarData()
  },
  beforeDestroy() {
    this.handleDrawerHide()
  },
  methods: {
    async getNodeSidebarData() {
      const id = +(this.monitor.resourceId || this.monitor.id)
      const data = await objectDBWorker.getObjectById(id)
      if (this.monitor.resourceType === 'vm') {
        this.vm = this.monitor.name
      } else if (this.monitor.resourceType === 'application') {
        this.application = this.monitor.name
      } else if (this.monitor.resourceType === 'ap') {
        this.ap = this.monitor.name
      } else {
        this.vm = undefined
        this.application = undefined
        this.ap = undefined
      }
      this.data = Object.freeze({ ...data, ...this.monitor, object: data })
      this.loading = false
      setTimeout(() => {
        this.hasPanels = true
      }, 700)
    },
    handleDrawerHide() {
      this.isOpen = false
      this.$emit('close')
    },
  },
}
</script>

<style lang="less" scoped>
.group-picker-container {
  min-width: 0; /* Prevents overflow issues */
  max-width: 50%; /* Limits the width to 50% when content overflows */
}

.loose-tags-container {
  min-width: 0; /* Prevents overflow issues */
  max-width: 50%; /* Limits the width to 50% when content overflows */
}
</style>
