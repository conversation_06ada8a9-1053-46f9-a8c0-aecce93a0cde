<template>
  <FlotoContentLoader
    :loading="loading"
    class="h-full w-full flex flex-col min-h-0"
  >
    <MonitorAlertCountProvider :selected-target="widgetParams">
      <div class="w-full h-full flex flex-col min-h-0">
        <MRow :gutter="0" :class="{ 'mx-3': !forMonitorSidebar }">
          <MTab v-model="activeTab" class="w-full">
            <MTabPane v-for="tab in tabs" :key="tab.key" :tab="tab.title" />
          </MTab>
        </MRow>

        <FlotoScrollView :class="{ 'px-2': !forMonitorSidebar }">
          <template v-if="activeTab === 'summary'">
            <MRow
              v-for="(section, index) in availableSummarySections"
              :key="section.key"
              class="w-full py-2 mt-1"
              :gutter="0"
              :class="{
                'border-bot':
                  (metrics.length > 0 && !hideMetrics) ||
                  index !== availableSummarySections.length - 1,
              }"
            >
              <MCol :size="12">
                <h5 class="text-primary mb-0 pl-1">
                  {{ section.title }}
                </h5>
              </MCol>

              <MCol :size="12">
                <component
                  :is="sectionComponent(section)"
                  :monitor="fullMonitor"
                  :section="section"
                  :vm="vm"
                  :ap="ap"
                  :application="computedApplication"
                  v-bind="$attrs"
                />
              </MCol>
            </MRow>
            <MRow
              v-if="metrics.length > 0 && !hideMetrics"
              class="w-full py-2 mt-1"
              :gutter="0"
            >
              <MCol :size="12" class="flex justify-between items-center mb-2">
                <h5 class="text-primary mb-0 pl-1"> Metrics </h5>

                <MRadioGroup
                  v-model="orientation"
                  :options="layoutViewOptions"
                  as-button
                >
                  <template v-slot:option="{ option }">
                    <MIcon
                      :name="
                        option.value === 'full' ? 'grip-line' : 'line-columns'
                      "
                      style="margin-right: 0 !important"
                    />
                  </template>
                </MRadioGroup>
              </MCol>

              <MCol :key="orientation" :size="12" class="w-full">
                <MRow :gutter="0" class="w-full">
                  <MCol
                    v-for="(metric, index) in metrics"
                    :key="metric.counter"
                    :size="orientation === 'full' ? 12 : 6"
                    style="height: 180px"
                    class="mb-2"
                  >
                    <div
                      class="flex h-full pr-2 mb-2 pb-0 rounded bg-neutral-lightest"
                      :class="
                        orientation !== 'full'
                          ? (index + 1) % 2 !== 0
                            ? 'mr-1'
                            : 'ml-1'
                          : ''
                      "
                    >
                      <MonitorMetricChart
                        :key="`${fullMonitor.resourceId || monitor.id}-${vm}`"
                        :counter="metric.counter"
                        :color="colors[index % colors.length]"
                        :enable-legend="true"
                        :monitor-id="
                          fullMonitor.resourceId ||
                          fullMonitor.monitorId ||
                          fullMonitor.id
                        "
                        :title="metric.title"
                        v-bind="metricChartFilter"
                      />
                    </div>
                  </MCol>
                </MRow>
              </MCol>
            </MRow>
          </template>
          <template v-else>
            <component
              :is="sectionComponent({ key: activeTab })"
              :monitor="fullMonitor"
              :section="{ key: activeTab }"
              :vm="vm"
              :ap="ap"
              :application="computedApplication"
              v-bind="$attrs"
              @show-action-result="$emit('show-action-result', $event)"
            />
          </template>
        </FlotoScrollView>
      </div>
    </MonitorAlertCountProvider>
  </FlotoContentLoader>
</template>

<script>
import { authComputed } from '@state/modules/auth'
import { getMonitorApi } from '@modules/settings/monitoring/monitors-api'
import { getVmType } from '@/src/utils/vm-map'
import { colors } from '@utils/chart-colors'
import { MONITOR_SIDEBAR_METRICS } from './monitor-sidebar-metrics'
import { MONITOR_COUNTER_SECTIONS } from './monitor-counter-sections'
import RawCounterSection from './sections/raw-counter-section.vue'
import MonitorInfoSection from './sections/monitor-info-section.vue'
import PollingInfoSection from './sections/polling-info-section.vue'
import TagInfoSection from './sections/tag-info-section.vue'
import MonitorMetricChart from './monitor-metric-chart.vue'
import ActivePolicySection from './sections/active-policy-section.vue'
import MonitorAlertCountProvider from '@components/data-provider/monitor-alert-count-provider.vue'
import ActionHistorySection from './sections/action-history-section.vue'

export default {
  name: 'MonitorStringCounters',
  components: {
    MonitorMetricChart,
    ActivePolicySection,
    MonitorAlertCountProvider,
    ActionHistorySection,
  },
  props: {
    monitor: {
      type: Object,
      required: true,
    },
    vm: {
      type: String,
      default: undefined,
    },
    showLastPoll: {
      type: Boolean,
      default: false,
    },
    hideGroups: {
      type: Boolean,
      default: false,
    },
    defaultActiveKey: {
      type: [Array, String],
      default() {
        return ['metrics']
      },
    },
    hideMetrics: { type: Boolean, default: false },
    ap: {
      type: [String, Number],
      default: undefined,
    },
    applicationName: { type: [String, Number], default: undefined },
    forMonitorSidebar: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    this.showOldDesign = false
    this.layoutViewOptions = [
      { value: 'half', label: 'Half Width' },
      { value: 'full', label: 'Full Width' },
    ]
    return {
      fullMonitor: {},
      loading: true,
      orientation: 'half',
      activeTab: 'summary',
    }
  },
  computed: {
    ...authComputed,

    tabs() {
      let tabs = [
        {
          key: 'summary',
          title: 'Summary',
        },
      ]
        .concat(this.availableSections)
        .concat([{ key: 'action-history', title: 'Action History' }])

      if (
        !this.hasPermission(
          this.$constants.PLUGIN_LIBRARY_SETTINGS_READ_PERMISSION
        )
      ) {
        tabs = tabs.filter((s) => !['action-history'].includes(s.key))
      }

      tabs = tabs.filter(
        (s) => !['monitor-info', 'system-info', 'tag-info'].includes(s.key)
      )
      return tabs
    },
    availableSummarySections() {
      let sections = this.availableSections

      sections = sections.filter(
        (s) => !['poll-info', 'active-policies'].includes(s.key)
      )

      return sections
    },

    initiallyOpenPanels() {
      if (this.defaultActiveKey) {
        return [
          this.availableSections[0].key,
          ...(Array.isArray(this.defaultActiveKey)
            ? this.defaultActiveKey
            : [this.defaultActiveKey]),
        ]
      }
      return this.availableSections[0].key
    },
    colors() {
      return colors
    },
    metricChartFilter() {
      if (this.vm && this.fullMonitor.type) {
        return {
          instanceType: getVmType(this.fullMonitor.type) || 'vm',
          instance: this.vm,
        }
      }
      return undefined
    },
    metrics() {
      if (this.fullMonitor.type === 'AWS ELB') {
        if (this.monitor.makeModel === 'Application') {
          return (
            MONITOR_SIDEBAR_METRICS[`${this.fullMonitor.type}-application`] ||
            []
          )
        } else if (this.monitor.makeModel === 'Network') {
          return (
            MONITOR_SIDEBAR_METRICS[`${this.fullMonitor.type}-network`] || []
          )
        }
      }

      if (this.vm || this.application) {
        if (this.vm && this.fullMonitor.type) {
          return MONITOR_SIDEBAR_METRICS[`${this.fullMonitor.type}-vm`] || []
        }
        return []
      }
      if (this.fullMonitor.type) {
        return MONITOR_SIDEBAR_METRICS[this.fullMonitor.type] || []
      }

      return []
    },
    availableSections() {
      let vmType
      if (this.vm) {
        vmType = getVmType(this.fullMonitor.type)
      }
      let sections =
        (vmType && MONITOR_COUNTER_SECTIONS[vmType]) ||
        MONITOR_COUNTER_SECTIONS[this.fullMonitor.type] ||
        MONITOR_COUNTER_SECTIONS[this.fullMonitor.category] ||
        []
      if (!this.hasPermission(this.$constants.ALERT_READ_PERMISSION)) {
        sections = sections.filter((s) => s.key !== 'active-policies')
      }

      return sections
    },
    widgetParams() {
      return {
        resourceType: 'Monitor',
        id: this.monitor.id,
        instance: this.vm || this.ap,
        application: this.applicationName,
      }
    },
    computedApplication() {
      return this.applicationName || this.$route.application
    },
  },
  created() {
    this.getObject()
  },
  methods: {
    getObject() {
      getMonitorApi(this.monitor.id).then((data) => {
        this.fullMonitor = Object.freeze(data)
        this.loading = false
      })
    },
    sectionComponent(section) {
      if (section.key === 'monitor-info') {
        return MonitorInfoSection
      } else if (section.key === 'poll-info') {
        return PollingInfoSection
      } else if (section.key === 'tag-info') {
        return TagInfoSection
      } else if (section.key === 'active-policies') {
        return ActivePolicySection
      } else if (section.key === 'action-history') {
        return ActionHistorySection
      }
      return RawCounterSection
    },
  },
}
</script>
<style lang="less" scoped>
.chevron-icon {
  left: 4px !important;
  margin-top: 1px !important;
  vertical-align: middle !important;
}
</style>
