<template>
  <div
    class="flex py-2 message-container"
    :class="{ 'justify-end': isMyMessage }"
  >
    <div class="message-box" :class="{ 'my-message': isMyMessage }">
      <MTooltip>
        <template v-slot:trigger>
          <div class="avatar">
            <FlotoUserAvatar
              size="large"
              class="rounded-full"
              :avatar="currentSender.avatarUrl"
            />
          </div>
        </template>
        {{ currentSender.name || currentSender.userName }}
      </MTooltip>
      <div class="message-content">
        <Editor :value="content" disabled />
        <small class="date-time text-neutral" v-text="dateTime" />
      </div>
    </div>
  </div>
</template>

<script>
import Find from 'lodash/find'
import Editor from './editor/editor.vue'

export default {
  name: 'Message',
  components: {
    Editor,
  },
  props: {
    userOptions: {
      type: Array,
      default() {
        return []
      },
    },
    content: {
      type: String,
      default: '',
    },
    senderId: {
      type: [String, Number],
      required: true,
    },
    sender: {
      type: [String, Number],
      default: undefined,
    },
    dateTime: {
      type: String,
      default: '',
    },
    isMyMessage: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    currentSender() {
      if (this.sender) {
        return this.sender
      }
      return (
        Find(this.userOptions, { id: this.senderId }) || {
          avatarUrl: '/default-user.png',
        }
      )
    },
  },
}
</script>

<style lang="less" scoped>
.message-container {
  width: 100%;

  .message-box {
    display: flex;
    width: 75%;

    .avatar {
      @apply mx-4;
    }

    .message-content {
      @apply px-4 py-2;

      position: relative;
      word-break: break-word;
      background: var(--page-background-color);
      border: 1px solid var(--border-color);

      &::before,
      &::after {
        position: absolute;
        top: 20px;
        right: 100%;
        width: 0;
        height: 0;
        pointer-events: none;
        content: '';
        border: solid transparent;
      }

      &::after {
        margin-top: -8px;
        border-color: rgba(245, 188, 24, 0);
        border-width: 8px;
        border-right-color: var(--page-background-color);
      }

      &::before {
        margin-top: -9px;
        border-color: rgba(194, 225, 245, 0);
        border-width: 9px;
        border-right-color: var(--border-color);
      }

      img {
        width: 100%;
      }
    }

    &.my-message {
      flex-direction: row-reverse;

      .message-content {
        background: fade(#099dd9, 20);

        &::before,
        &::after {
          left: 100%;
        }

        &::after {
          border-right-color: transparent;
          border-left-color: fade(#099dd9, 20);
        }

        &::before {
          border-right-color: transparent;
          border-left-color: var(--border-color);
        }
      }
    }
  }
}
</style>
