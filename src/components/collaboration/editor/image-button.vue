<template>
  <MPopover
    ref="popoverRef"
    placement="bottomRight"
    :overlay-style="{ width: '300px' }"
    overlay-class-name="picker-overlay"
    @show="handlePopoverShow"
  >
    <template v-slot:trigger="{ toggle }">
      <EditorButton title="Image" class="mr-1" :active="active" @click="toggle">
        <slot />
      </EditorButton>
    </template>
    <FlotoForm class="p-2" @submit="handleFormSubmit">
      <FlotoFormItem rules="required" label="Image">
        <FileDropper
          v-model="image"
          mode="file"
          :max-files="1"
          button-text="Select Image"
          :allowed-extensions="['png', 'jpg', 'jpeg']"
        />
      </FlotoFormItem>
      <template v-slot:submit>
        <div class="text-right">
          <MButton type="submit" size="small" class="mr-2"> Add </MButton>
          <MButton size="small" variant="default" @click="hideMenu">
            Cancel
          </MButton>
        </div>
      </template>
    </FlotoForm>
  </MPopover>
</template>

<script>
import { buildAvatarUrl } from '@data/avatar'
import FileDropper from '@components/file-dropper.vue'
import EditorButton from './editor-button'

export default {
  name: 'LinkButton',
  components: { EditorButton, FileDropper },
  props: {
    active: { type: Boolean, default: false },
  },
  data() {
    return {
      image: [],
    }
  },
  computed: {
    buttonStyle() {
      if (this.disabled) {
        return { pointerEvents: 'none', opacity: 0.5 }
      }
      return {}
    },
  },
  methods: {
    handlePopoverShow() {
      this.image = []
    },
    handleFormSubmit() {
      this.$emit('add', buildAvatarUrl(this.image[0].result))
      this.hideMenu()
    },
    hideMenu() {
      this.$emit('hide')
      this.$refs.popoverRef.hide()
    },
  },
}
</script>
