<template>
  <EditorButton title="Table">
    <template v-slot:trigger="{ hide, show }">
      <MDropdown
        :options="options"
        @hide="$emit('hide')"
        @change="handleOptionSelected"
      >
        <template v-slot:trigger="{ toggle }">
          <MButton
            variant="transparent"
            class="text-neutral-darker"
            size="small"
            :shadow="false"
            @click="toggle"
            @mouseenter="show"
            @mouseleave="hide"
          >
            <slot />
          </MButton>
        </template>
      </MDropdown>
    </template>
  </EditorButton>
</template>

<script>
import EditorButton from './editor-button'
export default {
  name: 'TableButton',
  components: { EditorButton },
  props: {
    active: { type: Boolean, default: false },
    commands: { type: Object, required: true },
  },
  computed: {
    options() {
      let o = [{ text: `Add Table`, key: 'table' }]
      const additionalOptions = [
        {
          text: `Merge Columns`,
          key: 'toggle_merge_cells',
        },
        {
          text: `Add Column before`,
          key: 'add_column_before',
        },
        {
          text: `Add Column after`,
          key: 'add_column_after',
        },
        {
          text: `Remove Column`,
          key: 'delete_column',
        },
        {
          text: `Add Row before`,
          key: 'add_row_before',
        },
        {
          text: `Add Row after`,
          key: 'add_row_after',
        },
        {
          text: `Remove Row`,
          key: 'delete_row',
        },
        { text: `Remove Table`, key: 'delete' },
      ]
      if (this.active) {
        o = o.concat(additionalOptions)
      }
      return o
    },
  },
  methods: {
    handleOptionSelected($event) {
      switch ($event.key) {
        case 'table':
          this.commands.createTable({
            rowsCount: 3,
            colsCount: 3,
            withHeaderRow: false,
          })
          break
        case 'toggle_merge_cells':
          this.commands.toggleCellMerge()
          break
        case 'add_row_before':
          this.commands.addRowBefore()
          break
        case 'add_row_after':
          this.commands.addRowAfter()
          break
        case 'delete_row':
          this.commands.deleteRow()
          break
        case 'add_column_before':
          this.commands.addColumnBefore()
          break
        case 'add_column_after':
          this.commands.addColumnAfter()
          break
        case 'delete_column':
          this.commands.deleteColumn()
          break
        case 'delete':
          this.commands.deleteTable()
          break
        default:
          break
      }
    },
  },
}
</script>
