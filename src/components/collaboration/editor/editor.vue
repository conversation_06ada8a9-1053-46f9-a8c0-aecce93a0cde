<template>
  <div
    class="flex flex-col editor-container"
    :class="{ disabled }"
    :style="controlPreview ? { pointerEvents: 'none' } : {}"
  >
    <EditorMenuBar
      v-if="showToolbar"
      v-slot="{ commands, isActive, getMarkAttrs }"
      :editor="editor"
      class="editor-buttons"
    >
      <div style="height: 40px">
        <Transition name="placeholder">
          <div class="editor-menu-bar shadow" :class="{ affixed: menuAffixed }">
            <EditorButton
              v-if="disallowedExtensions.indexOf('bold') === -1"
              class="mr-1"
              title="Bold"
              :active="isActive.bold()"
              @click="commands.bold"
            >
              <MIcon name="bold" />
            </EditorButton>
            <EditorButton
              v-if="disallowedExtensions.indexOf('italic') === -1"
              class="mr-1"
              title="Italic"
              :active="isActive.italic()"
              @click="commands.italic"
            >
              <MIcon name="italic" />
            </EditorButton>
            <EditorButton
              v-if="disallowedExtensions.indexOf('underline') === -1"
              class="mr-1"
              title="Underline"
              :active="isActive.underline()"
              @click="commands.underline"
            >
              <MIcon name="underline" />
            </EditorButton>
            <EditorButton
              v-if="disallowedExtensions.indexOf('paragraph') === -1"
              class="mr-1"
              title="Paragraph"
              :active="isActive.paragraph()"
              @click="commands.paragraph"
            >
              <MIcon name="paragraph" />
            </EditorButton>
            <EditorButton
              v-for="level in [1, 2, 3]"
              :key="level"
              :title="`Heading ${level}`"
              class="mr-1"
              :active="isActive.heading({ level })"
              @click="commands.heading({ level })"
            >
              <MIcon :name="`h${level}`" />
            </EditorButton>
            <EditorButton
              v-if="disallowedExtensions.indexOf('bullet-list') === -1"
              class="mr-1"
              title="Bullet List"
              :active="isActive.bullet_list()"
              @click="commands.bullet_list"
            >
              <MIcon name="list" />
            </EditorButton>
            <EditorButton
              v-if="disallowedExtensions.indexOf('ordered-list') === -1"
              class="mr-1"
              title="Ordered List"
              :active="isActive.ordered_list()"
              @click="commands.ordered_list"
            >
              <MIcon name="list-ol" />
            </EditorButton>
            <EditorButton
              v-if="disallowedExtensions.indexOf('horizontal-rule') === -1"
              class="mr-1"
              title="HR"
              @click="commands.horizontal_rule"
            >
              <MIcon name="horizontal-rule" />
            </EditorButton>
            <TableButton
              v-if="disallowedExtensions.indexOf('table') === -1"
              :commands="{
                createTable: commands.createTable,
                deleteTable: commands.deleteTable,
                addRowBefore: commands.addRowBefore,
                addRowAfter: commands.addRowAfter,
                deleteRow: commands.deleteRow,
                addColumnBefore: commands.addColumnBefore,
                addColumnAfter: commands.addColumnAfter,
                deleteColumn: commands.deleteColumn,
                toggleCellMerge: commands.toggleCellMerge,
              }"
              :active="isActive.table()"
            >
              <MIcon name="table" />
            </TableButton>
            <LinkButton
              v-if="disallowedExtensions.indexOf('link') === -1"
              :active="isActive.link()"
              :link="getMarkAttrs('link').href"
              class="mr-1"
              :disabled="editor.state.selection.empty"
              @hide="() => editor.view.focus()"
              @add="commands.link({ href: $event, target: '_blank' })"
              @remove="commands.link({ href: null })"
            >
              <MIcon name="link" />
            </LinkButton>
            <EmbedButton
              v-if="disallowedExtensions.indexOf('video') === -1"
              class="mr-1"
              @hide="() => editor.view.focus()"
              @add="commands.video({ src: $event })"
            >
              <MIcon name="film" />
            </EmbedButton>
            <ImageButton
              v-if="disallowedExtensions.indexOf('image') === -1"
              class="mr-1"
              @hide="() => editor.view.focus()"
              @add="commands.image({ src: $event })"
            >
              <MIcon name="image" />
            </ImageButton>
            <EditorButton
              v-if="disallowedExtensions.indexOf('undo') === -1"
              class="mr-1"
              title="Undo"
              @click="commands.undo"
            >
              <MIcon name="undo" />
            </EditorButton>
            <EditorButton
              v-if="disallowedExtensions.indexOf('redo') === -1"
              class="mr-1"
              title="Redo"
              @click="commands.redo"
            >
              <MIcon name="redo" />
            </EditorButton>
          </div>
        </Transition>
      </div>
    </EditorMenuBar>
    <div
      v-if="!disabled"
      class="editor cursor-text"
      :style="{
        height: height ? `${height}px` : undefined,
        minHeight: minHeight ? `${minHeight}px` : undefined,
        maxHeight: maxHeight ? `${maxHeight}px` : undefined,
      }"
      v-bind="$attrs"
      @click="handleEditorClick"
    >
      <FlotoScrollView
        :show-duration="0"
        class="flex flex-col w-full"
        :style="{
          height: height ? `${height}px` : undefined,
          minHeight: minHeight ? `${minHeight}px` : undefined,
          maxHeight: maxHeight ? `${maxHeight}px` : undefined,
        }"
      >
        <div class="px-2 mt-1 flex flex-1">
          <EditorContent
            class="editor-content flex w-full flex-col"
            :editor="editor"
          />
        </div>
      </FlotoScrollView>
    </div>
    <div v-else class="editor cursor-text" v-bind="$attrs">
      <EditorContent
        class="editor-content flex w-full flex-col"
        :editor="editor"
      />
    </div>
  </div>
</template>
<script>
/* eslint-disable */
import { Editor, EditorContent, EditorMenuBar, Paragraph } from 'tiptap'
import {
  Bold,
  Italic,
  HardBreak,
  Heading,
  Underline,
  OrderedList,
  BulletList,
  History,
  ListItem,
  HorizontalRule,
  Mention,
  Image,
  Table,
  TableHeader,
  TableCell,
  TableRow,
  Link,
} from 'tiptap-extensions'
import './icons'
import Embed from './node/embed/embed'
import Div from './node/div'
import EditorButton from './editor-button'
import LinkButton from './link-button'
import EmbedButton from './embed-button'
import ImageButton from './image-button'
import TableButton from './table-button'

export default {
  name: 'Editor',
  components: {
    EditorContent,
    EditorMenuBar,
    EditorButton,
    LinkButton,
    ImageButton,
    EmbedButton,
    TableButton,
  },
  model: {
    event: 'update',
  },
  props: {
    controlPreview: { type: Boolean, default: false },
    disabled: { type: Boolean, default: false },
    rows: { type: [String, Number], default: undefined },
    minRows: { type: [String, Number], default: 8 },
    value: { type: String, default: undefined },
    placeholder: { type: String, default: '' },
    maxHeight: { type: Number, default: undefined },
    autoFocus: { type: Boolean, default: false },
    // eslint-disable-next-line
    toolbar: { type: Boolean, default: true },
    disallowedExtensions: {
      type: Array,
      default() {
        return []
      },
    },
  },
  data() {
    return {
      menuAffixed: false,
      editor: new Editor({
        onUpdate: ({ getHTML }) => {
          const newContent = getHTML()
          this.$emit(
            'update',
            /^<p><\/p>$/.test(newContent) ? undefined : newContent
          )
        },
        onBlur: () => {
          this.$emit('blur')
        },
        onFocus: () => {
          this.$emit('focus')
        },
        content: this.value || ``,
        extensions: [
          // new Div(),
          new Bold(),
          new Italic(),
          new Underline(),
          new Paragraph(),
          new Link(),
          new HardBreak(),
          new OrderedList(),
          new BulletList(),
          new ListItem(),
          new History(),
          new HorizontalRule(),
          new Embed(),
          new Image(),
          new Heading({
            levels: [1, 2, 3],
          }),
          new Table({
            resizable: true,
          }),
          new TableHeader(),
          new TableCell(),
          new TableRow(),
          ...this.getConditionalExtensions(),
        ],
      }),
    }
  },
  computed: {
    showToolbar() {
      return this.disabled === false && this.toolbar
    },
    height() {
      if (this.rows) {
        return parseInt(this.rows, 10) * 20
      }
      return undefined
    },
    minHeight() {
      if (!this.rows && !this.minRows) {
        return 5 * 20
      }
      if (this.minRows) {
        return parseInt(this.minRows, 10) * 20
      }
      return undefined
    },
  },
  watch: {
    disabled: {
      immediate: true,
      handler(newValue) {
        this.editor.setOptions({
          editable: newValue === false,
        })
      },
    },
    value(newValue, oldValue) {
      if (this.disabled) {
        this.editor.setContent(newValue)
      }
    },
  },
  mounted() {
    if (this.autoFocus) {
      this.editor.focus('end')
    }
  },
  beforeDestroy() {
    // this.editor.destroy()
  },
  methods: {
    focus(type = 'end') {
      this.editor.focus(type)
    },
    handleEditorClick() {
      if (!this.editor.view.focused) {
        this.editor.focus('end')
      }
    },
    addContentAtCurrentPosition(content) {
      const transaction = this.editor.state.tr.insertText(` ${content} `)
      this.editor.view.dispatch(transaction)
    },
    getConditionalExtensions() {
      const extensions = []
      return extensions
    },
  },
}
</script>

<style lang="less" scoped>
.editor-container {
  &:not(.border-none) {
    @apply border-solid rounded border;

    border-color: var(--border-color);
  }

  &.disabled {
    cursor: auto;

    @apply border-none;
  }

  .editor {
    // stylelint-disable-next-line
    .__panel {
      margin-right: -18px !important;
    }

    div:focus {
      outline: 0 !important;
    }
  }

  .editor-menu-bar {
    @apply p-2 flex flex-wrap;

    &.affixed {
      border: 1px solid var(--border-color);

      @apply shadow-lg;
    }
  }
  .is-active,
  .@{ant-prefix}-btn:hover {
    background: var(--neutral-lighter);

    @apply text-neutral-dark;
  }
}
</style>
