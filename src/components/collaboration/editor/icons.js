import { addIcons } from '@assets/icons'

import { faBold } from '@fortawesome/pro-light-svg-icons/faBold'
import { faItalic } from '@fortawesome/pro-light-svg-icons/faItalic'
import { faUnderline } from '@fortawesome/pro-light-svg-icons/faUnderline'
import { faParagraph } from '@fortawesome/pro-light-svg-icons/faParagraph'
import { faH1 } from '@fortawesome/pro-light-svg-icons/faH1'
import { faH2 } from '@fortawesome/pro-light-svg-icons/faH2'
import { faH3 } from '@fortawesome/pro-light-svg-icons/faH3'
import { faList } from '@fortawesome/pro-light-svg-icons/faList'
import { faListOl } from '@fortawesome/pro-light-svg-icons/faListOl'
import { faHorizontalRule } from '@fortawesome/pro-light-svg-icons/faHorizontalRule'
import { faLink } from '@fortawesome/pro-light-svg-icons/faLink'
import { faTable } from '@fortawesome/pro-light-svg-icons/faTable'
import { faImage } from '@fortawesome/pro-light-svg-icons/faImage'
import { faUndo } from '@fortawesome/pro-light-svg-icons/faUndo'
import { faRedo } from '@fortawesome/pro-light-svg-icons/faRedo'
import { faFilm } from '@fortawesome/pro-light-svg-icons/faFilm'

const Icons = [
  faBold,
  faItalic,
  faUnderline,
  faParagraph,
  faH1,
  faH2,
  faH3,
  faList,
  faListOl,
  faHorizontalRule,
  faUndo,
  faRedo,
  faLink,
  faTable,
  faImage,
  faFilm,
]

addIcons(Icons)
