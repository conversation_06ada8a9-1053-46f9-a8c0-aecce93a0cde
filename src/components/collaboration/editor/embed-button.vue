<template>
  <EditorButton title="Video">
    <template v-slot:trigger="{ hide, show }">
      <MPopover
        ref="popover"
        placement="bottomRight"
        :overlay-style="{ width: '300px' }"
        overlay-class-name="picker-overlay"
        @show="handlePopoverShow"
      >
        <template v-slot:trigger>
          <MButton
            variant="transparent"
            class="text-neutral-darker"
            size="small"
            :shadow="false"
            @mouseenter="show"
            @mouseleave="hide"
          >
            <slot />
          </MButton>
        </template>
        <FlotoForm class="p-2" @submit="handleFormSubmit">
          <FlotoFormItem
            ref="input"
            v-model="url"
            label="Video Link"
            rules="required|url"
            placeholder="http://youtube.com/embed/xxx"
            @keydown.esc="hideMenu"
          />
          <template v-slot:submit>
            <div class="text-right">
              <MButton type="submit" size="small" class="mr-2"> Add </MButton>
              <MButton size="small" variant="default" @click="hideMenu">
                Cancel
              </MButton>
            </div>
          </template>
        </FlotoForm>
      </MPopover>
    </template>
  </EditorButton>
</template>

<script>
import EditorButton from './editor-button'
export default {
  name: 'EmbedButton',
  components: { EditorButton },
  data() {
    return {
      url: '',
    }
  },
  methods: {
    handlePopoverShow() {
      setTimeout(() => {
        this.$refs.input.focus()
      }, 250)
    },
    handleFormSubmit() {
      this.$emit('add', this.url)
      this.hideMenu()
    },
    hideMenu() {
      this.$emit('hide')
      this.$refs.popover.hide()
    },
  },
}
</script>
