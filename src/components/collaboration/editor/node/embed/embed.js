import { Node } from 'tiptap'
import EmbedNode from './embed.vue'
// import { toggleBlockType } from 'tiptap-commands'

export default class Embed extends Node {
  get name() {
    return 'video'
  }

  get schema() {
    return {
      attrs: {
        src: {
          default: null,
        },
      },
      group: 'block',
      selectable: false,
      parseDOM: [
        {
          tag: 'iframe',
          getAttrs: (dom) => ({
            src: dom.getAttribute('src'),
          }),
        },
      ],
      toDOM: (node) => [
        'iframe',
        {
          src: node.attrs.src,
          frameborder: 0,
          allowfullscreen: 'true',
        },
      ],
    }
  }

  commands({ type }) {
    return (attrs) => (state, dispatch) => {
      const { selection } = state
      const position = selection.$cursor
        ? selection.$cursor.pos
        : selection.$to.pos
      const node = type.create(attrs)
      const transaction = state.tr.insert(position, node)
      dispatch(transaction)
    }
  }

  get view() {
    return EmbedNode
  }
}
