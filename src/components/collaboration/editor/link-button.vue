<template>
  <MPopover
    ref="popoverRef"
    placement="bottomRight"
    :overlay-style="{ width: '300px' }"
    overlay-class-name="picker-overlay"
    @show="handlePopoverShow"
  >
    <template v-slot:trigger="{ toggle }">
      <EditorButton
        title="Link"
        class="mr-1"
        :active="active"
        :style="buttonStyle"
        @click="toggle"
      >
        <slot />
      </EditorButton>
    </template>
    <FlotoForm class="p-2" @submit="handleFormSubmit">
      <FlotoFormItem
        ref="input"
        v-model="url"
        label="Link"
        rules="required|url"
        placeholder="http://example.com"
        @keydown.esc="hideMenu"
      />
      <template v-slot:submit>
        <div class="text-right">
          <MButton type="submit" size="small" class="mr-2"> Link </MButton>
          <MButton
            v-if="link"
            size="small"
            variant="default"
            class="mr-2"
            @click="removeLink"
          >
            Remove
          </MButton>
          <MButton size="small" variant="default" @click="hideMenu">
            Cancel
          </MButton>
        </div>
      </template>
    </FlotoForm>
  </MPopover>
</template>

<script>
import EditorButton from './editor-button'
export default {
  name: 'LinkButton',
  components: { EditorButton },
  props: {
    link: { type: String, default: undefined },
    disabled: { type: Boolean, default: false },
    active: { type: Boolean, default: false },
  },
  data() {
    return {
      url: '',
    }
  },
  computed: {
    buttonStyle() {
      if (this.disabled) {
        return { pointerEvents: 'none', opacity: 0.5 }
      }
      return {}
    },
  },
  watch: {
    link(newValue) {
      this.url = newValue
    },
  },
  methods: {
    handlePopoverShow() {
      setTimeout(() => {
        this.$refs.input.focus()
      }, 250)
    },
    handleFormSubmit() {
      this.$emit('add', this.url)
      this.hideMenu()
    },
    removeLink() {
      this.$emit('remove')
      this.hideMenu()
    },
    hideMenu() {
      this.$emit('hide')
      this.$refs.popoverRef.hide()
    },
  },
}
</script>
