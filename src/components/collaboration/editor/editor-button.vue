<template>
  <MTooltip>
    <template v-slot:trigger="triggerSlotData">
      <slot name="trigger" v-bind="triggerSlotData">
        <MButton
          :shadow="false"
          :rounded="false"
          :variant="active ? 'neutral-lighter' : 'transparent'"
          size="small"
          v-bind="$attrs"
          v-on="$listeners"
        >
          <slot />
        </MButton>
      </slot>
    </template>
    {{ title }}
  </MTooltip>
</template>

<script>
export default {
  name: 'EditorButton',
  props: {
    title: { type: String, required: true },
    active: { type: Boolean, default: false },
  },
}
</script>
