<template>
  <div class="flex flex-1 flex-col min-h-0">
    <div class="min-h-0 flex-col bg-neutral-lightest flex flex-1">
      <MessageViewer
        ref="messageViewerRef"
        :messages="messages"
        :current-user-id="viewerId"
        :current-user="viewer"
        :user-options="UserProviderContext.options"
      />
    </div>
    <div v-if="sender" :key="viewerId" class="my-2 flex flex-col">
      <Compose
        ref="composeRef"
        :sender="sender"
        :processing="processing"
        @send="handleNewMessageSend"
      />
    </div>
  </div>
</template>

<script>
import Find from 'lodash/find'
import MessageViewer from './message-viewer.vue'
import Compose from './compose.vue'

export default {
  name: 'Collaboration',
  components: {
    MessageViewer,
    Compose,
  },
  inject: { UserProviderContext: { default: { options: [] } } },
  props: {
    messages: {
      type: Array,
      default() {
        return []
      },
    },
    viewerId: {
      type: Number,
      required: true,
    },
    viewer: {
      type: String,
      default: undefined,
    },
    sendMessageFn: {
      type: Function,
      required: true,
    },
  },
  data() {
    return {
      processing: false,
    }
  },
  computed: {
    sender() {
      return Find(this.UserProviderContext.options, { id: this.viewerId })
    },
  },
  methods: {
    handleNewMessageSend(data) {
      this.processing = true
      this.sendMessageFn(data)
        .then(() => {
          this.$nextTick(() => {
            this.$refs.messageViewerRef.scrollToBottom()
            this.$refs.composeRef.clearData()
          })
        })
        .finally(() => (this.processing = false))
    },
  },
}
</script>
