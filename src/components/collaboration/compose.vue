<template>
  <div class="flex flex-1">
    <div class="avatar px-4">
      <FlotoUserAvatar
        v-if="sender"
        size="large"
        class="rounded-full"
        :avatar="sender.avatarUrl"
      />
    </div>
    <div :key="renderKey" class="flex-1 message-compose-container">
      <!-- <div class="collaboration-recipients-picker">
        <FlotoTagsPicker
          v-model="recipients"
          class="mb-0"
          title="Recipients"
          always-text-mode
          rounded
          variant="default"
          placeholder="Recipients"
        />
      </div>
      <MDivider class="m-0" /> -->
      <Editor
        v-model="content"
        class="border-none"
        :rows="5"
        auto-focus
        placeholder="Start Typing"
      />
      <div class="flex my-2 px-2">
        <MButton
          :disabled="!content"
          :loading="processing"
          @click="handleSubmitMessage"
        >
          Send
        </MButton>
      </div>
    </div>
  </div>
</template>

<script>
import Editor from './editor/editor.vue'

export default {
  name: 'Compose',
  components: {
    Editor,
  },
  props: {
    sender: {
      type: Object,
      required: true,
    },
    processing: { type: Boolean, default: false },
  },
  data() {
    return {
      content: undefined,
      recipients: [],
      renderKey: 1,
    }
  },
  methods: {
    handleSubmitMessage() {
      this.$emit('send', {
        content: this.content,
        recipients: this.recipients,
      })
    },
    clearData() {
      this.content = undefined
      this.recipients = []
      this.renderKey++
    },
  },
}
</script>

<style lang="less" scoped>
.message-compose-container {
  border: 1px solid var(--border-color);
}
</style>
<style lang="less">
.collaboration-recipients-picker {
  @apply px-2 py-1;

  * {
    padding-bottom: 0 !important;
    margin-bottom: 0 !important;
  }

  input {
    border-bottom: 0;
  }
}
</style>
