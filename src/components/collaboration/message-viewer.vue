<template>
  <div
    ref="messageScrollContainer"
    class="flex flex-col min-h-0 overflow-auto flex-1"
  >
    <Message
      v-for="message in messages"
      :key="message.id"
      :content="message.content"
      :date-time="message.dateTime"
      :sender-id="message.senderId"
      :sender="message.sender"
      :user-options="userOptions"
      :is-my-message="
        message.senderId === currentUserId ||
        (message.sender !== undefined ? message.sender === currentUser : false)
      "
    />
  </div>
</template>

<script>
import { scrollTo } from '@utils/smooth-scroll'
import Message from './message.vue'

export default {
  name: 'MessageViewer',
  components: {
    Message,
  },
  props: {
    userOptions: {
      type: Array,
      default() {
        return []
      },
    },
    messages: {
      type: Array,
      required: true,
    },
    currentUserId: {
      type: Number,
      required: true,
    },
    currentUser: {
      type: String,
      default: undefined,
    },
  },
  mounted() {
    setTimeout(() => {
      if (this.$refs.messageScrollContainer) {
        this.$refs.messageScrollContainer.scrollTop =
          this.$refs.messageScrollContainer.scrollHeight
      }
    }, 50)
  },
  methods: {
    scrollToBottom() {
      scrollTo(
        this.$refs.messageScrollContainer,
        this.$refs.messageScrollContainer.scrollHeight
      )
    },
  },
}
</script>
