<template>
  <div />
</template>

<script>
import { severityDBWorker } from '@/src/workers'
import Bus from '@utils/emitter'
import { generateId } from '../utils/id'

export default {
  name: 'StatusDb',
  data() {
    return {
      guid: generateId(),
    }
  },
  mounted() {
    this.fillInitialStatus()
  },
  methods: {
    fillInitialStatus() {
      const handleAllStatusReceived = async (data) => {
        setTimeout(async () => {
          await this.fillStatusInDb(data)
          setTimeout(() => this.$emit('status-received'), 400)
        }, 2000)
      }
      Bus.$on('object.status.query', handleAllStatusReceived)
      Bus.$emit('server:event', {
        'event.type': 'object.status.query',
        'event.context': {
          [this.$constants.UI_EVENT_UUID]: this.guid,
        },
      })
      this.$once('hook:beforeDestroy', () => {
        Bus.$off('object.status.query', handleAllStatusReceived)
      })
    },
    async fillStatusInDb(result) {
      const severitiesMap = await severityDBWorker.getSeverityMap({}, true)
      const objectSeverities = Object.keys(result['object.status'] || {}).map(
        (key) => ({
          id: +key,
          entity: +key,
          status: result['object.status'][key],
          severity: severitiesMap[key],
        })
      )
      const instanceSeverities = Object.keys(
        result['instance.status'] || {}
      ).map((key) => ({
        'policy.key': key,
        id: +key.split('``||``')[0],
        entity: +key.split('``||``')[0],
        instance: key.split('``||``')[1],
        status: result['instance.status'][key],
        severity: severitiesMap[key.replace('``||``', '@@')],
      }))

      const allSeverities = [...objectSeverities, ...instanceSeverities]
      if (allSeverities.length > 0) {
        await severityDBWorker.updateBulk(allSeverities, true, false)
        allSeverities.forEach((item) => {
          if (item.instance || item.counter) {
            Bus.$emit(this.$constants.EVENT_STATUS_COUNTER_DB_CHANGED, {
              objectId: item.id,
              instance: item.instance,
              status: item.status,
            })
          } else {
            Bus.$emit(
              this.$constants.EVENT_STATUS_DB_CHANGED,
              item.id,
              item.status
            )
          }
        })
        Bus.$emit(this.$constants.EVENT_STATUS_COUNTER_DB_CHANGED)
      }
    },
  },
}
</script>
