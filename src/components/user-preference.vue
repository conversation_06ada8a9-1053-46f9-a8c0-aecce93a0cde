<script>
export default {
  name: 'UserPreference',
  provide() {
    const UserPreferenceContext = {}
    Object.defineProperty(UserPreferenceContext, 'notifications', {
      enumerable: true,
      get: () => {
        return this.notificationPreferences
      },
    })
    return { UserPreferenceContext }
  },
  data() {
    return {}
  },
  created() {
    // this.getNotificationPreferences()
  },
  methods: {},
  render() {
    return this.$scopedSlots.default({})
  },
}
</script>
