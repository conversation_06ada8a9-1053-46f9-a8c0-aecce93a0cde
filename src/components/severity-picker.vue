<template>
  <FlotoDropdownPicker
    :value="value"
    :options="severityOptions"
    v-bind="$attrs"
    @change="$emit('change', $event)"
  >
    <template v-slot:before-menu-text="{ item }">
      <Severity disable-tooltip :severity="item.key" class="mx-1" />
    </template>
  </FlotoDropdownPicker>
</template>

<script>
import Capitalize from 'lodash/capitalize'
import { SEVERITY_MAP } from '@data/monitor'
import Severity from './severity.vue'

export default {
  name: 'SeverityPicker',
  components: {
    Severity,
  },
  model: { event: 'change' },
  props: {
    value: {
      type: [String, Array],
      default: undefined,
    },
  },
  data() {
    this.severityOptions = Object.keys(SEVERITY_MAP).map((s) => ({
      key: s,
      text: Capitalize(s),
    }))
    return {}
  },
}
</script>
