<template>
  <MAvatar class="user-avatar" :size="size" :icon="icon" :src="userAvatar">
    <slot />
  </MAvatar>
</template>

<script>
export default {
  name: 'FlotoUserAvatar',
  props: {
    userId: { type: Number, default: undefined },
    size: { type: [String, Number], default: undefined },
    avatar: { type: String, default: undefined },
    icon: { type: String, default: 'user' },
  },
  data() {
    return {
      fullUser: {},
      loading: true,
    }
  },
  computed: {
    userAvatar() {
      if (this.avatar) {
        return this.avatar
      }
      return undefined
    },
  },
}
</script>

<style lang="less" scoped>
.user-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--border-color);
}
</style>
