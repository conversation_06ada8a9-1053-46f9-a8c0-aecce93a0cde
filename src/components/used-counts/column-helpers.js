import Constants from '@constants'
import { generateId } from '@utils/id'

export const PREDEFINED_COLUMNS = {
  user: {
    defaultSort: '-userName',
    columns: [
      {
        key: 'userName',
        name: 'User Name',
        searchable: true,
        sortable: true,
      },
      {
        key: 'userType',
        name: 'Type',
        searchable: true,
        sortable: true,
      },
      {
        key: 'userEmail',
        name: 'Email',
        searchable: true,
        sortable: true,
      },
    ],
    transform(user) {
      return {
        id: user.id,
        userName: user['user.name'],
        userType:
          (user['user.type'] || '').toLowerCase() === 'ldap'
            ? 'LDAP'
            : user['user.type'],
        userEmail: user['user.email'],
      }
    },
  },
  trapForwarder: {
    defaultSort: '-name',
    columns: [
      {
        key: 'name',
        name: 'SNMP Trap Forwarder Name',
        searchable: true,
        sortable: true,
      },
      {
        key: 'ipAddress',
        name: 'SNMP Trap Destination',
        searchable: true,
        sortable: true,
      },
      {
        key: 'port',
        name: 'Port',
        searchable: true,
        sortable: true,
      },
    ],
    transform(trapForwarder) {
      return {
        id: trapForwarder.id,
        name: trapForwarder['snmp.trap.forwarder.name'],
        ipAddress: trapForwarder['snmp.trap.forwarder.destination'],
        port: trapForwarder['snmp.trap.forwarder.port'],
      }
    },
  },
  'assigned.log.parsers': {
    defaultSort: '-name',
    columns: [
      {
        key: 'name',
        name: 'Log Parser Name',
        searchable: true,
        sortable: true,
      },
      {
        key: 'monitorType',
        name: 'Source Type',
        searchable: true,
        sortable: true,
        align: 'center',
      },
    ],
    transform(assignedLogParsers) {
      return {
        id: assignedLogParsers.id,
        name: assignedLogParsers['log.parser.name'],
        monitorType: assignedLogParsers['log.parser.source.type'],
      }
    },
  },
  discovery: {
    defaultSort: '-profileName',
    columns: [
      {
        key: 'profileName',
        name: 'Discovery Profile Name',
        searchable: true,
        sortable: true,
      },
      {
        key: 'ipSubnet',
        name: 'IP/Host/IP Range/CIDR/CSV',
        searchable: true,
        sortable: true,
      },
      {
        key: 'status',
        name: 'Status',
        searchable: true,
        sortable: true,
      },
      {
        key: 'discoveredMonitors',
        name: 'Discovered Objects',
        searchable: true,
        sortable: true,
        align: 'center',
      },
    ],
    transform(discovery) {
      return {
        id: discovery.id,
        ipSubnet: discovery['discovery.target.name'],
        profileName: discovery['discovery.name'],
        discoveredMonitors: discovery['discovery.discovered.objects'] || 0,
        status: discovery['discovery.status'],
      }
    },
  },

  mailConfiguration: {
    defaultSort: '-authenticationType',
    columns: [
      {
        key: 'username',
        name: 'UserName',
        searchable: true,
        sortable: true,
      },
      {
        key: 'email',
        name: 'From Email',
        searchable: true,
        sortable: true,
      },
    ],
    transform(mailConfiguration) {
      return {
        id: mailConfiguration.id,
        email: mailConfiguration['mail.server.sender'],
        username: mailConfiguration['mail.server.username'],
      }
    },
  },
  group: {
    defaultSort: '-name',
    columns: [
      {
        key: 'name',
        name: 'Group Name',
        searchable: true,
        sortable: true,
      },
    ],
    transform(group) {
      return {
        id: group.name,
        name: group['group.name'],
      }
    },
  },
  monitor: {
    defaultSort: '-monitorName',
    columns: [
      {
        key: 'monitorName',
        name: 'Monitor',
        searchable: true,
        sortable: true,
      },
      {
        key: 'ip',
        name: 'IP',
        searchable: true,
        sortable: true,
        width: '120px',
      },
      {
        key: 'host',
        name: 'Host',
        searchable: true,
        sortable: true,
      },

      {
        key: 'monitorType',
        name: 'Type',
        searchable: true,
        sortable: true,
        align: 'center',
        width: '75px',
      },
      {
        key: 'group',
        name: 'Groups',
        contextKey: 'groupContext',
        searchable: true,
        sortable: true,
        searchKey: 'groupDisplay',
        sortKey: 'groupDisplay',
      },
    ],
    transform(monitor) {
      return {
        id: monitor.id || monitor['object.ip'],
        monitorName:
          monitor['object.category'] === Constants.SERVICE_CHECK
            ? monitor['object.target'] || monitor.target
            : monitor['object.name'] ||
              monitor.name ||
              monitor['object.ip'] ||
              monitor.ip,
        ip: monitor['object.ip'] || monitor.ip,
        host: monitor['object.host'] || monitor.host,
        monitorType: monitor['object.type'] || monitor.type,
        severity: monitor['object.severity'] || monitor.severity || 'UNKNOWN',
        group: monitor['object.groups'] || monitor.groups || undefined,
      }
    },
  },
  metric: {
    defaultSort: '-metricName',
    columns: [
      {
        key: 'metricName',
        name: 'Metric',
        searchable: true,
        sortable: true,
      },
      {
        key: 'ip',
        name: 'IP',
        searchable: true,
        sortable: true,
        width: '120px',
      },
      {
        key: 'host',
        name: 'Host',
        searchable: true,
        sortable: true,
      },
      {
        key: 'monitorType',
        name: 'Type',
        searchable: true,
        sortable: true,
        align: 'center',
        width: '75px',
      },
      {
        key: 'group',
        name: 'Groups',
        contextKey: 'groupContext',
        searchable: true,
        sortable: true,
        searchKey: 'groupDisplay',
        sortKey: 'groupDisplay',
      },
    ],
    transform(metric) {
      return {
        id: metric.id,
        metricName: metric['metric.name'] || metric.name,
        ip: metric['object.ip'] || metric.ip,
        host: metric['object.host'] || metric.host,
        monitorType: metric['object.type'] || metric.type,
        severity: metric['object.severity'] || metric.severity || 'Unknown',
        group: metric['object.groups'] || metric.groups || [],
      }
    },
  },
  apps: {
    defaultSort: '-applicationName',
    columns: [
      {
        key: 'applicationName',
        name: 'Apps',
        searchable: true,
        sortable: true,
      },
      {
        key: 'ip',
        name: 'IP',
        searchable: true,
        sortable: true,
        width: '120px',
      },
      {
        key: 'host',
        name: 'Host',
        searchable: true,
        sortable: true,
      },
      {
        key: 'monitorType',
        name: 'Type',
        searchable: true,
        sortable: true,
        align: 'center',
        width: '75px',
      },
      {
        key: 'group',
        name: 'Groups',
        contextKey: 'groupContext',
        searchable: true,
        sortable: true,
        searchKey: 'groupDisplay',
        sortKey: 'groupDisplay',
      },
    ],
    transform(apps) {
      return {
        id: apps.id,
        applicationName: apps['metric.name'] || apps.name,
        ip: apps['object.ip'] || apps.ip,
        host: apps['object.host'] || apps.host,
        monitorType: apps['object.type'] || apps.type,
        severity: apps['object.severity'] || apps.severity || 'Unknown',
        group: apps['object.groups'] || apps.groups || [],
      }
    },
  },
  agent: {
    defaultSort: '-name',
    columns: [
      {
        key: 'name',
        name: 'Monitor',
        searchable: true,
        sortable: true,
      },
      {
        key: 'ip',
        name: 'IP',
        searchable: true,
        sortable: true,
        width: '120px',
      },
      {
        key: 'host',
        name: 'Host',
        searchable: true,
        sortable: true,
      },

      {
        key: 'monitorType',
        name: 'Type',
        searchable: true,
        sortable: true,
        width: '75px',
      },
      {
        key: 'group',
        name: 'Groups',
        contextKey: 'groupContext',
        searchable: true,
        sortable: true,
        searchKey: 'groupDisplay',
        sortKey: 'groupDisplay',
      },
    ],
    transform(monitor) {
      return {
        id: monitor.id,
        name:
          monitor['object.category'] === Constants.SERVICE_CHECK
            ? monitor['object.target'] || monitor.target
            : monitor['object.name'] || monitor.name,
        ip: monitor['object.ip'] || monitor.ip,
        host: monitor['object.host'] || monitor.host,
        monitorType: monitor['object.type'] || monitor.type,
        group: monitor['object.groups'] || monitor.groups || [],
      }
    },
  },
  'parser-mapper': {
    defaultSort: '-name',
    columns: [
      {
        key: 'name',
        name: 'Monitor',
        searchable: true,
        sortable: true,
      },
      {
        key: 'ip',
        name: 'IP',
        searchable: true,
        sortable: true,
        width: '120px',
      },
      {
        key: 'host',
        name: 'Host',
        searchable: true,
        sortable: true,
      },
      {
        key: 'monitorType',
        name: 'Type',
        searchable: true,
        sortable: true,
        width: '75px',
      },
      {
        key: 'group',
        name: 'Groups',
        contextKey: 'groupContext',
        searchable: true,
        sortable: true,
        searchKey: 'groupDisplay',
        sortKey: 'groupDisplay',
      },
    ],
    transform(log) {
      return {
        id: log.id,
        name: log['log.monitor'],
        ip: log['log.ip'],
        host: log['log.host'],
        monitorType: log['log.monitorType'],
        group: log['log.group'],
      }
    },
  },
  'Event Source': {
    defaultSort: '-sourceName',
    columns: [
      {
        key: 'monitorName',
        name: 'Source',
        searchable: true,
        sortable: true,
      },

      {
        key: 'group',
        name: 'Groups',
        contextKey: 'groupContext',
        searchable: true,
        sortable: true,
        searchKey: 'groupDisplay',
        sortKey: 'groupDisplay',
      },
    ],
    transform(monitor) {
      return {
        id: monitor.id,
        monitorName: monitor['event.source'],
        group: monitor['source.groups'],
      }
    },
  },
  NetRoute: {
    defaultSort: '-sourceName',
    columns: [
      {
        key: 'name',
        name: 'NetRoute Name',
        searchable: true,
        sortable: true,
      },
      {
        key: 'source',
        name: 'Source',
        searchable: true,
        sortable: true,
      },
      {
        key: 'destination',
        name: 'Destination',
        searchable: true,
        sortable: true,
      },
    ],
    transform(monitor) {
      return {
        id: monitor.id,
        name: monitor['netroute.name'],
        source: monitor['netroute.source'],
        destination: monitor['netroute.destination'],
      }
    },
  },
  'Event Policy': {
    defaultSort: '-name',
    columns: [
      {
        key: 'name',
        name: 'Policy Name',
        searchable: true,
        sortable: true,
      },
      {
        key: 'policyType',
        name: 'Policy Type',
        searchable: true,
        sortable: true,
      },
    ],
    transform(policy) {
      return {
        name: policy['policy.name'],
        policyType: policy['policy.type'],
      }
    },
  },
  'Metric Policy': {
    defaultSort: '-name',
    columns: [
      {
        key: 'name',
        name: 'Policy Name',
        searchable: true,
        sortable: true,
      },
      {
        key: 'policyType',
        name: 'Policy Type',
        searchable: true,
        sortable: true,
      },
    ],
    transform(policy) {
      return {
        name: policy['policy.name'],
        policyType: policy['policy.type'],
      }
    },
  },
  Runbook: {
    defaultSort: '-name',
    columns: [
      {
        key: 'name',
        name: 'Runbook Name',
        searchable: true,
        sortable: true,
      },
      {
        key: 'runbookType',
        name: 'Runbook Type',
        searchable: true,
        sortable: true,
      },
    ],
    transform(runbook) {
      return {
        name: runbook['runbook.plugin.name'],
        runbookType: runbook['runbook.plugin.type'],
      }
    },
  },
  Configuration: {
    defaultSort: '-sourceName',
    columns: [
      {
        key: 'monitorName',
        name: 'Monitor',
        searchable: true,
        sortable: true,
      },
      {
        key: 'ip',
        name: 'IP',
        searchable: true,
        sortable: true,
        width: '120px',
      },

      {
        key: 'host',
        name: 'Host',
        searchable: true,
        sortable: true,
      },

      {
        key: 'monitorType',
        name: 'Type',
        searchable: true,
        sortable: true,
        align: 'center',
        width: '75px',
      },
      {
        key: 'group',
        name: 'Groups',
        contextKey: 'groupContext',
        searchable: true,
        sortable: true,
        searchKey: 'groupDisplay',
        sortKey: 'groupDisplay',
      },
    ],
    transform(monitor) {
      return {
        ip: monitor['object.ip'],
        monitorName: monitor['object.name'],
        id: monitor['config.object.id'],
        monitorType: monitor['object.type'],
        host: monitor['object.host'],
        group: monitor['object.groups'],
      }
    },
  },
  'Config Template': {
    defaultSort: '-sourceName',
    columns: [
      {
        key: 'templateName',
        name: 'Template Name',
        searchable: true,
        sortable: true,
      },
      {
        key: 'os',
        name: 'Template Os Type',
        searchable: true,
        sortable: true,
      },

      {
        key: 'vendor',
        name: 'Template Vendor',
        searchable: true,
        sortable: true,
      },
    ],
    transform(template) {
      return {
        templateName: template['config.template.name'],
        os: template['config.template.os.type'],
        vendor: template['config.template.vendor'],
      }
    },
  },

  'Backup Profile': {
    defaultSort: '-sourceName',
    columns: [
      {
        key: 'name',
        name: 'Backup Profile Name',
        searchable: true,
        sortable: true,
      },
      {
        key: 'profileType',
        name: 'Backup Profile Type',
        searchable: true,
        sortable: true,
      },
    ],
    transform(template) {
      return {
        name: template['backup.profile.name'],
        profileType: template['backup.profile.type'],
      }
    },
  },

  Interface: {
    defaultSort: '-sourceName',
    columns: [
      {
        key: 'interfaceName',
        name: 'Interface Name',
        searchable: true,
        sortable: true,
      },
      // {
      //   key: 'portType',
      //   name: 'Port Type',
      //   searchable: true,
      //   sortable: true,
      // },
      {
        key: 'alias',
        name: 'Interface Alias',
        searchable: true,
        sortable: true,
      },
      {
        key: 'tags',
        name: 'Tags',
        searchable: true,
        sortable: true,
        width: '520px',
        minWidth: '520px',
      },
    ],
    transform(i) {
      return {
        id: generateId(),
        name: i['object.name'],
        interfaceName: i['interface.name'],
        ip: i['object.ip'],
        tags: i['instance.tags'],
        alias: i['interface.alias'],
        index: i['interface.index'],
        description: i['interface.description'],
        portType: i['interface.port.type'],

        untouchedContext: {
          ...i,
        },
      }
    },
  },
  VM: {
    defaultSort: '-sourceName',
    columns: [
      {
        key: 'name',
        name: 'Vm Name',
        searchable: true,
        sortable: true,
      },

      {
        key: 'ip',
        name: 'IP',
        searchable: true,
        sortable: true,
      },
      {
        key: 'type',
        name: 'Type',
        searchable: true,
        sortable: true,
      },
      {
        key: 'tags',
        name: 'Tags',
        searchable: true,
        sortable: true,
        width: '520px',
        minWidth: '520px',
      },
    ],
    transform(i) {
      return {
        id: generateId(),
        name: i['object.name'],
        ip: i['object.ip'],
        type: i['object.type'],
        tags: i['instance.tags'],
        untouchedContext: {
          ...i,
        },
        // alias: i['object.alias'],
      }
    },
  },
  Disk: {
    defaultSort: '-sourceName',
    columns: [
      {
        key: 'name',
        name: 'Disk Name',
        searchable: true,
        sortable: true,
      },

      {
        key: 'tags',
        name: 'Tags',
        searchable: true,
        sortable: true,
      },
    ],
    transform(i) {
      return {
        id: generateId(),
        name: i['object.name'],
        ip: i['object.ip'],
        type: i['object.type'],
        tags: i['instance.tags'],
        // alias: i['object.alias'],
        untouchedContext: {
          ...i,
        },
      }
    },
  },
  Processes: {
    defaultSort: '-sourceName',
    columns: [
      {
        key: 'name',
        name: 'Process Name',
        searchable: true,
        sortable: true,
      },
      // {
      //   key: 'processId',
      //   name: 'Process ID',
      //   searchable: true,
      //   sortable: true,
      // },

      {
        key: 'tags',
        name: 'Tags',
        searchable: true,
        sortable: true,
        width: '520px',
        minWidth: '520px',
      },
    ],
    transform(i) {
      return {
        id: generateId(),
        name: i['object.name'],
        // processId: i[''],
        tags: i['instance.tags'],
        untouchedContext: {
          ...i,
        },
      }
    },
  },
  Services: {
    defaultSort: '-sourceName',
    columns: [
      {
        key: 'name',
        name: 'Service Name',
        searchable: true,
        sortable: true,
      },

      {
        key: 'tags',
        name: 'Tags',
        searchable: true,
        sortable: true,
        width: '520px',
        minWidth: '520px',
      },
    ],
    transform(i) {
      return {
        id: generateId(),
        name: i['object.name'],
        tags: i['instance.tags'],
        untouchedContext: {
          ...i,
        },
      }
    },
  },
  'Access points': {
    defaultSort: '-sourceName',
    columns: [
      {
        key: 'name',
        name: 'Access point Name',
        searchable: true,
        sortable: true,
      },
      // {
      //   key: 'ip',
      //   name: 'IP',
      //   searchable: true,
      //   sortable: true,
      // },
      // {
      //   key: 'type',
      //   name: 'Type',
      //   searchable: true,
      //   sortable: true,
      // },

      {
        key: 'tags',
        name: 'Tags',
        searchable: true,
        sortable: true,
        width: '520px',
        minWidth: '520px',
      },
    ],
    transform(i) {
      return {
        id: generateId(),
        name: i['object.name'],
        tags: i['instance.tags'],
        untouchedContext: {
          ...i,
        },
      }
    },
  },
  'audit.policies': {
    defaultSort: '-sourceName',
    columns: [
      {
        key: 'name',
        name: 'Name',
        searchable: true,
        sortable: true,
      },
      {
        key: 'description',
        name: 'description',
        searchable: true,
        sortable: true,
      },

      {
        key: 'tags',
        name: 'Tags',
        searchable: true,
        sortable: true,
        cellRender: 'tag',
      },
    ],
    transform(i) {
      return {
        id: generateId(),
        name: i['compliance.policy.name'],
        tags: i['compliance.policy.tags'],
        description: i['compliance.policy.description'],
        untouchedContext: {
          ...i,
        },
      }
    },
  },
  Integration: {
    defaultSort: '-sourceName',
    columns: [
      {
        key: 'integrationType',
        name: 'Integration Type',
        searchable: true,
        sortable: true,
      },
    ],
    transform(i) {
      return {
        id: generateId(),
        integrationType: i['integration.type'],

        untouchedContext: {
          ...i,
        },
      }
    },
  },
}
