<template>
  <MonitorTypeProvider
    :device-types="[
      $constants.SERVER,
      $constants.NETWORK,
      $constants.SDN,
      $constants.WIRELESS,
      $constants.VIRTUALIZATION,
      $constants.CLOUD,
      $constants.SERVICE_CHECK,
      $constants.OTHER,
      $constants.HYPERCONVERGED_INFRASTRUCTURE,
    ]"
  >
    <MCol class="slide-filters">
      <MRow class="px-4">
        <MCol :size="4">
          <FlotoFormItem label="Monitors">
            <FlotoDropdownPicker
              id="used-count-monitor-picker"
              v-model="currentValue.monitorName"
              allow-clear
              :options="monitorOptions"
              multiple
              searchable
            />
          </FlotoFormItem>
        </MCol>
        <MCol :size="4">
          <FlotoFormItem label="Types">
            <MonitorTypePicker
              id="used-count-type-picker"
              v-model="currentValue.types"
              allow-clear
              class="w-full"
              multiple
            />
          </FlotoFormItem>
        </MCol>
        <MCol class="mt-6">
          <MButton
            id="used-count-reset-btn"
            variant="default"
            @click="$emit('change', { monitorName: [], types: [] })"
          >
            Reset
          </MButton>
          <MButton id="used-count-apply-btn" class="ml-2" @click="apply"
            >Apply</MButton
          >
        </MCol>
        <MCol class="text-right">
          <MButton
            id="close-used-count"
            variant="transparent"
            :shadow="false"
            shape="circle"
            class="filter-close"
            @click="$emit('hide')"
          >
            <MIcon name="times" class="text-neutral-light" />
          </MButton>
        </MCol>
      </MRow>
    </MCol>
  </MonitorTypeProvider>
</template>

<script>
import MonitorTypePicker from '@components/data-picker/monitor-type-picker.vue'
import MonitorTypeProvider from '@components/data-provider/monitor-type-provider.vue'

export default {
  name: 'MonitorCountFilter',
  components: { MonitorTypePicker, MonitorTypeProvider },
  model: {
    event: 'change',
  },
  props: {
    value: { type: Object, required: true },
    monitorOptions: { type: Array, required: true },
  },
  data() {
    return {
      currentValue: { ...this.value },
    }
  },
  watch: {
    value(newValue) {
      this.currentValue = { ...newValue }
    },
  },
  methods: {
    apply() {
      this.$emit('change', this.currentValue)
    },
  },
}
</script>
