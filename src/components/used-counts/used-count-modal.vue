<template>
  <FlotoDrawer
    :open="open"
    width="60%"
    :scrolled-content="false"
    @hide="hideUsedCountModal"
  >
    <template v-slot:trigger>
      <span />
    </template>
    <template v-slot:title>
      <slot name="title" />
    </template>
    <div class="flex flex-col h-full">
      <FlotoContentLoader :loading="loading">
        <div class="px-4 mb-2">
          <!-- display tab is more than 1 type is given -->
          <MTab v-if="showTabs && !loading" v-model="currentCountType">
            <MTabPane
              v-for="tab in allowedTabs"
              :key="tab.countType"
              :tab="`${tab.title} (${getCounts(tab)})`"
            />
          </MTab>
        </div>
        <div class="flex flex-1 min-h-0 flex-col px-4">
          <GroupProvider>
            <div class="flex flex-1 min-h-0 flex-col">
              <UsedCountList
                v-if="!loading"
                ref="listRef"
                :key="currentCountType"
                :type="currentCountType"
                :fetch-fn="fetchReferencesData"
                :default-sort="defaultSort"
                :columns="columns"
                :use-custom-resource-key="useCustomResourceKey"
                :allow-export="allowExport"
                @updateContext="updateContext"
                @updateDisabledState="updateDisabledState"
              />
            </div>
          </GroupProvider>
        </div>

        <div class="flex w-full justify-end mb-2 mr-2">
          <MButton
            v-if="
              useCustomResourceKey && (data[selectedResourceKey] || []).length
            "
            :loading="disabledSaveBtn || processing"
            class="mr-2"
            :disabled="disabledSaveBtn || processing"
            @click="saveTagInfo"
            >Save</MButton
          >
        </div>
      </FlotoContentLoader>
    </div>
  </FlotoDrawer>
</template>

<script>
import GroupProvider from '@components/data-provider/group-provider.vue'
import { getReferencesApi } from './used-counts-api'
import { PREDEFINED_COLUMNS } from './column-helpers'
import UsedCountList from './used-count-list.vue'
// import { arrayWorker } from '@/src/workers'
import Api from '@api'

export default {
  name: 'UsedCountModal',
  components: { UsedCountList, GroupProvider },
  props: {
    allowedTabs: { type: Array, required: true },
    parentResourceType: { type: String, required: true },
    parentResourceId: { type: Number, required: true },
    apiPrefix: { type: String, required: true },
    open: { type: Boolean, default: false },
    useCustomResourceKey: { type: Boolean, default: false },
    allowExport: { type: Boolean, default: false },
  },
  data() {
    return {
      currentCountType: this.allowedTabs[0].countType,
      loading: true,
      data: {},
      processing: false,
      disabledSaveBtn: false,
    }
  },

  computed: {
    showTabs() {
      return this.allowedTabs.length > 1
    },
    columns() {
      if (
        PREDEFINED_COLUMNS[this.currentCountType] &&
        PREDEFINED_COLUMNS[this.currentCountType].columns
      ) {
        return PREDEFINED_COLUMNS[this.currentCountType].columns
      }
      const tab = this.allowedTabs.find(
        (t) => t.countType === this.currentCountType
      )
      if (tab && tab.columns) {
        return tab.columns
      }
      return []
    },
    defaultSort() {
      if (
        PREDEFINED_COLUMNS[this.currentCountType] &&
        PREDEFINED_COLUMNS[this.currentCountType].defaultSort
      ) {
        return PREDEFINED_COLUMNS[this.currentCountType].defaultSort
      }
      return undefined
    },
    selectedResourceKey() {
      const countType = this.countType()

      if (this.useCustomResourceKey) {
        return Object.keys(this.data || {}).find((key) =>
          key.includes(countType.resourceKey)
        )
      }
      return countType.resourceKey
    },
  },
  watch: {
    open(newValue, oldValue) {
      if (newValue !== oldValue) {
        if (newValue) {
          this.fetchReferences()
        }
      }
    },
  },
  created() {
    if (this.open) this.fetchReferences()
  },
  methods: {
    countType() {
      return this.allowedTabs.find((c) => c.countType === this.currentCountType)
    },
    fetchReferencesData() {
      const countType = this.countType()
      let records

      if (this.useCustomResourceKey) {
        const resourceKey = Object.keys(this.data || {}).find((key) =>
          key.includes(countType.resourceKey)
        )
        records = this.data[resourceKey] || []
      } else {
        records = this.data[countType.resourceKey] || []
      }

      let transformFn = (r) => r

      if (
        PREDEFINED_COLUMNS[this.currentCountType] &&
        PREDEFINED_COLUMNS[this.currentCountType].transform
      ) {
        transformFn = PREDEFINED_COLUMNS[this.currentCountType].transform
      }
      let data = (records || []).filter(Boolean).map(transformFn)
      return Promise.resolve(data)
    },
    fetchReferences() {
      const countType = this.countType()
      return getReferencesApi(
        this.apiPrefix,
        this.parentResourceType,
        this.parentResourceId,
        countType.url,
        this.currentCountType,
        countType.parentKey,
        countType.resourceKey
      ).then((result) => {
        this.data = Object.freeze(result || {})
        this.loading = false
      })
    },

    async updateContext(items = {}) {
      // const countType = this.countType()

      // let records
      if (this.useCustomResourceKey) {
        this.updateDisabledState(true)

        // const resourceKey = Object.keys(this.data || {}).find((key) =>
        //   key.includes(countType.resourceKey)
        // )
        // if (this.data[resourceKey]) {
        //   this.data = {
        //     ...this.data,
        //     [resourceKey]: items,
        //   }
        // }

        this.updateDisabledState(false)
        if (this.$refs.listRef) {
          this.$refs.listRef.resetSelection()
        }
      }
    },
    getCounts(tab) {
      if (this.useCustomResourceKey) {
        const resourceKey = Object.keys(this.data || {}).find((key) =>
          key.includes(tab.resourceKey)
        )
        return (this.data[resourceKey] || []).length
      }
      return (this.data[tab.resourceKey] || []).length
    },
    saveTagInfo() {
      if (this.$refs.listRef) {
        this.processing = true

        const countType = this.countType()

        const data = this.$refs.listRef.getSelectedResourceKeyData()
        return Api.put(countType.putUrl || countType.url, {
          [this.selectedResourceKey]: data,
        })
          .then(() => {
            this.processing = false
          })
          .finally(() => {
            setTimeout(() => {
              this.$emit('hide')
            }, 300)
          })
      }
    },
    updateDisabledState(state) {
      this.disabledSaveBtn = state
    },
    hideUsedCountModal() {
      this.loading = true
      this.$emit('hide')
    },
  },
}
</script>
