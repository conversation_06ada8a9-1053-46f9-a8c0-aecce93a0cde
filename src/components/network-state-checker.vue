<template>
  <Transition name="slideTop">
    <div
      v-if="!isConnected"
      class="network-state-checker text-center text-white"
    >
      <div class="inline-block">
        <!-- Unable to connect to server! Please
        <a class="text-white" @click.prevent="reloadWindow">
          <b>click here</b>
        </a>
        to refresh page -->

        Server connection is taking longer than expected. Retrying
        automatically... Please wait.
      </div>
    </div>
  </Transition>
</template>
<script>
export default {
  name: 'NetworkStateChecker',
  inject: { SocketContext: { default: {} } },
  computed: {
    isConnected() {
      return this.SocketContext.connected === undefined
        ? true
        : this.SocketContext.connected
    },
  },
  methods: {
    reloadWindow() {
      window.location.reload()
    },
  },
}
</script>
<style lang="less" scoped>
.network-state-checker {
  position: fixed;
  top: 0;
  z-index: 99999999999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  pointer-events: none;

  > div {
    padding: 0.5rem;
    pointer-events: all;
    background: var(--secondary-red);

    @apply rounded shadow-lg;
  }
}
</style>
