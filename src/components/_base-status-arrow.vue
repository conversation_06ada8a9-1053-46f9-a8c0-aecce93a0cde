<template>
  <MTooltip v-if="!disableTooltip">
    <template v-slot:trigger>
      <MIcon
        v-if="iconName"
        :name="iconName"
        :class="color"
        :size="size"
        class="inline-flex justify-center"
      />
    </template>
    {{ tooltipText || statusName }}
  </MTooltip>
  <MIcon
    v-else-if="iconName && disableTooltip"
    :name="iconName"
    :class="color"
    :size="size"
    class="inline-flex justify-center"
  />
</template>

<script>
import Capitalize from 'lodash/capitalize'

const statusIconMap = {
  up: 'up',
  down: 'down',
  running: 'up',
  unknown: 'down',
  'not running': 'down',
  'not reachable': 'down',
}

const statusTextColorMap = {
  up: 'text-secondary-green',
  down: 'text-secondary-red',
  unknown: 'text-secondary-orange',
  running: 'text-secondary-green',
  'not running': 'text-secondary-red',
  'not reachable': 'text-secondary-red',
}

export default {
  name: 'MStatusArrow',
  props: {
    status: { type: String, required: true },
    tooltipText: { type: String, default: undefined },
    disableTooltip: { type: Boolean, default: false },
    size: { type: String, default: undefined },
  },
  computed: {
    statusName() {
      return Capitalize(this.status)
    },
    iconName() {
      if (!this.status) {
        return null
      }
      const status = this.status.toLowerCase()
      if (statusIconMap[status]) {
        return `long-arrow-${statusIconMap[status]}`
      }
      return 'exclamation'
    },
    color() {
      const status = this.status.toLowerCase()
      return statusTextColorMap[status]
    },
  },
}
</script>
