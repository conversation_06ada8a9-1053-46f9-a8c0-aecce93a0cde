<template>
  <div class="flex flex-1 flex-col min-h-0">
    <GroupProvider v-if="useGroupProvider">
      <Hierarchy
        v-bind="$attrs"
        :value="value"
        v-on="listeners"
        @change="$emit('change', $event)"
      />
    </GroupProvider>
    <Hierarchy
      v-else
      v-bind="$attrs"
      :value="value"
      v-on="listeners"
      @change="$emit('change', $event)"
    />
  </div>
</template>

<script>
import GroupProvider from '@components/data-provider/group-provider.vue'
import Hierarchy from './hierarchy.vue'

export default {
  name: 'MonitorHierarchy',
  components: {
    GroupProvider,
    Hierarchy,
  },
  inheritAttrs: false,
  model: { event: 'change' },
  props: {
    useGroupProvider: {
      type: Boolean,
      default: false,
    },
    value: {
      type: Object,
      default: undefined,
    },
  },
  computed: {
    listeners() {
      const { change, ...listeners } = this.$listeners
      return listeners
    },
  },
}
</script>
