<template>
  <FlotoContentLoader :loading="loading">
    <div class="flex flex-1 min-h-0 flex-col">
      <div class="px-2 ml-4">
        <MInput
          v-model="searchTerm"
          class="search-box"
          placeholder="Search"
          name="search"
          style="max-width: unset"
        >
          <template v-slot:prefix>
            <MIcon name="search" />
          </template>
        </MInput>
      </div>
      <div
        class="flex-1 flex-col min-h-0 flex min-w-0 monitor-hierarchy-view hierarchy-explorer pt-2 overflow-auto px-2 ml-2"
      >
        <InfiniteTree
          v-if="defaultHierarchy.length"
          v-model="innerValue"
          :row-height="27"
          :search-term="searchTerm"
          :filter-fn="filterNode"
          :data="defaultHierarchy"
          :open-ids="defaultExpandedKeys"
          @change="handleTargetChange"
        >
          <template v-slot="{ item }">
            <div
              v-if="
                item.resourceType === 'monitor' ||
                item.resourceType === 'application' ||
                item.resourceType === 'vm'
              "
              :title="`${
                item.monitor
                  ? item.monitor.name || item.monitor.target
                  : item.name || item.target
              }${
                item.ip || (item.monitor || {}).ip
                  ? ` - (${item.ip || (item.monitor || {}).ip})`
                  : ''
              }`"
              class="flex cursor-pointer flex-1 items-center"
              style="white-space: nowrap"
            >
              <Severity
                disable-tooltip
                :severity="item.severity"
                class="mr-2"
              />
              <MonitorType
                width="15px"
                :type="
                  item.resourceType === 'application'
                    ? item.monitor
                      ? item.monitor.type
                      : item.name
                    : item.resourceType === 'vm'
                    ? $constants.VIRTUAL_MACHINE
                    : item.type
                "
                disable-tooltip
                class="mr-1"
              />
              <MonitorType
                v-if="item.isAgent"
                width="15px"
                :type="$constants.AGENT"
                disable-tooltip
                class="mr-1"
              />
              {{
                item.monitor
                  ? item.monitor.name || item.monitor.target
                  : item.name || item.target
              }}
              {{
                item.ip || (item.monitor || {}).ip
                  ? ` - (${item.ip || (item.monitor || {}).ip}${
                      item.port && item.category === $constants.SERVICE_CHECK
                        ? `:${item.port}`
                        : ''
                    })`
                  : ''
              }}
            </div>
            <div
              v-else
              class="flex flex-1 cursor-pointer items-center"
              style="white-space: nowrap"
            >
              <Severity
                disable-tooltip
                :severity="item.severity"
                class="mr-2"
              />
              <MonitorType
                v-if="item.icon"
                width="15px"
                :type="item.icon"
                disable-tooltip
                class="mr-1"
              />
              {{ item.name }}
            </div>
          </template>
        </InfiniteTree>
        <FlotoNoData
          v-else
          hide-svg
          icon="info-circle"
          header-tag="h3"
          variant="neutral"
        />
      </div>
    </div>
  </FlotoContentLoader>
</template>

<script>
import CloneDeep from 'lodash/cloneDeep'
import Uniq from 'lodash/uniq'
import Constants from '@constants'
import Bus from '@utils/emitter'
import Severity from '@components/severity.vue'
import InfiniteTree from '@components/hierarchy/infinite-tree.vue'
import { objectDBWorker, severityDBWorker } from '@/src/workers'
import MonitorType from '@components/monitor-type.vue'
import { TemplateIdMap } from '@/src/modules/inventory/inventory-api'
import { getInstanceSeverityApi } from '@/src/utils/socket-event-as-api'

export default {
  name: 'Hierarchy',
  components: { InfiniteTree, Severity, MonitorType },
  inject: { groupContext: { default: { options: new Map() } } },
  model: { event: 'change' },
  props: {
    groups: { type: Array, default: undefined },
    forTopology: { type: Boolean, default: false },
    value: {
      type: Object,
      default: undefined,
    },
    category: {
      type: [String, Array],
      default: undefined,
    },
    disableInstance: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: true,
      searchTerm: '',
      defaultHierarchy: [],
      defaultExpandedKeys: [],
    }
  },
  computed: {
    innerValue: {
      get() {
        return this.value
      },
      set(v) {
        if (!v) {
          return
        }
        if (v.disabled) {
          return
        }
        let value = CloneDeep(v)
        value.id = value.resourceId || value.id
        if (this.category === v.name) {
          this.$emit('change', value)
          return
        }
        this.$emit('change', value)
      },
    },
  },
  watch: {
    'value.id': function (newValue, oldValue) {
      if (newValue !== oldValue) {
        this.findValueObject()
      }
    },
    'groupContext.options': {
      handler(newValue, oldValue) {
        this.buildHierarchy()
      },
      immediate: true,
    },
  },
  created() {
    const monitorLoadedHandler = async () => {
      await objectDBWorker.clearHierarchyCache()
      this.buildHierarchy()
    }
    // const severityChangedHandler = async () => {
    //   await objectDBWorker.clearHierarchyCache()
    // }
    Bus.$on(this.$constants.EVENT_MONITOR_DB_CHANGED, monitorLoadedHandler)
    // Bus.$on(
    //   this.$constants.EVENT_SEVERITY_COUNTER_DB_CHANGED,
    //   severityChangedHandler
    // )
    this.$once('hook:beforeDestroy', () => {
      Bus.$off(this.$constants.EVENT_MONITOR_DB_CHANGED, monitorLoadedHandler)
      // Bus.$off(
      //   this.$constants.EVENT_SEVERITY_COUNTER_DB_CHANGED,
      //   severityChangedHandler
      // )
    })
  },
  beforeDestroy() {
    this.defaultHierarchy = []
  },
  methods: {
    handleTargetChange(node, tree) {
      if (this.category !== node.name) {
        tree.toggleNode(node)
      }
      if (this.category === node.name) {
        tree.selectNode(node)
        return
      }
      if (!node.disabled) {
        tree.selectNode(node)
        tree.openNode(node)
      }
    },
    filterNode(node, term) {
      return (
        (node.ip || '').toLowerCase().indexOf(term.toLowerCase()) >= 0 ||
        (node.name || '').toLowerCase().indexOf(term.toLowerCase()) >= 0 ||
        (node.severity || '').toLowerCase().indexOf(term.toLowerCase()) >= 0
      )
    },
    async findValueObject() {
      if (!this.value || !this.value.id) {
        return
      }
      if (
        this.defaultExpandedKeys.includes(this.value.id) ||
        this.defaultExpandedKeys.includes(this.value.parentId)
      ) {
        return
      }
      const defaultHierarchy = this.defaultHierarchy
      const path = await objectDBWorker.getPathById(
        defaultHierarchy,
        this.value.id
      )
      this.defaultExpandedKeys = Uniq([...this.defaultExpandedKeys, ...path])
    },
    async buildHierarchy() {
      if (this.groupContext.options.size === 0) {
        return
      }
      let fn
      const categories = Array.isArray(this.category)
        ? this.category
        : [this.category]
      let hierarchy = []

      let instanceSeverityMap = await getInstanceSeverityApi({
        instance: !categories.includes(this.$constants.SERVER),
        'event.context': await objectDBWorker.getInstancesOfObjects(categories),
      })
      let severities = await severityDBWorker.getSeverityMap({})
      for (let i = 0; i < categories.length; i++) {
        const category = categories[i]
        if (category === Constants.CLOUD) {
          fn = objectDBWorker.buildCloudObjectHierarchy
        } else {
          fn = category
            ? objectDBWorker.buildObjectHieararchyByCategory
            : objectDBWorker.buildObjectHieararchy
        }
        const categoryHierarchy = await fn(
          Array.from(this.groupContext.options.values()),
          [category],
          {
            disableInstance: this.disableInstance,
            excludeRootCloud: true,
            availableGroupTemplates: TemplateIdMap,
            severities: { ...severities, ...instanceSeverityMap },
          }
        )

        hierarchy = hierarchy.concat(categoryHierarchy)
      }
      instanceSeverityMap = null
      severities = null
      this.defaultHierarchy = Object.freeze(hierarchy)
      this.$emit('hierarchy-received', this.defaultHierarchy)
      this.defaultExpandedKeys = Object.freeze(hierarchy.map(({ id }) => id))
      this.findValueObject()
      // if (hierarchy.length) {
      this.loading = false
      // }
    },
  },
}
</script>
