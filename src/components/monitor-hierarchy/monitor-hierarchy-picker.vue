<template>
  <FlotoDropdownPicker
    ref="dropdownPickerRef"
    avoid-keyboard-navigation
    :searchable="false"
  >
    <MonitorHierarchy
      v-model="innerValue"
      class="mt-2"
      v-bind="$attrs"
      @change="$emit('change', $event)"
      v-on="listeners"
    />
  </FlotoDropdownPicker>
</template>

<script>
import MonitorHierarchy from './monitor-hierarchy.vue'

export default {
  name: 'MonitorHierarchyPicker',
  components: {
    MonitorHierarchy,
  },
  model: { event: 'change' },
  props: {
    value: {
      type: Object,
      default: undefined,
    },
  },
  computed: {
    listeners() {
      const { change, ...listeners } = this.$listeners
      return listeners
    },
    innerValue: {
      get() {
        return this.value
      },
      set(v) {
        this.$refs.dropdownPickerRef.handleHide()
        this.$emit('change', v)
      },
    },
  },
}
</script>
