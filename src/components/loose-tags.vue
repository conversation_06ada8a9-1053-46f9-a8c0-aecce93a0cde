<template>
  <SelectedItemPills v-if="disabled" tags :value="value" v-bind="$attrs" />
  <FlotoDropdownPicker
    v-else-if="asDropdown"
    :value="value"
    :options="tagOptions"
    placeholder=" "
    as-input
    searchable
    multiple
    allow-clear
    :max-allowed-selection="singleSelection ? 1 : undefined"
    @change="handleChange"
  />
  <MSelect
    v-else
    mode="tags"
    class="loose-tags-input"
    :options="tagOptions"
    placeholder="Add Tags"
    :value="value"
    @change="handleChange"
  >
    <template v-slot:clearIcon>
      <MIcon name="times-circle" />
    </template>
  </MSelect>
</template>

<script>
import Trim from 'lodash/trim'
import SelectedItemPills from '@components/dropdown-trigger/selected-item-pills.vue'
import { getAllTagsApi } from '@modules/settings/monitoring/monitors-api'
import Uniq from 'lodash/uniq'

export default {
  name: 'LooseTags',
  components: {
    SelectedItemPills,
  },
  model: {
    event: 'change',
  },
  props: {
    disabled: { type: Boolean, default: false },
    singleSelection: { type: Boolean, default: false },
    value: {
      type: Array,
      default() {
        return []
      },
    },
    // eslint-disable-next-line
    sm: { type: Boolean, default: true },
    asDropdown: { type: Boolean, default: false },
    counter: {
      type: Object,
      default: undefined,
    },
    userTagOnly: {
      type: Boolean,
      default: false,
    },
    tagType: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      tagOptions: [],
      loading: false,
    }
  },
  computed: {
    size() {
      if (this.sm) {
        return 'small'
      }
      return undefined
    },
  },
  watch: {
    counter(newValue, oldValue) {
      this.getAvailableTags()
    },
  },
  created() {
    if (!this.disabled) {
      this.getAvailableTags()
    }
  },
  methods: {
    handleChange(event) {
      if (this.asDropdown) {
        this.$emit(
          'change',
          (event || []).filter((i) => Trim(i))
        )
      } else {
        this.$emit(
          'change',
          Uniq((event || []).map((i) => Trim(i.toLowerCase())))
        )
      }
    },
    getAvailableTags() {
      getAllTagsApi(
        false,
        this.counter?.key,
        this.tagType,
        this.userTagOnly
      ).then((data) => {
        this.tagOptions = data.map((d) => ({
          ...d,
          ...(this.asDropdown
            ? { key: d.tag, text: d.tag }
            : { label: d.tag, value: d.tag }),
        }))
      })
    },
  },
}
</script>
