<template>
  <GroupProvider>
    <FlotoContentLoader :loading="loading">
      <div class="flex flex-col flex-1 min-h-0 min-w-0">
        <MRow :gutter="0">
          <MCol>
            <MInput
              v-if="!disableSearching"
              id="assign-monitor-search"
              v-model="searchTerm"
              auto-focus
              class="search-box dropdown-search"
              placeholder="Search"
            >
              <template v-slot:prefix>
                <MIcon name="search" />
              </template>
              <template v-if="searchTerm" v-slot:suffix>
                <MIcon
                  name="times-circle"
                  class="text-neutral-light cursor-pointer"
                  @click="searchTerm = undefined"
                />
              </template>
            </MInput>
            <template v-if="!disableSelection">
              <MBadge
                :count="value.length"
                class="mx-2 primary-bg"
                @click="showSelected = !showSelected"
              />
              <a
                v-if="value.length && listMode !== 'selected'"
                @click="listMode = 'selected'"
              >
                View Selected
              </a>
              <a v-if="listMode !== 'all'" @click="listMode = 'all'">
                View All
              </a>
              <span v-if="value.length && !disablePreSelectedItem" class="mx-1">
                |
              </span>
              <a
                v-if="value.length && !disablePreSelectedItem"
                @click="$emit('change', [])"
              >
                Clear Selected
              </a>
            </template>
          </MCol>
        </MRow>
        <VirtualTable
          :columns="columns"
          :search-term="searchTerm"
          :data="data"
          default-sort="-name"
          :selectable="!disableSelection"
          :disable-pre-selected-item="disablePreSelectedItem"
          :max-allowed-selection="maxAllowedSelection"
          :pre-selected-items="selectedItems"
          :filters="filters"
          v-bind="$attrs"
          @selection-change="$emit('change', $event)"
        >
          <template v-slot:name="{ item }">
            <div class="flex items-center">
              <Severity :object-id="item.id" disable-tooltip class="mr-2" />
              <span class="text-ellipsis">
                {{
                  item.category === $constants.SERVICE_CHECK
                    ? item.target
                    : item.name || item.target
                }}
              </span>
            </div>
          </template>
          <template v-slot:type="{ item }">
            <div class="flex min-w-0 overflow-hidden">
              <div class="mr-2">
                <MonitorType disable-tooltip :type="item.type" />
              </div>
              <div>
                <MonitorType
                  v-if="item.isAgent"
                  disable-tooltip
                  :type="$constants.AGENT"
                />
              </div>
            </div>
          </template>
          <template v-slot:groups="{ item }">
            <GroupPicker :value="item.groups" disabled />
          </template>
          <template v-slot:device="{ item }">
            <div class="flex items-center">
              <Severity
                :object-id="item.objectId"
                disable-tooltip
                class="mr-2"
              />
              <span class="text-ellipsis">
                {{ item.device }}
              </span>
            </div>
          </template>
          <template
            v-for="(_, name) in $scopedSlots"
            v-slot:[name]="nestedSlot"
          >
            <slot :name="name" v-bind="nestedSlot" />
          </template>
        </VirtualTable>
      </div>
    </FlotoContentLoader>
  </GroupProvider>
</template>

<script>
import Throttle from 'lodash/throttle'
import FindIndex from 'lodash/findIndex'
import Constants from '@constants'
import Bus from '@utils/emitter'
import { arrayWorker, objectDBWorker } from '@/src/workers'
import GroupProvider from '@components/data-provider/group-provider.vue'
import VirtualTable from '@components/crud/virtual-table.vue'
import Severity from '../severity.vue'
import MonitorType from '../monitor-type.vue'

export default {
  name: 'MonitorSelection',
  components: {
    GroupProvider,
    Severity,
    MonitorType,
    VirtualTable,
  },
  model: { event: 'change' },
  props: {
    defaultValue: { type: [Array], default: undefined },
    disablePreSelectedItem: { type: Boolean, default: false },
    maxAllowedSelection: { type: Number, default: 0 },
    fetchFn: { type: Function, default: undefined },
    defaultMode: { type: String, default: 'all' },
    filters: { type: [Array], default: undefined },
    category: { type: [Array], default: undefined },
    deviceType: { type: String, default: undefined },
    value: {
      type: [Array, String],
      default() {
        return []
      },
    },
    forNcmDevice: {
      type: Boolean,
      default: false,
    },
    disableSelection: {
      type: Boolean,
      default: false,
    },
    disableSearching: {
      type: Boolean,
      default: false,
    },
    appendColumns: {
      type: Array,
      default() {
        return []
      },
    },
  },
  data() {
    this.commanColumns = [
      {
        key: 'severity',
        name: 'severity',
        searchable: true,
        sortable: true,
        width: '0px',
      },
      {
        key: 'name',
        name: 'Monitor',
        searchable: true,
        sortable: true,
        cellRender: 'name',
      },
      {
        key: 'ip',
        name: 'IP',
        searchable: true,
        sortable: true,
        width: '120px',
      },
      {
        key: 'type',
        name: 'Type',
        searchable: true,
        sortable: true,
        width: '120px',
        cellRender: 'type',
      },
      {
        key: 'groups',
        name: 'Groups',
        contextKey: 'groupContext',
        searchable: true,
        sortable: true,
        searchKey: 'groupsDisplay',
        sortKey: 'groupsDisplay',
        cellRender: 'groups',
      },
    ]
    let mode = this.defaultMode
    if (this.maxAllowedSelection !== 1 && this.value.length) {
      mode = 'selected'
    }
    return {
      storedData: [],
      data: [],
      listMode: mode,
      loading: true,
      searchTerm: undefined,
    }
  },
  computed: {
    columns() {
      let col = []
      let region = [
        Constants.AMAZON_EC2,
        Constants.AWS_ELB,
        Constants.AMAZON_DYNAMO_DB,
        Constants.AMAZON_EBS,
        Constants.AMAZON_RDS,
        Constants.AMAZON_S3,
        Constants.AMAZON_SNS,
        Constants.AMAZON_CLOUD_FRONT,
        Constants.AWS_AUTO_SCALING,
        Constants.AWS_LAMBDA,
        Constants.AMAZON_SQS,
        Constants.AWS_ELASTIC_BEANSTALK,
        Constants.AMAZON_DOCUMENTDB,
      ]
      let resource = [
        Constants.AZURE_COSMOS_DB,
        Constants.AZURE_SQL_DATABASE,
        Constants.AZURE_STORAGE,
        Constants.AZURE_VM,
        Constants.AZURE_WEB_APP,
        Constants.AZURE_SERVICE_BUS,
        Constants.AZURE_APPLICATION_GATEWAY,
        Constants.AZURE_FUNCTION,
        Constants.AZURE_LOAD_BALANCER,
        Constants.AZURE_POSTGRESQL_SERVER,
        Constants.AZURE_MYSQL_SERVER,
        Constants.AZURE_VM_SCALE_SET,
        Constants.AZURE_CDN,
      ]

      if (region.indexOf(this.deviceType) !== -1) {
        col = [
          ...this.commanColumns,
          {
            key: 'region',
            name: 'Region',
            searchable: true,
            sortable: true,
            align: 'center',
          },
        ]
      } else if (resource.indexOf(this.deviceType) !== -1) {
        col = [
          ...this.commanColumns,
          {
            key: 'region',
            name: 'Resource',
            searchable: true,
            sortable: true,
            align: 'center',
          },
        ]
      } else if (this.forNcmDevice) {
        col = [
          {
            key: 'device',
            name: 'Device',
            searchable: true,
            sortable: true,
          },
          ...this.commanColumns.filter((c) => !['name'].includes(c.key)),
          {
            key: 'vendor',
            name: 'vendor',
            searchable: true,
            sortable: true,
          },
        ]
      } else {
        col = [...this.commanColumns]
      }
      return [...col, ...this.appendColumns]
    },
    selectedItems() {
      return this.defaultValue || this.value
    },
  },
  watch: {
    listMode() {
      this.buildData()
    },
    value() {
      this.buildData()
    },
    storedData() {
      this.buildData()
    },
  },
  created() {
    this.buildData = Throttle(this.buildDataRaw, 750)
    this.fetchMonitorOptions()
    Bus.$on(this.$constants.EVENT_SEVERITY_UPDATED, this.updateMonitorSeverity)
    this.$once('hook:beforeDestroy', () => {
      Bus.$off(
        this.$constants.EVENT_SEVERITY_UPDATED,
        this.updateMonitorSeverity
      )
    })
  },
  methods: {
    updateItem(item) {
      setTimeout(() => {
        const index = FindIndex(this.storedData, { id: item.id })
        if (index !== -1) {
          this.storedData = [
            ...this.storedData.slice(0, index),
            { ...this.storedData[index], ...item },
            ...this.storedData.slice(index + 1),
          ]
        }
      }, 0)
    },
    updateMonitorSeverity(monitorId, severity) {
      setTimeout(() => {
        const index = FindIndex(this.storedData, { id: monitorId })
        if (index !== -1) {
          this.storedData = [
            ...this.storedData.slice(0, index),
            { ...this.storedData[index], severity },
            ...this.storedData.slice(index + 1),
          ]
        }
      }, 0)
    },
    async buildDataRaw() {
      const listMode = this.listMode
      const value = this.selectedItems
      const data = this.storedData
      if (listMode === 'selected') {
        if (value.length === 0) {
          this.setListMode('all')
        }
        if (value.length > 0) {
          this.data = await arrayWorker.getItemsByIds(
            data,
            Array.isArray(this.value) ? this.value : [this.value]
          )
          return
        }
      } else if (this.listMode === 'unselected') {
        this.data = await arrayWorker.excludeFromList(data, this.value)
        return
      }
      this.data = data
    },
    setListMode(mode) {
      this.listMode = mode
    },
    fetchMonitorOptions() {
      let p = this.fetchFn
        ? this.fetchFn()
        : objectDBWorker.getObjects(
            this.category ? { category: this.category } : {}
          )
      return p.then((data) => {
        this.storedData = Object.freeze(data)
        this.loading = false
      })
    },
  },
}
</script>
