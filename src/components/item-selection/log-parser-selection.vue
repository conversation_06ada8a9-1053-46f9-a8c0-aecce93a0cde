<template>
  <div class="flex flex-col flex-1 min-h-0 min-w-0">
    <table v-if="selectedLogParser.length" class="item-list-table w-full">
      <thead>
        <tr>
          <td class="checkbox" style="width: 50px">
            <MCheckbox
              :checked="selectedLogParser.length === checkedItems.length"
              :disabled="disabled"
              @change="handleToggleAll"
            />
          </td>
          <td> Log Parser Name </td>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(field, index) in selectedLogParser" :key="index">
          <td class="m-5">
            <MCheckbox
              :checked="Boolean(isItemSelected(field.key))"
              :disabled="disabled"
              @change="toggleItem(field)"
            />
          </td>
          <td>
            {{ field.name }}
          </td>
        </tr>
      </tbody>
    </table>
    <FlotoNoData v-else />
  </div>
</template>

<script>
export default {
  name: 'LogParserSelection',
  inject: { logParserContext: { default: { options: new Map() } } },
  model: { event: 'change' },
  props: {
    selectedItems: { type: Array, required: true },
    disabled: { type: Boolean, default: false },
    checkedItems: { type: Array, required: true },
  },
  computed: {
    selectedLogParser() {
      const logParserMap = this.logParserContext.options
      return this.selectedItems
        .map((id) => logParserMap.get(id))
        .filter(Boolean)
    },
  },
  methods: {
    toggleItem(field) {
      const selectedField = this.isItemSelected(field.key)
      if (selectedField) {
        this.$emit('remove-fields', [field.key])
      } else {
        this.$emit('add-fields', [field.key])
      }
    },
    handleToggleAll(isChecked) {
      if (isChecked) {
        this.$emit('add-fields', this.selectedItems)
      } else {
        this.$emit('remove-fields', this.checkedItems)
      }
    },
    isItemSelected(key) {
      if (this.checkedItems !== undefined) {
        return this.checkedItems.find((f) => f === key)
      }
    },
  },
}
</script>
