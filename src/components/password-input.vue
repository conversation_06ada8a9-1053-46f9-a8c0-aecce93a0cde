<template>
  <!-- <MPopover
    :open="isPolicyPopoverOpen"
    :placement="policyPopoverPosition"
    overlay-class-name="-mr-6"
    :get-popup-container="getPopupContainer"
  >
  <template v-slot:trigger>-->
  <FlotoFormItem
    v-model="innerValue"
    validation-label="Password"
    :type="type"
    autocomplete="off"
    v-bind="attrs"
    @focus="isFocused = true"
    @blur="handleInputBlur"
  >
    <template v-for="(_, name) in $scopedSlots" v-slot:[name]="nestedSlot">
      <slot :name="name" v-bind="nestedSlot" />
    </template>
    <template v-slot:suffix>
      <MIcon
        :name="`${type === 'password' ? 'eye' : 'eye-slash'}`"
        class="cursor-pointer text-neutral-light"
        @click="toggleType"
      />
    </template>
  </FlotoFormItem>
  <!-- </template>
    <ul v-if="passwordPolicyContext.policies" class="policy-rules-list">
      <li
        v-for="policy in Object.keys(passwordPolicyContext.policies)"
        :key="policy"
      >
        <MIcon
          :name="invalidPolicies.indexOf(policy) >= 0 ? 'times' : 'check'"
          :class="{
            'text-secondary-red': invalidPolicies.indexOf(policy) >= 0,
            'text-secondary-green': invalidPolicies.indexOf(policy) === -1,
          }"
          class="mr-2"
          size="lg"
        />
        <span>
          {{ getPolicyText(policy) }}
        </span>
      </li>
    </ul>
  </MPopover>-->
</template>

<script>
import { validatePasswordWithPolicies } from '@src/validations'

export default {
  name: 'PasswordInput',
  inject: { passwordPolicyContext: { default: { policies: null } } },
  inheritAttrs: false,
  model: { event: 'update' },
  props: {
    value: { type: String, default: undefined },
    usePolicy: { type: Boolean, default: false },
    policyPopoverPosition: { type: String, default: 'left' },
    getPopupContainer: { type: Function, default: undefined },
  },
  data() {
    return {
      type: 'text',
      isFocused: false,
    }
  },
  computed: {
    isPolicyPopoverOpen() {
      return this.usePolicy && this.isFocused && this.passwordPolicy !== null
    },
    attrs() {
      return this.$attrs
    },
    innerValue: {
      get() {
        return this.value
      },
      set(v) {
        this.$emit('update', v)
      },
    },
    listeners() {
      const { change, input, update, ...listeners } = this.$listeners
      return listeners
    },
    invalidPolicies() {
      if (this.usePolicy) {
        const value = this.innerValue || ''
        return validatePasswordWithPolicies(value, {
          policies: this.passwordPolicyContext.policies,
          getInvalidPolicies: true,
        })
      }
      return ''
    },
  },
  mounted() {
    setTimeout(() => {
      this.type = 'password'
    }, 600)
  },
  methods: {
    handleInputBlur() {
      this.isFocused = false
    },
    getPolicyText(policy) {
      let extra = ''
      if (
        policy === 'passwordPolicyPasswordMinimumLength' &&
        this.passwordPolicyContext.policies
      ) {
        extra =
          this.passwordPolicyContext.policies
            .passwordPolicyPasswordMinimumLength
      }
      return (
        `${
          this.passwordPolicyContext.policyTextMap
            ? this.passwordPolicyContext.policyTextMap[policy]
            : ''
        }` + extra
      )
    },
    toggleType() {
      if (this.type === 'text') {
        this.type = 'password'
      } else {
        this.type = 'text'
      }
    },
  },
}
</script>

<style lang="less" scoped>
.policy-rules-list {
  padding: 0;
  margin: 0;

  li {
    list-style: none;

    @apply py-2;
  }
}
</style>
