<template>
  <MTag
    v-if="status"
    :closable="false"
    rounded
    class="cursor-auto inline-flex items-center"
    :class="tagType"
  >
    {{ tagText }}
  </MTag>
</template>

<script>
import Capitalize from 'lodash/capitalize'

const TAG_MAP = {
  off: 'tag-red',
  on: 'tag-green',
  poweredoff: 'tag-red',
  poweredon: 'tag-green',
  success: 'tag-green',
  error: 'tag-red',
  up: 'tag-green',
  active: 'tag-green',
  down: 'tag-red',
  inactive: 'tag-red',
  unknown: 'tag-unknown',
  completed: 'tag-green',
  halted: 'tag-yellow',
  paused: 'tag-yellow',
  succeed: 'tag-green',
  succeeded: 'tag-green',
  failed: 'tag-red',
  fail: 'tag-red',
  running: 'tag-green',
  queued: 'tag-yellow',
  abort: 'tag-yellow',
  aborted: 'tag-yellow',
  suspended: 'tag-orange',
  stopped: 'tag-red',
  yes: 'tag-green',
  no: 'tag-red',
  starting: 'tag-yellow',
  enable: 'tag-green',
  disable: 'tag-red',
  maintenance: 'tag-primary',
  unreachable: 'tag-purple',
  suspend: 'tag-orange',
  good: 'tag-green',
  poor: 'tag-red',
  fair: 'tag-orange',
  online: 'tag-green',
  offline: 'tag-red',
  available: 'tag-green',
  connected: 'tag-primary',
  standby: 'tag-primary',
  powering: 'tag-primary',
  disconnected: 'tag-red',
  connecting: 'tag-green',
  ready: 'tag-green',
  assign: 'tag-green',
  notready: 'tag-red',
  'not connected': 'tag-red',
  ok: 'tag-green',
  normal: 'tag-green',
  healthy: 'tag-green',
  optimal: 'tag-green',
  stable: 'tag-green',
  operational: 'tag-green',
  'in range': 'tag-green',
  nominal: 'tag-green',
  critical: 'tag-red',
  shutdown: 'tag-red',
  failure: 'tag-red',
  alarmed: 'tag-red',
  fault: 'tag-red',
  overload: 'tag-red',
  unavailable: 'tag-red',
  overheat: 'tag-red',
  remove: 'tag-red',
  present: 'tag-green',
  okay: 'tag-green',
  enabled: 'tag-green',
  functioning: 'tag-green',
  'not present': 'tag-red',
  absent: 'tag-red',
  unsupported: 'tag-red',
  unresponsive: 'tag-red',
  stalled: 'tag-red',
}

const textMap = {
  poweredoff: 'up',
  poweredon: 'down',
}

export default {
  name: 'MStatusTag',
  props: {
    status: { type: [String, Number], default: undefined },
    forcePrimary: { type: Boolean, default: false },
  },
  computed: {
    tagType() {
      return this.forcePrimary
        ? 'tag-primary'
        : TAG_MAP[String(this.status || '').toLowerCase()]
    },
    tagText() {
      if (textMap[String(this.status || '').toLowerCase()]) {
        return Capitalize(textMap[String(this.status || '').toLowerCase()])
      }
      return Capitalize(String(this.status || ''))
    },
  },
}
</script>
