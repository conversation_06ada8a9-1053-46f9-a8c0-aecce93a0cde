<template>
  <Capture v-if="hiddenWidgets.length !== children.length" :widget="widget">
    <template v-slot="{ capture }">
      <WidgetLayout
        v-bind="$attrs"
        :widget="widget"
        :guid="guid"
        :title="title"
        :hide-actions="hideActions"
        :excluded-actions="excludedActions"
        v-on="$listeners"
        @share="capture"
      >
        <MRow v-if="children" class="flex-1 min-h-0 items-center" :gutter="0">
          <template v-for="(child, index) in children">
            <MCol
              v-if="hiddenWidgets.includes(child.id) === false"
              :key="child.i"
              class="flex min-h-0"
              :class="{ 'px-4': child.w < 12 }"
              :style="{ height: `${(child.h * 100) / 12}%` }"
              :size="child.w"
            >
              <Widget
                v-bind="$attrs"
                :v-on="listeners"
                :title="child.options ? child.options.title : undefined"
                :widget-id="child.id"
                :hide-timeline="index > 0"
                :hide-title="
                  child.options ? child.options.showTitle === false : false
                "
                :hide-actions="
                  hideActions ||
                  ('showWidgetAction' in (child.options || {}) &&
                    child.options.showWidgetAction === false)
                "
                @hide="handleHideWidget(child.id)"
                v-on="listeners"
              />
            </MCol>
          </template>
        </MRow>
      </WidgetLayout>
    </template>
  </Capture>
</template>

<script>
import Uniq from 'lodash/uniq'
import { generateId } from '@utils/id'
import Capture from '@components/chart/capture.vue'
import WidgetLayout from './views/widget-layout.vue'
import Widget from './widget.vue'

export default {
  name: 'WidgetGroup',
  components: {
    Widget,
    WidgetLayout,
    Capture,
  },
  inheritAttrs: false,
  props: {
    children: {
      type: Array,
      default: undefined,
    },
    dashlet: {
      type: Object,
      required: true,
    },
    hideActions: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      guid: generateId(),
      hiddenWidgets: [],
    }
  },
  computed: {
    excludedActions() {
      return ['edit', 'clone']
    },
    widget() {
      return {}
    },
    title() {
      return this.dashlet.options && this.dashlet.options.title
    },
    listeners() {
      const { hide, ...listeners } = this.$listeners
      return listeners
    },
  },
  methods: {
    handleHideWidget(id) {
      this.hiddenWidgets = Uniq([...this.hiddenWidgets, id])
      if (this.hiddenWidgets.length === this.children.length) {
        this.$emit('hide')
      }
    },
  },
}
</script>
