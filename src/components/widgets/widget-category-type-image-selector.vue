<template>
  <div class="image-container">
    <component :is="component" />
  </div>
</template>

<script>
import { WidgetTypeConstants } from '@components/widgets/constants'
import { UserPreferenceComputed } from '@state/modules/user-preference'

import ChartDark from '@assets/images/widget-category-type/chart_dark.svg'
import ChartLight from '@assets/images/widget-category-type/chart_light.svg'

import TopnDark from '@assets/images/widget-category-type/topn_dark.svg'
import TopnLight from '@assets/images/widget-category-type/topn_light.svg'

import GaugeDark from '@assets/images/widget-category-type/gauge_dark.svg'
import GaugeLight from '@assets/images/widget-category-type/gauge_light.svg'

import GridDark from '@assets/images/widget-category-type/grid_dark.svg'
import GridLight from '@assets/images/widget-category-type/grid_light.svg'

import PieDark from '@assets/images/widget-category-type/pie_dark.svg'
import PieLight from '@assets/images/widget-category-type/pie_light.svg'

import MetroTileCountDark from '@assets/images/widget-category-type/metro_tile_count_dark.svg'
import MetroTileCountLight from '@assets/images/widget-category-type/metro_tile_count_light.svg'

import KeyValueDark from '@assets/images/widget-category-type/key_value_dark.svg'
import KeyValueLight from '@assets/images/widget-category-type/key_value_light.svg'

import SankeyDark from '@assets/images/widget-category-type/sankey_dark.svg'
import SankeyLight from '@assets/images/widget-category-type/sankey_light.svg'

import HeatmapDark from '@assets/images/widget-category-type/heatmap_dark.svg'
import HeatmapLight from '@assets/images/widget-category-type/heatmap_light.svg'

import StreamDark from '@assets/images/widget-category-type/stream_dark.svg'
import StreamLight from '@assets/images/widget-category-type/stream_light.svg'

import ActiveAlertDark from '@assets/images/widget-category-type/active_alert_dark.svg'
import ActiveAlertLight from '@assets/images/widget-category-type/active_alert_light.svg'

import TreemapDark from '@assets/images/widget-category-type/treemap_dark.svg'
import TreemapLight from '@assets/images/widget-category-type/treemap_light.svg'

import MapDark from '@assets/images/widget-category-type/map_dark.svg'
import MapLight from '@assets/images/widget-category-type/map_light.svg'

import AnomalyDark from '@assets/images/widget-category-type/anomaly_dark.svg'
import AnomalyLight from '@assets/images/widget-category-type/anomaly_light.svg'

import ForecastDark from '@assets/images/widget-category-type/forecast_dark.svg'
import ForecastLight from '@assets/images/widget-category-type/forecast_light.svg'

import FreeTextDark from '@assets/images/widget-category-type/free_text_dark.svg'
import FreeTextLight from '@assets/images/widget-category-type/free_text_light.svg'

export default {
  name: 'WidgetCategoryTypeImageSelector',

  components: {
    // InventoryDark,
    // InventoryLight
  },

  props: {
    type: {
      type: String,
      required: true,
    },
  },

  data() {
    this.dark = {
      [WidgetTypeConstants.CHART]: ChartDark,
      [WidgetTypeConstants.TOPN]: TopnDark,
      [WidgetTypeConstants.GAUGE]: GaugeDark,
      [WidgetTypeConstants.GRID]: GridDark,
      [WidgetTypeConstants.PIE]: PieDark,
      [WidgetTypeConstants.METRO_TILE]: MetroTileCountDark,
      [WidgetTypeConstants.KEY_VALUE_LAYOUT]: KeyValueDark,
      [WidgetTypeConstants.SANKEY]: SankeyDark,
      [WidgetTypeConstants.HEATMAP]: HeatmapDark,
      [WidgetTypeConstants.STREAM]: StreamDark,
      [WidgetTypeConstants.ACTIVE_ALERT]: ActiveAlertDark,
      [WidgetTypeConstants.TREE_VIEW]: TreemapDark,
      [WidgetTypeConstants.MAP_VIEW]: MapDark,
      [WidgetTypeConstants.ANOMALY]: AnomalyDark,
      [WidgetTypeConstants.FORECAST]: ForecastDark,
      [WidgetTypeConstants.EVENT_HISTORY]: ActiveAlertDark,
      [WidgetTypeConstants.FREE_TEXT]: FreeTextDark,
    }
    this.light = {
      [WidgetTypeConstants.CHART]: ChartLight,
      [WidgetTypeConstants.TOPN]: TopnLight,
      [WidgetTypeConstants.GAUGE]: GaugeLight,
      [WidgetTypeConstants.GRID]: GridLight,
      [WidgetTypeConstants.PIE]: PieLight,
      [WidgetTypeConstants.METRO_TILE]: MetroTileCountLight,
      [WidgetTypeConstants.KEY_VALUE_LAYOUT]: KeyValueLight,
      [WidgetTypeConstants.SANKEY]: SankeyLight,
      [WidgetTypeConstants.HEATMAP]: HeatmapLight,
      [WidgetTypeConstants.STREAM]: StreamLight,
      [WidgetTypeConstants.ACTIVE_ALERT]: ActiveAlertLight,
      [WidgetTypeConstants.TREE_VIEW]: TreemapLight,
      [WidgetTypeConstants.MAP_VIEW]: MapLight,
      [WidgetTypeConstants.ANOMALY]: AnomalyLight,
      [WidgetTypeConstants.FORECAST]: ForecastLight,
      [WidgetTypeConstants.EVENT_HISTORY]: ActiveAlertLight,
      [WidgetTypeConstants.FREE_TEXT]: FreeTextLight,
    }

    return {}
  },

  computed: {
    ...UserPreferenceComputed,

    component() {
      return this.theme === 'black'
        ? this.dark[this.type]
        : this.light[this.type]
    },
  },

  methods: {},
}
</script>
