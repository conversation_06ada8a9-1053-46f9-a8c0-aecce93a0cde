<template>
  <MonitorTypeProvider ignore-available-filter :device-types="deviceTypes">
    <MRow class="items-center" :gutter="rowGutter">
      <MCol :size="size.type">
        <FlotoFormItem
          v-if="!disableTypeSelector"
          :label="
            showLabel
              ? 'Source Filter'
              : labelsProps
              ? labelsProps.entityTypeLabel
              : undefined
          "
          :class="{ 'disabled-bordered-dropdown': disabledSourceFilter }"
          :rules="isSourceSeletionRequired ? { required: true } : {}"
        >
          <FlotoDropdownPicker
            id="entity"
            v-model="entityType"
            :options="availableEntityOptions"
            :searchable="false"
            :disabled="disabled || disabledSourceFilter"
            :allow-clear="true"
            placeholder="Everywhere"
          />
        </FlotoFormItem>
      </MCol>
      <MCol v-if="selectedOption.inputType === 'monitor'" :size="size.value">
        <FlotoFormItem
          :label="
            showLabel
              ? 'Source'
              : labelsProps
              ? labelsProps.entitiesLabel
              : undefined
          "
          :rules="isSourceSeletionRequired ? { required: true } : {}"
        >
          <NcmMonitorPicker
            v-if="useNcmMonitorPicker"
            v-model="entities"
            class="mt-1"
            placeholder=" "
            :disabled="disabled"
            :class="{ divider: disabled }"
            :multiple="multipeEntity"
            is-grid-selector
          />
          <MonitorPicker
            v-else
            v-model="entities"
            placeholder=" "
            :disabled="disabled"
            :class="{ divider: disabled }"
            :multiple="multipeEntity"
          />
        </FlotoFormItem>
      </MCol>
      <MCol v-else-if="selectedOption.inputType === 'group'" :size="size.value">
        <FlotoFormItem
          :label="
            showLabel
              ? 'Source'
              : labelsProps
              ? labelsProps.entitiesLabel
              : undefined
          "
          :rules="isSourceSeletionRequired ? { required: true } : {}"
        >
          <GroupPicker
            v-model="entities"
            placeholder=" "
            :disabled="disabled"
            :class="{ divider: disabled }"
            multiple
          />
        </FlotoFormItem>
      </MCol>
      <MCol
        v-else-if="selectedOption.inputType === 'source'"
        :size="size.value"
      >
        <FlotoFormItem
          :label="
            showLabel
              ? 'Source'
              : labelsProps
              ? labelsProps.entitiesLabel
              : undefined
          "
          :rules="
            sourceType === 'trap' || isSourceSeletionRequired
              ? { required: true }
              : {}
          "
          :class="{ 'disabled-bordered-dropdown': disabled }"
        >
          <FlotoDropdownPicker
            v-model="entities"
            placeholder=" "
            :disabled="disabled"
            :class="{ divider: disabled }"
            :multiple="multipleSourceSelection"
            as-input
            :options="sourceOptions"
          />
        </FlotoFormItem>
      </MCol>
      <MCol
        v-else-if="selectedOption.inputType === 'source.plugin'"
        :size="size.value"
      >
        <FlotoFormItem
          :label="
            showLabel
              ? 'Source'
              : labelsProps
              ? labelsProps.entitiesLabel
              : undefined
          "
          :rules="isSourceSeletionRequired ? { required: true } : {}"
        >
          <FlotoDropdownPicker
            v-model="entities"
            placeholder=" "
            :disabled="disabled"
            :class="{ divider: disabled }"
            as-input
            multiple
            :options="logPluginTypeOptions"
          />
        </FlotoFormItem>
      </MCol>
      <MCol
        v-else-if="selectedOption.inputType === 'source.type'"
        :size="size.value"
      >
        <FlotoFormItem
          :label="
            showLabel
              ? 'Source'
              : labelsProps
              ? labelsProps.entitiesLabel
              : undefined
          "
          :rules="isSourceSeletionRequired ? { required: true } : {}"
        >
          <MonitorTypePicker
            v-model="entities"
            :disabled="disabled"
            :class="{ divider: disabled }"
            multiple
            as-input
            placeholder=" "
          />
        </FlotoFormItem>
      </MCol>
      <MCol v-else-if="selectedOption.inputType === 'tags'" :size="size.value">
        <FlotoFormItem
          :label="
            showLabel
              ? 'Source'
              : labelsProps
              ? labelsProps.entitiesLabel
              : undefined
          "
          :rules="isSourceSeletionRequired ? { required: true } : {}"
        >
          <LooseTags
            v-model="entities"
            :disabled="disabled"
            :class="{ divider: disabled }"
            class="w-full"
            placeholder=" "
            :counter="counter"
            as-dropdown
          />
        </FlotoFormItem>
      </MCol>
      <MCol v-else :size="size.value">
        <FlotoFormItem
          :label="
            showLabel
              ? 'Source'
              : labelsProps
              ? labelsProps.entitiesLabel
              : undefined
          "
          :class="{ 'disabled-bordered-dropdown': disabled || !entityType }"
          :rules="isSourceSeletionRequired ? { required: true } : {}"
        >
          <FlotoDropdownPicker
            placeholder=" "
            :options="[]"
            :disabled="disabled || !entityType"
          />
        </FlotoFormItem>
      </MCol>
    </MRow>
  </MonitorTypeProvider>
</template>

<script>
import MonitorTypePicker from '@components/data-picker/monitor-type-picker.vue'
import LooseTags from '@components/loose-tags.vue'
import MonitorTypeProvider from '@components/data-provider/monitor-type-provider'
import MonitorPicker from '@components/data-picker/monitor-picker.vue'
import { getEventCategoriesApi, getSourceApi } from './widgets-api'
import { AvailableReportCategories } from '@modules/report/helpers/report'
import NcmMonitorPicker from '@components/data-picker/ncm-monitor-picker.vue'

export default {
  name: 'MonitorOrGroupSelection',
  components: {
    MonitorPicker,
    MonitorTypePicker,
    MonitorTypeProvider,
    LooseTags,
    NcmMonitorPicker,
  },
  model: { event: 'change' },
  props: {
    // eslint-disable-next-line
    multipeEntity: { type: Boolean, default: true },
    // eslint-disable-next-line
    multipleSourceSelection: { type: Boolean, default: true },
    disableTypeSelector: {
      type: Boolean,
      default: false,
    },
    value: {
      type: Object,
      default() {
        return undefined
      },
    },
    entityOptions: {
      type: Array,
      default() {
        return [
          { key: 'Monitor', text: 'Monitor', inputType: 'monitor' },
          { key: 'Group', text: 'Group', inputType: 'group' },
          { key: 'Tag', text: 'Tag', inputType: 'tags' },
        ]
      },
    },
    excludedEntityTypeOptions: {
      type: Array,
      default() {
        return []
      },
    },
    disabled: { type: Boolean, default: false },
    sourceType: { type: String, default: 'flow' },
    size: {
      type: Object,
      default() {
        return {
          type: 6,
          value: 6,
        }
      },
    },
    rowGutter: {
      type: Number,
      default: 32,
    },
    timeline: {
      type: Object,
      default: undefined,
    },
    showLabel: {
      type: Boolean,
      default: false,
    },
    labelsProps: {
      type: Object,
      default: undefined,
    },
    disabledSourceFilter: {
      type: Boolean,
      default: false,
    },
    counter: {
      type: Object,
      default: undefined,
    },
    showArchivedMonitors: {
      type: Boolean,
      default: false,
    },
    sourceRequired: {
      type: Boolean,
      default: false,
    },
    useNcmMonitorPicker: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      sourceOptions: [],
      logPluginTypeOptions: [],
    }
  },
  computed: {
    availableEntityOptions() {
      const excludedEntityTypes = this.excludedEntityTypeOptions.map((s) =>
        s.toLowerCase()
      )
      return this.entityOptions.filter(
        (i) => excludedEntityTypes.includes(i.key.toLowerCase()) === false
      )
    },
    deviceTypes() {
      return [
        this.$constants.SERVER,
        this.$constants.CLOUD,
        this.$constants.NETWORK,
        this.$constants.SDN,
        this.$constants.SERVICE_CHECK,
        this.$constants.VIRTUALIZATION,
        this.$constants.WIRELESS,
        this.$constants.OTHER,
        this.$constants.HYPERCONVERGED_INFRASTRUCTURE,

        this.$constants.STORAGE,
      ]
    },
    selectedOption() {
      const entity = this.entityType
      const type = this.entityOptions.find((o) => o.key === entity)
      return type || {}
    },
    entityType: {
      get() {
        return (this.value || {}).entityType
      },
      set(entityType) {
        this.$emit('change', {
          ...(this.value || {}),
          entityType,
          entities: [],
        })
      },
    },
    entities: {
      get() {
        return (this.value || {}).entities
      },
      set(entities) {
        this.$emit('change', { ...(this.value || {}), entities })
      },
    },

    isSourceSeletionRequired() {
      if (this.sourceRequired) {
        return this.sourceRequired
      }
      if (
        [
          AvailableReportCategories.AVAILABILITY,
          AvailableReportCategories.INVENTORY,
          AvailableReportCategories.METRIC_ALERT,
          AvailableReportCategories.AVAILABILITY_ALERT,
          AvailableReportCategories.AVAILABILITY_FLAP_SUMMARY,

          AvailableReportCategories.POLLING_REPORT,
        ].includes(this.$attrs['report-category'])
      ) {
        return true
      }
      return false
    },
  },
  watch: {
    showArchivedMonitors: {
      handler: function (newValue, oldValue) {
        if (newValue !== oldValue) {
          this.$emit('change', {
            ...this.value,
            entities: [],
            entityType: undefined,
          })
        }
      },
    },
  },
  created() {
    const options = this.entityOptions.map(({ key }) => key)
    if (options.includes('event.source')) {
      this.getSources()
    }
    if (options.includes('event.source.type')) {
      this.getLogPlugins()
    }
  },
  methods: {
    getLogPlugins() {
      getEventCategoriesApi(this.timeline).then(
        (data) =>
          (this.logPluginTypeOptions = Object.freeze(
            data.map((d) => ({
              key: d['event.category'],
              text: d['event.category'],
            }))
          ))
      )
    },
    getSources() {
      getSourceApi(this.sourceType).then(({ result }) => {
        this.sourceOptions = Object.freeze(
          Object.keys(result).map((i) => ({ key: i, text: result[i] }))
        )
        this.$emit('source-options', this.sourceOptions)
      })
    },
  },
}
</script>
