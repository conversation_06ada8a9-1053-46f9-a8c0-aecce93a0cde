<template>
  <div
    class="widget-group-form my-2 flex"
    :class="{ 'bg-neutral-lightest shadow': !disableBg, [value.type]: true }"
  >
    <div
      v-if="!hideRibbon"
      class="widget-name"
      :style="{ background: colorMap[value.type] }"
    >
      <span style="white-space: nowrap">
        {{ groupName }}
      </span>
    </div>
    <div
      class="flex-1 flex flex-col min-w-0 relative main-container"
      :class="{ 'px-2 py-3': !disablePadding }"
    >
      <MButton
        v-if="!disableRemoveGroup"
        size="small"
        class="remove-action-btn absolute"
        variant="neutral-lighter"
        :rounded="false"
        shape="circle"
        title="Remove"
        @click="$emit('remove')"
      >
        <MIcon name="times" />
      </MButton>
      <Component
        :is="groupComponent"
        v-model="innerValue"
        v-bind="props"
        :group-type="value.type"
        :widget-category="category"
        :max-allowed-counters="maxAllowedCounters"
        :widget-type="widgetType"
        :is-edit-mode="isEditMode"
        @change-widget-type="$emit('change-widget-type', $event)"
      />
    </div>
  </div>
</template>

<script>
import Capitalize from 'lodash/capitalize'
import { WidgetTypeConstants, AVAILABLE_GROUPS } from '../constants'
import { AvailableReportCategories } from '@modules/report/helpers/report'
import DefaultGroup from './default-group.vue'
import LogGroup from './log-group.vue'
import FlowGroup from './flow-group.vue'
import AlertGroup from './alert-group.vue'
import AiMetricGroup from './ai-metric-group.vue'
// import CustomGroup from './custom-group.vue'

export default {
  name: 'WidgetGroupForm',
  components: {
    DefaultGroup,
    LogGroup,
    FlowGroup,
    AlertGroup,
    AiMetricGroup,
  },
  inheritAttrs: false,
  model: { event: 'change' },
  props: {
    value: { type: Object, required: true },
    category: { type: String, required: true },
    widgetType: { type: String, default: undefined },
    hideRibbon: { type: Boolean, default: false },
    disableBg: { type: Boolean, default: false },
    disableRemoveGroup: { type: Boolean, default: false },
    disablePadding: { type: Boolean, default: false },
    isEditMode: {
      type: Boolean,
      default: false,
    },
    // isCustomGroup : {type: Boolean, default: false }
  },
  computed: {
    maxAllowedCounters() {
      if (
        [
          AvailableReportCategories.INVENTORY,
          AvailableReportCategories.AVAILABILITY,
          AvailableReportCategories.ACTIVE_ALERTS,
          AvailableReportCategories.AVAILABILITY_ALERT,
          AvailableReportCategories.METRIC_ALERT,
          AvailableReportCategories.POLLING_REPORT,
          AvailableReportCategories.AVAILABILITY_FLAP_SUMMARY,
        ].includes(this.$attrs['report-category'])
      ) {
        return 1
      }
      if (
        this.category === WidgetTypeConstants.HEATMAP ||
        this.category === WidgetTypeConstants.ACTIVE_ALERT
      ) {
        return 1
      }
      if (this.category === WidgetTypeConstants.GRID) {
        return 10
      }
      if (this.category === WidgetTypeConstants.GAUGE) {
        if (this.value.type === 'availability') {
          return undefined
        }
        return 1
      }
      return undefined
    },
    colorMap() {
      const map = {}
      AVAILABLE_GROUPS.forEach((g) => {
        if (g.key === 'policy') {
          map[g.key] = g.color
          map['policy.flap'] = g.color
          map['policy.stream'] = g.color
        } else {
          map[g.key] = g.color
        }
      })
      return map
    },
    groupName() {
      return ['policy.flap', 'policy', 'alert', 'policy.stream'].includes(
        this.value.type
      )
        ? 'Alert'
        : Capitalize(this.value.type)
    },
    props() {
      let additionalProps = {}
      // if (this.category !== WidgetTypeConstants.CHART) {
      if (this.value.type === 'metric') {
        additionalProps.useSameTypeOfCounters = true
      }
      // }
      return {
        ...additionalProps,
        ...this.$attrs,
      }
    },
    groupComponent() {
      if (
        this.category === WidgetTypeConstants.ANOMALY ||
        this.category === WidgetTypeConstants.FORECAST
      ) {
        return AiMetricGroup
      }
      if (this.value.type === 'log') {
        return LogGroup
      } else if (this.value.type === 'flow') {
        return FlowGroup
      } else if (
        this.value.type === 'alert' ||
        this.value.type === 'policy' ||
        this.value.type === 'policy.flap' ||
        this.value.type === 'policy.stream'
      ) {
        return AlertGroup
      }
      // if(this.isCustomGroup){

      //   return CustomGroup

      // }

      return DefaultGroup
    },
    innerValue: {
      get() {
        return this.value || {}
      },
      set(value) {
        this.$emit('change', value)
      },
    },
  },
}
</script>

<style lang="less" scoped>
.widget-group-form {
  min-height: 100px;
  border-radius: 4px;

  .main-container {
    padding-right: 10px;
  }

  .remove-action-btn {
    top: 10px;
    right: 10px;
    z-index: 2;
    cursor: pointer;
    opacity: 0;
    transition: transform opacity 0.5s linear;
    transform: translateY(-20px);
  }

  &:hover {
    .remove-action-btn {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .widget-name {
    width: 30px;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;

    span {
      color: white;
      transform: rotate(270deg);
    }

    @apply flex items-center justify-center;
  }
}
</style>
