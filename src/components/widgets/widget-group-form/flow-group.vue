<template>
  <MonitorProvider :search-params="searchParams">
    <GroupProvider>
      <CounterProvider
        type="Flow"
        :search-params="counterSearchParams"
        @counters="setCounters"
        @loaded="isCounterLoaded = true"
      >
        <div v-if="isCounterLoaded" class="flex flex-col flex-1">
          <MRow :gutter="16">
            <MCol :size="12">
              <FlotoFormItem rules="required">
                <MultipleFormItems
                  v-model="counters"
                  :item-template="itemTemplate"
                  :show-icon="false"
                  :max-items="maxAllowedCounters"
                  add-btn-text="Add Counter"
                >
                  <template
                    v-slot="{
                      item,
                      update,
                      remove,
                      index,
                      add,
                      isLastItem,
                      isFirstItem,
                    }"
                  >
                    <MRow class="items-center flex-no-wrap" :gutter="16">
                      <MCol :size="3">
                        <FlotoFormItem
                          rules="required"
                          :label="index === 0 ? 'Counter' : undefined"
                        >
                          <CounterPicker
                            :value="(item.counter || {}).key"
                            :data-type="counterDataType"
                            placeholder="Select Counter"
                            :disabled="disabled"
                            :disabled-options="disabledCounters"
                            @change="
                              update({ ...(item || {}), counter: $event })
                            "
                          />
                        </FlotoFormItem>
                      </MCol>
                      <MCol style="max-width: 210px">
                        <FlotoFormItem
                          rules="required"
                          :label="index === 0 ? 'Aggregation' : undefined"
                        >
                          <FlotoDropdownPicker
                            :value="item.aggrigateFn"
                            :searchable="false"
                            :options="
                              item.counter
                                ? (item.counter.dataType || []).length === 1
                                  ? aggrigateOptions[item.counter.dataType[0]]
                                  : aggrigateOptions.all
                                : []
                            "
                            placeholder=" "
                            @change="
                              update({ ...(item || {}), aggrigateFn: $event })
                            "
                          />
                        </FlotoFormItem>
                      </MCol>
                      <MCol
                        v-if="index === 0"
                        :size="shouldShowAddionalColumns ? 3 : 5"
                      >
                        <MonitorGroupSelection
                          v-model="target"
                          :disabled="disabled"
                          :entity-options="entityOptions"
                          :row-gutter="16"
                          :size="{ type: 4, value: 8 }"
                          show-label
                        />
                      </MCol>

                      <MCol v-if="shouldShowResultBy && index === 0" :size="2">
                        <FlotoFormItem label="Result By">
                          <FlotoDropdownPicker
                            id="resultBy"
                            v-model="resultBy"
                            :disabled-options="selectedCounters"
                            placeholder=" "
                            multiple
                            :options="resultByOptions"
                          />
                        </FlotoFormItem>
                      </MCol>

                      <MCol v-if="index === 0 && shouldShowAddionalColumns">
                        <AddiitionalColumnProvider
                          :report-category="$attrs['report-category']"
                          :search-params="{
                            counter: item.counter,
                          }"
                        >
                          <FlotoFormItem label="Additional Columns">
                            <AddiitionalColumnPicker
                              id="custom-monitoring-field"
                              v-model="additionalColumns"
                              multiple
                              :allow-clear="true"
                            />
                          </FlotoFormItem>
                        </AddiitionalColumnProvider>
                      </MCol>
                      <MCol
                        style="width: 80px"
                        class="fixed-size flex items-center"
                        :class="{ 'mb-2': index !== 0 }"
                      >
                        <span
                          v-if="!isFirstItem"
                          id="remove-counter"
                          class="mr-2 flex items-center"
                          @click="remove"
                        >
                          <MIcon
                            name="times-circle"
                            class="cursor-pointer text-secondary-red"
                            size="lg"
                          />
                        </span>
                        <span
                          v-if="isLastItem && canAddNewItem"
                          id="add-counter"
                          outline
                          class="flex items-center"
                          @click="add"
                        >
                          <MIcon
                            name="plus-circle"
                            class="text-primary cursor-pointer"
                            size="lg"
                          />
                        </span>
                      </MCol>
                    </MRow>
                  </template>
                </MultipleFormItems>
              </FlotoFormItem>
            </MCol>
            <MCol :size="12">
              <MRow :gutter="16" style="margin-right: 70px !important">
                <MCol :size="12" class="relative">
                  <FlotoFormItem>
                    <FiltersContainer
                      v-if="selectedCounters.length > 0"
                      v-model="filters"
                      group-type="flow"
                      :timeline="timeline"
                      :max-post-groups="1"
                      :max-pre-groups="3"
                      :all-counters="resultByOptions"
                      :selected-counters="availableCountersForConditions"
                      placeholder="Filters"
                    />
                  </FlotoFormItem>
                </MCol>
              </MRow>
            </MCol>
          </MRow>
        </div>
        <div v-else class="flex flex-col flex-1 items-center justify-center">
          <MLoader />
        </div>
      </CounterProvider>
    </GroupProvider>
  </MonitorProvider>
</template>

<script>
import IsEqual from 'lodash/isEqual'
import CloneDeep from 'lodash/cloneDeep'
import Uniq from 'lodash/uniq'
import Bus from '@utils/emitter'
import { generateId } from '@utils/id'

import MonitorProvider from '@components/data-provider/monitor-provider.vue'
import GroupProvider from '@components/data-provider/group-provider.vue'
import CounterProvider from '@components/data-provider/counter-provider.vue'
import CounterPicker from '@components/data-picker/counter-picker.vue'
import MultipleFormItems from '@components/multiple-form-items.vue'
import FiltersContainer from '@components/filters/filters-container.vue'
import {
  FILTER_CONDITION_DEFAULT_DATA,
  WidgetTypeConstants,
  AGGRIGATION_OPTIONS,
} from '../constants'
import MonitorGroupSelection from '../monitor-or-group-selection.vue'
import { getResultByOptionsApi } from '../widgets-api'
import AddiitionalColumnProvider from '@components/data-provider/additional-column-provider.vue'
import AddiitionalColumnPicker from '@components/data-picker/additional-column-picker.vue'
// import { AvailableReportCategories } from '@modules/report/helpers/report'

export default {
  name: 'FlowGroup',
  components: {
    MonitorProvider,
    CounterProvider,
    GroupProvider,
    CounterPicker,
    MultipleFormItems,
    MonitorGroupSelection,
    FiltersContainer,
    AddiitionalColumnProvider,
    AddiitionalColumnPicker,
  },
  model: { event: 'change' },
  props: {
    value: { type: Object, default: undefined },
    disabled: { type: Boolean, default: false },
    widgetCategory: { type: String, default: undefined },
    useSameTypeOfCounters: { type: Boolean, default: false },
    timeline: { type: Object, required: true },
    counterDataType: {
      type: Array,
      default: undefined,
    },
    widget: {
      type: Object,
      required: true,
    },
  },
  data() {
    this.flowPluginId = 500000
    this.entityOptions = [
      { key: 'event.source', text: 'Source Host', inputType: 'source' },
      { key: 'Group', text: 'Group', inputType: 'group' },
    ]
    this.itemTemplate = {}
    this.aggrigateOptions = CloneDeep({
      ...AGGRIGATION_OPTIONS,
      numeric: AGGRIGATION_OPTIONS.numeric
        .filter((i) => ['sum', 'avg'].includes(i.key))
        .concat([{ key: 'count', text: 'Count' }]),
      string: AGGRIGATION_OPTIONS.string.filter(({ key }) => key === 'count'),
      all: AGGRIGATION_OPTIONS.all
        .filter((i) => ['sum', 'avg'].includes(i.key))
        .concat([{ key: 'count', text: 'Count' }]),
    })
    // this.mapResultbyOptions = [
    //   'source.country',
    //   'destination.country',
    //   'source.city',
    //   'destination.city',
    // ]
    return {
      resultByOptionsWithPlugins: [],
      allCounters: [],
      resultByOptions: [],
      untouchedData: undefined,
      isCounterLoaded: false,
      defaultDisabledCounters: [],
    }
  },
  computed: {
    shouldShowResultBy() {
      if (this.widgetCategory === WidgetTypeConstants.CHART) {
        return this.selectedCounters.length <= 1
      } else if (this.widgetCategory === WidgetTypeConstants.GAUGE) {
        return false
      }
      return true
    },
    availableCountersForConditions() {
      const counters = this.counters
      return counters.map((c) => ({ ...c.counter, aggrigateFn: c.aggrigateFn }))
    },
    counterSearchParams() {
      return {
        'visualization.group.type': 'flow',
      }
    },
    selectedCounters() {
      const counters = this.counters
      return counters
        .map((c) => (c.counter || {}).parentId || (c.counter || {}).key)
        .filter(Boolean)
    },
    selectedPluginIds() {
      const selectedCounterNames = this.selectedCounters
      if (selectedCounterNames.length === 0) {
        return []
      }

      const filteredPluginIdBySelectedCounter =
        this.resultByOptionsWithPlugins.filter((c) =>
          selectedCounterNames.includes(c.key)
        )

      let pluginIdMap = []

      for (const i of filteredPluginIdBySelectedCounter) {
        pluginIdMap = pluginIdMap.concat(i.metricPlugins || [])
      }

      return pluginIdMap

      // this.resultByOptionsWithPlugins
      //   .filter((c) => selectedCounterNames.includes(c.key))
      //   .reduce((prev, i) => [...prev, ...(i.metricPlugins || [])], [])
    },
    searchParams() {
      return {
        category: [
          this.$constants.SERVER,
          this.$constants.NETWORK,
          this.$constants.OTHER,
          this.$constants.CLOUD,
          this.$constants.VIRTUALIZATION,
          this.$constants.SERVICE_CHECK,
          this.$constants.HYPERCONVERGED_INFRASTRUCTURE,
          this.$constants.SDN,
          this.$constants.STORAGE,
        ],
      }
    },
    target: {
      get() {
        return (this.value || {}).target
      },
      set(target) {
        this.$emit('change', {
          ...(this.value || {}),
          target,
        })
        this.$nextTick(() => {
          this.generateNewPreview()
        })
      },
    },
    resultBy: {
      get() {
        return (this.value || {}).resultBy
      },
      set(resultBy) {
        // Max 4 counter can be group by
        if (resultBy?.length > this.$constants.FLOW_MAX_RESULT_BY) {
          resultBy = resultBy.slice(
            resultBy.length - this.$constants.FLOW_MAX_RESULT_BY
          )
        }
        this.$emit('change', { ...(this.value || {}), resultBy })
        this.$nextTick(() => {
          this.generateNewPreview()
        })
      },
    },
    additionalColumns: {
      get() {
        return this.value.additionalColumns || []
      },
      set(additionalColumns) {
        this.$emit('change', { ...this.value, additionalColumns })
        this.$nextTick(this.generateNewPreview)
      },
    },
    counters: {
      get() {
        return (this.value || {}).counters
      },
      set(counters) {
        let data = counters.map((c) =>
          c.counter
            ? {
                ...c,
                ...(c.aggrigateFn
                  ? c.counter &&
                    c.counter.dataType &&
                    this.aggrigateOptions[
                      c.counter.dataType.length === 1
                        ? c.counter.dataType[0]
                        : 'all'
                    ].find(({ key }) => key === c.aggrigateFn)
                    ? {}
                    : {
                        aggrigateFn: (
                          (c.counter || {}).dataType || []
                        ).includes('numeric')
                          ? 'avg'
                          : c.aggrigateFn,
                      }
                  : {
                      aggrigateFn: ((c.counter || {}).dataType || []).includes(
                        'numeric'
                      )
                        ? 'avg'
                        : c.aggrigateFn,
                    }),
              }
            : c
        )
        const additionalData = this.getCounterChangedData(counters)
        this.$emit('change', {
          ...(this.value || {}),
          counters: data,
          ...additionalData,
        })

        let actualCounterLengthOfOldValue = this.value.counters.filter(
          (counterGroup) =>
            counterGroup &&
            counterGroup['counter'] &&
            counterGroup['counter']['counterName'] !== null
        )
        let actualCounterLengthOfNewValue = counters.filter(
          (counterGroup) =>
            counterGroup &&
            counterGroup['counter'] &&
            counterGroup['counter']['counterName'] !== null
        )

        if (
          counters.length === this.value.counters.length ||
          actualCounterLengthOfOldValue.length !==
            actualCounterLengthOfNewValue.length
        ) {
          this.$nextTick(() => {
            this.generateNewPreview()
          })
        }
      },
    },
    filters: {
      get() {
        return (this.value || {}).filters
      },
      set(filters) {
        this.$emit('change', { ...(this.value || {}), filters })
        this.$nextTick(() => {
          this.generateNewPreview()
        })
      },
    },
    canAddNewItem() {
      if (this.widgetCategory === WidgetTypeConstants.SANKEY) {
        return false
      }

      if (this.widget.widgetType === WidgetTypeConstants.TREE_VIEW) {
        return false
      }

      return true
    },
    shouldShowAddionalColumns() {
      return [
        // AvailableReportCategories.FLOW_ANALYTICS
      ].includes(this.$attrs['report-category'])
    },
    disabledCounters() {
      return Uniq(
        this.selectedCounters.concat(
          this.resultByOptions.map((o) => o.key),
          this.defaultDisabledCounters &&
            Array.isArray(this.defaultDisabledCounters)
            ? this.defaultDisabledCounters
            : []
        )
      )
    },
    maxAllowedCounters() {
      return this.widgetCategory === WidgetTypeConstants.TOPN &&
        this.widget.widgetType === WidgetTypeConstants.TREE_VIEW
        ? 1
        : undefined
    },
  },
  watch: {
    selectedPluginIds(newValue, oldValue) {
      if (!IsEqual(newValue, oldValue)) {
        this.getResultByOptions()
      }
    },
    widgetCategory(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.getResultByOptions()
        this.setCounters()
      }
    },
  },
  created() {
    this.generateNewPreview()
    this.getDefaultDesabledCounters()
  },
  beforeDestroy() {
    setTimeout(() => {
      this.generateNewPreview()
    })
  },
  methods: {
    getResultByOptions() {
      const pluginIds = this.selectedPluginIds
      if (pluginIds.length) {
        getResultByOptionsApi(pluginIds).then((data) => {
          this.untouchedData = data
          this.resultByOptions = Object.freeze(
            (this.widgetCategory === WidgetTypeConstants.MAP_VIEW
              ? data.filter((option) => /(city|country)$/.test(option))
              : data
            ).map((i) => ({ key: i, text: i }))
          )
          this.resultBy = this.resultBy?.filter((i) => data.includes(i))
        })
      } else {
        this.resultByOptions = []
      }
    },
    setCounters(optionsMap) {
      if (optionsMap && !Array.isArray(optionsMap)) {
        const optionsArr = Array.from(optionsMap.keys())
        this.resultByOptionsWithPlugins = Array.from(optionsMap.values())
        this.allCounters = optionsArr.map((o) => ({ text: o, key: o }))
      }

      if (
        this.value?.counters?.[0]?.counter &&
        !this.value.counters[0]?.counter?.key &&
        this.resultByOptionsWithPlugins?.length
      ) {
        const counter = this.resultByOptionsWithPlugins.filter(
          (c) => c.counterName === 'volume.bytes'
        )[0] || {
          key: 'volume.bytes',
          counterName: 'volume.bytes',
          name: 'volume.bytes',
          isStatusCounter: false,
        }

        this.$emit('change', {
          ...(this.value || {}),
          counters: [
            {
              key: generateId(),
              aggrigateFn: 'avg',
              counter,
            },
          ],
        })
      }
    },
    generateNewPreview() {
      // const nonCounterItem = (this.counters || []).filter(
      //   (c) => !(c.counter || {}).key
      // )
      if (this.widgetCategory === WidgetTypeConstants.SANKEY) {
        if (
          // nonCounterItem.length === 0 &&
          this.resultBy &&
          this.resultBy?.length > 1
        ) {
          Bus.$emit('widget.generate.preview')
        }
      } else {
        // if (nonCounterItem.length === 0) {
        Bus.$emit('widget.generate.preview')
        // }
      }
    },
    getCounterChangedData(counters) {
      let additionalData = {}
      // if chart or gauge then apply permutation
      const counterKeys = counters
        .map((c) => (c.counter || {}).key)
        .filter(Boolean)
      const selectedKeys = this.selectedCounters
      const updatedCounterLength = counterKeys.filter(
        (k) => selectedKeys.indexOf(k) === -1
      ).length
      if (updatedCounterLength) {
        additionalData.filters = {
          pre: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
          post: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
        }
      }
      if (!counters?.[0]?.counter) {
        additionalData.resultBy = []
      }
      if (
        [WidgetTypeConstants.CHART, WidgetTypeConstants.GAUGE].includes(
          this.widgetCategory
        ) &&
        counterKeys.length > 1
      ) {
        additionalData.resultBy = undefined
      }
      return additionalData
    },
    getDefaultDesabledCounters() {
      getResultByOptionsApi([this.flowPluginId]).then((data) => {
        this.defaultDisabledCounters = data
      })
    },
  },
}
</script>

<style lang="less" scoped>
.filter-viewer {
  background: var(--neutral-lightest);
  border: 1px solid var(--border-color);
}
</style>
