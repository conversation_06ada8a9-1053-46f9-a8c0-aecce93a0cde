<template>
  <div>
    <MRow class="items-center no-wrap" :gutter="16">
      <MCol :size="counterColSize">
        <FlotoFormItem
          rules="required"
          :label="showLabel ? counterLabel : undefined"
        >
          <CounterPicker
            :value="(counter || {}).key"
            :scalar-only="scalarOnly"
            :data-type="counterDataType"
            :instance-type="instanceType"
            :table-name="tableNameFilter"
            :ignore-status-filter="
              groupType === 'availability' || groupType === 'status.flap'
            "
            :root-counter-type="rootCounterType"
            :root-aggrigate-fn="rootAggrigateFn"
            :disabled-options="excludedCounters"
            placeholder="Select Counter"
            :disabled="disabled"
            :filter-fn="counterFilter"
            @change="
              update({
                ...(value || {}),
                counter: $event,
                ...($event && $event.key !== (counter || {}).key
                  ? { aggrigateFn: undefined, filterCounter: undefined }
                  : {}),
                ...(!$event
                  ? { aggrigateFn: undefined, filterCounter: undefined }
                  : {}),
              })
            "
          />

          <!-- :status-only-counters="
              reportCategory === AvailableReportCategories.AVAILABILITY
            " -->
        </FlotoFormItem>
      </MCol>

      <MCol v-if="showCounterFilter">
        <FlotoFormItem
          rules="required"
          :label="showLabel ? 'Counters' : undefined"
        >
          <FilterCounterPicker
            v-model="filterCounter"
            :searchable="false"
            :report-category="reportCategory"
            :selected-counter="counter"
          />
        </FlotoFormItem>
      </MCol>
      <MCol v-if="!hideAggrigation" style="max-width: 210px">
        <FlotoFormItem
          rules="required"
          :label="showLabel ? 'Aggregation' : undefined"
        >
          <FlotoDropdownPicker
            v-model="aggrigateFn"
            :searchable="false"
            placeholder=" "
            :disabled-options="disabledAggrigators"
            :options="validateAggrigateOptions()"
          />
        </FlotoFormItem>
      </MCol>
      <MCol
        v-if="canSelectMonitor"
        :size="
          monitorSelectionProps.disableTypeSelector
            ? 3
            : monitorSelectionProps.colSize || 5
        "
      >
        <MonitorGroupSelection
          v-model="target"
          :row-gutter="16"
          :disabled="disabled"
          v-bind="monitorSelectionProps"
        />
      </MCol>
      <MCol v-else :size="5" />

      <slot />
      <MCol
        v-if="shouldShowButtons"
        style="flex-shrink: 0; width: 80px"
        class="fixed-size flex items-center"
        :class="{ 'mb-2': itemIndex !== 0, 'mt-2': itemIndex === 0 }"
      >
        <span
          v-if="canRemove"
          id="remove-counter"
          class="mr-2 flex items-center"
          outline
          @click="$emit('remove')"
        >
          <MIcon
            name="times-circle"
            class="cursor-pointer text-secondary-red"
            size="lg"
          />
        </span>
        <span
          v-if="canAdd"
          id="add-counter"
          outline
          class="flex items-center"
          @click="$emit('add')"
        >
          <MIcon
            name="plus-circle"
            class="text-primary cursor-pointer"
            size="lg"
          />
        </span>
      </MCol>
    </MRow>
  </div>
</template>

<script>
import CounterPicker from '@components/data-picker/counter-picker.vue'
import FilterCounterPicker from '@components/data-picker/filter-counter-picker.vue'

import MonitorGroupSelection from '../monitor-or-group-selection.vue'
import { WidgetTypeConstants } from '../constants'
import { AvailableReportCategories } from '@modules/report/helpers/report'

export default {
  name: 'DefaultGroupItem',
  components: {
    CounterPicker,
    MonitorGroupSelection,
    FilterCounterPicker,
  },
  model: { event: 'change' },
  props: {
    groupType: { type: String, default: 'metric' },
    disabled: { type: Boolean, default: false },
    itemIndex: { type: Number, default: 0 },
    canSelectMonitor: { type: Boolean, default: false },
    forAiMl: { type: Boolean, default: false },
    counterDataType: {
      type: [String, Array],
      default() {
        return ['numeric']
      },
    },
    rootCounterType: {
      type: [String, Array],
      default: undefined,
    },
    rootAggrigateFn: {
      type: String,
      default: undefined,
    },
    instanceType: {
      type: String,
      default: undefined,
    },
    excludedCounters: {
      type: Array,
      default: undefined,
    },
    aggrigateOptions: {
      type: Array,
      default() {
        return []
      },
    },
    instance: { type: String, default: undefined },
    tableNameFilter: { type: String, default: undefined },
    canRemove: {
      type: Boolean,
      default: false,
    },
    canAdd: {
      type: Boolean,
      default: false,
    },
    scalarOnly: {
      type: Boolean,
      default: false,
    },
    value: {
      type: Object,
      default() {
        return {}
      },
    },
    allCounters: {
      type: Array,
      default() {
        return []
      },
    },
    monitorSelectionProps: {
      type: Object,
      default() {
        return {}
      },
    },
    widgetCategory: { type: String, default: undefined },

    hideAggrigationOptions: {
      type: Boolean,
      default: false,
    },
    reportCategory: {
      type: String,
      default: undefined,
    },
  },
  data() {
    this.MAX_FILTER_COUNTER_SELECTION = 8
    this.AvailableReportCategories = AvailableReportCategories
    return {}
  },
  computed: {
    disabledAggrigators() {
      const currentCounterName = this.counter && this.counter.counterName
      const key = this.value.key
      const items = this.allCounters.filter(
        (c) =>
          c.counter &&
          c.counter.counterName === currentCounterName &&
          c.key !== key
      )
      if (items) {
        return items.map((i) => i.aggrigateFn)
      }
      return []
    },
    counter() {
      return this.value.counter
    },
    target: {
      get() {
        return this.value.target || {}
      },
      set(target) {
        this.$emit('change', { ...this.value, target })
      },
    },
    filterCounter: {
      get() {
        return this.value.filterCounter || []
      },
      set(filterCounter) {
        if (filterCounter.length > this.MAX_FILTER_COUNTER_SELECTION) {
          filterCounter = filterCounter.slice(
            filterCounter.length - this.MAX_FILTER_COUNTER_SELECTION
          )
        }

        this.$emit('change', { ...this.value, filterCounter })
      },
    },
    status: {
      get() {
        return this.value.status || []
      },
      set(status) {
        this.$emit('change', { ...this.value, status })
      },
    },
    aggrigateFn: {
      get() {
        return this.value.aggrigateFn
      },
      set(aggrigateFn) {
        this.$emit('change', { ...this.value, aggrigateFn })
      },
    },
    showLabel() {
      return this.itemIndex === 0
    },
    isHeatmap() {
      return [
        WidgetTypeConstants.HEATMAP,
        WidgetTypeConstants.ACTIVE_ALERT,
      ].includes(this.widgetCategory)
    },
    hideAggrigation() {
      return (
        (this.groupType === 'availability' &&
          [
            WidgetTypeConstants.GAUGE,
            WidgetTypeConstants.HEATMAP,
            WidgetTypeConstants.ACTIVE_ALERT,
          ].includes(this.widgetCategory)) ||
        this.hideAggrigationOptions
      )
    },
    shouldShowButtons() {
      return ![
        AvailableReportCategories.INVENTORY,
        AvailableReportCategories.ACTIVE_ALERTS,
        AvailableReportCategories.AVAILABILITY_ALERT,
        AvailableReportCategories.AVAILABILITY,
        AvailableReportCategories.METRIC_ALERT,
        AvailableReportCategories.AVAILABILITY_FLAP_SUMMARY,
      ].includes(this.reportCategory)
    },
    showCounterFilter() {
      return [AvailableReportCategories.AVAILABILITY].includes(
        this.reportCategory
      )
    },

    counterLabel() {
      if (this.reportCategory === AvailableReportCategories.INVENTORY) {
        return 'Inventory By'
      } else if (this.isHeatmap && this.groupType === 'availability') {
        return 'Map By'
      } else if (
        this.reportCategory === AvailableReportCategories.AVAILABILITY
      ) {
        return 'Availability By'
      } else if (
        [
          AvailableReportCategories.ACTIVE_ALERTS,
          AvailableReportCategories.AVAILABILITY_ALERT,
          AvailableReportCategories.METRIC_ALERT,
          AvailableReportCategories.AVAILABILITY_FLAP_SUMMARY,
        ].includes(this.reportCategory)
      ) {
        return 'Alert By'
      } else {
        return 'Counter'
      }
    },

    counterFilter() {
      if (
        [
          AvailableReportCategories.AVAILABILITY,
          AvailableReportCategories.AVAILABILITY_ALERT,
          // AvailableReportCategories.AVAILABILITY_FLAP_SUMMARY,
        ].includes(this.reportCategory) ||
        (this.groupType === 'metric' &&
          this.widgetCategory === WidgetTypeConstants.HEATMAP)
      ) {
        return this.filterFn
      } else {
        return undefined
      }
    },
    counterColSize() {
      if (
        [AvailableReportCategories.AVAILABILITY].includes(this.reportCategory)
      ) {
        return 2
      } else {
        return 3
      }
    },
  },
  methods: {
    validateAggrigateOptions() {
      // const dataType = (this.counter || {}).dataType
      const dataType = this.firstCounterType
      if (
        dataType &&
        (dataType.includes('string') || dataType.includes('numeric')) &&
        this.firstAggrigateFn === 'last'
      ) {
        return this.aggrigateOptions.filter((a) => a.key === 'last')
      }
      return this.aggrigateOptions
    },
    update(payload) {
      setTimeout(() => {
        this.$emit('change', payload)
      }, this.itemIndex * 100)
    },

    filterFn(counterMap) {
      if (
        [
          AvailableReportCategories.AVAILABILITY,
          AvailableReportCategories.AVAILABILITY_ALERT,
          // AvailableReportCategories.AVAILABILITY_FLAP_SUMMARY,
        ].includes(this.reportCategory)
      ) {
        const computedMap = new Map()
        Array.from(counterMap.keys()).map((c) => {
          const counterKey =
            c.indexOf('~') >= 0 ? `${c.split('~')[0]}~status` : 'status'
          const counter = counterMap.get(c)
          counter.key = counterKey.replace('~status', '')
          counter.counterName = counterKey.replace('~status', '')
          counter.name = counterKey.replace('~status', '')

          computedMap.set(counterKey, counter)
        })
        return Object.freeze(
          Array.from(computedMap.values()).map((c) => ({
            ...c,
            key: c.key === 'status' ? 'monitor' : c.key.replace('.status', ''),
            text:
              c.key === 'status'
                ? 'monitor'
                : c.key.replace(/~/g, '.').replace('.status', ''),
          }))
        )
      } else if (
        this.groupType === 'metric' &&
        this.widgetCategory === WidgetTypeConstants.HEATMAP
      ) {
        const computedMap = new Map()
        Array.from(counterMap.keys()).map((c) => {
          if (c.includes('percent')) {
            const counter = counterMap.get(c)

            if (!counter.isStatusCounter) {
              computedMap.set(c, counter)
            }
          }
        })
        return Object.freeze(
          Array.from(computedMap.values()).map((c) => ({
            ...c,
          }))
        )
      }
    },
  },
}
</script>
