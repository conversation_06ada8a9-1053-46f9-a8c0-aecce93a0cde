<template>
  <MonitorProvider :search-params="searchParams">
    <GroupProvider>
      <CounterProvider
        :search-params="counterSearchParams"
        @loaded="isCounterLoaded = true"
        @counters="setCounters"
      >
        <div v-if="isCounterLoaded" class="flex flex-col flex-1">
          <MRow :gutter="16">
            <MCol :size="12">
              <FlotoFormItem rules="required">
                <MultipleFormItems
                  v-model="counters"
                  :max-items="1"
                  :item-template="itemTemplate"
                  :show-icon="false"
                  add-btn-text="Add Counter"
                >
                  <template
                    v-slot="{ item, update, remove, add, index, isFirstItem }"
                  >
                    <DefaultGroupItem
                      :value="item"
                      :group-type="groupType"
                      for-ai-ml
                      :all-counters="counters"
                      :item-index="index"
                      :can-select-monitor="canSelectMonitorWithEachCounter"
                      :disabled="disabled"
                      :instance-type="instanceType"
                      :counter-data-type="counterDataType"
                      :monitor-selection-props="{
                        multipeEntity: false,
                        disableTypeSelector: true,
                        size: {
                          type: 0,
                          value: 12,
                        },
                        'show-label': true,
                      }"
                      :excluded-counters="selectedCounters"
                      :aggrigate-options="
                        item.counter
                          ? (item.counter.dataType || []).length === 1
                            ? aggrigateOptions[item.counter.dataType[0]]
                            : aggrigateOptions.all
                          : []
                      "
                      :can-add="false"
                      :can-remove="!isFirstItem"
                      :instance="selectedInstance"
                      :scalar-only="
                        !isFirstItem &&
                        !selectedInstance &&
                        selectedCounters.length > 0
                      "
                      v-bind="metricItemAttrs"
                      @change="update"
                      @add="
                        canSelectMonitorWithEachCounter && counters.length > 0
                          ? add({ target: counters[0].target })
                          : add()
                      "
                      @remove="remove"
                    >
                      <MCol :size="3">
                        <FlotoFormItem
                          v-if="instanceOptions.length"
                          label="Instance"
                        >
                          <FlotoDropdownPicker
                            v-model="instance"
                            placeholder="Select Instance"
                            :options="instanceOptions"
                          />
                        </FlotoFormItem>
                      </MCol>
                    </DefaultGroupItem>
                  </template>
                </MultipleFormItems>
              </FlotoFormItem>
            </MCol>
          </MRow>
          <MRow class="mt-auto items-center" :gutter="16">
            <MCol :size="9" class="relative">
              <InstanceMetricProvider
                v-if="counters && counters.length && counters[0].counter"
                ref="instanceMetricProviderRef"
                :instance="counters[0].counter.instanceType"
                group-type="metric"
                :selected-counter="selectedCounters[0]"
                :target="counters[0].counter.instanceType && counters[0].target"
                :timeline="timeline"
                @data="handleInstanceDataReceived"
              />
              <!-- <FlotoFormItem>
                  <FiltersContainer
                    v-model="filters"
                    :max-post-groups="1"
                    :max-pre-groups="3"
                    :use-instance-grid="showInstanceSelector"
                    :selected-counters="availableCountersForConditions"
                    placeholder="Filters"
                  />
                </FlotoFormItem>
              </InstanceMetricProvider> -->
            </MCol>
          </MRow>
        </div>
        <div v-else class="flex flex-col flex-1 items-center justify-center">
          <MLoader />
        </div>
      </CounterProvider>
    </GroupProvider>
  </MonitorProvider>
</template>

<script>
// import Pick from 'lodash/pick'
import CloneDeep from 'lodash/cloneDeep'
import Bus from '@utils/emitter'
import { generateId } from '@utils/id'
import MonitorProvider from '@components/data-provider/monitor-provider.vue'
import GroupProvider from '@components/data-provider/group-provider.vue'
import CounterProvider from '@components/data-provider/counter-provider.vue'
import MultipleFormItems from '@components/multiple-form-items.vue'
import InstanceMetricProvider from '@components/data-provider/instance-metric-provider.vue'
import { getAllowedUnit } from '@utils/unit-checker'
import DefaultGroupItem from './default-group-item.vue'
import {
  WidgetTypeConstants,
  FILTER_CONDITION_DEFAULT_DATA,
} from '../constants'

export default {
  name: 'AiMetricGroup',
  components: {
    MonitorProvider,
    CounterProvider,
    GroupProvider,
    MultipleFormItems,
    InstanceMetricProvider,
    DefaultGroupItem,
  },
  model: { event: 'change' },
  props: {
    groupType: { type: String, default: 'metric' },
    value: { type: Object, default: undefined },
    widgetType: { type: String, default: undefined },
    widgetCategory: { type: String, default: undefined },
    disabled: { type: Boolean, default: false },
    useSameTypeOfCounters: { type: Boolean, default: false },
    counterDataType: { type: Array, default: undefined },
    widget: {
      type: Object,
      required: true,
    },
    timeline: {
      type: Object,
      required: true,
    },
  },

  data() {
    this.aggrigateOptions = CloneDeep({
      numeric: [{ key: 'avg', text: 'Avg' }],
      string: [{ key: 'avg', text: 'Avg' }],
      all: [{ key: 'avg', text: 'Avg' }],
    })
    return {
      instanceOptions: [],
      counterSearchParams: {
        'visualization.group.type': this.groupType,
        'visualization.category': this.widgetCategory,
      },
      isCounterLoaded: false,
    }
  },
  computed: {
    metricItemAttrs() {
      const counters = this.counters
      return {
        rootCounterType: counters.length
          ? (counters[0].counter || {}).dataType
          : undefined,
        rootAggrigateFn: counters.length
          ? (counters[0] || {}).aggrigateFn
          : undefined,
      }
    },
    instanceType() {
      const counters = this.counters
      let instance
      if (counters.length) {
        counters.map((e) => {
          if ((e.counter || {}).instanceType) {
            instance = e.counter.instanceType
          }
        })

        return instance
      }
      return undefined
    },
    selectedCounters() {
      const items = this.counters
      return items
        .filter(({ counter }) => counter)
        .map(({ counter }) => counter.key)
    },
    isAvailabilityWidget() {
      return this.groupType === 'availability'
    },
    canSelectMonitorWithEachCounter() {
      return true
    },
    itemTemplate() {
      return {}
    },
    selectedCounterType() {
      const counters = this.counters
      const firstCounter = counters.length ? counters[0] : undefined
      if (firstCounter && firstCounter.counter) {
        return firstCounter.counter.key.indexOf('~') >= 0
          ? 'instance'
          : 'metric'
      }
      return undefined
    },
    availableCountersForConditions() {
      const counters = this.counters
      return counters.map((c) => ({ ...c.counter, aggrigateFn: c.aggrigateFn }))
    },
    searchParams() {
      return {
        category: [
          this.$constants.SERVER,
          this.$constants.NETWORK,
          this.$constants.SDN,
          this.$constants.OTHER,
          this.$constants.CLOUD,
          this.$constants.VIRTUALIZATION,
          this.$constants.SERVICE_CHECK,
          this.$constants.HYPERCONVERGED_INFRASTRUCTURE,
          this.$constants.STORAGE,
        ],
      }
    },
    selectedInstance() {
      if (this.selectedCounterType === 'instance') {
        const counters = this.counters
        if (counters.length) {
          return (counters[0].counter || {}).instanceType
        }
      }
      return undefined
    },
    monitorSelectionInfo() {
      return ((this.value || {}).counters[0] || {}).target || []
    },
    resultBy: {
      get() {
        return this.value.resultBy
      },
      set(resultBy) {
        this.$emit('change', { ...this.value, resultBy })
        this.$nextTick(this.generateNewPreview)
      },
    },
    instance: {
      get() {
        return this.value.instance
      },
      set(instance) {
        const filters = {
          pre: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
          post: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
        }
        filters.pre.groups[0].conditions[0] = {
          ...filters.pre.groups[0].conditions[0],
          operator: '=',
          value: instance,
          operand: this.instanceType,
        }
        this.$emit('change', { ...this.value, instance, filters })
        this.$nextTick(this.generateNewPreview)
      },
    },
    counters: {
      get() {
        return (this.value || {}).counters || []
      },
      set(counters) {
        let data = counters.map((c, index) =>
          c.counter
            ? {
                ...c,
                ...(c.aggrigateFn ||
                (c.counter &&
                  Boolean(
                    counters
                      .filter(
                        (x, innerIndex) => x.counter && innerIndex !== index
                      )
                      .find(
                        (innerC) =>
                          innerC.counter.counterName === c.counter.counterName
                      )
                  ))
                  ? {
                      ...(!this.aggrigateOptions[c.counter.dataType[0]].find(
                        (agg) => agg.key === c.aggrigateFn
                      )
                        ? {
                            aggrigateFn: 'avg',
                          }
                        : {}),
                    }
                  : {
                      aggrigateFn: ((c.counter || {}).dataType || []).includes(
                        'numeric'
                      )
                        ? 'avg'
                        : 'last',
                    }),
              }
            : c
        )
        const additionalData = this.getCounterChangedData(data)
        if (!data[0].counter) {
          data = [data[0]]
        }
        // if first counter is changed and doesn't match others then remove all other counters
        // const resolvedCounters = data
        //   .map(({ counter }) => (counter || {}).dataType)
        //   .filter(Boolean)

        // if (
        //   [WidgetTypeConstants.TOPN].includes(this.widgetCategory) &&
        //   resolvedCounters.length === data.length
        // ) {
        //   this.$emit('change', {
        //     ...(this.value || {}),
        //     counters: [data[0]],
        //     ...additionalData,
        //   })
        // } else {
        // set counter provider to ref
        if (
          (data[0].counter || {}).instanceType &&
          Boolean((data[0].target || {}).entities)
        ) {
          this.$nextTick(() => {
            if (this.$refs.instanceMetricProviderRef) {
              this.$refs.instanceMetricProviderRef.setSelectedCounter(
                (data[0].counter || {}).key
              )
            }
          })
        }
        let hasSameInstanceForAllCounters = false
        if (this.selectedCounterType === 'instance') {
          hasSameInstanceForAllCounters = data.every((i) =>
            i.counter
              ? i.counter.instanceType === (data[0].counter || {}).instanceType
              : true
          )
        }
        if (!((counters || [])[0] || {}).counter) {
          this.$emit('change', {
            ...(this.value || {}),
            counters: data,
            ...additionalData,
            filters: {
              pre: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
              post: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
            },
          })
        } else if (
          [WidgetTypeConstants.GAUGE].includes(this.widgetCategory) &&
          data[0].counter &&
          data[0].counter.key &&
          getAllowedUnit(data[0].counter.key) !== 'percent'
        ) {
          this.$emit('change-widget-type', WidgetTypeConstants.METRO_TILE)
          this.$emit('change', {
            ...(this.value || {}),
            counters: data,
            ...additionalData,
          })
        } else if (
          this.useSameTypeOfCounters &&
          [WidgetTypeConstants.CHART].includes(this.widgetCategory) === false &&
          ((this.selectedCounterType === 'instance' &&
            !hasSameInstanceForAllCounters) ||
            (this.selectedCounterType === 'metric' &&
              (data[0].counter || {}).instanceType)) // &&
          // resolvedCounters.length === data.length
        ) {
          this.$emit('change', {
            ...(this.value || {}),
            counters: [data[0]],
            ...additionalData,
          })
        } else {
          this.$emit('change', {
            ...(this.value || {}),
            counters: data,
            ...additionalData,
          })
        }
        // }
        this.$nextTick(() => {
          this.generateNewPreview()
        })
      },
    },
    filters: {
      get() {
        return (this.value || {}).filters
      },
      set(filters) {
        this.$emit('change', { ...(this.value || {}), filters })
        this.$nextTick(this.generateNewPreview)
      },
    },
  },
  watch: {
    instanceType(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.instance = undefined
        this.instanceOptions = []
      }
    },
    groupType(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.counterSearchParams = {
          'visualization.group.type': this.groupType,
          'visualization.category': this.widgetCategory,
        }
      }
    },
    widgetCategory(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.setCounters()
      }
    },
  },
  created() {
    // this.generateNewPreview = Debounce(this.generateNewPreviewRaw, 1000, {
    //   trailing: true,
    // })
    this.generateNewPreview()
  },
  beforeDestroy() {
    setTimeout(() => {
      this.generateNewPreview()
    })
  },
  methods: {
    handleInstanceDataReceived(data) {
      const instanceType = this.instanceType
      this.instanceOptions = Object.freeze(
        data.data.map((i) => ({ key: i[instanceType], text: i[instanceType] }))
      )
    },
    generateNewPreview() {
      const nonCounterItem = (this.counters || []).filter(
        (c) => !(c.counter || {}).key
      )
      if (nonCounterItem.length === 0) {
        Bus.$emit('widget.generate.preview')
      }
    },
    getCounterChangedData(counters) {
      let additionalData = {}
      if (
        this.counters[0].counter &&
        'instanceType' in this.counters[0].counter &&
        counters[0].counter &&
        'instanceType' in counters[0].counter &&
        this.counters[0].counter &&
        this.counters[0].counter.instanceType !==
          (counters[0].counter && counters[0].counter.instanceType)
      ) {
        additionalData.counters = [counters[0]]
        additionalData.filters = {
          pre: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
          post: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
        }
      }
      // @TODO only for post filter check
      // const counterKeys = counters
      //   .map((c) => (c.counter || {}).key)
      //   .filter(Boolean)
      // const selectedKeys = this.selectedCounters
      // const updatedCounterLength = counterKeys.filter(
      //   (k) => selectedKeys.indexOf(k) === -1
      // ).length
      // if (updatedCounterLength) {
      //   additionalData.filters = {
      //     pre: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
      //     post: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
      //   }
      // }
      return additionalData
    },
    setCounters(optionsMap) {
      if (optionsMap && !Array.isArray(optionsMap)) {
        this.allCounters = Array.from(optionsMap.values())
      }

      if (
        this.value?.counters?.[0]?.counter &&
        !this.value.counters[0]?.counter?.key &&
        this.allCounters?.length
      ) {
        let counter = this.allCounters.filter(
          (c) => c.counterName === 'system.cpu.percent'
        )

        if (!counter) {
          counter = Array.from(optionsMap.values()).filter(
            (i) => i.instanceType !== i.key
          )
        }

        if (
          this.value?.counters?.[0].counter &&
          !this.value.counters[0]?.counter?.key &&
          counter
        ) {
          this.$emit('change', {
            ...(this.value || {}),
            counters: [
              {
                key: generateId(),
                aggrigateFn: 'avg',
                counter: counter[0],
                target: {
                  entityType: 'Monitor',
                },
              },
            ],
          })
        }
      }
    },
  },
}
</script>

<style lang="less" scoped>
.filter-viewer {
  background: var(--neutral-lightest);
  border: 1px solid var(--border-color);
}
</style>
