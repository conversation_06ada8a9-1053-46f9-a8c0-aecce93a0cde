<template>
  <Slider
    v-model="currentRange"
    class="time-range-slider"
    range
    :min="min"
    :max="max"
    :step="null"
    :marks="marks"
    :tip-formatter="formatValue"
    v-bind="$attrs"
    @afterChange="handleAfterChange"
  />
</template>

<script>
import Moment from 'moment'
import Slider from 'ant-design-vue/es/slider'
import 'ant-design-vue/es/slider/style'
import { getRange } from '../helper'
import { TIME_FORMAT } from '../constants'
import datetime from '@/src/filters/datetime'

export default {
  name: 'TimeRangeSlider',
  components: {
    Slider,
  },
  model: { event: 'change' },
  props: {
    value: {
      type: Object,
      default: undefined,
    },
  },
  data() {
    return {
      currentRange: [0, 0],
      min: 0,
      max: null,
      step: null,
      unit: null,
      ticks: null,
    }
  },
  computed: {
    marks() {
      const step = this.step
      const marks = {}
      let progress = step
      const unit = this.unit
      for (let i = 0; i < this.ticks; i++) {
        const shouldShowLabel = i % 10 === 0
        marks[this.min + progress] = {
          label: shouldShowLabel
            ? Moment.unix((this.min + progress) / 1000).format(unit)
            : '',
        }
        progress += step
      }
      marks[this.max] = Moment.unix(this.max / 1000).format(unit)
      return marks
    },
  },
  created() {
    this.currentRange = this.buildFromAndToFromRange(this.value)
    this.setMinMax()
  },
  methods: {
    getUnitToUse(step) {
      const duration = Moment.duration(step * 10)
      if (duration.asYears() > 1) {
        return 'YYYY'
      } else if (duration.asMonths() > 1) {
        return 'MMM'
      } else if (duration.asWeeks() > 1) {
        return 'DD/MM'
      } else if (duration.asDays() > 1) {
        return 'DD/MM'
      }
      return 'HH:mm'
    },
    formatValue(value) {
      let d = Moment.unix(value / 1000).milliseconds(0)
      if (this.unit !== 'HH:mm') {
        d.startOf('day')
      }
      return datetime(d.unix())
    },
    setMinMax() {
      let [start, end] = this.currentRange
      const now = Moment().unix() * 1000
      end = Math.min(end, now)
      const singleDuration = end - start
      const totalDuration = singleDuration * 4
      if (now - end > singleDuration) {
        end = end + singleDuration
      }
      if (now - end > singleDuration * 2) {
        end = end + singleDuration
      }
      start = end - totalDuration
      this.min = start
      this.max = end
      const diff = this.max - this.min
      let ticks = 100
      this.step = diff / ticks
      this.ticks = ticks
      this.unit = this.getUnitToUse(this.step)
    },
    buildFromAndToFromRange(timeRange) {
      let startDate = timeRange.startDate
      let endDate = timeRange.endDate
      if (!startDate) {
        const range = getRange(timeRange.selectedKey)
        startDate = range.startDate
        endDate = range.endDate
      }
      return [startDate, endDate]
    },
    handleAfterChange(event) {
      const startDate =
        Moment.unix(event[0] / 1000)
          .milliseconds(0)
          .seconds(0)
          .unix() * 1000
      const endDate =
        Moment.unix(event[1] / 1000)
          .milliseconds(0)
          .seconds(0)
          .unix() * 1000
      let startTime
      let endTime
      if (this.unit !== 'HH:mm') {
        startTime = Moment.unix(startDate / 1000)
          .startOf('day')
          .format(TIME_FORMAT)
        endTime = Moment.unix(endDate / 1000)
          .startOf('day')
          .format(TIME_FORMAT)
      } else {
        startTime = Moment.unix(startDate / 1000).format(TIME_FORMAT)
        endTime = Moment.unix(endDate / 1000).format(TIME_FORMAT)
      }
      this.$emit('change', {
        selectedKey: 'custom',
        startDate,
        endDate,
        startTime,
        endTime,
      })
    },
  },
}
</script>

<style lang="less">
.time-range-slider {
  .@{ant-prefix}-slider-rail {
    display: none;
  }
  .@{ant-prefix}-slider-mark-text {
    margin-top: 5px;
    font-size: 0.6rem;
    color: var(--page-text-color);
  }
  .@{ant-prefix}-slider-dot {
    top: -5px;
    width: 2px;
    height: 14px;
    border: 1px solid var(--border-color);
  }
  .@{ant-prefix}-slider-handle {
    z-index: 2;
    width: 10px;
    height: 10px;
    background: white;
    transform: translateY(2px);

    &-1 {
      margin-left: 0;
    }

    &-2 {
      margin-left: -18px;
    }
  }
  .@{ant-prefix}-slider-track {
    z-index: 1;
    height: 15px;
    transform: translate(-3px, -5px);
  }
}
</style>
