import Moment from 'moment'
import Capitalize from 'lodash/capitalize'
import { AVAILABLE_RANGE_OPTIONS } from '../constants'
import { getRange, getAdjustedTime } from '../helper'

export const unitMap = {
  m: 'Mins',
  h: 'Hours',
  d: 'Days',
  mo: 'Months',
  q: 'Quarters',
}

const subtractionMap = {
  m: 'minutes',
  h: 'hours',
  d: 'days',
  mo: 'months',
  q: 'quarters',
}

export function convertDurationToText(diff) {
  const m = Moment.duration(diff, 'seconds')

  if (Math.round(m.asMonths()) > 0) {
    return `${Math.round(m.asMonths())}mo`
  } else if (Math.round(m.asDays()) > 0) {
    return `${Math.round(m.asDays())}d`
  } else if (Math.floor(m.asHours()) > 0) {
    return `${Math.floor(m.asHours())}h${
      m.subtract(Math.floor(m.asHours()), 'hours').asMinutes() > 0
        ? ` ${Math.round(
            m.subtract(Math.floor(m.asHours()), 'hours').asMinutes()
          )}m`
        : ''
    }`
  } else if (Math.round(m.asMinutes()) > 0) {
    return `${Math.round(m.asMinutes())}m`
  }
}

export function parseRangeAsText(value) {
  value = (value || '').toLowerCase()
  if (value === 'today' || value === 'yesterday') {
    return {
      ...getRange(value),
      selectedKey: value,
    }
  }
  const regex = /^(\d+)(m|h|d|mo|q)$/
  if (!regex.test(value)) {
    return null
  }
  const existingItem = AVAILABLE_RANGE_OPTIONS.find(
    (item) =>
      item.shortcut &&
      (typeof item.shortcut === 'function'
        ? item.shortcut()
        : item.shortcut) === value
  )
  if (existingItem) {
    return {
      ...getRange(existingItem.key),
      selectedKey: existingItem.key,
    }
  }
  const result = regex.exec(value)
  const time = parseInt(result[1])
  const unit = result[2]
  const defaultValue = {
    selectedKey: 'custom',
    endDate: Moment().unix() * 1000,
    endTime: getAdjustedTime(Moment()),
  }
  if (subtractionMap[unit]) {
    return {
      ...defaultValue,
      startDate: Moment().subtract(time, subtractionMap[unit]).unix() * 1000,
      startTime: getAdjustedTime(Moment().subtract(time, subtractionMap[unit])),
    }
  }
  return null
}

export function parseRangeAsReadableText(value, key) {
  value = (value || '').toLowerCase()
  if (value === 'today' || value === 'yesterday') {
    return Capitalize(value)
  }
  if (['week', 'month', 'quarter', 'year'].includes(value)) {
    return `${key.includes('last') ? 'Last' : 'This'} ${Capitalize(value)}`
  }

  const regex = /^-?(\d+)(m|h|d|mo|q|w|y)$/
  if (!regex.test(value)) {
    return null
  }
  const existingItem = AVAILABLE_RANGE_OPTIONS.find(
    (item) =>
      item.shortcut &&
      (typeof item.shortcut === 'function'
        ? item.shortcut()
        : item.shortcut) === value
  )
  if (existingItem) {
    return existingItem.text
  }
  const result = regex.exec(value)
  const time = parseInt(result[1])
  const unit = result[2]
  let appliedUnit = unitMap[unit]
  if (time <= 1) {
    appliedUnit = appliedUnit.replace(/s$/, '')
  }
  return `Last ${time} ${appliedUnit}`
}
