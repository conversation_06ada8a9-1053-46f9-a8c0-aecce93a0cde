<template>
  <div class="relative">
    <input
      class="time-input autocomplete"
      type="text"
      readonly
      :placeholder="
        autocompleteValues.length &&
        !autocompleteOptions.includes(currentValue.toLowerCase())
          ? autocompleteValues[selectedIndex]
          : undefined
      "
      @click.stop.prevent
    />
    <input
      ref="inputRef"
      v-model="currentValue"
      class="time-input"
      type="text"
      placeholder="Type Range..."
      @input="handleChange"
      @focus="currentValue = ''"
      @blur="currentValue = initialValue"
      @keyup.prevent="handleKeyUp"
      @click.stop.prevent
    />
  </div>
</template>

<script>
export default {
  name: 'TimeRangeInput',
  model: { event: 'change' },
  props: {
    initialValue: {
      type: String,
      default: 'Please Select Time Range',
    },
    autocompleteOptions: {
      type: Array,
      default: undefined,
    },
  },
  data() {
    return {
      currentValue: this.initialValue,
      selectedIndex: 0,
    }
  },
  computed: {
    autocompleteValues() {
      const value = this.currentValue
      if (this.currentValue) {
        return (this.autocompleteOptions || []).filter(
          (item) => item.toLowerCase().indexOf(value.toLowerCase()) === 0
        )
      }
      return []
    },
  },
  watch: {
    initialValue(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.currentValue = newValue
      }
    },
  },
  mounted() {
    this.$refs.inputRef.focus()
  },
  methods: {
    handleChange() {
      this.selectedIndex = 0
      this.$emit('input-value-changed', this.currentValue)
    },
    handleKeyUp($event) {
      if ($event.key === 'ArrowRight') {
        if (this.autocompleteValues.length) {
          this.currentValue = this.autocompleteValues[this.selectedIndex]
          this.$emit('input-value-changed', this.currentValue)
        }
      }
      if ($event.key === 'ArrowUp') {
        $event.preventDefault()
        $event.stopPropagation()
        if (this.autocompleteValues.length) {
          this.selectedIndex =
            (this.selectedIndex + this.autocompleteValues.length - 1) %
            this.autocompleteValues.length
        }
      }
      if ($event.key === 'ArrowDown') {
        $event.preventDefault()
        $event.stopPropagation()
        if (this.autocompleteValues.length) {
          this.selectedIndex =
            (this.selectedIndex + 1) % this.autocompleteValues.length
        }
      }
      if ($event.key === 'Enter') {
        // this.currentValue = this.autocompleteValues[this.selectedIndex]
        this.$emit('input-value-changed', this.currentValue)
        this.$emit('change', this.currentValue)

        // if (this.currentValue) {
        //   $event.target.blur()
        // }
      }
    },
  },
}
</script>

<style lang="less" scoped>
input.time-input {
  position: relative;
  z-index: 2;
  max-width: 100px;
  color: var(--page-text-color);
  background-color: transparent;
  border: none;
  appearance: none;

  &.autocomplete {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
    pointer-events: none;
  }
}
</style>
