import Moment from 'moment'
import Uniq from 'lodash/uniq'
import Omit from 'lodash/omit'
import FindIndex from 'lodash/findIndex'
import Capitalize from 'lodash/capitalize'
import GroupBy from 'lodash/groupBy'
import SortBy from 'lodash/sortBy'
import SumBy from 'lodash/sumBy'
import Constants from '@constants'
import { generateId } from '@utils/id'
import MsToUnit from '@src/filters/ms'
// import { SEVERITY_MAP } from '@data/monitor'
import {
  AVAILABILITY_INCLUDED_KEYS,
  WidgetTypeConstants,
  MAP_SERIES_TYPE,
} from './constants'
import {
  gridWorker,
  chartWorker,
  userDBWorker,
  objectDBWorker,
} from '@/src/workers'
import {
  convertTimeLineForServer,
  SERIES_COLOR_MAP,
  CONFIG_SERIES_NAME,
} from './helper'
import { isUnitConvertible } from '@utils/unit-checker'
import applyUnit from '@/src/utils/unit-applier'

import UniqBy from 'lodash/uniqBy'

import { convertTopNChartToGrid } from '@/src/modules/report/helpers/create-grid-from-chart'

async function getTimezone() {
  const user = await userDBWorker.getUser()
  if (user) {
    return user.preference.timezone
  }
  return Intl.DateTimeFormat().resolvedOptions().timeZone
}

export default async function buildWidgetResult(
  widget,
  response,
  options = {}
) {
  let result = {}
  if (!response.result) {
    response.result = {}
  }
  if (widget.widgetType === WidgetTypeConstants.STATUS_FLAP) {
    result = await buildGridResult(widget, response, options)
  } else if (widget.widgetType === WidgetTypeConstants.HORIZONTAL_TOPN) {
    result = await buildHorizontalTopNResult(widget, response, options)
  } else if (widget.category === WidgetTypeConstants.CHART) {
    result = await buildChartResult(widget, response, options)
  } else if (
    widget.category === WidgetTypeConstants.GRID ||
    widget.category === WidgetTypeConstants.ACTIVE_ALERT
  ) {
    if (
      widget.containerType === 'report' &&
      ((widget.groups[0] || {}).type === 'status.flap' ||
        (widget.groups[0] || {}).type === 'hourly.status.flap' ||
        ((widget.groups[0] || {}).type === 'policy.flap' &&
          (widget.groups[0] || {}).category === 'metric'))
    ) {
      result = await buildAlertGridResult(widget, response, options)
    } else {
      result = await buildGridResult(widget, response, options)
    }
  } else if (widget.category === WidgetTypeConstants.TOPN) {
    if (widget.widgetType === WidgetTypeConstants.GRID) {
      result = await buildGridResult(widget, response, options)
    } else if (widget.widgetType === WidgetTypeConstants.TREE_VIEW) {
      result = await buildMapViewResult(response, widget)
    } else {
      const series = await buildChartResult(widget, response, options)
      result = {
        ...series,
        responseColumns: Uniq(
          (response.result[WidgetTypeConstants.CHART] || {}).columns || []
        ),
      }
    }
  } else if (widget.category === WidgetTypeConstants.GAUGE) {
    result = await buildGaugeResult(widget, response, options)
  } else if (widget.category === WidgetTypeConstants.HEATMAP) {
    result = await buildHeatmapResult(widget, response, options)
  } else if (widget.category === WidgetTypeConstants.SANKEY) {
    result = await buildSankeyResult(widget, response, options)
  } else if (widget.category === WidgetTypeConstants.CUSTOM) {
    result = await buildCustomResult(widget, response, options)
  } else if (widget.category === WidgetTypeConstants.STREAM) {
    result = await buildGridResult(widget, response, options)
  } else if (widget.category === WidgetTypeConstants.MAP_VIEW) {
    result = await buildMapViewResult(response)
  } else if ([WidgetTypeConstants.FORECAST].includes(widget.category)) {
    result = await buildForecastWidgetResult(response, widget)
  } else if ([WidgetTypeConstants.ANOMALY].includes(widget.category)) {
    result = await buildAnomalyWidgetResult(response, widget)
  } else if (widget.category === WidgetTypeConstants.EVENT_HISTORY) {
    result = await buildGridResult(widget, response, options)
  }
  return result
}

export const extractAvailabilityCounterType = (counter) => {
  const regex = new RegExp(`(${AVAILABILITY_INCLUDED_KEYS.join('|')})`)
  const result = regex.exec(counter)
  if (result) {
    return result[0]
  }
  return counter
}

/**
 *
 * This is for availability Pie widget for monitor Template
 *
 * @param {result} result
 * @returns
 */
function buildAvailabilityPieResult(result) {
  const severities = (result.series || [])
    .filter((c) => /percent\./.test(c.name) && !!c.data[0])
    .map((s) => s.name)
  const data =
    severities.length > 0
      ? [
          {
            displayTextInCenter: true,
            flipCounterAndSeriesName: true,
            centerText: {
              title: 'Up',
              fontSize: 24,
              value() {
                const upIndex = FindIndex(
                  this.options.data,
                  (d) => d.name === 'Up'
                )
                if (upIndex !== -1) {
                  return this.options.formattedValues[upIndex]
                }
                return '0%'
              },
            },
            name: '',
            counter:
              severities[0].indexOf('.sec.') > 0
                ? 'severity.time.sum'
                : 'severity.percentage',
            data: severities.map((counter) => {
              const row = result.series.find((s) => s.name === counter)
              const durationRow =
                result.series.find(
                  (s) =>
                    s.name ===
                    counter.replace('percent', 'seconds').replace('avg', 'sum')
                ) || {}
              return {
                name: `${extractAvailabilityCounterType(counter)}`,
                y: row.data[0],
                ...(durationRow.formattedValues
                  ? { duration: durationRow.formattedValues[0] }
                  : {}),
              }
            }),
            formattedValues: severities.map((counter) => {
              const row = result.series.find((s) => s.name === counter)
              return row.formattedValues[0]
            }),
          },
        ]
      : []
  return {
    series: data,
    categories: severities.map((c) => extractAvailabilityCounterType(c)),
  }
}

function buildAlertTopNPieResult(result) {
  if ((result.series || []).length > 1) {
    // result by alert
    const nameSeries = (result.series || []).find(
      (s) => s.counter === 'policy.name'
    ) || { data: [] }
    const countSeries = (result.series || []).find(
      (s) => s.counter === 'severity.count'
    ) || { data: [] }
    const series = nameSeries.data.length
      ? [
          {
            name: '',
            counter: 'severity.count',
            data: nameSeries.data.map((policyName, index) => {
              return {
                name: policyName,
                y: countSeries.data[index],
              }
            }),
          },
        ]
      : []
    return {
      series: series,
      categories: nameSeries.data,
    }
  } else {
    // result by monitor and severity
    return buildCategoriesAndSeriesForPieChart(result)
  }
}

async function buildChartResult(widget, response, options) {
  let result = response.result[WidgetTypeConstants.CHART] || {}
  if (
    widget.widgetType === WidgetTypeConstants.PIE ||
    widget.widgetType === WidgetTypeConstants.APPLICATION_TODAY_AVAILABILITY
  ) {
    if (widget.groups.find((g) => g.type === 'availability')) {
      return buildAvailabilityPieResult(result)
    }
    if (widget.groups.find((g) => g.type === 'netroute.availability')) {
      return buildAvailabilityPieResult(result)
    }
    if (widget.groups.find((g) => g.type === 'alert' || g.type === 'policy')) {
      return buildAlertTopNPieResult(result)
    }
    return buildCategoriesAndSeriesForPieChart(result)
  }
  let monitorMap = await objectDBWorker.getObjectsAsMap({}, [
    'ip',
    'name',
    'id',
  ])
  return {
    isMultiTrendReport: Boolean(response['multi.trend']),
    ...(result.categories ? { categories: result.categories } : {}),
    ...(result.groupByColumns ? { groupByColumns: result.groupByColumns } : {}),
    series: (result.series || []).map((i) => ({
      ...i,
      ...(response['multi.trend'] && monitorMap[response['entity.id']]
        ? {
            id: response['entity.id'],
            monitor: monitorMap[response['entity.id']]['name'],
            entity: monitorMap[response['entity.id']]['name'],
            name: `${i.counter} (${monitorMap[response['entity.id']]['name']})`,
            ip: monitorMap[response['entity.id']]['ip'],
          }
        : {}),
      zoomInfo: {
        defaultTimeline: convertTimeLineForServer(options.timeRange),
        individualZoom: false,
        params: {
          id: widget.id,
          ...Omit(options, ['timeRange']),
        },
        serverEvent: Constants.UI_WIDGET_RESULT_EVENT,
      },
    })),
  }
}

async function buildHorizontalTopNResult(widget, response, options) {
  let result
  if (response.result[WidgetTypeConstants.CHART]) {
    const chart = await buildChartResult(widget, response, options)
    result = { chart }
  } else if (response.result[WidgetTypeConstants.GRID]) {
    const grid = await buildGridResult(widget, response, options)
    result = { grid }
  }
  return result
}

function buildCategoriesAndSeriesForPieChart(result) {
  const categories = result.categories || []
  const data = (result.series || []).map((counterSeries) => ({
    ...counterSeries,
    name: `${counterSeries.counter}`,
    data: categories.map((host, index) => ({
      name: `${host}`,
      y: counterSeries.data[index],
    })),
    counter: counterSeries.counter,
    formattedValues: counterSeries.formattedValues || [],
  }))
  return {
    ...(result.categories ? { categories: result.categories } : {}),
    series: data,
  }
}

async function buildAlertGridResult(widget, response) {
  let result = await gridWorker.buildGridWidgetData(widget, response)
  return gridWorker.groupRowsForAlertReport(result, widget)
}

async function buildGridResult(widget, response, options) {
  if (widget.usePivotGroupBy) {
    let result = await gridWorker.buildGridWidgetData(widget, response)
    return gridWorker.groupRowsForPivotReport(result, widget)
  }
  if (
    [
      WidgetTypeConstants.AVAILABILITY_TIME_SERIES,
      WidgetTypeConstants.APPLICATION_AVAILABILITY_TIME_SERIES,
    ].includes(widget.widgetType)
  ) {
    return buildAvailabilityTimeSeries(widget, response)
  } else if (widget.widgetType === WidgetTypeConstants.KPI_GAUGE) {
    return buildGaugeResult(widget, response)
  } else if (
    widget.widgetType === WidgetTypeConstants.WIRELESS_SIGNAL_STRENGTH
  ) {
    return buildTableResult(widget, response)
  } else if (widget.widgetType === WidgetTypeConstants.MONITOR_HEALTH) {
    return buildChartResult(widget, response)
  } else if (widget.widgetType === WidgetTypeConstants.HARDWARE_SENSOR_GRID) {
    const data =
      ((response.result || {})[WidgetTypeConstants.GRID] || {}).data || []
    let instanceColumn = (
      ((response.result || {})[WidgetTypeConstants.GRID] || {})
        .groupByColumns || []
    ).find((c) => c !== 'monitor')
    return data.map((i) => ({
      ...i,
      instance: i[instanceColumn],
      instance_type: instanceColumn,
    }))
  } else if (widget.widgetType === WidgetTypeConstants.VLAN_GRID) {
    return buildVlanGridResult(widget, response)
  } else if (widget.widgetType === WidgetTypeConstants.INTERFACE_GRID) {
    return buildInterfaceGridResult(widget, response)
  } else if (widget.widgetType === WidgetTypeConstants.ACCESS_POINT) {
    return buildAccessPointGridResult(widget, response)
  } else if (
    widget.widgetType === WidgetTypeConstants.SWTICH_PORT_MAPPER_GRID
  ) {
    return buildSwitchPortMapperGridResult(widget, response)
  } else if (
    [
      WidgetTypeConstants.CONFIGURED_ALERT,
      WidgetTypeConstants.ACTIVE_ALERT,
    ].includes(widget.widgetType)
  ) {
    return buildTableResult(widget, response, options)
  } else if (widget.widgetType === WidgetTypeConstants.PORT_VIEW) {
    return SortBy(
      (
        ((response.result || {})[WidgetTypeConstants.GRID] || {}).data || []
      ).map((i) => ({
        key: generateId(),
        monitor: i.monitor,
        interface: i?.interface?.split('-')?.pop() || '',
        name: i['interface.name.last'],
        status: i['interface.status.last'] || 'Disable',
        interfaceIndex: i['interface.index.last'],
      })),
      'interfaceIndex'
    )
  } else if (widget.widgetType === WidgetTypeConstants.STATUS_FLAP) {
    return buildAvailabilityFlapView(widget, response, options)
  } else if (widget.widgetType === WidgetTypeConstants.APPLICATION_STATUS) {
    return buildTableResult(widget, response, options)
  } else if (widget.widgetType === WidgetTypeConstants.STACKED_SWITCH_VIEW) {
    return buildStackedSwitchGridResult(widget, response)
  } else if (widget.widgetType === WidgetTypeConstants.METRO_TILE_COUNT_VIEW) {
    return buildMetrotileCountFormGrid(widget, response)
  }
  return buildTableResult(widget, response, options)
}

export async function buildTableResult(widget, response, options = {}) {
  const result = await gridWorker.buildGridWidgetData(
    widget,
    response,
    options.useWidgetColumnsOnly
  )
  return result
}

async function buildGaugeResult(widget, response) {
  let result =
    ((response.result || {})[WidgetTypeConstants.GRID] || {}).data || []
  if ([WidgetTypeConstants.KPI_GAUGE].includes(widget.widgetType)) {
    return result[0] || {}
  }
  const isAvailabilityWidget = Boolean(
    widget.groups.find((g) => g.type === 'availability')
  )
  const isAlertWidget = Boolean(
    widget.groups.find(
      (g) =>
        g.type === 'alert' ||
        g.type === 'alert.stream' ||
        g.type === 'policy' ||
        g.type === 'policy.stream'
    )
  )

  const isConfigTypeWidget = Boolean(
    widget.groups.find((g) => g.type === Constants.CONFIG_GROUP)
  )
  if (isAvailabilityWidget || isAlertWidget || isConfigTypeWidget) {
    let categories =
      ((response.result || {})[WidgetTypeConstants.GRID] || {}).columns || []

    if (isAlertWidget && widget?.groups?.find((g) => g.category !== 'metric')) {
      let obj = {}

      // result.reduce(
      //   (acc, row, index) => ({
      //     ...acc,
      //     ...(row.severity
      //       ? {
      //           [row.severity.toLowerCase()]: result[index]['severity.count'],
      //         }
      //       : {}),
      //   }),
      //   {}
      // )

      for (let [index, row] of result.entries()) {
        if (row.severity) {
          obj[row.severity.toLowerCase()] = result[index]['severity.count']
        }
      }

      result = [obj]

      categories = (
        ((response.result || {})[WidgetTypeConstants.GRID] || {}).data || []
      ).map((d) => d.severity)
    }
    const row = result[0]
    const data = [
      {
        name: isAvailabilityWidget
          ? 'Availability'
          : isAlertWidget
          ? 'Alert'
          : '',
        displayTextInCenter: true,

        ...(isConfigTypeWidget
          ? { flipCounterAndSeriesName: isConfigTypeWidget }
          : {}),

        data: categories.map((host, index) => ({
          ...(isConfigTypeWidget && SERIES_COLOR_MAP[host.toLowerCase()]
            ? {
                color: SERIES_COLOR_MAP[host.toLowerCase()],
              }
            : {}),

          name: isConfigTypeWidget
            ? CONFIG_SERIES_NAME[(host || '').toLowerCase()] ||
              Capitalize(host || '')
            : Capitalize(host),
          y: row[host.toLowerCase()],
        })),
      },
    ]
    return {
      categories,
      series: data,
    }
  }
  if (result.length) {
    const counters = ((response.result || {})[WidgetTypeConstants.GRID] || {})
      .columns
    const row = result[0] || {}

    const finalData = {}

    // counters.forEach((key) => {
    //   finalData[key] = row[key]

    //   if (row[`${key}.formatted`]) {
    //     finalData[`${key}.formatted`] = row[`${key}.formatted`]
    //   }

    //   if (/\.ms(?:\.avg|max|min|last|sum)?$/.test(key)) {
    //     finalData[`${key}.formatted`] = MsToUnit(row[key])
    //   }
    // })

    for (const key of counters) {
      finalData[key] = row[key]

      if (row[`${key}.formatted`]) {
        finalData[`${key}.formatted`] = row[`${key}.formatted`]
      }

      if (/\.ms(?:\.avg|max|min|last|sum)?$/.test(key)) {
        finalData[`${key}.formatted`] = MsToUnit(row[key])
      }
    }

    return {
      columns:
        ((response.result || {})[WidgetTypeConstants.GRID] || {}).columns || [],
      data: finalData,

      // counters.reduce(
      //   (o, key) => ({
      //     ...o,
      //     [key]: row[key],
      //     ...(row[`${key}.formatted`]
      //       ? { [`${key}.formatted`]: row[`${key}.formatted`] }
      //       : {}),
      //     ...(/\.ms(?:\.avg|max|min|last|sum)?$/.test(key)
      //       ? { [`${key}.formatted`]: MsToUnit(row[key]) }
      //       : {}),
      //   }),
      //   {}
      // ),
    }
  }
  return {}
}

async function buildHeatmapResult(widget, response) {
  // const specialGroupByColumns = [
  //   'object.type',
  //   'object.resource.group',
  //   'monitor',
  //   'application',
  // ]
  const hasMetricGroup = widget.groups[0].type === 'metric'

  const firstCounterContext = widget?.groups?.[0]?.counters?.[0]

  const columnMap = {
    monitor: 'object.id',
  }

  let result =
    ((response.result || {})[WidgetTypeConstants.GRID] || {}).data || []

  let groupByColumns = []
  if (
    widget.groups[0].type === 'availability' &&
    widget?.groups[0]?.counters[0]?.counter?.key === 'monitor'
  ) {
    groupByColumns = widget.groups[0].resultBy
  } else if (
    (widget.groups[0].type === 'availability' ||
      widget.groups[0].type === 'policy') &&
    widget.groups[0].resultBy
  ) {
    groupByColumns = widget.groups[0].resultBy

    if (
      widget.groups[0].type === 'policy' &&
      widget.groups[0].category === 'metric' &&
      widget.groups[0].resultBy
    ) {
      groupByColumns = []
    }
  } else if (hasMetricGroup) {
    groupByColumns = response['visualization.data.sources']?.[0]?.[
      'visualization.result.by'
    ]
      ? Uniq([
          'monitor',
          ...(Array.isArray(
            response['visualization.data.sources']?.[0]?.[
              'visualization.result.by'
            ]
          )
            ? response['visualization.data.sources']?.[0]?.[
                'visualization.result.by'
              ]
            : []),
        ])
      : []
  } else {
    // groupByColumns =
    //   ((response.result || {})[WidgetTypeConstants.GRID] || {})
    //     .groupByColumns || []
  }
  // const availableSpecialGroupByColumns = groupByColumns.filter((c) =>
  //   specialGroupByColumns.includes(c)
  // )
  if (!widget?.primaryResultBy?.length) {
    groupByColumns = []
  }
  // result = SortBy(
  //   result,
  //   (i) => SEVERITY_MAP['status' in i ? i.status : i.severity]
  // )

  const transformItem = (monitor) => {
    const groupBycolumnMap = {}

    groupByColumns.forEach((key) => {
      groupBycolumnMap[key] = monitor[key]
    })

    return {
      id: monitor['entity.id'] || generateId(),
      'object.id': monitor['object.id'],
      policyName: monitor['policy.name'],
      'object.type': monitor['object.type'],
      'object.vendor': monitor['object.vendor'],
      severity: monitor.severity || monitor.status,
      instance: monitor.instance,
      dateTime: monitor.interval,
      category: monitor['object.category'],
      monitor: monitor['monitor'],
      instanceIp:
        monitor['instance.ip'] && monitor['instance.ip'] !== ''
          ? monitor['instance.ip']
          : undefined,

      // ...groupByColumns.reduce((result, key) => {
      //   return {
      //     ...result,
      //     ...(monitor[key] ? { [key]: monitor[key] } : {}),
      //   }
      // }, {}),
      ...groupBycolumnMap,
      ...(hasMetricGroup
        ? {
            percentage:
              monitor[
                `${firstCounterContext?.counter?.key.replace(/[~^]/g, '.')}.${
                  firstCounterContext?.aggrigateFn
                }`
              ],

            ...((firstCounterContext?.counter?.key || '').indexOf('~') >= 0
              ? {
                  instance:
                    monitor[
                      (firstCounterContext?.counter?.key || '').split('~')[0]
                    ],
                }
              : {}),
          }
        : {}),
    }
  }

  if (groupByColumns.length) {
    let groupedResult = GroupBy(result, (i) =>
      [groupByColumns[0]].reduce(
        (c, key) =>
          `${
            c.length
              ? ` - ${i[columnMap[key]] || i[key]}`
              : i[columnMap[key]] || i[key]
          }`,
        ''
      )
    )
    setTimeout(() => {
      groupedResult = null
    })
    return {
      groupByColumn: groupByColumns[0],
      // we show only maximum 100 groups
      data: Object.keys(groupedResult)
        .slice(0, 100)
        .map((key) => ({
          title: key,
          data: groupedResult[key].slice(0, 250).map(transformItem),
          totalCount: groupedResult[key].length,
        })),
    }
  }
  let data = result.slice(0, 250).map(transformItem)
  setTimeout(() => {
    data = null
    result = null
  })
  return {
    data,
    totalCount: result.length,
  }
}

async function buildAvailabilityFlapView(widget, response, options) {
  const data =
    ((response.result || {})[WidgetTypeConstants.GRID] || {}).data || []
  const timezone = await getTimezone()
  const offset = Moment().tz(timezone).utcOffset() * 60 * 1000
  return {
    flaps: data.map((row) => ({
      resourceName: options['filter.keys'] ? options['filter.keys'][0] : '',
      x: row.timestamp + offset,
      name: `${row['status.flap.history.value']}`,
      label: `${row['status.flap.history.value']}`,
      color: `var(--severity-${row[
        'status.flap.history.value'
      ].toLowerCase()})`,
      duration: row['duration.value'],
      status: row['status.flap.history.value'],
      interval: row.timestamp + offset,
    })),
  }
}

// function buildApplicationResult(response) {
//   if (response.result.queryMeta.progress < 100) {
//     return {
//       _keepLoading: true,
//     }
//   }
//   return (
//     ((response.result || {})[WidgetTypeConstants.GRID] || {}).data || []
//   ).map((c) => ({
//     key: generateId(),
//     process: c['system.process'] || c['system.service'],
//     monitorId: c['entity.id'],
//     data: Uniq(
//       Object.keys(c)
//         .filter((key) => {
//           return (
//             /\.percent/.test(key) &&
//             AVAILABILITY_INCLUDED_KEYS.includes(
//               extractAvailabilityCounterType(key)
//             ) &&
//             /\.formatted$/.test(key) === false
//           )
//         })
//         .map((counter) => ({
//           name: extractAvailabilityCounterType(counter),
//           value: c[counter],
//         }))
//     ),
//   }))
// }

async function buildSankeyResult(widget, response) {
  const series = await chartWorker.buildSankeySeries(
    (response.result || {})[WidgetTypeConstants.GRID] || {}
  )
  return series
}

async function buildCustomResult(widget, response) {
  if (
    widget.widgetType === WidgetTypeConstants.PIE ||
    widget.widgetType === WidgetTypeConstants.APPLICATION_TODAY_AVAILABILITY
  ) {
    if (response.result && response.result[WidgetTypeConstants.CHART]) {
      const result = await buildChartResult(widget, response)
      let isPercentSeries = false
      if (
        result.series &&
        result.series.length &&
        result.series[0].counter.indexOf('severity.percentage') >= 0
      ) {
        isPercentSeries = true
      }
      return {
        _keepLoading: response.result.queryMeta.progress < 100,
        [isPercentSeries ? 'percentage' : 'time']: result,
      }
    }
    return {
      _keepLoading: response.result.queryMeta.progress < 100,
    }
  }
  return buildTableResult(widget, response)
}

function buildAvailabilityTimeSeries(widget, response) {
  const result = (response.result[WidgetTypeConstants.GRID] || {}).data[0]
  let data = {}
  if (result) {
    const timeRange = widget.timeRange.selectedKey

    if (timeRange === '-1d') {
      data.name = 'Last Day'
    } else if (timeRange === '-7d') {
      data.name = 'Last 7 Days'
    } else {
      data.name = 'Last 15 Days'
    }

    data = {
      ...result,
      ...data,
    }
  }
  return {
    _keepLoading: response.result.queryMeta.progress < 100,
    [widget.timeRange.selectedKey]: {
      series: [data],
    },
  }
}

async function buildVlanGridResult(widget, response) {
  const groupColumns =
    response.result[WidgetTypeConstants.GRID].groupByColumns || []
  if (groupColumns.includes('vlan')) {
    return {
      vlan: await buildTableResult(widget, response, {
        useWidgetColumnsOnly: true,
      }),
      _keepLoading: response.result.queryMeta.progress < 100,
    }
  }
  return {
    interface: response.result[WidgetTypeConstants.GRID],
    _keepLoading: response.result.queryMeta.progress < 100,
  }
}

async function buildInterfaceGridResult(widget, response) {
  const groupColumns =
    response.result[WidgetTypeConstants.GRID].groupByColumns || []
  if (groupColumns.includes('vlan')) {
    return {
      vlan: response.result[WidgetTypeConstants.GRID],
      _keepLoading: response.result.queryMeta.progress < 100,
    }
  } else if (groupColumns.includes('interface')) {
    return {
      interface: await buildTableResult(widget, response, {
        useWidgetColumnsOnly: true,
      }),
      _keepLoading: response.result.queryMeta.progress < 100,
    }
  } else if (groupColumns.includes('client.mac.address')) {
    return {
      macAddress: response.result[WidgetTypeConstants.GRID],
      _keepLoading: response.result.queryMeta.progress < 100,
    }
  }
  return {
    _keepLoading: response.result.queryMeta.progress < 100,
  }
}

async function buildAccessPointGridResult(widget, response) {
  const groupColumns =
    response.result[WidgetTypeConstants.GRID].groupByColumns || []
  if (groupColumns.includes('aruba.wireless.access.point.interface')) {
    return {
      interface: response.result[WidgetTypeConstants.GRID],
      _keepLoading: response.result.queryMeta.progress < 100,
    }
  } else if (groupColumns.includes('aruba.wireless.access.point')) {
    return {
      accessPoints: await buildTableResult(widget, response, {
        useWidgetColumnsOnly: true,
      }),
      _keepLoading: response.result.queryMeta.progress < 100,
    }
  }
  return {
    _keepLoading: response.result.queryMeta.progress < 100,
  }
}

async function buildSwitchPortMapperGridResult(widget, response) {
  const groupColumns =
    response.result[WidgetTypeConstants.GRID].groupByColumns || []
  if (groupColumns.includes('interface')) {
    return {
      interface: await buildTableResult(widget, response, {
        useWidgetColumnsOnly: true,
      }),
      _keepLoading: response.result.queryMeta.progress < 100,
    }
  } else if (groupColumns.includes('client.mac.address')) {
    return {
      macAddress: response.result[WidgetTypeConstants.GRID],
      _keepLoading: response.result.queryMeta.progress < 100,
    }
  }
  return {
    _keepLoading: response.result.queryMeta.progress < 100,
  }
}
async function buildStackedSwitchGridResult(widget, response) {
  const groupColumns =
    response.result[WidgetTypeConstants.GRID].groupByColumns || []
  const column = response.result[WidgetTypeConstants.GRID].columns || []
  if (groupColumns.includes('cisco.stack.switch')) {
    return {
      stackedSwiitch: await buildTableResult(widget, response),
      _keepLoading: response.result.queryMeta.progress < 100,
    }
  } else if (column.includes('cisco.stack.bandwidth.last')) {
    return {
      bandWidth: response.result[WidgetTypeConstants.GRID],
      _keepLoading: response.result.queryMeta.progress < 100,
    }
  }
  return {
    _keepLoading: response.result.queryMeta.progress < 100,
  }
}

async function buildMapViewResult(response, widget) {
  const isTopNWidget = widget?.category === WidgetTypeConstants.TOPN
  const selectedCounter = widget?.groups?.[0]?.counters?.map(
    (c) => c.counter.key
  )
  let gridResult

  if (isTopNWidget && widget.groups[0].type === 'metric') {
    gridResult = await buildTableResult(widget, response)
  }

  if (isTopNWidget && widget.groups[0].type !== 'metric') {
    const gridResult = await convertTopNChartToGrid({ widget, response })

    return buildTreeViewForEventTopN(gridResult, response)
  }

  const groupByColumns =
    ((response?.result || {})[WidgetTypeConstants.GRID] || {}).groupByColumns ||
    []

  const data =
    ((response.result || {})[WidgetTypeConstants.GRID] || {}).data || []
  const columns =
    ((response.result || {})[WidgetTypeConstants.GRID] || {}).columns || []

  const isCitySelected = columns.find((c) => c.includes('city'))

  const sourceCounters = ['source.longitude', 'source.latitude']
  const destinationCounters = ['destination.longitude', 'destination.latitude']

  let seriesType
  if (
    columns.find((c) => sourceCounters.includes(c)) &&
    columns.find((c) => destinationCounters.includes(c))
  ) {
    seriesType = MAP_SERIES_TYPE.MAP_LINE
  } else {
    if (isCitySelected) {
      seriesType = MAP_SERIES_TYPE.MAP_POINT
    } else {
      seriesType = MAP_SERIES_TYPE.MAP_BUBBLE
    }
  }

  let buildedData = []

  if (
    seriesType === MAP_SERIES_TYPE.MAP_POINT ||
    seriesType === MAP_SERIES_TYPE.MAP_BUBBLE
  ) {
    for (const eachData of data) {
      const counterData = colData(
        columns,
        eachData,
        true,
        isTopNWidget,
        selectedCounter,
        groupByColumns
      )
      const coordinates = Object.keys(eachData).find((d) =>
        sourceCounters.includes(d)
      )
        ? [eachData['source.longitude'], eachData['source.latitude']]
        : [eachData['destination.longitude'], eachData['destination.latitude']]

      buildedData = buildedData.concat([
        {
          ...counterData,
          geometry: {
            type: 'Point',
            coordinates: coordinates,
          },
        },
      ])
    }
  }

  if (seriesType === MAP_SERIES_TYPE.MAP_LINE) {
    for (const eachData of data) {
      let destinationName = Object.keys(eachData).find((d) =>
        d.includes('destination.city')
      )
        ? eachData[
            Object.keys(eachData).find((d) => d.includes('destination.city'))
          ]
        : eachData[
            Object.keys(eachData).find((d) => d.includes('destination.country'))
          ]

      let sourceName = eachData[
        Object.keys(eachData).find((d) => d.includes('source.city'))
      ]
        ? eachData[Object.keys(eachData).find((d) => d.includes('source.city'))]
        : eachData[
            Object.keys(eachData).find((d) => d.includes('source.country'))
          ]
      // const counterData = colData(columns, eachData)

      buildedData = buildedData.concat([
        {
          // ...counterData,
          name: sourceName,
          geometry: {
            type: 'Point',
            coordinates: [
              eachData['source.longitude'],
              eachData['source.latitude'],
            ],
          },
        },
        {
          // ...counterData,
          name: destinationName,
          geometry: {
            type: 'Point',
            coordinates: [
              eachData['destination.longitude'],
              eachData['destination.latitude'],
            ],
          },
        },
      ])
    }
    // buildedData = getGroupedData(buildedData)
  }

  return {
    type: seriesType,
    data: {
      ...(seriesType === MAP_SERIES_TYPE.MAP_BUBBLE
        ? {
            [MAP_SERIES_TYPE.MAP_BUBBLE]: data.map((eachData) => {
              let longitude =
                eachData[columns.find((c) => c.includes('longitude'))]
              let latitude =
                eachData[columns.find((c) => c.includes('latitude'))]
              const coordinates = [longitude, latitude]
              const counterData = colData(
                columns,
                eachData,
                true,
                isTopNWidget,
                selectedCounter,
                groupByColumns
              )
              return {
                ...counterData,
                geometry: {
                  coordinates,
                },
              }
            }),
          }
        : {}),

      ...(seriesType === MAP_SERIES_TYPE.MAP_POINT
        ? {
            [MAP_SERIES_TYPE.MAP_POINT]: UniqBy(buildedData, 'name'),
          }
        : {}),

      ...(seriesType === MAP_SERIES_TYPE.MAP_LINE
        ? {
            [MAP_SERIES_TYPE.MAP_LINE]: data.map((eachData) => {
              let destinationName = eachData[
                Object.keys(eachData).find((d) =>
                  d.includes('destination.city')
                )
              ]
                ? eachData[
                    Object.keys(eachData).find((d) =>
                      d.includes('destination.city')
                    )
                  ]
                : eachData[
                    Object.keys(eachData).find((d) =>
                      d.includes('destination.country')
                    )
                  ]
              let sourceName = eachData[
                Object.keys(eachData).find((d) => d.includes('source.city'))
              ]
                ? eachData[
                    Object.keys(eachData).find((d) => d.includes('source.city'))
                  ]
                : eachData[
                    Object.keys(eachData).find((d) =>
                      d.includes('source.country')
                    )
                  ]
              const counterData = colData(columns, eachData, false)

              return {
                geometry: {
                  type: 'LineString',

                  coordinates: [
                    [eachData['source.longitude'], eachData['source.latitude']],
                    [
                      eachData['destination.longitude'],
                      eachData['destination.latitude'],
                    ],
                  ],
                },
                source: sourceName,
                destination: destinationName,
                ...counterData,
              }
            }),

            ...(isCitySelected
              ? {
                  [MAP_SERIES_TYPE.MAP_POINT]: UniqBy(buildedData, 'name'),
                }
              : {
                  [MAP_SERIES_TYPE.MAP_POINT]: UniqBy(buildedData, 'name'),
                }),
          }
        : {}),
    },
    ...(isTopNWidget
      ? {
          responseColumns: (
            ((response?.result || {})[WidgetTypeConstants.GRID] || {})
              .rawColumns || []
          ).filter(
            (c) =>
              ![
                'monitor',
                'object.type',
                'object.vendor',
                'object.ip',
              ].includes(c.rawName)
          ),
          result: gridResult,
        }
      : {}),
  }
}

async function buildForecastWidgetResult(response, widget) {
  let forecastSeries = (
    ((response.result || {})[WidgetTypeConstants.CHART] || {}).series || []
  ).find((s) => s.seriesType === 'forecast')
  const mainSeries = (
    ((response.result || {})[WidgetTypeConstants.CHART] || {}).series || []
  ).find((s) => s.seriesType !== 'forecast')
  if (forecastSeries) {
    forecastSeries = await chartWorker.getForecastSeries(forecastSeries)
    const resolvedObject =
      (await objectDBWorker.getObjectBySmallId(
        +Object.keys(widget.groups[0].counters[0].target.entities)[0]
      )) || {}
    return {
      ...((response.result || {})[WidgetTypeConstants.CHART] || {}),
      series: [
        {
          ...mainSeries,
          id: resolvedObject.id,
          ip: resolvedObject.ip,
          monitor:
            resolvedObject.category === Constants.SERVICE_CHECK
              ? resolvedObject.target
              : resolvedObject.name || resolvedObject.ip,
        },
        { ...forecastSeries, seriesType: 'forecast' },
      ],
    }
  }
  return (response.result || {})[WidgetTypeConstants.CHART] || {}
}

async function buildAnomalyWidgetResult(response, widget) {
  let aiSeries = (
    ((response.result || {})[WidgetTypeConstants.CHART] || {}).series || []
  ).find((s) => s.seriesType === 'anomaly')
  const mainSeries = (
    ((response.result || {})[WidgetTypeConstants.CHART] || {}).series || []
  ).find((s) => s.seriesType !== 'anomaly')
  const resolvedObject =
    (await objectDBWorker.getObjectBySmallId(
      +Object.keys(widget.groups[0].counters[0].target.entities)[0]
    )) || {}
  if (aiSeries) {
    const { range } = await chartWorker.buildAnomalyRangeSeries(aiSeries.data)
    return {
      ...((response.result || {})[WidgetTypeConstants.CHART] || {}),
      series: [
        {
          ...mainSeries,
          id: resolvedObject.id,
          ip: resolvedObject.ip,
          monitor: resolvedObject,
          entity: resolvedObject.id,
          color: '#099dd9',
        },
        { data: range, seriesType: 'anomaly' },
      ],
    }
  }
  return {
    ...((response.result || {})[WidgetTypeConstants.CHART] || {}),
    series: [
      {
        ...mainSeries,
        id: resolvedObject.id,
        ip: resolvedObject.ip,
        monitor: resolvedObject,
        entity: resolvedObject.id,
        color: '#099dd9',
      },
    ],
  }
}

function colData(
  cols,
  currentRow,
  isIncludesValue = true,
  isTopNMap,
  selectedCounter,
  groupByColumns
) {
  let columns = cols.filter((c) => c !== '')

  if (isTopNMap) {
    columns = columns.filter((c) =>
      selectedCounter?.some((sc) => c.includes(sc.replace('~', '.')))
    )
  }

  const colDataMap = {}

  for (const col of columns) {
    if (currentRow[`${col}.formatted`]) {
      colDataMap[`${col}`] = currentRow[`${col}.formatted`]
      colDataMap['z'] = currentRow[col]
      colDataMap['value'] = currentRow[col]

      if (isTopNMap) {
        colDataMap['formattedValue'] = currentRow[`${col}.formatted`]
      }
    } else {
      if (!/formated|country|city|latitude|longitude/.test(col.toLowerCase())) {
        colDataMap[`${col}`] = currentRow[col]
        colDataMap['value'] = currentRow[col]
        colDataMap['z'] = currentRow[col]
      }
      if (isTopNMap) {
        colDataMap['formattedValue'] = currentRow[col]
      }
    }

    if (
      /country|city/.test(col.toLowerCase()) &&
      isIncludesValue &&
      col !== 'country.code'
    ) {
      if (
        columns.find(
          (col) =>
            col.includes('city') &&
            columns.find((col) => col.includes('country'))
        )
      ) {
        colDataMap['name'] =
          currentRow[columns.find((col) => col.includes('city'))]
      } else {
        colDataMap['name'] = currentRow[col]
      }
    }
  }

  if (isTopNMap) {
    colDataMap['name'] = `${
      currentRow['monitor'] ||
      currentRow['object.ip'] ||
      currentRow['group'] ||
      currentRow['tag']
    } ${
      groupByColumns?.length > 1 && currentRow[groupByColumns[1]]
        ? `( ${currentRow[groupByColumns[1]]} )`
        : ''
    }`
  }

  if (currentRow['country.code']) {
    colDataMap['code2'] = currentRow['country.code']
  }
  if (!isIncludesValue) {
    colDataMap['value'] = 1
    colDataMap['z'] = 1
  }

  colDataMap['untouchedData'] = currentRow

  return colDataMap
}

function buildMetrotileCountFormGrid(widget, response) {
  const result =
    ((response.result || {})[WidgetTypeConstants.GRID] || {}).data || []

  if (result.length) {
    return {
      customResult: {
        value: result.length,
        fontSizeSmall: true,
        hideAction: true,
      },
    }
  }
  return {
    showNodata: true,
    customResult: {
      hideAction: true,
    },
  }
}

export function getGroupedData(data) {
  const groupedData = GroupBy(data, 'name')

  const excludedKeys = [
    'name',
    'value',
    'z',
    'geometry',
    '_i',
    'color',
    'className',
    'untouchedData',
  ]

  const transformedData = Object.keys(groupedData).map((group) => {
    // Use lodash's omit to exclude specified keys
    const singleGroupedData = groupedData[group]

    if (singleGroupedData.length === 1) {
      return singleGroupedData[0]
    }

    return {
      ...Object.keys(singleGroupedData[0]).reduce(
        (acc, key) => {
          const sumedData = SumBy(singleGroupedData, (r) => {
            return +r.untouchedData[key]
          })

          return {
            ...acc,

            ...(!excludedKeys.includes(key)
              ? {
                  [key]: isUnitConvertible
                    ? applyUnit(key, sumedData)
                    : sumedData,
                }
              : {}),
          }
        },
        { ...singleGroupedData[0] }
      ),
    }
  })

  return transformedData
}

function buildTreeViewForEventTopN(gridResult, response) {
  const firstSeriesResultByResolver =
    response?.result[WidgetTypeConstants.CHART]?.series?.[0]?.resultByResolver

  const idMap = response?.result[WidgetTypeConstants.CHART]?.series?.[0]?.ids
  const columns = gridResult.columns.filter((c) => c !== '')

  let data = gridResult.rows.map((row, index) => {
    return {
      ...columns.reduce((acc, col) => {
        if (col.key !== 'category') {
          acc[col.name] = row[col.key]
          acc['formattedValue'] = row[col.key]

          if (col.sortKey) {
            acc['z'] = row[col.sortKey]
            acc['value'] = row[col.sortKey]
          } else {
            acc['z'] = row[col.key]
            acc['value'] = row[col.key]
          }
        }

        acc['untouchedData'] = {
          ...row,
          resultByResolverValues: firstSeriesResultByResolver[index],
          id: idMap[index],
        }

        return acc
      }, {}),
      name: row.category,
    }
  })
  return {
    result: gridResult,
    responseColumns: Uniq(
      (response.result[WidgetTypeConstants.CHART] || {}).columns || []
    ),
    type: MAP_SERIES_TYPE.MAP_BUBBLE,
    data: { [MAP_SERIES_TYPE.MAP_BUBBLE]: data },
  }
}
