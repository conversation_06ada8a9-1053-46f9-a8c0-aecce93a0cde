<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="48"
    height="38"
    viewBox="0 0 48 38"
  >
    <path
      d="M43.2,34.33H4.8c-.44,0-.8-.36-.8-.8s.36-.8.8-.8h38.4c.44,0,.8.36.8.8s-.36.8-.8.8Z"
      :style="{ fill: fillLine }"
    />
    <path
      d="M31.47,23.93v6.4h6.4v-6.4h-6.4ZM10.13,30.33h6.4v-6.4h-6.4v6.4ZM20.8,30.33h6.4v-3.2h-6.4v3.2Z"
      style="opacity: 0.8"
      :style="{ fill }"
    />
    <path
      d="M31.47,14.33v9.6h6.4v-9.6h-6.4ZM10.13,23.93h6.4v-8.53h-6.4v8.53ZM20.8,27.13h6.4v-6.4h-6.4v6.4Z"
      style="opacity: 0.6"
      :style="{ fill }"
    />
    <path
      d="M15.47,3.67h-4.27c-.59,0-1.07.48-1.07,1.07v10.67h6.4V4.73c0-.59-.48-1.07-1.07-1.07ZM36.8,5.8h-4.27c-.59,0-1.07.48-1.07,1.07v7.47h6.4v-7.47c0-.59-.48-1.07-1.07-1.07ZM26.13,13.27h-4.27c-.59,0-1.07.48-1.07,1.07v6.4h6.4v-6.4c0-.59-.48-1.07-1.07-1.07Z"
      style="opacity: 0.4"
      :style="{ fill }"
    />
  </svg>
</template>

<script>
export default {
  name: 'VerticalBarStacked',
  props: {
    active: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    fill() {
      return this.active ? 'white' : 'var(--chart-type-icon)'
    },
    fillLine() {
      return this.active ? 'white' : 'var(--chart-line-type-icon)'
    },
  },
}
</script>
