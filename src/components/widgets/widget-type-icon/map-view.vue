<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="48"
    height="38"
    viewBox="0 0 48 38"
  >
    <path
      d="M23.43,26.75l-6.32-8.42c-.33-.43-.92-.57-1.4-.33l-4.71,2.37c-.32.16-.55.47-.6.82l-1.76,10.64c-.14.87.72,1.56,1.53,1.22l7.32-2.99c.25-.1.53-.11.78-.02l11.42,3.86c.27.09.56.08.82-.04l8.41-3.74c.48-.21.75-.73.65-1.24l-2.1-10.91c-.13-.69-.87-1.09-1.52-.83l-5.3,2.13c-.2.08-.37.21-.49.39l-5.09,7.08c-.15.21-.37.35-.62.4-.38.08-.77-.07-1-.38Z"
      style="opacity: 0.2"
      :style="{ fill }"
    />
    <path
      d="M24.04,4.52c4.67,0,8.25,3.69,8.25,8.25,0,3.69-5.32,9.99-7.82,13.03-.22.22-.33.43-.43.54-.11-.22-.33-.33-.43-.54-2.5-3.04-7.82-9.34-7.82-13.03-.11-4.67,3.58-8.25,8.25-8.25Z"
      style="opacity: 0.1"
      :style="{ fill }"
    />
    <path
      d="M28.71,12.77c0-2.61-2.06-4.67-4.67-4.67s-4.67,2.06-4.67,4.67,2.06,4.67,4.67,4.67,4.67-2.06,4.67-4.67ZM20.89,12.77c0-1.74,1.41-3.04,3.04-3.04s3.15,1.41,3.15,3.04-1.41,3.04-3.04,3.04-3.15-1.3-3.15-3.04ZM40.54,29.6l-2.5-13.24c0-.22-.22-.43-.43-.54-.22-.11-.54-.11-.76,0l-4.67,2.28c.98-1.85,1.63-3.58,1.63-5.21,0-5.54-4.34-9.88-9.88-9.88-5.54-.11-9.88,4.23-9.88,9.77,0,1.41.54,3.04,1.52,4.78l-5.32,2.28c-.22.11-.43.33-.43.65l-2.39,13.46c0,.33.11.65.33.76.11.11.33.22.54.22.11,0,.22,0,.33-.11l9.34-4.13,11.94,4.23c.22.11.43.11.65,0l9.66-4.34c.22-.22.43-.54.33-.98ZM24.04,4.52c4.67,0,8.25,3.69,8.25,8.25,0,3.69-5.32,9.99-7.82,13.03-.22.22-.33.43-.43.54-.11-.22-.33-.33-.43-.54-2.5-3.04-7.82-9.34-7.82-13.03-.11-4.67,3.58-8.25,8.25-8.25ZM17.09,29.16l-7.92,3.47,2.06-11.51,4.99-2.17c.43.76.98,1.63,1.63,2.39l-.76,7.82ZM18.72,29.27l.65-5.97c1.09,1.3,2.06,2.61,2.93,**********.87.98,1.09,*********.43.33.65.33s.43-.11.65-.33c.22-.33.65-.76,1.09-1.3.65-.76,1.52-1.74,2.28-2.82l.98,8.79-10.31-3.58ZM30.66,32.96l-1.19-10.75c.43-.54.76-1.09,1.19-1.74l5.97-2.82,2.17,11.62-8.14,3.69Z"
      :style="{ fill: fillLine }"
    />
  </svg>
</template>

<script>
export default {
  name: 'MapView',
  props: {
    active: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    fill() {
      return this.active ? 'white' : 'var(--chart-type-icon)'
    },
    fillLine() {
      return this.active ? 'white' : 'var(--chart-line-type-icon)'
    },
  },
}
</script>
