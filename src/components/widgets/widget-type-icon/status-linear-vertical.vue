<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="48"
    height="48"
    viewBox="0 0 48 48"
  >
    <path
      d="M36.11,23.19H7.08c-.59,0-1.08.48-1.08,1.08v7.14c0,.59.48,1.08,1.08,1.08h29.04c.59,0,1.08-.48,1.08-1.08v-7.14c0-.59-.48-1.08-1.08-1.08ZM40.92,7.84H7.08c-.59,0-1.08.48-1.08,1.08v7.14c0,.59.48,1.08,1.08,1.08h33.85c.59,0,1.08-.48,1.08-1.08v-7.14c0-.59-.48-1.08-1.08-1.08Z"
      style="opacity: 0.2"
      :style="{ fill }"
    />
    <path
      d="M7.08,24.81h17c.59,0,1.08-.48,1.08-1.08v-7.14c0-.59-.48-1.08-1.08-1.08H7.08c-.59,0-1.08.48-1.08,1.08v7.14c0,.59.48,1.08,1.08,1.08ZM30.97,30.87H7.08c-.59,0-1.08.48-1.08,1.08v7.14c0,.59.48,1.08,1.08,1.08h23.9c.59,0,1.08-.48,1.08-1.08v-7.14c0-.59-.48-1.08-1.08-1.08Z"
      style="opacity: 0.1"
      :style="{ fill }"
    />
    <path
      d="M40.92,7.84H7.08c-.59,0-1.08.48-1.08,1.08v7.14c0,.**********.27-.02.09-.05.17-.05.27v7.14c0,.**********.27-.02.09-.05.17-.05.27v7.14c0,.**********.27-.02.09-.05.17-.05.27v7.14c0,.59.48,1.08,1.08,1.08h23.9c.59,0,1.08-.48,1.08-1.08v-6.6h4.07c.59,0,1.08-.48,1.08-1.08v-7.14c0-.59-.48-1.08-1.08-1.08h-10.96v-6.06h15.77c.59,0,1.08-.48,1.08-1.08v-7.14c0-.59-.48-1.08-1.08-1.08ZM7.73,17.24h15.7v5.84H7.73v-5.84ZM30.32,38.43H7.73v-5.84h22.59v5.84ZM35.46,24.92v5.84H7.73v-5.84h27.74ZM40.27,15.41H7.73v-5.84h32.55v5.84Z"
      style="fill-rule: evenodd"
      :style="{ fill: fillLine }"
    />
  </svg>
</template>

<script>
export default {
  name: 'StatusLinearVertical',
  props: {
    active: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    fill() {
      return this.active ? 'white' : 'var(--chart-type-icon)'
    },
    fillLine() {
      return this.active ? 'white' : 'var(--chart-line-type-icon)'
    },
  },
}
</script>
