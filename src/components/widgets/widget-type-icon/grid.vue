<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="48"
    height="40"
    viewBox="0 0 48 40"
  >
    <path
      d="M41.17,4.93H6.81c-1.27,0-2.3.93-2.3,2.09v5.55h38.94v-5.55c0-1.15-1.03-2.09-2.3-2.09h0Z"
      style="opacity: 0.2"
      :style="{ fill }"
    />
    <path
      d="M39.55,7.84h-5.55c-.61,0-1.11.5-1.11,1.11s.5,1.11,1.11,1.11h5.55c.61,0,1.11-.5,1.11-1.11s-.5-1.11-1.11-1.11ZM14,7.84h-5.55c-.61,0-1.11.5-1.11,1.11s.5,1.11,1.11,1.11h5.55c.61,0,1.11-.5,1.11-1.11s-.5-1.11-1.11-1.11ZM26.22,7.84h-5.55c-.61,0-1.11.5-1.11,1.11s.5,1.11,1.11,1.11h5.55c.61,0,1.11-.5,1.11-1.11s-.5-1.11-1.11-1.11Z"
      style="opacity: 0.8"
      :style="{ fill }"
    />
    <path
      d="M30.87,12.57H4.52v20.25c0,1.24,1.03,2.25,2.3,2.25h34.36c1.27,0,2.3-1.01,2.3-2.25V12.57h-12.61Z"
      style="opacity: 0.1"
      :style="{ fill }"
    />
    <path
      d="M41.65,4.51H6.35c-1.3,0-2.35,1.06-2.35,2.35v26.28c0,1.3,1.06,2.35,2.35,2.35h35.3c1.3,0,2.35-1.06,2.35-2.35V6.86c0-1.3-1.06-2.35-2.35-2.35ZM16.94,34.32H6.35c-.65,0-1.18-.52-1.18-1.18v-5.1h11.76v6.28h0ZM16.94,26.87H5.18v-6.28h11.76v6.28ZM16.94,19.41H5.18v-6.28h11.76v6.28ZM16.94,11.97H5.18v-5.1c0-.65.52-1.18,1.18-1.18h10.59v6.28h0ZM29.89,34.32h-11.76v-6.28h11.76v6.28ZM29.89,26.87h-11.76v-6.28h11.76v6.28ZM29.89,19.41h-11.76v-6.28h11.76v6.28ZM29.89,11.97h-11.76v-6.28h11.76v6.28ZM42.83,33.15c0,.65-.52,1.18-1.18,1.18h-10.59v-6.28h11.76v5.1h0ZM42.83,26.87h-11.76v-6.28h11.76v6.28ZM42.83,19.41h-11.76v-6.28h11.76v6.28ZM42.83,11.97h-11.76v-6.28h10.59c.65,0,1.18.52,1.18,1.18v5.1h0Z"
      :style="{ fill: fillLine }"
    />
    <path
      d="M8.45,17.84h5.55c.61,0,1.11-.5,1.11-1.11s-.5-1.11-1.11-1.11h-5.55c-.61,0-1.11.5-1.11,1.11s.5,1.11,1.11,1.11ZM14,30.07h-5.55c-.61,0-1.11.5-1.11,1.11s.5,1.11,1.11,1.11h5.55c.61,0,1.11-.5,1.11-1.11s-.5-1.11-1.11-1.11ZM14,22.29h-5.55c-.61,0-1.11.5-1.11,1.11s.5,1.11,1.11,1.11h5.55c.61,0,1.11-.5,1.11-1.11s-.5-1.11-1.11-1.11Z"
      style="opacity: 0.5"
      :style="{ fill }"
    />
  </svg>
</template>

<script>
export default {
  name: 'Grid',
  props: {
    active: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    fill() {
      return this.active ? 'white' : 'var(--chart-type-icon)'
    },
    fillLine() {
      return this.active ? 'white' : 'var(--chart-line-type-icon)'
    },
  },
}
</script>
