<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="48"
    height="38"
    viewBox="0 0 48 38"
  >
    <path
      d="M12.24,9.57c-5.2,0-9.43,4.23-9.43,9.44s4.23,9.44,9.43,9.44,9.44-4.23,9.44-9.44-4.23-9.44-9.44-9.44ZM12.24,26.43c-4.1,0-7.43-3.33-7.43-7.42s3.33-7.43,7.43-7.43,7.43,3.33,7.43,7.43-3.33,7.42-7.43,7.42ZM35.76,9.57c-5.2,0-9.43,4.23-9.43,9.44s4.23,9.44,9.43,9.44,9.44-4.23,9.44-9.44-4.23-9.44-9.44-9.44ZM35.76,26.43c-4.09,0-7.42-3.33-7.42-7.42s3.33-7.43,7.42-7.43,7.43,3.33,7.43,7.43-3.33,7.42-7.43,7.42Z"
      style="opacity: 0.4"
      :style="{ fill }"
    />
    <path
      d="M12.24,9.01c-.86,0-1.56.7-1.56,1.56s.7,1.56,1.56,1.56c3.79,0,6.87,3.09,6.87,6.87s-3.09,6.86-6.87,6.86-6.87-3.09-6.87-6.86c0-.86-.7-1.56-1.56-1.56s-1.56.7-1.56,1.56c0,5.5,4.48,9.99,9.98,9.99s9.99-4.48,9.99-9.99-4.48-9.99-9.99-9.99h.01ZM35.76,9.01c-.86,0-1.56.7-1.56,1.56s.7,1.56,1.56,1.56c3.79,0,6.87,3.09,6.87,6.87s-3.09,6.86-6.87,6.86c-.86,0-1.56.7-1.56,1.56s.7,1.56,1.56,1.56c5.5,0,9.99-4.48,9.99-9.99s-4.48-9.99-9.99-9.99h0Z"
      style="opacity: 0.6"
      :style="{ fill }"
    />
  </svg>
</template>

<script>
export default {
  name: 'StatusRadial',
  props: {
    active: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    fill() {
      return this.active ? 'white' : 'var(--chart-type-icon)'
    },
  },
}
</script>
