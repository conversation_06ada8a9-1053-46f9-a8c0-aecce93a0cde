<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="48"
    height="38"
    viewBox="0 0 48 38"
  >
    <path
      d="M4.8,33.61c-.12,0-.25-.03-.36-.09-.39-.2-.54-.69-.34-1.08l5.97-11.56c.11-.21.3-.36.54-.42.24-.05.48,0,.67.15l3.46,2.76,3.85-8.58c.1-.24.32-.41.58-.46s.52.04.71.22l2.68,2.67,5.45-9.3c.14-.25.41-.4.7-.4h0c.29,0,.54.15.7.4l5.19,8.86,3.63-8.1c.18-.4.66-.58,1.06-.4.4.18.58.66.4,1.06l-4.27,9.52c-.12.28-.39.46-.69.48-.3,0-.58-.13-.73-.39l-5.29-9.02-5.29,9.02c-.12.21-.34.36-.59.39-.25.04-.5-.05-.67-.23l-2.59-2.58-3.79,8.47c-.1.23-.3.39-.54.45s-.5,0-.69-.15l-3.5-2.79-5.52,10.7c-.14.28-.42.43-.71.43h0Z"
      style="opacity: 0.8"
      :style="{ fill }"
    />
    <path
      d="M5.87,34.2c-.3,0-.6-.18-.73-.49-.17-.41,0-.88.42-1.05l11.5-4.99,3.29-5.48c.16-.27.46-.42.78-.38l5.27.6,1.54-6.78c.08-.31.32-.55.65-.61s.64.09.81.36l5.33,8.5,3.57-5.12c.25-.36.75-.46,1.11-.**********.75.2,1.11l-4.27,6.12c-.15.22-.38.34-.68.34-.27,0-.51-.15-.66-.37l-4.94-7.89-1.28,5.6c-.1.4-.46.67-.87.62l-5.46-.62-3.15,5.25c-.09.14-.21.26-.37.32l-11.73,5.1c-.1.05-.21.07-.31.07l-.02-.02Z"
      style="opacity: 0.5"
      :style="{ fill }"
    />
    <path
      d="M43.2,34.73H5.87c-1.03,0-1.87-.84-1.87-1.87V4.07c0-.44.36-.8.8-.8s.8.36.8.8v28.8c0,.***********.27h37.33c.44,0,.8.36.8.8s-.36.8-.8.8Z"
      :style="{ fill: fillLine }"
    />
  </svg>
</template>

<script>
export default {
  name: 'LineIcon',
  props: {
    active: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    fill() {
      return this.active ? 'white' : 'var(--chart-type-icon)'
    },
    fillLine() {
      return this.active ? 'white' : 'var(--chart-line-type-icon)'
    },
  },
}
</script>
