<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="48"
    height="38"
    viewBox="0 0 48 38"
  >
    <path
      d="M24.16,2.97l13.88,8.02v16.03l-13.88,8.02-13.88-8.02V10.98L24.16,2.97Z"
      style="opacity: 0.1"
      :style="{ fill }"
    />
    <path
      d="M24,4.26l-12.76,7.37v14.74l12.76,7.37,12.76-7.37v-14.74l-12.76-7.37ZM38.72,10.5L24,2l-14.72,8.5v17l14.72,8.5,14.72-8.5V10.5Z"
      style="fill-rule: evenodd"
      :style="{ fill: fillLine }"
    />
    <path
      d="M16.15,16.38c0-.72.59-1.31,1.31-1.31h13.08c.72,0,1.31.59,1.31,1.31h0c0,.72-.59,1.31-1.31,1.31h-13.08c-.72,0-1.31-.59-1.31-1.31h0Z"
      style="opacity: 0.8"
      :style="{ fill }"
    />
    <path
      d="M16.15,21.62c0-.72.59-1.31,1.31-1.31h7.85c.72,0,1.31.59,1.31,1.31h0c0,.72-.59,1.31-1.31,1.31h-7.85c-.72,0-1.31-.59-1.31-1.31h0Z"
      style="opacity: 0.6"
      :style="{ fill }"
    />
  </svg>
</template>

<script>
export default {
  name: 'HeatmapDetail',
  props: {
    active: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    fill() {
      return this.active ? 'white' : 'var(--chart-type-icon)'
    },
    fillLine() {
      return this.active ? 'white' : 'var(--chart-line-type-icon)'
    },
  },
}
</script>
