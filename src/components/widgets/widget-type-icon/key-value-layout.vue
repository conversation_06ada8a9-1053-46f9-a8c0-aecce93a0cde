<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="48"
    height="38"
    viewBox="0 0 48 38"
  >
    <path
      d="M29.86,11.13h12.38c.97,0,1.76-.79,1.76-1.76s-.79-1.76-1.76-1.76h-12.38c-.97,0-1.76.79-1.76,1.76s.79,1.76,1.76,1.76ZM42.24,27.71h-12.38c-.97,0-1.76.79-1.76,1.76s.79,1.76,1.76,1.76h12.38c.97,0,1.76-.79,1.76-1.76s-.79-1.76-1.76-1.76Z"
      style="opacity: 0.6"
      :style="{ fill }"
    />
    <path
      d="M6.33,25.53c-.56.44-1.33.84-2.33,1.16v2.42c.68-.21,1.25-.43,1.69-.65.44-.22.91-.52,1.4-.89v7.07h2.98v-10.78h-2.43c-.31.67-.74,1.24-1.3,1.68ZM6.33,4.89c-.56.44-1.33.84-2.33,1.16v2.42c.68-.21,1.25-.43,1.69-.65.44-.22.91-.52,1.4-.89v7.08h2.98V3.21h-2.43c-.31.67-.74,1.24-1.3,1.68ZM16.87,8.81c-1.41,1.05-2.37,1.95-2.89,2.73-.52.78-.83,1.59-.93,2.47h8.86v-2.41h-4.61c.27-.26.51-.47.7-.64.2-.17.59-.46,1.19-.86,1-.7,1.68-1.35,2.06-1.94.38-.59.57-1.21.57-1.85,0-.61-.16-1.16-.49-1.64s-.78-.85-1.36-1.09c-.58-.23-1.42-.36-2.42-.36s-1.77.12-2.33.37c-.57.25-1,.62-1.31,1.07-.31.46-.53,1.11-.64,1.94l2.95.23c.09-.59.23-1.01.48-1.25.25-.23.57-.36.93-.36s.67.12.9.35c.23.22.36.51.36.83,0,.3-.12.62-.36.95-.23.33-.79.82-1.66,1.46ZM21.07,28.55c-.65-.68-1.45-1.01-2.4-1.01-.48,0-.91.09-1.28.26-.37.17-.73.43-1.09.79.1-1.14.23-1.88.43-2.22.26-.48.63-.73,1.11-.73.26,0,.49.1.67.26.17.16.31.46.38.86l2.93-.36h0c-.17-.58-.42-1.06-.74-1.43-.32-.37-.73-.65-1.22-.85-.49-.2-1.16-.3-2-.3-1.42,0-2.54.44-3.35,1.33-.8.89-1.21,2.3-1.21,4.21,0,1.31.19,2.36.58,*********.9,1.37,1.54,**********,1.49.54,**********,0,1.62-.15,2.2-.44.58-.3,1.04-.73,1.37-1.31.33-.58.49-1.21.49-1.91,0-1.04-.32-1.89-.98-2.57ZM18.87,32.53c-.27.3-.59.44-.98.44-.42,0-.78-.17-1.06-.49-.28-.32-.43-.78-.43-1.35h.01c0-.57.15-1,.42-1.31.27-.31.63-.46,1.03-.46s.73.15,1,.47c.***********.41,1.37s-.12,1.03-.4,1.32Z"
      :style="{ fill }"
    />
  </svg>
</template>

<script>
export default {
  name: 'KeyValueLayout',
  props: {
    active: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    fill() {
      return this.active ? 'white' : 'var(--chart-type-icon)'
    },
  },
}
</script>
