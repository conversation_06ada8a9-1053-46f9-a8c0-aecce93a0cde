<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="48"
    height="38"
    viewBox="0 0 48 38"
  >
    <path
      d="M43.2,34.33H4.8c-.44,0-.8-.36-.8-.8s.36-.8.8-.8h38.4c.44,0,.8.36.8.8s-.36.8-.8.8Z"
      :style="{ fill: fillLine }"
    />
    <rect
      x="20.39"
      y="15.4"
      width="6.4"
      height="14.93"
      rx="1.07"
      ry="1.07"
      style="opacity: 0.6"
      :style="{ fill }"
    />
    <rect
      x="31.05"
      y="9"
      width="6.4"
      height="21.33"
      rx="1.07"
      ry="1.07"
      style="opacity: 0.4"
      :style="{ fill }"
    />
    <rect
      x="9.72"
      y="3.67"
      width="6.4"
      height="26.67"
      rx="1.07"
      ry="1.07"
      style="opacity: 0.8"
      :style="{ fill }"
    />
  </svg>
</template>

<script>
export default {
  name: 'VerticalBar',
  props: {
    active: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    fill() {
      return this.active ? 'white' : 'var(--chart-type-icon)'
    },
    fillLine() {
      return this.active ? 'white' : 'var(--chart-line-type-icon)'
    },
  },
}
</script>
