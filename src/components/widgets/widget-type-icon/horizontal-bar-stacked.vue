<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="48"
    height="38"
    viewBox="0 0 48 38"
  >
    <path
      d="M4.83,35.23c-.46,0-.83-.37-.83-.83V3.6c0-.46.37-.83.83-.83s.83.37.83.83v30.8c0,.46-.37.83-.83.83Z"
      :style="{ fill: fillLine }"
    />
    <path
      d="M12.1,15.7h-4.4v6.6h4.4v-6.6ZM15.4,4.7h-7.7v6.6h7.7v-6.6ZM7.7,26.7v6.6h8.8v-6.6H7.7Z"
      style="opacity: 0.8"
      :style="{ fill }"
    />
    <path
      d="M27.5,4.7h-12.1v6.6h12.1v-6.6ZM20.9,15.7h-8.8v6.6h8.8v-6.6ZM16.5,26.7v6.6h13.2v-6.6h-13.2Z"
      style="opacity: 0.6"
      :style="{ fill }"
    />
    <path
      d="M42.9,4.7h-15.4v6.6h15.4c.61,0,1.1-.49,1.1-1.1v-4.4c0-.61-.49-1.1-1.1-1.1ZM30.8,21.2v-4.4c0-.61-.49-1.1-1.1-1.1h-8.8v6.6h8.8c.61,0,1.1-.49,1.1-1.1ZM39.6,26.7h-9.9v6.6h9.9c.61,0,1.1-.49,1.1-1.1v-4.4c0-.61-.49-1.1-1.1-1.1Z"
      style="opacity: 0.4"
      :style="{ fill }"
    />
  </svg>
</template>

<script>
export default {
  name: 'HorizontalBarStacked',
  props: {
    active: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    fill() {
      return this.active ? 'white' : 'var(--chart-type-icon)'
    },
    fillLine() {
      return this.active ? 'white' : 'var(--chart-line-type-icon)'
    },
  },
}
</script>
