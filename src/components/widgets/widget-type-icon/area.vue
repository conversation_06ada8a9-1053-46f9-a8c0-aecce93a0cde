<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="48"
    height="38"
    viewBox="0 0 48 38"
  >
    <path
      d="M7.74,29.6l-3.35-3.16v7.87h39.39V14.07l-5.86,6.56-4.26-4.38-5.83,8.57-5.03-4.87-5.65,8.87-4.5-4.01-4.92,4.76v.02Z"
      style="opacity: 0.5"
      :style="{ fill }"
    />
    <path
      d="M8.66,22.55h-.06c-.26-.02-.5-.15-.63-.37l-3.7-5.65c-.25-.38-.14-.89.24-1.13s.89-.14,1.13.24l3.1,4.74,3.55-4.1c.14-.17.35-.27.57-.29.22-.02.44.06.6.21l3.54,3.19,4.8-7.4c.13-.21.36-.34.61-.37.25-.03.5.07.67.25l4.05,4.15,4.99-8.43c.14-.24.38-.38.66-.4.26,0,.53.1.7.31l4.38,5.66,4.63-7.72c.23-.39.73-.51,1.12-.29s.51.73.29,1.12l-5.25,8.75c-.14.23-.38.38-.66.4-.29.02-.53-.1-.7-.31l-4.37-5.66-4.92,8.3c-.13.22-.35.36-.61.4-.26.03-.5-.06-.69-.24l-4.08-4.19-4.76,7.34c-.13.2-.34.33-.58.36-.24.04-.48-.04-.66-.2l-3.64-3.27-3.71,4.29c-.15.18-.38.29-.62.29Z"
      style="opacity: 0.8"
      :style="{ fill }"
    />
    <path
      d="M43.14,35.14H5.91c-1.06,0-1.91-.86-1.91-1.91V3.68c0-.46.37-.82.82-.82s.82.37.82.82v29.54c0,.***********.28h37.23c.46,0,.82.37.82.82s-.37.82-.82.82h0Z"
      :style="{ fill: fillLine }"
    />
  </svg>
</template>

<script>
export default {
  name: 'Area',
  props: {
    active: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    fill() {
      return this.active ? 'white' : 'var(--chart-type-icon)'
    },
    fillLine() {
      return this.active ? 'white' : 'var(--chart-line-type-icon)'
    },
  },
}
</script>
