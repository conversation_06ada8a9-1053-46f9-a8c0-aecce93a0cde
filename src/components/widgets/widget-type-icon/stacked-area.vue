<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="48"
    height="38"
    viewBox="0 0 48 38"
  >
    <path
      d="M4.39,20.02l8.55-9.57,8.17,7.21,8.61-13.12,6.84,8.39,6.64-4.61v25.78H4.39v-14.07Z"
      style="opacity: 0.3"
      :style="{ fill }"
    />
    <path
      d="M4.39,31.11l8.74-6.85,7.14,5.6,8.81-11.46,7.66,7.52,6.44-4.04v12.21H4.39v-2.98Z"
      style="opacity: 0.4; fill: var(--chart-type-icon)"
      :style="{ fill }"
    />
    <path
      d="M4.93,20.35c-.19,0-.38-.07-.53-.2-.33-.3-.37-.81-.07-1.14l8.08-9.16c.29-.33.8-.37,1.14-.08l7.39,6.41,8.11-12.17c.14-.21.38-.35.63-.36.25-.01.5.1.67.3l6.52,8.03,5.31-3.86c.36-.26.87-.18,1.13.18.26.36.18.87-.18,1.13l-5.93,4.31c-.35.25-.83.19-1.1-.14l-6.31-7.77-8.01,12.02c-.13.19-.33.32-.56.35-.23.03-.46-.04-.64-.19l-7.48-6.48-7.55,8.56c-.16.18-.38.27-.61.27Z"
      style="opacity: 0.8; fill: var(--chart-type-icon)"
      :style="{ fill }"
    />
    <path
      d="M5.47,31.12c-.24,0-.48-.11-.64-.31-.28-.35-.21-.86.14-1.13l7.54-5.93c.29-.23.7-.23.99,0l6.38,4.91,8.67-10.7c.14-.18.36-.29.59-.3.23,0,.45.07.61.24l7.09,7.09,5.38-3.42c.37-.24.88-.13,**********.38.13.88-.25,1.12l-5.93,3.77c-.32.2-.74.16-1.01-.11l-6.91-6.91-8.6,10.62c-.28.34-.77.4-1.12.13l-6.51-5.01-7.05,5.54c-.15.12-.32.17-.5.17Z"
      style="opacity: 0.8"
      :style="{ fill }"
    />
    <path
      d="M42.11,34.9H5.89c-1.04,0-1.89-.85-1.89-1.89V3.91c0-.45.36-.81.81-.81s.81.36.81.81v29.1c0,.***********.27h36.23c.15,0,.27-.12.27-.27V8.22c0-.45.36-.81.81-.81s.81.36.81.81v24.79c0,1.04-.85,1.89-1.89,1.89Z"
      :style="{ fill }"
    />
  </svg>
</template>

<script>
export default {
  name: 'Area',
  props: {
    active: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    fill() {
      return this.active ? 'white' : 'var(--chart-type-icon)'
    },
    fillLine() {
      return this.active ? 'white' : 'var(--chart-line-type-icon)'
    },
  },
}
</script>
