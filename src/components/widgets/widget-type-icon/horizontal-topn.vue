<template>
  <svg
    id="uuid-6e83bc29-d4d9-4a72-a214-1f7f2e1c7720"
    xmlns="http://www.w3.org/2000/svg"
    width="48"
    height="48"
    viewBox="0 0 48 48"
  >
    <path d="m40.17,23.82h-16.56v16.34h16.56v-16.34Z" :style="{ fill }" />
    <path
      d="m40.97,21.57h-17.94c-.79,0-1.44.64-1.44,1.44v17.94c0,.79.64,1.44,1.44,1.44h17.94c.79,0,1.44-.64,1.44-1.44v-17.94c0-.79-.64-1.44-1.44-1.44Zm-1.25,18.13v-.02h-15.24l4.44-4.67,2.47,2.1c.54.46,1.34.42,1.84-.08l4.64-4.76c.52-.53.51-1.39-.02-1.91-.53-.52-1.39-.51-1.91.02l-3.76,3.86-2.48-2.11c-.54-.46-1.36-.42-1.85.1l-3.55,3.74v-11.69h15.42v15.42Zm-.46-31.96c0-1.18-.96-2.14-2.14-2.14H7.73c-1.18,0-2.14.96-2.14,2.14v29.37c0,1.18.96,2.14,2.14,2.14h10.96v-20.57h20.56V7.75Zm-23.26,28.81h-7.7v-7.47h7.7v7.47Zm0-10.17h-7.7v-7.7h7.7v7.7Zm0-10.4h-7.7v-7.68h7.7v7.68Zm10.39,0h-7.69v-7.68h7.69v7.68Zm10.17,0h-7.47v-7.68h7.47v7.68Z"
      :style="{ fill }"
    />
  </svg>
</template>

<script>
export default {
  name: 'HorizontalTopn',
  props: {
    active: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    fill() {
      return this.active ? 'white' : 'var(--page-text-color)'
    },
  },
}
</script>
