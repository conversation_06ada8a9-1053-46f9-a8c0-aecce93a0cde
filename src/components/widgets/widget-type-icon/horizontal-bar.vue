<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="48"
    height="38"
    viewBox="0 0 48 38"
  >
    <path
      d="M4.83,35.23c-.46,0-.83-.37-.83-.83V3.6c0-.46.37-.83.83-.83s.83.37.83.83v30.8c0,.46-.37.83-.83.83Z"
      :style="{ fill: fillLine }"
    />
    <rect
      x="7.91"
      y="15.7"
      width="18.95"
      height="6.6"
      rx="1.1"
      ry="1.1"
      style="opacity: 0.6"
      :style="{ fill }"
    />
    <rect
      x="7.91"
      y="26.7"
      width="28.43"
      height="6.6"
      rx="1.1"
      ry="1.1"
      style="opacity: 0.4"
      :style="{ fill }"
    />
    <rect
      x="7.7"
      y="4.7"
      width="36.3"
      height="6.6"
      rx="1.1"
      ry="1.1"
      style="opacity: 0.8"
      :style="{ fill }"
    />
  </svg>
</template>

<script>
export default {
  name: 'HorizontalBar',
  props: {
    active: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    fill() {
      return this.active ? 'white' : 'var(--chart-type-icon)'
    },
    fillLine() {
      return this.active ? 'white' : 'var(--chart-line-type-icon)'
    },
  },
}
</script>
