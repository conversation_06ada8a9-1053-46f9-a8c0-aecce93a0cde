<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="48"
    height="38"
    viewBox="0 0 48 38"
  >
    <polygon
      points="34 15.56 32.36 15.56 21.28 15.56 21.28 4.44 5 4.44 5 33.56 20.67 33.56 22.28 33.56 33 33.56 33 29.54 43 29.54 43 15.56 34 15.56"
      style="opacity: 0.1"
      :style="{ fill }"
    />
    <path
      d="M33.36,33.56h9.64v-4.67h-9.64v4.67ZM21.67,4.44v10.74h21.33V4.44h-21.33Z"
      style="opacity: 0.2"
      :style="{ fill }"
    />
    <path
      d="M22.33,3.44H4v31.11h40V3.44h-21.67ZM20.66,32.89H5.67V5.11h15v27.78ZM32.33,32.89h-10v-16.67h10v16.67ZM42.33,32.89h-8.31v-3.33h8.31v3.33ZM42.33,27.88h-8.31v-11.65h8.31v11.65ZM42.33,14.52h-20V5.11h20v9.41Z"
      :style="{ fill: fillLine }"
    />
  </svg>
</template>

<script>
export default {
  name: 'TreeView',
  props: {
    active: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    fill() {
      return this.active ? 'white' : 'var(--chart-type-icon)'
    },
    fillLine() {
      return this.active ? 'white' : 'var(--chart-line-type-icon)'
    },
  },
}
</script>
