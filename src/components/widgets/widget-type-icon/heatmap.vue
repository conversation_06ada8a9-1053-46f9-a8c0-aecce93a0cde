<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="48"
    height="38"
    viewBox="0 0 48 38"
  >
    <path
      d="M24,3.08l13.79,7.96v15.92l-13.79,7.96-13.79-7.96v-15.92l13.79-7.96Z"
      style="opacity: 0.1"
      :style="{ fill }"
    />
    <path
      d="M24,4.26l-12.76,7.37v14.74l12.76,7.37,12.76-7.37v-14.74l-12.76-7.37ZM38.72,10.5L24,2l-14.72,8.5v17l14.72,8.5,14.72-8.5V10.5Z"
      style="fill-rule: evenodd"
      :style="{ fill: fillLine }"
    />
  </svg>
</template>

<script>
export default {
  name: 'HeatmapIcon',
  props: {
    active: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    fill() {
      return this.active ? 'white' : 'var(--chart-type-icon)'
    },
    fillLine() {
      return this.active ? 'white' : 'var(--chart-line-type-icon)'
    },
  },
}
</script>
