<template>
  <!-- <svg
    xmlns="http://www.w3.org/2000/svg"
    width="48"
    height="38"
    viewBox="0 0 48 38"
  >
    <rect
      x="6"
      y="5"
      width="36"
      height="28"
      rx="1"
      ry="1"
      style="opacity: 0.4"
      :style="{ fill }"
    />
    <path
      d="M13.5,15.6h1.36v8.4h2.02v-10.21h-3.37v1.81ZM23.46,20.47c.5-.49.95-1.07,1.34-1.74.39-.66.59-1.36.59-2.09,0-.92-.28-1.67-.85-2.23-.57-.57-1.39-.85-2.45-.85-.97,0-1.77.29-2.41.87-.63.58-.96,1.43-.99,2.55h1.9c0-.54.14-.97.39-1.27.25-.32.61-.48,1.08-.48s.81.14,1.04.41c.22.27.34.64.34,1.12,0,.52-.18,1.05-.53,1.6-.35.54-.8,1.06-1.33,1.57-.53.49-1.25,1.12-2.14,1.86l-.73.62v1.46h6.92v-1.62h-4.13c.81-.69,1.47-1.28,1.97-1.76ZM33.22,19.52c-.3-.43-.72-.73-1.26-.9v-.06c.42-.14.78-.41,1.08-.81.31-.41.46-.91.46-1.5,0-.51-.13-.97-.38-1.37-.24-.41-.61-.73-1.09-.97-.49-.24-1.07-.36-1.75-.36-1,0-1.81.26-2.42.77-.61.5-.93,1.22-.98,2.16h1.92c.04-.36.17-.66.41-.9s.58-.36,1.02-.36.79.11,1.02.34c.24.22.36.54.36.94,0,.89-.66,1.33-1.99,1.33h-.41v1.61h.41c.75,0,1.3.12,1.65.36.35.24.53.64.53,1.2,0,.42-.13.77-.39,1.04-.25.26-.62.39-1.09.39-.51,0-.92-.14-1.22-.41-.29-.28-.44-.65-.46-1.11h-1.9c.04,1.04.39,1.82,1.05,2.35.66.53,1.52.8,2.58.8.7,0,1.3-.12,1.79-.36.5-.25.88-.6,1.13-1.04.26-.44.39-.94.39-1.5,0-.67-.15-1.22-.46-1.65Z"
      style="opacity: 0.8"
      :style="{ fill }"
    />
  </svg> -->
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="48"
    height="38"
    viewBox="0 0 48 38"
  >
    <path
      d="M6.67,7.52c0-1.1.9-2,2-2h30.66c1.1,0,2,.9,2,2v22.95c0,1.1-.9,2-2,2H8.67c-1.1,0-2-.9-2-2V7.52Z"
      style="opacity: 0.1"
      :style="{ fill }"
    />
    <path
      d="M14.15,26.57c0-.41.29-.75.66-.75h3.28c.36,0,.66.34.66.75h0c0,.41-.29.75-.66.75h-3.28c-.36,0-.66-.34-.66-.75h0Z"
      style="opacity: 0.8"
      :style="{ fill }"
    />
    <path
      d="M40,6.5H8c-.28,0-.5.22-.5.5v24c0,.28.22.5.5.5h32c.28,0,.5-.22.5-.5V7c0-.28-.22-.5-.5-.5ZM8,5c-1.1,0-2,.9-2,2v24c0,1.1.9,2,2,2h32c1.1,0,2-.9,2-2V7c0-1.1-.9-2-2-2H8Z"
      style="fill-rule: evenodd"
      :style="{ fill: fillLine }"
    />
    <path
      d="M13.5,12.74h1.36v8.4h2.02v-10.21h-3.37v1.81ZM33.22,16.66c-.3-.43-.72-.73-1.26-.9v-.06c.42-.14.78-.41,1.08-.81.31-.41.46-.91.46-1.5,0-.51-.13-.97-.38-1.37-.24-.41-.61-.73-1.09-.97-.49-.24-1.07-.36-1.75-.36-1,0-1.81.26-2.42.77-.61.5-.93,1.22-.98,2.16h1.92c.04-.36.17-.66.41-.9.24-.24.58-.36,1.02-.36s.79.11,1.02.34c.24.22.36.54.36.94,0,.89-.66,1.33-1.99,1.33h-.41v1.61h.41c.75,0,1.3.12,1.65.36.35.24.53.64.53,1.2,0,.42-.13.77-.39,1.04-.25.26-.62.39-1.09.39-.51,0-.92-.14-1.22-.41-.29-.28-.44-.65-.46-1.11h-1.9c.04,1.04.39,1.82,1.05,2.35.66.53,1.52.8,2.58.8.7,0,1.3-.12,1.79-.36.5-.25.88-.6,1.13-1.04.26-.44.39-.94.39-1.5,0-.67-.15-1.22-.46-1.65ZM23.46,17.61c.5-.49.95-1.07,1.34-1.74.39-.66.59-1.36.59-2.09,0-.92-.28-1.67-.85-2.23-.57-.57-1.39-.85-2.45-.85-.97,0-1.77.29-2.41.87-.63.58-.96,1.43-.99,2.55h1.9c0-.54.14-.97.39-1.27.25-.32.61-.48,1.08-.48s.81.14,1.04.41.34.64.34,1.12c0,.52-.18,1.05-.53,1.6-.35.54-.8,1.06-1.33,1.57-.53.49-1.25,1.12-2.14,1.86l-.73.62v1.46h6.92v-1.62h-4.13c.81-.69,1.47-1.28,1.97-1.76Z"
      style="opacity: 0.6"
      :style="{ fill }"
    />
    <path
      d="M21.7,26.57c0-.41.29-.75.66-.75h3.28c.36,0,.66.34.66.75h0c0,.41-.29.75-.66.75h-3.28c-.36,0-.66-.34-.66-.75h0Z"
      style="opacity: 0.6"
      :style="{ fill }"
    />
    <path
      d="M29.25,26.57c0-.41.29-.75.66-.75h3.28c.36,0,.66.34.66.75h0c0,.41-.29.75-.66.75h-3.28c-.36,0-.66-.34-.66-.75h0Z"
      style="opacity: 0.4"
      :style="{ fill }"
    />
  </svg>
</template>

<script>
export default {
  name: 'MetroTile',
  props: {
    active: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    fill() {
      return this.active ? 'white' : 'var(--chart-type-icon)'
    },
    fillLine() {
      return this.active ? 'white' : 'var(--chart-line-type-icon)'
    },
  },
}
</script>
