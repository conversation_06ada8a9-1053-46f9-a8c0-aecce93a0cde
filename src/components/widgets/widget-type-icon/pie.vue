<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="48"
    height="38"
    viewBox="0 0 48 38"
  >
    <path
      d="M37.97,20.9c0,3.14-.73,4.89-2.48,7.5s-3.69,4.38-6.59,5.58-5.5,1.72-8.58,1.11-5.74-1.89-7.96-4.11c-2.22-2.22-3.69-4.62-4.3-7.7-.61-3.08-.44-5.77.76-8.67,1.2-2.9,2.86-5.04,5.47-6.78,2.61-1.74,4.49-2.61,7.63-2.61l.26,15.78,15.78-.1h0Z"
      style="opacity: 0.4"
      :style="{ fill }"
    />
    <path
      d="M22.87,4.27c-.61,0-1.22.04-1.82.1-2.49.29-4.89,1.17-6.99,2.57-2.61,1.74-4.64,4.22-5.84,7.12-1.2,2.9-1.52,6.09-.9,9.17.61,3.08,2.12,5.91,4.34,8.12,2.22,2.22,5.05,3.73,8.12,4.34,3.08.61,6.27.3,9.17-.9s5.38-3.23,7.12-5.84c1.4-2.1,2.28-4.5,2.57-6.99.07-.6.1-1.21.1-1.82h-15.87s0-15.87,0-15.87ZM36.79,21.96c-.28,2.13-1.04,4.18-2.25,5.98-1.54,2.31-3.74,4.11-6.3,5.17-2.57,1.06-5.39,1.34-8.11.8s-5.23-1.88-7.19-3.84c-1.96-1.96-3.3-4.47-3.84-7.19-.54-2.72-.26-5.55.8-8.11,1.06-2.57,2.86-4.76,5.17-6.3,1.8-1.2,3.85-1.97,5.98-2.25v15.75h15.75-.01ZM40.9,16.04c-.17-1.46-.54-2.89-1.1-4.25-.8-1.92-1.97-3.67-3.44-5.15-1.47-1.47-3.22-2.64-5.15-3.44-1.36-.56-2.79-.93-4.25-1.1-.6-.07-1.21-.1-1.82-.1v15.87h15.87c0-.61-.04-1.22-.1-1.82h-.01ZM26.95,16.04V3.94c1.22.16,2.41.48,3.55.95,1.7.71,3.25,1.74,4.56,3.04,1.3,1.3,2.34,2.85,3.04,4.56.47,1.14.79,2.34.95,3.55h-12.11,0Z"
      :style="{ fill: fillLine }"
    />
  </svg>
</template>

<script>
export default {
  name: 'Pie',
  props: {
    active: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    fill() {
      return this.active ? 'white' : 'var(--chart-type-icon)'
    },
    fillLine() {
      return this.active ? 'white' : 'var(--chart-line-type-icon)'
    },
  },
}
</script>
