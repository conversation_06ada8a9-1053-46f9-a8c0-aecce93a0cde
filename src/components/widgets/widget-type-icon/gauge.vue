<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="48"
    height="38"
    viewBox="0 0 48 38"
  >
    <path
      d="M23.83,2.87c3.32,0,6.31.68,9.08,2.51,2.77,1.82,4.95,4.42,6.27,7.46l-9.42,3.15c-.45-1.05-1.21-1.95-2.16-2.58-.96-.63-2.45-1.04-3.59-1.04l-.17-9.51Z"
      style="opacity: 0.8"
      :style="{ fill }"
    />
    <path
      d="M38.94,12.25c1.51,3.49,1.81,7.39.84,11.07-.97,3.68-2.62,6.2-5.65,8.5-3.03,2.3-6.75,3.51-10.55,3.45-3.8-.06-6.82-1.3-9.78-3.7l6.46-7.36c1.02.83,2.29,1.29,3.6,1.31,1.31.02,2.86-.37,3.9-1.17,1.05-.79,2.26-2.59,2.59-3.86s.23-2.62-.29-3.82l8.88-4.42Z"
      style="opacity: 0.4"
      :style="{ fill }"
    />
    <path
      d="M24,2C14.61,2,7,9.61,7,19s7.61,17,17,17,17-7.61,17-17S33.39,2,24,2ZM24.91,3.87c5.61.33,10.38,3.7,12.72,8.49l-6.79,3.14c-1.14-2.23-3.34-3.82-5.93-4.13V3.87ZM29.87,19c0,3.24-2.63,5.87-5.87,5.87s-5.87-2.63-5.87-5.87,2.63-5.87,5.87-5.87,5.87,2.63,5.87,5.87ZM8.82,19c0-8.08,6.31-14.66,14.27-15.13v7.5c-3.82.45-6.78,3.69-6.78,7.63,0,2.13.87,4.06,2.27,5.46l-4.74,5.8c-3.07-2.78-5.02-6.78-5.02-11.25ZM24,34.18c-3.26,0-6.27-1.04-8.74-2.78l4.76-5.82c1.16.71,2.52,1.12,3.98,1.12,4.25,0,7.69-3.44,7.69-7.69,0-.62-.08-1.22-.22-1.79l6.86-3.17c.54,1.56.85,3.22.85,4.96,0,8.38-6.8,15.18-15.18,15.18Z"
      :style="{ fill: fillLine }"
    />
  </svg>
</template>

<script>
export default {
  name: 'SolidGauge',
  props: {
    active: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    fill() {
      return this.active ? 'white' : 'var(--chart-type-icon)'
    },
    fillLine() {
      return this.active ? 'white' : 'var(--chart-line-type-icon)'
    },
  },
}
</script>
