<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="48"
    height="38"
    viewBox="0 0 48 38"
  >
    <path
      d="M11.72,12.28h24.56c3.71,0,6.72,3.01,6.72,6.72s-3.01,6.72-6.72,6.72H11.72c-3.71,0-6.72-3.01-6.72-6.72s3.01-6.72,6.72-6.72ZM11.72,13.83c-2.86,0-5.17,2.31-5.17,5.17s2.31,5.17,5.17,5.17h24.56c2.86,0,5.17-2.31,5.17-5.17s-2.31-5.17-5.17-5.17H11.72Z"
      style="fill-rule: evenodd"
      :style="{ fill: fillLine }"
    />
    <path
      d="M6.55,19c0-2.86,2.31-5.17,5.17-5.17h13.11c2.86,0,5.17,2.31,5.17,5.17h0c0,2.86-2.31,5.17-5.17,5.17h-13.11c-2.86,0-5.17-2.31-5.17-5.17h0Z"
      style="opacity: 0.4"
      :style="{ fill }"
    />
  </svg>
</template>

<script>
export default {
  name: 'StatusLinearHorizontal',
  props: {
    active: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    fill() {
      return this.active ? 'white' : 'var(--chart-type-icon)'
    },
    fillLine() {
      return this.active ? 'white' : 'var(--chart-line-type-icon)'
    },
  },
}
</script>
