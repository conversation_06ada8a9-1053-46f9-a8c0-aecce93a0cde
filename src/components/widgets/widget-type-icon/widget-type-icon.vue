<template>
  <MTooltip v-if="tooltip" class="flex">
    <template v-slot:trigger>
      <div :style="widgetStyle">
        <component :is="widgetIcon" class="w-full h-full" :active="selected" />
      </div>
    </template>
    {{ tooltipText }}
  </MTooltip>
  <div v-else :style="widgetStyle">
    <component :is="widgetIcon" class="w-full h-full" :active="selected" />
  </div>
</template>

<script>
import KebabCase from 'lodash/kebabCase'
import UpperFirst from 'lodash/upperFirst'
import GridSvg from './grid.vue'
import HorizontalBar from './horizontal-bar.vue'
import Heatmap from './heatmap.vue'
import HorizontalBarStacked from './horizontal-bar-stacked.vue'
import VerticalBar from './vertical-bar.vue'
import VerticalBarStacked from './vertical-bar-stacked.vue'
import StackedLine from './stacked-line.vue'
import StackedArea from './stacked-area.vue'
import Area from './area.vue'
import Pie from './pie.vue'
import Line from './line.vue'
import MetroTile from './metro-tile.vue'
import SolidGauge from './gauge.vue'
import SankeySvg from './sankey.vue'
import StatusLinearHorizontal from './status-linear-horizontal.vue'
import StatusLinearVertical from './status-linear-vertical.vue'
import StatusRadil from './status-radial.vue'
import HorizontalTopn from './horizontal-topn.vue'
import MapView from './map-view.vue'
import TreeView from './tree-view.vue'
import HeatmapPlain from './heatmap-plain.vue'
import HeatmapDetail from './heatmap-detail.vue'
import { WidgetTypeConstants } from '../constants'
import KeyValueSvg from './key-value-layout.vue'
import FreeText from './free-text.vue'

export default {
  name: 'WidgetTypeIcon',
  props: {
    selected: {
      type: Boolean,
      default: false,
    },
    size: {
      type: Number,
      default: 50,
    },
    category: {
      type: String,
      default: undefined,
    },
    widgetType: {
      type: String,
      default: undefined,
    },
    tooltip: {
      type: String,
      default: undefined,
    },
    isAvailabilityWidget: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    tooltipText() {
      const tooltip = this.tooltip
      if (tooltip) {
        return KebabCase(tooltip)
          .replace(/-/g, ' ')
          .replace('100', '')
          .split(' ')
          .map(UpperFirst)
          .join(' ')
      }
      return undefined
    },
    widgetStyle() {
      const size = this.size
      return {
        width: `${size}px`,
        height: `${size}px`,
      }
    },
    widgetIcon() {
      const category = this.category
      const widgetType = this.widgetType
      const isAvailabilityWidget = this.isAvailabilityWidget
      if (category === WidgetTypeConstants.GAUGE && isAvailabilityWidget) {
        return Pie
      }

      if (category === WidgetTypeConstants.FREE_TEXT) {
        return FreeText
      }
      if (category === WidgetTypeConstants.HEATMAP) {
        if (widgetType === WidgetTypeConstants.HEATMAP_PLAIN) {
          return HeatmapPlain
        } else if (widgetType === WidgetTypeConstants.HEATMAP_WITH_HOST) {
          return HeatmapDetail
        }
        return Heatmap
      } else if (widgetType === WidgetTypeConstants.HEATMAP_PLAIN) {
        return HeatmapPlain
      } else if (widgetType === WidgetTypeConstants.HEATMAP_WITH_HOST) {
        return HeatmapDetail
      } else if (category === WidgetTypeConstants.SANKEY) {
        return SankeySvg
      } else if (
        category === WidgetTypeConstants.GRID ||
        widgetType === WidgetTypeConstants.GRID
      ) {
        return GridSvg
      } else if (
        category === WidgetTypeConstants.KEY_VALUE_LAYOUT ||
        widgetType === WidgetTypeConstants.KEY_VALUE_LAYOUT
      ) {
        return KeyValueSvg
      } else if (widgetType === WidgetTypeConstants.HORIZONTAL_BAR) {
        return HorizontalBar
      } else if (widgetType === WidgetTypeConstants.STACKED_HORIZONTAL_BAR) {
        return HorizontalBarStacked
      } else if ([WidgetTypeConstants.VERTICAL_BAR].includes(widgetType)) {
        return VerticalBar
      } else if (widgetType === WidgetTypeConstants.STACKED_VERTICAL_BAR) {
        return VerticalBarStacked
      } else if (widgetType === WidgetTypeConstants.STACKED_LINE) {
        return StackedLine
      } else if (widgetType === WidgetTypeConstants.LINE) {
        return Line
      } else if (widgetType === WidgetTypeConstants.STACKED_AREA) {
        return StackedArea
      } else if (widgetType === WidgetTypeConstants.AREA) {
        return Area
      } else if (widgetType === WidgetTypeConstants.PIE) {
        return Pie
      } else if (widgetType === WidgetTypeConstants.SOLID_GAUGE) {
        return SolidGauge
      } else if (widgetType === WidgetTypeConstants.METRO_TILE) {
        return MetroTile
      } else if (widgetType === WidgetTypeConstants.METRIC) {
        return GridSvg
      } else if (widgetType === WidgetTypeConstants.RADIAL_VIEW) {
        return StatusRadil
      } else if (widgetType === WidgetTypeConstants.PROGRESS_WITH_COUNT_VIEW) {
        return StatusLinearHorizontal
      } else if (
        widgetType === WidgetTypeConstants.HORIZONTAL_BAR_WITH_COUNT_VIEW
      ) {
        return StatusLinearVertical
      } else if (widgetType === WidgetTypeConstants.HORIZONTAL_TOPN) {
        return HorizontalTopn
      } else if (widgetType === WidgetTypeConstants.MAP_VIEW) {
        return MapView
      } else if (widgetType === WidgetTypeConstants.TREE_VIEW) {
        return TreeView
      } else if (widgetType === WidgetTypeConstants.ONLINE_MAP) {
        return MapView
      } else if (widgetType === WidgetTypeConstants.STREAM) {
        return GridSvg
      } else if (widgetType === WidgetTypeConstants.TOPN_SOLID_GAUGE_VIEW) {
        return SolidGauge
      } else if (widgetType === WidgetTypeConstants.KPI_GAUGE) {
        return SolidGauge
      }
      return GridSvg
    },
  },
}
</script>
