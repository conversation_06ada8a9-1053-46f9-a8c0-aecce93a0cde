<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="60"
    height="30"
    viewBox="0 0 60 30"
  >
    <g>
      <path
        d="M40.61,4l.45,9.38h-1.14c-.22-1.65-.52-2.83-.9-3.54-.62-1.14-1.45-1.98-2.49-2.51-1.02-.55-2.37-.83-4.05-.83h-5.73v30.56c0,2.46.27,3.99.81,*********,1.93,1.24,3.51,1.24h1.41v1.09H15.25v-1.09h1.44c1.72,0,2.94-.51,3.66-1.53.44-.63.66-2.06.66-4.31V6.51h-4.89c-1.9,0-3.25.14-4.05.41-1.04.37-1.93,1.09-2.67,2.15-.74,1.06-1.18,2.5-1.32,4.31h-1.14l.48-9.38h33.19Z"
        :style="{ fill: fillLine }"
        style="transform: translate(0, -8px)"
      />
    </g>
  </svg>
</template>

<script>
export default {
  name: 'FreeText',
  props: {
    active: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    fill() {
      return this.active ? 'white' : 'var(--chart-type-icon)'
    },
    fillLine() {
      return this.active ? 'white' : 'var(--chart-line-type-icon)'
    },
  },
}
</script>
