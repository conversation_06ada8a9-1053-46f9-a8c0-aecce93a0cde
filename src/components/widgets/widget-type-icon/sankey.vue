<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="48"
    height="38"
    viewBox="0 0 48 38"
  >
    <path
      d="M43,2.65C23.67,2.65,20.22,23.38,5,23.38v4.26c16.17,0,18.72-16.72,38-16.72V2.65ZM5.05,31.79v3.55c15.87,0,18.47-13.87,37.95-13.87v-10.46c-19.17,0-22.98,20.78-37.95,20.78Z"
      style="opacity: 0.4"
      :style="{ fill }"
    />
    <path
      d="M5,2.75v5.86c17.32-1.3,23.38,20.43,37.9,19.17v-4.26C28.38,23.53,22.92,2.75,5,2.75ZM5,8.81v5.86c17.32-1.3,23.38,21.78,37.9,20.53v-3.5c-14.52,0-19.98-22.88-37.9-22.88Z"
      style="opacity: 0.6"
      :style="{ fill }"
    />
  </svg>
</template>

<script>
export default {
  name: 'Pie',
  props: {
    active: {
      type: <PERSON><PERSON><PERSON>,
      default: false,
    },
  },
  computed: {
    fill() {
      return this.active ? 'white' : 'var(--chart-type-icon)'
    },
    fillLine() {
      return this.active ? 'white' : 'var(--chart-line-type-icon)'
    },
  },
}
</script>
