<template>
  <MPopover
    ref="popoverRef"
    overlay-class-name="save-as-widget-overlay"
    placement="bottomRight"
    @show="handleShowPopover"
  >
    <template v-slot:trigger>
      <slot name="trigger">
        <MButton :title="`Save as ${saveFor}`" class="ml-2">
          <MIcon name="save" size="lg" class="mr-1" />
          Save as {{ saveFor }}
        </MButton>
      </slot>
    </template>

    <div class="flex w-full flex-col px-4 py-2">
      <h5 class="border-bot">Save as {{ saveFor }}</h5>
      <FlotoForm @submit="handleSave">
        <div class="text-right">
          <MRow>
            <MCol :size="12">
              <FlotoFormItem
                v-model="name"
                :placeholder="`${saveFor} Name`"
                label="Name"
                :rules="rules"
              />
            </MCol>
            <MCol :size="12">
              <FlotoFormItem
                v-model="description"
                label="Description"
                placeholder="Description"
              />
            </MCol>
          </MRow>
        </div>
        <template v-slot:submit="{ submit }">
          <MRow>
            <MCol class="text-right">
              <MButton variant="default" @click="handleCancel">Cancel</MButton>
              <MButton :loading="loading" class="ml-2" @click="submit">
                Save
              </MButton>
            </MCol>
          </MRow>
        </template>
      </FlotoForm>
    </div>
  </MPopover>
</template>

<script>
export default {
  name: 'SaveAsWidget',
  props: {
    saveWidget: {
      type: Function,
      required: true,
    },
    saveFor: {
      type: String,
      default: 'Widget',
    },
  },
  data() {
    return {
      loading: false,
      name: '',
      description: '',
    }
  },
  computed: {
    rules() {
      return {
        required: true,
        no_slash: this.saveFor === 'Report',
      }
    },
  },
  methods: {
    handleSave() {
      this.loading = true
      this.saveWidget({
        name: this.name,
        description: this.description,
      })
        .then(() => {
          this.$refs.popoverRef.hide()
        })
        .finally(() => (this.loading = false))
    },
    handleCancel() {
      this.$refs.popoverRef.hide()
    },
    handleShowPopover() {
      this.name = ''
      this.description = ''
    },
  },
}
</script>
