import FindIndex from 'lodash/findIndex'
import UniqBy from 'lodash/uniqBy'
import applyUnit from '@/src/utils/unit-applier'

export function buildInterfaceCalculatedGrid(previousResult, newResult) {
  if (!newResult._keepLoading) {
    const finalResult = {
      ...previousResult,
      ...newResult,
    }
    const interfaceResult = finalResult.interface || {}
    const vlan = finalResult.vlan || {}
    const macAddress = finalResult.macAddress || {}

    return {
      ...interfaceResult,
      rows: (interfaceResult.rows || []).map((row) => {
        const index = row['interface_index_last']
        const assignedMacAddressess = (macAddress.data || [])
          .filter(
            (mac) =>
              String(mac['client.mac.address.client.port.last']) ===
              String(index)
          )
          .map((row) => row['client.mac.address'])
        row['client_mac_address_client_port_last'] =
          assignedMacAddressess.join(',')

        const appliedVlan = (vlan.data || []).find((vlanItem) =>
          String(vlanItem['vlan.port.last'] || '')
            .split(',')
            .includes(String(index))
        )
        if (appliedVlan) {
          row['vlan_name_last'] = appliedVlan['vlan.name.last']
          row['vlan'] = appliedVlan['vlan']
        }

        return row
      }),
    }
  }
  return {
    ...newResult,
    ...previousResult,
  }
}

export function buildVlanCalculatedGrid(previousResult, newResult) {
  if (!newResult._keepLoading) {
    const finalResult = { ...previousResult, ...newResult }
    let vlanResult = finalResult.vlan || {}
    let interfaceResult = finalResult.interface || {}
    return {
      ...vlanResult,
      rows: (vlanResult.rows || []).map((item) => {
        const ports = String(item['vlan_port_last'] || '').split(',')
        item['vlan_port_count_last'] = ports.length
        if (ports.length > 0) {
          // merge traffic of all interfaces included in ports

          let traffic = { in: 0, out: 0 }

          // (interfaceResult.data || [])
          //   .reduce(
          //     (total, interfaceItem) => {
          //       if (
          //         ports.includes(String(interfaceItem['interface.index.last']))
          //       ) {
          //         return {
          //           in:
          //             total.in +
          //             parseInt(
          //               interfaceItem[
          //                 'interface.in.traffic.bytes.per.sec.last'
          //               ] || 0
          //             ),
          //           out:
          //             total.out +
          //             parseInt(
          //               interfaceItem[
          //                 'interface.out.traffic.bytes.per.sec.last'
          //               ] || 0
          //             ),
          //         }
          //       }
          //       return total
          //     },
          //     { in: 0, out: 0 }
          //   )

          const result = interfaceResult.data || []

          for (const interfaceItem of result) {
            if (ports.includes(String(interfaceItem['interface.index.last']))) {
              traffic.in += parseInt(
                interfaceItem['interface.in.traffic.bytes.per.sec.last'] || 0
              )

              traffic.out = parseInt(
                interfaceItem['interface.out.traffic.bytes.per.sec.last'] || 0
              )
            }
          }

          const vlanPortsWithInterfaceIndex = ports.map((p) => {
            let interfaceIndexContainsVlanPort = ((
              interfaceResult.data || []
            ).filter(
              (i) => i['interface.index.last'].toString() === p.toString()
            ) || [])[0]
            return `${p} ${
              (interfaceIndexContainsVlanPort || {})['interface']
                ? `(${(interfaceIndexContainsVlanPort || {})['interface']})`
                : ''
            }`
          })
          item['interface_in_traffic_bytes_per_sec_last'] =
            traffic.in / ports.length
          item['interface_in_traffic_bytes_per_sec_last.formatted'] = applyUnit(
            'interface.in.traffic.bytes.per.sec.last',
            traffic.in / ports.length
          )
          item['interface_out_traffic_bytes_per_sec_last'] =
            traffic.out / ports.length
          item['interface_out_traffic_bytes_per_sec_last.formatted'] =
            applyUnit(
              'interface.out.traffic.bytes.per.sec.last',
              traffic.out / ports.length
            )
          item['vlan_port_last'] = vlanPortsWithInterfaceIndex
        }
        return item
      }),
    }
  }
  return {
    ...previousResult,
    ...newResult,
  }
}

export function buildArubaAccessPointCalculatedGrid(previousResult, newResult) {
  if (!newResult._keepLoading) {
    const finalResult = { ...previousResult, ...newResult }
    let accessPointsResult = finalResult.accessPoints || {}
    let interfaceResult = finalResult.interface || {}
    return {
      ...accessPointsResult,
      rows: (accessPointsResult.rows || []).map((item) => {
        const macAddress = item['aruba_wireless_access_point']
        if (macAddress) {
          const interfaces = (interfaceResult.data || []).filter(
            (interfaceItem) =>
              interfaceItem[
                'aruba.wireless.access.point.interface.mac.address.last'
              ] &&
              interfaceItem[
                'aruba.wireless.access.point.interface.mac.address.last'
              ].toLowerCase() ===
                item[
                  'aruba_wireless_access_point_mac_address_last'
                ].toLowerCase()
          )
          let clients = 0
          let sentBytes = 0
          let receivedBytes = 0
          interfaces.forEach((interfaceItem) => {
            clients += parseInt(
              interfaceItem[
                'aruba.wireless.access.point.interface.clients.avg'
              ] || 0
            )
            sentBytes += parseInt(
              interfaceItem[
                'aruba.wireless.access.point.interface.sent.bytes.per.sec.avg'
              ] || 0
            )
            receivedBytes += parseInt(
              interfaceItem[
                'aruba.wireless.access.point.interface.received.bytes.per.sec.avg'
              ] || 0
            )
          })
          item['aruba_wireless_access_point_interface_clients_avg'] = clients
          if (interfaces.length) {
            item[
              'aruba_wireless_access_point_interface_sent_bytes_per_sec_avg'
            ] = sentBytes / interfaces.length
            item[
              'aruba_wireless_access_point_interface_received_bytes_per_sec_avg'
            ] = receivedBytes / interfaces.length
          }
          item[
            'aruba_wireless_access_point_interface_sent_bytes_per_sec_avg.formatted'
          ] = applyUnit(
            'aruba.wireless.access.point.interface.sent.bytes.per.sec.avg',
            item[
              'aruba_wireless_access_point_interface_sent_bytes_per_sec_avg'
            ] || 0
          )
          item[
            'aruba_wireless_access_point_interface_received_bytes_per_sec_avg.formatted'
          ] = applyUnit(
            'aruba.wireless.access.point.interface.received.bytes.per.sec.avg',
            item[
              'aruba_wireless_access_point_interface_received_bytes_per_sec_avg'
            ] || 0
          )
        }
        return item
      }),
    }
  }
  return {
    ...previousResult,
    ...newResult,
  }
}

export function buildSwitchPortMapperGrid(previousResult, newResult) {
  if (!newResult._keepLoading) {
    const finalResult = { ...previousResult, ...newResult }
    const interfaceResult = finalResult.interface || {}
    const macAddressResult = finalResult.macAddress || {}
    return {
      ...interfaceResult,
      rows: (interfaceResult.rows || []).map((row) => {
        const index = row['interface_index_last']
        const assignedMacAddressess = (macAddressResult.data || [])
          .filter(
            (mac) =>
              String(mac['client.mac.address.client.port.last']) ===
              String(index)
          )
          .map((row) => row['client.mac.address'])
        row['client_mac_address'] = assignedMacAddressess.join(',')
        row['client_mac_address_client_port_last'] = index

        return row
      }),
    }
  }
  return {
    ...newResult,
    ...previousResult,
  }
}

export function buildStackedSwitchGrid(previousResult, newResult) {
  if (!newResult._keepLoading) {
    const finalResult = { ...previousResult, ...newResult }
    const stackedSwiitchResult = finalResult.stackedSwiitch || {}
    const bandWidthResult = finalResult.bandWidth || {}
    return {
      ...stackedSwiitchResult,
      rows: (stackedSwiitchResult.rows || []).map((row) => {
        row['bandwidth'] = ((bandWidthResult.data || [])[0] || {})[
          'cisco.stack.bandwidth.last'
        ]

        return row
      }),
    }
  }
  return {
    ...newResult,
    ...previousResult,
  }
}

export function buildAvailabilityTimeSeries(previousResult, newResult) {
  const mergedResults = {
    ...(previousResult || {}),
    ...(newResult || {}),
  }
  if (newResult._keepLoading) {
    return mergedResults
  }
  const mergedSeries = Object.keys(mergedResults).filter(
    (key) => key !== '_keepLoading'
  )
  // .reduce(
  //   (result, item) => [...result, ...(mergedResults[item].series || [])],
  //   []
  // )

  let finalSeries = []
  for (const item of mergedSeries) {
    finalSeries = finalSeries.concat(mergedResults[item].series || [])
  }

  return {
    series: ['Last Day', 'Last 7 Days', 'Last 15 Days']
      .map((key) => finalSeries.find((s) => s.name === key))
      .filter(Boolean),
  }
}

export function mergeAvailabilityPieResult(previousResult, newResult) {
  if (!newResult._keepLoading) {
    const finalResult = { ...previousResult, ...newResult }
    const percentSeries = finalResult.percentage || {}
    const timeSeries = finalResult.time || {}
    if ((percentSeries.series || []).length === 0) {
      return {
        series: [],
      }
    }
    return {
      ...percentSeries,
      series: [
        {
          ...percentSeries.series[0],
          data: percentSeries.series[0].data.map((item) => {
            let durationIndex = -1
            if (timeSeries.series && timeSeries.series.length) {
              durationIndex = FindIndex(
                timeSeries.series[0].data || [],
                (internal) => internal.name === item.name
              )
            }
            return {
              ...item,
              ...(durationIndex !== -1
                ? {
                    duration:
                      timeSeries.series[0].formattedValues[durationIndex],
                  }
                : {}),
            }
          }),
        },
      ],
    }
  }
  return {
    ...newResult,
    ...previousResult,
  }
}

export function mergeTopNHorizontalResult(previousResult, newResult) {
  return {
    ...previousResult,
    ...newResult,
  }
}

export function defaultMerger(previousResult, newResult, uniqByFn) {
  const data = [
    ...(Array.isArray(previousResult) ? previousResult : []),
    ...newResult,
  ]
  if (uniqByFn) {
    return UniqBy(data, uniqByFn)
  }

  return data
}
