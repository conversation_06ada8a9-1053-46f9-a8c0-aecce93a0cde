<template>
  <FlotoContentLoader v-if="!isWidgetHidden" :loading="loading">
    <Container
      v-if="!isFreeTextWidget"
      :widget.sync="widget"
      :time-range="timeRange"
      :disabled="disabled"
      :hide-actions="hideActions"
      :for-template="forTemplate"
      v-bind="$attrs"
      @update-columns="handleUpdateColumns"
      v-on="listeners"
      @hide="handleWidgetHidden"
    />
    <FreeTextView
      v-else
      :widget="widget"
      v-bind="$attrs"
      v-on="$listeners"
      @refresh-widget="refreshWidget"
    />
  </FlotoContentLoader>
</template>

<script>
import Container from './views/container.vue'
import { updateWidgetApi, getWidgetApi } from './widgets-api'
import { WidgetTypeConstants } from './constants'
import FreeTextView from './views/free-text-view.vue'

export default {
  name: 'Widget',
  components: {
    Container,
    FreeTextView,
  },
  inheritAttrs: false,
  props: {
    forTemplate: {
      type: Boolean,
      default: false,
    },
    widgetId: {
      type: Number,
      default: undefined,
    },
    timeRange: {
      type: Object,
      default: undefined,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    hideActions: {
      type: Boolean,
      default: false,
    },
    ignoreWidgetUpdate: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: true,
      widget: undefined,
      isWidgetHidden: false,
    }
  },
  computed: {
    listeners() {
      const { hide, ...listeners } = this.$listeners
      return listeners
    },
    isFreeTextWidget() {
      return this.widget?.category === WidgetTypeConstants.FREE_TEXT
    },
  },
  watch: {
    widgetId: {
      handler(newValue, oldValue) {
        if (newValue !== oldValue) {
          this.getWidget()
        }
      },
      immediate: true,
    },
  },
  methods: {
    handleWidgetHidden() {
      // this.isWidgetHidden = true
      this.$emit('hide')
    },
    getWidget() {
      this.loading = false
      this.widget = {
        id: this.widgetId,
      }
      getWidgetApi(this.widgetId).then((data) => {
        this.widget = Object.freeze(data)
        this.loading = false
      })
    },
    handleUpdateColumns(columns) {
      if (!this.ignoreWidgetUpdate) {
        getWidgetApi(this.widgetId).then((data) => {
          this.widget = Object.freeze(data)
          this.widget = {
            ...this.widget,
            widgetProperties: {
              ...this.widget.widgetProperties,
              columnSettings: columns,
            },
          }
          this.updateWidget()
        })
      }
    },
    updateWidget() {
      if (this.forTemplate) {
        return
      }
      updateWidgetApi(this.widget)
    },
    refreshWidget() {
      this.loading = true
      getWidgetApi(this.widgetId).then((data) => {
        this.widget = Object.freeze(data)

        this.loading = false
      })
    },
  },
}
</script>
