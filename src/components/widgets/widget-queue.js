import PQueue from 'p-queue'

export const createQueue = (concurrency = 5, intervalCap = 2000) => {
  const queue = new PQueue({
    intervalCap,
    concurrency,
    timeout: 24 * 60 * 60 * 1000,
    carryoverConcurrencyCount: true,
  })
  if (process.env.NODE_ENV !== 'production') {
    queue.on('error', (error) => {
      // eslint-disable-next-line
      console.log(`Error getting result of widget ${(error.widget || {}).id}`)
      // eslint-disable-next-line
      console.error(error)
    })
  }
  return queue
}
