<template>
  <ErrorH<PERSON>ler v-if="!isFreeTextWidget" @error="$emit('error', $event)">
    <WidgetContainer
      v-if="canRenderPreview"
      :widget="widget"
      is-preview
      :time-range="widget.timeRange"
      v-bind="$attrs"
      v-on="$listeners"
      @update-columns="handleUpdateColumns"
    />
    <div v-else class="flex flex-col flex-1">
      <slot name="no-preview">
        <div class="flex items-center justify-center flex-1">
          <h5 class="text-primary">{{ noPreviewMessage }} </h5>
        </div>
      </slot>
    </div>
  </ErrorHandler>
  <FreeTextView
    v-else
    :widget="widget"
    v-bind="$attrs"
    is-preview
    v-on="$listeners"
  />
</template>

<script>
import { canRenderWidgetPreview } from './helper'
import WidgetContainer from './views/container.vue'
import { WidgetTypeConstants } from './constants'
import FreeTextView from './views/free-text-view.vue'

export default {
  name: 'Preview',
  components: {
    WidgetContainer,
    FreeTextView,
  },
  props: {
    widget: {
      type: Object,
      required: true,
    },
    ignoreWidgetUpdate: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    canRenderPreview() {
      const widget = this.widget
      let canRender = canRenderWidgetPreview(widget)

      if (!canRender) {
        this.$emit('preview-rendered', false)
      }
      return canRender
    },
    noPreviewMessage() {
      if (this.widget.reportId === 0) {
        return 'Please write and execute script'
      }
      return ' Please Select Group'
    },
    isFreeTextWidget() {
      return this.widget?.category === WidgetTypeConstants.FREE_TEXT
    },
  },
  methods: {
    handleUpdateColumns(columns) {
      if (!this.ignoreWidgetUpdate) {
        this.$emit('update:widget', {
          ...this.widget,
          widgetProperties: {
            ...this.widget.widgetProperties,
            columnSettings: columns,
          },
        })
      }
    },
  },
}
</script>
