<template>
  <FlotoDrawer
    :open="open"
    :mask="false"
    :scrolled-content="false"
    wrap-class-name="widget-selector-drawer"
    :wrap-style="{
      zIndex: 1000,
      ...(isDraggingWidget ? { opacity: 0 } : {}),
    }"
    @hide="$emit('cancel')"
  >
    <template v-slot:title>
      <h5 class="mb-0 text-ellipsis pr-6 inline-flex">Add New Widget</h5>
    </template>
    <div v-if="open" class="flex flex-col flex-1 min-h-0 px-4">
      <FlotoContentLoader :loading="loading">
        <div class="flex flex-col flex-1 min-h-0">
          <MTab v-model="currentTab">
            <MTabPane key="create_widget" tab="Create Widget" />
            <MTabPane key="predefined" tab="Predefined" />
            <MTabPane key="custom" tab="User Define" />
          </MTab>
          <div class="flex justify-between my-2">
            <MInput
              v-model="searchTerm"
              class="search-box"
              placeholder="Search"
              name="search"
            >
              <template v-slot:prefix>
                <MIcon name="search" />
              </template>
              <template v-if="searchTerm" v-slot:suffix>
                <MIcon
                  name="times-circle"
                  class="text-neutral-light cursor-pointer"
                  @click="searchTerm = ''"
                />
              </template>
            </MInput>
            <!-- <MPermissionChecker
              v-if="!forReportForm"
              :permission="$constants.WIDGET_SETTINGS_CREATE_PERMISSION"
            >
              <MButton @click="$emit('create')">Create Widget</MButton>
            </MPermissionChecker> -->
          </div>
          <div
            v-if="currentTab !== 'create_widget'"
            class="flex flex-col flex-1 min-h-0 mt-2"
          >
            <RecycleScroller :items="widgetsForTab" :item-size="60">
              <template v-slot="{ item }">
                <div style="height: 60px" class="pb-2">
                  <div
                    class="flex items-center widget-item text-neutral"
                    :gutter="0"
                    style="height: 50px"
                  >
                    <div class="icon mr-4">
                      <WidgetIcon
                        :category="item.category"
                        :widget-type="item.widgetType"
                        :is-availability-widget="item.isAvailabilityWidget"
                        :size="48"
                      />
                    </div>
                    <div class="flex-1 min-w-0 flex flex-col">
                      <h6
                        class="m-0 text-ellipsis"
                        style="white-space: nowrap"
                        >{{ displayText(item) }}</h6
                      >
                      <small
                        class="text-neutral-light text-ellipsis"
                        style="white-space: nowrap"
                        >{{ item.description }}</small
                      >
                    </div>
                    <div v-if="!forReportForm" class="btn-actions">
                      <WidgetJsonEditor
                        disabled
                        :widget="item"
                        @update="handleUpdateWidgetDefinition"
                      />
                      <MPermissionChecker
                        :permission="
                          $constants.DASHBOARD_SETTINGS_UPDATE_PERMISSION
                        "
                      >
                        <MButton
                          draggable="true"
                          unselectable="on"
                          class="cursor-move"
                          variant="transparent"
                          title="Add Widget"
                          :data-category="item.category"
                          :data-widgetType="item.widgetType"
                          :data-isAvailabilityWidget="item.isAvailabilityWidget"
                          :data-id="item.id"
                          :shadow="false"
                          :rounded="false"
                          shape="circle"
                          @drag="$emit('drag', $event)"
                          @dragend="$emit('dragend', $event)"
                        >
                          <MIcon name="drag-arrows" />
                        </MButton>
                      </MPermissionChecker>
                      <MPermissionChecker
                        :permission="
                          $constants.DASHBOARD_SETTINGS_UPDATE_PERMISSION
                        "
                      >
                        <MButton
                          variant="transparent"
                          :shadow="false"
                          title="Add Widget at last"
                          :rounded="false"
                          shape="circle"
                          @click="$emit('add', item)"
                        >
                          <MIcon name="plus-circle" />
                        </MButton>
                      </MPermissionChecker>
                      <MPermissionChecker
                        :permission="
                          $constants.WIDGET_SETTINGS_UPDATE_PERMISSION
                        "
                      >
                        <MButton
                          v-if="!item.isPredefined"
                          variant="transparent"
                          title="Edit Widget"
                          :shadow="false"
                          :rounded="false"
                          shape="circle"
                          @click="$emit('edit', item)"
                        >
                          <MIcon name="pencil" />
                        </MButton>
                      </MPermissionChecker>
                      <MPermissionChecker
                        :permission="
                          $constants.WIDGET_SETTINGS_CREATE_PERMISSION
                        "
                      >
                        <MButton
                          variant="transparent"
                          title="Clone Widget"
                          :shadow="false"
                          :rounded="false"
                          shape="circle"
                          @click="$emit('clone', item)"
                        >
                          <MIcon name="clone" />
                        </MButton>
                      </MPermissionChecker>
                      <MPermissionChecker
                        :permission="
                          $constants.WIDGET_SETTINGS_DELETE_PERMISSION
                        "
                      >
                        <MButton
                          v-if="!item.isPredefined"
                          variant="transparent"
                          title="Delete Widget"
                          :shadow="false"
                          :rounded="false"
                          shape="circle"
                          @click="$emit('delete', item)"
                        >
                          <MIcon name="trash" class="text-secondary-red" />
                        </MButton>
                      </MPermissionChecker>
                    </div>

                    <MButton
                      v-else
                      variant="transparent"
                      :shadow="false"
                      title="Add Widget"
                      :rounded="false"
                      shape="circle"
                      @click="$emit('add', item)"
                    >
                      <MIcon name="monitor-import" />
                    </MButton>
                  </div>
                </div>
              </template>
            </RecycleScroller>
          </div>
          <WidgetCategoryTypeSelector
            v-else
            :search-term="searchTerm"
            @create="$emit('create', $event)"
          />
        </div>
      </FlotoContentLoader>
    </div>
  </FlotoDrawer>
</template>

<script>
import FindIndex from 'lodash/findIndex'
import FilterBy from 'lodash/filter'
import Bus from '@utils/emitter'
import WidgetIcon from './widget-type-icon/widget-type-icon.vue'
import { getWidgetsApi, updateWidgetApi } from './widgets-api'
import WidgetJsonEditor from './widget-json-editor.vue'
import { WidgetTypeConstants } from '@components/widgets/constants'

import WidgetCategoryTypeSelector from './widget-category-type-selector.vue'

export default {
  name: 'WidgetSelector',
  components: {
    WidgetIcon,
    WidgetJsonEditor,
    WidgetCategoryTypeSelector,
  },
  inheritAttrs: false,
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    isDraggingWidget: {
      type: Boolean,
      default: false,
    },
    excludedWidgetIds: {
      type: Array,
      default: undefined,
    },
    forReportForm: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    this.excludedCategory = {
      metric: [
        WidgetTypeConstants.SANKEY,
        WidgetTypeConstants.HEATMAP,
        WidgetTypeConstants.GAUGE,
        WidgetTypeConstants.STREAM,
        WidgetTypeConstants.MAP_VIEW,
      ],
      alert: [
        WidgetTypeConstants.SANKEY,
        WidgetTypeConstants.GAUGE,
        WidgetTypeConstants.STREAM,
        WidgetTypeConstants.MAP_VIEW,
      ],
      availability: [
        WidgetTypeConstants.TOPN,
        WidgetTypeConstants.GAUGE,
        WidgetTypeConstants.HEATMAP,
        WidgetTypeConstants.SANKEY,
        WidgetTypeConstants.STREAM,
        WidgetTypeConstants.MAP_VIEW,
      ],
    }
    return {
      loading: true,
      currentTab: 'create_widget',
      widgets: [],
      searchTerm: '',
    }
  },
  computed: {
    widgetsForTab() {
      const tab = this.currentTab
      const searchTerm = this.searchTerm
      const excludedWidgetIds = this.excludedWidgetIds || []
      const widgets = this.widgets
      return FilterBy(widgets, (widget) => {
        if (excludedWidgetIds.includes(widget.id)) {
          return false
        }
        if (searchTerm && (searchTerm || '').length) {
          if (
            widget.category === WidgetTypeConstants.FREE_TEXT &&
            widget?.widgetProperties?.textToDisplay
              ?.toLowerCase()
              ?.indexOf(searchTerm.toLowerCase()) !== -1
          ) {
            return true
          }
        }
        if (searchTerm && (searchTerm || '').length) {
          if (
            widget?.name?.toLowerCase()?.indexOf(searchTerm.toLowerCase()) ===
            -1
          ) {
            return false
          }
        }
        if (this.forReportForm) {
          let category = widget.groups.find((g) => g.type === 'metric')
            ? 'metric'
            : widget.groups.find((g) => g.type === 'availability')
            ? 'availability'
            : widget.groups.find((g) => ['alert', 'policy'].includes(g.type))
            ? 'alert'
            : 'metric'
          if (
            (this.excludedCategory[category] || []).includes(widget.category) ||
            widget.groups.find((group) => ['log', 'flow'].includes(group.type))
          ) {
            return false
          }
        }

        if (tab === 'predefined') {
          return widget.isPredefined
        }
        return !widget.isPredefined
      })
    },
  },
  watch: {
    open(newValue, oldValue) {
      if (newValue !== oldValue && newValue) {
        this.searchTerm = ''
      }
    },
  },
  created() {
    this.getWidgets()
  },
  methods: {
    getWidgetById(id) {
      return this.widgets.find((w) => String(w.id) === String(id))
    },
    getWidgets() {
      getWidgetsApi().then((data) => {
        this.widgets = Object.freeze(data)
        this.loading = false
        this.$emit('widgets-received', this.widgets)
      })
    },
    addWidget(widget) {
      this.widgets = Object.freeze([...this.widgets, widget])
      this.currentTab = 'custom'
    },
    updateWidget(widget) {
      const index = FindIndex(this.widgets, { id: widget.id })
      if (index !== -1) {
        this.widgets = Object.freeze([
          ...this.widgets.slice(0, index),
          widget,
          ...this.widgets.slice(index + 1),
        ])
        this.$emit('widgets-received', this.widgets)
      }
    },
    handleUpdateWidgetDefinition(widget) {
      updateWidgetApi(widget).then((data) => {
        this.updateWidget(data)
        Bus.$emit('widget.refresh', data.id)
      })
    },
    removeWidget(widget) {
      const index = FindIndex(this.widgets, { id: widget.id })
      if (index >= 0) {
        this.widgets = Object.freeze(
          this.widgets.filter((w) => w.id !== widget.id)
        )
        this.$emit('widgets-received', this.widgets)
      }
    },
    close() {
      this.open = false
    },
    displayText(widget) {
      if (widget.category === WidgetTypeConstants.FREE_TEXT) {
        return widget?.widgetProperties?.textToDisplay
      } else {
        return widget?.name
      }
    },
  },
}
</script>

<style lang="less" scoped>
.widget-item {
  border: 1px solid var(--border-color);
  border-radius: 4px;

  @apply px-2;

  .btn-actions {
    visibility: hidden;
  }

  &:hover {
    .btn-actions {
      visibility: visible;
    }
  }
}
</style>
