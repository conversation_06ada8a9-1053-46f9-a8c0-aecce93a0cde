<template>
  <ErrorHandler>
    <div
      ref="widgetsContainerRef"
      class="relative h-100 dashboard-container"
      :class="{ 'overflow-y-auto': !disableOverflow }"
      @scroll="containerScrollTop = $event.target.scrollTop"
    >
      <GridLayout
        ref="gridLayoutRef"
        :layout.sync="innerWidgets"
        :col-num="gridColumns"
        :row-height="dashboardStyle.rowHeight || 85"
        :is-draggable="!disabled"
        :is-resizable="!disabled"
        :use-css-transforms="true"
        :prevent-collisionn="true"
        :responsive="false"
        :margin="[dashboardStyle.horizontalGap, dashboardStyle.verticalGap]"
        @layout-updated="handleLayoutUpdate"
        @layout-ready="handleLayoutReady"
      >
        <GridItem
          v-for="item in innerWidgets"
          :key="item.i"
          drag-ignore-from="a, button, canvas, svg, table, .vue-grid-drag-ignore"
          class="flex flex-col"
          :x="item.x"
          :y="item.y"
          :w="item.w"
          :h="item.h"
          :i="item.i"
          :min-h="1"
          :min-w="1"
        >
          <ErrorHandler hide-image message="Unable to generate widget!">
            <slot
              name="widget"
              :item="item"
              :layoutIsReady="layoutIsReady"
              :dashboardStyle="dashboardStyle"
              :queue="queue"
            >
              <Component
                :is="item.children ? 'WidgetGroup' : 'Widget'"
                v-if="item.i !== 'drop'"
                :queue="queue"
                :widget-id="item.id"
                :dashlet="item"
                :is-dashboard-in-fullscreen="fullscreen"
                :children="item.children"
                :use-initial-request="layoutIsReady"
                :cell-height="dashboardStyle.rowHeight || 85"
                :time-range="timeRange"
                :font-size="dashboardStyle.fontSize"
                :server-params="params"
                :primary-bg="blinkingItems.indexOf(item.i) >= 0"
                :disabled="disabled"
                :title="item.options ? item.options.title : undefined"
                :hide-title="
                  item.options ? item.options.showTitle === false : false
                "
                :hide-actions="
                  hideWidgetActions ||
                  ('showWidgetAction' in (item.options || {}) &&
                    item.options.showWidgetAction === false)
                "
                :sidebar-monitor="sidebarMonitorItem"
                v-bind="$attrs"
                @remove="handleRemoveWidget(item)"
                @edit="handleEditWidget"
                @clone="handleCloneWidget"
                @update-height="handleUpdateHeight(item, $event)"
                @hide="handleHideWidget(item)"
                @drilldown="$emit('drilldown', $event)"
                @monitor-sidebar="setSidebarMonitor"
                @template-drilldown="setTemplateDrilldown"
                @open-gauge-drilldown-grid="setOpenGaugeDrilldownDrawer"
                @force-drill-down="$emit('force-drill-down', $event)"
              />
            </slot>
          </ErrorHandler>
        </GridItem>
      </GridLayout>
      <MPermissionChecker
        v-if="shouldShowAddButton"
        :permission="plusButtonPermissions"
      >
        <MButton
          class="create-widget-btn"
          rounded
          @click="isSelectingWidget = true"
        >
          <MIcon name="plus" size="lg" />
        </MButton>
      </MPermissionChecker>
      <WidgetSelector
        v-if="shouldShowAddButton"
        ref="widgetSelectorRef"
        :is-dragging-widget="isDraggingWidget"
        :open="isSelectingWidget"
        :excluded-widget-ids="selectedWidgetIds"
        @add="addWidget"
        @create="handleCreateWidget"
        @cancel="isSelectingWidget = false"
        @clone="handleCloneWidget"
        @edit="handleEditWidget"
        @delete="showConfirmDeleteModalFor = $event"
        @drag="widgetDragStart"
        @dragend="widgetDragEnd"
      />
      <ErrorHandler v-if="isCreating">
        <WidgetForm
          :default-value="defaultWidgetData"
          :can-clone-widget="shouldShowAddButton"
          @cancel="handleCancelWidgetForm"
          @created="handleNewWidgetCreated"
          @updated="handleWidgetUpdated"
          @add="addWidget"
        />
      </ErrorHandler>
      <MonitorSidebar
        v-if="sidebarMonitorItem"
        :key="monitorSidebarRefreshKey"
        :monitor="sidebarMonitorItem"
        :top-offset="containerScrollTop"
        :vm="
          sidebarMonitorItem.isVm
            ? sidebarMonitorItem.monitorInstance
            : undefined
        "
        :application="
          sidebarMonitorItem.isApplication
            ? sidebarMonitorItem.application
            : undefined
        "
        :hide-metrics="
          ['vm', 'application', 'ap'].includes(sidebarMonitorItem.resourceType)
        "
        :use-drawer="(sidebarMonitorItem || {}).useDrawer"
        @close="resetSidebarMonitor"
      />
      <ProcessTemplate
        v-if="
          showTemplateFor && (showTemplateFor || {}).templateType === 'process'
        "
        :open="Boolean(showTemplateFor)"
        :process="showTemplateFor"
        d
        @hide="showTemplateFor = null"
      />
      <InterfaceTemplate
        v-if="
          showTemplateFor &&
          (showTemplateFor || {}).templateType === 'interface'
        "
        :open="Boolean(showTemplateFor)"
        :interface-item="showTemplateFor"
        @hide="showTemplateFor = null"
      />
      <GaugeDrilldownDrawer
        :open="Boolean(showGaugeDrilldownFor)"
        :item="showGaugeDrilldownFor"
        @hide="showGaugeDrilldownFor = null"
        @monitor-sidebar="setSidebarMonitor"
        @template-drilldown="setTemplateDrilldown"
      />

      <!-- Delete Confirm Modal -->
      <FlotoConfirmModal
        v-if="showConfirmDeleteModalFor !== null"
        open
        no-icon-shadow
        @confirm="handleDeleteWidget(showConfirmDeleteModalFor)"
        @hide="showConfirmDeleteModalFor = null"
      >
        <template v-slot:icon>
          <slot name="confirm-delete-icon">
            <MIcon name="trash-alt" size="2x" class="text-secondary-red" />
          </slot>
        </template>
        <template v-slot:message>
          <slot name="confirm-delete-message" :item="showConfirmDeleteModalFor">
            {{
              $message('confirm', {
                message: $message('delete_resource', {
                  resource: 'widget',
                }),
              })
            }}?
          </slot>
        </template>
      </FlotoConfirmModal>
      <!-- Delete Failed Modal -->
      <LinkedRecordsDetailModal
        v-if="isLinkedRecordsDetailModalVisible"
        :data="linkedRecordsDetailData"
        @hide="isLinkedRecordsDetailModalVisible = false"
      >
        <template v-slot:title>
          <div class="flex items-center">
            <div class="flex-1">
              <slot name="title">
                <h4 id="header-id" class="mb-0 text-secondary-red">
                  Usage Details
                </h4>
              </slot>
            </div>
            <MButton
              id="close-used-count"
              variant="transparent"
              :shadow="false"
              shape="circle"
              @click="isLinkedRecordsDetailModalVisible = false"
            >
              <MIcon name="times" class="text-neutral-light" />
            </MButton>
          </div>
        </template>
      </LinkedRecordsDetailModal>
    </div>
  </ErrorHandler>
</template>

<script>
import FindIndex from 'lodash/findIndex'
import CloneDeep from 'lodash/cloneDeep'
import { GridLayout, GridItem } from 'vue-grid-layout'
import Bus from '@utils/emitter'
import { scrollTo } from '@utils/smooth-scroll'
import MonitorSidebar from '@components/monitor-sidebar/monitor-sidebar.vue'
import LinkedRecordsDetailModal from '@components/crud/linked-records-detail-modal.vue'
import { WidgetTypeConstants } from '@components/widgets/constants'
import {
  calculateNewItemPosition,
  getWidgetGroupByChartType,
  getWidgetProperties,
  getRange,
  overrideWidgetPropertyByWidgetCategory,
} from './helper'
import { getWidgetDefaultSize } from './constants'
import { deleteWidgetApi, getWidgetApi } from './widgets-api'
import { generateId } from '@utils/id'
import WidgetSelector from './widget-selector.vue'
import WidgetForm from './widget-form.vue'
import Widget from './widget.vue'
import WidgetGroup from './widget-group.vue'
import { createQueue } from './widget-queue'
import ProcessTemplate from '@components/templates/process-template.vue'
import InterfaceTemplate from '@components/templates/interface-template.vue'
import GaugeDrilldownDrawer from './views/components/gauge-drilldown-drawer.vue'

let mouseXY = { x: null, y: null }
let DragPos = { x: null, y: null, w: 1, h: 1, i: null }

export default {
  name: 'Widgets',
  components: {
    GridLayout,
    GridItem,
    Widget,
    WidgetGroup,
    WidgetSelector,
    WidgetForm,
    MonitorSidebar,
    LinkedRecordsDetailModal,
    ProcessTemplate,
    InterfaceTemplate,
    GaugeDrilldownDrawer,
  },
  inheritAttrs: false,
  props: {
    params: {
      type: Object,
      default() {
        return {}
      },
    },
    allowCreate: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    widgets: {
      type: Array,
      default: undefined,
    },
    timeRange: {
      type: Object,
      default: undefined,
    },
    gridColumns: {
      type: Number,
      default() {
        return 12
      },
    },
    blinkingItems: {
      type: Array,
      default() {
        return []
      },
    },
    fullscreen: {
      type: Boolean,
      default: false,
    },
    hideWidgetActions: {
      type: Boolean,
      default: false,
    },
    disableOverflow: {
      type: Boolean,
      default: false,
    },
    maxWidgets: {
      type: Number,
      default: undefined,
    },
    dashboardStyle: {
      type: Object,
      default() {
        return {
          fontSize: 'medium',
          horizontalGap: 8,
          verticalGap: 8,
        }
      },
    },
  },
  data() {
    this.plusButtonPermissions = [
      this.$constants.WIDGET_SETTINGS_CREATE_PERMISSION,
      this.$constants.DASHBOARD_SETTINGS_UPDATE_PERMISSION,
    ]
    return {
      showConfirmDeleteModalFor: null,
      layoutIsReady: false,
      isDraggingWidget: false,
      isSelectingWidget: false,
      isCreating: false,
      innerWidgets: CloneDeep(this.widgets || []),
      defaultWidgetData: undefined,
      sidebarMonitorItem: null,
      containerScrollTop: 0,
      isLinkedRecordsDetailModalVisible: false,
      linkedRecordsDetailData: undefined,
      showTemplateFor: null,
      showGaugeDrilldownFor: null,
    }
  },
  computed: {
    shouldShowAddButton() {
      if (this.disabled) {
        return false
      }
      if (this.allowCreate) {
        if (this.maxWidgets && this.maxWidgets <= this.widgets.length) {
          return false
        }
        if (this.fullscreen) {
          return false
        }
        return true
      }
      return false
    },
    selectedWidgetIds() {
      return (this.widgets || []).map(({ id }) => id)
    },
    monitorSidebarRefreshKey() {
      const sidebarMonitorItem = this.sidebarMonitorItem || {}
      return `${sidebarMonitorItem.id}-${sidebarMonitorItem.name}-${sidebarMonitorItem.type}`
    },
  },
  watch: {
    widgets: function (newValue, oldValue) {
      if (newValue !== oldValue) {
        this.innerWidgets = CloneDeep(this.widgets || [])
        if (!this.shouldShowAddButton) {
          if (this.isSelectingWidget) {
            this.isSelectingWidget = false
          }
          if (this.isCreating) {
            this.isCreating = false
          }
        }
      }
    },
  },
  created() {
    this.queue = createQueue()
  },
  mounted() {
    this.__refreshPageTimeout = setTimeout(() => {
      window.location.reload()
    }, 1 * 60 * 60 * 1000)
    const handler = function (e) {
      mouseXY.x = e.clientX
      mouseXY.y = e.clientY
    }
    document.addEventListener('dragover', handler, false)
    const setup = this.$route.query._setup
    if (setup) {
      try {
        const decodedSetup = JSON.parse(atob(decodeURIComponent(setup)))
        if (decodedSetup.defaultData) {
          setTimeout(() => {
            this.isSelectingWidget = true
            this.defaultWidgetData = CloneDeep(decodedSetup.defaultData)
            this.isCreating = true
          }, 700)
        }
      } catch (e) {}
    }

    const hideHandler = () => {
      this.isSelectingWidget = false
      this.isCreating = false
    }

    Bus.$on('setup:hide-form-drawer', hideHandler)
    this.$once('hook:beforeDestroy', () => {})
    this.$once('hook:beforeDestroy', () => {
      Bus.$off('setup:hide-form-drawer', hideHandler)
      document.removeEventListener('dragover', handler, false)
    })
  },
  activated() {
    this.$refs.gridLayoutRef.eventBus.$emit(
      'updateWidth',
      this.$refs.widgetsContainerRef.offsetWidth
    )
  },
  beforeDestroy() {
    this.showGaugeDrilldownFor = null

    clearTimeout(this.__refreshPageTimeout)
    if (this.queue) {
      this.queue.clear()
      this.queue = null
    }
  },
  methods: {
    handleLayoutReady() {
      setTimeout(() => {
        Bus.$emit('widgets:layout:ready')
        this.layoutIsReady = true
      }, 400)
    },
    getScrollContainer() {
      return this.$refs.widgetsContainerRef
    },
    scrollToBottom() {
      scrollTo(
        this.$refs.widgetsContainerRef,
        this.$refs.widgetsContainerRef.scrollHeight
      )
    },
    resetSidebarMonitor() {
      this.sidebarMonitorItem = null
    },
    setSidebarMonitor(item, box) {
      this.sidebarMonitorItem = Object.freeze({
        ...item,
        ...(box ? { guid: box.id } : {}),
      })
    },

    setTemplateDrilldown(item, box) {
      this.showTemplateFor = Object.freeze({
        ...item,
        ...(box ? { guid: box.id } : {}),
      })
    },

    handleLayoutUpdate(updatedLayout) {
      if (this.isDraggingWidget) {
        return
      }
      const newStr = updatedLayout.reduce(
        (prev, item) =>
          `${prev}-${item.id}-${item.h}-${item.w}-${item.x}-${item.y}`,
        ''
      )
      const oldStr = this.widgets.reduce(
        (prev, item) =>
          `${prev}-${item.id}-${item.h}-${item.w}-${item.x}-${item.y}`,
        ''
      )
      if (newStr !== oldStr) {
        this.$emit('change', updatedLayout)
      }
    },
    addWidget(item) {
      let widgetSize = getWidgetDefaultSize(item)
      if (widgetSize[item.widgetType]) {
        widgetSize = widgetSize[item.widgetType]
      }
      if (!(widgetSize.h && widgetSize.w)) {
        widgetSize = { h: 3, w: 6 }
      }
      const widgetPosition = calculateNewItemPosition(
        widgetSize,
        this.widgets,
        this.gridColumns
      )
      this.$emit('add-widget', {
        id: item.id,
        ...widgetPosition,
        ...widgetSize,
        i: generateId(),
      })
    },
    handleRemoveWidget(widget) {
      this.$emit(
        'change',
        this.widgets.filter((w) => w.i !== widget.i)
      )
    },
    handleNewWidgetCreated(widget) {
      this.defaultWidgetData = undefined
      if (this.$refs.widgetSelectorRef) {
        this.$refs.widgetSelectorRef.addWidget(widget)
      }
      this.$nextTick(() => {
        this.isCreating = false
      })
    },
    handleWidgetUpdated(widget) {
      this.defaultWidgetData = undefined
      if (this.$refs.widgetSelectorRef) {
        this.$refs.widgetSelectorRef.updateWidget(widget)
      }
      this.$nextTick(() => {
        this.isCreating = false
      })
    },
    handleCancelWidgetForm() {
      this.isCreating = false
      this.defaultWidgetData = undefined
    },
    async handleCreateWidget(defaultData) {
      this.defaultWidgetData = await this.buildDefaultDataForWidget(defaultData)

      this.isCreating = true
      // this.defaultWidgetData = undefined
    },
    handleCloneWidget(widget) {
      getWidgetApi(widget.id).then((response) => {
        this.defaultWidgetData = {
          ...CloneDeep(response),
          id: undefined,
          name: `Copy of ${widget.name}`,
          clonning: true,
        }
        this.isCreating = true
      })
    },
    handleEditWidget(widget) {
      getWidgetApi(widget.id).then((response) => {
        this.defaultWidgetData = {
          ...CloneDeep(response),
        }
        this.isCreating = true
      })
    },
    handleUpdateHeight(item, height) {
      const index = FindIndex(this.innerWidgets, { i: item.i })
      if (index !== -1) {
        this.innerWidgets = [
          ...this.innerWidgets.slice(0, index),
          { ...item, h: height },
          ...this.innerWidgets.slice(index + 1),
        ]
      }
    },
    handleHideWidget(item) {
      const index = FindIndex(this.innerWidgets, { i: item.i })
      if (index !== -1) {
        this.innerWidgets = [
          ...this.innerWidgets.slice(0, index),
          { ...item, h: 0, w: 0, x: 0, y: 0 },
          ...this.innerWidgets.slice(index + 1),
        ]
      }
    },
    handleDeleteWidget(widget) {
      deleteWidgetApi(widget.id)
        .then(() => {
          this.showConfirmDeleteModalFor = null
          this.$refs.widgetSelectorRef.removeWidget(widget)
        })
        .catch((e) => {
          this.linkedRecordsDetailData = e.response.data
          this.isLinkedRecordsDetailModalVisible = e.response.status === 400
          throw e
        })
    },
    dragOverHandler(e) {
      e.preventDefault()
    },
    widgetDragStart(e) {
      this.$refs.widgetsContainerRef.addEventListener(
        'dragover',
        this.dragOverHandler
      )
      this.isDraggingWidget = true
      let parentRect = this.$refs.widgetsContainerRef.getBoundingClientRect()
      let mouseInGrid = false
      if (
        mouseXY.x > parentRect.left &&
        mouseXY.x < parentRect.right &&
        mouseXY.y > parentRect.top &&
        mouseXY.y < parentRect.bottom
      ) {
        mouseInGrid = true
      }
      let widget = e.target.dataset

      if (this.$refs.widgetSelectorRef) {
        widget =
          this.$refs.widgetSelectorRef.getWidgetById(e.target.dataset.id) ||
          e.target.dataset
      }

      let widgetSize = getWidgetDefaultSize(widget)
      if (
        mouseInGrid === true &&
        this.innerWidgets.findIndex((item) => item.i === 'drop') === -1
      ) {
        this.innerWidgets.push({
          x: (this.innerWidgets.length * 2) % (this.gridColumns || 12),
          y: this.innerWidgets.length + (this.gridColumns || 12), // puts it at the bottom
          ...widgetSize,
          id: e.target.dataset.id,
          i: 'drop',
        })
      }
      let index = this.innerWidgets.findIndex((item) => item.i === 'drop')
      if (index !== -1) {
        // try {
        // this.$refs.gridLayoutRef.$children[
        //   this.innerWidgets.length
        // ].$refs.item.style.display = 'none'
        // } catch {}
        let el = this.$refs.gridLayoutRef.$children[index]
        el.dragging = {
          top: mouseXY.y - parentRect.top,
          left: mouseXY.x - parentRect.left,
        }
        let newPos = el.calcXY(
          mouseXY.y - parentRect.top,
          mouseXY.x - parentRect.left
        )
        if (mouseInGrid === true) {
          this.$refs.gridLayoutRef.dragEvent(
            'dragstart',
            'drop',
            newPos.x,
            newPos.y,
            widgetSize.h,
            widgetSize.w
          )
          DragPos.i = generateId()
          DragPos.x = this.innerWidgets[index].x
          DragPos.y = this.innerWidgets[index].y
        }
        if (mouseInGrid === false) {
          this.$refs.gridLayoutRef.dragEvent(
            'dragend',
            'drop',
            newPos.x,
            newPos.y,
            widgetSize.h,
            widgetSize.w
          )
          this.innerWidgets = this.innerWidgets.filter(
            (obj) => obj.i !== 'drop'
          )
        }
      }
    },
    widgetDragEnd(e) {
      let parentRect = this.$refs.widgetsContainerRef.getBoundingClientRect()
      let mouseInGrid = false
      if (
        mouseXY.x > parentRect.left &&
        mouseXY.x < parentRect.right &&
        mouseXY.y > parentRect.top &&
        mouseXY.y < parentRect.bottom
      ) {
        mouseInGrid = true
      }
      let widget = e.target.dataset

      if (this.$refs.widgetSelectorRef) {
        widget =
          this.$refs.widgetSelectorRef.getWidgetById(e.target.dataset.id) ||
          e.target.dataset
      }

      let widgetSize = getWidgetDefaultSize(widget)

      if (mouseInGrid === true) {
        this.$refs.gridLayoutRef.dragEvent(
          'dragend',
          'drop',
          DragPos.x,
          DragPos.y,
          widgetSize.h,
          widgetSize.w
        )
        this.$refs.gridLayoutRef.dragEvent(
          'dragend',
          DragPos.i,
          DragPos.x,
          DragPos.y,
          widgetSize.h,
          widgetSize.w
        )
        this.$refs.gridLayoutRef.$children[
          this.innerWidgets.length
        ].$refs.item.style.display = 'block'
        this.innerWidgets = this.innerWidgets.filter((obj) => obj.i !== 'drop')
        this.innerWidgets.push({
          x: DragPos.x,
          y: DragPos.y,
          id: parseInt(e.target.dataset.id),
          ...widgetSize,
          i: generateId(),
        })
        setTimeout(() => {
          this.isDraggingWidget = false
          this.$refs.widgetsContainerRef.removeEventListener(
            'dragover',
            this.dragOverHandler
          )
          this.$emit('change', this.innerWidgets)
        }, 200)
      } else {
        this.isDraggingWidget = false
      }
    },
    async buildDefaultDataForWidget(defaultData) {
      return {
        timeRangeInclusive: false,
        category: defaultData.category,
        widgetType: defaultData.widgetType,
        ...(defaultData.category === WidgetTypeConstants.FREE_TEXT
          ? {
              name: `${
                WidgetTypeConstants.FREE_TEXT
              }-${Date.now()}-${Math.random()}`,
            }
          : []),
        ...(defaultData.category === WidgetTypeConstants.CHART ||
        defaultData.category === WidgetTypeConstants.ANOMALY ||
        defaultData.category === WidgetTypeConstants.FORECAST
          ? {
              granularity: {
                value: 5,
                unit: 'm',
              },
            }
          : {}),

        timeRange: {
          selectedKey: 'today',
          ...getRange('today'),
        },
        groups: CloneDeep(getWidgetGroupByChartType(defaultData.category)),
        widgetProperties: getWidgetProperties(
          defaultData.category,
          overrideWidgetPropertyByWidgetCategory(
            defaultData.category,
            defaultData.layout
              ? {
                  layout: WidgetTypeConstants.KEY_VALUE_LAYOUT,
                }
              : undefined
          )
        ),

        eventCount: [WidgetTypeConstants.EVENT_HISTORY].includes(
          defaultData.category
        )
          ? 500
          : undefined,
      }
    },
    setOpenGaugeDrilldownDrawer(drilldownContext) {
      this.showGaugeDrilldownFor = drilldownContext
    },
  },
}
</script>

<style lang="less" scoped>
.create-widget-btn {
  position: fixed;
  right: 20px;
  bottom: 20px;
  z-index: 99;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  box-shadow: 0 10px 15px -3px var(--primary), 0 4px 6px -2px var(--primary);
}
</style>
