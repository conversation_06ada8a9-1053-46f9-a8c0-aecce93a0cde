<template>
  <span v-if="shouldShowEditor">
    <slot :open="openModal">
      <MButton
        variant="transparent"
        :shadow="false"
        :rounded="false"
        shape="circle"
        @click="openModal"
      >
        <MIcon name="external-link" />
      </MButton>
    </slot>
    <MModal
      :open="isModalOpen"
      :width="1020"
      overlay-class-name="scrollable-modal hide-footer restrict-width"
      @hide="$emit('hide')"
    >
      <template v-slot:trigger>
        <span />
      </template>
      <template v-slot:title>
        <h4 class="mb-0 text-primary">
          {{ widget.name || 'Edit Widget Definition' }}
        </h4>
      </template>
      <div class="flex flex-1 min-h-0 flex-col bg-neutral-lightest">
        <CodeEditor
          v-model="widgetDefinition"
          mode="application/json"
          :style="{ height: `100%` }"
        />
      </div>
      <div class="text-right my-2">
        <MButton variant="default" @click="closeModal">Close</MButton>
        <MButton class="ml-2" variant="error" @click="handleUpdateDefinition">
          Update Definition
        </MButton>
      </div>
    </MModal>
  </span>
</template>

<script>
import Bus from '@utils/emitter'
import CodeEditor from '@components/code-editor.vue'
// import { transformWidgetforServer, transformWidget } from './helper'
import { transformWidgetForServer, transformWidget } from './translator'
import { updateWidgetApi } from './widgets-api'

export default {
  name: 'WidgetJsonEditor',
  components: {
    CodeEditor,
  },
  props: {
    widget: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      isModalOpen: false,
      widgetDefinition: '',
    }
  },
  computed: {
    shouldShowEditor() {
      if (process.env.NODE_ENV === 'production') {
        if (this.$route.query._showEditor || window._showEditor) {
          return true
        }
        return false
      }
      return true
    },
  },
  methods: {
    handleUpdateWidgetDefinition(widget) {
      if (widget.id) {
        updateWidgetApi(widget, false).then((data) => {
          Bus.$emit('widget.refresh', data.id)
        })
      }
    },
    handleUpdateDefinition() {
      try {
        const updatedDef = JSON.parse(this.widgetDefinition)
        this.handleUpdateWidgetDefinition(updatedDef)
        this.closeModal()
        this.$nextTick(() => {
          this.$emit('update', transformWidget(updatedDef))
        })
      } catch (e) {
        this.$errorToast('Invalid JSON')
      }
    },
    openModal() {
      this.isModalOpen = true
      this.widgetDefinition = JSON.stringify(
        {
          id: this.widget.id,
          ...transformWidgetForServer(this.widget),
        },
        undefined,
        4
      )
    },
    closeModal() {
      this.isModalOpen = false
    },
  },
}
</script>
