<template>
  <div>
    <MRow>
      <MCol>
        <span class="text-neutral-light">Sparkline Chart</span>
        <MSwitch
          v-model="showSparklineChart"
          class="mx-4"
          :disabled="disabled"
          checked-children="ON"
          un-checked-children="OFF"
        />
      </MCol>
    </MRow>
    <MRow>
      <MCol class="mt-2">
        <MRadioGroup
          v-if="showSparklineChart"
          v-model="sparklineChartType"
          as-button
          :disabled="disabled"
          :options="sparklineTypeOptions"
        >
          <template v-slot:option="{ option }">
            <WidgetTypeIcon
              :widget-type="option.icon"
              :tooltip="option.text"
              :size="28"
              class="relative"
              :selected="option.value === showSparklineChart"
            />
          </template>
        </MRadioGroup>

        <ColorPicker
          v-if="showSparklineChart"
          v-model="sparklineColor"
          :disabled="disabled"
          class="ml-2"
        />
      </MCol>
    </MRow>
  </div>
</template>

<script>
import Bus from '@utils/emitter'
import ColorPicker from '../../color-picker.vue'
import { WidgetTypeConstants } from '@components/widgets/constants'
import WidgetTypeIcon from '@components/widgets/widget-type-icon/widget-type-icon.vue'

export default {
  name: 'SparklineStyle',
  components: { ColorPicker, WidgetTypeIcon },
  model: { event: 'change' },
  props: {
    value: {
      type: Object,
      default: undefined,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },

  computed: {
    showSparklineChart: {
      get() {
        return (this.value || {}).showSparklineChart
      },
      set(showSparklineChart) {
        this.$emit('change', {
          ...(this.value || {}),
          showSparklineChart,
          sparklineChartType: 'sparkline',
        })
        this.$nextTick(this.generateNewPreview)
      },
    },
    sparklineChartType: {
      get() {
        return (this.value || {}).sparklineChartType
      },
      set(sparklineChartType) {
        this.$emit('change', { ...(this.value || {}), sparklineChartType })
      },
    },
    sparklineColor: {
      get() {
        return (this.value || {}).sparklineColor || '#099dd9'
      },
      set(sparklineColor) {
        this.$emit('change', { ...(this.value || {}), sparklineColor })
      },
    },
    sparklineTypeOptions() {
      return [
        { value: 'sparkline', text: 'Line', icon: WidgetTypeConstants.LINE },
        { value: 'sparkarea', text: 'Area', icon: WidgetTypeConstants.AREA },
        {
          value: 'sparkbar',
          text: 'Bar',
          icon: WidgetTypeConstants.VERTICAL_BAR,
        },
      ]
    },
  },
  methods: {
    generateNewPreview() {
      Bus.$emit('widget.generate.preview')
    },
  },
}
</script>
