<template>
  <MultipleFormItems
    v-model="markerProperty"
    :show-icon="false"
    :max-items="4"
    :item-template="defaultMarkerTemplate"
    add-btn-text="Add Markers"
  >
    <template v-slot="{ item, remove, add, index, total, canAdd, isLastItem }">
      <MRow>
        <MCol :size="2">
          <FlotoFormItem :label="index === 0 ? 'Type' : ''">
            <FlotoDropdownPicker
              v-model="item.markerType"
              class="w-full"
              :options="markerType"
              allow-clear
            />
          </FlotoFormItem>
        </MCol>
        <MCol :size="2">
          <FlotoFormItem
            v-if="!item.markerType"
            :label="index === 0 ? 'Threshold' : ''"
            name="threshold"
            validation-label="Threshold"
            placeholder="Threshold"
            :v-id="`${index}-threshold`"
            type="number"
            :disabled="!item.markerType"
          />

          <FlotoFormItem
            v-if="item.markerType === 'line'"
            v-model="item.markerThreshold"
            :label="index === 0 ? 'Threshold' : ''"
            rules="required"
            name="threshold"
            validation-label="Threshold"
            placeholder="Threshold"
            :v-id="`${index}-threshold`"
            type="number"
          />
          <template v-if="item.markerType === 'range'">
            <MRow :gutter="6">
              <MCol :size="6">
                <FlotoFormItem
                  v-model="item.start"
                  :label="index === 0 ? 'Start' : ''"
                  rules="required"
                  name="start"
                  validation-label="Start"
                  placeholder="Start"
                  :v-id="`${index}-start`"
                  type="number"
                />
              </MCol>
              <MCol :size="6">
                <FlotoFormItem
                  v-model="item.end"
                  :label="index === 0 ? 'End' : ''"
                  rules="required"
                  name="end"
                  validation-label="End"
                  placeholder="End"
                  :v-id="`${index}-end`"
                  type="number"
                />
              </MCol>
            </MRow>
          </template>
        </MCol>
        <MCol :size="2">
          <FlotoFormItem :label="index === 0 ? 'Color' : ''">
            <FlotoDropdownPicker
              v-model="item.markerColor"
              class="w-full"
              :options="rangeColorOptions"
              display-name
              as-input
            >
              <template v-slot:before-menu-text="{ item: optionItem }">
                <Severity
                  :severity="optionItem.key"
                  class="mr-2"
                  style="float: left"
                />
              </template>
            </FlotoDropdownPicker>
          </FlotoFormItem>
        </MCol>
        <MCol :size="2">
          <FlotoFormItem :label="index === 0 ? 'Line Type' : ''">
            <FlotoDropdownPicker
              v-model="item.markerLineType"
              class="w-full"
              :options="rangeLineTypeOptions"
              display-name
              as-input
            >
              <template v-slot:before-menu-text="{ item: optionItem }">
                <div
                  style="width: 20px; height: 5px; overflow: hidden"
                  class="flex items-center pr-1"
                >
                  <div v-if="optionItem.key === 'dash'" class="w-full flex"
                    >---------</div
                  >
                  <div
                    v-else-if="optionItem.key === 'solid'"
                    style="border-top: 1px solid var(--page-text-color)"
                    class="w-full flex"
                  ></div>
                  <div
                    v-else
                    style="border-top: 2px solid var(--page-text-color)"
                    class="w-full flex"
                  ></div>
                </div>
              </template>
            </FlotoDropdownPicker>
          </FlotoFormItem>
        </MCol>
        <MCol :size="2">
          <FlotoFormItem
            v-model="item.markerLabel"
            :label="index === 0 ? 'Label' : ''"
            name="label"
            validation-label="label"
            placeholder="Label"
            :v-id="`${index}-label`"
          />
        </MCol>
        <MCol auto-size class="flex items-center">
          <a v-if="total > 1" @click="remove">
            <MIcon name="times-circle" class="text-secondary-red" size="lg" />
          </a>
          <a v-if="isLastItem && canAdd" class="ml-2" @click="add">
            <MIcon name="plus-circle" class="text-primary" size="lg" />
          </a>
        </MCol>
      </MRow>
    </template>
  </MultipleFormItems>
</template>

<script>
import MultipleFormItems from '@components/multiple-form-items.vue'
import Constants from '@constants'
import Severity from '@components/severity'

export default {
  name: 'ChartMarkerProperty',

  components: {
    MultipleFormItems,
    Severity,
  },
  model: { event: 'change' },

  props: {
    value: { type: Array, default: undefined },
  },
  data() {
    this.defaultMarkerTemplate = {
      markerType: 'line',
    }
    this.markerType = [
      {
        key: 'line',
        text: 'Line',
      },
      {
        key: 'range',
        text: 'Range',
      },
    ]

    this.rangeColorOptions = [
      {
        key: Constants.CLEAR,
        text: 'Green/Clear',
      },
      {
        key: Constants.MAJOR,
        text: 'Orange/Major',
      },
      {
        key: Constants.WARNING,
        text: 'Yellow/Warning',
      },
      {
        key: Constants.CRITICAL,
        text: 'Red/Critical',
      },
    ]
    this.rangeLineTypeOptions = [
      {
        key: 'dash',
        text: 'Dash',
      },
      {
        key: 'solid',
        text: 'Solid',
      },
      {
        key: 'solid.bold',
        text: 'Solid Bold',
      },
    ]
    return {}
  },

  computed: {
    markerProperty: {
      get() {
        return (this.value || {}).markerProperty || []
      },
      set(markerProperty) {
        this.$emit('change', { ...(this.value || {}), markerProperty })
      },
      // get() {
      //   return this.value || []
      // },
      // set(markerProperty) {
      //   this.$emit('change', markerProperty)
      // },
    },
  },
}
</script>
