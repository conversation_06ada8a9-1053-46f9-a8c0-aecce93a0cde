<template>
  <div>
    <MRow class="w-full" :gutter="0">
      <MCol :size="3" class="mt-4 ml-2">
        <!-- <FlotoFormItem label=" ">
        <FlotoDropdownPicker
          v-model="direction"
          :options="topTypeOptions"
          :searchable="false"
          :disabled="disabled"
          allow-clear
        />
      </FlotoFormItem> -->
        <MRadioGroup v-model="direction" as-button :options="topTypeOptions" />
      </MCol>
    </MRow>
    <MRow class="w-full" :gutter="0">
      <MCol :size="3">
        <FlotoFormItem
          v-model="topCount"
          label="Count"
          placeholder="Count"
          :disabled="disabled"
          class="ml-2 mt-4"
          rules="numeric|max_value:50"
        />
      </MCol>
    </MRow>
  </div>
</template>

<script>
import Bus from '@utils/emitter'

export default {
  name: 'ChartSortingProperty',
  model: { event: 'change' },
  props: {
    value: {
      type: Object,
      default: undefined,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },

  computed: {
    topTypeOptions() {
      return [
        { value: 'desc', label: 'Top' },
        { value: 'asc', label: 'Last' },
      ]
    },
    direction: {
      get() {
        return (this.value || {}).direction
      },
      set(direction) {
        this.$emit('change', { ...(this.value || {}), direction })
        this.$nextTick(this.generateNewPreview)
      },
    },
    topCount: {
      get() {
        return (this.value || {}).topCount
      },
      set(topCount) {
        this.$emit('change', { ...(this.value || {}), topCount })
        this.$nextTick(this.generateNewPreview)
      },
    },
  },
  methods: {
    generateNewPreview() {
      Bus.$emit('widget.generate.preview')
    },
  },
}
</script>
