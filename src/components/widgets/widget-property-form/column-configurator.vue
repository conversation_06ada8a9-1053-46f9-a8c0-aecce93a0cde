<template>
  <MModal
    width="50%"
    open
    overlay-class-name="scrollable-modal"
    @hide="$emit('close')"
  >
    <template v-slot:title>
      <div class="flex items-center justify-between">
        <div class="flex-1">
          <h5 class="text-ellipsis m-0">
            {{ value.displayName || value.name }}
          </h5>
        </div>
        <MButton variant="transparent" :shadow="false" @click="$emit('close')">
          <MIcon name="times" />
        </MButton>
      </div>
    </template>

    <FlotoForm
      ref="formRef"
      class="overflow-y-auto overflow-x-hidden"
      @submit="$emit('change', formData)"
    >
      <h5 class="text-primary my-5">Basic Configurations</h5>
      <!-- <MDivider class="my-1" /> -->
      <MRow>
        <!-- <MCol :size="4">
          <FlotoFormItem v-model="formData.displayName" label="Display Name" />
        </MCol> -->

        <MCol :size="8" class="flex items-center">
          <FlotoFormItem class="flex-1">
            <!-- <MCheckbox v-model="formData.resizable" /> -->
            <label class="mr-2">Resizable</label>
            <MSwitch
              v-model="formData.resizable"
              checked-children="ON"
              un-checked-children="OFF"
            />
          </FlotoFormItem>
          <FlotoFormItem v-if="!isSparklineColumn" class="flex-1">
            <!-- <MCheckbox v-model="formData.sortable" /> -->
            <label class="mr-2">Sortable</label>
            <MSwitch
              v-model="formData.sortable"
              checked-children="ON"
              un-checked-children="OFF"
            />
          </FlotoFormItem>
          <FlotoFormItem class="flex-1">
            <!-- <MCheckbox v-model="formData.orderable" /> -->
            <label class="mr-2">Orderable</label>
            <MSwitch
              v-model="formData.orderable"
              checked-children="ON"
              un-checked-children="OFF"
            />
          </FlotoFormItem>
        </MCol>
        <MCol :size="4">
          <FlotoFormItem
            rules="regex:^\d+%?$"
            validation-label="Width"
            class="flex-1"
          >
            <label class="mr-2">Width (%)</label>
            <ColumnWidthInput v-model="formData.width" />
          </FlotoFormItem>
        </MCol>
        <!-- <MCol :size="4">
          <FlotoFormItem label="Visualization Type">
            <FlotoDropdownPicker
              v-model="formData.visulizationType"
              :options="visulizationTypeOptions"
            />
          </FlotoFormItem>
        </MCol> -->
      </MRow>
      <!-- <MDivider class="my-3" /> -->

      <template v-if="!isSparklineColumn">
        <h5 class="text-primary m-0 my-4">Color Configurations</h5>
        <!-- <MDivider class="my-1" /> -->
        <MultipleFormItems
          v-model="formData.colorConfig"
          :item-template="colorConfigTemplate"
          add-btn-text="Add Color Configuration"
        >
          <template v-slot="{ item, remove, isLastItem, canAdd, add, index }">
            <div class="flex items-center">
              <span class="relative" style="top: -0.25rem"> If value </span>
              <FlotoFormItem
                rules="required"
                :vid="`${index}-operator`"
                validation-label="Condition"
              >
                <FlotoDropdownPicker
                  v-model="item.operator"
                  class="mx-1"
                  style="max-width: 220px"
                  :options="operatorOptions"
                />
              </FlotoFormItem>
              <FlotoFormItem
                v-model="item.value"
                :rules="{ required: true }"
                class="mx-2"
                placeholder="Thresold"
                vaildation-label="Thresold"
              />
              <span class="mx-2 relative" style="top: -0.25rem"> set </span>
              <FlotoFormItem
                rules="required"
                validation-label="Type"
                :vid="`${index}-type`"
              >
                <!-- <FlotoDropdownPicker
                  v-model="item.colorType"
                  :options="colorTypeOptions"
                  style="max-width: 150px"
                  placeholder="Background"
                  :searchable="false"
                  class="mx-1"
                /> -->
                <MRadioGroup
                  v-model="item.colorType"
                  as-button
                  :options="colorTypeOptions"
                />
              </FlotoFormItem>
              <span class="mx-2 relative" style="top: -0.25rem"> to </span>
              <FlotoFormItem
                validation-label="Color"
                :vid="`${index}-color`"
                class="relative"
                style="top: 0.25rem"
              >
                <ColorPicker v-model="item.color" class="mx-1" />
              </FlotoFormItem>
              <span class="relative" style="top: -0.25rem" @click="remove">
                <MIcon
                  name="times-circle"
                  class="cursor-pointer text-secondary-red"
                  size="lg"
                />
              </span>
              <span
                v-if="isLastItem && canAdd"
                class="ml-2 relative"
                outline
                style="top: -0.25rem"
                @click="add"
              >
                <MIcon
                  name="plus-circle"
                  class="text-primary cursor-pointer"
                  size="lg"
                />
              </span>
            </div>
          </template>
        </MultipleFormItems>
        <h5 class="text-primary m-0 my-4">Value Configuration</h5>
        <!-- <MDivider class="my-1" /> -->
        <MultipleFormItems
          v-model="formData.valueDisplayConfig"
          :item-template="valueConfigTemplate"
          add-btn-text="Add Value Configuration"
        >
          <template v-slot="{ item, remove, isLastItem, canAdd, add, index }">
            <div class="flex items-center">
              <span class="relative" style="top: -0.25rem"> If value </span>
              <FlotoFormItem
                rules="required"
                :vid="`${index}-value-config-operator`"
                validation-label="Condition"
              >
                <FlotoDropdownPicker
                  v-model="item.operator"
                  class="mx-1"
                  style="max-width: 220px"
                  :options="operatorOptions"
                />
              </FlotoFormItem>
              <FlotoFormItem
                v-model="item.comparedValue"
                :rules="{ required: true }"
                class="mx-2"
                placeholder="Value"
                vaildation-label="Value"
                :vid="`${index}-value-config-value`"
              />
              <span class="mx-2 relative" style="top: -0.25rem"> set </span>
              <FlotoFormItem
                rules="required"
                validation-label="Type"
                :vid="`${index}-value-config-type`"
              >
                <!-- <FlotoDropdownPicker
                  v-model="item.colorType"
                  :options="colorTypeOptions"
                  style="max-width: 150px"
                  placeholder="Background"
                  :searchable="false"
                  class="mx-1"
                /> -->
                <MRadioGroup
                  v-model="item.operatorType"
                  as-button
                  :options="operatorTypeOptions"
                />
              </FlotoFormItem>
              <span class="mx-2 relative" style="top: -0.25rem"> to </span>
              <FlotoFormItem
                v-model="item.value"
                :validation-label="
                  item.operatorType === 'suffix' ? 'Suffix' : 'Prefix'
                "
                :rules="{ required: true }"
                :vid="`${index}-value-config-value-addition`"
                class="relative"
                style="top: 0.25rem"
              />
              <span class="relative" style="top: -0.25rem" @click="remove">
                <MIcon
                  name="times-circle"
                  class="cursor-pointer text-secondary-red"
                  size="lg"
                />
              </span>
              <span
                v-if="isLastItem && canAdd"
                class="ml-2 relative"
                outline
                style="top: -0.25rem"
                @click="add"
              >
                <MIcon
                  name="plus-circle"
                  class="text-primary cursor-pointer"
                  size="lg"
                />
              </span>
            </div>
          </template>
        </MultipleFormItems>
      </template>
      <!-- <MDivider class="my-5" /> -->

      <template v-if="!isSparklineColumn">
        <h5 class="text-primary m-0 mt-4">Icon</h5>
        <!-- <MDivider class="my-1" /> -->
        <MRow class="my-3">
          <MCol :size="3">
            <FlotoFormItem label="Icon">
              <FlotoDropdownPicker
                v-model="formData.iconName"
                :options="iconOptions"
                allow-clear
              >
                <template v-slot:before-menu-text="{ item }">
                  <MIcon v-if="item" :name="item.key" class="mr-1" />
                </template>
              </FlotoDropdownPicker>
            </FlotoFormItem>
          </MCol>
          <MCol :size="3">
            <FlotoFormItem
              :rules="{ required: Boolean(formData.iconName) }"
              label="Icon Position"
            >
              <MRadioGroup
                v-model="formData.iconPosition"
                as-button
                :options="iconPositionOptions"
              >
                <template v-slot:option="{ option }">
                  <MIcon
                    :name="
                      option.value === 'suffix'
                        ? 'arrow-from-right'
                        : 'arrow-from-left'
                    "
                  />
                </template>
              </MRadioGroup>
            </FlotoFormItem>
          </MCol>
        </MRow>
      </template>

      <template v-slot:submit>
        <span />
      </template>
    </FlotoForm>

    <template v-slot:footer>
      <div class="text-right">
        <MButton variant="default" @click="handleReset"> Reset </MButton>
        <MButton @click="handleSubmitForm"> Update </MButton>
      </div>
    </template>
  </MModal>
</template>

<script>
import CloneDeep from 'lodash/cloneDeep'
import MultipleFormItems from '@components/multiple-form-items.vue'
import ColorPicker from '@components/color-picker.vue'
import ColumnWidthInput from './column-width-input.vue'
import { getOperatorOptions } from '../helper'
import { IconPositionOptions } from '../constants'
import iconOptions from '../column-icons'

export default {
  name: 'ColumnConfigurator',
  components: {
    MultipleFormItems,
    ColumnWidthInput,
    ColorPicker,
  },
  model: { event: 'change' },
  props: {
    value: {
      type: Object,
      required: true,
    },
  },
  data() {
    this.colorConfigTemplate = {
      colorType: 'background',
    }
    this.valueConfigTemplate = {
      operatorType: 'prefix',
    }
    this.colorTypeOptions = [
      { value: 'background', label: 'Background' },
      { value: 'foreground', label: 'Foreground' },
    ]
    this.operatorTypeOptions = [
      { value: 'prefix', label: 'Prefix' },
      { value: 'suffix', label: 'Suffix' },
    ]
    this.operatorOptions = getOperatorOptions([
      '>',
      '>=',
      '<',
      '<=',
      '=',
      'contain',
    ])
    this.visulizationTypeOptions = []
    this.iconPositionOptions = CloneDeep(IconPositionOptions)
    this.iconOptions = CloneDeep(iconOptions())
    return {
      formData: CloneDeep(this.value),
    }
  },
  computed: {
    isSparklineColumn() {
      return /\.sparkline$/.test(this.value.name)
    },
  },
  methods: {
    handleSubmitForm() {
      this.$refs.formRef.submit()
    },
    handleReset() {
      this.formData = CloneDeep(this.value)
    },
  },
}
</script>
