<template>
  <div
    class="flex-1 flex flex-col min-h-0 overflow-y-auto w-full"
    :class="{
      'overflow-x-hidden': !allowXOverFlow,
      'overflow-x-auto': allowXOverFlow,
    }"
  >
    <MRow class="mb-2" :gutter="0">
      <MCol :size="5">
        <small class="text-neutral-light">Column Name</small>
      </MCol>
      <MCol :size="4">
        <small class="text-neutral-light">Display Name</small>
      </MCol>
      <MCol :size="1" class="flex justify-left">
        <small class="text-neutral-light" style="white-space: nowrap"
          >Hide Column</small
        >
      </MCol>
      <MCol :size="2" />
    </MRow>
    <SortableList
      tag="div"
      class="flex flex-col"
      :value="columns"
      :sort="true"
      handle=".swap-icon"
      group-name="column-order"
      @change="handleOrderChange"
    >
      <ColumnSettingRow
        v-for="(column, index) in columns"
        :key="column.key"
        :value="value[index]"
        :hide-disabled="
          hiddenColumnCount === value.length - 1 && column.hidden === false
        "
        @change="updateColumn(index, $event)"
      />
    </SortableList>

    <template v-if="shouldShowTagDropdown">
      <MDivider class="my-1 mx-2" />

      <MRow :gutter="0">
        <MCol :size="5" class="flex items-center text-center">
          Select Tag to Add as Column
        </MCol>
        <MCol :size="5">
          <FlotoFormItem label=" ">
            <FlotoDropdownPicker
              v-model="visualizationTags"
              :searchable="true"
              placeholder=" "
              :options="finalTagOptions"
              allow-clear
              multiple
            />
          </FlotoFormItem>
        </MCol>
      </MRow>
    </template>
  </div>
</template>

<script>
import UniqBy from 'lodash/uniqBy'

import SortableList from '@components/sortable/sortable-list.vue'
import ColumnSettingRow from './column-setting-row.vue'

export default {
  name: 'ColumnSetting',
  components: {
    SortableList,
    ColumnSettingRow,
  },
  inject: {
    widgetFormContext: {
      default: {
        setSelectedTags() {},
        formData: {},
      },
    },
  },
  model: { event: 'change' },
  props: {
    value: {
      type: Array,
      required: true,
    },
    allowXOverFlow: {
      type: Boolean,
      default: false,
    },
    tagOptions: {
      type: Array,
      default: () => [],
    },
    shouldShowTagDropdown: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    this.MAX_TAG_SELECTION_LIMIT = 5
    return {}
  },

  computed: {
    columns() {
      return this.value
    },
    hiddenColumnCount() {
      return this.value.filter((c) => c.hidden).length
    },
    visualizationTags: {
      get() {
        return this.widgetFormContext.formData.visualizationTags
      },
      set(visualizationTags) {
        if (visualizationTags?.length > this.MAX_TAG_SELECTION_LIMIT) {
          visualizationTags = visualizationTags.slice(
            visualizationTags.length - this.MAX_TAG_SELECTION_LIMIT
          )
        }
        this.widgetFormContext.setSelectedTags(visualizationTags)
      },
    },
    finalTagOptions() {
      return UniqBy(
        this.tagOptions.map((tag) => ({
          key: tag.splitedKey,
          text: tag.splitedKey,
        })),
        'key'
      )
    },
  },

  methods: {
    updateColumn(index, $event) {
      this.$emit('change', [
        ...this.value.slice(0, index),
        { ...$event },
        ...this.value.slice(index + 1),
      ])
    },
    handleOrderChange(order) {
      this.$emit(
        'change',
        order.map((i, index) => ({ ...i, orderIndex: index + 1 }))
      )
    },
  },
}
</script>
