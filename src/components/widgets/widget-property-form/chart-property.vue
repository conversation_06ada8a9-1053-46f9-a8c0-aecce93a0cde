<template>
  <div class="flex flex-col px-2 flex-1 min-h-0 w-full">
    <MRow v-if="!hideTab" class="min-h-0">
      <MCol :size="12" class="mb-2">
        <MTab v-model="currentTab">
          <MTabPane key="style" tab="Style" />
          <MTabPane v-if="!isAiMlWidget" key="sorting" tab="Sorting" />
          <MTabPane key="markers" tab="Markers" />
        </MTab>
      </MCol>
    </MRow>
    <div
      class="flex flex-col flex-1 min-h-0 overflow-y-auto overflow-x-hidden"
      :class="{
        'overflow-x-hidden': !allowXOverFlow,
        'overflow-x-auto': allowXOverFlow,
      }"
    >
      <ChartStyleProperty
        v-if="currentTab === 'style'"
        v-model="styleSetting"
        :is-ai-ml-widget="isAiMlWidget"
        :widget-type="widgetType"
        :is-report-preview="isReportPreview"
        @change-widget-type="$emit('change-widget-type', $event)"
      />
      <ChartSortingProperty
        v-else-if="currentTab === 'sorting'"
        v-model="sortingSetting"
      />
      <ChartMarkerProperty
        v-else-if="currentTab === 'markers'"
        v-model="styleSetting"
      />
    </div>
  </div>
</template>

<script>
import { WidgetTypeConstants } from '../constants'
import ChartSortingProperty from './chart-sorting-property.vue'
import ChartStyleProperty from './chart-style-property.vue'
import ChartMarkerProperty from './chart-marker-property.vue'

export default {
  name: 'ChartProprty',
  components: {
    ChartStyleProperty,
    ChartSortingProperty,
    ChartMarkerProperty,
  },
  inheritAttrs: false,
  model: { event: 'change' },
  props: {
    widgetType: {
      type: String,
      default: undefined,
    },
    value: {
      type: Object,
      default: undefined,
    },
    hideTab: {
      type: Boolean,
      default: false,
    },
    isReportPreview: {
      type: Boolean,
      default: false,
    },
    allowXOverFlow: {
      type: Boolean,
      default: false,
    },
    widgetCategory: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      currentTab: 'style',
    }
  },
  computed: {
    isAiMlWidget() {
      return [
        WidgetTypeConstants.FORECAST,
        WidgetTypeConstants.ANOMALY,
      ].includes(this.widgetCategory)
    },
    styleSetting: {
      get() {
        return (this.value || {}).styleSetting || {}
      },
      set(styleSetting) {
        this.$emit('change', { ...(this.value || {}), styleSetting })
      },
    },
    sortingSetting: {
      get() {
        return (this.value || {}).sortingSetting || {}
      },
      set(sortingSetting) {
        this.$emit('change', { ...(this.value || {}), sortingSetting })
      },
    },
    // markerProperty: {
    //   get() {
    //     return (this.value || {}).markerProperty || []
    //   },
    //   set(markerProperty) {
    //     this.$emit('change', { ...(this.value || {}), markerProperty })
    //   },
    // },
  },
}
</script>
