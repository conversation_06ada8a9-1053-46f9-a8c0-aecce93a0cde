<template>
  <MRow class="w-full" :gutter="16">
    <MCol :size="8">
      <FlotoFormItem label="Counter">
        <FlotoDropdownPicker
          v-model="column"
          :options="columnOptions"
          :disabled="disabled"
        />
      </FlotoFormItem>
    </MCol>
    <MCol :size="4" />
    <MCol :size="4" class="mt-6">
      <!-- <FlotoFormItem label=" " class="ml-4">
        <FlotoDropdownPicker
          v-model="direction"
          :options="topTypeOptions"
          :searchable="false"
          :disabled="disabled"
        />
      </FlotoFormItem> -->
      <MRadioGroup v-model="direction" as-button :options="topTypeOptions" />
    </MCol>
    <MCol :size="4" style="padding-left: 0 !important">
      <FlotoFormItem
        v-model="topCount"
        placeholder="Count"
        :disabled="disabled"
        label="Count"
        class="ml-4"
        rules="numeric|max_value:50"
      />
    </MCol>
  </MRow>
</template>

<script>
import Bus from '@utils/emitter'

export default {
  name: 'TopnSortingProperty',
  model: { event: 'change' },
  props: {
    value: {
      type: Object,
      default: undefined,
    },
    columns: {
      type: Array,
      required: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    groupByColumns: {
      type: Array,
      default: undefined,
    },
  },
  data() {
    this.ignoreRowNames = ['object.type', 'object.vendor', 'object.ip']
    return {}
  },
  computed: {
    sparklineTypeOptions() {
      return [
        { value: 'sparkline', text: 'Line' },
        { value: 'sparkarea', text: 'Area' },
        { value: 'sparkbar', text: 'Bar' },
      ]
    },
    topTypeOptions() {
      return [
        { value: 'desc', label: 'Top' },
        { value: 'asc', label: 'Last' },
      ]
    },
    columnOptions() {
      const columnsWithOutSparkline = this.columns.filter(
        (c) => /\.sparkline$/.test(c.name) === false
      )
      if (this.groupByColumns) {
        const groupColumns = this.groupByColumns
        return columnsWithOutSparkline
          .filter(
            (c) =>
              groupColumns?.includes(c.name) === false &&
              !this.ignoreRowNames.includes(c.rawName)
          )
          .map((c) => ({
            key: (c.rawName || c.name).replace(/\^/g, '.'),
            text: c.name.replace(/[~^]/g, '.'),
          }))
      }
      return columnsWithOutSparkline.map((c) => ({
        key: (c.rawName || c.name).replace(/\^/g, '.'),
        text: c.name.replace(/[~^]/g, '.'),
      }))
    },
    column: {
      get() {
        return (this.value || {}).column
      },
      set(column) {
        this.$emit('change', { ...(this.value || {}), column })
        this.$nextTick(this.generateNewPreview)
      },
    },
    direction: {
      get() {
        return (this.value || {}).direction
      },
      set(direction) {
        this.$emit('change', { ...(this.value || {}), direction })
        this.$nextTick(this.generateNewPreview)
      },
    },
    topCount: {
      get() {
        return (this.value || {}).topCount
      },
      set(topCount) {
        this.$emit('change', { ...(this.value || {}), topCount })
        this.$nextTick(this.generateNewPreview)
      },
    },
  },
  methods: {
    generateNewPreview() {
      Bus.$emit('widget.generate.preview')
    },
  },
}
</script>
