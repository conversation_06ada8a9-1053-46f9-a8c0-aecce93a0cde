<template>
  <div class="flex flex-col px-2 flex-1 min-h-0 w-full">
    <MRow v-if="!hideTab" class="min-h-0">
      <MCol :size="12" class="mb-2">
        <MTab v-model="currentTab">
          <MTabPane key="style" tab="Style" />
        </MTab>
      </MCol>
    </MRow>
    <div class="item flex flex-col mt-2">
      <MRow>
        <MCol :size="6">
          <label class="mb-1 text-neutral-light">Result By</label>
          <SortableList
            tag="div"
            class="flex flex-col mt-2"
            :value="selectedKeys"
            :sort="true"
            handle=".swap-icon"
            group-name="source-order"
            @change="handleChange('orderChange', $event)"
          >
            <div
              v-for="v in selectedKeys"
              :key="v"
              class="selected-item inline-flex items-center mb-1 bg-neutral-lightest rounded-lg"
            >
              <span class="pr-2 flex-1">{{ v }}</span>
              <MIcon
                name="drag"
                size="lg"
                class="mx-1 swap-icon cursor-move text-neutral-light"
              />
              <MIcon
                v-if="selectedKeys.length > 2"
                name="times"
                class="mx-1 cursor-pointer text-neutral-light"
                size="lg"
                @click="handleChange('remove', v)"
              />
            </div>
          </SortableList>
        </MCol>
      </MRow>
    </div>
  </div>
</template>

<script>
import SortableList from '@components/sortable/sortable-list.vue'
export default {
  name: 'SankeyProperty',
  components: { SortableList },
  props: {
    widget: {
      type: Object,
      default: undefined,
    },
    hideTab: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      currentTab: 'style',
    }
  },
  computed: {
    selectedKeys() {
      return this.widget?.groups?.[0]?.resultBy || []
    },
  },
  methods: {
    handleChange(actionType, data) {
      if (actionType === 'orderChange') {
        this.$emit('change-result-by', data)
      } else {
        this.$emit(
          'change-result-by',
          this.selectedKeys.filter((i) => i !== data)
        )
      }
    },
  },
}
</script>

<style lang="less" scoped>
.selected-item {
  height: 35px;
  padding: 10px;

  .selected-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 25px;
    height: 25px;
    border-radius: 5px;

    &.source {
      color: @flow-source-color;
    }

    &.destination {
      color: @flow-destination-color;
    }

    &.metric {
      color: @flow-metric-color;
    }
  }
}

.chart-type-picker {
  .@{ant-prefix}-input-prefix {
    > div {
      position: relative;
      left: -5px;
    }
  }
}
</style>
