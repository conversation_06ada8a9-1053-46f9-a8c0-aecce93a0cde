<template>
  <MInput
    v-model="innerValue"
    placeholder="For ex. 20"
    type="number"
    :max="100"
    :min="2"
    :precision="0"
  />
</template>

<script>
export default {
  name: 'ColumnWidthInput',
  model: { event: 'change' },
  props: {
    value: {
      type: [Number, String],
      default: undefined,
    },
  },
  computed: {
    innerValue: {
      get() {
        if (/^\d+%$/.test(this.value)) {
          return this.value.replace('%', '')
        }
        return undefined
      },
      set(value) {
        if (/^\d+$/.test(value)) {
          if (value === 0) {
            this.$emit('change', undefined)
          } else {
            this.$emit('change', `${value}%`)
          }
        } else {
          this.$emit('change', value)
        }
      },
    },
  },
}
</script>
