<template>
  <MRow :gutter="0" class="flex flex-col px-2 flex-1 min-h-0 w-full">
    <MCol :size="12">
      <MRow class="w-full" :gutter="0">
        <MCol :size="3">
          <FlotoFormItem
            v-model="textToDisplay"
            label="Text to display"
            placeholder="Enter the text"
            rules="required"
          />
        </MCol>
      </MRow>
      <MRow class="w-full" :gutter="0">
        <MCol :size="3">
          <FlotoFormItem label="Font Size">
            <FlotoDropdownPicker
              v-model="fontSize"
              :options="fontSizeOptions"
              :disabled="disabled"
            />
          </FlotoFormItem>
        </MCol>
      </MRow>
      <MRow class="w-full" :gutter="0">
        <MCol :size="3">
          <FlotoFormItem label="Font Color">
            <FlotoDropdownPicker
              v-model="fontColor"
              :options="colorOptions"
              :disabled="disabled"
            >
              <template v-slot:before-menu-text="{ item: optionItem }">
                <div
                  class="rounded-sm"
                  style="
                    display: inline-block;
                    width: 12px;
                    height: 12px;
                    margin-right: 8px;
                    vertical-align: middle;
                  "
                  :style="{
                    background: optionItem.color,
                  }"
                ></div>
              </template>
            </FlotoDropdownPicker>
          </FlotoFormItem>
        </MCol>
      </MRow>
      <MRow class="w-full" :gutter="0">
        <MCol :size="3">
          <FlotoFormItem label="Text Align">
            <MRadioGroup
              v-model="textAlign"
              as-button
              :options="textAlignOptions"
            >
              <template v-slot:option="{ option }">
                <MIcon
                  size="lg"
                  style="margin-right: 0 !important"
                  :name="
                    option.value === 'left'
                      ? 'align-left'
                      : option.value === 'right'
                      ? 'align-right'
                      : 'align-center'
                  "
                />
              </template>
            </MRadioGroup>
          </FlotoFormItem>
        </MCol>
      </MRow>
    </MCol>
  </MRow>
</template>

<script>
import {
  TextAlignOptions,
  FontSizeOptionsForFreeText,
  ColorOptionsForFreeText,
} from '../constants'

export default {
  name: 'FreeTextPropertyForm',
  inheritAttrs: false,
  model: { event: 'change' },
  props: {
    value: {
      type: Object,
      default: undefined,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    this.fontSizeOptions = FontSizeOptionsForFreeText

    this.colorOptions = ColorOptionsForFreeText
    return {}
  },

  computed: {
    textAlignOptions() {
      return TextAlignOptions
    },
    textToDisplay: {
      get() {
        return this.value.textToDisplay
      },
      set(textToDisplay) {
        this.$emit('change', {
          ...(this.value || {}),
          textToDisplay,
        })
      },
    },
    fontSize: {
      get() {
        return this.value.fontSize
      },
      set(fontSize) {
        this.$emit('change', {
          ...(this.value || {}),
          fontSize,
        })
      },
    },
    fontColor: {
      get() {
        return this.value.fontColor
      },
      set(fontColor) {
        this.$emit('change', {
          ...(this.value || {}),
          fontColor,
        })
      },
    },
    textAlign: {
      get() {
        return (this.value || {}).textAlign
      },
      set(textAlign) {
        this.$emit('change', { ...(this.value || {}), textAlign })
      },
    },
  },
}
</script>
