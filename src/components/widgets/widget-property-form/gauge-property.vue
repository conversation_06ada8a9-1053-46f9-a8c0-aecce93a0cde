<template>
  <div class="flex flex-col px-2 flex-1 min-h-0 w-full">
    <MRow class="min-h-0">
      <MCol :size="12" class="mb-2">
        <MTab v-model="currentTab">
          <MTabPane key="style" tab="Style" />
          <MTabPane
            v-if="shouldShowThresoldTab"
            key="threshold"
            tab="Threshold"
          />
        </MTab>
      </MCol>
    </MRow>

    <div
      class="flex flex-col flex-1 min-h-0 overflow-y-auto"
      :class="{
        'overflow-x-hidden': !allowXOverFlow,
        'overflow-x-auto': allowXOverFlow,
      }"
    >
      <MRow v-if="currentTab === 'style'">
        <MCol :size="12">
          <MRow>
            <MCol :size="4">
              <FlotoFormItem v-if="allowTypeSelection">
                <MRadioGroup
                  v-model="innerWidgetType"
                  as-button
                  :options="chartTypeOptions"
                  class="chart-selector-radio mt-4"
                >
                  <template v-slot:option="{ option }">
                    <div class="flex items-center justify-center h-full">
                      <WidgetTypeIcon
                        :widget-type="option.value"
                        :tooltip="option.text"
                        :size="28"
                        :selected="option.value === widgetType"
                      />
                    </div>
                  </template>
                </MRadioGroup>
                <!-- <FlotoDropdownPicker
                  v-model="innerWidgetType"
                  as-button
                  :options="chartTypeOptions"
                  class="chart-type-picker"
                  :searchable="false"
                  :allow-clear="false"
                >
                  <template v-slot:before-menu-text="{ item }">
                    <WidgetTypeIcon
                      :widget-type="item.key"
                      :tooltip="item.text"
                      :size="15"
                    />
                  </template>
                </FlotoDropdownPicker> -->
              </FlotoFormItem>
            </MCol>
          </MRow>
        </MCol>
        <MCol v-if="isAvailabilityWidget || isAlertWidget" :size="12">
          <FlotoFormItem>
            <MRadioGroup
              v-model="layout"
              as-button
              :options="chartTypeOptions"
              class="chart-selector-radio mt-4"
            >
              <template v-slot:option="{ option }">
                <div class="flex items-center justify-center h-full">
                  <WidgetTypeIcon
                    :widget-type="option.value"
                    :tooltip="option.text"
                    :size="28"
                    :selected="option.value === layout"
                  />
                </div>
              </template>
            </MRadioGroup>
          </FlotoFormItem>
          <FlotoFormItem
            v-if="layout === WidgetTypeConstants.PIE"
            label="Legend"
            class="mr-4"
          >
            <MSwitch
              v-model="legendEnabled"
              checked-children="ON"
              un-checked-children="OFF"
            />
          </FlotoFormItem>

          <FlotoFormItem
            v-if="layout === WidgetTypeConstants.PIE"
            label="Data Labels"
          >
            <MSwitch
              v-model="pieDataLabelsEnabled"
              checked-children="ON"
              un-checked-children="OFF"
            />
          </FlotoFormItem>
          <!-- <FlotoFormItem
            v-if="isAlertWidget || isAvailabilityWidget"
            label="Severity Counts"
          >
            <MSwitch
              :checked="isRadialView"
              checked-children="ON"
              un-checked-children="OFF"
              @change="handelViewChange(!isRadialView)"
            />
          </FlotoFormItem> -->
        </MCol>
        <template v-else>
          <!-- <MCol :size="12">
            <MRow :gutter="0">
              <MCol :size="6">
                <FlotoFormItem v-model="titleText" label="Title" />
              </MCol>
            </MRow>
          </MCol> -->
          <MCol :size="5">
            <!-- <FlotoFormItem label="Font Size">
              <FlotoDropdownPicker
                v-model="fontSize"
                :options="headerFontSizeOptions"
                placeholder="Select Font Size"
              />
            </FlotoFormItem> -->
            <FlotoFormItem label="Font Size">
              <MRadioGroup
                v-model="fontSize"
                as-button
                :options="headerFontSizeOptions"
              />
            </FlotoFormItem>
          </MCol>
          <MCol v-if="allowIconAndTextAlignmentSelection" :size="5">
            <!-- <FlotoFormItem label="Text Align">
              <FlotoDropdownPicker
                v-model="textAlign"
                :options="textAlignOptions"
                placeholder="Select Text Alignment"
              />
            </FlotoFormItem> -->

            <FlotoFormItem label="Text Align">
              <MRadioGroup
                v-model="textAlign"
                as-button
                :options="textAlignOptions"
              >
                <template v-slot:option="{ option }">
                  <MIcon
                    size="lg"
                    style="margin-right: 0 !important"
                    :name="
                      option.value === 'left'
                        ? 'align-left'
                        : option.value === 'right'
                        ? 'align-right'
                        : 'align-center'
                    "
                  />
                </template>
              </MRadioGroup>
            </FlotoFormItem>
          </MCol>
        </template>
      </MRow>

      <MRow v-if="currentTab === 'style'">
        <MCol v-if="allowIconAndTextAlignmentSelection" :size="5">
          <FlotoFormItem label="Icon ">
            <FlotoDropdownPicker
              v-model="iconName"
              :options="iconOptions"
              allow-clear
            >
              <template v-slot:before-menu-text="{ item }">
                <MIcon v-if="item" :name="item.key" class="mr-1" />
              </template>
            </FlotoDropdownPicker>
          </FlotoFormItem>
        </MCol>
        <MCol v-if="allowIconAndTextAlignmentSelection" :size="5">
          <FlotoFormItem
            :rules="{ required: Boolean(iconName) }"
            label="Icon Position"
          >
            <!-- <FlotoDropdownPicker
              v-model="iconPosition"
              :options="iconPositionOptions"
              :searchable="false"
            /> -->
            <MRadioGroup
              v-model="iconPosition"
              as-button
              :options="iconPositionOptions"
            >
              <template v-slot:option="{ option }">
                <MIcon
                  size="lg"
                  style="margin-right: 0 !important"
                  :name="
                    option.value === 'suffix'
                      ? 'arrow-from-right'
                      : 'arrow-from-left'
                  "
                />
              </template>
            </MRadioGroup>
          </FlotoFormItem>
        </MCol>
      </MRow>

      <MRow v-if="canConfigureColor && currentTab === 'threshold'">
        <MCol :size="12">
          <GaugeColor v-model="criticalColor" severity="Critical" />
        </MCol>
        <MCol :size="12">
          <GaugeColor v-model="majorColor" severity="Major" />
        </MCol>
        <MCol :size="12">
          <GaugeColor v-model="warningColor" severity="Warning" />
        </MCol>
      </MRow>
    </div>
  </div>
</template>

<script>
import CloneDeep from 'lodash/cloneDeep'
import { getAllowedUnit } from '@utils/unit-checker'
import {
  FontSizeOptions,
  IconPositionOptions,
  WidgetTypeConstants,
  TextAlignOptions,
} from '../constants'
import iconOptions from '../column-icons'
import GaugeColor from './gauge-color.vue'
import WidgetTypeIcon from '../widget-type-icon/widget-type-icon.vue'

export default {
  name: 'GaugeProperty',
  components: {
    GaugeColor,
    WidgetTypeIcon,
  },
  inheritAttrs: false,
  model: { event: 'change' },
  props: {
    widgetType: {
      type: String,
      default: undefined,
    },
    widget: {
      type: Object,
      default: undefined,
    },
    value: {
      type: Object,
      default: undefined,
    },
    allowXOverFlow: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    this.iconOptions = CloneDeep(iconOptions())
    return {
      currentTab: 'style',
    }
  },
  computed: {
    shouldShowThresoldTab() {
      return !['availability', 'policy'].includes(this.widget?.groups[0]?.type)
    },
    WidgetTypeConstants() {
      return WidgetTypeConstants
    },
    isAvailabilityWidget() {
      return Boolean(this.widget.groups.find((g) => g.type === 'availability'))
    },
    isAlertWidget() {
      return Boolean(
        this.widget.groups.find(
          (g) => g.type === 'alert' || g.type === 'policy'
        )
      )
    },
    canConfigureColor() {
      if (this.isAvailabilityWidget || this.isAlertWidget) {
        return false
      }
      return true
    },
    allowIconAndTextAlignmentSelection() {
      if (this.isAvailabilityWidget || this.isAlertWidget) {
        return false
      }
      return this.widget.widgetType === WidgetTypeConstants.METRO_TILE
    },
    allowTypeSelection() {
      if (this.isAvailabilityWidget || this.isAlertWidget) {
        return false
      }
      const group = (this.widget.groups || [])[0]
      if (group && group.counters && group.counters.length > 0) {
        if (group.counters[0].counter && group.counters[0].counter.key) {
          const unit = getAllowedUnit(group.counters[0].counter.key)
          return unit === 'percent'
        }
      }
      return false
    },
    chartTypeOptions() {
      if (this.isAvailabilityWidget || this.isAlertWidget) {
        return [
          WidgetTypeConstants.PIE,
          WidgetTypeConstants.RADIAL_VIEW,
          WidgetTypeConstants.PROGRESS_WITH_COUNT_VIEW,
          WidgetTypeConstants.HORIZONTAL_BAR_WITH_COUNT_VIEW,
        ].map((t) => ({ value: t, text: t }))
      } else {
        return [
          WidgetTypeConstants.METRO_TILE,
          WidgetTypeConstants.SOLID_GAUGE,
        ].map((t) => ({ value: t, text: t }))
      }
    },
    iconPositionOptions() {
      return IconPositionOptions
    },
    headerFontSizeOptions() {
      return FontSizeOptions
    },
    textAlignOptions() {
      return TextAlignOptions
    },
    innerWidgetType: {
      get() {
        return this.widgetType
      },
      set(type) {
        this.$emit('change-widget-type', type)
      },
    },
    layout: {
      get() {
        return (this.value || {}).layout || WidgetTypeConstants.PIE
      },
      set(layout) {
        this.$emit('change', {
          ...(this.value || {}),
          layout: layout,
        })
      },
    },
    fontSize: {
      get() {
        return (this.value || {}).fontSize
      },
      set(fontSize) {
        this.$emit('change', { ...(this.value || {}), fontSize })
      },
    },
    textAlign: {
      get() {
        return (this.value || {}).textAlign
      },
      set(textAlign) {
        this.$emit('change', { ...(this.value || {}), textAlign })
      },
    },
    // titleText: {
    //   get() {
    //     return (this.value || {}).titleText
    //   },
    //   set(titleText) {
    //     this.$emit('change', { ...(this.value || {}), titleText })
    //   },
    // },
    iconName: {
      get() {
        return (this.value || {}).iconName
      },
      set(iconName) {
        this.$emit('change', { ...(this.value || {}), iconName })
      },
    },
    iconPosition: {
      get() {
        return (this.value || {}).iconPosition
      },
      set(iconPosition) {
        this.$emit('change', { ...(this.value || {}), iconPosition })
      },
    },
    criticalColor: {
      get() {
        return (this.value || {}).criticalColor
      },
      set(criticalColor) {
        this.$emit('change', { ...(this.value || {}), criticalColor })
      },
    },
    majorColor: {
      get() {
        return (this.value || {}).majorColor
      },
      set(majorColor) {
        this.$emit('change', { ...(this.value || {}), majorColor })
      },
    },
    warningColor: {
      get() {
        return (this.value || {}).warningColor
      },
      set(warningColor) {
        this.$emit('change', { ...(this.value || {}), warningColor })
      },
    },
    legendEnabled: {
      get() {
        return (this.value || {}).legendEnabled
      },
      set(legendEnabled) {
        this.$emit('change', { ...(this.value || {}), legendEnabled })
      },
    },
    pieDataLabelsEnabled: {
      get() {
        return (this.value || {}).pieDataLabelsEnabled
      },
      set(pieDataLabelsEnabled) {
        this.$emit('change', { ...(this.value || {}), pieDataLabelsEnabled })
      },
    },
    displayTotalInCenter: {
      get() {
        return (this.value || {}).displayTotalInCenter
      },
      set(displayTotalInCenter) {
        this.$emit('change', { ...(this.value || {}), displayTotalInCenter })
      },
    },
    isRadialView() {
      return this.value.layout === WidgetTypeConstants.RADIAL_VIEW
    },
  },
  // methods: {
  //   handelViewChange(event) {
  //     this.$emit('change', {
  //       ...(this.value || {}),
  //       layout: event ? WidgetTypeConstants.RADIAL_VIEW : undefined,
  //     })
  //   },
  // },
}
</script>
