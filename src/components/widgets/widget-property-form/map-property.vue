<template>
  <div class="flex flex-col px-2 flex-1 min-h-0 w-full">
    <MRow class="min-h-0">
      <MCol :size="12" class="mb-2">
        <MTab value="style">
          <MTabPane key="style" tab="Style" />
        </MTab>
      </MCol>
    </MRow>
    <div class="flex flex-col flex-1 min-h-0 overflow-y-auto overflow-x-hidden">
      <MRow>
        <MCol
          v-if="widget.category === WidgetTypeConstants.MAP_VIEW"
          :size="12"
        >
          <MRow>
            <MCol :size="4">
              <FlotoFormItem>
                <MRadioGroup
                  v-model="innerWidgetType"
                  as-button
                  :options="chartTypeOptions"
                  class="chart-selector-radio mt-4"
                >
                  <template v-slot:option="{ option }">
                    <div class="flex items-center justify-center h-full">
                      <WidgetTypeIcon
                        :widget-type="option.value"
                        :tooltip="option.text"
                        :size="28"
                        :selected="option.value === widgetType"
                      />
                    </div>
                  </template>
                </MRadioGroup>
              </FlotoFormItem>
            </MCol>
          </MRow>
        </MCol>
        <MCol v-else>
          <FlotoFormItem>
            <MRadioGroup
              v-model="innerWidgetType"
              as-button
              :options="chartTypeOptions"
              class="chart-selector-radio mt-4"
            >
              <template v-slot:option="{ option }">
                <div class="flex items-center justify-center h-full">
                  <WidgetTypeIcon
                    :widget-type="option.value"
                    :tooltip="option.text"
                    :size="28"
                    :selected="option.value === widgetType"
                  />
                </div>
              </template>
            </MRadioGroup>
          </FlotoFormItem>
          <MRow :gutter="0">
            <MCol :size="3">
              <FlotoFormItem label="Show Counts">
                <MSwitch
                  v-model="showCounts"
                  checked-children="ON"
                  un-checked-children="OFF"
                />
              </FlotoFormItem>
            </MCol>
            <MCol v-if="isMetricGroup" :size="4">
              <FlotoFormItem label="Select Color Palette">
                <FlotoDropdownPicker
                  v-model="selectedColorPalette"
                  class="w-full"
                  :options="selectedColorPaletteOptions"
                  display-name
                  as-input
                  use-large-padding
                >
                  <template v-slot:before-menu-text="{ item }">
                    <div
                      class="flex items-center h-full"
                      style="padding: 2px 0"
                    >
                      <div class="flex h-full">
                        <div
                          v-for="(color, index) in item.colors"
                          :key="index"
                          :style="{
                            backgroundColor: color,
                            minHeight: '18px',
                            width: '6.08px',
                            height: '100%',
                            borderRadius:
                              index === 0
                                ? '2px 0 0 2px'
                                : index === item.colors.length - 1
                                ? '0 2px 2px 0'
                                : '0',
                          }"
                        ></div>
                      </div>
                    </div>
                  </template>
                </FlotoDropdownPicker>
              </FlotoFormItem>
            </MCol>
          </MRow>
        </MCol>
      </MRow>
    </div>
  </div>
</template>

<script>
import CloneDeep from 'lodash/cloneDeep'
import { WidgetTypeConstants, COLOR_PALETTES } from '../constants'
import WidgetTypeIcon from '../widget-type-icon/widget-type-icon.vue'
export default {
  name: 'MapProperty',
  components: {
    WidgetTypeIcon,
  },
  inheritAttrs: false,
  model: { event: 'change' },
  props: {
    widgetType: {
      type: String,
      default: undefined,
    },
    widget: {
      type: Object,
      default: undefined,
    },
    value: {
      type: Object,
      default: undefined,
    },
  },
  data() {
    return {
      WidgetTypeConstants: CloneDeep(WidgetTypeConstants),
    }
  },
  computed: {
    isMetricGroup() {
      return this.widget?.groups?.[0]?.type === 'metric'
    },
    chartTypeOptions() {
      if (this.widget.category === WidgetTypeConstants.HEATMAP) {
        return [
          WidgetTypeConstants.HEATMAP_PLAIN,
          WidgetTypeConstants.HEATMAP_WITH_HOST,
        ].map((t) => ({ value: t, text: t }))
      }
      return [
        WidgetTypeConstants.MAP_VIEW,
        WidgetTypeConstants.TREE_VIEW,
        WidgetTypeConstants.ONLINE_MAP,
      ].map((t) => ({ value: t, text: t }))
    },
    innerWidgetType: {
      get() {
        return this.widgetType
      },
      set(type) {
        this.$emit('change-widget-type', type)
      },
    },
    showCounts: {
      get() {
        return (this.value || {}).showCounts
      },
      set(showCounts) {
        this.$emit('change', { ...(this.value || {}), showCounts })
      },
    },
    selectedColorPalette: {
      get() {
        return (this.value || {}).selectedColorPalette
      },
      set(selectedColorPalette) {
        this.$emit('change', { ...(this.value || {}), selectedColorPalette })
      },
    },
    selectedColorPaletteOptions() {
      return Object.keys(COLOR_PALETTES).map((key) => ({
        key,
        text: COLOR_PALETTES[key].name,
        colors: COLOR_PALETTES[key].colors,
        ranges: COLOR_PALETTES[key].ranges,
      }))
    },
  },
}
</script>
