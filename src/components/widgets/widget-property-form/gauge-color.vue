<template>
  <div class="threshold-container">
    <div
      class="severity flex items-center justify-center bg"
      :class="severity.toLowerCase()"
    >
      <span class="text" :class="severity.toLowerCase()">{{ severity }}</span>
    </div>
    <div class="flex items-center flex-1 material-input threshold-type">
      <FlotoFormItem class="mx-2 form-item">
        <FlotoDropdownPicker
          v-model="condition"
          :searchable="false"
          allow-clear
          :options="conditionOptions"
        />
      </FlotoFormItem>
      <FlotoFormItem
        v-model="conditionValue"
        class="mx-2 form-item border-left"
        validation-label="Value"
        placeholder="Value"
        type="number"
        :min="0"
      />
    </div>

    <FlotoFormItem rules="required" class="mx-2 form-item border-left pl-5">
      <ColorPicker v-model="color" class="mt-1 ant-input" />
    </FlotoFormItem>
  </div>
</template>

<script>
import ColorPicker from '@components/color-picker.vue'
import { getOperatorOptions } from '../helper'

export default {
  name: 'GaugeColor',
  components: { ColorPicker },
  inheritAttrs: false,
  model: { event: 'change' },
  props: {
    value: {
      type: Object,
      default: undefined,
    },
    severity: {
      type: String,
      required: true,
    },
  },
  computed: {
    conditionOptions() {
      return getOperatorOptions(['<', '<=', '=', '>', '>='])
    },
    color: {
      get() {
        return (this.value || {}).color
      },
      set(color) {
        this.$emit('change', { ...(this.value || {}), color })
      },
    },
    condition: {
      get() {
        return (this.value || {}).condition
      },
      set(condition) {
        this.$emit('change', { ...(this.value || {}), condition })
      },
    },
    conditionValue: {
      get() {
        return (this.value || {}).conditionValue
      },
      set(conditionValue) {
        this.$emit('change', { ...(this.value || {}), conditionValue })
      },
    },
  },
}
</script>

<style lang="less">


.threshold-container {
  @apply inline-flex rounded mb-3 w-full;

  border: 1px solid var(--border-color);

  & .severity {
    width: 20%;
    margin-right: 10px;
    font-weight: 400;
    color: var(--white-regular);

    @apply px-6 py-2;
  }

  .threshold-type {
    .dropdown-trigger-input{
      input.@{ant-prefix}-input{
      background: transparent !important;
      border : none !important;
      }
    }

  .@{ant-prefix}-input-number{
     border-bottom: none !important;
    }
  }

  .@{ant-prefix}-form-item  .@{ant-prefix}-form-item-control-wrapper .@{ant-prefix}-form-item-control  .@{ant-prefix}-input{
    border-bottom: none !important;
  }

  .color-preview {
    border-radius: 5px;
  }

  .form-item {
    height: 40px;
  }
}
</style>
