<template>
  <MRow class="justify-center mb-1 w-full">
    <MCol :size="5" class="flex items-center">
      <MIcon name="bars" class="swap-icon mr-1 cursor-move" title="Move" />
      <span class="text-ellipsis">
        {{ value.name }}
      </span>
    </MCol>
    <MCol :size="4">
      <FlotoFormItem v-model="displayName" placeholder="Display Name" />
    </MCol>
    <MCol :size="1" class="flex justify-center items-center">
      <MCheckbox
        :checked="hidden"
        :disabled="hideDisabled"
        @change="hidden = !hidden"
      />
    </MCol>
    <MCol :size="2" class="mt-2 flex justify-center">
      <a
        class="cursor-pointer flex items-center"
        @click="showColumnConfigurator = true"
      >
        Configure
      </a>
    </MCol>
    <ColumnConfigrator
      v-if="showColumnConfigurator"
      :value="innerValue"
      @close="showColumnConfigurator = false"
      @change="handleUpdateConfigurations"
    />
  </MRow>
</template>

<script>
import ColumnConfigrator from './column-configurator.vue'

export default {
  name: 'ColumnSettingRow',
  components: {
    ColumnConfigrator,
  },
  model: { event: 'change' },
  props: {
    value: {
      type: Object,
      required: true,
    },
    hideDisabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      showColumnConfigurator: false,
    }
  },
  computed: {
    innerValue: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('change', value)
      },
    },
    displayName: {
      get() {
        return this.value.displayName
      },
      set(displayName) {
        this.$emit('change', { ...this.value, displayName })
      },
    },
    hidden: {
      get() {
        return this.value.hidden
      },
      set(hidden) {
        this.$emit('change', { ...this.value, hidden })
      },
    },
  },
  methods: {
    handleUpdateConfigurations(event) {
      this.innerValue = event
      this.showColumnConfigurator = false
    },
  },
}
</script>
