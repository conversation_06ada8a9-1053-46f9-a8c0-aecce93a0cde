<template>
  <MRow>
    <slot name="visulization-type-options"></slot>
    <MCol :size="6" class="mt-3">
      <!-- <FlotoFormItem label="Header Font Size">
        <FlotoDropdownPicker
          v-model="fontSize"
          :options="options"
          placeholder="Select Font Size "
        />
      </FlotoFormItem> -->
      <FlotoFormItem label="Header Font Size">
        <MRadioGroup v-model="fontSize" as-button :options="options" />
      </FlotoFormItem>
    </MCol>
  </MRow>
</template>

<script>
import { FontSizeOptions } from '../constants'

export default {
  name: 'GridHeaderStyle',
  model: { event: 'change' },
  props: {
    value: {
      type: Object,
      default: undefined,
    },
  },
  computed: {
    options() {
      return FontSizeOptions
    },
    fontSize: {
      get() {
        return (this.value || {}).fontSize
      },
      set(fontSize) {
        this.$emit('change', { ...(this.value || {}), fontSize })
      },
    },
  },
}
</script>
