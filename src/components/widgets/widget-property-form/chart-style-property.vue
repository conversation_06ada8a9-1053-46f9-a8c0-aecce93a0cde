<template>
  <div class="flex flex-col flex-1">
    <MRow>
      <MCol :size="12">
        <slot name="visulization-type-options">
          <!-- <FlotoFormItem label="Visulization Type">
              <FlotoDropdownPicker
                v-model="innerWidgetType"
                as-button
                :options="chartTypeOptions"
                class="chart-type-picker"
                :searchable="false"
                :allow-clear="false"
              >
                <template v-slot:before-menu-text="{ item }">
                  <WidgetTypeIcon
                    :widget-type="item.key"
                    :tooltip="item.text"
                    :size="15"
                  />
                </template>
              </FlotoDropdownPicker>
            </FlotoFormItem> -->
          <FlotoFormItem v-if="!isAiMlWidget">
            <MRadioGroup
              v-model="innerWidgetType"
              as-button
              :options="chartTypeOptions"
              class="chart-selector-radio mt-4"
            >
              <template v-slot:option="{ option }">
                <div class="flex items-center justify-center h-full">
                  <WidgetTypeIcon
                    :widget-type="option.value"
                    :tooltip="option.text"
                    :size="28"
                    :selected="option.value === widgetType"
                  />
                </div>
              </template>
            </MRadioGroup>
          </FlotoFormItem>
        </slot>
      </MCol>
      <template v-if="shouldShowChartConfigurations">
        <MCol :size="12" />
        <MCol :size="6">
          <FlotoFormItem
            v-model="rotation"
            style="max-width: 100px"
            label="Rotation"
            rules="regex:^-?\d+$"
          >
            <template v-slot:prefix>
              <MIcon name="rotation" size="lg" />
            </template>
            <template v-slot:suffix>
              <span style="position: relative; top: -5px; font-size: 1.3rem">
                &#176;
              </span>
            </template>
          </FlotoFormItem>
        </MCol>
        <MCol :size="6" class="flex items-center">
          <!-- <FlotoFormItem label="Ellipsis" class="mr-4">
            <MSwitch
              v-model="ellipsisEnabled"
              checked-children="ON"
              un-checked-children="OFF"
            />
          </FlotoFormItem> -->

          <FlotoFormItem label="Legend" class="mr-4">
            <MSwitch
              v-model="legendEnabled"
              checked-children="ON"
              un-checked-children="OFF"
            />
          </FlotoFormItem>

          <FlotoFormItem v-if="isPie" label="Data Labels">
            <MSwitch
              v-model="pieDataLabelsEnabled"
              checked-children="ON"
              un-checked-children="OFF"
            />
          </FlotoFormItem>
        </MCol>
      </template>
    </MRow>
    <MRow v-if="shouldShowChartConfigurations">
      <MCol :size="6" class="flex items-center">
        <FlotoFormItem v-if="!isPie" label="X-Axis Title">
          <MSwitch
            v-model="xAxisTitleEnabled"
            checked-children="ON"
            un-checked-children="OFF"
          />
        </FlotoFormItem>
        <FlotoFormItem
          v-if="xAxisTitleEnabled && !isPie"
          v-model="xAxisTitle"
          placeholder="Enter Text"
          rules="required"
          class="ml-2 flex-1"
        />
      </MCol>
      <MCol :size="6" class="flex items-center">
        <FlotoFormItem v-if="!isPie" label="Y-Axis Title">
          <MSwitch
            v-model="yAxisTitleEnabled"
            checked-children="ON"
            un-checked-children="OFF"
          />
        </FlotoFormItem>
        <FlotoFormItem
          v-if="yAxisTitleEnabled && !isPie"
          v-model="yAxisTitle"
          placeholder="Enter Text"
          rules="required"
          class="ml-2 flex-1"
        />
      </MCol>
      <MCol v-if="!isPie && !isAiMlWidget" :size="6" class="flex items-center">
        <FlotoFormItem label="Z-Axis Title">
          <MSwitch
            v-model="zAxisTitleEnabled"
            checked-children="ON"
            un-checked-children="OFF"
          />
        </FlotoFormItem>
        <FlotoFormItem
          v-if="zAxisTitleEnabled && !isPie"
          v-model="zAxisTitle"
          placeholder="Enter Text"
          rules="required"
          class="ml-2 flex-1"
        />
      </MCol>
      <!-- <MCol :size="6" class="flex items-center">
        <FlotoFormItem v-if="!isPie && isReportPreview" label="Vertical Legend">
          <MSwitch
            v-model="verticalLegend"
            checked-children="ON"
            un-checked-children="OFF"
            :disabled="!legendEnabled"
          />
        </FlotoFormItem>
      </MCol> -->
      <MCol :size="6">
        <FlotoFormItem
          v-if="!isPie && shouldShowLineWidth"
          label="Line Width"
          class="mr-1"
        >
          <MRow :gutter="0" class="flex justify-between">
            <MCol :size="10">
              <Slider
                v-model="lineWidth"
                :marks="sliderMarks"
                :default-value="2"
                :step="null"
                :tip-formatter="null"
                :min="lineWidthLimits.min"
                :max="lineWidthLimits.max"
                class="range-slider"
              />
            </MCol>
            <MCol :size="2" :class="{ 'pt-2': isReportPreview }">
              <span class="px-2 py-1 rounded linwith-box">{{ lineWidth }}</span>
            </MCol>
          </MRow>
        </FlotoFormItem>
      </MCol>
    </MRow>
    <MRow>
      <MCol v-if="!isTopnChartProperty" :size="6" class="flex items-center">
        <div class="inline-flex items-center relative" style="top: 3px">
          <GranularityInput v-model="granularity" />
        </div>
      </MCol>
    </MRow>
  </div>
</template>

<script>
import Bus from '@utils/emitter'
import WidgetTypeIcon from '../widget-type-icon/widget-type-icon.vue'
import { WidgetTypeConstants, LineWidthLimits } from '../constants'
import Slider from 'ant-design-vue/es/slider'
import 'ant-design-vue/es/slider/style'
import GranularityInput from '@src/components/widgets/granularity-input.vue'

export default {
  name: 'ChartStyleProperty',
  components: {
    WidgetTypeIcon,
    Slider,
    GranularityInput,
  },
  inject: {
    widgetFormContext: {
      default: {
        setGranularity() {
          throw new Error('Please set granularity in widgetFormContext')
        },
        formData: {},
      },
    },
  },
  model: { event: 'change' },
  props: {
    widgetType: {
      type: String,
      default: undefined,
    },
    value: {
      type: Object,
      default: undefined,
    },
    isReportPreview: {
      type: Boolean,
      default: false,
    },
    isAiMlWidget: {
      type: Boolean,
      default: false,
    },
    isTopnChartProperty: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    chartTypeOptions() {
      return [
        WidgetTypeConstants.AREA,
        WidgetTypeConstants.LINE,
        WidgetTypeConstants.HORIZONTAL_BAR,
        WidgetTypeConstants.VERTICAL_BAR,
        WidgetTypeConstants.STACKED_AREA,
        WidgetTypeConstants.STACKED_LINE,
        WidgetTypeConstants.STACKED_HORIZONTAL_BAR,
        WidgetTypeConstants.STACKED_VERTICAL_BAR,
      ].map((t) => ({ value: t, text: t }))
    },
    isPie() {
      return this.widgetType === WidgetTypeConstants.PIE
    },
    isTopNSolidGauge() {
      return this.widgetType === WidgetTypeConstants.TOPN_SOLID_GAUGE_VIEW
    },
    isTopNTreeMap() {
      return this.widgetType === WidgetTypeConstants.TREE_VIEW
    },
    shouldShowChartConfigurations() {
      return !this.isTopNSolidGauge && !this.isTopNTreeMap
    },

    innerWidgetType: {
      get() {
        return this.widgetType
      },
      set(type) {
        this.$emit('change-widget-type', type)
      },
    },
    rotation: {
      get() {
        return (this.value || {}).rotation
      },
      set(rotation) {
        this.$emit('change', { ...(this.value || {}), rotation })
      },
    },
    ellipsisEnabled: {
      get() {
        return (this.value || {}).ellipsisEnabled
      },
      set(ellipsisEnabled) {
        this.$emit('change', { ...(this.value || {}), ellipsisEnabled })
      },
    },
    legendEnabled: {
      get() {
        return (this.value || {}).legendEnabled
      },
      set(legendEnabled) {
        this.$emit('change', {
          ...(this.value || {}),
          legendEnabled,
          // verticalLegend: legendEnabled
          //   ? (this.value || {}).verticalLegend
          //   : legendEnabled,
        })
      },
    },
    pieDataLabelsEnabled: {
      get() {
        return (this.value || {}).pieDataLabelsEnabled
      },
      set(pieDataLabelsEnabled) {
        this.$emit('change', { ...(this.value || {}), pieDataLabelsEnabled })
      },
    },
    xAxisTitleEnabled: {
      get() {
        return (this.value || {}).xAxisTitle !== undefined
      },
      set(value) {
        if (value) {
          this.$emit('change', { ...(this.value || {}), xAxisTitle: '' })
        } else {
          this.$emit('change', { ...(this.value || {}), xAxisTitle: undefined })
        }
      },
    },
    xAxisTitle: {
      get() {
        return (this.value || {}).xAxisTitle
      },
      set(xAxisTitle) {
        this.$emit('change', { ...(this.value || {}), xAxisTitle })
      },
    },
    yAxisTitleEnabled: {
      get() {
        return (this.value || {}).yAxisTitle !== undefined
      },
      set(value) {
        if (value) {
          this.$emit('change', { ...(this.value || {}), yAxisTitle: '' })
        } else {
          this.$emit('change', { ...(this.value || {}), yAxisTitle: undefined })
        }
      },
    },
    yAxisTitle: {
      get() {
        return (this.value || {}).yAxisTitle
      },
      set(yAxisTitle) {
        this.$emit('change', { ...(this.value || {}), yAxisTitle })
      },
    },
    zAxisTitleEnabled: {
      get() {
        return (this.value || {}).zAxisTitle !== undefined
      },
      set(value) {
        if (value) {
          this.$emit('change', { ...(this.value || {}), zAxisTitle: '' })
        } else {
          this.$emit('change', { ...(this.value || {}), zAxisTitle: undefined })
        }
      },
    },
    zAxisTitle: {
      get() {
        return (this.value || {}).zAxisTitle
      },
      set(zAxisTitle) {
        this.$emit('change', { ...(this.value || {}), zAxisTitle })
      },
    },
    granularity: {
      get() {
        return this.widgetFormContext.formData.granularity
      },
      set(newGranularity) {
        this.widgetFormContext.setGranularity(newGranularity)
      },
    },
    // verticalLegend: {
    //   get() {
    //     return (this.value || {}).verticalLegend
    //   },
    //   set(verticalLegend) {
    //     this.$emit('change', { ...(this.value || {}), verticalLegend })
    //   },
    // },
    sliderMarks() {
      let marks = {}
      for (
        let i = this.lineWidthLimits.min;
        i <= this.lineWidthLimits.max;
        i++
      ) {
        marks[i] = i
      }
      return marks
    },
    lineWidth: {
      get() {
        return (this.value || {}).lineWidth
      },
      set(lineWidth) {
        this.$emit('change', { ...(this.value || {}), lineWidth })
      },
    },
    lineWidthLimits() {
      return LineWidthLimits
    },
    shouldShowLineWidth() {
      return (
        [
          WidgetTypeConstants.AREA,
          WidgetTypeConstants.LINE,
          WidgetTypeConstants.STACKED_AREA,
          WidgetTypeConstants.STACKED_LINE,
        ].includes(this.innerWidgetType) || this.isAiMlWidget
      )
    },
  },
  methods: {
    generateNewPreview() {
      Bus.$emit('widget.generate.preview')
    },
  },
}
</script>

<style lang="less">
.range-slider {
  .@{ant-prefix}-slider-mark-text {
    display: none;
    margin-top: 5px;
    font-size: 0.6rem;
    color: var(--page-text-color);
  }
  .@{ant-prefix}-slider-rail {
    right: 2px;
    height: 2px;
    background-color: var(--border-color);
    transition: none;
  }
  .@{ant-prefix}-slider-dot {
    top: -5px;
    width: 2px;
    height: 14px;
    border: 1px solid var(--border-color);
  }
  .@{ant-prefix}-slider-handle {
    z-index: 2;
    width: 10px;
    height: 10px;
    background: var(--primary-alt);
    transform: translateY(2px);

    &-1 {
      margin-left: 0;
    }

    &-2 {
      margin-left: -18px;
    }
  }
  .@{ant-prefix}-slider-track {
    display: none;
  }

   &:hover .@{ant-prefix}-slider-rail{
 background-color: var(--border-color);
   }
}

.linwith-box {
  border: 1px solid var(--border-color);
}

.chart-selector-radio{
  .ant-radio-button-wrapper.ant-radio-button-wrapper-checked {
    padding: 0 6px !important;
  }

  .ant-radio-button-wrapper {
    padding: 0 6px !important;
  }
}
</style>
