<template>
  <div class="flex flex-1 h-full w-full min-h-0">
    <ChartOptions
      v-if="((data || {}).series || []).length"
      :key="widget.widgetType"
      :data="mainSeries"
      :widget-type="widgetType"
      :widget="widget"
      :date-time="true"
      :server-side-zoom="false"
      :legend-options="legendOptions"
      :line-width="lineWidth"
      :forecast-series="forecastSeries"
      :range-series="anomalySeries"
      :range-series-name="rangeSeriesName"
      :is-anomaly-range="isAnomalyWidget"
      v-bind="$attrs"
      @drilldown="handleDrillDown"
      @rendered="$emit('rendered', { widget: widget, data: data })"
    >
      <template v-slot="{ options }">
        <Chart
          v-if="!loading && options.series.length"
          :key="renderKey"
          :options="options"
          class="flex-1 h-full w-full"
        />
        <FlotoNoData
          v-else-if="!loading"
          hide-svg
          header-tag="h5"
          variant="neutral"
          icon="exclamation-triangle"
        />
        <div v-else class="flex flex-col flex-1 items-center justify-center">
          <MLoader />
          <h5>Building Chart...</h5>
        </div>
      </template>
    </ChartOptions>
    <FlotoNoData
      v-else
      hide-svg
      header-tag="h5"
      icon="exclamation-triangle"
      variant="neutral"
    />
  </div>
</template>

<script>
import Uniq from 'lodash/uniq'
import Chart from '@components/chart/chart.vue'
import ChartOptions from '@/src/components/chart/options/chart-options.vue'
import { WidgetTypeConstants } from '../constants'

export default {
  name: 'AiChartView',
  components: {
    Chart,
    ChartOptions,
  },
  inheritAttrs: false,
  props: {
    isPreview: { type: Boolean, default: false },
    disableServerZoom: { type: Boolean, default: false },
    widget: {
      type: Object,
      required: true,
    },
    data: {
      type: [Array, Object],
      default() {
        return []
      },
    },
    forTemplate: {
      type: Boolean,
      default: false,
    },
    disableSeverityColors: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: true,
      renderKey: 1,
    }
  },
  computed: {
    widgetType() {
      return 'line'
    },
    WidgetTypeConstants() {
      return WidgetTypeConstants
    },
    isGaugeWidget() {
      return this.widget.category === WidgetTypeConstants.GAUGE
    },
    stacked() {
      const widgetType = this.widget.widgetType
      if (
        [
          WidgetTypeConstants.STACKED_VERTICAL_BAR,
          WidgetTypeConstants.STACKED_HORIZONTAL_BAR,
          WidgetTypeConstants.STACKED_AREA,
          WidgetTypeConstants.STACKED_LINE,
          WidgetTypeConstants.AVAILABILITY_TIME_SERIES,
        ].indexOf(widgetType) >= 0
      ) {
        return true
      }
      if (((this.widget.widgetProperties || {}).chartOptions || {}).stacked) {
        return true
      }
      return false
    },
    rangeSeriesName() {
      return this.widget.category === WidgetTypeConstants.FORECAST
        ? 'Prediction Range'
        : 'Prediction Range'
    },
    isAnomalyWidget() {
      return this.widget.category === WidgetTypeConstants.ANOMALY
    },
    legendOptions() {
      if (
        ((this.widget.widgetProperties || {}).styleSetting || {}).verticalLegend
      ) {
        return { layout: 'vertical' }
      }
      return {}
    },
    lineWidth() {
      return ((this.widget.widgetProperties || {}).styleSetting || {}).lineWidth
    },
    mainSeries() {
      return ((this.data || {}).series || []).filter((s) => !s.seriesType)
    },
    forecastSeries() {
      return ((this.data || {}).series || []).find(
        (s) => s.seriesType === 'forecast'
      )
    },
    anomalySeries() {
      const series = ((this.data || {}).series || []).find(
        (s) => s.seriesType === 'anomaly'
      )
      if (series) {
        return series.data
      }
      return undefined
    },
  },
  watch: {
    data: {
      handler(newValue, oldValue) {
        if (newValue !== oldValue) {
          this.buildChartData()
        }
      },
      immediate: true,
    },
  },
  methods: {
    async buildChartData() {
      this.loading = false
      if (
        ((this.data || {}).series || []).length === 0 &&
        this.widget.hideEmptyWidget
      ) {
        this.$emit('hide')
      }
    },
    handleDrillDown(map) {
      const widgetGroupTypes = new Set(this.widget.groups.map((g) => g.type))
      if (this.forTemplate) {
        if (widgetGroupTypes.size === 1 && widgetGroupTypes.has('metric')) {
          this.$emit('drilldown', {
            type: 'metric.explorer',
            counters: Object.values(map),
          })
        } else if (
          widgetGroupTypes.size === 1 &&
          widgetGroupTypes.has('flow')
        ) {
          this.$emit('drilldown', {
            type: 'flow.explorer',
            counters: Object.values(map),
          })
        } else if (widgetGroupTypes.size === 1 && widgetGroupTypes.has('log')) {
          this.$emit('drilldown', {
            type: 'log.explorer',
            counters: Object.values(map),
          })
        }
        return
      }
      if (
        widgetGroupTypes.size === 1 &&
        widgetGroupTypes.has('metric') &&
        this.widget.groups[0].resultBy === 'monitor'
      ) {
        this.$router.push(
          this.$modules.getModuleRoute('metric-explorer', '', {
            query: {
              metrics: encodeURIComponent(btoa(JSON.stringify(map))),
              t: encodeURIComponent(
                btoa(JSON.stringify(this.widget.timeRange))
              ),
            },
          })
        )
      } else if (widgetGroupTypes.size === 1 && widgetGroupTypes.has('flow')) {
        const group = this.widget.groups.find(
          (group) => group.category === 'flow'
        )
        if (
          group &&
          (group.resultBy || []).length > 0 &&
          (group.target || {}).entityType === 'event.source' &&
          ((group.target || {}).entities || []).length > 0
        ) {
          let data = {
            ...group,
            countersValue: Uniq(Object.values(map)),
          }
          this.$router.push(
            this.$modules.getModuleRoute('flow', 'explorer', {
              query: {
                flow: encodeURIComponent(btoa(JSON.stringify(data))),
                t: encodeURIComponent(
                  btoa(JSON.stringify(this.widget.timeRange))
                ),
              },
            })
          )
        }
      } else if (widgetGroupTypes.size === 1 && widgetGroupTypes.has('log')) {
        const filters = (
          this.widget.groups.find((group) => group.category === 'log') || {}
        ).filters
        if (
          filters?.pre.groups[0]?.conditions?.[0]?.operand ||
          filters?.post.groups[0]?.conditions?.[0]?.operand
        ) {
          this.$router.push(
            this.$modules.getModuleRoute('log', 'log-search', {
              query: {
                filter: encodeURIComponent(btoa(JSON.stringify(filters))),
                t: encodeURIComponent(
                  btoa(JSON.stringify(this.widget.timeRange))
                ),
              },
            })
          )
        }
      }
      // @TODO handle log and flow drilldown here
    },
  },
}
</script>
