<template>
  <div
    v-if="seriesData.length"
    ref="containerRef"
    class="flex justify-around items-center h-full w-full px-4 flex-col"
    :style="{}"
  >
    <div class="flex justify-start w-full">
      <h4 class="self-start text-3xl mb-0 font-600">
        {{ total }} <span class="text-sm">| Total</span></h4
      >
    </div>
    <div
      class="m-1 severity-text min-w-0 flex w-full"
      :style="{
        gap: '20px',
      }"
    >
      <div
        v-for="categories in seriesData"
        :key="categories.name"
        class="flex items-center flex-col"
        :style="{
          width: `${width}px`,
          minWidth: '120px',
        }"
        ><h4 class="text-xl mb-0 self-start"
          >{{ categories.y }}
          <!-- <span class="text-sm text-neutral">| {{ total }}</span> -->
        </h4>
        <!-- <div class="flex justify-between w-full">

        </div> -->
        <Progressbar
          :data="categories"
          :total="total"
          view="line"
          :style="{
            cursor: widget ? 'pointer' : 'unset',
          }"
          @filter-by-severity="onSeverityFilterApplied"
        />
        <div :class="`text-neutral text-center self-start`">
          {{ categories.name }}
        </div>
      </div>
    </div>
  </div>

  <FlotoNoData
    v-else
    hide-svg
    header-tag="h5"
    icon="exclamation-triangle"
    variant="neutral"
  />
</template>

<script>
import CloneDeep from 'lodash/cloneDeep'
import Progressbar from './components/progressbar.vue'

export default {
  name: 'ProgressWithCountView',
  components: {
    Progressbar,
  },
  props: {
    data: { type: Object, required: true },
    widget: {
      type: Object,
      default: undefined,
    },
  },

  data() {
    return {
      seriesData: CloneDeep(this.data.series[0].data) || [],
      width: undefined,
    }
  },
  computed: {
    total() {
      let total = 0

      // let total = this.seriesData.reduce((accumulator, currentValue) => {
      //   return accumulator + currentValue.y
      // }, 0)

      for (const dataPoint of this.seriesData) {
        total += dataPoint.y
      }

      return total
    },
  },
  mounted() {
    this.$_resizeObserver = new ResizeObserver(() => {
      this.width =
        this.$el.offsetWidth / this.seriesData.length -
        this.seriesData.length * 5
    })

    this.$_resizeObserver.observe(this.$el)

    this.$once('hook:beforeDestroy', () => {
      if (this.$_resizeObserver) {
        this.$_resizeObserver.disconnect()
        this.$_resizeObserver = undefined
      }
    })

    this.width =
      this.$el.offsetWidth / this.seriesData.length - this.seriesData.length * 5
  },
  methods: {
    onSeverityFilterApplied(event) {
      this.$emit('drilldown', undefined, event)
    },
  },
}
</script>
