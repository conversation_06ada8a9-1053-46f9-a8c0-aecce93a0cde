<template>
  <div
    class="flex flex-1 min-h-0 flex-col widget-view __panel"
    :class="{ 'blinking-div': $attrs['primary-bg'] }"
  >
    <Title
      v-if="
        Boolean(isLoading)
          ? true
          : !isGauge || isAlertWidget || isConfigTypeWidget
      "
      :progress="progress"
      :is-dashboard-fullscreen="$attrs['is-dashboard-fullscreen']"
      :widget="widget"
      :hide-title="hideTitle"
      :title="widgetTitle"
      :time-range="timeRange"
      :hide-timeline="$attrs['hide-timeline']"
      :is-preview="isPreview"
      :for-template="$attrs['for-template']"
      :hide-actions="
        isPreview || shouldHideWidgetActions ? true : fullscreen || hideActions
      "
      :disabled="disabled"
      :guid="guid"
      :header-font-size="headerFontSize"
      :is-fullscreen="isFullscreen"
      :result-error="
        widget.widgetType === WidgetTypeConstants.KPI_GAUGE
          ? undefined
          : $attrs['result-error']
      "
      :excluded-actions="$attrs['excluded-actions']"
      :additional-actions="$attrs['additional-actions']"
      :is-custom-template="$attrs['is-custom-template']"
      @fullscreen="handleFullScreen"
      @exit-fullscreen="exitFullScreen"
      @share="$emit('share')"
      @remove="$emit('remove')"
      @clone="$emit('clone')"
      @edit="$emit('edit')"
      @csv-export="$emit('csv-export')"
    />
    <div class="flex flex-1 min-h-0 flex-col overflow-auto w-full">
      <slot
        :headerFontSize="headerFontSize"
        :guid="guid"
        :isFullscreen="isFullscreen"
        :title="widgetTitle"
        :disabled="disabled"
        :forTemplate="$attrs['for-template']"
        :makeFullScreen="handleFullScreen"
        :exitFullScreen="exitFullScreen"
        :hideActions="isPreview ? true : hideActions"
      />
      <slot name="error" />
    </div>
    <div
      v-if="progress && progress > 0 && progress < 100"
      class="ant-progress ant-progress-line ant-progress-status-success ant-progress-small"
      style="height: 5px; line-height: 0"
    >
      <div>
        <div class="ant-progress-outer">
          <div class="ant-progress-inner">
            <div
              class="ant-progress-bg"
              style="height: 5px; border-radius: 100px"
              :style="{ width: `${progress}%` }"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { exitFullScreen, makeFullScreen } from '@utils/fullscreen'
import { FontSizeMap, WidgetTypeConstants } from '../constants'
import Title from './components/widget-title.vue'

export default {
  name: 'WidgetLayout',
  components: {
    Title,
  },
  inheritAttrs: false,
  props: {
    timeRange: {
      type: Object,
      default: undefined,
    },
    guid: {
      type: String,
      required: true,
    },
    fontSize: {
      type: String,
      default: 'medium',
    },
    widget: {
      type: Object,
      required: true,
    },
    title: {
      type: String,
      default: undefined,
    },
    hideActions: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    isPreview: {
      type: Boolean,
      default: false,
    },
    hideTitle: {
      type: Boolean,
      default: false,
    },
    fullscreen: {
      type: Boolean,
      default: false,
    },
    isLoading: {
      type: [Boolean, Object],
      default: false,
    },
    progress: {
      type: Number,
      default: undefined,
    },
  },
  data() {
    return {
      isFullscreen: false,
    }
  },
  computed: {
    WidgetTypeConstants() {
      return WidgetTypeConstants
    },
    shouldHideWidgetActions() {
      return (
        this.isKpiGauge ||
        this.widget.widgetType === WidgetTypeConstants.PORT_VIEW
      )
    },
    isKpiGauge() {
      return [WidgetTypeConstants.KPI_GAUGE].includes(this.widget.widgetType)
    },
    isAvailabilityWidget() {
      const availabilityGroup = (this.widget.groups || []).find(
        (g) => g.type === 'availability'
      )
      return Boolean(availabilityGroup)
    },
    isAlertWidget() {
      const alertGroup = (this.widget.groups || []).find(
        (g) => g.type === 'alert' || g.type === 'policy'
      )
      return Boolean(alertGroup)
    },
    isConfigTypeWidget() {
      const isConfigDeviceGroup = (this.widget.groups || []).find(
        (g) => g.type === this.$constants.CONFIG_GROUP
      )
      return Boolean(isConfigDeviceGroup)
    },
    isRegularGauge() {
      return (
        [
          WidgetTypeConstants.METRO_TILE,
          WidgetTypeConstants.SOLID_GAUGE,
        ].includes(this.widget.widgetType) &&
        !this.isAvailabilityWidget &&
        !this.isAlertWidget
      )
    },
    isGauge() {
      if (this.widget.category === WidgetTypeConstants.FREE_TEXT) {
        return true
      }
      if (this.widget.widgetType === WidgetTypeConstants.KPI_GAUGE) {
        return true
      }
      if (this.isAvailabilityWidget) {
        return false
      }
      return (
        this.widget.category === WidgetTypeConstants.GAUGE ||
        this.widget.widgetType === WidgetTypeConstants.KPI_GAUGE
      )
    },
    widgetTitle() {
      return this.title || this.widget.name
    },
    headerFontSize() {
      return FontSizeMap[this.fontSize || 'small']
    },
  },
  created() {
    const fullscreenHandler = (e) => {
      if (!document.fullscreen) {
        this.isFullscreen = false
      }
    }
    document.addEventListener('fullscreenchange', fullscreenHandler)

    this.$once('hook:beforeDestroy', () => {
      document.removeEventListener('fullscreenchange', fullscreenHandler)
    })
  },
  methods: {
    exitFullScreen() {
      exitFullScreen()
    },
    handleFullScreen() {
      makeFullScreen(this.$el)
      this.isFullscreen = true
    },
  },
}
</script>
