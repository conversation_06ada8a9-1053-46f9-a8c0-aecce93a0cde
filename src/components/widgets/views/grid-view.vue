<script>
import { WidgetTypeConstants } from '../constants'
import DefaultGridView from './grid/default-grid.vue'
import InterfaceTrafficGrid from './grid/interface-traffic-grid.vue'
import SiteToSiteVpn from './grid/site-to-site-vpn/site-to-site-vpn.vue'
import Storage from './grid/storage.vue'
import Billing from './grid/billing-view.vue'
import ColumnGridView from './grid/column-grid-view.vue'
import VerticalValueGrid from './grid/vertical-value-grid.vue'
import ChartView from './chart-view.vue'
import HardwareSensor from './grid/hardware-sensor.vue'
import SwitchPortView from './grid/switch-port-view/switch-port-view.vue'
import OverviewLayout from './grid/overview-layout.vue'
import StatusTimeline from './grid/status-timeline.vue'
// import ApplicationsView from './grid/applications-view.vue'
import WirelessSignalStrength from './grid/wireless-signal-strength.vue'
import LastThirtyDaysAvailability from './grid/last-30-days-availability-view.vue'
import KpiGauge from './gauge/kpi-gauge.vue'
import MetroTile from './gauge/metro-tile.vue'
import SwitchStackView from './grid/switch-stack-view/switch-stack-view.vue'
import AlertHistoryList from './grid/alert-history-list.vue'
import AlertStackedVerticalBarChartView from './grid/alert-stacked-vertical-bar-chart.vue'

function getComponent(widgetType, view, layout) {
  if (view === 'AlertStackedVerticalBar') {
    return AlertStackedVerticalBarChartView
  }
  if (['2-columns', 'overview'].includes(layout)) {
    return OverviewLayout
  } else if (
    [
      WidgetTypeConstants.AVAILABILITY_TIME_SERIES,
      WidgetTypeConstants.APPLICATION_AVAILABILITY_TIME_SERIES,
    ].includes(widgetType)
  ) {
    return LastThirtyDaysAvailability
  } else if ([WidgetTypeConstants.MONITOR_HEALTH].includes(widgetType)) {
    return ChartView
  } else if (widgetType === WidgetTypeConstants.HARDWARE_SENSOR_GRID) {
    return HardwareSensor
  } else if (widgetType === WidgetTypeConstants.PORT_VIEW) {
    return SwitchPortView
  } else if (widgetType === WidgetTypeConstants.STACKED_SWITCH_VIEW) {
    return SwitchStackView
  } else if (widgetType === WidgetTypeConstants.STATUS_FLAP) {
    return StatusTimeline
  } else if (widgetType === WidgetTypeConstants.APPLICATION_STATUS) {
    return DefaultGridView
  } else if (widgetType === WidgetTypeConstants.WIRELESS_SIGNAL_STRENGTH) {
    return WirelessSignalStrength
  } else if (widgetType === WidgetTypeConstants.KPI_GAUGE) {
    return KpiGauge
  } else if (widgetType === WidgetTypeConstants.ALERT_HISTORY_LIST) {
    return AlertHistoryList
  } else if (widgetType === WidgetTypeConstants.METRO_TILE_COUNT_VIEW) {
    return MetroTile
  }
  if (layout === 'column') {
    return ColumnGridView
  } else if (layout === 'key-value') {
    return VerticalValueGrid
  }
  switch (view) {
    case 'Interface Traffic':
      return InterfaceTrafficGrid
    case 'Site VPN':
      return SiteToSiteVpn
    case 'Storage Details':
      return Storage
    case 'AWS Billing':
      return Billing
    default:
      return DefaultGridView
  }
}

export default {
  name: 'GridView',
  functional: true,
  inheritAttrs: false,
  props: {
    widget: {
      type: Object,
      required: true,
    },
    progress: {
      type: Number,
      default: 0,
    },
  },
  render(h, { data, children, props }) {
    const Element = getComponent(
      props.widget.widgetType,
      props.widget.widgetProperties.view,
      props.widget.widgetProperties.layout
    )
    return h(
      Element,
      {
        ...data,
        props,
        key: props.progress,
      },
      children
    )
  },
}
</script>
