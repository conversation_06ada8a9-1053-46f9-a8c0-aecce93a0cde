<template>
  <Component
    :is="component"
    :show-no-data="!(data && data.data)"
    :title="title"
    :value="value"
    :font-size="fontSize"
    :text-align="textAlign"
    :title-text="titleText"
    :icon-name="iconName"
    :color="color"
    :formatted-value="formattedValue"
    :icon-position="iconPosition"
    :is-preview="isPreview"
    :is-circle="widget.widgetProperties.view === 'circle'"
    :widget="widget"
    :counter="counter"
    :data="data"
    pie-inner-size="70%"
    v-bind="attrs"
    :time-range="timeRange"
    @drilldown="handleDrillDown"
    v-on="$listeners"
  />
</template>

<script>
import Uniq from 'lodash/uniq'
import NumberFormat from '@src/filters/number-format'
import MetroTile from './gauge/metro-tile.vue'
import SolidGauge from './gauge/solid-gauge.vue'
import KpiGauge from './gauge/kpi-gauge.vue'
import {
  evaluateExpression,
  FLOW_DRILLDOWN_IGNORED_COUNTERS,
  FLOW_DRILLDOWN_REPLACEMENT_COUNTERS_MAP,
} from '../helper'
import { WidgetTypeConstants } from '../constants'
import ChartView from './chart-view.vue'
import RadialView from './radial-view.vue'
import ProgressWithCountView from './progress-with-count-view.vue'
import HorizontalBarWithCountView from './horizontal-bar-with-count-view.vue'
import { appendNewFilter } from '@/src/modules/log/helpers/log.helper'

export default {
  name: 'GaugeView',
  inheritAttrs: false,
  props: {
    widget: {
      type: Object,
      required: true,
    },
    data: {
      type: Object,
      required: true,
    },
    isPreview: {
      type: Boolean,
      default: false,
    },
    timeRange: {
      type: Object,
      default: undefined,
    },
  },
  computed: {
    isAvailabilityGauge() {
      const availabilityGroup = this.widget.groups.find(
        (g) => g.type === 'availability'
      )
      return Boolean(availabilityGroup)
    },
    isAlertGauge() {
      const alertGroup = this.widget.groups.find(
        (g) => g.type === 'alert' || g.type === 'policy'
      )
      return Boolean(alertGroup)
    },
    isConfigTypeWidget() {
      const alertGroup = this.widget.groups.find(
        (g) => g.type === this.$constants.CONFIG_GROUP
      )
      return Boolean(alertGroup)
    },
    attrs() {
      return {
        ...this.$attrs,
        ...(this.widget.widgetType === WidgetTypeConstants.KPI_GAUGE
          ? {
              data: this.data,
            }
          : {}),
      }
    },
    component() {
      if (
        this.widget.widgetProperties.layout === WidgetTypeConstants.RADIAL_VIEW
      ) {
        return RadialView
      }
      if (
        this.widget.widgetProperties.layout ===
        WidgetTypeConstants.PROGRESS_WITH_COUNT_VIEW
      ) {
        return ProgressWithCountView
      }
      if (
        this.widget.widgetProperties.layout ===
        WidgetTypeConstants.HORIZONTAL_BAR_WITH_COUNT_VIEW
      ) {
        return HorizontalBarWithCountView
      }
      if (this.widget.widgetType === WidgetTypeConstants.KPI_GAUGE) {
        return KpiGauge
      }
      if (
        this.isAvailabilityGauge ||
        this.isAlertGauge ||
        this.isConfigTypeWidget
      ) {
        return ChartView
      }
      if (this.widget.widgetType === WidgetTypeConstants.SOLID_GAUGE) {
        return SolidGauge
      }
      return MetroTile
    },
    fontSize() {
      const properties = this.widget.widgetProperties
      return properties.fontSize
    },
    textAlign() {
      const properties = this.widget.widgetProperties
      return properties.textAlign
    },
    titleText() {
      const properties = this.widget.widgetProperties
      return properties.titleText
    },
    counter() {
      if (!this.data) {
        return
      }
      const groupByColumns = this.data.groupByColumns || []
      const columns = (this.data.columns || []).filter(
        (c) => groupByColumns.includes(c) === false
      )

      return columns[0]
    },
    color() {
      if (this.isAvailabilityGauge) {
        if (this.data && this.data.data && this.data.data.status) {
          return `var(--severity-${this.data.data.status.toLowerCase()})`
        }
        return
      }
      const widgetProperties = this.widget.widgetProperties || {}
      if (widgetProperties.criticalColor) {
        if (
          widgetProperties.criticalColor.condition &&
          widgetProperties.criticalColor.conditionValue
        ) {
          const result = evaluateExpression(
            this.value,
            widgetProperties.criticalColor.condition,
            widgetProperties.criticalColor.conditionValue
          )
          if (result) {
            return widgetProperties.criticalColor.color
          }
        }
      }
      if (widgetProperties.majorColor) {
        if (
          widgetProperties.majorColor.condition &&
          widgetProperties.majorColor.conditionValue
        ) {
          const result = evaluateExpression(
            this.value,
            widgetProperties.majorColor.condition,
            widgetProperties.majorColor.conditionValue
          )
          if (result) {
            return widgetProperties.majorColor.color
          }
        }
      }
      if (widgetProperties.warningColor) {
        if (
          widgetProperties.warningColor.condition &&
          widgetProperties.warningColor.conditionValue
        ) {
          const result = evaluateExpression(
            this.value,
            widgetProperties.warningColor.condition,
            widgetProperties.warningColor.conditionValue
          )
          if (result) {
            return widgetProperties.warningColor.color
          }
        }
      }
      return undefined
    },
    title() {
      if (this.$attrs.title) {
        return this.$attrs.title
      }
      if (this.widget.name) {
        return this.widget.name
      }
      return this.counter
    },
    value() {
      if (!this.data) {
        return
      }
      if (this.data.data && this.counter) {
        return this.data.data[this.counter]
      }
      return undefined
    },
    formattedValue() {
      if (!this.data) {
        return
      }
      if (this.data.data && this.counter) {
        if (this.data.data[`${this.counter}.formatted`]) {
          return this.data.data[`${this.counter}.formatted`]
        }
        return NumberFormat(this.data.data[this.counter])
      }
      return undefined
    },
    iconName() {
      const widgetProperty = this.widget.widgetProperties || {}
      if (widgetProperty.iconName) {
        return widgetProperty.iconName
      }
      if (this.isAvailabilityGauge) {
        if (this.data && this.data.data && this.data.data.status) {
          if (this.data.data.status.toLowerCase() === 'up') {
            return 'long-arrow-up'
          }
          if (this.data.data.status.toLowerCase() === 'down') {
            return 'long-arrow-down'
          }
        }
      }
      return undefined
    },
    iconPosition() {
      const widgetProperty = this.widget.widgetProperties || {}
      if (widgetProperty.iconPosition) {
        return widgetProperty.iconPosition
      }
      if (this.isAvailabilityGauge) {
        if (this.data && this.data.data && this.data.data.status) {
          if (['up', 'down'].includes(this.data.data.status.toLowerCase())) {
            return 'prefix'
          }
        }
      }
      return undefined
    },
  },
  methods: {
    handleDrillDown(map, event) {
      const widgetGroupTypes = new Set(this.widget.groups.map((g) => g.type))
      if (this.forTemplate) {
        if (widgetGroupTypes.size === 1 && widgetGroupTypes.has('metric')) {
          this.$emit('drilldown', {
            type: 'metric.explorer',
            counters: Object.values(map),
          })
        } else if (
          widgetGroupTypes.size === 1 &&
          widgetGroupTypes.has('flow')
        ) {
          this.$emit('drilldown', {
            type: 'flow.explorer',
            counters: Object.values(map),
          })
        } else if (widgetGroupTypes.size === 1 && widgetGroupTypes.has('log')) {
          this.$emit('drilldown', {
            type: 'log.explorer',
            counters: Object.values(map),
          })
        }
        return
      }

      if (
        !this.forTemplate &&
        this.widget.widgetType === WidgetTypeConstants.SOLID_GAUGE &&
        widgetGroupTypes.size === 1 &&
        widgetGroupTypes.has('metric')
      ) {
        this.$emit('drilldown', {
          type: 'metric.explorer',
          counters: map,
        })
      }
      if (widgetGroupTypes.size === 1 && widgetGroupTypes.has('flow')) {
        const group = this.widget.groups.find((group) => group.type === 'flow')

        const changedCounterKey =
          FLOW_DRILLDOWN_REPLACEMENT_COUNTERS_MAP[
            group?.counters?.[0]?.counter?.key
          ]

        const changedCounters =
          changedCounterKey && group?.counters?.length === 1
            ? {
                counters: [
                  {
                    aggrigateFn: 'sum',
                    counter: {
                      key: changedCounterKey,
                    },
                  },
                ],
              }
            : undefined

        if (
          group?.counters?.find((c) =>
            FLOW_DRILLDOWN_IGNORED_COUNTERS.includes(c.counter.key)
          )
        ) {
          return
        }
        let data = {
          ...group,
          category: WidgetTypeConstants.CHART,
          chartType: WidgetTypeConstants.AREA,
          // countersValue: Uniq(group.counters.map((c) => c.counter.key)),
          countersValue: changedCounterKey
            ? [changedCounterKey]
            : Uniq(group.counters.map((c) => c.counter.key)),
          ...(changedCounters || {}),
        }

        let resultBy = {}

        for (const counter of group.counters) {
          if ((counter.target || {}).entityType === 'event.source') {
            resultBy['event.source'] = [
              ...(resultBy['event.source'] || []),
              ...(Array.isArray((counter.target || {}).entities)
                ? (counter.target || {}).entities?.['event.source']?.includes(
                    (counter.target || {}).entityType
                  ) || []
                : Object.keys((counter.target || {}).entities || {}).length
                ? Object.keys((counter.target || {}).entities)
                : []),
            ]
          }
        }

        // group.counters.reduce(
        //   (prev, counter) => ({
        //     ...prev,
        //     ...((counter.target || {}).entityType === 'event.source'
        //       ? {
        //           'event.source': [
        //             ...(prev['event.source'] || []),
        //             ...((counter.target || {}).entities || []),
        //           ],
        //         }
        //       : {}),
        //   }),
        //   {}
        // )

        // if (/^\d+$/.test(event.point.category)) {
        //   resultBy = event.point.series.userOptions.resultByResolver
        // } else {
        //   resultBy =
        //     event.point.series.userOptions.resultByResolver[
        //       event.point.index
        //     ] || {}
        // }

        let filterMap = []

        for (const key of Object.keys(resultBy)) {
          filterMap = filterMap.concat([
            {
              operand: key,
              operator: 'in',
              value: Array.isArray(resultBy[key])
                ? resultBy[key]
                : [resultBy[key]],
            },
          ])
        }

        if (Object.keys(resultBy).filter((k) => k !== 'event.source').length) {
          group.filters.drillDownFilters = appendNewFilter(
            // Object.keys(resultBy).reduce(
            //   (prev, key) => [
            //     ...prev,
            //     {
            //       operand: key,
            //       operator: 'in',
            //       value: Array.isArray(resultBy[key])
            //         ? resultBy[key]
            //         : [resultBy[key]],
            //     },
            //   ],
            //   []
            // ),
            filterMap,
            group.filters.drillDownFilters || {},
            true
          )
        }
        if (resultBy['event.source']) {
          data.target = {
            entityType: 'event.source',
            entities: Array.isArray(resultBy['event.source'])
              ? Uniq(resultBy['event.source'])
              : [resultBy['event.source']],
          }
        }

        if (this.$route.name === 'flow.dashboard') {
          this.$emit('drilldown', {
            type: 'flow.explorer',
            query: {
              flow: encodeURIComponent(btoa(JSON.stringify(data))),
              t: encodeURIComponent(
                btoa(JSON.stringify(this.widget.timeRange))
              ),
            },
          })

          return
        } else if (this.$route.name === 'flow.explorer') {
          return
        }
        this.$router.push(
          this.$modules.getModuleRoute('flow', 'explorer', {
            query: {
              flow: encodeURIComponent(btoa(JSON.stringify(data))),
              t: encodeURIComponent(
                btoa(JSON.stringify(this.widget.timeRange))
              ),
            },
          })
        )
      } else if (widgetGroupTypes.size === 1 && widgetGroupTypes.has('log')) {
        const group =
          this.widget.groups.find((group) => group.type === 'log') || {}
        let data = {
          ...group,
          category: WidgetTypeConstants.CHART,
          chartType: WidgetTypeConstants.AREA,
          countersValue: Uniq(group.counters.map((c) => c.counter.key)),
        }
        let resultBy = {}

        for (const counter of group.counters) {
          if (
            ['event.source', 'event.source.type'].includes(
              (counter.target || {}).entityType
            )
          ) {
            resultBy[(counter.target || {}).entityType] = [
              ...(resultBy[(counter.target || {}).entityType] || []),
              ...(Array.isArray((counter.target || {}).entities)
                ? (counter.target || {}).entities
                : Object.keys((counter.target || {}).entities || {}).length
                ? Object.keys((counter.target || {}).entities)
                : []),
            ]
          }
        }

        // group.counters.reduce(
        //   (prev, counter) => ({
        //     ...prev,
        //     ...(['event.source', 'event.source.type'].includes(
        //       (counter.target || {}).entityType
        //     )
        //       ? {
        //           [(counter.target || {}).entityType]: [
        //             ...(prev[(counter.target || {}).entityType] || []),
        //             ...((counter.target || {}).entities || []),
        //           ],
        //         }
        //       : {}),
        //   }),
        //   {}
        // )
        // if (/^\d+$/.test(event.point.category)) {
        //   resultBy = event.point.series.userOptions.resultByResolver
        // } else {
        //   resultBy =
        //     event.point.series.userOptions.resultByResolver[
        //       event.point.index
        //     ] || {}
        // }

        let filterMap = []

        for (const key of Object.keys(resultBy)) {
          filterMap = filterMap.concat([
            {
              operand: key,
              operator: 'in',
              value: Array.isArray(resultBy[key])
                ? resultBy[key]
                : [resultBy[key]],
            },
          ])
        }

        if (Object.keys(resultBy).length) {
          group.filters.drillDownFilters = appendNewFilter(
            // Object.keys(resultBy).reduce(
            //   (prev, key) => [
            //     ...prev,
            //     {
            //       operand: key,
            //       operator: 'in',
            //       value: Array.isArray(resultBy[key])
            //         ? resultBy[key]
            //         : [resultBy[key]],
            //     },
            //   ],
            //   []
            // ),
            filterMap,
            group.filters.drillDownFilters || {},
            true
          )
        }
        this.$router.push(
          this.$modules.getModuleRoute('log', 'log-search', {
            query: {
              filter: encodeURIComponent(btoa(JSON.stringify(group.filters))),
              log: encodeURIComponent(btoa(JSON.stringify(data))),
              t: encodeURIComponent(
                btoa(JSON.stringify(this.widget.timeRange))
              ),
            },
          })
        )
      } else if (
        widgetGroupTypes.size === 1 &&
        widgetGroupTypes.has('availability') &&
        !this.forTemplate
      ) {
        this.$emit('open-gauge-drilldown-grid', {
          type: 'availability',
          widget: this.widget,
          drilldownSeries: event?.series,
        })
      } else if (
        widgetGroupTypes.size === 1 &&
        widgetGroupTypes.has('policy') &&
        !this.forTemplate
      ) {
        this.$emit('open-gauge-drilldown-grid', {
          type: 'policy',
          widget: this.widget,
          drilldownSeries: event?.series,
          timeRange: this.timeRange,
        })
      }
    },
  },
}
</script>
