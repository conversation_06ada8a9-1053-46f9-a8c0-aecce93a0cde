<template>
  <div
    class="h-full w-full"
    :style="
      (forMonitorDetails && active) ||
      (data.name === 'total' && useComplianceSeverity)
        ? {
            backgroundColor: backgroundColor,
            borderRadius: '50%',
            border: `1px sold ${backgroundColor}`,
          }
        : {}
    "
    @click="filterBySeverity"
  >
    <Progress
      v-if="view === 'line'"
      :width="percentage"
      :stroke-color="color"
      :stroke-width="6"
    />
    <div v-else-if="view === 'bar'" class="flex my-1">
      <div class="w-11/12 relative">
        <div
          :style="{ width: `${percentage}%`, backgroundColor: `${color}` }"
          class="h-full"
        ></div>
        <h6 class="absolute z-50 mb-0 ml-2 mt-1" style="top: 0">
          {{ data.name }}
        </h6>
      </div>
      <div class="w-1/12 flex justify-end">
        <h4 :class="`mb-0 `">
          {{ data.y }}
        </h4>
      </div>
    </div>
    <RadialProgress
      v-else
      :value="percentage || 0"
      :color="color"
      :stroke-width="strokeWidth"
    >
      <h4
        :class="{
          'mb-0': true,
          [`${data.name.toLowerCase()}`]: true,
          text: true,
          'text-radial-view': !forMonitorDetails && data.y <= 1000,
          'font-600': !forMonitorDetails || useComplianceSeverity,
          'text-xs': data.y >= 1000,
        }"
        :style="
          data.name === 'total'
            ? {
                color: useComplianceSeverity
                  ? 'var(--white-regular)'
                  : 'var(--primary)',
              }
            : {
                ...((forMonitorDetails && active) || useComplianceSeverity
                  ? { color }
                  : {}),
              }
        "
        >{{ data.y | numberFormat }}</h4
      >

      <template v-if="!forMonitorDetails" v-slot:bottom-header>
        <div class="mt-1 text-neutral text-center">
          {{ data.name }}
        </div>
      </template>
    </RadialProgress>
  </div>
</template>

<script>
import RadialProgress from '@components/radial-progress.vue'
import Progress from '@components/progress.vue'
import Bus from '@utils/emitter'
import { COMPLIANCE_SEVERITY_TO_SVERITY_MAP } from '@modules/ncm/helpers/compliance'

export default {
  name: 'Progressbar',
  components: {
    RadialProgress,
    Progress,
  },
  inject: { policyGridContext: { default: { data: {} } } },
  props: {
    total: {
      type: Number,
      default: 0,
    },
    data: {
      type: Object,
      default: undefined,
    },
    view: {
      type: String,
      default: undefined,
    },
    forMonitorDetails: {
      type: Boolean,
      default: false,
    },
    disabledFiltering: {
      type: Boolean,
      default: false,
    },
    useComplianceSeverity: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      active: false,
    }
  },
  computed: {
    percentage() {
      return (this.data.y * 100) / this.total
    },
    color() {
      if (this.data.name === 'total') {
        return `var(--primary)`
      } else {
        if (this.useComplianceSeverity) {
          return `var(--severity-${COMPLIANCE_SEVERITY_TO_SVERITY_MAP[
            this.data.name
          ].toLowerCase()})`
        }
        return `var(--severity-${this.data.name.toLowerCase()})`
      }
    },
    backgroundColor() {
      if (this.data.name === 'total') {
        return `var(--primary)`
      } else {
        if (this.useComplianceSeverity) {
          return `var(--severity-${COMPLIANCE_SEVERITY_TO_SVERITY_MAP[
            this.data.name
          ].toLowerCase()}-lightest)`
        }
        return `var(--severity-${this.data.name.toLowerCase()}-lightest)`
      }
    },
    strokeWidth() {
      return this.forMonitorDetails
        ? this.active && this.data.name !== 'total'
          ? 0.1
          : 4
        : 6
    },
  },
  watch: {
    'policyGridContext.filteredSevertiy': {
      handler(newValue) {
        this.active = newValue.includes(this.data.name)
      },
    },
  },
  created() {
    Bus.$on('filter:policy_template', this.handler)
  },
  beforeDestroy() {
    Bus.$off('filter:policy_template', this.handler)
  },
  methods: {
    filterBySeverity() {
      if (this.disabledFiltering) {
        return
      }
      this.active = !this.active
      this.$emit('filter-by-severity', {
        severity: this.data.name,
        active: this.active,
        series: this.data,
      })
      if (this.policyGridContext.updateFilteredSevertiy) {
        this.policyGridContext.updateFilteredSevertiy(
          this.data.name,
          this.active
        )
      }
    },
    handler(event) {
      if (event.severity === 'total') {
        this.active = false
      }
    },
  },
}
</script>
