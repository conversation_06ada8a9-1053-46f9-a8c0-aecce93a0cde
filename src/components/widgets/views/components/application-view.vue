<template>
  <div
    v-if="application"
    class="application-container items-center h-full min-w-0"
  >
    <div class="flex flex-1 min-w-0 internal-container h-full">
      <FlotoLink
        v-if="monitor"
        class="flex flex-col items-center justify-center"
        style="flex-shrink: 0; width: 40%"
        :to="
          $modules.getModuleRoute('inventory', 'application-tab', {
            params: {
              monitorId: monitor.id,
              applicationName: application,
            },
          })
        "
      >
        <div class="flex items-center justify-center w-full">
          <div class="mx-2 items-center justify-center flex">
            <MonitorType disable-tooltip :type="application" width="40px" />
          </div>

          <div class="flex items-center h-full min-w-0 w-full">
            <div class="flex min-w-0">
              <h5 class="m-0 text-primary text-ellipsis">
                {{ application }}
              </h5>
            </div>
          </div>
        </div>
      </FlotoLink>
      <div
        class="flex-1 h-full flex flex-col min-w-0"
        style="flex-shrink: 0; width: 60%"
      >
        <AvailabilityBar :data="data" />
      </div>
    </div>
  </div>
</template>

<script>
import MonitorType from '@components/monitor-type.vue'
import AvailabilityBar from './availability-bar.vue'

export default {
  name: 'ApplicationView',
  components: {
    MonitorType,
    AvailabilityBar,
  },
  inheritAttrs: false,
  props: {
    process: {
      type: String,
      required: true,
    },
    data: {
      type: Array,
      default() {
        return []
      },
    },
    monitor: {
      type: Object,
      required: true,
    },
  },
  computed: {
    application() {
      const app = Object.keys(this.monitor.appProcesses || {}).find(
        (key) => this.monitor.appProcesses[key].name === this.process
      )
      return app
    },
  },
}
</script>

<style lang="less" scoped>
.application-container {
  @apply flex flex-1;

  .internal-container {
    border-bottom: 1px solid var(--code-tag-background-color);
    border-radius: 4px;

    @apply py-4 pr-2;
  }
}
</style>
