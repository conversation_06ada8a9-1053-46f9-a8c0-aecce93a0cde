<template>
  <div class="size flex flex-col flex-1 min-w-0">
    <div class="progress-bar">
      <div
        class="ant-progress ant-progress-line ant-progress-status-primary ant-progress-large"
      >
        <div>
          <div class="ant-progress-outer relative">
            <div class="ant-progress-inner flex">
              <MTooltip
                v-for="(counter, innerIndex) in data"
                :key="`${counter.name}-${innerIndex}`"
                :style="{ width: `${counter.value || 0}%` }"
              >
                <template v-slot:trigger>
                  <div
                    class="ant-progress-bg"
                    :class="counter.name"
                    :style="{
                      height: '8px',
                      ...(innerIndex === 0
                        ? {
                            borderTopLeftRadius: '100px',
                            borderBottomLeftRadius: '100px',
                          }
                        : innerIndex === data.length - 1
                        ? {
                            borderTopRightRadius: '100px',
                            borderBottomRightRadius: '100px',
                          }
                        : {}),
                    }"
                  />
                </template>
                {{ `${capitalize(counter.name)}` }}: {{ counter.value || 0 }}%
              </MTooltip>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="flex justify-between relative min-w-0" style="top: -5px">
      <!-- <template v-for="(series, innerIndex) in data.series">
              <span
                v-if="series.data[index] && innerIndex < data.series.length - 1"
                :key="series.counter"
                class="absolute font-500"
                :style="{ left: `calc(${series.data[index]}% - 15px)` }"
              >
                {{ series.data[index].toFixed(2) }}%
              </span>
            </template> -->
      <span class="text-xs">0%</span>
      <!-- <span class="text-xxs">25%</span>
            <span class="text-xxs">50%</span>
            <span class="text-xxs">75%</span> -->
      <span class="text-xs"> 100% </span>
    </div>
  </div>
</template>

<script>
import Capitalize from 'lodash/capitalize'

export default {
  name: 'AvailabilityBar',
  inheritAttrs: false,
  props: {
    data: {
      type: Array,
      default() {
        return []
      },
    },
  },
  methods: {
    capitalize(s) {
      return Capitalize(s)
    },
  },
}
</script>
