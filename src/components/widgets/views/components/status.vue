<template>
  <span
    :class="{
      'text-secondary-green': isSucess,
      'text-secondary-red': isFail,
      'text-secondary-orange': isFair,
      'text-primary-alt': isDefault,
      'text-secondary-green': isOnline,
      'text-secondary-red': isOffline,
      'text-neutral-light': isDormant,
    }"
    class="flex items-center"
  >
    {{ formattedStatus }}

    <MIcon
      class="mx-1"
      :name="
        isSucess
          ? 'check-circle'
          : isFail
          ? 'times-circle'
          : isFair
          ? 'fair'
          : isDefault
          ? ''
          : ''
      "
      :class="{
        'text-secondary-green': isSucess,
        'text-secondary-red': isFail,
        'text-secondary-orange': isFair,
        'text-primary-alt': isDefault,
      }"
    />
  </span>
</template>

<script>
import Capitalize from 'lodash/capitalize'

export default {
  name: 'Status',
  props: {
    value: {
      type: String,
      default: undefined,
    },
  },

  data() {
    this.STATUS_MAP = {
      COMPLETED: 'Completed',
      SCHEDULED: 'Scheduled',
      QUEUED: 'Queued',
    }
    return {}
  },

  computed: {
    formattedStatus() {
      return Capitalize(this.STATUS_MAP[this.value] || this.value)
    },
    isSucess() {
      return [
        'Completed',
        'Scheduled',
        'unreachable',
        'Queued',
        'reachable',
        'auth-failed',
        'Good',
      ].includes(this.value)
    },
    isFail() {
      return ['Poor'].includes(this.value)
    },
    isFair() {
      return ['Fair'].includes(this.value)
    },
    isDefault() {
      return ['default'].includes(this.value)
    },
    isOnline() {
      return ['online'].includes(this.value)
    },
    isOffline() {
      return ['offline', 'alerting'].includes(this.value)
    },
    isDormant() {
      return ['dormant'].includes(this.value)
    },
  },
}
</script>
