<template>
  <FlotoDrawer
    ref="drawerRef"
    :scrolled-content="false"
    width="65%"
    :wrap-style="{ zIndex: 998 }"
    :open="open"
    @hide="handleDrawerHide"
  >
    <template v-if="item" v-slot:title> {{ title }} </template>

    <FlotoContentLoader v-if="item" :loading="loading">
      <ObjectDrilldownEmmiter
        :status="appliedStatus"
        @monitor-sidebar="$emit('monitor-sidebar', $event)"
        @template-drilldown="$emit('template-drilldown', $event)"
      >
        <GroupProvider>
          <ApiSocketGrid
            :widget="widgetDef"
            :columns="columns"
            :counter="selectedCounter"
            :get-fn="
              isAlertEventGauge ? getLogFlowAlertStreamGridDataApi : undefined
            "
            :grid-filters="gridFilters"
            class="px-4"
            default-sort="-duration"
          />
        </GroupProvider>
      </ObjectDrilldownEmmiter>
    </FlotoContentLoader>
  </FlotoDrawer>
</template>

<script>
import GroupProvider from '@components/data-provider/group-provider.vue'
import ObjectDrilldownEmmiter from '@modules/inventory/components/object-drilldown-emmiter.vue'

import ApiSocketGrid from '@src/components/common/api-socket-grid.vue'
import { getWidgetApi } from '@/src/components/widgets/widgets-api'
import { WidgetTypeConstants } from '@components/widgets/constants'
import { buildWidgetForTheGaugeDrilldown } from '@src/components/widgets/helper'
import { transformWidgetForServer } from '@components/widgets/translator.js'
import {
  getLogFlowAlertStreamGrid,
  COLUMNS,
} from '@modules/alert/helpers/alert-helper'

export default {
  name: 'GaugeDrilldownDrawer',
  components: {
    ApiSocketGrid,
    GroupProvider,
    ObjectDrilldownEmmiter,
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    item: {
      type: Object,
      default: undefined,
    },
  },
  data() {
    return {
      isOpen: false,
      loading: true,
      widgetDef: {},
      filteredCounters: [],
      isAlertEventGauge: false,
    }
  },
  computed: {
    gridFilters() {
      let filters

      if (this.item.drillDownObjectType) {
        filters = [
          ...(filters || []),
          {
            field: 'object_type',
            operator: 'eq',
            value: this.item.drillDownObjectType,
          },
        ]
      }
      // if (this.item.category) {
      //   filters = [
      //     ...(filters || []),
      //     {
      //       field: 'category',
      //       operator: 'eq',
      //       value: this.item.category,
      //     },
      //   ]
      // }

      return filters
    },
    appliedStatus() {
      return (
        this.item?.drilldownSeries?.name &&
        this.item?.drilldownSeries?.name.toUpperCase()
      )
    },
    title() {
      if (this.item?.type === 'availability') {
        return `${(this.filteredCounters?.[0]?.counter?.key || '').replace(
          '~',
          '.'
        )}: ${this.item?.drilldownSeries?.y}`
      } else if (this.item?.type === 'policy') {
        return `Severity: ${this.item?.drilldownSeries?.name}`
      }
      return ''
    },
    isInstaceCounterSelected() {
      return (this.filteredCounters?.[0]?.counter?.key || '').includes('~')
    },

    selectedCounter() {
      return this.filteredCounters?.[0]
    },
    columns() {
      if (this.item?.type === 'availability') {
        return [
          ...(this.isInstaceCounterSelected
            ? [
                {
                  key: 'instance',
                  name: 'instance',
                  searchable: true,
                  sortable: true,
                  cellRender: 'instanceDrilldown',
                },
              ]
            : []),

          {
            key: 'monitor',
            name: 'Monitor',
            searchable: true,
            sortable: true,
            cellRender: 'monitorWithSeverity',
          },
          {
            key: 'object_ip',
            name: 'Ip',
            searchable: true,
            sortable: true,
          },
          {
            key: 'object_type',
            name: 'Type',
            searchable: true,
            sortable: true,
          },
          {
            key: 'object_groups',
            name: 'Groups',
            searchable: true,
            sortable: true,
            contextKey: 'groupContext',
            searchKey: 'groupsDisplay',
            sortKey: 'groupsDisplay',
            exportComputationKey: 'split',
          },
          {
            key: 'object_tags',
            name: 'Tag',
            searchable: true,
            sortable: true,
            cellRender: 'tag_str',
          },

          {
            key: 'duration',
            name: 'Duration',
            searchable: true,
            sortable: true,
            exportType: 'duration',
          },
        ]
      } else if (this.item?.type === 'policy') {
        if (!this.isAlertEventGauge) {
          return [
            {
              key: 'policy_name',
              name: 'Policy Name',
              searchable: true,
              sortable: true,
              cellRender: 'alertDrilldown',
            },
            {
              key: 'monitor',
              name: 'Monitor',
              searchable: true,
              sortable: true,
              cellRender: 'monitorName',
            },
            {
              key: 'instance',
              name: 'instance',
              searchable: true,
              sortable: true,
            },
            {
              key: 'metric',
              name: 'metric',
              searchable: true,
              sortable: true,
            },

            {
              key: 'value',
              name: 'value',
              searchable: true,
              sortable: true,
            },

            {
              key: 'duration',
              name: 'duration',
              searchable: true,
              sortable: true,
              exportType: 'duration',
            },

            {
              key: 'policy_tags',
              name: 'Tags',
              searchable: true,
              sortable: true,
              cellRender: 'tag_str',
            },

            {
              key: 'event_timestamp',
              name: 'Last Seen',
              searchable: true,
              sortable: true,
              cellRender: 'timestemp',
              exportType: 'datetime',
            },

            // {
            //   key: 'duration',
            //   name: 'Last Seen',
            //   searchable: true,
            //   sortable: true,
            // },
          ]
        } else {
          return COLUMNS[this.widgetDef?.groups?.[0]?.category]
            .filter((c) => !['actions'].includes(c.key))
            .map((c) => ({
              ...c,
              hidden: false,
            }))
        }
      }
      return []
    },
  },

  watch: {
    item: {
      immediate: true,
      handler(newValue) {
        if (newValue) {
          this.isOpen = true
        }
      },
    },
    open(newValue, oldValue) {
      if (newValue) {
        this.getWidgetData()
      } else {
      }
    },
  },
  async created() {
    this.getWidgetData()
  },
  beforeDestroy() {
    this.handleDrawerHide()
  },

  methods: {
    handleDrawerHide() {
      this.isOpen = false
      this.$emit('hide')
      this.loading = true
    },
    getLogFlowAlertStreamGridDataApi() {
      const widget = this.widgetDef
      const groupCategory = widget?.groups?.[0]?.category
      const target = widget?.groups?.[0]?.target
      const severity = widget?.groups?.[0]?.severity
      const alertIds = widget?.groups?.[0]?.alertIds
      const tags = widget?.groups?.[0]?.tags

      return getLogFlowAlertStreamGrid(
        this.item?.timeRange || widget.timeline,
        groupCategory,
        {
          target,
          severity,
          alertIds,
          tags,
        }
      )
    },
    async getWidgetData() {
      if (this.item?.widget.id) {
        this.loading = true

        await getWidgetApi(this.item?.widget?.id).then((widget) => {
          const buildedContext = buildWidgetForTheGaugeDrilldown({
            ...this.item,
            widget,
          })

          let widgetDef = buildedContext.widget

          this.isAlertEventGauge = ['log', 'flow', 'trap'].includes(
            widget?.groups?.[0]?.category
          )

          this.widgetDef = {
            ...(!this.isAlertEventGauge
              ? transformWidgetForServer(widgetDef)
              : widgetDef),
            'drill.down': WidgetTypeConstants.GAUGE,
          }
          this.filteredCounters = buildedContext.filteredCounters

          this.loading = false
        })
      }
    },
  },
}
</script>
