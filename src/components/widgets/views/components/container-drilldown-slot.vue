<template>
  <span
    v-if="shouldShowContainerDrilldown"
    class="text-ellipsis text-primary cursor-pointer"
    style="font-weight: 500"
    :title="value"
    @click="handleMountDrilldown"
  >
    {{ value || '-' }}
    <ContainerTemplate
      v-if="mountDrilldown"
      :open="isDrawerOpen"
      :container-item="drilldownItem"
      :is-fullscreen="$attrs['is-fullscreen']"
      @hide="handleHide"
    />
  </span>
  <span v-else>
    {{ value || '-' }}
  </span>
</template>

<script>
import ContainerTemplate from '@components/templates/container-template.vue'
import { objectDBWorker } from '@/src/workers'

export default {
  name: 'ContaineDrilldownSlot',
  components: {
    ContainerTemplate,
  },
  props: {
    column: {
      type: Object,
      default() {
        return {}
      },
    },
    value: {
      type: [String, Number],
      default: '',
    },
    columnProps: {
      type: Object,
      default() {
        return {}
      },
    },
    row: {
      type: Object,
      required: true,
    },
    hideTitle: {
      type: Boolean,
      default: false,
    },
    showStatus: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      mountDrilldown: false,
      isDrawerOpen: false,
      monitor: {},
    }
  },
  computed: {
    drilldownItem() {
      return {
        ...this.row,
        name: this.value,
        type: this.monitor.type,
        monitorId: this.monitor.id,
        monitorName: this.monitor.name,
        container: this.row.container || 'Docker',
      }
    },

    shouldShowContainerDrilldown() {
      return this.monitor.type === this.$constants.LINUX && this.value
    },
  },
  async mounted() {
    let monitor
    if (this.row['monitor.id']) {
      monitor = await objectDBWorker.getObjectById(this.row['monitor.id'])
    }
    if (this.row['entity.id']) {
      monitor = await objectDBWorker.getObjectById(this.row['entity.id'])
    } else if (this.row['monitor']) {
      monitor = await objectDBWorker.getObjectByIP(this.row['monitor'])
    }
    if (monitor) {
      this.monitor = Object.freeze(monitor)
    }
  },
  methods: {
    handleMountDrilldown() {
      this.mountDrilldown = true
      this.$nextTick(() => {
        this.isDrawerOpen = true
      })
    },
    handleHide() {
      this.isDrawerOpen = false
      setTimeout(() => {
        this.mountDrilldown = false
      }, 400)
    },
  },
}
</script>
