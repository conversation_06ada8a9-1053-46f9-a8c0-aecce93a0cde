<template>
  <!-- <div class="column">
    <div class="content">
      <MIcon
        v-if="showStatus"
        :name="statusIcon"
        class="mr-1"
        :class="statusClass"
        size="lg"
      /> -->
  <span
    class="text-ellipsis font-500"
    :class="{ 'text-primary cursor-pointer': <PERSON><PERSON><PERSON>(processName) }"
    :title="value"
    @click="handleMountDrilldown"
  >
    {{ processName || '-' }}
    <MIcon
      v-if="commandLine"
      name="info-circle"
      class="mr-1"
      :title="commandLine"
    />
    <ProcessTemplate
      v-if="mountDrilldown"
      :open="isDrawerOpen"
      :is-fullscreen="$attrs['is-fullscreen']"
      :process="drilldownItem"
      @hide="handleHide"
    />
  </span>
  <!-- </div>
  </div> -->
</template>

<script>
import ProcessTemplate from '@components/templates/process-template.vue'
import { objectDBWorker } from '@/src/workers'

export default {
  name: 'ProcessDrilldownSlot',
  components: {
    ProcessTemplate,
  },
  props: {
    column: {
      type: Object,
      default() {
        return {}
      },
    },
    value: {
      type: [String, Number],
      default: '',
    },
    columnProps: {
      type: Object,
      default() {
        return {}
      },
    },
    row: {
      type: Object,
      required: true,
    },
    hideTitle: {
      type: Boolean,
      default: false,
    },
    showStatus: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      mountDrilldown: false,
      isDrawerOpen: false,
      monitor: {},
    }
  },
  computed: {
    processName() {
      if (!this.value) {
        return undefined
      }
      return this.value.split('|')[0]
    },
    commandLine() {
      if (!this.value) {
        return undefined
      }
      return this.value.split('|').slice(1).join('|')
    },
    drilldownItem() {
      return {
        ...this.row,
        name: this.value,
        type: this.monitor.type,
        monitorId: this.monitor.id,
        monitorName: this.monitor.name,
      }
    },
  },
  async mounted() {
    let monitor
    if (this.row['entity.id']) {
      monitor = await objectDBWorker.getObjectById(this.row['entity.id'])
    } else if (this.row['monitor.id']) {
      monitor = await objectDBWorker.getObjectById(this.row['monitor.id'])
    } else if (this.row['monitor']) {
      monitor = await objectDBWorker.getObjectByIP(this.row['monitor'])
    }
    if (monitor) {
      this.monitor = Object.freeze(monitor)
    }
  },
  methods: {
    handleMountDrilldown() {
      if (this.commandLine) {
        this.mountDrilldown = true
        this.$nextTick(() => {
          this.isDrawerOpen = true
        })
      }
    },
    handleHide() {
      this.isDrawerOpen = false
      setTimeout(() => {
        this.mountDrilldown = false
      }, 400)
    },
  },
}
</script>
