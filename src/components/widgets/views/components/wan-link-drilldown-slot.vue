<template>
  <span
    v-if="shouldShowWANLinkDrilldown"
    class="text-ellipsis text-primary cursor-pointer"
    style="font-weight: 500"
    :title="value"
    @click="handleMountDrilldown"
  >
    {{ value || '-' }}
    <WanLinkTemplate
      v-if="mountDrilldown"
      :open="isDrawerOpen"
      :wan-link-item="drilldownItem"
      :is-fullscreen="$attrs['is-fullscreen']"
      @hide="handleHide"
    />
  </span>
  <span v-else>
    {{ value || '-' }}
  </span>
</template>

<script>
import WanLinkTemplate from '@components/templates/wan-link-template.vue'
import { objectDBWorker } from '@/src/workers'

export default {
  name: 'WanLinkDrilldownSlot',
  components: {
    WanLinkTemplate,
  },
  props: {
    column: {
      type: Object,
      default() {
        return {}
      },
    },
    value: {
      type: [String, Number],
      default: '',
    },
    columnProps: {
      type: Object,
      default() {
        return {}
      },
    },
    row: {
      type: Object,
      required: true,
    },
    hideTitle: {
      type: Boolean,
      default: false,
    },
    showStatus: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      mountDrilldown: false,
      isDrawerOpen: false,
      monitor: {},
    }
  },
  computed: {
    drilldownItem() {
      return {
        ...this.computeWanlik,
        ...this.row,
        name: this.value,
        type: this.monitor.type,
        monitorId: this.monitor.id,
        monitorName: this.monitor.name,
      }
    },

    shouldShowWANLinkDrilldown() {
      return (
        this.drilldownItem.wanProbe ||
        this.drilldownItem.ipsla_operation_type_last
      )
    },
    computeWanlik() {
      const wanlink = this.value.split('-')

      return {
        ipsla_operation_type_last: wanlink[0],
        isp: wanlink[1],
        sourceTodestination: wanlink[2],
      }
    },
  },
  async mounted() {
    let monitor
    if (this.row['monitor.id']) {
      monitor = await objectDBWorker.getObjectById(this.row['monitor.id'])
    }
    if (this.row['entity.id']) {
      monitor = await objectDBWorker.getObjectById(this.row['entity.id'])
    } else if (this.row['monitor']) {
      monitor = await objectDBWorker.getObjectByIP(this.row['monitor'])
    }
    if (monitor) {
      this.monitor = Object.freeze(monitor)
    }
  },
  methods: {
    handleMountDrilldown() {
      this.mountDrilldown = true
      this.$nextTick(() => {
        this.isDrawerOpen = true
      })
    },
    handleHide() {
      this.isDrawerOpen = false
      setTimeout(() => {
        this.mountDrilldown = false
      }, 400)
    },
  },
}
</script>
