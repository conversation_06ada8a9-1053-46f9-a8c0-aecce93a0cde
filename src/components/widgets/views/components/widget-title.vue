<template>
  <div class="flex justify-between items-center" :data-widget-id="guid">
    <div class="flex-1 flex w-8/12">
      <div
        v-if="title && !hideTitle"
        class="pl-2 min-w-0 text-ellipsis flex-1 flex font-500 items-center my-2"
        :title="title"
      >
        <div class="text-ellipsis" :style="{ fontSize: headerFontSize }">
          {{ title }}
        </div>
        <!-- <MPopover
          v-if="shouldShowResultError"
          placement="bottom"
          overlay-class-name="readable-content-overlay widget-error"
        >
          <template v-slot:trigger>
            <small class="text-xs ml-1">
              <MIcon
                name="exclamation-triangle"
                class="text-secondary-orange"
              />
            </small>
          </template>
          <div v-html="convertedResultError"></div>
        </MPopover> -->
      </div>
    </div>
    <MButton
      v-if="isFullscreen"
      variant="transparent"
      @click="$emit('exit-fullscreen')"
    >
      <MIcon name="exit-fullscreen" size="lg" />
    </MButton>
    <slot name="flip-toggle" />
    <div
      v-if="!isPreview || hideTimeline"
      class="inline-flex items-center ml-auto"
    >
      <TimeRangePicker
        v-if="shouldShowTimeLine"
        class="widget-action"
        :pill-style="timeRangePillStyle"
        :value="timeRange || widget.timeRange"
        disabled
        hide-selected-time
        only-label
        :get-popup-container="getPopupContainer"
      />
      <FlotoGridActions
        v-if="!isFullscreen && !hideActions"
        use-delayed-hide
        :class="{
          'my-2': hideTitle || !title,
        }"
        :text-class-name="Boolean(backgroundColor) ? 'text-white' : undefined"
        :get-popup-container="getPopupContainer"
        class="pb-0 pt-0 pl-1 pr-2 ml-auto widget-action"
        :actions="actions"
        :resource="{}"
        :edit-permission-name="$constants.WIDGET_SETTINGS_UPDATE_PERMISSION"
        :delete-permission-name="
          $constants.DASHBOARD_SETTINGS_UPDATE_PERMISSION
        "
        :create-permission-name="$constants.WIDGET_SETTINGS_CREATE_PERMISSION"
        @fullscreen="$emit('fullscreen')"
        @share="$emit('share')"
        @remove="$emit('remove')"
        @clone="$emit('clone')"
        @edit="$emit('edit')"
        @csv-export="$emit('csv-export')"
      />
    </div>
  </div>
</template>

<script>
import Uniq from 'lodash/uniq'
import Color from 'color'
import { authComputed } from '@state/modules/auth'
import TimeRangePicker from '../../time-range-picker.vue'
import { WidgetTypeConstants } from '../../constants'
export default {
  name: 'WidgetTitle',
  components: { TimeRangePicker },
  inheritAttrs: false,
  props: {
    widget: {
      type: Object,
      default() {
        return {
          timeRange: undefined,
        }
      },
    },
    timeRange: {
      type: Object,
      default: undefined,
    },
    progress: {
      type: Number,
      default: undefined,
    },
    guid: {
      type: String,
      required: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    forTemplate: {
      type: Boolean,
      default: false,
    },
    isPreview: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: undefined,
    },
    hideActions: {
      type: Boolean,
      default: false,
    },
    hideTitle: {
      type: Boolean,
      default: false,
    },
    headerFontSize: {
      type: String,
      default: '11px',
    },
    resultError: {
      type: String,
      default: undefined,
    },
    isFullscreen: {
      type: Boolean,
      default: false,
    },
    isDashboardFullscreen: {
      type: Boolean,
      default: false,
    },
    isCustomTemplate: {
      type: Boolean,
      default: false,
    },
    excludedActions: {
      type: Array,
      default() {
        return []
      },
    },
    additionalActions: {
      type: Array,
      default() {
        return []
      },
    },
    backgroundColor: {
      type: String,
      default: undefined,
    },
    hideTimeline: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    ...authComputed,
    timeRangePillStyle() {
      if (this.backgroundColor) {
        const color = Color(this.backgroundColor).darken(0.3).toString()
        return {
          background: `${color} !important`,
          color: 'white',
        }
      }
      return undefined
    },
    shouldShowTimeLine() {
      if (this.widget.category === WidgetTypeConstants.FREE_TEXT) {
        return false
      }
      if (this.widget.category === WidgetTypeConstants.HEATMAP) {
        return false
      }
      if (this.hideTimeline) {
        return false
      }
      return (
        !this.forTemplate && !this.isPreview && Boolean(this.widget.timeRange)
      )
    },
    shouldShowCsvExport() {
      return (
        (this.widget.widgetType === WidgetTypeConstants.GRID ||
          [
            WidgetTypeConstants.GRID,
            WidgetTypeConstants.STREAM,
            WidgetTypeConstants.EVENT_HISTORY,
            WidgetTypeConstants.ACTIVE_ALERT,
          ].includes(this.widget.category)) &&
        ![
          WidgetTypeConstants.APPLICATION_AVAILABILITY_TIME_SERIES,
          WidgetTypeConstants.STACKED_SWITCH_VIEW,
          WidgetTypeConstants.AVAILABILITY_TIME_SERIES,
          WidgetTypeConstants.MONITOR_HEALTH,
          WidgetTypeConstants.HARDWARE_SENSOR_GRID,
          WidgetTypeConstants.PORT_VIEW,
          WidgetTypeConstants.STATUS_FLAP,
          WidgetTypeConstants.APPLICATION_STATUS,
          WidgetTypeConstants.WIRELESS_SIGNAL_STRENGTH,
          WidgetTypeConstants.KPI_GAUGE,
          WidgetTypeConstants.ALERT_HISTORY_LIST,
          WidgetTypeConstants.METRO_TILE_COUNT_VIEW,
        ].includes(this.widget.widgetType) &&
        ![
          WidgetTypeConstants.KEY_VALUE_LAYOUT,
          WidgetTypeConstants.COLUMN_LAYOUT,
          WidgetTypeConstants.TWO_COLUMNS_LAYOUT,
          WidgetTypeConstants.OVERVIEW_LAYOUT,
        ].includes(this.widget?.widgetProperties?.layout) &&
        ![
          WidgetTypeConstants.ALERT_STACKED_VERTICAL_BAR,
          WidgetTypeConstants.INTERFACE_TRAFFIC_VIEW,
          WidgetTypeConstants.SITE_VPN_VIEW,
          WidgetTypeConstants.STORAGE_DETAILS_VIEW,
          WidgetTypeConstants.AWS_BILLING_VIEW,
        ].includes(this.widget?.widgetProperties?.view) &&
        ![WidgetTypeConstants.SANKEY].includes(this.widget.category)
      )
    },
    actions() {
      let allActions = [
        { key: 'edit', name: 'Edit Widget', icon: 'pencil' },
        { key: 'clone', name: 'Clone Widget', icon: 'clone' },
        { key: 'fullscreen', name: 'Full Screen', icon: 'fullscreen' },
        { key: 'share', name: 'Share', icon: 'share-alt' },
        {
          key: 'remove',
          name: 'Remove Widget',
          icon: 'times-circle',
          isDanger: true,
        },
      ]

      // Add CSV Export action if the widget type is grid
      if (this.shouldShowCsvExport) {
        allActions.splice(4, 0, {
          key: 'csv-export',
          name: 'Export as CSV',
          icon: 'export-csv',
        })
      }

      const disabled = this.disabled
      if (disabled) {
        return allActions.filter(
          (a) => ['fullscreen', 'share', 'csv-export'].indexOf(a.key) >= 0
        )
      }
      let excludedActions = this.excludedActions
      if (
        !this.hasPermission(this.$constants.WIDGET_SETTINGS_UPDATE_PERMISSION)
      ) {
        excludedActions = [...excludedActions, 'edit']
      }
      if (
        !this.hasPermission(this.$constants.WIDGET_SETTINGS_CREATE_PERMISSION)
      ) {
        excludedActions = [...excludedActions, 'clone']
      }
      if (
        !this.hasPermission(
          this.$constants.DASHBOARD_SETTINGS_UPDATE_PERMISSION
        )
      ) {
        excludedActions = [...excludedActions, 'remove']
      }
      allActions = allActions.filter(
        (i) => excludedActions.includes(i.key) === false
      )
      if (this.additionalActions && this.additionalActions.length) {
        allActions = [...this.additionalActions, ...allActions]
      }
      if (this?.widget.isPredefined) {
        allActions = allActions.filter(
          (i) =>
            ['edit', ...(this.isCustomTemplate ? [] : ['remove'])].includes(
              i.key
            ) === false
        )
      }
      return allActions
    },
    isTodayAvailabilityWidget() {
      return (
        Boolean(this.widget?.groups?.find((g) => g?.type === 'availability')) &&
        [
          WidgetTypeConstants.PIE,
          WidgetTypeConstants.APPLICATION_TODAY_AVAILABILITY,
        ].includes(this.widget.widgetType)
      )
    },
    shouldShowResultError() {
      return this.resultError && !this.isTodayAvailabilityWidget
    },

    convertedResultError() {
      return `<span>${Uniq(this.resultError.split('\n')).join('<br/>')}</span>`
    },
  },
  methods: {
    getPopupContainer() {
      return this.$el.closest('.dashboard-container')
    },
  },
}
</script>
