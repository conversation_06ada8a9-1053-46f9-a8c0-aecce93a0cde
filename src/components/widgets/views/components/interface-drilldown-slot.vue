<template>
  <span
    class="text-ellipsis text-primary cursor-pointer"
    style="font-weight: 500"
    :title="value"
    @click="handleMountDrilldown"
  >
    {{ value || '-' }}
    <Portal v-if="mountDrilldown" to="interface-template">
      <InterfaceTemplate
        :open="isDrawerOpen"
        :interface-item="drilldownItem"
        :is-fullscreen="$attrs['is-fullscreen']"
        @hide="handleHide"
      />
    </Portal>
  </span>
</template>

<script>
import InterfaceTemplate from '@components/templates/interface-template.vue'
import { objectDBWorker } from '@/src/workers'

export default {
  name: 'InterfaceDrilldownSlot',
  components: {
    InterfaceTemplate,
  },
  props: {
    column: {
      type: Object,
      default() {
        return {}
      },
    },
    value: {
      type: [String, Number],
      default: '',
    },
    columnProps: {
      type: Object,
      default() {
        return {}
      },
    },
    row: {
      type: Object,
      required: true,
    },
    hideTitle: {
      type: <PERSON>olean,
      default: false,
    },
    showStatus: {
      type: <PERSON><PERSON><PERSON>,
      default: false,
    },
  },
  data() {
    return {
      mountDrilldown: false,
      isDrawerOpen: false,
      monitor: {},
    }
  },
  computed: {
    drilldownItem() {
      let index = this.row.interface || this.row.instance
      let name = this.row['interface.name'] || this.row['interface_name']
      if (!index) {
        const interfaceFull = this.row.interface || this.row.instance
        index = interfaceFull.split('-').pop()
      }
      if (!name) {
        const interfaceFull = this.row.interface || this.row.instance
        const splitted = interfaceFull.split('-')
        name = splitted.slice(0, splitted.length - 1).join('-')
      }
      return {
        ...this.row,
        interface: index,
        monitorId: this.monitor.id,
        monitorName: this.monitor.name,
        interfaceName: name,
      }
    },
  },
  async mounted() {
    let monitor
    if (this.row['monitor.id']) {
      monitor = await objectDBWorker.getObjectById(this.row['monitor.id'])
    }
    if (this.row['entity.id']) {
      monitor = await objectDBWorker.getObjectById(this.row['entity.id'])
    } else if (this.row['monitor']) {
      monitor = await objectDBWorker.getObjectByIP(this.row['monitor'])
    }
    if (monitor) {
      this.monitor = Object.freeze(monitor)
    }
  },
  methods: {
    handleMountDrilldown() {
      this.mountDrilldown = true
      this.$nextTick(() => {
        this.isDrawerOpen = true
      })
    },
    handleHide() {
      this.isDrawerOpen = false
      setTimeout(() => {
        this.mountDrilldown = false
      }, 400)
    },
  },
}
</script>
