<template>
  <div class="w-full h-full p-3 vue-grid-drag-ignore">
    <div ref="leafletMap" class="w-full h-full"></div>
  </div>
</template>

<script>
import * as Leaflet from 'leaflet'

export default {
  name: 'LeafletMap',
  props: {
    options: { type: Object, required: true },
  },
  data() {
    this.markerIcon = Leaflet.icon({
      iconUrl: require('@assets/images/map-pin.png'),
      iconSize: [22, 25],
      iconAnchor: [9, 21],
      popupAnchor: [0, -14],
    })
    return {
      mapContainer: undefined,
      boxWidth: undefined,
    }
  },
  mounted() {
    this.leafletInit()

    this.$_resizeObserver = new ResizeObserver(() => {
      this.boxWidth = this.$el.querySelector('div:first-child').offsetWidth
    })
    this.$_resizeObserver.observe(this.$el.querySelector('div:first-child'))

    this.$once('hook:beforeDestroy', () => {
      if (this.$_resizeObserver) {
        this.$_resizeObserver.disconnect()
        this.$_resizeObserver = undefined
      }
    })
    this.boxWidth = this.$el.querySelector('div:first-child').offsetWidth
  },
  methods: {
    leafletInit() {
      var mapContainer = Leaflet.map(this.$refs.leafletMap, {
        center: [20.0, 5.0],
        minZoom: 2,
        zoom: 3,
        attributionControl: false,
        noWrap: true,
      })
      var southWest = Leaflet.latLng(-89.98155760646617, -180)
      var northEast = Leaflet.latLng(89.99346179538875, 180)
      var bounds = Leaflet.latLngBounds(southWest, northEast)
      mapContainer.setMaxBounds(bounds)
      Leaflet.tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png', {
        maxZoom: 19,
        transparent: true,
        noWrap: true,
        maxBounds: bounds,
        maxBoundsViscosity: 1.0,
        className: 'map-tiles',
      }).addTo(mapContainer)

      this.registerMarker(mapContainer)
      mapContainer.eachLayer(function (l) {
        if (l.getTooltip) {
          var toolTip = l.getTooltip()
          if (toolTip) {
            mapContainer.closeTooltip(toolTip)
          }
        }
      })
      this.$watch('boxWidth', (newValue, oldValue) => {
        if (newValue !== oldValue) {
          mapContainer.invalidateSize()
        }
      })
    },
    registerMarker(mapContainer) {
      const series = this.options.leafletSeries || []
      series.forEach((p) => {
        if (p.coordinates[0] && p.coordinates[1]) {
          Leaflet.marker([p.coordinates[1], p.coordinates[0]], {
            icon: this.markerIcon,
          })
            .addTo(mapContainer)
            .bindTooltip(this.tooltip(p), {
              className: 'shadow-lg hc-tooltip-bg',
            })
            .openTooltip()
        }
      })
    },
    tooltip(point) {
      let tooltip = `<div style="overflow-wrap: break-word "><b>${
        point.name || ''
      }<br />`
      Object.keys(point || {}).map((key) => {
        if (
          ![
            'name',
            'value',
            'z',
            'geometry',
            '_i',
            'color',
            'className',
            'coordinates',
            'code2',
            'untouchedData',
          ].includes(key)
        ) {
          tooltip += `${key} : ${point[key]}<br/>`
        }
      })
      return `${tooltip}</div>`
    },
  },
}
</script>

<style>
@import url('leaflet/dist/leaflet.css');

/* setOptions */
.leaflet-container {
  background: transparent !important;
}
</style>
