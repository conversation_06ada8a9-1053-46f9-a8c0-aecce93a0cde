<template>
  <div v-if="data.groupByColumn" class="flex flex-col px-2 flex-1">
    <div v-if="!(data.data || []).length" class="flex flex-1 min-h-0">
      <FlotoNoData
        hide-svg
        header-tag="h5"
        variant="neutral"
        icon="exclamation-triangle"
      />
    </div>
    <template v-else>
      <div v-for="group in data.data" :key="group.title" class="mb-2">
        <div class="m-0 mb-1 font-500" style="font-size: 12px">{{
          group.title
        }}</div>
        <component
          :is="component"
          :data="group.data"
          :total-count="group.totalCount"
          :widget="widget"
          remove-padding
          :size="minSize"
          v-bind="$attrs"
          :prevent-center-alignment="((group || {}).data || []).length > 1"
          @heatmap-group-rendered="updateSize($event, group.title)"
          v-on="$listeners"
        />
      </div>
    </template>
  </div>
  <component
    :is="component"
    v-else
    :data="data.data"
    :total-count="data.totalCount"
    :widget="widget"
    v-bind="$attrs"
    v-on="$listeners"
  />
</template>

<script>
import Min from 'lodash/min'

import { WidgetTypeConstants } from '../constants'
import HeatMapSingleGroup from './heatmap/heatmap-single-group.vue'
import HeatMapWithHost from './heatmap/heatmap-with-host.vue'

export default {
  name: 'HeatmapView',
  components: {
    HeatMapSingleGroup,
  },
  inheritAttrs: false,
  props: {
    widget: {
      type: Object,
      default() {
        return {}
      },
    },
    data: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      sizeMap: [],
    }
  },
  computed: {
    component() {
      if (this.widget.widgetType === WidgetTypeConstants.HEATMAP_WITH_HOST) {
        return HeatMapWithHost
      }
      return HeatMapSingleGroup
    },

    minSize() {
      return Min(this.sizeMap)
    },
  },
  created() {
    this.sizeMap = []
    if (
      ((this.data || {}).data || []).length === 0 &&
      this.widget.hideEmptyWidget
    ) {
      this.$emit('hide')
    }
  },
  methods: {
    updateSize(event, title) {
      this.sizeMap.push(event)
    },
  },
}
</script>
