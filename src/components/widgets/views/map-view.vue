<template>
  <MapChartOptions
    v-if="data.data"
    :key="widget.widgetType"
    :data="data"
    :widget-type="widgetType"
    :widget="widget"
    @drilldown="handleDrillDown"
  >
    <template v-slot="{ options }">
      <Component
        :is="component"
        v-if="shadowMap"
        :options="options"
        :constructor-type="constructorType"
      />
      <FlotoNoData
        v-else
        hide-svg
        header-tag="h5"
        variant="neutral"
        icon="exclamation-triangle"
      />
    </template>
  </MapChartOptions>
  <FlotoContentLoader v-else loading />
</template>

<script>
import CloneDeep from 'lodash/cloneDeep'
import Uniq from 'lodash/uniq'

import Highmap from './map/highmap.vue'
import LeafletMap from './map/leaflet-map.vue'

// import chart from '@components/chart/chart.vue'
import MapChartOptions from '@/src/components/chart/options/map-chart-options.vue'
import {
  WidgetTypeConstants,
  MAP_SERIES_TYPE,
  FILTER_CONDITION_DEFAULT_DATA,
} from '@components/widgets/constants'
import {
  FLOW_DRILLDOWN_IGNORED_COUNTERS,
  FLOW_DRILLDOWN_REPLACEMENT_COUNTERS_MAP,
} from '../helper'
import { appendNewFilter } from '@/src/modules/log/helpers/log.helper'

export default {
  name: 'MapView',
  components: {
    // Chart,
    MapChartOptions,
  },
  inheritAttrs: false,
  props: {
    data: {
      type: [Array, Object],
      default() {
        return []
      },
    },
    widget: {
      type: Object,
      required: true,
    },
  },
  computed: {
    widgetType() {
      return this.widget.widgetType
    },
    constructorType() {
      if (this.widgetType === WidgetTypeConstants.MAP_VIEW) {
        return 'mapChart'
      }
      return 'chart'
    },
    component() {
      if (this.widget.widgetType === WidgetTypeConstants.ONLINE_MAP) {
        return LeafletMap
      }
      return Highmap
    },
    shadowMap() {
      if (
        this.data?.data &&
        ((this.data?.data?.[MAP_SERIES_TYPE.MAP_BUBBLE] || [])?.length ||
          (this.data?.data?.[MAP_SERIES_TYPE.MAP_POINT] || [])?.length)
      ) {
        return true
      } else {
        return false
      }
    },
  },
  methods: {
    handleDrillDown(e) {
      if (this.widget.category !== WidgetTypeConstants.TOPN) {
        return
      }
      if (this.widget?.groups?.[0]?.type === 'metric') {
        let instance

        const selectedCounter = this.widget?.groups?.[0]?.counters?.map(
          (c) => c.counter.key
        )

        let counterPart = selectedCounter?.[0]?.split('~')

        if (
          counterPart?.length > 1 &&
          counterPart[0] === this.widget.primaryResultBy?.[0]
        ) {
          instance =
            e.point.options.untouchedData[this.widget.primaryResultBy?.[0]]
        }

        const instanceContext = {
          instance: instance,
          instanceType: this.widget.primaryResultBy?.[0],
        }

        const metrics = {
          [e.point?.untouchedData?.['entity.id']]: selectedCounter?.[0],
        }

        if (instance || this.widget.primaryResultBy?.[0] === 'monitor') {
          this.$router.push(
            this.$modules.getModuleRoute('metric-explorer', '', {
              query: {
                metrics: encodeURIComponent(btoa(JSON.stringify(metrics))),
                t: encodeURIComponent(
                  btoa(JSON.stringify(this.widget.timeRange))
                ),
                ...(instance
                  ? {
                      instanceContext: encodeURIComponent(
                        btoa(JSON.stringify(instanceContext))
                      ),
                    }
                  : {}),
              },
            })
          )
        }
      } else if (this.widget?.groups?.[0]?.type === 'log') {
        const group =
          this.widget.groups.find((group) => group.type === 'log') || {}

        let resultBy = {}

        resultBy = e.point.options.untouchedData.resultByResolverValues

        let filterMap = []

        for (const key of Object.keys(resultBy)) {
          filterMap = filterMap.concat([
            {
              operand: key,
              operator: '=',
              value: resultBy[key],
            },
          ])
        }

        group.filters.drillDownFilters = appendNewFilter(
          filterMap,
          group.filters.drillDownFilters ||
            CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
          true
        )
        this.$router.push(
          this.$modules.getModuleRoute('log', 'log-search', {
            query: {
              filter: encodeURIComponent(btoa(JSON.stringify(group.filters))),
              t: encodeURIComponent(
                btoa(JSON.stringify(this.widget.timeRange))
              ),
            },
          })
        )
      } else if (this.widget?.groups?.[0]?.type === 'flow') {
        const group = this.widget.groups.find((group) => group.type === 'flow')

        const changedCounterKey =
          FLOW_DRILLDOWN_REPLACEMENT_COUNTERS_MAP[
            group?.counters?.[0]?.counter?.key
          ]
        const changedCounters =
          changedCounterKey && group?.counters?.length === 1
            ? {
                counters: [
                  {
                    aggrigateFn: 'sum',
                    counter: {
                      key: changedCounterKey,
                    },
                  },
                ],
              }
            : undefined

        if (
          group?.counters?.find((c) =>
            FLOW_DRILLDOWN_IGNORED_COUNTERS.includes(c.counter.key)
          )
        ) {
          return
        }

        let data = {
          ...group,
          category: this.widget.category,
          chartType: WidgetTypeConstants.PIE,
          countersValue: changedCounterKey
            ? [changedCounterKey]
            : Uniq([group?.counters?.[0]?.counter?.key]),
          ...(changedCounters || {}),
        }

        let resultBy = {}

        resultBy = e.point.options.untouchedData.resultByResolverValues

        let filterMap = []

        for (const key of Object.keys(resultBy)) {
          filterMap = filterMap.concat([
            {
              operand: key,
              operator: '=',
              value: resultBy[key],
            },
          ])
        }

        data.filters.drillDownFilters = appendNewFilter(
          filterMap,

          data.filters.drillDownFilters ||
            CloneDeep(FILTER_CONDITION_DEFAULT_DATA),

          true
        )

        for (const counter of group.counters) {
          if ((counter.target || {}).entityType === 'event.source') {
            resultBy['event.source'] = [
              ...(resultBy['event.source'] || []),
              ...(Array.isArray((counter.target || {}).entities)
                ? (counter.target || {}).entities?.['event.source']?.includes(
                    (counter.target || {}).entityType
                  ) || []
                : Object.keys((counter.target || {}).entities || {}).length
                ? Object.keys((counter.target || {}).entities)
                : []),
            ]
          }
        }

        if (resultBy['event.source']) {
          data.target = {
            entityType: 'event.source',
            entities: Array.isArray(resultBy['event.source'])
              ? Uniq(resultBy['event.source'])
              : [resultBy['event.source']],
          }
        }

        this.$router.push(
          this.$modules.getModuleRoute('flow', 'explorer', {
            query: {
              flow: encodeURIComponent(btoa(JSON.stringify(data))),
              t: encodeURIComponent(
                btoa(JSON.stringify(this.widget.timeRange))
              ),
            },
          })
        )
      }
    },
  },
}
</script>
