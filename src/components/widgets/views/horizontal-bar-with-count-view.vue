<template>
  <div
    v-if="seriesData.length"
    ref="containerRef"
    class="flex justify-around items-center h-full w-full px-4 flex-col"
  >
    <div class="flex justify-between w-full">
      <h4 class="self-start text-sm mb-0">
        <span class="text-3xl font-600">{{ total }} </span>| Total</h4
      >
      <!-- <div class="flex">
        <Severity
          v-for="categories in seriesData"
          :key="categories.name"
          :severity="categories.name.toLowerCase()"
          class="mr-2"
        />
      </div> -->
    </div>
    <div
      v-for="categories in seriesData"
      :key="categories.name"
      class="w-full"
      :style="{
        cursor: widget ? 'pointer' : 'unset',
      }"
    >
      <Progressbar
        :data="categories"
        :total="total"
        view="bar"
        @filter-by-severity="onSeverityFilterApplied"
      />
    </div>
  </div>
  <FlotoNoData
    v-else
    hide-svg
    header-tag="h5"
    icon="exclamation-triangle"
    variant="neutral"
  />
</template>

<script>
import CloneDeep from 'lodash/cloneDeep'
import Progressbar from './components/progressbar.vue'
// import Severity from '@components/severity'

export default {
  name: 'HorizontalBarWithCountView',
  components: {
    Progressbar,
    //  Severity
  },
  props: {
    data: { type: Object, required: true },
    widget: {
      type: Object,
      default: undefined,
    },
  },

  data() {
    return {
      seriesData: CloneDeep(this.data.series[0].data) || [],
    }
  },
  computed: {
    total() {
      let total = 0

      // let total = this.seriesData.reduce((accumulator, currentValue) => {
      //   return accumulator + currentValue.y
      // }, 0)

      for (const dataPoint of this.seriesData) {
        total += dataPoint.y
      }

      return total
    },
  },
  methods: {
    onSeverityFilterApplied(event) {
      this.$emit('drilldown', undefined, event)
    },
  },
}
</script>
