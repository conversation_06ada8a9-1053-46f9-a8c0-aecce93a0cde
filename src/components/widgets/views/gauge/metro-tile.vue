<template>
  <div
    class="flex flex-1 flex-col justify-between rounded"
    :style="headerStyle"
  >
    <Title
      :title="metroTileTitle"
      :background-color="headerStyle.background"
      :progress="$attrs['progress']"
      :hide-timeline="$attrs['hide-timeline']"
      :hide-actions="
        $attrs.fullscreen || $attrs['hide-actions'] || customResult.hideAction
      "
      :disabled="$attrs.disabled"
      :is-dashboard-fullscreen="$attrs['is-dashboard-fullscreen']"
      :guid="$attrs.guid"
      :is-preview="isPreview"
      :is-fullscreen="$attrs['is-fullscreen']"
      :for-template="$attrs['for-template']"
      :time-range="$attrs['time-range']"
      :widget="$attrs['widget']"
      :result-error="$attrs['result-error']"
      :header-font-size="headerFontSize"
      :hide-title="$attrs['hide-title']"
      :is-custom-template="$attrs['is-custom-template']"
      @fullscreen="makeFullScreen"
      @exit-fullscreen="exitFullScreen"
      v-on="$listeners"
    />
    <FlotoNoData
      v-if="shouldShowNodata"
      hide-svg
      header-tag="h5"
      icon="exclamation-triangle"
      variant="neutral"
    />
    <div
      v-else
      class="flex w-full flex-1 items-center rounded"
      :style="{
        fontSize: `${textFontSizeValue}px`,
        ...style,
        justifyContent: `${textAlign}`,
      }"
      @click="$emit('drilldown')"
    >
      <template v-if="containerHeight">
        <MIcon
          v-if="iconName && iconPosition === 'prefix'"
          :name="iconName"
          class="mx-2"
          style="font-size: inherit"
        />
        <div class="min-w-0 mx-2 text-ellipsis">
          <span
            v-if="isNaN(extractedValue.value)"
            :style="{
              fontSize: `${kpiFontSizeValue}px`,
              fontWeight: 600,
              lineHeight: 1,
            }"
            v-text="extractedValue.value"
          />
          <NumberAnimation
            v-else
            :style="{
              fontSize: `${kpiFontSizeValue}px`,
              lineHeight: 1,
              fontWeight: '600',
            }"
            :format="numberFormat"
            :from="previousValue"
            :to="extractedValue.value"
            :duration="0.5"
          />
          <span
            v-if="extractedValue.unit"
            class="font-500"
            v-text="extractedValue.unit"
          />
        </div>
        <MIcon
          v-if="iconName && iconPosition === 'suffix'"
          :name="iconName"
          class="mx-2"
          style="font-size: inherit"
        />
      </template>
    </div>
  </div>
</template>

<script>
import NumberFormat from '@src/filters/number-format'
import MsToUnit from '@src/filters/ms'
import NumberAnimation from 'vue-number-animation/Number.vue'
import { extractUnitAndValue, getAllowedUnit } from '@utils/unit-checker'
import Title from '../components/widget-title.vue'

export default {
  name: 'MetroTile',
  components: {
    Title,
    NumberAnimation,
  },
  inheritAttrs: false,
  props: {
    headerFontSize: {
      type: String,
      default: undefined,
    },
    fontSize: {
      type: String,
      default: undefined,
    },
    textAlign: {
      type: String,
      default: undefined,
    },
    titleText: {
      type: String,
      default: undefined,
    },
    title: {
      type: String,
      default: undefined,
    },
    value: {
      type: [String, Number],
      default: undefined,
    },
    formattedValue: {
      type: [String, Number],
      default: undefined,
    },
    iconName: {
      type: String,
      default: undefined,
    },
    iconPosition: {
      type: String,
      default: undefined,
    },
    color: {
      type: String,
      default: undefined,
    },
    isPreview: {
      type: Boolean,
      default: false,
    },
    makeFullScreen: {
      type: Function,
      default: undefined,
    },
    exitFullScreen: {
      type: Function,
      default: undefined,
    },
    counter: {
      type: String,
      default: undefined,
    },
    showNoData: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  data() {
    this.$_resizeObserver = undefined
    return {
      containerHeight: null,
      previousValue: this.value,
      previousFromattedValue: this.formattedValue,
    }
  },
  computed: {
    extractedValue() {
      const allowedUnit = getAllowedUnit(this.counter)
      const isMsCounter = ['ms', 's'].includes(allowedUnit)

      if (this.customResult.value) {
        return {
          value: NumberFormat(this.customResult.value || 0),
        }
      }
      if (isMsCounter) {
        return extractUnitAndValue(
          MsToUnit(
            allowedUnit === 's' ? (this.value || 0) * 1000 : this.value || 0
          )
        )
      }
      if (this.formattedValue && typeof this.formattedValue === 'string') {
        return extractUnitAndValue(this.formattedValue)
      }

      return {
        value: NumberFormat(this.value || 0),
      }
    },
    kpiFontSizeValue() {
      if (!this.containerHeight) {
        return undefined
      }
      let fontSize
      if (this.fontSize === 'small' || this.customResult.fontSizeSmall) {
        fontSize = this.containerHeight / 3
      } else if (this.fontSize === 'medium') {
        fontSize = this.containerHeight / 2
      } else if (this.fontSize === 'large') {
        fontSize = this.containerHeight / 1.5
      }
      return Math.max(fontSize, 50)
    },
    textFontSizeValue() {
      if (!this.containerHeight) {
        return undefined
      }
      let fontSize
      if (this.fontSize === 'small' || this.customResult.fontSizeSmall) {
        fontSize = this.containerHeight / (this.iconName ? 7 : 6)
      } else if (this.fontSize === 'medium') {
        fontSize = this.containerHeight / (this.iconName ? 6 : 5)
      } else if (this.fontSize === 'large') {
        fontSize = this.containerHeight / (this.iconName ? 5 : 4)
      }
      return Math.max(fontSize, 30)
    },
    style() {
      const baseStyle = {
        transition: 'background 0.2s ease-in, color 0.2s ease-in',
      }
      if (this.color) {
        return {
          ...baseStyle,
          background: this.color,
          color: 'white',
        }
      }
      return { ...baseStyle, color: `var(--primary)` }
    },
    headerStyle() {
      const baseStyle = {
        transition: 'background 0.2s ease-in, color 0.2s ease-in',
      }
      if (this.color) {
        return {
          ...baseStyle,
          background: this.color,
          color: 'white',
        }
      }
      return { ...baseStyle }
    },
    metroTileTitle() {
      return this.titleText || this.title
    },
    customResult() {
      return {
        ...(this.data?.customResult || {}),
      }
    },
    shouldShowNodata() {
      return this.showNoData || this.data?.showNodata
    },
  },
  watch: {
    value(newValue, oldValue) {
      if (newValue !== oldValue && oldValue) {
        this.previousValue = oldValue
      }
    },
    formattedValue(newValue, oldValue) {
      if (newValue !== oldValue && oldValue) {
        this.previousFromattedValue = oldValue
      }
    },
  },
  mounted() {
    this.$_resizeObserver = new ResizeObserver(() => {
      this.containerHeight = this.$el.offsetHeight
    })
    this.$_resizeObserver.observe(this.$el)
    this.containerHeight = this.$el.offsetHeight
  },
  beforeDestroy() {
    if (this.$_resizeObserver) {
      this.$_resizeObserver.disconnect()
      this.$_resizeObserver = undefined
    }
  },
  methods: {
    numberFormat(number) {
      if (/\.\d+$/.test(number)) {
        return parseFloat(number).toFixed(2)
      }
      return parseInt(number)
    },
  },
}
</script>
