<template>
  <div class="flex flex-col flex-1">
    <Title
      v-if="!forMonitorTemplate"
      :excluded-actions="['edit', 'clone']"
      :title="title"
      :hide-timeline="$attrs['hide-timeline']"
      :hide-actions="$attrs.fullscreen || $attrs['hide-actions']"
      :disabled="$attrs.disabled"
      :is-dashboard-fullscreen="$attrs['is-dashboard-fullscreen']"
      :guid="$attrs.guid"
      :is-preview="isPreview"
      :is-fullscreen="$attrs['is-fullscreen']"
      :for-template="$attrs['for-template']"
      :time-range="$attrs['time-range']"
      :widget="$attrs['widget']"
      :result-error="$attrs['result-error']"
      :header-font-size="headerFontSize"
      :hide-title="$attrs['hide-title']"
      :is-custom-template="$attrs['is-custom-template']"
      @fullscreen="makeFullScreen"
      @exit-fullscreen="exitFullScreen"
      v-on="$listeners"
    />
    <div
      class="flex flex-col justify-around flex-1 px-4 pt-4 pb-2"
      :class="{ 'cursor-pointer': hasDrillDown }"
      @click="handleShowGaugeDrilldown"
    >
      <div class="flex flex-col flex-1">
        <div class="flex flex-1 flex-col" style="flex-shrink: 0">
          <div v-if="icon" class="icon">
            <div class="icon-container text-xl" :class="severity">
              <MIcon :name="icon || 'question'" size="lg" />
            </div>
          </div>
          <div class="title">
            <h4
              class="m-0 mt-1 text-ellipsis text-kpi-header-title"
              :title="header.title"
              >{{ header.title }}</h4
            >
          </div>
          <div class="counters">
            <div
              v-for="(counter, index) in headerCounters"
              :key="counter.counter"
              class="flex-1 min-w-0 flex"
            >
              <span
                class="text-ellipsis"
                style="max-width: 70%; margin-right: 4px"
                :title="counter.title"
                >{{ counter.title }}:</span
              >

              <template v-if="hasData">
                <div
                  class="flex-1"
                  :title="`${
                    nextHeaderCounters[index]
                      ? nextHeaderCounters[index].value
                      : counter.value
                  }${
                    nextHeaderCounters[index]
                      ? nextHeaderCounters[index].unit || ''
                      : counter.unit || ''
                  }`"
                >
                  <Status
                    v-if="isColoredStatus"
                    :value="counter.value !== '' ? counter.value : '-'"
                  />
                  <span
                    v-else-if="isNaN(counter.value) || counter.value === ''"
                    :title="counter.value"
                  >
                    {{ counter.value !== '' ? counter.value : '-' }}
                  </span>
                  <NumberAnimation
                    v-else
                    :format="numberFormat"
                    :from="counter.value"
                    :to="
                      nextHeaderCounters[index]
                        ? nextHeaderCounters[index].value
                        : counter.value
                    "
                    :duration="0.5"
                  />
                  <span
                    v-if="counter.unit && !isColoredStatus"
                    v-text="
                      nextHeaderCounters[index]
                        ? nextHeaderCounters[index].unit
                        : counter.unit
                    "
                  />
                </div>
              </template>

              <div v-else class="flex-1"> - </div>
            </div>
          </div>
        </div>
        <div class="flex flex-1">
          <div class="flex flex-1 items-end justify-between min-w-0">
            <div
              v-for="(counter, index) in footerCounters"
              :key="counter.counter"
              class="flex-1 min-w-0"
              style="flex-shrink: 0"
            >
              <h6 class="m-0 font-500 text-kpi-footer" :title="header.title">{{
                counter.title
              }}</h6>
              <div
                v-if="hasData"
                :class="
                  counter.slot === 'gauge' ? '' : 'flex items-center min-w-0'
                "
              >
                <MIcon
                  v-if="
                    ['up', 'down'].includes(
                      (counter.title || counter.counter).toLowerCase()
                    )
                  "
                  :name="
                    (counter.title || counter.counter).toLowerCase() === 'up'
                      ? 'long-arrow-up'
                      : 'long-arrow-down'
                  "
                  size="lg"
                  class="mr-1"
                  :class="{
                    'text-secondary-green':
                      (counter.title || counter.counter).toLowerCase() === 'up',
                    'text-secondary-red':
                      (counter.title || counter.counter).toLowerCase() ===
                      'down',
                  }"
                />
                <div
                  class="text text-ellipsis"
                  :title="`${
                    nextFooterCounters[index] &&
                    nextFooterCounters[index].value !== undefined
                      ? nextFooterCounters[index].value
                      : counter.value
                  }${
                    nextFooterCounters[index] && nextFooterCounters[index].unit
                      ? nextFooterCounters[index].unit || ''
                      : counter.unit || ''
                  }`"
                  :class="severity"
                >
                  <Status
                    v-if="isColoredStatus"
                    :value="counter.value !== '' ? counter.value : '-'"
                  />
                  <span
                    v-else-if="isNaN(counter.value) || counter.value === ''"
                    :style="{
                      fontSize: footerFontSizeValue,
                      lineHeight: 1,
                      fontWeight: 600,
                    }"
                    v-text="counter.value !== '' ? counter.value : '-'"
                  />
                  <NumberAnimation
                    v-else
                    :style="{
                      fontSize: footerFontSizeValue,
                      lineHeight: 1,
                      fontWeight: 600,
                    }"
                    :format="numberFormat"
                    :from="counter.value"
                    :to="
                      nextFooterCounters[index] &&
                      nextFooterCounters[index].value !== undefined
                        ? nextFooterCounters[index].value
                        : counter.value
                    "
                    :duration="0.5"
                  />
                  <span
                    v-if="counter.unit && !isColoredStatus"
                    :style="{
                      fontSize: `16px`,
                      marginLeft: `4px`,
                      fontWeight: 500,
                    }"
                    v-text="
                      nextFooterCounters[index] &&
                      nextFooterCounters[index].unit
                        ? nextFooterCounters[index].unit
                        : counter.unit
                    "
                  />
                </div>
                <div
                  v-if="counter.slot === 'gauge'"
                  class="ant-progress ant-progress-line ant-progress-small"
                >
                  <div>
                    <div class="ant-progress-outer">
                      <div class="ant-progress-inner">
                        <div
                          class="ant-progress-bg"
                          :class="severity"
                          :style="{
                            width:
                              nextFooterCounters[index] &&
                              nextFooterCounters[index].value !== undefined
                                ? `${nextFooterCounters[index].value.replace(
                                    /\s/g,
                                    ''
                                  )}${nextFooterCounters[index].unit || '%'}`
                                : counter.value
                                ? `${counter.value.replace(/\s/g, '')}${
                                    counter.unit || '%'
                                  }`
                                : 0,
                          }"
                          style="height: 6px; border-radius: 100px"
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div
                v-else
                :style="{
                  fontSize: footerFontSizeValue,
                  lineHeight: 1,
                  fontWeight: 600,
                }"
              >
                -
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <FlotoNoData
      v-else
      hide-svg
      header-tag="h5"
      icon="exclamation-triangle"
      variant="neutral"
    /> -->
  </div>
</template>

<script>
import NumberAnimation from 'vue-number-animation/Number.vue'
import Bus from '@utils/emitter'
import NumberFormat from '@src/filters/number-format'
import MsToUnit from '@src/filters/ms'
import { extractUnitAndValue, getAllowedUnit } from '@utils/unit-checker'
import { severityDBWorker } from '@/src/workers'
import Title from '../components/widget-title.vue'
import Datetime from '@src/filters/datetime'
import Status from '../components/status.vue'

export default {
  name: 'KPIGauge',
  components: {
    NumberAnimation,
    Title,
    Status,
  },
  inheritAttrs: false,
  props: {
    widget: {
      type: Object,
      required: true,
    },
    data: {
      type: Object,
      default() {
        return {}
      },
    },
    monitor: {
      type: Object,
      default: undefined,
    },
    headerFontSize: {
      type: String,
      default: undefined,
    },
    fontSize: {
      type: String,
      default: undefined,
    },
    textAlign: {
      type: String,
      default: undefined,
    },
    titleText: {
      type: String,
      default: undefined,
    },
    title: {
      type: String,
      default: undefined,
    },
    value: {
      type: [String, Number],
      default: undefined,
    },
    formattedValue: {
      type: [String, Number],
      default: undefined,
    },
    forMonitorTemplate: {
      type: Boolean,
      default: false,
    },
    iconName: {
      type: String,
      default: undefined,
    },
    iconPosition: {
      type: String,
      default: undefined,
    },
    color: {
      type: String,
      default: undefined,
    },
    isPreview: {
      type: Boolean,
      default: false,
    },
    makeFullScreen: {
      type: Function,
      default: undefined,
    },
    exitFullScreen: {
      type: Function,
      default: undefined,
    },
    counter: {
      type: String,
      default: undefined,
    },
    showNoData: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    this.windowWidth = window.innerWidth
    this.timestempCounters = ['cache.average.access.time.last']
    return {
      severity: 'neutral-severity',
      headerCounters: [],
      footerCounters: [],
      nextHeaderCounters: [],
      nextFooterCounters: [],
    }
  },
  computed: {
    header() {
      return this.widget.widgetProperties.header || {}
    },
    headerStyle() {
      return this.header.style || {}
    },

    footer() {
      return this.widget.widgetProperties.footer || {}
    },
    footerStyle() {
      return this.footer.style || {}
    },
    footerFontSizeValue() {
      if (this.footerStyle.fontSize === 'small') {
        return '15px'
      } else if (this.footerStyle.fontSize === 'medium') {
        return '24px'
      } else if (this.footerStyle.fontSize === 'large') {
        return this.windowWidth <= 1366 ? '24px' : '30px'
      }

      return '24px'
    },
    icon() {
      return this.widget.widgetProperties.iconName
    },
    hasDrillDown() {
      return (
        ((this.widget.groups[0] || {}).corelatedCounters || []).filter(
          (c) => c.type === 'metric'
        ).length > 0 && this.hasData
      )
    },

    hasData() {
      return !!Object.keys(this.data).length
    },
    isColoredStatus() {
      return [
        10000000002969, 10000000002981, 10000000002991, 10000000003001,
        10000000008098, 10000000008111, 10000000008123,
      ].includes(this.widget.id)
    },
  },
  watch: {
    data(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.headerCounters =
          this.nextHeaderCounters.length > 0
            ? this.nextHeaderCounters
            : this.headerCounters
        this.footerCounters =
          this.nextFooterCounters.length > 0
            ? this.nextFooterCounters
            : this.footerCounters
        this.buildHeaderCounters(newValue, true)
        this.buildFooterCounters(newValue, true)
      }
    },
  },
  created() {
    this.resolveSeverity()

    Bus.$on(
      this.$constants.EVENT_SEVERITY_COUNTER_DB_CHANGED,
      this.resolveSeverity
    )
    this.$once('hook:beforeDestroy', () => {
      Bus.$off(
        this.$constants.EVENT_SEVERITY_COUNTER_DB_CHANGED,
        this.resolveSeverity
      )
    })
    this.buildHeaderCounters(this.data)
    this.buildFooterCounters(this.data)
  },
  methods: {
    numberFormat(number) {
      if (/\.\d+$/.test(number)) {
        return parseFloat(number).toFixed(1)
      }
      return parseInt(number)
    },
    buildHeaderCounters(data, forNextData) {
      this[forNextData ? 'nextHeaderCounters' : 'headerCounters'] = (
        this.header.counters || []
      ).map((c) => {
        const allowedUnit = getAllowedUnit(c.counter)
        const isMsCounter = ['ms', 's'].includes(allowedUnit)
        const counterName = c.counter.replace(/[~^]/g, '.')
        return {
          ...c,
          ...(data[`${counterName}.formatted`] && !isMsCounter
            ? extractUnitAndValue(data[`${counterName}.formatted`])
            : {
                ...(isMsCounter
                  ? extractUnitAndValue(
                      MsToUnit(
                        allowedUnit === 's'
                          ? (data[counterName] || 0) * 1000
                          : data[counterName] || 0
                      )
                    )
                  : typeof data[counterName] === 'number'
                  ? extractUnitAndValue(NumberFormat(data[counterName] || 0))
                  : { value: data[counterName] }),
              }),
        }
      })
    },
    buildFooterCounters(data, forNextData) {
      this[forNextData ? 'nextFooterCounters' : 'footerCounters'] =
        this.footer.counters.map((c) => {
          const allowedUnit = getAllowedUnit(c.counter)
          const isMsCounter = ['ms', 's'].includes(allowedUnit)
          const counterName = c.counter.replace(/[~^]/g, '.')
          return {
            ...c,
            ...(data[`${counterName.replace(/[~^]/g, '.')}.formatted`] &&
            !isMsCounter
              ? this.timestempCounters.includes(counterName)
                ? {
                    unit: '',
                    value: Datetime(data[counterName]),
                  }
                : extractUnitAndValue(
                    data[`${counterName}.formatted`],
                    undefined,
                    counterName
                  )
              : {
                  ...(isMsCounter
                    ? extractUnitAndValue(
                        MsToUnit(
                          allowedUnit === 's'
                            ? (data[counterName] || 0) * 1000
                            : data[counterName] || 0
                        )
                      )
                    : typeof data[counterName] === 'number'
                    ? extractUnitAndValue(NumberFormat(data[counterName] || 0))
                    : { value: data[counterName] }),
                }),
          }
        })
    },
    handleShowGaugeDrilldown() {
      const counters = ((this.widget.groups[0] || {}).corelatedCounters || [])
        .map((c) => (c.type === 'metric' ? c.counter : undefined))
        .filter(Boolean)
      if (counters.length && this.hasData) {
        this.$emit('drilldown', {
          type: 'metric.explorer',
          counters,
        })
      }
    },
    async resolveSeverity() {
      if (!this.monitor) {
        return
      }
      const counter = this.widget.widgetProperties.severityCounter
      if (!counter) {
        return
      }
      const monitorId = this.monitor.id
      const severity = await severityDBWorker.getSeverityByEntity(
        monitorId,
        counter
      )
      if (severity) {
        this.severity = severity.severity.toLowerCase()
      }
    },
  },
}
</script>

<style lang="less" scoped>
.counters {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
  color: var(--neutral-regular);
}

.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 45px;
  transition: all 0.5s linear;

  @apply inline-flex rounded-md;

  .@{ant-prefix}icon {
    font-size: inherit;
  }
}

.text {
  color: var(--gauge-text-color) !important;
  transition: all 0.1s ease-in-out;
}
</style>
