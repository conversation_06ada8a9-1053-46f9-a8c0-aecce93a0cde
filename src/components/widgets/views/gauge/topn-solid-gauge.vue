<template>
  <div
    v-if="dataForSolidGauge.length"
    class="flex justify-center items-center h-full w-full px-4"
  >
    <div
      ref="containerRef"
      class="flex justify-center items-center h-full w-full"
    >
      <div
        class="mx-1 severity-text min-w-0 h-full"
        :style="{
          display: 'grid',
          'grid-auto-rows': `minmax(auto, max-content)`,
          'grid-template-columns': `repeat(${repeatedColumn}, minmax(150px, max-content))`,
          'grid-gap': '10px',
        }"
      >
        <div
          v-for="(gauge, index) in dataForSolidGauge"
          :key="index"
          class="flex flex-col justify-center items-center"
        >
          <solidGauge
            :value="gauge.value"
            :formatted-value="gauge.formattedValue"
            :for-topn-solid-gauge="true"
          />
          <div
            class="flex overflow-hidden w-full justify-center"
            :title="gauge['entity.name']"
          >
            <MonitorName :value="gauge['entity.name']" :row="gauge" />
          </div>
        </div>
      </div>
    </div>
  </div>
  <FlotoNoData
    v-else
    hide-svg
    header-tag="h5"
    icon="exclamation-triangle"
    variant="neutral"
  />
</template>

<script>
import CloneDeep from 'lodash/cloneDeep'
import solidGauge from './solid-gauge.vue'
import MonitorName from '@components/widgets/views/grid/view-more/monitor-name.vue'

export default {
  name: 'TopnSolidGauge',
  components: {
    solidGauge,
    MonitorName,
  },
  props: {
    data: { type: Object, required: true },
  },
  data() {
    return {
      categories: CloneDeep(this.data.categories) || [],
      width: undefined,
    }
  },
  computed: {
    seriesData() {
      if (this.data && this.data.series && this.isChartResult) {
        return this.data.series[0]
      }
      return {}
    },

    formattedValues() {
      return this.seriesData.formattedValues || []
    },
    entityIds() {
      return this.seriesData.ids || []
    },
    total() {
      const seriesData = this.seriesData.data || []

      let total = 0

      // (this.seriesData.data || []).reduce(
      //   (accumulator, currentValue) => {
      //     return accumulator + +currentValue
      //   },
      //   0
      // )

      for (const s of seriesData) {
        total += Number(s)
      }

      return total
    },
    dataForSolidGauge() {
      return (this.seriesData.data || []).map((seriesValue, index) => {
        return {
          value: seriesValue,
          formattedValue: this.formattedValues[index],
          'entity.id': this.entityIds[index],
          'entity.name': this.categories[index],
        }
      })
    },
    repeatedColumn() {
      return Math.floor(
        Math.min(
          Math.min(this.width / 180, 6),
          ((this.seriesData || {}).data || []).length
        )
      )
    },
    isChartResult() {
      if (
        this.data &&
        this.data.series &&
        typeof ((this.data.series[0] || {}).data || [])[0] === 'number'
      ) {
        return true
      } else {
        return false
      }
    },
  },
  mounted() {
    if (this.$refs.containerRef) {
      this.$_resizeObserver = new ResizeObserver(() => {
        this.width = this.$refs.containerRef.offsetWidth
        this.$emit('rendered', {
          data: this.data,
          widget: this.$attrs.widget,
          chart: true,
        })
      })
      this.$_resizeObserver.observe(this.$refs.containerRef)
      this.$once('hook:beforeDestroy', () => {
        if (this.$_resizeObserver) {
          this.$_resizeObserver.disconnect()
          this.$_resizeObserver = undefined
        }
      })
      this.width = this.$refs.containerRef.offsetWidth
    }
  },
}
</script>
