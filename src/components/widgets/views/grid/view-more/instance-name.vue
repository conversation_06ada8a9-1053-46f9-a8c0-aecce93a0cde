<template>
  <a v-if="shouldDrilldown" class="text-ellipsis" @click.stop="emitDrilldown">
    {{ row.instance }}
  </a>
  <div v-else> {{ row.instance }}</div>
</template>

<script>
import Bus from '@utils/emitter'
import { isVmInstance, isWirelessAccessPointInstance } from '@utils/vm-map'

export default {
  name: 'InstanceName',
  props: {
    row: {
      type: Object,
      required: true,
    },

    counter: {
      type: Object,
      default: undefined,
    },
  },
  data() {
    return {}
  },
  computed: {
    shouldDrilldown() {
      return (
        this.counter &&
        this.counter?.counter?.instanceType &&
        this.row.instance &&
        (this.isInterface || this.isVm || this.isAp || this.isProcess)
      )
    },

    isVm() {
      return isVmInstance(this.counter?.counter?.instanceType)
    },
    isAp() {
      return isWirelessAccessPointInstance(this.counter?.counter?.instanceType)
    },

    isInterface() {
      return ['interface'].includes(this.counter?.counter?.instanceType)
    },
    isProcess() {
      return ['system.process'].includes(this.counter?.counter?.instanceType)
    },
  },

  methods: {
    emitDrilldown() {
      Bus.$emit('instance-drilldown', {
        ...(this.row || {}),
        isVm: this.isVm,
        isAp: this.isAp,
        isInterface: this.isInterface,
        isProcess: this.isProcess,
      })
    },
  },
}
</script>
