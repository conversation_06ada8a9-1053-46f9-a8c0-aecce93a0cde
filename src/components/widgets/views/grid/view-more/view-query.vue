<template>
  <div class="flex flex-1 min-h-0">
    <slot name="trigger" :toggle="toggle" />
    <MModal
      v-if="open"
      open
      centered
      :width="700"
      :mask-closable="false"
      overlay-class-name="no-padding-confrim-modal"
      @hide="hide"
    >
      <template v-slot:title>
        <div class="flex justify-between">
          <h5 class="text-primary my-2">Query</h5>
          <MIcon name="times" class="mt-2" @click="toggle" />
        </div>
      </template>
      <MRow :gutter="0" class="code-container mx-4 my-4">
        <div
          style="width: 90%; max-height: 400px; overflow: auto"
          class="px-2 py-2"
        >
          {{ data }}
        </div>
        <div style="width: 5%">
          <MButton
            :shadow="false"
            class="grow-0 bg-neutral-lightest cursor-pointer my-2 ml-4 p-1 flex justify-center items-center"
            variant="neutral-lightest"
            @click="handleCopy"
          >
            <MIcon name="copy" size="lg"></MIcon>
          </MButton>
        </div>
      </MRow>

      <template v-slot:footer>
        <slot name="footer">
          <span />
        </slot>
      </template>
    </MModal>
  </div>
</template>

<script>
export default {
  name: 'ViewQuery',
  props: {
    data: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      processing: false,
      open: false,
    }
  },
  methods: {
    handleCopy() {
      const cb = navigator.clipboard
      if (cb) {
        cb.writeText(this.data).then(() => {
          this.$successToast('Query copied to clipboard')
        })
      }
    },
    hide() {
      this.$emit('hide', 400)
    },
    toggle() {
      this.open = !this.open
    },
  },
}
</script>

<style lang="less" scoped>
.code-container {
  border: 1px solid var(--border-color);
  border-radius: 5px;

  .copy-text {
    position: absolute;
    top: 0;
    right: 0;
    padding: 0.2rem 0.5rem;
    visibility: hidden;
    background: var(--page-background-color);
    border: 1px dashed var(--border-color);
  }

  &:hover {
    .copy-text {
      visibility: visible;
    }
  }

  .text-primary {
    font-weight: 600;
    color: var(--primary) !important;
  }
}
</style>
