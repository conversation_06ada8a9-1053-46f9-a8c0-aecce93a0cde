<template>
  <span
    class="text-ellipsis text-primary cursor-pointer"
    style="font-weight: 500"
    :title="values.length"
  >
    <MTag
      :closable="false"
      rounded
      class="used-count-pill"
      @click="handleMountDrilldown"
    >
      {{ values.length }}
    </MTag>

    <ViewMoreGrid
      v-if="mountDrilldown"
      :open="isDrawerOpen"
      :items="values"
      :column-name="column.name"
      :name="nameColumn ? row[nameColumn] : undefined"
      @hide="handleHide"
    />
  </span>
</template>

<script>
import { generateId } from '@utils/id'

import ViewMoreGrid from './view-more-grid.vue'

export default {
  name: 'ViewMoreDrawer',
  components: {
    ViewMoreGrid,
  },
  props: {
    column: {
      type: Object,
      default() {
        return {}
      },
    },
    value: {
      type: [String, Number],
      default: '',
    },
    columnProps: {
      type: Object,
      default() {
        return {}
      },
    },
    row: {
      type: Object,
      required: true,
    },
    hideTitle: {
      type: <PERSON>olean,
      default: false,
    },
    showStatus: {
      type: Boolean,
      default: false,
    },
    columns: {
      type: Array,
      default() {
        return []
      },
    },
  },
  data() {
    return {
      mountDrilldown: false,
      isDrawerOpen: false,
      monitor: {},
    }
  },
  computed: {
    values() {
      if (this.value === '') {
        return []
      }
      return (this.value?.split(',') || [])?.map((item) => ({
        key: generateId(),
        value: item,
      }))
    },
    nameColumn() {
      return this.columns.find((column) => column.name === 'name')?.key
    },
  },

  methods: {
    handleMountDrilldown() {
      this.mountDrilldown = true
      this.$nextTick(() => {
        this.isDrawerOpen = true
      })
    },
    handleHide() {
      this.isDrawerOpen = false
      setTimeout(() => {
        this.mountDrilldown = false
      }, 400)
    },
  },
}
</script>
