<template>
  <component
    :is="
      monitor && params && !isArchive && !isCloud && !ignorelink
        ? 'FlotoLink'
        : 'div'
    "
    class="text-ellipsis"
    v-bind="params"
  >
    <slot name="monitor" :monitor="monitor">
      <span v-if="monitor" style="font-weight: 500">{{ value }}</span>
      <template v-else>{{ value }}</template>
    </slot>
  </component>
</template>

<script>
import isIP from 'validator/lib/isIP'
import { objectDBWorker } from '@/src/workers'

export default {
  name: 'MonitorName',
  props: {
    value: {
      type: String,
      default: undefined,
    },
    row: {
      type: Object,
      default() {
        return {}
      },
    },
    forCloud: {
      type: Boolean,
      default: false,
    },
    forVm: {
      type: Boolean,
      default: false,
    },
    ignorelink: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      monitor: undefined,
      resolving: true,
    }
  },
  computed: {
    params() {
      if (this.monitor) {
        if (this.forVm) {
          if (!(this.monitor.vms || []).includes(this.value)) {
            return null
          }
          return {
            to: this.$modules.getModuleRoute('inventory', 'vm-template', {
              params: {
                monitorId: this.monitor.id,
                category: this.monitor.category,
                vm: this.value,
              },
            }),
          }
        }
        return {
          to: this.$modules.getModuleRoute('inventory', 'monitor-template', {
            params: {
              monitorId: this.monitor.id,
              category: this.monitor.category,
            },
          }),
          target: '_blank',
        }
      }
      return {}
    },
    isArchive() {
      return this.monitor.status === 'ARCHIVE'
    },
    isCloud() {
      return [
        this.$constants.AWS_CLOUD,
        this.$constants.AZURE_CLOUD,
        this.$constants.OFFICE_365,
      ].includes(this.monitor?.type)
    },
  },
  async created() {
    if (this.row['entity.id']) {
      const monitor = await objectDBWorker.getObjectById(this.row['entity.id'])
      this.monitor = Object.freeze(monitor)
      return
    }
    let value = this.value
    if (this.forCloud || this.forVm) {
      value = this.row.monitor
    }
    if (value && isIP(value)) {
      const monitor = await objectDBWorker.getObjectByIP(value)
      if (monitor) {
        this.monitor = Object.freeze(monitor)
      }
      this.resolving = false
    } else if (this.forCloud) {
      const monitor = await objectDBWorker.getObjectByName(value)
      if (monitor) {
        this.monitor = Object.freeze(monitor)
      }
      this.resolving = false
    } else {
      const monitor = await objectDBWorker.getObjectByName(value)
      if (monitor) {
        this.monitor = Object.freeze(monitor)
      }
      this.resolving = false
    }
  },
}
</script>
