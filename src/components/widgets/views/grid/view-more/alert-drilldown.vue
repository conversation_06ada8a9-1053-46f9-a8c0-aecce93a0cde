<template>
  <FlotoLink v-if="tragetBlank" v-bind="params" class="text-ellipsis">
    {{ field }}
  </FlotoLink>

  <a v-else class="text-ellipsis" :title="field" @click="handleClick">
    {{ field }}
  </a>
</template>

<script>
import Capitalize from 'lodash/capitalize'
import { isUnitConvertible } from '@/src/utils/unit-checker'
import applyUnit from '@/src/utils/unit-applier'
import { objectDBWorker } from '@/src/workers'
import { triggerCondition } from '@modules/alert/helpers/alert-helper'

export default {
  name: 'AlertDrilldown',
  props: {
    alert: {
      type: Object,
      required: true,
    },
    field: {
      type: String,
      required: true,
    },
    tragetBlank: {
      type: Boolean,
      default: false,
    },
    isEventPolicyDrilldown: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      monitor: {},
      transformedAlert: undefined,
    }
  },
  computed: {
    params() {
      const params = {
        category: 'Server',
        tab: 'overview',
        uuid: btoa(
          unescape(
            encodeURIComponent(
              JSON.stringify({
                ...(this.transformedAlert || {}),
                view: 'live',
              })
            )
          )
        ),
      }

      const query = {
        t: btoa(
          unescape(
            encodeURIComponent(
              JSON.stringify({
                selectedKey: 'today',
              })
            )
          )
        ),
      }
      return {
        to: this.$modules.getModuleRoute('alert', 'detail', {
          params,
          query,
        }),
        target: '_blank',
      }
    },
  },
  async created() {
    if (this.alert?.['entity.id']) {
      const monitor = await objectDBWorker.getObjectById(
        this.alert['entity.id']
      )
      this.monitor = Object.freeze(monitor)
    }
    await this.transformAlertData()
  },
  methods: {
    transformAlertData() {
      if (this.isEventPolicyDrilldown) {
        this.transformedAlert = this.alert

        return
      }
      const value =
        isUnitConvertible(this.alert['metric']) &&
        /^\d+(.\d+)?$/.test(this.alert.value)
          ? applyUnit(this.alert['metric'], +this.alert.value)
          : this.alert.value
      this.transformedAlert = {
        id: `${this?.monitor?.name}-${this.alert['policy_id']}`,
        policy: this.alert['policy_name'],
        policyType: this.alert['policy_type'],
        policyId: this.alert['policy_id'],
        message: `${
          this.alert['instance']
            ? `<span title='${this.alert['instance']}'>${this.alert[
                'instance'
              ].substr(0, 20)}${
                this.alert['instance'].length > 20 ? '... ' : ''
              }</span>`
            : ''
        } <span class="font-semibold">${this.alert['metric']?.replace(
          /[~^]/g,
          '.'
        )}</span> has entered into <span class="text ${this.alert[
          'severity'
        ]?.toLowerCase()}">${Capitalize(
          this.alert['severity']
        )}</span> state with value [${value}]`,
        monitor: this.alert['entity.id'],
        category: 'Server',
        counterRawName: this.alert['counterRawName'] || this.alert.metric,
        metric: this.alert['metric'],
        acknowledged: this.alert['policy_acknowledged'],
        instance: this.alert['instance'],
        tag: this.alert['tag'],
        severity: this.alert['severity'],
        groupCategory: 'metric',
        firstSeen: this.alert['policy_first_trigger_tick'],
        duration: this.alert['duration'],
        counts: this.alert['counts'],
        view: 'live',
        value,
        ...(this.alert['policySeverity']
          ? {
              triggerCondition: triggerCondition(
                this.alert['policy_type'],
                this.alert['policySeverity'],
                this.alert['value'],
                this.alert['policy_threshold'],
                this.alert['severity'],
                this.alert['metric']?.replace(/[~^]/g, '.')
              ),
            }
          : {}),
      }
    },
    handleClick() {
      const params = {
        category: 'Server',
        tab: 'overview',
        uuid: btoa(
          unescape(
            encodeURIComponent(
              JSON.stringify({
                ...(this.transformedAlert || {}),
                view: 'live',
              })
            )
          )
        ),
      }

      const query = {
        t: btoa(
          unescape(
            encodeURIComponent(
              JSON.stringify({
                selectedKey: 'today',
              })
            )
          )
        ),
      }
      this.$emit('rowclick')

      setTimeout(() => {
        this.$router.push(
          this.$modules.getModuleRoute('alert', 'detail', {
            params,
            query,
          })
        )
      }, 700)
    },
  },
}
</script>
