<template>
  <component
    :is="monitor && params ? 'FlotoLink' : 'div'"
    class="text-ellipsis flex items-center"
    v-bind="params"
  >
    <MonitorType :type="value" :center="false" class="mr-2" />
    {{ value }}
  </component>
</template>

<script>
import MonitorType from '@components/monitor-type.vue'

export default {
  name: 'ApplicationName',
  components: { MonitorType },
  props: {
    value: {
      type: String,
      default: undefined,
    },
    row: {
      type: Object,
      default() {
        return {}
      },
    },
    monitor: {
      type: Object,
      default: undefined,
    },
  },
  computed: {
    params() {
      if (this.monitor) {
        return {
          to: this.$modules.getModuleRoute('inventory', 'application-tab', {
            params: {
              monitorId: this.monitor.id,
              applicationName: this.value,
            },
          }),
          target: '_blank',
        }
      } else {
        return {}
      }
    },
    // monitor() {
    //   return this.$attrs.monitor
    // },
  },
}
</script>
