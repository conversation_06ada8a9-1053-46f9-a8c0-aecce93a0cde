<template>
  <FlotoDrawer
    :open="open"
    width="40%"
    :wrap-style="{ zIndex: 99 }"
    @hide="handleHide"
  >
    <template v-slot:title>
      {{ title }}
    </template>
    <div class="flex flex-col flex-1 min-h-0 px-2">
      <div class="flex mt-3">
        <MInput
          v-model="searchTerm"
          class="search-box"
          placeholder="Search"
          name="search"
        >
          <template v-slot:prefix>
            <MIcon name="search" />
          </template>
          <template v-if="searchTerm" v-slot:suffix>
            <MIcon
              name="times-circle"
              class="text-neutral-light cursor-pointer"
              @click="searchTerm = undefined"
            />
          </template>
        </MInput>
      </div>
      <MGrid
        :search-term="searchTerm"
        :data="items"
        :columns="columns"
        :paging="false"
      >
      </MGrid>
    </div>
  </FlotoDrawer>
</template>

<script>
import Bus from '@utils/emitter'

export default {
  name: 'ViewMoreGrid',
  components: {},
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    columnName: {
      type: String,
      required: true,
    },

    items: {
      type: Array,
      default() {
        return []
      },
    },
    name: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      searchTerm: '',
    }
  },
  computed: {
    columns() {
      return [
        {
          name: this.columnName,
          key: 'value',
          sortable: true,
          searchable: true,
          reorderable: true,
          resizable: true,
          hidden: false,
        },
      ]
    },
    title() {
      return `${this.columnName} ${this.name ? `Of ${this.name}` : ''}`
    },
  },

  created() {
    Bus.$on('row-click', this.handleHide)
  },
  methods: {
    handleHide() {
      this.$emit('hide')
    },
  },
}
</script>
