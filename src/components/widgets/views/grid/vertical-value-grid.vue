<template>
  <div class="px-2 w-full h-full min-h-0 min-w-0 flex flex-1 justify-center">
    <div v-if="showVerticalValueGrid" class="w-full h-full">
      <!-- <MRow
        class="justify-between sticky top-0 w-full"
        :gutter="0"
        style="z-index: 9999"
        :style="{
          height: `${
            isPreview
              ? `var(--widget-background-color)`
              : `var(--page-background-color)`
          } !important`,
        }"
      >
        <MCol :size="10">
          <MInput v-model="searchTerm" class="search-box" placeholder="Search">
            <template v-slot:prefix>
              <MIcon name="search" />
            </template>
            <template v-if="searchTerm" v-slot:suffix>
              <MIcon
                name="times-circle"
                class="text-neutral-light cursor-pointer"
                @click="searchTerm = undefined"
              />
            </template>
          </MInput>
        </MCol>
        <MCol :size="2" class="flex justify-end"> </MCol>
      </MRow> -->
      <MRow
        class="justify-between sticky top-0 w-full"
        :gutter="0"
        style="z-index: 9999"
        :style="{
          height: `${
            isPreview
              ? `var(--widget-background-color)`
              : `var(--page-background-color)`
          } !important`,
        }"
      >
        <!-- <MCol :size="10">
          <MInput v-model="searchTerm" class="search-box" placeholder="Search">
            <template v-slot:prefix>
              <MIcon name="search" />
            </template>
            <template v-if="searchTerm" v-slot:suffix>
              <MIcon
                name="times-circle"
                class="text-neutral-light cursor-pointer"
                @click="searchTerm = undefined"
              />
            </template>
          </MInput>
        </MCol>
        <MCol :size="2" class="flex justify-end"> </MCol> -->
      </MRow>
      <MRow
        v-for="(item, index) in slicedData"
        :key="`${index}-${item.valueColValue}`"
        :gutter="0"
        class="py-2 items-end mb-4 mt-2 flex justify-center items-center"
      >
        <MCol :size="7" class="pr-3 text-right">
          <div class="font-600 text-ellipsis text-vertical-value-grid">
            <div
              class="flex justify-end w-full"
              :title="String(item[valueColumn] || []).replace(/\s/g, '')"
            >
              <ColorCodedCell
                :data-item="item.row"
                :column="valueColumnContext"
                ignore-background
                is-preview
              >
                <span
                  class="flex justify-center items-center"
                  style="color: inherit"
                  v-html="item.value"
                />
                <!-- {{ String(item.value || '').replace(/\s/g, '') }}
                  <h4 class="self-end m-1" style="color: inherit">
                    {{ String(item.unit || '').replace(/\s/g, '') }}
                  </h4> -->
                <!-- </span> -->
              </ColorCodedCell>
            </div>
          </div>
        </MCol>
        <MCol :size="5" class="text-ellipsis text-xs pl-3" :title="item.key">
          <ColorCodedCell
            :data-item="item.row"
            :column="keyColumnContext"
            ignore-background
            is-preview
          >
            {{ item.key }}
          </ColorCodedCell>
        </MCol>
      </MRow>
    </div>
    <FlotoNoData
      v-else
      hide-svg
      header-tag="h5"
      icon="exclamation-triangle"
      variant="neutral"
    />
  </div>
</template>

<script>
import GroupBy from 'lodash/groupBy'
import Pick from 'lodash/pick'
import CloneDeep from 'lodash/cloneDeep'
import { gridWorker } from '@/src/workers'
import Throttle from 'lodash/throttle'
import SortBy from 'lodash/sortBy'
import ColorCodedCell from './color-coded-cell.vue'
import Reverse from 'lodash/reverse'

export default {
  name: 'VerticalValueGrid',
  components: {
    ColorCodedCell,
  },
  inheritAttrs: false,

  props: {
    isPreview: { type: Boolean, default: false },
    widget: {
      type: Object,
      required: true,
    },
    data: {
      type: [Array, Object],
      default() {
        return []
      },
    },
  },
  data() {
    this.ignoreRowNames = ['object.type', 'object.vendor', 'object.ip']
    return {
      columns: [],
      dataMap: [],
      searchTerm: undefined,
      processData: [],
    }
  },
  computed: {
    slicedData() {
      return (this.dataMap || []).slice(0, 100)
    },
    groupByColumns() {
      return this.data.groupByColumns || []
    },
    valueColumn() {
      if (this.widget && (this.widget.groups || []).length === 1) {
        const group = this.widget.groups[0]
        if ((group.counters || []).length === 1) {
          const counter = group.counters[0].counter || {}
          if (counter.key) {
            return `${counter.key}.${group.counters[0].aggrigateFn}`
              .replace(/~/g, '.')
              .replace(/\./g, '_')
          }
        }
      }
      return null
    },
    valueColumnContext() {
      return (
        (this.columns.filter((col) => col.key === this.valueColumn || '') ||
          [])[0] || { rawColumnContext: {} }
      )
    },
    keyColumnContext() {
      return (
        (this.columns.filter(
          (col) =>
            col.key !== this.valueColumn &&
            !this.ignoreRowNames.includes(col.rawName)
        ) || [])[0] || { rawColumnContext: {} }
      )
    },
    currentFilters() {
      return {
        logic: 'and',
        filters: this.searchTerm
          ? [
              {
                logic: 'or',
                filters: [
                  {
                    field: this.valueColumn,
                    operator: 'contains',
                    value: this.searchTerm,
                  },
                  {
                    field: this.keyColumnContext.rawName,
                    operator: 'contains',
                    value: this.searchTerm,
                  },
                ],
              },
            ]
          : [],
      }
    },
    showVerticalValueGrid() {
      return (
        this.valueColumn &&
        this?.data?.rows?.length > 0 &&
        this.keyColumnContext
      )
    },
  },
  watch: {
    data: {
      handler(newValue, oldValue) {
        if (newValue !== oldValue) {
          this.buildData(newValue)
        }
      },
      immediate: true,
    },
    searchTerm(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.setItems()
      }
    },
    'widget.widgetProperties.layoutSorting'(newValue, oldValue) {
      if (newValue !== oldValue) {
        const dataMap = Reverse(CloneDeep(this.dataMap))
        // const reversedKeys = Object.keys(dataMap)?.reverse() || []
        // const reverseDataMap = {}
        // reversedKeys?.forEach((key) => {
        //   if (key) {
        //     reverseDataMap[key] = CloneDeep(dataMap[key])
        //   }
        // })

        // console.log(reverseDataMap)
        this.dataMap = dataMap
      }
    },
  },
  created() {
    if (this.isPreview) {
      this.$watch(
        'widget.widgetProperties.columnSettings',
        async (newValue, oldValue) => {
          if (newValue !== oldValue) {
            const columns = await gridWorker.buildWidgetGridColumns(this.widget)
            this.columns = CloneDeep(columns)
            if (this.data.rows && this.data.rows.length) {
              const data = await gridWorker.reEvaluateColorConfigForGridColumns(
                this.data.rows,
                this.widget
              )
              this.buildData({
                rows: data,
                columns,
                groupByColumns: this.data.groupByColumns,
                responseColumns: this.data.responseColumns,
              })
            }
          }
        }
      )
      this.$watch(
        'widget.widgetProperties.headerStyle',
        async (newValue, oldValue) => {
          const columns = await gridWorker.buildWidgetGridColumns(this.widget)
          this.columns = CloneDeep(columns)
        }
      )
    }
    this.setItems = Throttle(this.setItemsRaw, this.debounceTime, {
      trailing: true,
    })
  },

  methods: {
    buildData(newValue) {
      this.columns = newValue.columns
      let sortedRow = SortBy(
        newValue?.rows || [],
        Object.keys(newValue?.rows?.[0] || {}).includes(
          `${this.valueColumn}_sort`
        )
          ? `${this.valueColumn}_sort`
          : this.valueColumn
      ).map((row) => ({
        id: row.id,
        key: row[
          ((newValue.groupByColumns || [])[0] || '').replace(/[~^\.]/g, '_')
        ],
      }))

      sortedRow = Reverse(sortedRow)

      if (this.widget?.widgetProperties?.layoutSorting === 'asc') {
        // sortedRow.reverse()

        sortedRow = Reverse(sortedRow)
      }
      const groupedResult = GroupBy(
        newValue.rows,
        ((newValue.groupByColumns || [])[0] || '').replace(/[~^\.]/g, '_')
      )

      let rowMap = []

      for (const item of sortedRow) {
        let shouldSplitCounter = !/(ms|time|duration|sec|seconds)/.test(
          this.valueColumn
        )

        const currentItemGroupedResult = groupedResult[item.key].find(
          (r) => r.id === item.id
        )
        let coulmn = currentItemGroupedResult[this.valueColumn]
        if (coulmn !== undefined && coulmn !== null) {
          coulmn = coulmn.toString()
        }
        rowMap = rowMap.concat([
          {
            ...Pick(currentItemGroupedResult, [this.valueColumn]),
            row: currentItemGroupedResult,
            valueColValue: coulmn || '',
            value: shouldSplitCounter
              ? `${
                  (coulmn || '').toString().replace(/[^0-9.]/g, '') || ''
                } <h4 class="self-end m-1" style="color: inherit">
                    ${(coulmn || '').toString().replace(/[0-9.]+/gi, '') || ''}
                  </h4>`
              : `${
                  (coulmn || '')
                    .toString()
                    .split('')
                    .map((char) =>
                      isNaN(char)
                        ? `<h4  class="self-end m-1" style="color: inherit">${char}</h4>`
                        : char
                    )
                    .join('') || ''
                }`,

            key: item.key,
          },
        ])
      }

      this.dataMap = Object.freeze(rowMap)

      // Object.freeze(
      //   sortedRow.reduce(
      //     (prev, item) => {
      //       let shouldSplitCounter = !/(ms|time|duration|sec|seconds)/.test(
      //         this.valueColumn
      //       )

      //       const currentItemGroupedResult = groupedResult[item.key].find(
      //         (r) => r.id === item.id
      //       )
      //       let coulmn = currentItemGroupedResult[this.valueColumn]

      //       return [
      //         ...prev,
      //         {
      //           ...Pick(currentItemGroupedResult, [this.valueColumn]),
      //           row: currentItemGroupedResult,
      //           valueColValue: coulmn || '',
      //           value: shouldSplitCounter
      //             ? `${
      //                 (coulmn || '').toString().replace(/[^0-9.]/g, '') || ''
      //               } <h4 class="self-end m-1" style="color: inherit">
      //               ${(coulmn || '').toString().replace(/[0-9.]+/gi, '') || ''}
      //             </h4>`
      //             : `${
      //                 (coulmn || '')
      //                   .toString()
      //                   .split('')
      //                   .map((char) =>
      //                     isNaN(char)
      //                       ? `<h4  class="self-end m-1" style="color: inherit">${char}</h4>`
      //                       : char
      //                   )
      //                   .join('') || ''
      //               }`,

      //           key: item.key,
      //         },
      //       ]
      //     },

      //     []
      //   )
      // )
      this.$emit('column-received', newValue.responseColumns)
      this.$emit('group-column-received', newValue.groupByColumns)

      if ((this.dataMap || []).length === 0 && this.widget.hideEmptyWidget) {
        this.$emit('hide')
      }
    },
    async setItemsRaw() {
      const processedData = await this.getFilteredData()
      this.buildData({
        ...this.data,
        rows: processedData.data || [],
      })
    },
    async getFilteredData() {
      const currentFilters = this.currentFilters
      let processedData = await gridWorker.processList(this.data.rows, {
        skip: 0,
        ...(currentFilters ? { filter: currentFilters } : {}),
      })
      return processedData
    },
  },
}
</script>
