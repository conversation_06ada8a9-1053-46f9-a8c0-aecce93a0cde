<template>
  <SelectedItemPills :value="tags" :wrap="false" :use-popover="true" />
</template>

<script>
import SelectedItemPills from '@components/dropdown-trigger/selected-item-pills.vue'

export default {
  name: 'Tags',
  components: {
    SelectedItemPills,
  },
  inheritAttrs: false,
  props: {
    value: {
      type: [String, Array],
      default: undefined,
    },
  },
  computed: {
    tags() {
      if (!this.value) {
        return []
      }
      if (typeof this.value === 'string') {
        return JSON.parse(this.value)
      } else if (Array.isArray(this.value)) {
        return this.value
      }
      return []
    },
  },
}
</script>
