<template>
  <ChartView
    v-bind="$attrs"
    :data="chartData"
    enable-legend
    is-preview
    availability-colors
    :widget="computedWidget"
    @force-drill-down="$emit('force-drill-down', $event)"
  />
</template>

<script>
import Uniq from 'lodash/uniq'
import GroupBy from 'lodash/groupBy'
import { WidgetTypeConstants } from '@components/widgets/constants'
import ChartView from '../chart-view.vue'

export default {
  name: 'AlertStackedVerticalBar',
  components: {
    ChartView,
  },
  inheritAttrs: false,
  props: {
    widget: {
      type: Object,
      required: true,
    },
    data: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  computed: {
    computedWidget() {
      return {
        ...this.widget,
        widgetType: WidgetTypeConstants.STACKED_VERTICAL_BAR,
      }
    },
    chartData() {
      const groups = GroupBy(this.data.rows || [], 'object_type')
      const severities = Uniq((this.data.rows || []).map((i) => i.severity))
      return {
        series: severities.map((severity) => {
          return {
            counter: severity.toUpperCase(),
            data: Object.keys(groups).map(
              (policyType) => {
                let countSum = 0

                for (const item of groups[policyType]) {
                  if (item.severity === severity.toUpperCase()) {
                    countSum += item.count || item['severity.count'] || 0
                  }
                }
                return countSum
              }

              // groups[policyType].reduce(
              //     (prev, item) =>
              //       item.severity === severity.toUpperCase()
              //         ? prev + (item.count || item['severity.count'] || 0)
              //         : prev,
              //     0
              //   )
              //
            ),
            stack: 'severity',
            aggr: 'count',
            name: severity.toUpperCase(),
          }
        }),
        groupByColumns: ['policy.type', 'severity'],
        categories: Object.keys(groups),
        columns: [],
      }
    },
  },
}
</script>
