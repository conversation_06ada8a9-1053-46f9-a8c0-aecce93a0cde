<template>
  <MRow :gutter="0" class="px-2 bg-neutral-lightest mx-2 rounded pt-1">
    <MCol
      v-for="(column, index) in columns"
      :key="column.key"
      :size="6"
      :class="{
        'border-right': index % 2 === 0,
        'pr-2': index % 2 === 0,
        'pl-2': index % 2 !== 0,
      }"
    >
      <div class="flex justify-between w-full">
        <h6 class="text-ellipsis flex-1 min-w-0">
          {{ column.name }}
        </h6>
        <h5 style="flex-shrink: 0" class="ml-2">
          {{ row[column.key] }}
        </h5>
      </div>
    </MCol>
  </MRow>
</template>

<script>
export default {
  name: 'BillingView',
  inheritAttrs: false,
  props: {
    widget: {
      type: Object,
      required: true,
    },
    data: {
      type: Object,
      required: true,
    },
  },
  computed: {
    columns() {
      return this.data.columns.filter((c) => c.hidden === false)
    },
    row() {
      return this.data.rows[0] || {}
    },
    columnConfig() {
      const map = {}
      this.columns.forEach((c) => {
        map[c.key] = c
      })
      return map
    },
  },
  created() {
    if (
      ((this.data || {}).rows || []).length === 0 &&
      this.widget.hideEmptyWidget
    ) {
      this.$emit('hide')
    }
  },
}
</script>
