<template>
  <div
    class="rounded-xl flex justify-between my-2 p-2 self-end relative switch-view"
    :style="{ background: switchColor }"
  >
    <div class="flex justify-center">
      <div
        class="h-full p-2 rounded-lg flex mr-4"
        :style="{ background: portbackgroundColor }"
      >
        <div
          class="rounded-lg mr-2 h-full border-2 border-white border-solid relative flex port"
        >
          <div class="line line-up"></div>
        </div>
        <div
          class="rounded-lg h-full border-4 border-white border-solid relative flex port"
        >
          <div class="line line-down"></div>
        </div>
      </div>
      <h5 class="mb-0 self-center">{{ switchName }}</h5>
    </div>
    <div class="flex flex-col justify-center">
      <!-- <div>icon</div>
      <h6 class="mb-0 text-xs">Cisco</h6> -->
      <MIcon name="cisco" size="2x"></MIcon>
    </div>
    <div class="horizontal-line"></div>
  </div>
</template>

<script>
export default {
  name: 'StackedSwitch',
  props: {
    status: {
      type: String,
      default: 'disable',
    },

    switchName: {
      type: String,
      required: true,
    },
  },
  computed: {
    switchColor() {
      const status = this.status.toLowerCase()
      if (status === 'up') {
        return `var(--secondary-green)`
      } else if (status === 'Disable') {
        return `var(--severity-disable-lightest)`
      } else {
        return `var(--secondary-red)`
      }
    },
    portbackgroundColor() {
      const status = this.status.toLowerCase()
      if (status === 'up') {
        return `var(--status-up)`
      } else if (status === 'disable') {
        return `var(--button-disabled-bg)`
      } else {
        return `var(--status-down)`
      }
    },
  },
}
</script>

<style lang="less" scoped>
.switch-view {
  width: 100%;
  min-width: 250px;

  .port {
    width: 20px;
  }

  .line {
    position: absolute;
    right: 50%;
    width: 2px;
    margin: auto;
    background-color: var(--nav-text-color);

    &-up {
      top: 100%;
      height: 25px;
    }

    &-down {
      bottom: 100%;
      height: 30px;
    }
  }

  .horizontal-line {
    position: absolute;
    top: -10px;
    left: 25px;
    width: 29px;
    border-top: 2px solid var(--nav-text-color);
  }
}
</style>
