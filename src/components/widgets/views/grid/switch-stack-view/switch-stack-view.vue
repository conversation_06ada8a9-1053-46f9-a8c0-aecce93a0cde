<template>
  <div
    v-if="switchesData.length"
    class="flex-1 min-h-0 w-full flex justify-center items-center"
  >
    <div class="switches-container w-2/6 p-6 h-full">
      <div class="w-full h-full overflow-auto">
        <div class="switch-box h-auto w-full pl-6 flex flex-col relative">
          <StackedSwitch
            v-for="(s, index) in switchesData"
            :key="index"
            :status="s.status"
            :switch-name="s.name"
          />
          <div class="absolute line line-top"></div>
          <div class="absolute line line-bottom"></div>
        </div>
      </div>
    </div>
    <div class="switches-details w-4/6 p-6 h-full flex flex-col justify-center">
      <div class="p-6 bg-neutral-lightest h-full">
        <MRow :gutter="0"> <h4 class="text-primary">Stack Details</h4></MRow>
        <MRow class="text-sm">
          <MCol :size="4" class="text-neutral-light">Bandwidth</MCol>
          <MCol :size="8"> {{ bandWidth }} </MCol>
        </MRow>
        <MRow class="text-sm">
          <MCol :size="4" class="text-neutral-light">Master Switch</MCol>
          <MCol :size="8"> {{ masterSwitch.name }}</MCol>
        </MRow>
        <MRow class="text-sm">
          <MCol :size="4" class="text-neutral-light"
            >Switch In Problem / Down</MCol
          >
          <MCol :size="8">{{ downSwitchesCounts }}</MCol>
        </MRow>
      </div>
    </div>
  </div>
  <FlotoNoData
    v-else
    hide-svg
    header-tag="h5"
    variant="neutral"
    icon="exclamation-triangle"
  />
</template>

<script>
import StackedSwitch from './stacked-switch.vue'
export default {
  name: 'SwitchStackView',

  components: {
    StackedSwitch,
  },
  inheritAttrs: false,
  props: {
    data: {
      type: [Array, Object],
      default() {
        return []
      },
    },
  },
  computed: {
    switchesData() {
      return ((this.data.stackedSwiitch || {}).rows || this.data.rows || [])
        .map((e) => {
          return {
            name: e['cisco_stack_switch'],
            role: e['cisco_stack_switch_role_last'],
            status: e['cisco_stack_switch_port_status_last'],
          }
        })
        .filter((s) => s.name)
    },
    bandWidth() {
      return (((this.data.stackedSwiitch || {}).rows ||
        this.data.rows ||
        [])[0] || {})['bandwidth']
    },
    masterSwitch() {
      return (
        this.switchesData.filter((s) => s.role.toLowerCase() === 'master')[0] ||
        {}
      )
    },
    downSwitchesCounts() {
      return this.switchesData.filter((s) => s.status.toLowerCase() === 'down')
        .length
    },
  },
}
</script>

<style lang="less" scoped>
.switches-container {
  min-width: 445px;
}

.switch-box {
  border-left: 2px solid var(--nav-text-color);
}

.line {
  border-top: 2px solid var(--nav-text-color);

  &-top {
    top: 0;
    left: 0;
    width: calc(1.5rem + 0.5rem + 0.5rem + 1.2rem + 0.5rem + 0.625rem);
  }

  &-bottom {
    bottom: 0;
    left: 0;
    width: calc(1.5rem + 0.5rem + 0.5rem + 0.625rem);
  }
}
</style>
