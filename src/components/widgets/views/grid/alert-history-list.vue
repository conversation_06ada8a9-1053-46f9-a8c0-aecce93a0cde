<template>
  <div
    v-if="historyData.length"
    class="flex-1 min-h-0 h-full w-full flex flex-col justify-center items-center"
  >
    <h4 class="text-primary-alt mb-3">History</h4>
    <Timeline :items="historyData" />
  </div>
  <FlotoNoData
    v-else
    hide-svg
    header-tag="h5"
    variant="neutral"
    icon="exclamation-triangle"
  />
</template>

<script>
import Timeline from '@components/timeline.vue'

import { transformAlertHistoryDataForClient } from '@modules/alert/helpers/alert-helper'
export default {
  name: 'AlertHistoryList',
  components: {
    Timeline,
  },
  inheritAttrs: false,
  props: {
    data: {
      type: [Array, Object],
      default() {
        return []
      },
    },
  },
  computed: {
    historyData() {
      return ((this.data || {}).rows || []).map((e) =>
        transformAlertHistoryDataForClient(e)
      )
    },
  },
}
</script>
