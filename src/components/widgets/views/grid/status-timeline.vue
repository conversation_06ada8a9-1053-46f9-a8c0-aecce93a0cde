<template>
  <div class="flex flex-col p-2 justify-between h-full">
    <TimelineChartOptions :data="data.flaps || []">
      <template v-slot="{ options }">
        <Chart :options="options" />
      </template>
    </TimelineChartOptions>
  </div>
</template>

<script>
import Chart from '@components/chart/chart.vue'
import TimelineChartOptions from '@components/chart/options/timeline-chart-options.vue'

export default {
  name: 'StatusTimeline',
  components: {
    TimelineChartOptions,
    Chart,
  },
  inheritAttrs: false,
  props: {
    data: {
      type: [Object, Array],
      default() {
        return {}
      },
    },
    isPreview: { type: Boolean, default: false },
    widget: {
      type: Object,
      required: true,
    },
  },
}
</script>
