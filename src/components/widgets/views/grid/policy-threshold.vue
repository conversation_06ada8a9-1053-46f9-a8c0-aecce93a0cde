<template>
  <span v-if="!threshold" />
  <span v-else>
    <span
      v-for="(severity, index) in threshold"
      :key="severity.key"
      :class="{ text: true, [severity.key]: true }"
    >
      <b>
        {{ severity.value }}
      </b>
      <span
        v-if="index < (threshold || []).length - 1"
        class="mx-1 text-neutral-light"
      >
        |
      </span>
    </span>
  </span>
</template>

<script>
import Constants from '@constants'

export default {
  name: 'PolicyThreshold',
  inheritAttrs: false,
  props: {
    value: {
      type: [String, Array],
      default: undefined,
    },
  },
  computed: {
    threshold() {
      if (!this.value) {
        return undefined
      }
      try {
        const parsed = JSON.parse(this.value)
        if (parsed && parsed['policy.severity']) {
          if (parsed['policy.severity']) {
            return [
              Constants.CRITICAL,
              Constants.MAJOR,
              Constants.WARNING,
              // Constants.CLEAR,
            ].map((key) => ({
              key: key.toLowerCase(),
              value:
                (parsed['policy.severity'][key] || {})['policy.threshold'] || 0,
            }))
          }
        }
      } catch (e) {
        return undefined
      }
      return undefined
    },
  },
}
</script>
