<template>
  <ChartOptions :data="series" unit="dBm" :min="-120" :max="0">
    <template v-slot="{ options }">
      <Chart :options="options" />
    </template>
  </ChartOptions>
</template>

<script>
import Chart from '@components/chart/chart.vue'
import ChartOptions from '@components/chart/options/radar-chart-options.vue'

export default {
  name: 'WirelessSignalStrength',
  components: {
    Chart,
    ChartOptions,
  },
  inheritAttrs: false,
  props: {
    widget: {
      type: Object,
      required: true,
    },
    data: {
      type: Object,
      default() {
        return {}
      },
    },
    monitor: {
      type: Object,
      default: undefined,
    },
  },
  computed: {
    series() {
      const points = (this.data.rows || []).filter(
        (i) => i['wireless_client_signal_strength_dbm_last_sort'] !== null
      )
      return {
        color: 'var(--secondary-red-light)',
        data: points.map((i) =>
          parseFloat(
            i['wireless_client_signal_strength_dbm_last_sort'] > 0
              ? i['wireless_client_signal_strength_dbm_last_sort'] * -1
              : i['wireless_client_signal_strength_dbm_last_sort']
          )
        ),
        clients: points.map((i) => i['wireless_client']),
      }
    },
  },
}
</script>
