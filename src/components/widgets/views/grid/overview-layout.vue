<template>
  <MRow
    v-if="visibleColumns.length && Object.keys(row || {}).length"
    :gutter="0"
  >
    <MCol
      v-for="c in visibleColumns"
      :key="c.key"
      class="my-2"
      :size="12 / columnCounts"
    >
      <MRow :gutter="0">
        <MCol :size="6" class="text-neutral px-2">{{ c.name }}</MCol>
        <MCol :size="6" class="flex items-center">
          <ColorCodedCell :is-preview="isPreview" :data-item="row" :column="c">
            <Status
              v-if="c.cellRender === 'status'"
              :column="c"
              :value="row[c.key]"
            />
            <template v-else>
              <div
                v-if="c.key === 'system_process'"
                class="text-ellipsis"
                style="color: inherit"
              >
                {{ row[c.key] }}
                <MTooltip>
                  {{ row[c.key] }}
                </MTooltip>
              </div>
              <template v-else>
                {{ row[c.key] }}
              </template>
            </template>
          </ColorCodedCell>
        </MCol>
      </MRow>
    </MCol>
  </MRow>
  <FlotoNoData
    v-else
    hide-svg
    header-tag="h5"
    icon="exclamation-triangle"
    variant="neutral"
  />
</template>

<script>
import SortBy from 'lodash/sortBy'
import CloneDeep from 'lodash/cloneDeep'
import { gridWorker } from '@/src/workers'
import Status from '../grid/status.vue'
import ColorCodedCell from '../grid/color-coded-cell.vue'

export default {
  name: 'OverviewLayout',
  components: {
    Status,
    ColorCodedCell,
  },
  inheritAttrs: false,
  props: {
    isPreview: { type: Boolean, default: false },
    widget: {
      type: Object,
      required: true,
    },
    data: {
      type: [Array, Object],
      default() {
        return []
      },
    },
  },
  data() {
    this.statusColumnProps = {
      hideTitle: true,
    }
    return {
      row: {},
      columns: [],
    }
  },
  computed: {
    visibleColumns() {
      return SortBy(
        (this.columns || []).filter((c) => c.hidden !== true),
        'orderIndex'
      )
    },
    columnCounts() {
      let count = 1
      if (this.widget.widgetProperties.layout === '2-columns') {
        count = 2
      }
      return count
    },
  },
  watch: {
    data: {
      handler(newValue, oldValue) {
        if (newValue !== oldValue) {
          this.columns = newValue.columns
          this.row = Object.freeze(newValue.rows[0])
          this.$emit(
            'column-received',
            (newValue.responseColumns || []).map((c) => c)
          )
          this.$emit('group-column-received', newValue.groupByColumns)
        }
      },
      immediate: true,
    },
  },
  mounted() {
    if (this.isPreview) {
      this.$watch(
        'widget.widgetProperties.columnSettings',
        async (newValue, oldValue) => {
          const columns = await gridWorker.buildWidgetGridColumns(this.widget)
          this.columns = CloneDeep(columns)
          const data = await gridWorker.reEvaluateColorConfigForGridColumns(
            [this.data],
            this.widget
          )
          this.row = Object.freeze(data[0])
        }
      )
      this.$watch(
        'widget.widgetProperties.headerStyle',
        async (newValue, oldValue) => {
          const columns = await gridWorker.buildWidgetGridColumns(this.widget)
          this.columns = CloneDeep(columns)
        }
      )
    }
  },
}
</script>
