<template>
  <SparklineOptions
    v-if="series.length && !isPreview"
    v-once
    :key="color"
    :data="sparklineSeries"
    :min-value="minYValue"
    :color="color"
    :type="calculatedType"
    :timezone="timezone"
    :date-format="dateFormat"
  >
    <template v-slot="{ options }">
      <Chart :options="options" />
    </template>
  </SparklineOptions>
  <SparklineOptions
    v-else-if="series.length && isPreview"
    :key="color"
    :data="sparklineSeries"
    :min-value="minYValue"
    :color="color"
    :type="calculatedType"
    :timezone="timezone"
    :date-format="dateFormat"
  >
    <template v-slot="{ options }">
      <Chart :options="options" />
    </template>
  </SparklineOptions>
  <span v-else />
</template>

<script>
import Moment from 'moment'
import 'moment-timezone'
import Min from 'lodash/min'
import SparklineOptions from '@components/chart/options/sparkline-options.vue'
import Chart from '@components/chart/chart.vue'
import { UserPreferenceComputed } from '@state/modules/user-preference'
import { isUnitConvertible } from '@utils/unit-checker'
import { getTrimmedNullValueIndexes } from '@utils/arr'
import applyUnit from '@utils/unit-applier'

export default {
  name: 'Sparkline',
  components: { Chart, SparklineOptions },
  props: {
    type: { type: String, default: 'line' },
    column: {
      type: Object,
      default() {
        return {}
      },
    },
    color: {
      type: String,
      default: undefined,
    },
    series: {
      type: [Array],
      default() {
        return []
      },
    },
    isPreview: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    ...UserPreferenceComputed,
    seriesValues() {
      return (this.series || []).map((i) => i[1])
    },
    sparklineSeries() {
      const { start, end } = getTrimmedNullValueIndexes(this.seriesValues)
      const counter = this.column.serverName
      const offset = Moment().tz(this.timezone).utcOffset() * 60 * 1000
      return [
        {
          name: this.column.name,
          counterRawName: this.column?.rawName?.replace(/.sparkline/g, ''),
          data: this.series
            .slice(start, end)
            .map((item) => [item[0] + offset, item[1]]),
          ...(isUnitConvertible(counter)
            ? {
                formattedValues: this.seriesValues
                  .slice(start, end)
                  .map((value) => applyUnit(counter, value)),
              }
            : {}),
        },
      ]
    },
    calculatedType() {
      if (this.type === 'sparkarea') {
        return 'areaspline'
      }
      if (this.type === 'sparkbar') {
        return 'column'
      }
      return 'spline'
    },
    minYValue() {
      return Min(this.seriesValues)
    },
  },
}
</script>
