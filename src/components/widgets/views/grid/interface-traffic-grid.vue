<template>
  <VirtualTable
    v-if="rows.length"
    class="dashboard-widget-grid w-full min-w-0"
    :data="rows"
    :columns="columns"
  />
  <FlotoNoData
    v-else
    hide-svg
    header-tag="h5"
    icon="exclamation-triangle"
    variant="neutral"
  />
</template>

<script>
import NumberFormat from '@src/filters/number-format'
import VirtualTable from '@components/crud/virtual-table.vue'

const ROWS = [
  {
    title: 'Interface Bandwidth',
    in: 'interface.in.traffic.bytes.per.sec.avg',
    out: 'interface.out.traffic.bytes.per.sec.avg',
  },
  // {
  //   title: 'Current Traffic',
  //   in: 'interface.in.traffic.bytes.per.sec.last',
  //   out: 'interface.out.traffic.bytes.per.sec.last',
  // },
  {
    title: '% Utilization',
    in: 'interface.in.traffic.utilization.percent.avg',
    out: 'interface.out.traffic.utilization.percent.avg',
  },
  {
    title: 'Packet Count',
    in: 'interface.in.packets.avg',
    out: 'interface.out.packets.avg',
  },
]

export default {
  name: 'InterfaceTrafficGrid',
  components: {
    VirtualTable,
  },
  inheritAttrs: false,
  props: {
    widget: {
      type: Object,
      required: true,
    },
    data: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      rows: [],
      columns: [
        {
          key: 'name',
          name: ' ',
        },
        {
          key: 'in',
          name: 'IN Traffic',
          sortKey: 'in_sort',
        },
        {
          key: 'out',
          name: 'OUT Traffic',
          sortKey: 'out_sort',
        },
      ],
    }
  },
  created() {
    this.buildRows()
  },
  methods: {
    buildRows() {
      const resultRow = this.data.rows[0]
      if (resultRow) {
        const rows = ROWS.map((row) => {
          const inKey = row.in.replace(/\./g, '_')
          const outKey = row.out.replace(/\./g, '_')
          return {
            name: row.title,
            in: resultRow[`${inKey}_sort`]
              ? resultRow[inKey]
              : NumberFormat(resultRow[inKey]),
            in_sort: resultRow[`${inKey}_sort`] || resultRow[inKey],
            out: resultRow[`${outKey}_sort`]
              ? resultRow[outKey]
              : NumberFormat(resultRow[outKey]),
            out_sort: resultRow[`${outKey}_sort`] || resultRow[outKey],
          }
        })
        this.rows = Object.freeze(rows)
      }
      if (
        ((this.data || {}).rows || []).length === 0 &&
        this.widget.hideEmptyWidget
      ) {
        this.$emit('hide')
      }
    },
  },
}
</script>
