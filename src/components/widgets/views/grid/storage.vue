<template>
  <div class="flex flex-col flex-1 min-w-0">
    <div
      v-for="partition in partitions"
      :key="partition.drive"
      class="px-2 py-1"
    >
      <div class="py-2 flex min-w-0 items-center">
        <div class="icon partition-icon rounded-lg p-2">
          <MIcon name="disk" size="lg" />
        </div>
        <div class="size flex flex-col flex-1 ml-4 min-w-0">
          <div class="flex justify-between min-w-0">
            <span class="text-ellipsis font-500">{{ partition.drive }}</span>
            <span v-if="partition.mounted" class="ml-4 font-500">
              Mounted on: {{ partition.mounted }}
            </span>

            <span v-if="partition.free && !isLinux" class="ml-4 font-500">
              Free: {{ partition.free }}
            </span>
          </div>
          <div class="progress-bar">
            <div
              class="ant-progress ant-progress-line ant-progress-status-primary ant-progress-large"
            >
              <div>
                <div class="ant-progress-outer">
                  <div class="ant-progress-inner">
                    <div
                      class="ant-progress-bg"
                      :style="{
                        width: `${partition.progress}%`,
                        height: '8px',
                        'border-radius': '100px',
                      }"
                    >
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="flex justify-between min-w-0">
            <span v-if="partition.used" class="font-500"
              >Used: {{ partition.used }}</span
            >
            <span v-if="partition.total && isLinux" class="ml-4 font-500">
              {{ partition.free }} free of {{ partition.total }}
            </span>
            <span v-if="partition.total && !isLinux" class="ml-4 font-500">
              Total: {{ partition.total }}
            </span>
          </div>
        </div>
      </div>
    </div>
    <FlotoNoData
      v-if="!partitions.length"
      hide-svg
      header-tag="h5"
      variant="neutral"
      icon="exclamation-triangle"
    />
  </div>
</template>

<script>
import SortBy from 'lodash/sortBy'

const countersMap = {
  drive: 'system_disk_volume',
  free: 'system_disk_volume_free_bytes_last',
  used: 'system_disk_volume_used_bytes_last',
  total: 'system_disk_volume_capacity_bytes_last',
  mounted: 'system_disk_volume_mount_path_last',
}

export default {
  name: 'Storage',
  inheritAttrs: false,
  props: {
    widget: {
      type: Object,
      required: true,
    },
    data: {
      type: Object,
      default() {
        return {}
      },
    },
    monitor: {
      type: Object,
      required: true,
    },
  },
  computed: {
    partitions() {
      const row = (this.data.rows || [])[0]
      if (!row) {
        return []
      }
      return SortBy(
        (this.data.rows || []).map((item) => ({
          drive: item[countersMap.drive],
          free: item[countersMap.free],
          used: item[countersMap.used],
          total: item[countersMap.total],
          mounted: item[countersMap.mounted],
          progress:
            (item[`${countersMap.used}_sort`] * 100) /
            item[`${countersMap.total}_sort`],
        })),
        'used'
      ).reverse()
    },
    isLinux() {
      return [
        this.$constants.LINUX,
        this.$constants.IBM_AIX,
        this.$constants.HP_UX,
        this.$constants.SOLARIS,
      ].includes(this.monitor.type)
    },
  },
  created() {
    if (
      ((this.data || {}).rows || []).length === 0 &&
      this.widget.hideEmptyWidget
    ) {
      this.$emit('hide')
    }
  },
}
</script>

<style lang="less" scoped>
.partition-icon {
  color: var(--partition-icon-text-color);
  background: var(--partition-icon-background-color);
}
</style>
