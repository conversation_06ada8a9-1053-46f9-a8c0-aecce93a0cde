<template>
  <MGrid
    v-if="data.length"
    :columns="columns"
    :paging="false"
    :default-group="groupColumn"
    class="hide-grouping hide-header"
    hide-selection-info
    :data="calculatedData"
    :default-page-size="data.length"
    expandable
  >
    <template v-slot:state="{ item }">
      <div class="text-ellipsis">
        <!-- <MIcon
          :name="item.state === 'Normal' ? 'check-circle' : 'times-circle'"
          class="ml-1"
          :class="
            item.state === 'Normal'
              ? 'text-secondary-green'
              : 'text-secondary-red'
          "
          size="lg"
        /> -->

        <MStatusTag :status="item.state" />
      </div>
    </template>
    <template v-slot:value="{ item }">
      {{ item.value }}&nbsp;{{ item.unit }}
    </template>
  </MGrid>
  <FlotoNoData
    v-else
    hide-svg
    header-tag="h5"
    icon="exclamation-triangle"
    variant="neutral"
  />
</template>

<script>
import UniqBy from 'lodash/uniqBy'

const NameMap = {
  'fan.sensor': 'Fan',
  'power.supply.sensor': 'Power Supply',
  'temperature.sensor': 'Temperature',
  'voltage.sensor': 'Voltage',
  'fortinet.hardware.sensor': 'Fortinate Hardware Sensor',
  'cisco.vedge.hardware.temperature.sensor': 'Temperature',
  'cisco.vedge.hardware.voltage.sensor': 'Voltage',
  'cisco.vedge.hardware.power.sensor': 'Power',
  'cisco.vedge.hardware.fan.sensor': 'Fan',
  'cisco.vedge.hardware.current.sensor': 'Current',
}

export default {
  name: 'HardwareSensor',
  inheritAttrs: false,
  props: {
    data: {
      type: [Object, Array],
      default() {
        return {}
      },
    },
    widget: {
      type: Object,
      required: true,
    },
  },
  computed: {
    calculatedData() {
      const dataMap = this.data
        .map((i) => {
          const row = this.buildRow(i.instance_type, i)
          const instanceType = NameMap[i.instance_type] || i.instance_type
          if (!row) {
            return null
          }
          return {
            ...i,
            ...row,
            instance_type: instanceType,
            uniqueKey: `${instanceType}-${i.instance}-${row.state}-${i.monitor}`,
          }
        })
        .filter(Boolean)

      return UniqBy((dataMap || []).reverse(), 'uniqueKey')
    },
    groupColumn() {
      return [{ field: 'instance_type' }]
    },
    columns() {
      return [
        {
          key: 'instance',
          name: 'Sensor',
          groupable: false,
          className: 'font-500',
        },
        {
          key: 'state',
          name: 'Status',
          groupable: false,
          className: 'font-500',
          width: '150px',
        },
        {
          key: 'value',
          name: 'Value',
          groupable: false,
          className: 'font-500',
          width: '100px',
        },
      ]
    },
  },
  methods: {
    buildRow(instanceType, row) {
      if (instanceType === 'temperature.sensor') {
        if (!row['temperature.sensor.status.last']) {
          return null
        }
        return {
          state: row['temperature.sensor.status.last'],
          value: row['temperature.sensor.reading.celsius.last'],
          unit: '°C',
        }
      } else if (['fan.sensor', 'power.supply.sensor'].includes(instanceType)) {
        if (!row[`${instanceType}.status.last`]) {
          return null
        }
        return {
          state: row[`${instanceType}.status.last`],
        }
      } else if (instanceType === 'voltage.sensor') {
        if (!row['voltage.sensor.status.last']) {
          return null
        }
        return {
          state: row['voltage.sensor.status.last'],
          value: row['voltage.sensor.reading.mill.volts.last'],
          unit: 'mV',
        }
      } else if (instanceType === 'fortinet.hardware.sensor') {
        return {
          value: row['fortinet.hardware.sensor.value.last'],
        }
      } else if (instanceType === 'cisco.vedge.hardware.temperature.sensor') {
        if (!row['cisco.vedge.hardware.temperature.sensor.state.last']) {
          return null
        }
        return {
          state: row['cisco.vedge.hardware.temperature.sensor.state.last'],
          value: row['cisco.vedge.hardware.temperature.sensor.celsius.last'],
          unit: '°C',
        }
      } else if (instanceType === 'cisco.vedge.hardware.voltage.sensor') {
        if (!row['cisco.vedge.hardware.voltage.sensor.state.last']) {
          return null
        }
        return {
          state: row['cisco.vedge.hardware.voltage.sensor.state.last'],
          value: row['cisco.vedge.hardware.voltage.sensor.milli.volts.last'],
          unit: 'mV',
        }
      } else if (instanceType === 'cisco.vedge.hardware.power.sensor') {
        if (!row['cisco.vedge.hardware.power.sensor.state.last']) {
          return null
        }
        return {
          state: row['cisco.vedge.hardware.power.sensor.state.last'],
          value: row['cisco.vedge.hardware.power.sensor.watts.last'],
          unit: 'watts',
        }
      } else if (instanceType === 'cisco.vedge.hardware.fan.sensor') {
        if (!row['cisco.vedge.hardware.fan.sensor.state.last']) {
          return null
        }
        return {
          state: row['cisco.vedge.hardware.fan.sensor.state.last'],
          value: row['cisco.vedge.hardware.fan.sensor.speed.last'],
          unit: 'rpm',
        }
      } else if (instanceType === 'cisco.vedge.hardware.current.sensor') {
        if (!row['cisco.vedge.hardware.current.sensor.state.last']) {
          return null
        }
        return {
          state: row['cisco.vedge.hardware.current.sensor.state.last'],
          value: row['cisco.vedge.hardware.current.sensor.amperes.last'],
          unit: 'amp',
        }
      }
      return {}
    },
  },
}
</script>
