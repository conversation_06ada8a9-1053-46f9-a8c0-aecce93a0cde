<template>
  <Combined
    v-if="operation === 'combine'"
    :columns="columns"
    :combined-columns="operationColumns"
    :row="row"
    :class="{
      'ml-4': column.iconName && column.iconPosition === 'prefix',
      'mr-4': column.iconName && column.iconPosition === 'suffix',
    }"
  />
  <ValueComputed
    v-else-if="column.rawColumnContext.formula.conditions"
    :columns="columns"
    :column="column"
    :value="row[column.key]"
  />
  <span v-else class="text-ellipsis">
    {{ computedCellValue }}
  </span>
</template>

<script>
import ValueComputed from './value-computed.vue'
import Combined from './combined.vue'

export default {
  name: 'Computed',
  components: {
    Combined,
    ValueComputed,
  },
  inheritAttrs: false,
  props: {
    columns: {
      type: Array,
      required: true,
    },
    column: {
      type: Object,
      required: true,
    },
    row: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  computed: {
    operation() {
      return this.column.rawColumnContext.formula.operation
    },
    operationColumns() {
      return this.column.rawColumnContext.formula.columns || []
    },
    computedCellValue() {
      const columns = this.column.rawColumnContext.formula.columns || []
      const row = this.row

      let total = row[columns[0]?.replace(/\./g, '_')]
      for (const key of columns?.slice(1)) {
        const columnValue = row[key.replace(/\./g, '_')] || 0

        total -= columnValue
      }

      if (columns.length > 0) {
        switch (this.operation) {
          case 'subtract':
            return total

          // columns
          //   .slice(1)
          //   .reduce(
          //     (total, key) => total - (row[key.replace(/\./g, '_')] || 0),
          //     row[columns[0].replace(/\./g, '_')]
          //   )
          case 'add':
            return 'add'
          default:
            break
        }
      }
      return this.row[this.column.key]
    },
  },
}
</script>
