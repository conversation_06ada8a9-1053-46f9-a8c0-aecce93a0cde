<template>
  <span class="text-ellipsis">
    {{ computedValue }}
  </span>
</template>

<script>
import { evaluateExpression } from '../../../helper'

export default {
  name: 'ValueComputed',
  props: {
    column: {
      type: Object,
      required: true,
    },
    columns: {
      type: Array,
      default() {
        return []
      },
    },
    value: {
      type: [String, Number],
      default: undefined,
    },
  },

  computed: {
    computedValue() {
      if (this.column.rawColumnContext.formula.conditions) {
        const value = this.value
        const matchingCondition =
          this.column.rawColumnContext.formula.conditions.find((condition) =>
            evaluateExpression(value, condition.operator, condition.value)
          )
        if (matchingCondition) {
          return matchingCondition.result
        }
      }
      return this.value
    },
  },
}
</script>
