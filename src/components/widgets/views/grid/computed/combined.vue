<template>
  <div class="text-ellipsis flex min-w-0">
    <span
      v-for="column in columnConfigs"
      :key="column.key"
      class="mr-4 flex items-center min-w-0"
    >
      <MIcon
        v-if="column.iconName && column.iconPosition === 'prefix'"
        :name="column.iconName"
        class="mr-1"
        :class="column.iconClasses"
        :size="column.iconSize"
      />
      <span v-if="column.cellRender === 'number'" class="text-ellipsis">
        {{ row[column.key] || '-' | numberFormat }}
      </span>
      <span
        v-else
        class="text-ellipsis"
        v-text="row[`${column.key}.formatted`] || row[column.key]"
      />
      <MIcon
        v-if="column.iconName && column.iconPosition === 'suffix'"
        :name="column.iconName"
        class="mr-1"
        :class="column.iconClasses"
        :size="column.iconSize"
      />
    </span>
  </div>
</template>

<script>
export default {
  name: 'Combined',
  inheritAttrs: false,
  props: {
    combinedColumns: {
      type: Array,
      required: true,
    },
    row: {
      type: Object,
      required: true,
    },
    columns: {
      type: Array,
      required: true,
    },
  },
  computed: {
    columnConfigs() {
      const allColumns = this.columns
      return this.combinedColumns.map((key) => {
        const c = {
          ...(allColumns.find(
            (c) => c.rawName === key || c.rawName === key.replace(/[~^]/g, '.')
          ) || {}),
        }
        const iconClasses = c.iconClasses || []
        if (iconClasses.includes('2x')) {
          c.iconSize = '2x'
        } else if (iconClasses.includes('3x')) {
          c.iconSize = '3x'
        }
        return c
      })
    },
  },
}
</script>
