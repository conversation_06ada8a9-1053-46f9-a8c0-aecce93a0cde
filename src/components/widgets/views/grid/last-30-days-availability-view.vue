<template>
  <div
    v-if="data.series && data.series.length"
    class="flex flex-col flex-1 h-full"
  >
    <div
      v-for="series in data.series"
      :key="series.name"
      class="px-2 flex flex-1 flex-col justify-around"
    >
      <div class="flex min-w-0 items-center">
        <div class="icon partition-icon rounded-lg p-2">
          <MIcon name="availability" size="lg" />
        </div>
        <div class="size flex flex-col flex-1 ml-4 min-w-0">
          <div class="flex justify-between min-w-0">
            <span class="text-ellipsis font-500">{{ series.name }}</span>
          </div>
          <AvailabilityBar
            :data="
              counters.map((c) => ({
                name: counterName(c),
                value: series[c],
              }))
            "
          />
        </div>
      </div>
    </div>
  </div>
  <FlotoNoData
    v-else
    hide-svg
    header-tag="h5"
    icon="exclamation-triangle"
    variant="neutral"
  />
</template>

<script>
import Uniq from 'lodash/uniq'
import Capitalize from 'lodash/capitalize'
import { AVAILABILITY_INCLUDED_KEYS } from '@components/widgets/constants'
import AvailabilityBar from '../components/availability-bar.vue'
import { extractAvailabilityCounterType } from '../../result-builder'

export default {
  name: 'Last30DaysAvailability',
  components: {
    AvailabilityBar,
  },
  inheritAttrs: false,
  props: {
    data: {
      type: [Object, Array],
      default() {
        return {}
      },
    },
    widget: {
      type: Object,
      required: true,
    },
  },
  computed: {
    counters() {
      const series = this.data.series || []
      let counters = []
      // (this.data.series || [])
      //   .reduce((prev, next) => [...prev, ...Object.keys(next)], [])

      for (const next of series) {
        counters = counters.concat([...Object.keys(next)])
      }

      counters = counters.filter(
        (key) =>
          (/\.percent/.test(key) &&
            AVAILABILITY_INCLUDED_KEYS.includes(
              extractAvailabilityCounterType(key)
            )) ||
          /\.formatted/.test(key)
      )
      return Uniq(counters)
    },
  },
  methods: {
    counterName(counter) {
      return extractAvailabilityCounterType(counter)
    },
    capitalize(s) {
      return Capitalize(s)
    },
  },
}
</script>

<style lang="less" scoped>
.partition-icon {
  color: var(--availability-icon-text-color);
  background: var(--availability-icon-background-color);
}
</style>
