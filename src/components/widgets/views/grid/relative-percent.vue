<template>
  <div class="flex items-center">
    <!-- <span class="flex-1" style="flex-shrink: 0">{{ row[field] }}</span> -->
    <div class="mx-2 flex flex-col" style="flex: 2">
      <div class="ant-progress ant-progress-line ant-progress-small">
        <div>
          <div class="ant-progress-outer">
            <div class="ant-progress-inner">
              <div
                class="ant-progress-bg"
                :style="{
                  width: `${percentage}%`,
                  background: color,
                }"
                style="height: 6px; border-radius: 100px"
              >
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- <Progress :width="percentage" type="normal" /> -->
      <div class="text-neutral-light" style="font-size: 11px">
        {{ row[field] }}
      </div>
    </div>
  </div>
</template>

<script>
import Max from 'lodash/max'
import { PROGRESS_COLOR_MAP } from '@components/widgets/constants'

export default {
  name: 'RelativePercent',
  props: {
    data: {
      type: Array,
      default() {
        return []
      },
    },
    row: {
      type: Object,
      required: true,
    },
    field: {
      type: String,
      required: true,
    },
    index: {
      type: Number,
      default: 0,
    },
  },
  computed: {
    color() {
      let index = this.index - 1
      return PROGRESS_COLOR_MAP[index % PROGRESS_COLOR_MAP.length]
    },
    percentage() {
      if (!this.data.length) {
        return 100
      }
      const field = this.field
      const maxValue = Max(this.data.map((i) => i[`${field}_sort`] || i[field]))
      return Math.round(
        ((this.row[`${field}_sort`] || this.row[`${field}`]) * 100) / maxValue
      )
    },
  },
}
</script>
