<template>
  <div class="px-2">
    <MRow v-for="(column, index) in columns" :key="column.key" :gutter="0">
      <MCol
        :size="6"
        class="bg-neutral-lightest p-2 border-right border-left border-bot"
        :class="{ 'border-top': index === 0 }"
      >
        <span class="text-ellipsis" style="font-weight: 500">
          {{ column.name }}
        </span>
      </MCol>
      <MCol :size="6">
        <ColorCodedCell
          :is-preview="false"
          :data-item="row"
          :column="column"
          class="p-2 border-right border-bot h-full"
          :class="{ 'border-top': index === 0 }"
        >
          {{ row[column.key] }}
        </ColorCodedCell>
      </MCol>
    </MRow>
  </div>
</template>

<script>
import ColorCodedCell from './color-coded-cell.vue'

export default {
  name: 'ColumnGridView',
  components: {
    ColorCodedCell,
  },
  inheritAttrs: false,
  props: {
    widget: {
      type: Object,
      required: true,
    },
    data: {
      type: Object,
      required: true,
    },
  },
  computed: {
    columns() {
      return this.data.columns.filter((c) => c.hidden === false)
    },
    row() {
      return this.data.rows[0] || {}
    },
  },
  created() {
    if (
      ((this.data || {}).rows || []).length === 0 &&
      this.widget.hideEmptyWidget
    ) {
      this.$emit('hide')
    }
  },
}
</script>
