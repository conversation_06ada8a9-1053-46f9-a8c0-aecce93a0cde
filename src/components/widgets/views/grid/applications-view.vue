<template>
  <div v-if="data.length" class="flex flex-col flex-1 min-w-0">
    <div
      v-for="application in data"
      :key="application.application"
      class="flex min-w-0"
    >
      <ApplicationView
        :process="application.process"
        :data="application.data"
        v-bind="$attrs"
      />
    </div>
  </div>
  <FlotoNoData
    v-else
    hide-svg
    header-tag="h5"
    icon="exclamation-triangle"
    variant="neutral"
  />
</template>

<script>
import ApplicationView from '../components/application-view.vue'

export default {
  name: 'ApplicationsView',
  components: {
    ApplicationView,
  },
  inheritAttrs: false,
  props: {
    widget: {
      type: Object,
      required: true,
    },
    data: {
      type: [Array, Object],
      default() {
        return []
      },
    },
  },
  created() {
    if ((this.data || []).length === 0 && this.widget.hideEmptyWidget) {
      this.$emit('hide')
    }
  },
}
</script>
