<script>
import { evaluateExpression } from '../../helper'

const PositiveIcons = ['check-circle', 'check', 'long-arrow-up']
const NegativeIcons = ['times-circle', 'times', 'long-arrow-down']

function cellStyle(dataItem, column, ignoreBackground) {
  if (dataItem[`__color_${column.key}`]) {
    if (dataItem[`__colorType_${column.key}`] === 'background') {
      if (!ignoreBackground) {
        return {
          background: `${dataItem[`__color_${column.key}`]} !important`,
          color: 'white !important',
        }
      } else {
        return {}
      }
    } else {
      return {
        color: `${dataItem[`__color_${column.key}`]} !important`,
      }
    }
  }
  return {}
}

function cellClass(dataItem, column) {
  let existingClasses = column.className ? column.className.split(' ') : []
  if (dataItem.rowType === 'groupHeader') {
    existingClasses.push('report-group-header')
  }
  if (dataItem.rowType === 'pivotGroupHeader') {
    existingClasses.push('report-pivot-group-header')
  }
  if (dataItem[`__color_${column.key}`]) {
    existingClasses = existingClasses.concat(['text-ellipsis', 'has-color'])
  }
  if (column.align) {
    existingClasses.push(`text-${column.align}`)

    if (dataItem[`__color_${column.key}`]) {
      if (column.align === 'center') {
        existingClasses.push('justify-center')
      } else if (column.align === 'right') {
        existingClasses.push('justify-end')
      }
    }

    if (column.align === 'left') {
      existingClasses = existingClasses.concat([
        'flex',
        'items-center',
        'min-w-0',
      ])
    }
  }
  if (
    column.rawColumnContext &&
    column.rawColumnContext.classes &&
    column.rawColumnContext.classes.includes('label')
  ) {
    existingClasses = existingClasses
      .concat(['ant-tag', 'rounded', 'tag-primary', 'cursor-auto'])
      .filter((c) => c !== 'flex')
  }
  return existingClasses
}

function resolveIconName(dataItem, column) {
  if (column.rawColumnContext.iconConditions) {
    const currentValue = dataItem[column.key]
    const matchingCondition = column.rawColumnContext.iconConditions.find(
      (condition) =>
        evaluateExpression(currentValue, condition.operator, condition.value)
    )
    if (matchingCondition) {
      return matchingCondition.icon
    }
  }
  return column.iconName
}

function resolveIconClasses(column, icon) {
  const iconClasses = [...(column.rawColumnContext.iconClasses || [])]
  if (PositiveIcons.includes(icon)) {
    iconClasses.push('text-secondary-green')
  } else if (NegativeIcons.includes(icon)) {
    iconClasses.push('text-secondary-red')
  }
  return iconClasses
}

function resolveIconSize(iconClasses) {
  iconClasses = iconClasses || []
  if (iconClasses.includes('lg')) {
    return 'lg'
  } else if (iconClasses.includes('2x')) {
    return '2x'
  } else if (iconClasses.includes('3x')) {
    return '3x'
  }
  return undefined
}

function checkHasValue(dataItem, column) {
  const value = dataItem[column.key]
  return value !== null && value !== undefined && value !== ''
}

export default {
  name: 'ColorCodedCell',
  functional: true,
  props: {
    dataItem: {
      type: Object,
      required: true,
    },
    column: {
      type: Object,
      default: undefined,
    },
    isPreview: {
      type: Boolean,
      default: false,
    },
    linkIcon: {
      type: Boolean,
      default: false,
    },
    field: {
      type: Object,
      default() {
        return {}
      },
    },
    ignoreBackground: {
      type: Boolean,
      default: false,
    },
  },
  render(h, context) {
    const iconName = resolveIconName(
      context.props.dataItem,
      context.props.column
    )
    let iconClasses = resolveIconClasses(context.props.column, iconName)
    if (context.props.linkIcon) {
      iconClasses = iconClasses.concat(['text-primary'])
    }
    const iconSize = resolveIconSize(iconClasses)
    const iconPosition = context.props.column.rawColumnContext.iconPosition
    const icon =
      iconName &&
      h('MIcon', {
        class: iconClasses.concat(iconPosition === 'prefix' ? 'mr-1' : 'ml-1'),
        props: {
          name: iconName,
          size: iconSize,
        },
      })
    const hasValue = checkHasValue(context.props.dataItem, context.props.column)
    const isComputedColumn =
      context.props.column.rawColumnContext.slot === 'computed'
    if (context.props.dataItem.rowType === 'groupHeader') {
      return (
        <div
          class={[
            ...cellClass(context.props.dataItem, context.props.column),
            context.data.class,
            context.data.staticClass,
            ...(context.props.field.columnIndex ===
            context.props.field.columnsCount - 1
              ? ['flap-count']
              : []),
          ]}
        >
          {context.props.field.columnIndex === 0 ? (
            <h5 class="text-primary mb-0 ml-2">
              {context.props.dataItem.groupHeader}
            </h5>
          ) : null}
          {context.props.field.columnIndex ===
          context.props.field.columnsCount - 1 ? (
            <h5 class="mb-0 text-right">
              Flap Count: {context.props.dataItem.flapCount}
            </h5>
          ) : null}
        </div>
      )
    }
    return (
      <div
        class={[
          ...cellClass(context.props.dataItem, context.props.column),
          context.data.class,
          context.data.staticClass,
        ]}
        style={cellStyle(
          context.props.dataItem,
          context.props.column,
          context.props.ignoreBackground
        )}
      >
        {(hasValue || isComputedColumn) && iconPosition === 'prefix' && icon}
        {context.props.dataItem[`__prefix_${context.props.field.field}`]
          ? `${
              context.props.dataItem[`__prefix_${context.props.field.field}`]
            } `
          : ''}
        {context.scopedSlots.default()}
        {context.props.dataItem[`__suffix_${context.props.field.field}`]
          ? ` ${
              context.props.dataItem[`__suffix_${context.props.field.field}`]
            }`
          : ''}
        {(hasValue || isComputedColumn) && iconPosition === 'suffix' && icon}
      </div>
    )
  },
}
</script>
