<template>
  <div class="flex flex-col">
    <div class="row w-full min-w-0 rounded mb-2 page-background-color">
      <Cell value="Phase#1" />
      <Cell
        title="Hash Algorithm"
        :value="row['tunnel_phase1_hash_algorithm_last']"
      />
      <Cell
        title="Encryption Algorithm"
        :value="row['tunnel_phase1_encryption_algorithm_last']"
      />
      <Cell
        title="Negotiation Mode"
        :value="row['tunnel_phase1_negotiation_mode_last']"
      />
      <Cell title="Traffic" icon="traffic">
        <div class="flex">
          <span class="flex-1 min-w-0 text-ellipsis">
            {{ row['tunnel_phase1_in_traffic_bytes_per_sec_last'] }}(in)
          </span>
          <span class="flex-1 min-w-0 text-ellipsis">
            {{ row['tunnel_phase1_out_traffic_bytes_per_sec_last'] }}(out)
          </span>
        </div>
      </Cell>
      <Cell
        title="Active Time"
        icon="stopwatch"
        icon-class="text-primary"
        :value="row['tunnel_phase1_active_time_last']"
      />
    </div>
    <div class="row w-full min-w-0 rounded page-background-color">
      <Cell value="Phase#2" />
      <Cell
        title="Encapsulation Mode"
        :value="row['tunnel_phase2_encapsulation_mode_last']"
      />
      <Cell title="Decompression Algorithm">
        <div class="flex">
          <span class="flex-1 min-w-0 text-ellipsis">
            {{ row['tunnel_phase2_in_sa_decompression_algorithm_last'] }}(in)
          </span>
          <span class="flex-1 min-w-0 text-ellipsis">
            {{ row['tunnel_phase2_out_sa_decompression_algorithm_last'] }}(out)
          </span>
        </div>
      </Cell>
      <Cell title="Traffic" icon="traffic">
        <div class="flex">
          <span class="flex-1 min-w-0 text-ellipsis">
            {{ row['tunnel_phase2_in_traffic_bytes_per_sec'] }}(in)
          </span>
          <span class="flex-1 min-w-0 text-ellipsis">
            {{ row['tunnel_phase2_out_traffic_bytes_per_sec'] }}(out)
          </span>
        </div>
      </Cell>
      <Cell
        title="Active Time"
        icon="stopwatch"
        icon-class="text-primary"
        :value="row['tunnel_phase2_active_time']"
      />
    </div>
  </div>
</template>

<script>
import Cell from './cell.vue'

export default {
  name: 'ExpandedRow',
  components: {
    Cell,
  },
  props: {
    row: {
      type: Object,
      required: true,
    },
  },
}
</script>
