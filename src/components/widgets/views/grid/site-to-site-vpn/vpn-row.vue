<template>
  <div
    class="flex flex-col w-full min-w-0 bg-neutral-lightest w-full min-w-0 rounded mb-2"
  >
    <div class="flex row items-center">
      <div class="mr-2 cursor-pointer" @click="isExpanded = !isExpanded">
        <MIcon
          :name="isExpanded ? 'chevron-down' : 'chevron-right'"
          class="text-neutral-light"
        />
      </div>
      <Cell
        v-for="column in columns"
        :key="column.key"
        :title="column.title"
        :value="row[column.key]"
        :icon="column.icon"
        :icon-class="column.iconClass"
      >
        <template v-if="column.key === 'tunnel_status_last'">
          <MStatusTag :status="row[column.key]" />
        </template>
      </Cell>
    </div>
    <div v-if="isExpanded" class="w-full pb-2 pl-8 pr-4">
      <ExpandedRow :row="row" />
    </div>
  </div>
</template>

<script>
import Cell from './cell.vue'
import ExpandedRow from './expanded-row.vue'

export default {
  name: 'VpnRow',
  components: {
    Cell,
    ExpandedRow,
  },
  props: {
    row: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      isExpanded: false,
    }
  },
  computed: {
    columns() {
      return [
        { title: 'Tunnel', key: 'tunnel' },
        { title: 'Status', key: 'tunnel_status_last', icon: 'check-circle' },
        {
          title: 'Source IP',
          key: 'tunnel_source_ip_address_last',
          icon: 'map-marker',
          iconClass: 'text-secondary-orange',
        },
        {
          title: 'Destination IP',
          key: 'tunnel_destination_ip_address_last',
          icon: 'flag',
          iconClass: 'text-secondary-green',
        },
        {
          title: 'Source',
          key: 'tunnel_source_last',
          icon: 'map-marker',
          iconClass: 'text-secondary-orange',
        },
        {
          title: 'Destination',
          key: 'tunnel_destination_last',
          icon: 'flag',
          iconClass: 'text-secondary-green',
        },
      ]
    },
  },
}
</script>
