<template>
  <div class="column">
    <div
      v-if="computedIcon"
      class="icon mr-2"
      :class="{
        'text-neutral-light': !computedIconClass,
        [computedIconClass]: computedIconClass,
      }"
    >
      <MIcon :name="computedIcon" size="lg" />
    </div>
    <div class="content">
      <small class="header text-neutral-light">
        {{ title }}
      </small>
      <div class="value text-ellipsis" :title="value">
        <slot>
          {{ value !== undefined && value !== '' ? value : '-' }}
        </slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Cell',
  props: {
    title: {
      type: String,
      default: undefined,
    },
    value: {
      type: [String, Number],
      default: undefined,
    },
    icon: {
      type: String,
      default: undefined,
    },
    iconClass: {
      type: String,
      default: undefined,
    },
  },
  computed: {
    computedIconClass() {
      if (this.title === 'Status') {
        if (this.value && ['fail', 'down'].includes(this.value.toLowerCase())) {
          return 'text-secondary-red'
        }
        return 'text-secondary-green'
      }
      return this.iconClass
    },
    computedIcon() {
      if (this.title === 'Status') {
        if (this.value && ['fail', 'down'].includes(this.value.toLowerCase())) {
          return 'times-circle'
        }
        return this.icon
      }
      return this.icon
    },
  },
}
</script>
