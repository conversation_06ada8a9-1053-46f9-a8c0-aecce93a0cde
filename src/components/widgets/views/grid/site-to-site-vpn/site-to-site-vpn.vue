<template>
  <div
    v-if="data.rows.length"
    class="flex flex-col flex-1 min-w-0 p-2 site-to-site-vpn-widget"
  >
    <VpnRow v-for="row in data.rows" :key="row.id" :row="row" />
  </div>
  <FlotoNoData
    v-else
    hide-svg
    header-tag="h5"
    icon="exclamation-triangle"
    variant="neutral"
  />
</template>

<script>
import VpnRow from './vpn-row.vue'

export default {
  name: 'SiteToSiteVpn',
  components: {
    VpnRow,
  },
  inheritAttrs: false,
  props: {
    widget: {
      type: Object,
      required: true,
    },
    data: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  created() {
    if (
      ((this.data || {}).rows || []).length === 0 &&
      this.widget.hideEmptyWidget
    ) {
      this.$emit('hide')
    }
  },
}
</script>

<style lang="less">
.site-to-site-vpn-widget {
  .row {
    @apply mb-2 mx-1 flex rounded py-2 px-2;

    cursor: auto;

    .column {
      display: flex;
      flex: 1;
      flex-shrink: 0;
      align-items: center;
      min-width: 0;

      .content {
        display: flex;
        flex: 1;
        flex-direction: column;
        justify-content: center;
        min-width: 0;
        height: 100%;
        padding-right: 0.5rem;
        padding-left: 0.5rem;

        .heaader {
          font-size: 11px !important;
        }

        .value {
          font-weight: bold;
        }
      }
    }
  }
}
</style>
