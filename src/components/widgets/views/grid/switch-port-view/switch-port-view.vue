<template>
  <div
    v-if="data.length"
    class="switch-port-view-container shadow-lg flex-1 min-h-0"
    :class="{ flex: showIp }"
  >
    <div class="bolt-top-left bolt" />
    <div class="bolt-top-right bolt" />
    <div class="bolt-bottom-left bolt" />
    <div class="bolt-bottom-right bolt" />
    <div
      ref="portContainer"
      class="flex flex-wrap h-full items-center"
      :style="showIp ? { width: '90%' } : {}"
    >
      <Port
        v-for="i in data"
        :key="i.key"
        :status="i.status"
        :interface-index="i.interface"
        :interface-name="i.name"
        :monitor="i.monitor"
        :width="singlePortWidth"
      />
    </div>
    <div
      v-if="showIp && data.length > 0"
      class="bg-white flex-1 rounded-md flex flex-col items-center justify-center"
      style="margin-top: 10px; margin-bottom: 10px"
    >
      <h5 class="text-primary m-0">{{ data[0].monitor }}</h5>
      <h5 class="text-primary m-0" title="Vendor">{{ monitor.vendor }}</h5>
    </div>
  </div>
  <FlotoNoData
    v-else
    hide-svg
    header-tag="h5"
    icon="exclamation-triangle"
    variant="neutral"
  />
</template>

<script>
import Port from './port.vue'

export default {
  name: 'SwitchPortView',
  components: {
    Port,
  },
  inheritAttrs: false,
  props: {
    data: {
      type: [Array, Object],
      default() {
        return []
      },
    },
    showIp: {
      type: Boolean,
      // eslint-disable-next-line
      default: true,
    },
    cellHeight: {
      type: Number,
      default: 85,
    },
    monitor: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      containerWidth: undefined,
    }
  },
  computed: {
    singlePortWidth() {
      if (this.containerWidth) {
        return Math.round(this.containerWidth / 48)
      }
      return undefined
    },
    totalRows() {
      if (this.singlePortWidth) {
        const singleRowItems = this.containerWidth / this.singlePortWidth
        const rows = Math.ceil(this.data.length / singleRowItems)
        return rows
      }
      return 2
    },
  },
  mounted() {
    if (this.$refs.portContainer) {
      this.containerWidth = this.$refs.portContainer.offsetWidth
      setTimeout(() => this.updateAllocatedHeight())
      // this.$_resizeObserver = new ResizeObserver(() => {
      //   if (this.$refs.portContainer) {
      //     this.containerWidth = this.$refs.portContainer.offsetWidth
      //     setTimeout(() => this.updateAllocatedHeight())
      //   }
      // })
      // this.$_resizeObserver.observe(this.$refs.portContainer)
    }
  },
  beforeDestroy() {
    if (this.$_resizeObserver) {
      this.$_resizeObserver.disconnect()
      this.$_resizeObserver = undefined
    }
  },
  methods: {
    updateAllocatedHeight() {
      if (this.totalRows > 0) {
        const totalRowsHeight = this.singlePortWidth * this.totalRows
        const neededHeight = Math.ceil(totalRowsHeight / this.cellHeight)
        this.$emit('update-height', Math.max(neededHeight, 2))
      }
    },
  },
}
</script>

<style lang="less" scoped>
.switch-port-view-container {
  @apply rounded-md relative;

  padding: 10px 25px;
  background: var(--switch-port-view-bg);

  .bolt {
    position: absolute;
    width: 10px;
    height: 20px;
    background: white;
    border-radius: 10px;

    &.bolt-top-right {
      top: 5px;
      right: 5px;
    }

    &.bolt-top-left {
      top: 5px;
      left: 5px;
    }

    &.bolt-bottom-left {
      bottom: 5px;
      left: 5px;
    }

    &.bolt-bottom-right {
      right: 5px;
      bottom: 5px;
    }
  }
}
</style>
