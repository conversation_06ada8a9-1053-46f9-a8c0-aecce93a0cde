<template>
  <div
    class="inline-flex flex-col items-center px-1 cursor-auto mb-2"
    :style="{ width: `${width}px`, height: `${width}px` }"
    :title="interfaceName ? `${interfaceName}` : undefined"
    :class="{
      disabled: smallCaseStatus === 'disable' || smallCaseStatus === '',
    }"
  >
    <div class="led" :class="{ [smallCaseStatus]: true }" />
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 512 512"
      :style="{ width: `${width - 5}px`, height: `${width - 7}px` }"
    >
      <path fill="#b2c1d1" d="M506,491.29H6V20.71H506Z" />
      <path
        fill="#3f4f60"
        d="M57.69,426.43H454.24V191.14h-69v-56.8H299.07V85.66H212.86v48.68h-86.2v56.8h-69Z"
      />
      <path
        fill="#ffd884"
        d="M135.25,426.35h34.49V329H135.25Zm68.93,0h34.49V329H204.18ZM342.12,329v97.36h34.49V329Zm-68.93,97.36h34.49V329H273.19Z"
      />
    </svg>
  </div>
</template>

<script>
export default {
  name: 'Port',
  props: {
    status: {
      type: String,
      default: 'disable',
    },
    interfaceIndex: {
      type: String,
      required: true,
    },
    interfaceName: {
      type: String,
      required: true,
    },
    width: {
      type: Number,
      default: undefined,
    },
  },
  computed: {
    smallCaseStatus() {
      return (this.status || '').toLowerCase()
    },
  },
}
</script>

<style lang="less" scoped>
.disabled {
  svg {
    opacity: 0.4;
  }
}

.led {
  width: 8px;
  height: 4px;
  margin-bottom: 3px;
  background: var(--neutral-light);
}
</style>
