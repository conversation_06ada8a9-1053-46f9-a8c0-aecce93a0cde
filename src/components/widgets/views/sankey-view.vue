<template>
  <div class="flex flex-1 h-full w-full">
    <SankeyOptions :data="data" :widget="widget">
      <template v-slot="{ options }">
        <Chart
          v-if="options.series.length"
          :options="options"
          class="flex-1 h-full w-full"
        />
        <FlotoNoData
          v-else
          hide-svg
          header-tag="h5"
          variant="neutral"
          icon="exclamation-triangle"
        />
      </template>
    </SankeyOptions>
  </div>
</template>

<script>
import Chart from '@components/chart/chart.vue'
import SankeyOptions from '@components/chart/options/sankey-options.vue'

export default {
  name: 'SankeyView',
  components: {
    Chart,
    SankeyOptions,
  },
  inheritAttrs: false,
  props: {
    widget: {
      type: Object,
      required: true,
    },
    data: {
      type: [Array, Object],
      default() {
        return []
      },
    },
  },
  created() {
    if (
      (Array.isArray(this.data) ? this.data : this.data ? [this.data] : [])
        .length === 0 &&
      this.widget.hideEmptyWidget
    ) {
      this.$emit('hide')
    }
  },
}
</script>
