<template>
  <div class="flex justify-center items-center px-2 h-full w-full">
    <div
      v-if="seriesData.length"
      class="mx-1 severity-text min-w-0"
      :style="{
        display: `grid`,
        'grid-auto-rows': `minmax(${
          forMonitorDetails ? '45px' : `${radilBarWidth}px`
        }, max-content)`,
        'grid-template-columns': `repeat(${colRepeat}, minmax(${
          forMonitorDetails ? '45px' : `${radilBarWidth}px`
        }, 1fr))`,
        'grid-gap': `${forMonitorDetails ? '5px' : '10px'}`,
        cursor:
          (forMonitorDetails && !disabledFiltering) || widget
            ? 'pointer'
            : 'unset',
      }"
    >
      <Progressbar
        v-for="categories in sortedSeriesData"
        :key="categories.name"
        :data="categories"
        :total="total"
        :for-monitor-details="forMonitorDetails"
        :disabled-filtering="disabledFiltering"
        :use-compliance-severity="useComplianceSeverity"
        @filter-by-severity="onSeverityFilterApplied"
      />
      <!-- total severity count radial progress-->
      <Progressbar
        v-if="showTotalCount"
        :data="totalCountsData"
        :total="total"
        :disabled-filtering="disabledFiltering"
        :for-monitor-details="forMonitorDetails"
        :use-compliance-severity="useComplianceSeverity"
        @filter-by-severity="$emit('filter-by-severity', $event)"
      />
    </div>
    <FlotoNoData
      v-else
      hide-svg
      header-tag="h5"
      icon="exclamation-triangle"
      variant="neutral"
    />
  </div>
</template>

<script>
import CloneDeep from 'lodash/cloneDeep'
import { sortedSeriesData } from '../helper'
import Progressbar from './components/progressbar.vue'

export default {
  name: 'RadialView',
  components: {
    Progressbar,
  },
  props: {
    data: { type: Object, required: true },
    forMonitorDetails: {
      type: Boolean,
      default: false,
    },
    disabledFiltering: {
      type: Boolean,
      default: false,
    },
    widget: {
      type: Object,
      default: undefined,
    },
    useComplianceSeverity: {
      type: Boolean,
      default: false,
    },
    showTotalCount: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      containerWidth: undefined,
      containerHeight: undefined,
    }
  },
  computed: {
    seriesData() {
      return CloneDeep(this.data?.series?.[0]?.data) || []
    },

    total() {
      let total = 0

      // let total = this.seriesData.reduce((accumulator, currentValue) => {
      //   return accumulator + currentValue.y
      // }, 0)

      for (const dataPoint of this.seriesData) {
        total += dataPoint.y
      }

      return total
    },
    sortedSeriesData() {
      return sortedSeriesData(this.seriesData)
    },
    colRepeat() {
      // if (this.forMonitorDetails) {
      //   return this.data.categories.length + 1
      // }

      if (this.showTotalCount) {
        return this.data.categories.length + 1
      }
      return this.data.categories.length
    },
    totalCountsData() {
      return {
        name: 'total',
        y: this.total,
      }
    },
    radilBarWidth() {
      let width = Math.min(
        (this.containerWidth -
          (this.data.categories.length - 1) * 10 -
          16 * 2) /
          this.data.categories.length,
        this.containerHeight
      )

      if (width >= 130) {
        return 130
      } else if (width <= 50) {
        return 50
      }

      return width
    },
  },

  mounted() {
    this.$_resizeObserver = new ResizeObserver((entries) => {
      if (this.$el) {
        this.containerWidth = this.$el.offsetWidth
        this.containerHeight = this.$el.offsetHeight
      }
    })
    this.$_resizeObserver.observe(this.$el)
    this.containerWidth = this.$el.offsetWidth
    this.containerHeight = this.$el.offsetHeight
  },
  beforeDestroy() {
    if (this.$_resizeObserver) {
      this.$_resizeObserver.disconnect()
      this.$_resizeObserver = null
    }
  },
  methods: {
    onSeverityFilterApplied(event) {
      this.$emit('filter-by-severity', event)
      this.$emit('drilldown', undefined, event)
    },
  },
}
</script>
