<template>
  <Component
    :is="viewComponent"
    :data="isHorizontalTopnWidget ? data.chart : data"
    :widget="widget"
    :is-preview="isPreview"
    v-bind="$attrs"
    v-on="$listeners"
  />
</template>

<script>
import { WidgetTypeConstants } from '../constants'
import Grid from './grid-view.vue'
import Chart from './chart-view.vue'
import TopnSolidGauge from './gauge/topn-solid-gauge.vue'
import mapView from './map-view'

export default {
  name: 'TopNView',
  inheritAttrs: false,
  props: {
    isPreview: { type: Boolean, default: false },
    widget: {
      type: Object,
      required: true,
    },
    data: {
      type: [Array, Object],
      default() {
        return []
      },
    },
  },
  computed: {
    isHorizontalTopnWidget() {
      return this.widget.widgetType === WidgetTypeConstants.HORIZONTAL_TOPN
    },
    viewComponent() {
      if (this.widget.widgetType === WidgetTypeConstants.GRID) {
        return Grid
      } else if (
        this.widget.widgetType === WidgetTypeConstants.TOPN_SOLID_GAUGE_VIEW
      ) {
        return TopnSolidGauge
      } else if (this.widget.widgetType === WidgetTypeConstants.TREE_VIEW) {
        return mapView
      }
      return Chart
    },
  },
  watch: {
    data: {
      handler(newValue, oldValue) {
        if (
          newValue !== oldValue &&
          this.widget.widgetType !== WidgetTypeConstants.GRID &&
          newValue.responseColumns
        ) {
          this.$emit(
            'column-received',
            newValue.responseColumns.map((c) => c)
          )
          this.$emit('group-column-received', newValue.groupByColumns)
        }
        if (
          newValue !== oldValue &&
          this.widget.widgetType === WidgetTypeConstants.HORIZONTAL_TOPN &&
          newValue.grid &&
          newValue.grid.responseColumns
        ) {
          this.$emit(
            'column-received',
            newValue.grid.responseColumns.map((c) => c)
          )
          this.$emit('group-column-received', newValue.grid.groupByColumns)
        }
      },
      immediate: true,
    },
  },
}
</script>
