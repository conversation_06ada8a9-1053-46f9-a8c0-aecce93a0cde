<template>
  <div ref="mainContainer" class="flex flex-1 h-full w-full flex-col">
    <template v-if="result.length > 0 && containerHeight">
      <template v-if="forPrint">
        <div
          v-for="chart in result"
          :key="chart.key"
          class="flex flex-col h-full"
        >
          <div
            class="w-full flex flex-col"
            :style="{ height: enableGridView ? halfHeight : fullHeight }"
          >
            <div class="bg-neutral-lightest p-2 rounded">
              <h5 class="text-primary mb-0">{{ chart.monitor }}</h5>
            </div>
            <div class="w-full flex-1 min-h-0" :data-entity="chart.key">
              <ChartView
                :increment-key="200"
                :data="chart.data"
                :x-axis-force-date-time="true"
                :widget="widgetResponseDefinition"
                v-bind="attrs"
                :for-print="forPrint"
              />
            </div>
          </div>
          <div
            v-if="enableGridView && !forPrint"
            class="w-full vue-grid-item"
            :style="{ height: enableGridView ? halfHeight : fullHeight }"
          >
            <DefaultGrid
              :key="`${chart.key}-grid`"
              :widget="$attrs.widget"
              hide-title
              is-preview
              :data="chart.grid"
              ignore-widget-watch
            />
          </div>
          <MDivider />
        </div>
      </template>
      <div
        class="flex flex-col h-full justify-center items-center text-neutral-light"
      >
        <h4>Preview Not Available for this report</h4>
      </div>
    </template>
    <FlotoNoData
      v-else
      hide-svg
      header-tag="h5"
      icon="exclamation-triangle"
      variant="neutral"
    />
  </div>
</template>

<script>
import { UserPreferenceComputed } from '@/src/state/modules/user-preference'
import ChartView from './chart-view.vue'
import DefaultGrid from '@/src/components/widgets/views/grid/default-grid.vue'
import { WidgetTypeConstants } from '../constants'
import { convertHistoricalChartToGrid } from '@/src/modules/report/helpers/create-grid-from-chart'

export default {
  name: 'MultiTrendChartView',
  components: {
    ChartView,
    DefaultGrid,
  },
  inheritAttrs: false,
  props: {
    widgetResponseDefinition: {
      type: Object,
      default() {
        return {}
      },
    },
    data: {
      type: [Array, Object],
      default() {
        return []
      },
    },
    forPrint: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      containerHeight: null,
    }
  },
  computed: {
    ...UserPreferenceComputed,
    attrs() {
      const { widget, ...attrs } = this.$attrs
      return attrs
    },
    enableGridView() {
      return this.widgetResponseDefinition.enableGridView
    },
    halfHeight() {
      return `${this.containerHeight / 2}px`
    },
    fullHeight() {
      return `${this.containerHeight}px`
    },
    result() {
      let hasGridView = this.enableGridView
      return (this.data.data || []).map((data) => {
        return {
          ...data,
          ...(hasGridView
            ? {
                grid: convertHistoricalChartToGrid({
                  timezone: this.timezone,
                  response: {
                    result: {
                      [WidgetTypeConstants.CHART]: data.data,
                    },
                  },
                }),
              }
            : {}),
        }
      })
    },
  },
  mounted() {
    this.containerHeight = this.$el.clientHeight
  },
}
</script>
