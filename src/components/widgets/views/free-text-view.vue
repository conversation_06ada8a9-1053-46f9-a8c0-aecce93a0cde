<template>
  <Capture :key="refreshKey" :widget="widget">
    <template v-slot="{ capture }">
      <WidgetLayout
        :guid="widget.name"
        :widget="widget"
        :progress="100"
        :time-range="timeRange"
        :is-loading="false"
        :disabled="disabled"
        :font-size="fontSize"
        :is-preview="isPreview"
        :additional-actions="[]"
        :is-dashboard-fullscreen="isDashboardInFullscreen"
        hide-title
        v-bind="$attrs"
      >
        <template
          v-slot="{
            hideActions,
            makeFullScreen,
            exitFullScreen,
            isFullscreen,
            forTemplate,
          }"
        >
          <div
            class="flex flex-1 flex-col justify-between rounded h-full w-full"
          >
            <div
              class="absolute widget-action bg-neutral-lightest rounded"
              style="top: 5px; right: 10px; padding-left: 5px"
            >
              <Title
                :hide-timeline="true"
                :hide-actions="
                  $attrs.fullscreen || $attrs['hide-actions'] || hideActions
                "
                :disabled="$attrs.disabled"
                :is-dashboard-fullscreen="$attrs['is-dashboard-fullscreen']"
                :is-preview="isPreview"
                :is-fullscreen="isFullscreen"
                :for-template="forTemplate"
                :result-error="$attrs['result-error']"
                :hide-title="$attrs['hide-title']"
                :is-custom-template="$attrs['is-custom-template']"
                :guid="widget.name"
                :widget="widget"
                @fullscreen="makeFullScreen"
                @exit-fullscreen="exitFullScreen"
                @edit="$emit('edit', widget)"
                @clone="$emit('clone', widget)"
                @remove="$emit('remove')"
                @share="capture"
              />
            </div>

            <div
              ref="containerRef"
              class="flex w-full flex-1 items-center rounded h-full"
              :style="computedStyle"
            >
              <div class="min-w-0 mx-2 text-ellipsis"
                ><span
                  ref="textRef"
                  style="font-weight: 600; line-height: 1"
                  :title="displayText || 'Text to display'"
                >
                  {{ displayText || 'Text to display' }}</span
                ></div
              >
            </div>
          </div>
        </template>
      </WidgetLayout>
    </template>
  </Capture>
</template>

<script>
import Bus from '@utils/emitter'
import { ColorOptionsForFreeText } from '@components/widgets/constants'

import Title from './components/widget-title.vue'
import Capture from '@components/chart/capture.vue'
import WidgetLayout from './widget-layout.vue'

export default {
  name: 'FreeTextView',
  components: {
    Title,
    Capture,
    WidgetLayout,
  },
  inheritAttrs: false,

  props: {
    widget: {
      type: Object,
      required: true,
    },

    isPreview: {
      type: Boolean,
      default: false,
    },
    timeRange: {
      type: Object,
      default: undefined,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    fontSize: {
      type: String,
      default: 'small',
    },
    isDashboardInFullscreen: {
      type: Boolean,
      default: false,
    },
    dashlet: {
      type: Object,
      default: undefined,
    },
  },
  data() {
    this.colorMap = ColorOptionsForFreeText.reduce((acc, item) => {
      return {
        ...acc,
        [item.key]: item.color,
      }
    }, {})
    return {
      resultError: null,
      refreshKey: 1,
      dynamicFontSize: null,
      resizeObserver: null,
      MIN_FONT_SIZE: 30, // minimum font size in pixels
      MAX_FONT_SIZE: 88, // maximum font size in pixels
      previousContainerHeight: null, // Track previous height to detect changes
    }
  },

  computed: {
    computedStyle() {
      if (this.widget) {
        const widgetProperties = this.widget.widgetProperties
        return {
          ...(widgetProperties.fontColor
            ? {
                color: this.colorMap[widgetProperties.fontColor],
              }
            : {}),
          ...(widgetProperties.fontSize && widgetProperties.fontSize !== 'auto'
            ? {
                fontSize: widgetProperties.fontSize,
              }
            : this.dynamicFontSize !== null
            ? {
                fontSize: `${this.dynamicFontSize}px`,
              }
            : {}),
          ...(widgetProperties.textAlign
            ? {
                justifyContent: widgetProperties.textAlign,
              }
            : {}),
        }
      }
      return {}
    },
    displayText() {
      return this.widget?.widgetProperties?.textToDisplay
    },
    isAutoFontSize() {
      return this.widget?.widgetProperties?.fontSize === 'auto'
    },
  },
  watch: {
    'widget.widgetProperties.fontSize': {
      handler(newVal) {
        if (newVal === 'auto') {
          this.calculateDynamicFontSize()
        } else {
          this.dynamicFontSize = null
        }
      },
      immediate: true,
    },
    displayText: function () {
      if (this.isAutoFontSize) {
        this.calculateDynamicFontSize()
      }
    },
    isAutoFontSize(newVal) {
      if (newVal) {
        this.$nextTick(() => {
          this.calculateDynamicFontSize()
        })
      } else {
        this.dynamicFontSize = null
      }
    },
    'dashlet.h': function (newHeight) {
      if (this.isAutoFontSize) {
        this.dynamicFontSize = null

        if (newHeight === 1 && this.dashlet && this.dashlet.w > 4) {
          setTimeout(() => {
            this.dynamicFontSize = 35
          }, 50)
        } else {
          this.$nextTick(() => {
            this.calculateDynamicFontSize()
          })
        }
      }
    },
    'dashlet.w': function (newWidth) {
      if (this.isAutoFontSize) {
        this.dynamicFontSize = null

        if (this.dashlet && this.dashlet.h === 1) {
          if (newWidth > 4) {
            setTimeout(() => {
              this.dynamicFontSize = 35
            }, 50)
          } else {
            this.$nextTick(() => {
              this.calculateDynamicFontSize()
            })
          }
        } else {
          this.calculateDynamicFontSize()
        }
      }
    },
  },
  created() {
    const exportWidgetInitHandler = () => {
      setTimeout(() => {
        Bus.$emit('current:widget:queue')
      })
    }
    this.$emit('preview-rendered', true)
    Bus.$on('widgets:export', exportWidgetInitHandler)

    const refreshHandler = (id) => {
      if (id === this.widget.id) {
        this.refreshKey++

        this.$emit('refresh-widget')
      }
    }
    Bus.$on('widget.refresh', refreshHandler)

    this.$on('hook:beforeDestroy', () => {
      Bus.$off('widget.refresh', refreshHandler)
      this.cleanupResizeObserver()
      Bus.$off('widgets:export', exportWidgetInitHandler)
    })
  },
  mounted() {
    this.setupResizeObserver()

    // Check for h=1, w>4 case immediately if dashlet dimensions are available
    if (
      this.dashlet &&
      this.dashlet.h === 1 &&
      this.dashlet.w > 4 &&
      this.isAutoFontSize
    ) {
      this.dynamicFontSize = 35
    } else {
      // Initial calculation with a slight delay to ensure container is fully rendered
      this.$nextTick(() => {
        if (this.isAutoFontSize) {
          this.calculateDynamicFontSize()

          // Add additional calculation after a small delay to handle late renders
          setTimeout(() => {
            this.calculateDynamicFontSize()
          }, 300)
        }
      })
    }

    // Add window load event listener to ensure calculation happens after complete page load
    window.addEventListener('load', this.handleWindowLoad)
  },
  beforeDestroy() {
    this.cleanupResizeObserver()
    window.removeEventListener('load', this.handleWindowLoad)
  },
  methods: {
    handleWindowLoad() {
      if (this.isAutoFontSize) {
        // Check for special case first
        if (this.dashlet && this.dashlet.h === 1 && this.dashlet.w > 4) {
          this.dynamicFontSize = 35
        } else {
          this.calculateDynamicFontSize()
        }
      }
    },

    setupResizeObserver() {
      if (typeof ResizeObserver !== 'undefined' && this.$refs.containerRef) {
        this.cleanupResizeObserver()

        this.resizeObserver = new ResizeObserver(() => {
          if (this.isAutoFontSize) {
            // Add small delay to ResizeObserver callback to ensure accurate measurements
            setTimeout(() => {
              // Check current container height
              const currentHeight = this.$refs.containerRef.clientHeight

              // If transitioning from h>1 to h=1, force a complete recalculation
              if (
                this.previousContainerHeight !== null &&
                this.previousContainerHeight > 60 &&
                currentHeight <= 60
              ) {
                // Force reset dynamicFontSize to null to ensure complete recalculation
                this.dynamicFontSize = null
              }

              // Update previous height tracking
              this.previousContainerHeight = currentHeight

              this.calculateDynamicFontSize()
            }, 50)
          }
        })

        this.resizeObserver.observe(this.$refs.containerRef)
      }
    },

    cleanupResizeObserver() {
      if (this.resizeObserver) {
        this.resizeObserver.disconnect()
        this.resizeObserver = null
      }
    },

    calculateDynamicFontSize() {
      if (
        !this.$refs.containerRef ||
        !this.$refs.textRef ||
        !this.isAutoFontSize
      ) {
        return
      }

      // Direct check for dashlet dimensions if available - this is the most reliable method
      if (this.dashlet && this.dashlet.h === 1 && this.dashlet.w > 4) {
        this.dynamicFontSize = 35
        return
      }

      // Ensure the container is visible and has dimensions before calculating
      if (
        this.$refs.containerRef.offsetWidth === 0 ||
        this.$refs.containerRef.offsetHeight === 0
      ) {
        // If container isn't ready yet, try again after a short delay
        setTimeout(() => {
          this.calculateDynamicFontSize()
        }, 100)
        return
      }

      this.$nextTick(() => {
        const containerWidth = this.$refs.containerRef.clientWidth - 16 // Subtract padding
        const containerHeight = this.$refs.containerRef.clientHeight - 16 // Subtract padding

        // Update previous height tracking
        this.previousContainerHeight = containerHeight + 16

        const text = this.displayText || 'Text to display'

        if (containerWidth <= 0 || containerHeight <= 0) {
          return
        }

        // Check if this is a height:1 dashlet with width > 4
        // We detect this by checking if the height is very small (typically under 60px for h:1)
        // and width is relatively large (assuming w>4 correlates to width > ~400px)
        const isHeight1Width4Plus = containerHeight < 60 && containerWidth > 400

        // For the special case of h=1 and w>4, use fixed 40px font size
        if (isHeight1Width4Plus) {
          // Force update to exactly 40px
          this.dynamicFontSize = 35

          // Add a safety double-check with a small delay to ensure the size sticks
          // This helps with transition cases where rendering might still be in progress
          setTimeout(() => {
            if (this.isAutoFontSize && this.$refs.containerRef) {
              const currentHeight = this.$refs.containerRef.clientHeight - 16
              const currentWidth = this.$refs.containerRef.clientWidth - 16

              if (currentHeight < 60 && currentWidth > 400) {
                this.dynamicFontSize = 35
              }
            }
          }, 100)

          // Clean up and exit early - no need for further calculations
          return
        }

        // Create temporary element to measure text
        const tempSpan = document.createElement('span')
        tempSpan.style.visibility = 'hidden'
        tempSpan.style.position = 'absolute'
        tempSpan.style.whiteSpace = 'nowrap'
        tempSpan.style.fontWeight = '600'
        tempSpan.style.lineHeight = '1'
        tempSpan.innerText = text
        document.body.appendChild(tempSpan)

        // Start with a baseline font size
        let fontSize = 16
        tempSpan.style.fontSize = `${fontSize}px`

        // Measure the ratio between container and text at baseline size
        const textWidth = tempSpan.offsetWidth
        const textHeight = tempSpan.offsetHeight

        // Calculate the maximum font size that would fit in the container
        const widthRatio = containerWidth / textWidth
        const heightRatio = containerHeight / textHeight

        // Check if this is a height:1 dashlet - special case
        // We detect this by checking if the height is very small (typically under 60px for h:1)
        const isHeight1Dashlet = containerHeight < 60

        // Check if we have a wide but short dashlet (w is large but h is small)
        const isWideShortDashlet =
          containerWidth > containerHeight * 3 || isHeight1Dashlet

        let ratio
        if (isHeight1Dashlet) {
          // Special handling for h:1 dashlets
          // For h:1, we prioritize width even more and set a fixed minimum font size
          // that is higher than normal, while still ensuring text fits horizontally

          // For h:1, we'll use 85% of the width ratio with a minimum bound
          ratio = Math.min(widthRatio * 0.85, heightRatio * 1.8) * 0.9 // Allow significantly larger than pure height constraint
        } else if (isWideShortDashlet) {
          // For wide but short dashlets, prioritize width while ensuring text still fits in height
          // Use a weighted average that favors width more but still respects height constraints
          const heightWeight = 0.3 // Give less weight to height for wide dashlets
          const widthWeight = 0.7 // Give more weight to width for wide dashlets

          // Ensure the height constraint is still respected (text won't overflow vertically)
          // but allow the font to be larger based on width
          ratio =
            Math.min(
              widthRatio * widthWeight + heightRatio * heightWeight,
              heightRatio * 1.3 // Allow up to 30% larger than pure height constraint
            ) * 0.9 // 0.9 to add a small margin
        } else {
          // Use standard approach for normal dashlets
          ratio = Math.min(widthRatio, heightRatio) * 0.9 // 0.9 to add a small margin
        }

        // Calculate the optimal font size
        fontSize = Math.round(fontSize * ratio)

        // Apply min/max constraints with specific handling for different cases
        let effectiveMinFontSize

        if (isHeight1Dashlet) {
          // For h:1 dashlets, set a higher minimum font size based on both width and height
          // but with more emphasis on width since this is our special case
          const textLength = text.length
          const widthBasedMin = containerWidth / (textLength * 0.6) // Rough estimate based on text length

          // Use a minimum that scales with container width but is bounded
          effectiveMinFontSize = Math.max(
            18, // Absolute minimum for h:1 dashlets
            Math.min(
              Math.min(widthBasedMin, 32), // Cap at 32px
              containerHeight * 0.8 // Still respect height so text doesn't overflow
            )
          )
        } else if (isWideShortDashlet) {
          effectiveMinFontSize = Math.max(
            this.MIN_FONT_SIZE,
            Math.min(24, containerHeight * 0.7)
          ) // Higher min for wide dashlets
        } else {
          effectiveMinFontSize = this.MIN_FONT_SIZE
        }

        fontSize = Math.max(
          effectiveMinFontSize,
          Math.min(fontSize, this.MAX_FONT_SIZE)
        )

        // Clean up
        document.body.removeChild(tempSpan)

        // Update the component data
        this.dynamicFontSize = fontSize
      })
    },
  },
}
</script>
