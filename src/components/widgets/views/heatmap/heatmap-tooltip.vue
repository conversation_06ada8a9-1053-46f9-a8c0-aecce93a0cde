<template>
  <div class="flex items-center" style="max-width: 35vw">
    <div class="flex flex-col flex-1 min-w-0 px-2">
      <div class="text-ellipsis">
        <span class="font-500 mr-1 text-base">
          {{
            monitor.instanceIp ||
            object.ip ||
            object.host ||
            object.target ||
            monitor.name
          }}
        </span>
      </div>
      <div class="text-ellipsis">
        <span style="font-size: 11px" class="text-neutral-regular">
          {{ monitor.instance || object.name || monitor.name }}
        </span>
      </div>
    </div>
    <div
      v-if="monitor.severity"
      class="px-4 py-2 h-full flex items-center justify-center"
      :class="(monitor.severity || '').toLowerCase()"
    >
      {{
        `${((monitor.severity || '').charAt(0) || '').toUpperCase()}${(
          (monitor.severity || '').substr(1) || ''
        ).toLowerCase()}`
      }}
    </div>

    <div
      v-if="monitor.calculatedColor"
      class="px-4 py-2 h-full flex items-center justify-center"
      style="font-size: 20px; font-weight: 700"
      :style="{ background: monitor.calculatedColor }"
    >
      {{ `${monitor.percentage}%` }}
    </div>
  </div>
</template>

<script>
import isIP from 'validator/lib/isIP'
import { objectDBWorker } from '@/src/workers'

export default {
  name: 'HeatmapTooltip',
  props: {
    monitor: {
      type: [Object],
      default: undefined,
    },
  },

  data() {
    return {
      object: {},
    }
  },

  created() {
    this.fetchObject()
  },

  methods: {
    async fetchObject() {
      let value = this.monitor
      const ip = value.monitor || value.ip || value.target
      if (ip && isIP(ip)) {
        const monitor = await objectDBWorker.getObjectByIP(ip)
        if (monitor) {
          this.object = Object.freeze(monitor)
        }
      } else if (value.category === this.$constants.CLOUD) {
        const monitor = await objectDBWorker.getObjectByName(ip)
        if (monitor) {
          this.object = Object.freeze(monitor)
        }
      } else {
        if (/^\d+$/.test(value.id)) {
          const monitor = await objectDBWorker.getObjectById(value.id)
          if (monitor) {
            this.object = Object.freeze(monitor)
          }
        } else if (ip) {
          const monitor = await objectDBWorker.getObjectByName(ip)
          if (monitor) {
            this.object = Object.freeze(monitor)
          }
        }
      }
    },
  },
}
</script>
