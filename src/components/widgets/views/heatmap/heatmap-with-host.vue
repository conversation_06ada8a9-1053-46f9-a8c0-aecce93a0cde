<template>
  <div
    class="w-full flex flex-col heatmap-box-container h-full"
    :style="{
      '--s': `${currentSize}px`,
      '--m': `${Math.floor(currentSize / 17)}px`,
      '--rows': `${grantedTotalRows}`,
    }"
    :class="{
      'flex-1': !(data && data.length && containerWidth),
      'justify-center': isCenterHeatmap,
      'items-center': isCenterHeatmap,
    }"
  >
    <div
      v-if="showCounts"
      class="w-full flex justify-center flex-wrap p-2 text-sm"
    >
      <h1 class="self-end mb-0 text-3xl font-600">{{ totalCount }}</h1>
      <h6 class="self-end ml-1 cursor-text">| Total</h6>
    </div>
    <div
      v-if="data && data.length && containerWidth"
      class="w-full"
      :class="{
        'p-2': !removePadding,
        flex: isCenterHeatmap,
        'justify-center': isCenterHeatmap,
        'items-center': isCenterHeatmap,
      }"
    >
      <HeatmapBox
        v-for="(box, index) in displayableData"
        :key="`${box.id}-${index}-${
          box.instance || ''
        }-${selectedColorPalette}`"
        :max-size="maxBoxSize"
        :initial-severity="box.severity"
        :size="currentSize"
        :object-id="box.monitor || box.id"
        :category="box.category"
        :display-host="true"
        :counter="box.counter"
        :instance="box.instance"
        :use-live-severity="shouldUseLiveSeverity"
        :data-unique-id="`${box.id}-${index}-${box.instance || ''}`"
        :group-by-columns="groupByColumns"
        :item="box"
        :active="
          sidebarMonitor &&
          `${sidebarMonitor.guid}-${sidebarMonitor.monitorInstance || ''}` ===
            `${box.id}-${box.instance || ''}`
        "
        :style="
          sidebarMonitor &&
          `${sidebarMonitor.guid}-${sidebarMonitor.monitorInstance || ''}` !==
            `${box.id}-${box.instance || ''}`
            ? 'opacity: 0.2'
            : 'opacity: 1'
        "
        :palette-color="
          selectedColorPalette ? getColorFromPalette(box.percentage) : undefined
        "
        :is-process="isProcess"
        :is-interface="isInterface"
        :is-application="isApplication"
        @severity-resolved="
          handleSeverityResolved(
            $event,
            `${box.id}-${index}-${box.instance || ''}`
          )
        "
        @mouseenter="
          showTooltip(
            $event,
            box,
            index,
            selectedColorPalette
              ? getColorFromPalette(box.percentage)
              : undefined
          )
        "
        @mouseleave="hideTooltip"
        @click="handleOpenSidebar($event, box)"
      />
      <span
        v-if="remainingData > 0"
        class="mx-1 inline-flex items-center"
        style="width: 120px"
      >
        <MTag rounded :closable="false" class="tag-primary cursor-auto"
          >+{{ remainingData | numberFormat }} more</MTag
        >
      </span>
    </div>
    <FlotoNoData
      v-else
      hide-svg
      header-tag="h5"
      variant="neutral"
      icon="exclamation-triangle"
    />
    <template v-if="activeItem">
      <div
        ref="tooltipContainerRef"
        class="ant-popover readable-content-overlay shadow-lg rounded heatmap-tooltip"
        style="z-index: 19999999"
      >
        <div
          data-popper-arrow
          class="popover-arrow always-visible"
          :class="{
            hidden: !activeItem,
            block: activeItem,
          }"
          :style="{
            background: `var(--severity-${(
              activeItem.severity || ''
            ).toLowerCase()}) !important`,
          }"
        />
        <div v-if="activeItem" class="ant-popover-content">
          <div
            role="popover"
            class="ant-popover-inner relative"
            style="padding: 0 !important"
          >
            <HeatmapTooltip :monitor="activeItem" />
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import { createPopper } from '@popperjs/core'
import HeatmapBox from './heatmap-box-with-host.vue'
import HeatmapTooltip from './heatmap-tooltip.vue'
import { COLOR_PALETTES } from '@components/widgets/constants.js'

export default {
  name: 'HeatMapWithHost',
  components: {
    HeatmapBox,
    HeatmapTooltip,
  },
  inheritAttrs: false,
  props: {
    isPreview: { type: Boolean, default: false },
    removePadding: { type: Boolean, default: false },
    size: { type: Number, default: undefined },
    isFullscreen: { type: Boolean, default: false },
    totalCount: { type: Number, default: 0 },
    data: {
      type: [Array],
      default() {
        return []
      },
    },
    maxBoxes: {
      type: Number,
      default: 250,
    },
    maxBoxSize: {
      type: Number,
      default: 70,
    },
    minBoxSize: {
      type: Number,
      default: 10,
    },
    cellHeight: {
      type: Number,
      default: 85,
    },
    sidebarMonitor: {
      type: Object,
      default: undefined,
    },
    widget: {
      type: Object,
      default: undefined,
    },
    preventCenterAlignment: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    this.__resolvedSeverityMap = {}
    return {
      activeItem: null,
      containerWidth: undefined,
      containerHeight: undefined,
    }
  },
  computed: {
    selectedColorPalette() {
      if (this.widget?.groups?.[0]?.type === 'metric') {
        return (this.widget.widgetProperties || {}).selectedColorPalette
      }
      return undefined
    },
    shouldUseLiveSeverity() {
      if (
        this.widget &&
        this.widget.groups &&
        this.widget.groups[0].type === 'availability'
      ) {
        return false
      }
      return this.$attrs['use-live-severity']
    },
    boxesInSingleRow() {
      return Math.floor(this.containerWidth / (this.currentSize + 50))
    },
    grantedTotalRows() {
      return Math.ceil(this.maxBoxes / this.boxesInSingleRow)
    },
    totalDisplayableBoxesCount() {
      return this.grantedTotalRows * this.boxesInSingleRow - 2
    },
    displayableData() {
      return this.data.slice(0, this.totalDisplayableBoxesCount)
    },
    remainingData() {
      return this.totalCount - this.totalDisplayableBoxesCount
    },
    groupByColumns() {
      const groupColumns = ['monitor', 'name', 'group']
      return groupColumns
    },
    currentSize() {
      if (this.size) {
        return this.size
      }
      if (!this.containerWidth) {
        return 50
      }
      const length = Math.min(this.totalCount, this.maxBoxes + 1)
      let range
      if (this.containerWidth <= 200) {
        range = 20
      } else if (this.containerWidth <= 500) {
        range = 30
      } else if (this.containerWidth <= 1200) {
        range = 40
      } else if (this.containerWidth <= 1600) {
        range = 45
      } else if (this.containerWidth <= 2000) {
        range = 50
      } else {
        range = 50
      }

      return Math.min(
        Math.max(
          Math.max(this.containerWidth / length, range),
          this.minBoxSize
        ),
        this.maxBoxSize
      )
    },
    showCounts() {
      return (
        this?.widget?.widgetProperties?.showCounts &&
        this.data &&
        this.data.length &&
        this.containerWidth
      )
    },
    totalRows() {
      return Math.ceil(this.totalCount / this.boxesInSingleRow)
    },
    isCenterHeatmap() {
      return this.totalRows <= 1 && !this.preventCenterAlignment
    },
    isProcess() {
      return (
        this.widget?.groups?.[0]?.counters?.every(
          (c) => c.counter?.key === 'system.process'
        ) ||
        (this.widget?.primaryResultBy?.includes('system.process') &&
          this.isMetricHeatMap)
      )
    },
    isInterface() {
      return (
        this.widget?.groups?.[0]?.counters?.some(
          (c) => c.counter?.key === 'interface'
        ) ||
        (this.widget?.primaryResultBy?.includes('interface') &&
          this.isMetricHeatMap)
      )
    },
    isApplication() {
      return this.widget?.groups?.[0]?.counters?.some((c) =>
        ['system.process', 'system.service'].includes(c.counter?.instanceType)
      )
    },
    isMetricHeatMap() {
      return this.widget?.groups?.[0]?.type === 'metric'
    },
  },
  created() {
    this.$_resizeObserver = undefined
  },
  mounted() {
    this.$_resizeObserver = new ResizeObserver((entries) => {
      if (this.$el) {
        this.containerWidth = this.$el.offsetWidth
        // this.adjustHeight()
      }
    })
    this.$_resizeObserver.observe(this.$el)
    this.containerWidth = this.$el.offsetWidth
    // setTimeout(() => {
    //   this.adjustHeight()
    // }, 500)
    this.$emit('heatmap-group-rendered', this.currentSize)
  },
  beforeDestroy() {
    this.hideTooltip()
    if (this.$_resizeObserver) {
      this.$_resizeObserver.disconnect()
      this.$_resizeObserver = null
    }
  },
  methods: {
    getColorFromPalette(percentage) {
      if (!this.selectedColorPalette || percentage === undefined) return ''

      const paletteData = COLOR_PALETTES[this.selectedColorPalette]
      if (!paletteData) return ''

      const { colors } = paletteData

      // Convert percentage to a number if it's a string
      const percentValue =
        typeof percentage === 'string'
          ? parseFloat(percentage) || 0
          : percentage

      // Return color based on percentage ranges
      if (percentValue <= 25) return colors[3]
      if (percentValue <= 50) return colors[2]
      if (percentValue <= 75) return colors[1]
      return colors[0] // 76-100%
    },
    adjustHeight() {
      if (this.data && this.data.length && this.containerWidth) {
        const totalRowsHeight = Math.max(this.$el.offsetHeight, this.cellHeight)
        const neededHeight = Math.round(
          totalRowsHeight / (this.cellHeight - 30)
        )
        this.$emit('update-height', Math.max(neededHeight, 1))
      }
    },
    handleSeverityResolved(severity, uuid) {
      this.__resolvedSeverityMap[uuid] = severity
    },
    showTooltip(e, box, index, colorPaletteColor) {
      this.activeItem = Object.freeze({
        ...box,
        severity:
          this.__resolvedSeverityMap[
            `${box.id}-${index}-${box.instance || ''}`
          ] || box.severity,
        ...(colorPaletteColor ? { calculatedColor: colorPaletteColor } : {}),
      })
      if (this.popperInstance) {
        this.popperInstance.destroy()
      }
      this.$nextTick(() => {
        if (this.$el?.closest('.vue-grid-item')) {
          this.$el.closest('.vue-grid-item').style.zIndex = 99
        }
        if (this.$refs?.tooltipContainerRef) {
          this.popperInstance = createPopper(
            e.target,
            this.$refs.tooltipContainerRef,
            {
              placement: 'auto',
              modifiers: [
                {
                  name: 'offset',
                  options: {
                    offset:
                      this.currentSize >= this.maxBoxSize ? [8, 8] : [12, 12],
                  },
                },
              ],
            }
          )
        }
      })
    },
    hideTooltip() {
      this.activeItem = null
      if (this.popperInstance) {
        if (this.$el?.closest('.vue-grid-item')) {
          this.$el.closest('.vue-grid-item').style.zIndex = 'initial'
        }
        this.popperInstance.destroy()
        this.popperInstance = null
      }
    },
    handleOpenSidebar(event, box) {
      if (!this.isFullscreen) {
        if (this.isProcess || this.isInterface) {
          this.$emit('template-drilldown', event, box)
        } else {
          this.$emit('monitor-sidebar', event, box)
        }
      }
    },
  },
}
</script>

<style lang="less" scoped>
.heatmap-box-container {
  --s: 100px; /* size  */
  --m: 4px; /* margin */
  --f: calc(1.732 * var(--s) + 4 * var(--m) - 1px);

  .box-container {
    font-size: 0;

    &::before {
      float: left;
      width: calc(var(--s) / 2 + var(--m));
      height: 120%;
      content: '';
      shape-outside: repeating-linear-gradient(
        #0000 0 calc(var(--f) - 3px),
        #000 0 var(--f)
      );
    }
  }
}
</style>
<style lang="less">
.heatmap-box-container {
  padding-bottom: calc((var(--s) / var(--rows)) + 10px);

  .with-host-container {
    .host-name {
      width: calc(var(--s) + 30px);
      text-align: center;
    }
  }

  .heatmap-box-border {
    position: relative;
    display: inline-block;
    width: var(--s);
    height: calc(var(--s) * 1.1547);
    margin: var(--m);
    margin-bottom: calc(var(--m) - var(--s) * 0.2885);
    clip-path: polygon(0% 25%, 0% 75%, 50% 100%, 100% 75%, 100% 25%, 50% 0%);
    font-size: initial;
    background: var(--border-color);

    &.with-host {
      margin: 0;
      margin-bottom: 0;
    }

    .heatmapbox {
      position: absolute;
      top: 1px;
      right: 1px;
      bottom: 1px;
      left: 1px;
      clip-path: polygon(0% 25%, 0% 75%, 50% 100%, 100% 75%, 100% 25%, 50% 0%);
      font-size: initial;
    }
  }
}

.popover-arrow {
  background: var(--dropdown-background);

  &.block.always-visible {
    display: block;
  }

  &::before {
    position: absolute;
    top: -4px;
    left: 0;
    z-index: -1;
    width: 10px;
    height: 10px;
    content: '';
    background: inherit;
    transition: transform 0.2s ease-out 0s, visibility 0.2s ease-out 0s;
    transform: rotate(45deg);
    transform-origin: center center;
  }
}

[data-popper-placement^='top'] > [data-popper-arrow] {
  bottom: 2px;
  left: -5px !important;

  // &::before {
  //   border-right: 1px solid var(--border-color);
  //   border-bottom: 1px solid var(--border-color);
  // }
}

[data-popper-placement^='left'] > [data-popper-arrow] {
  right: 5px;

  // &::before {
  //   border-top: 1px solid var(--border-color);
  //   border-right: 1px solid var(--border-color);
  // }
}

[data-popper-placement^='right'] > [data-popper-arrow] {
  left: -5px;

  // &::before {
  //   border-bottom: 1px solid var(--border-color);
  //   border-left: 1px solid var(--border-color);
  // }
}

[data-popper-placement^='bottom'] > [data-popper-arrow] {
  top: 0;
  left: -5px !important;

  // &::before {
  //   border-top: 1px solid var(--border-color);
  //   border-left: 1px solid var(--border-color);
  // }
}
</style>
