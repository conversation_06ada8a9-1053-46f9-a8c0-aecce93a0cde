<template>
  <div class="inline-flex flex-col items-center mb-2 mx-2 with-host-container">
    <div
      class="heatmap-box-border relative"
      :class="{
        'with-host': displayHost,
        'max-scale': size >= maxSize,
        active,
      }"
    >
      <div
        class="severity heatmapbox cursor-pointer"
        :class="{
          ...(severity ? { [(severity || '').toLowerCase()]: true } : {}),
          'max-scale': size >= maxSize,
          active,
        }"
        :style="{
          ...(paletteColor ? { background: paletteColor } : {}),
        }"
        v-bind="$attrs"
        v-on="listeners"
        @click="handleMonitorClick"
      />
      <div
        v-if="displayHost && iconPath"
        class="absolute"
        :style="{
          pointerEvents: 'none',
          width: `${(size > maxSize ? maxSize : size) - 25}px`,
          top: `calc(50% - ${((size > maxSize ? maxSize : size) - 25) / 2}px`,
          left: `calc(50% - ${((size > maxSize ? maxSize : size) - 25) / 2}px`,
          height: `${(size > maxSize ? maxSize : size) - 25}px`,
        }"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 512 512"
          v-html="iconPath"
        ></svg>
      </div>
    </div>
    <div
      v-if="displayHost"
      class="mb-1 text-ellipsis host-name text-center"
      style="font-size: 0.7rem"
    >
      {{ item.instance || item['object.id'] || item['monitor'] }}
    </div>
  </div>
</template>

<script>
import isIP from 'validator/lib/isIP'
import Bus from '@utils/emitter'
import { severityDBWorker, objectDBWorker } from '@/src/workers'
import { MonitorTypeLineIcons } from '@assets/icons/monitor-type-line-icons/monitor-type-line-icons'
import { getVmType, getWirelessAccessPointType } from '@utils/vm-map'

export default {
  name: 'HeatmapBoxWithHost',
  inheritAttrs: false,
  props: {
    displayHost: {
      type: Boolean,
      default: false,
    },
    groupByColumns: {
      type: Array,
      default: undefined,
    },
    objectId: { type: [Number, String], default: undefined },
    instance: { type: String, default: undefined },
    category: { type: String, default: undefined },
    item: { type: Object, default: undefined },
    size: {
      type: Number,
      default: 30,
    },
    maxSize: {
      type: Number,
      default: 50,
    },
    active: {
      type: Boolean,
      default: false,
    },
    useLiveSeverity: {
      type: Boolean,
      default: false,
    },
    isProcess: {
      type: Boolean,
      default: false,
    },
    isInterface: {
      type: Boolean,
      default: false,
    },
    isApplication: {
      type: Boolean,
      default: false,
    },
    paletteColor: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      severity: (this.item || {}).severity,
      resolvedObjectId: undefined,
    }
  },
  computed: {
    iconPath() {
      return (
        MonitorTypeLineIcons[this.item.instance] ||
        MonitorTypeLineIcons[this.item['object.type']]
      )
    },
    listeners() {
      const { click, ...listeners } = this.$listeners
      return listeners
    },
    monitorId() {
      return (
        this.resolvedObjectId || (this.item || {}).resourceId || this.objectId
      )
    },
    isVm() {
      return getVmType(this.item['object.type']) && this.item.instance
    },
    isAp() {
      return (
        getWirelessAccessPointType(this.item['object.type']) &&
        this.item.instance
      )
    },
  },
  watch: {
    severity(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.$emit('update:item', { ...this.item, severity: newValue })
      }
    },
    monitorId(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.resolveSeverity()
      }
    },
    item(newValue, oldValue) {
      if (newValue?.severity !== oldValue?.severity) {
        this.severity = newValue.severity
      }
    },
  },
  created() {
    if (this.useLiveSeverity) {
      // Bus.$on(
      //   this.$constants.EVENT_SEVERITY_UPDATED,
      //   this.monitorSeverityUpdated
      // )
      // Bus.$on(
      //   this.$constants.EVENT_COUNTER_SEVERITY_UPDATED,
      //   this.instanceSeverityUpdated
      // )
      Bus.$on(
        this.$constants.EVENT_SEVERITY_COUNTER_DB_CHANGED,
        this.resolveSeverity
      )
      this.$once('hook:beforeDestroy', () => {
        // Bus.$off(
        //   this.$constants.EVENT_COUNTER_SEVERITY_UPDATED,
        //   this.instanceSeverityUpdated
        // )
        // Bus.$off(
        //   this.$constants.EVENT_SEVERITY_UPDATED,
        //   this.monitorSeverityUpdated
        // )
        Bus.$off(
          this.$constants.EVENT_SEVERITY_COUNTER_DB_CHANGED,
          this.resolveSeverity
        )
      })

      this.resolveSeverity()
    }
  },
  methods: {
    monitorSeverityUpdated(id, severity) {
      if (this.monitorId === id) {
        if (!this.counter && !this.instance) {
          this.severity = severity
          this.$emit('severity-resolved', this.severity)
        }
      }
    },
    instanceSeverityUpdated(payload) {
      if (payload.objectId === this.monitorId) {
        if (this.instance && payload.instance === this.instance) {
          this.severity = payload.severity
          this.$emit('severity-resolved', this.severity)
        }
      }
    },
    async resolveSeverity() {
      setTimeout(async () => {
        let severityObject
        if (isIP(String(this.objectId))) {
          const monitor = await objectDBWorker.getObjectByIP(
            this.objectId,
            this.category
          )
          if (monitor) {
            this.resolvedObjectId = monitor.id
          }
        } else {
          if (!/^\d+$/.test(this.objectId)) {
            const monitor = await objectDBWorker.getObjectByName(
              this.objectId,
              this.category
            )
            if (monitor) {
              this.resolvedObjectId = monitor.id
            }
          }
        }
        severityObject = await severityDBWorker.getSeverityByEntity(
          this.monitorId,
          this.counter,
          this.instance
        )
        if ((severityObject || {}).severity) {
          this.severity = (severityObject || {}).severity
        }
        this.$emit('severity-resolved', this.severity)
      })
    },
    async handleMonitorClick() {
      let monitor
      if (isIP(String(this.objectId))) {
        monitor = await objectDBWorker.getObjectByIP(
          this.objectId,
          this.category
        )
      } else {
        if (!/^\d+$/.test(this.objectId)) {
          monitor = await objectDBWorker.getObjectByName(
            this.objectId,
            this.category
          )
        } else {
          monitor = await objectDBWorker.getObjectById(+this.objectId)
        }
      }

      this.$emit('click', {
        ...monitor,
        monitorInstance: this.instance,
        isVm: this.isVm,
        isAp: this.isAp,
        resourceType: this.isVm
          ? 'vm'
          : this.isApplication
          ? 'application'
          : this.isAp
          ? 'ap'
          : undefined,
        ...((this.isVm || this.isAp) && this.instance
          ? { name: this.instance }
          : {}),

        ...(this.isProcess
          ? {
              name: this.item.instance,
              monitorName: monitor.name,
              monitorId: monitor.id,
              status: this.item.severity,
              templateType: 'process',
            }
          : {}),

        ...(this.isInterface
          ? {
              // name: monitor.appProcesses[this.instance].name,
              monitorName: monitor.name,
              monitorId: monitor.id,
              interface: this.item.instance,
              templateType: 'interface',
              status: this.item.severity,
            }
          : {}),
        ...(this.isApplication
          ? {
              isApplication: this.isApplication,
              name: this.item.instance,
            }
          : {}),
      })
    },
  },
}
</script>
