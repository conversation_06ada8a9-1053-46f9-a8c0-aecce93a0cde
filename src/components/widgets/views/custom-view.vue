<script>
import { WidgetTypeConstants } from '@components/widgets/constants'
import ChartView from './chart-view.vue'
import DefaultGrid from './grid/default-grid.vue'

function getComponent(widgetType) {
  if (
    widgetType === WidgetTypeConstants.PIE ||
    widgetType === WidgetTypeConstants.APPLICATION_TODAY_AVAILABILITY
  ) {
    return ChartView
  }

  return DefaultGrid
}

export default {
  name: 'CustomView',
  functional: true,
  inheritAttrs: false,
  props: {
    widget: {
      type: Object,
      required: true,
    },
  },
  render(h, { data, children, props }) {
    const Element = getComponent(
      props.widget.widgetType,
      props.widget.widgetProperties.view,
      props.widget.widgetProperties.layout
    )
    return h(
      Element,
      {
        ...data,
        props,
      },
      children
    )
  },
}
</script>
