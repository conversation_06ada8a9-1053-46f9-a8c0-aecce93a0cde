<template>
  <div class="flex flex-1 h-full w-full min-h-0">
    <ChartOptions
      v-if="
        xAxisDateTime ? ((data || {}).series || []).length : categories.length
      "
      :key="widget.widgetType"
      :is-availability-chart="isAvailabilityChart"
      :stacked="stacked"
      :data="data.series"
      :widget-type="widgetType"
      :widget="widget"
      :vertical="vertical"
      :guid="guid"
      :categories="categories"
      :date-time="xAxisDateTime"
      :server-side-zoom="
        $attrs['disable-auto-fetch-server-side-zoom-data']
          ? true
          : !isPreview && !disableServerZoom
      "
      :legend-options="legendOptions"
      :line-width="lineWidth"
      v-bind="$attrs"
      @drilldown="handleDrillDown"
      @rendered="$emit('rendered', { widget: widget, data: data })"
    >
      <template v-slot="{ options }">
        <Chart
          v-if="!loading && options.series.length"
          :key="renderKey"
          :options="options"
          :sync-group="$attrs['sync-group']"
          class="flex-1 h-full w-full"
        />
        <FlotoNoData
          v-else-if="!loading"
          hide-svg
          header-tag="h5"
          variant="neutral"
          icon="exclamation-triangle"
        />
        <div v-else class="flex flex-col flex-1 items-center justify-center">
          <MLoader />
          <h5>Building Chart...</h5>
        </div>
      </template>
    </ChartOptions>
    <FlotoNoData
      v-else
      hide-svg
      header-tag="h5"
      icon="exclamation-triangle"
      variant="neutral"
    />
  </div>
</template>

<script>
import Uniq from 'lodash/uniq'
import CloneDeep from 'lodash/cloneDeep'

import Chart from '@components/chart/chart.vue'
import ChartOptions from '@/src/components/chart/options/chart-options.vue'
import { WidgetTypeConstants } from '../constants'
import { generateId } from '@/src/utils/id'
import Bus from '@utils/emitter'
import {
  convertTimeLine,
  FLOW_DRILLDOWN_IGNORED_COUNTERS,
  FLOW_DRILLDOWN_REPLACEMENT_COUNTERS_MAP,
} from '../helper'
import { appendNewFilter } from '@/src/modules/log/helpers/log.helper'
import { FILTER_CONDITION_DEFAULT_DATA } from '@components/widgets/constants'

export default {
  name: 'ChartView',
  components: {
    Chart,
    ChartOptions,
  },
  inheritAttrs: false,
  props: {
    hasCategories: { type: Boolean, default: false },
    isPreview: { type: Boolean, default: false },
    disableServerZoom: { type: Boolean, default: false },
    availabilityColors: { type: Boolean, default: false },
    widget: {
      type: Object,
      required: true,
    },
    data: {
      type: [Array, Object],
      default() {
        return []
      },
    },
    forTemplate: {
      type: Boolean,
      default: false,
    },
    disableSeverityColors: {
      type: Boolean,
      default: false,
    },
    timeRange: {
      type: Object,
      default: undefined,
    },
    xAxisForceDateTime: {
      type: Boolean,
      default: false,
    },
    incrementKey: {
      type: Number,
      default: 0,
    },
    useForceDrillDown: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: true,
      guid: generateId(),
      renderKey: 1,
      categories: [],
    }
  },
  computed: {
    hasAvailablityGroup() {
      return Boolean(this.widget.groups.find((g) => g.type === 'availability'))
    },
    hasAlertGroup() {
      return Boolean(
        this.widget.groups.find(
          (g) => g.type === 'alert' || g.type === 'policy'
        )
      )
    },
    widgetType() {
      if (
        this.widget.widgetType === WidgetTypeConstants.AVAILABILITY_TIME_SERIES
      ) {
        return WidgetTypeConstants.STACKED_HORIZONTAL_BAR
      } else if (
        this.widget.widgetType === WidgetTypeConstants.MONITOR_HEALTH
      ) {
        return WidgetTypeConstants.PIE
      } else if (this.isGaugeWidget) {
        return WidgetTypeConstants.PIE
      }
      return this.widget.widgetType
    },
    WidgetTypeConstants() {
      return WidgetTypeConstants
    },
    isAvailabilityChart() {
      if (this.disableSeverityColors) {
        return false
      }
      if (this.hasAlertGroup) {
        if (this.widget.category === WidgetTypeConstants.GAUGE) {
          return true
        }
      }
      return (
        this.availabilityColors ||
        (([
          WidgetTypeConstants.AVAILABILITY_TIME_SERIES,
          WidgetTypeConstants.MONITOR_HEALTH,
          WidgetTypeConstants.PIE,
        ].includes(this.widget.widgetType) ||
          this.widget.category !== WidgetTypeConstants.CHART) &&
          this.hasAvailablityGroup)
      )
    },
    xAxisDateTime() {
      return (
        this.xAxisForceDateTime ||
        this.widget.category === WidgetTypeConstants.CHART ||
        this.widget.widgetType === WidgetTypeConstants.HORIZONTAL_TOPN
      )
    },
    vertical() {
      const widgetType = this.widget.widgetType
      if (
        [
          WidgetTypeConstants.STACKED_VERTICAL_BAR,
          WidgetTypeConstants.VERTICAL_BAR,
        ].indexOf(widgetType) >= 0
      ) {
        return true
      }
      return false
    },
    isGaugeWidget() {
      return this.widget.category === WidgetTypeConstants.GAUGE
    },
    stacked() {
      const widgetType = this.widget.widgetType
      if (
        [
          WidgetTypeConstants.STACKED_VERTICAL_BAR,
          WidgetTypeConstants.STACKED_HORIZONTAL_BAR,
          WidgetTypeConstants.STACKED_AREA,
          WidgetTypeConstants.STACKED_LINE,
          WidgetTypeConstants.AVAILABILITY_TIME_SERIES,
        ].indexOf(widgetType) >= 0
      ) {
        return true
      }
      if (((this.widget.widgetProperties || {}).chartOptions || {}).stacked) {
        return true
      }
      return false
    },
    legendOptions() {
      if (
        ((this.widget.widgetProperties || {}).styleSetting || {}).verticalLegend
      ) {
        return { layout: 'vertical' }
      }

      if (((this.widget.widgetProperties || {}).styleSetting || {}).itemWidth) {
        return {
          itemWidth: ((this.widget.widgetProperties || {}).styleSetting || {})
            .itemWidth,
        }
      }
      return {}
    },
    lineWidth() {
      return ((this.widget.widgetProperties || {}).styleSetting || {}).lineWidth
    },
  },
  watch: {
    data: {
      handler(newValue, oldValue) {
        if (newValue !== oldValue) {
          this.buildChartData()
          if (
            this.widget.category === WidgetTypeConstants.GAUGE &&
            this.widget.groups.find((g) => g.type === 'availability')
          ) {
            this.renderKey = this.renderKey + 1
          }
        }
      },
      immediate: true,
    },
  },
  created() {
    const handleServerSideZoom = (e) => {
      if (e.guid === this.guid) {
        this.$emit('timeline-change', convertTimeLine(e.timeline))
      }
    }
    Bus.$on('chart:server:zoom', handleServerSideZoom)

    this.$once('hook:beforeDestroy', () => {
      Bus.$off('chart:server:zoom', handleServerSideZoom)
    })
  },
  methods: {
    async buildChartData() {
      this.categories = Object.freeze((this.data || {}).categories || [])
      this.loading = false
      if (this.incrementKey > 0) {
        setTimeout(
          () => (this.renderKey = this.renderKey + 1),
          this.incrementKey
        )
      }
      if (
        ((this.data || {}).series || []).length === 0 &&
        this.widget.hideEmptyWidget
      ) {
        this.$emit('hide')
      }
    },
    handleDrillDown(map, event, instanceMap) {
      const widgetGroupTypes = new Set(this.widget.groups.map((g) => g.type))
      if (
        this.forTemplate &&
        widgetGroupTypes.has('metric') &&
        !Object.keys(instanceMap || {}).length
      ) {
        if (widgetGroupTypes.size === 1 && widgetGroupTypes.has('metric')) {
          this.$emit('drilldown', {
            type: 'metric.explorer',
            counters: Object.values(map),
          })
        } else if (
          widgetGroupTypes.size === 1 &&
          widgetGroupTypes.has('flow')
        ) {
          this.$emit('drilldown', {
            type: 'flow.explorer',
            counters: Object.values(map),
          })
        } else if (widgetGroupTypes.size === 1 && widgetGroupTypes.has('log')) {
          this.$emit('drilldown', {
            type: 'log.explorer',
            counters: Object.values(map),
          })
        }
        return
      }
      if (
        widgetGroupTypes.size === 1 &&
        widgetGroupTypes.has('metric') &&
        (Array.isArray(this.widget.groups[0].resultBy)
          ? this.widget.groups[0].resultBy
          : [this.widget.groups[0].resultBy]
        ).includes('monitor')
      ) {
        this.$router.push(
          this.$modules.getModuleRoute('metric-explorer', '', {
            query: {
              metrics: encodeURIComponent(btoa(JSON.stringify(map))),
              t: encodeURIComponent(
                btoa(JSON.stringify(this.widget.timeRange))
              ),
            },
          })
        )
      } else if (
        widgetGroupTypes.size === 1 &&
        widgetGroupTypes.has('metric') &&
        Object.keys(instanceMap)?.length
      ) {
        const group = this.widget?.groups.find(
          (group) => group?.type === 'metric'
        )

        let instanceType = group.counters?.[0]?.counter?.instanceType

        const isInstanceLevelDrilldown =
          instanceType === this.widget?.groups?.[0]?.resultBy

        if (isInstanceLevelDrilldown) {
          this.$router.push(
            this.$modules.getModuleRoute('metric-explorer', '', {
              query: {
                counter: encodeURIComponent(
                  btoa(JSON.stringify(map[Object.keys(map)[0]]))
                ),
                t: encodeURIComponent(
                  btoa(JSON.stringify(this.widget.timeRange))
                ),
                instanceMap: encodeURIComponent(
                  btoa(JSON.stringify(instanceMap))
                ),
                instanceType: encodeURIComponent(
                  btoa(JSON.stringify(instanceType))
                ),
              },
            })
          )
        }
      } else if (widgetGroupTypes.size === 1 && widgetGroupTypes.has('flow')) {
        const group = this.widget.groups.find((group) => group.type === 'flow')

        const changedCounterKey =
          FLOW_DRILLDOWN_REPLACEMENT_COUNTERS_MAP[
            group?.counters?.[0]?.counter?.key
          ]

        const changedCounters =
          changedCounterKey && group?.counters?.length === 1
            ? {
                counters: [
                  {
                    aggrigateFn: 'sum',
                    counter: {
                      key: changedCounterKey,
                    },
                  },
                ],
              }
            : undefined

        if (
          group?.counters?.find((c) =>
            FLOW_DRILLDOWN_IGNORED_COUNTERS.includes(c.counter.key)
          )
        ) {
          return
        }
        let data = {
          ...group,
          category: this.widget.category,
          chartType: this.widget.widgetType,
          countersValue: changedCounterKey
            ? [changedCounterKey]
            : Uniq(Object.values(map)),
          ...(changedCounters || {}),
        }
        let resultBy = {}
        if (/^\d+$/.test(event.point.category)) {
          resultBy = event.point.series.userOptions.resultByResolver
        } else {
          resultBy =
            event.point.series.userOptions.resultByResolver[
              event.point.index
            ] || {}
        }
        if (Object.keys(resultBy).filter((k) => k !== 'event.source').length) {
          let filterMap = []

          for (const key of Object.keys(resultBy)) {
            filterMap = filterMap.concat([
              {
                operand: key,
                operator: '=',
                value: resultBy[key],
              },
            ])
          }

          data.filters.drillDownFilters = appendNewFilter(
            // Object.keys(resultBy).reduce(
            //   (prev, key) => [
            //     ...prev,
            //     {
            //       operand: key,
            //       operator: '=',
            //       value: resultBy[key],
            //     },
            //   ],
            //   []
            // ),

            filterMap,

            data.filters.drillDownFilters ||
              CloneDeep(FILTER_CONDITION_DEFAULT_DATA),

            true
          )
        }

        for (const counter of group.counters) {
          if ((counter.target || {}).entityType === 'event.source') {
            resultBy['event.source'] = [
              ...(resultBy['event.source'] || []),
              ...(Array.isArray((counter.target || {}).entities)
                ? (counter.target || {}).entities?.['event.source']?.includes(
                    (counter.target || {}).entityType
                  ) || []
                : Object.keys((counter.target || {}).entities || {}).length
                ? Object.keys((counter.target || {}).entities)
                : []),
            ]
          }
        }

        if (resultBy['event.source']) {
          data.target = {
            entityType: 'event.source',
            entities: Array.isArray(resultBy['event.source'])
              ? Uniq(resultBy['event.source'])
              : [resultBy['event.source']],
          }
        }

        if (this.$route.name === 'flow.dashboard') {
          this.$emit('drilldown', {
            type: 'flow.explorer',
            query: {
              flow: encodeURIComponent(btoa(JSON.stringify(data))),
              t: encodeURIComponent(
                btoa(JSON.stringify(this.widget.timeRange))
              ),
            },
          })

          return
        } else if (this.$route.name === 'flow.explorer') {
          return
        }
        this.$router.push(
          this.$modules.getModuleRoute('flow', 'explorer', {
            query: {
              flow: encodeURIComponent(btoa(JSON.stringify(data))),
              t: encodeURIComponent(
                btoa(JSON.stringify(this.widget.timeRange))
              ),
            },
          })
        )
      } else if (widgetGroupTypes.size === 1 && widgetGroupTypes.has('log')) {
        const group =
          this.widget.groups.find((group) => group.type === 'log') || {}
        let data = {
          ...group,
          category: this.widget.category,
          chartType: this.widget.widgetType,
          countersValue: Uniq(Object.values(map)),
        }
        let resultBy = {}
        if (/^\d+$/.test(event.point.category)) {
          resultBy = event.point.series.userOptions.resultByResolver
        } else {
          resultBy =
            event.point.series.userOptions.resultByResolver[
              event.point.index
            ] || {}
        }
        if (Object.keys(resultBy).length) {
          let filterMap = []

          for (const key of Object.keys(resultBy)) {
            filterMap = filterMap.concat([
              {
                operand: key,
                operator: '=',
                value: resultBy[key],
              },
            ])
          }

          group.filters.drillDownFilters = appendNewFilter(
            // Object.keys(resultBy).reduce(
            //   (prev, key) => [
            //     ...prev,
            //     {
            //       operand: key,
            //       operator: '=',
            //       value: resultBy[key],
            //     },
            //   ],
            //   []
            // ),
            filterMap,
            group.filters.drillDownFilters ||
              CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
            true
          )
        }
        this.$router.push(
          this.$modules.getModuleRoute('log', 'log-search', {
            query: {
              filter: encodeURIComponent(btoa(JSON.stringify(group.filters))),
              log: encodeURIComponent(btoa(JSON.stringify(data))),
              t: encodeURIComponent(
                btoa(JSON.stringify(this.widget.timeRange))
              ),
            },
          })
        )
      } else if (
        widgetGroupTypes.size === 1 &&
        widgetGroupTypes.has('availability') &&
        !this.forTemplate &&
        this.isGaugeWidget
      ) {
        this.$emit('open-gauge-drilldown-grid', {
          type: 'availability',
          widget: this.widget,
          drilldownSeries:
            event?.point?.series?.userOptions?.data[event?.point?.index],
        })
      } else if (
        widgetGroupTypes.size === 1 &&
        widgetGroupTypes.has('policy') &&
        !this.forTemplate &&
        this.isGaugeWidget
      ) {
        this.$emit('open-gauge-drilldown-grid', {
          type: 'policy',
          widget: this.widget,
          drilldownSeries:
            event?.point?.series?.userOptions?.data[event?.point?.index],
          timeRange: this.timeRange,
        })
      }

      if (this.useForceDrillDown) {
        this.$emit('force-drill-down', {
          map,
          event,
          instanceMap,
          widget: this.widget,
        })
      }
    },
  },
}
</script>
