<template>
  <FlotoScrollView>
    <MRow
      v-for="(view, index) in filteredWidgetViews"
      :key="view.key"
      :class="{ 'mt-2': index }"
      :gutter="0"
    >
      <MCol :size="12">
        <h6 class="ml-2 mb-0">
          {{ view.displayChartText }}
        </h6>
      </MCol>

      <MCol :size="12">
        <MRow
          :gutter="0"
          class="w-full overflow-scroll widget-category-type-container"
        >
          <MCol
            v-for="type in view.children"
            :key="type.key"
            :size="3"
            class="p-1 type-card"
            style="min-height: 120px"
            @click="$emit('create', type.defaultData)"
          >
            <div
              class="rounded wiget-background-color cursor-pointer h-full flex flex-col items-center justify-center"
              :style="currentStyle"
            >
              <WidgetCategoryTypeImageSelector :type="type.key" />

              <!-- <div class="px-2 w-full flex flex-col justify-end h-full relative"> -->
              <h6 style="font-size: 0.8rem">{{ type.text }}</h6>
              <!-- {{ type.description }} -->
              <!-- </div> -->
            </div>
          </MCol>
        </MRow>
      </MCol>
    </MRow>

    <MCol :size="12" class="mt-12 mb-2">
      <span class="text-neutral"> For more information: </span>
      <a
        href="https://docs.motadata.com/motadata-aiops-docs/dashboards/Widgets"
        target="_blank"
        >Widgets</a
      >
      <MIcon name="external-link" class="ml-1 text-primary" />
    </MCol>
  </FlotoScrollView>
</template>

<script>
import Trim from 'lodash/trim'

import WidgetCategoryTypeImageSelector from './widget-category-type-image-selector.vue'
import { WIDGET_CATEGORY_MAP } from './helper'
import { authComputed } from '@/src/state/modules/auth'

export default {
  name: 'WidgetCategoryTypeSelector',

  components: {
    WidgetCategoryTypeImageSelector,
  },
  props: {
    searchTerm: {
      default: undefined,
      type: String,
    },
  },
  data() {
    this.allWidgetViews = WIDGET_CATEGORY_MAP

    return {}
  },
  computed: {
    ...authComputed,
    filteredWidgetViews() {
      let widgetView = this.allWidgetsWithLicensePermissions

      if (this.searchTerm && Trim(this.searchTerm.toLowerCase()) !== '') {
        return (widgetView || [])
          .map((view) => {
            return {
              ...view,

              children: view.children.filter((c) =>
                c.text
                  .toLowerCase()
                  .includes(Trim(this.searchTerm.toLowerCase()))
              ),
            }
          })
          .filter((v) => v.children.length)
      } else {
        return widgetView
      }
    },
    currentStyle() {
      return {
        ...(this.theme !== 'black'
          ? {
              border: `1px solid var(--transparent-border-color)`,
            }
          : {}),
      }
    },
    allWidgetsWithLicensePermissions() {
      return this.allWidgetViews
        .map((type) => this.validateSection(type))
        .filter(Boolean)
    },
  },
  methods: {
    validateSection(type) {
      if (
        type.licensePermissions &&
        !this.hasLicensePermission(type.licensePermissions)
      ) {
        return null
      }

      return {
        ...type,

        children: (type.children || [])
          .map((c) => {
            if (
              c.licensePermissions &&
              !this.hasLicensePermission(c.licensePermissions)
            ) {
              return null
            }

            return c
          })
          .filter(Boolean),
      }
    },
  },
}
</script>

<style scoped lang="less">
.widget-category-type-container {
  min-height: 0;

  .type-card {
    overflow: hidden;

    .hoverable-text-container {
      transition: transform 0.3s ease-in-out;
      transform: translateY(40px);
    }

    &:hover .hoverable-text-container {
      transform: translateY(0);
    }
  }
}
</style>
