<template>
  <div>
    <MButton
      title="granularity"
      class="mx-2"
      variant="neutral-lightest"
      @click="showGranularityOptions = true"
    >
      <span class="text-primary">
        {{
          shouldShowQueryType && value.queryType === QUERY_TYPE_OPTIONS.RAW
            ? 'Raw'
            : value.value + value.unit
        }}</span
      >
    </MButton>

    <FlotoForm ref="formRef">
      <MModal
        width="25%"
        :open="showGranularityOptions"
        centered
        @hide="showGranularityOptions === false"
      >
        <template v-slot:title>
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <h5 class="text-ellipsis m-0" style="color: var(--primary)">
                Granularity
              </h5>
            </div>
            <MButton
              variant="transparent"
              :shadow="false"
              @click="cancelGranularityChange"
            >
              <MIcon name="times" />
            </MButton>
          </div>
        </template>

        <MRadioGroup
          v-if="shouldShowQueryType"
          v-model="value.queryType"
          :options="queryTypeOptions"
          as-button
          class="mb-4"
          @change="onChangeQueryType"
        />

        <GranularityInput
          v-if="value.queryType === QUERY_TYPE_OPTIONS.AGGREGATION"
          v-model="granularity"
        />

        <template v-slot:footer>
          <div class="flex justify-end space-x-2">
            <MButton variant="default" @click="cancelGranularityChange"
              >Cancel</MButton
            >
            <MButton variant="primary" @click="handleGranularityChange"
              >Apply</MButton
            >
          </div>
        </template>
      </MModal>
      <template v-slot:submit>
        <span />
      </template>
    </FlotoForm>
  </div>
</template>
<script>
import Capitalize from 'lodash/capitalize'

import GranularityInput from '@/src/components/widgets/granularity-input.vue'
import { QUERY_TYPE_OPTIONS } from '@components/widgets/constants'

export default {
  name: 'GranularitySelection',
  components: {
    GranularityInput,
  },
  model: { event: 'change' },
  props: {
    value: { type: Object, required: true },
    shouldShowQueryType: { type: Boolean, default: false },
  },
  data() {
    this.QUERY_TYPE_OPTIONS = QUERY_TYPE_OPTIONS
    this.queryTypeOptions = Object.values(QUERY_TYPE_OPTIONS).map((option) => ({
      value: option,
      text: Capitalize(option),
    }))
    return {
      showGranularityOptions: false,
      granularity: { ...this.value },
    }
  },
  watch: {
    value(newValue) {
      this.granularity = { ...this.value }
    },
  },
  methods: {
    handleGranularityChange() {
      this.$refs.formRef.validate().then((valid) => {
        if (valid) {
          this.showGranularityOptions = false
          this.$emit('change', { ...this.granularity })
        }
      })
    },
    cancelGranularityChange() {
      this.showGranularityOptions = false
      this.granularity = { ...this.value }
    },
    onChangeQueryType(queryType) {
      this.granularity.queryType = queryType
    },
  },
}
</script>
