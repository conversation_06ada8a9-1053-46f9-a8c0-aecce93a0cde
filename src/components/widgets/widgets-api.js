import Uniq from 'lodash/uniq'
import {
  buildWidgetContext,
  getWidgetResponseApi,
  makeCounter,
} from '@/src/utils/socket-event-as-api'
import api from '@api'
import { WidgetTypeConstants } from './constants'
import { transformWidget, transformWidgetForServer } from './translator'

import {
  getAllPoliciesApi,
  getAllLogPoliciesApi,
  getAllFlowPoliciesApi,
  getAllTrapPoliciesApi,
} from '@modules/settings/policy-settings/policy-api.js'

export function getWidgetsApi(type) {
  return api
    .get(`/visualization/widgets`)
    .then(({ result }) =>
      result
        .filter((widget) => widget['container.type'] === 'dashboard')
        .map(transformWidget)
    )
}

export function createWidgetApi(widget) {
  const data = {
    ...transformWidgetForServer(widget),
    'container.type': 'dashboard',
  }
  return api
    .post(`/visualization/widgets`, data)
    .then(({ id }) => ({ ...widget, id }))
}

export function updateWidgetApi(widget, transform = true) {
  const data = transform
    ? {
        ...transformWidgetForServer(widget),
        'container.type': 'dashboard',
      }
    : widget
  return api.put(`/visualization/widgets/${widget.id}`, data).then(() => widget)
}

export function deleteWidgetApi(id) {
  return api.delete(`/visualization/widgets/${id}`)
}

export function getWidgetApi(widgetId) {
  return api
    .get(`/visualization/widgets/${widgetId}`)
    .then(({ result }) => transformWidget(result))
}

export function getAlertOptionsApi(params) {
  const category = params.category || 'metric'
  if (category === 'metric') {
    return getAllPoliciesApi()
  } else if (category === 'log') {
    return getAllLogPoliciesApi()
  } else if (category === 'trap') {
    return getAllTrapPoliciesApi()
  } else {
    return getAllFlowPoliciesApi()
  }
}

export function getResultByOptionsApi(pluginId, eventCategory) {
  const excludedItem = ['message', 'timestamp', 'event.received.time']
  return api
    .get(`/misc/indexable-columns`, {
      params: {
        filter: {
          'plugin.id': eventCategory
            ? pluginId.map((id) => `${id}-${eventCategory}`)
            : pluginId,
        },
      },
    })
    .then(({ result }) =>
      Uniq((result || []).filter((i) => excludedItem.includes(i) === false))
    )
}

export function getEventCategoriesApi(timeline) {
  return getWidgetResponseApi(
    buildWidgetContext({
      category: WidgetTypeConstants.GRID,
      groupType: 'log',
      timeline: timeline,
      counters: [makeCounter('event.category', 'count')],
      resultBy: ['event.category'],
    }).generateWidgetDefinition()
  ).then((rows) => {
    return rows || []
  })
}

export function getSourceApi(groupType, getFullObject = false) {
  return api
    .get(`/misc/sources`, {
      params: {
        'group.type': groupType,
      },
    })
    .then((data) => {
      if (getFullObject) {
        return {
          result: (data.result || []).map((i) => ({
            ip: i['event.source'],
            id: i.id,
            groups: i['source.groups'],
          })),
        }
      }

      const sourceMap = {}
      const result = data.result || []

      for (const item of result) {
        sourceMap[item['event.source']] = item['event.source']
      }

      return {
        result: sourceMap,

        // (data.result || []).reduce(
        //   (prev, item) => ({
        //     ...prev,
        //     [item['event.source']]: item['event.source'],
        //   }),
        //   {}
        // ),
      }
    })
}

export function getInterfaceBySourcesApi(entities = {}, timeline) {
  return getWidgetResponseApi(
    buildWidgetContext({
      groupType: 'flow',
      groupCategory: 'flow',
      category: WidgetTypeConstants.GRID,
      widgetType: WidgetTypeConstants.GRID,
      counters: [makeCounter('flows', 'avg', 'event.source', entities)],
      timeline,
      resultBy: ['source.if.index'],
    })
      .appendToGroup('flow', {
        target: { entityType: 'event.source', entities: entities },
      })
      .generateWidgetDefinition()
  )
}

export function getCounterUniqueValues(
  groupType,
  counter,
  timeline,
  aggr = 'count',
  target
) {
  const resultByKey = counter?.split('~')?.[0]
  return getWidgetResponseApi(
    buildWidgetContext({
      groupType,
      category: WidgetTypeConstants.GRID,
      widgetType: WidgetTypeConstants.GRID,
      counters: [
        makeCounter(counter, aggr, target?.entityType, target?.entities),
      ],
      timeline,
      resultBy: resultByKey ? [resultByKey] : [],
    }).generateWidgetDefinition()
  ).then((data) => {
    return (data || [])
      .filter(
        (i) =>
          Boolean(i[counter]) ||
          Boolean(i[`${counter.replace(/~/g, '.')}.${aggr}`])
      )
      .map((item) => {
        const value =
          item[counter] || item[`${counter.replace(/~/g, '.')}.${aggr}`]
        const key = /^\d+$/.test(value)
          ? parseInt(value)
          : /^\d+\.\d+$/.test(value)
          ? parseFloat(value)
          : value

        return { text: value, key }
      })
  })
}
