<template>
  <CredentialProvider>
    <div class="flex flex-1 flex-col min-h-0">
      <MGrid
        :columns="columns"
        :debounce-time="700"
        :data="items"
        style="height: 100%; margin-top: 0"
        :search-term="searchTerm"
        :default-group="defaultGroupColumn"
        default-sort="name"
      >
        <template v-slot:state="{ item }">
          <MTag
            v-if="item.state"
            :closable="false"
            :class="(item.state || '').toLowerCase()"
          >
            {{ (item.state || '').substr(0, 1).toUpperCase() }}
          </MTag>
          <span v-else />
        </template>

        <template v-slot:status="{ item }">
          <MTag
            v-if="item.status === $constants.EVENT_SUCCESS_STATUS"
            :closable="false"
            class="provision cursor-auto"
          >
            P
          </MTag>
          <span v-else />
        </template>
        <template v-slot:type="{ item }">
          <MonitorType
            v-if="
              ['Cloud', 'Virtual Machine', 'Container'].indexOf(
                item.rediscoverJob
              ) >= 0
            "
            :type="item.type"
            class="mr-2"
            disable-tooltip
          />
        </template>
        <template v-slot:containerState="{ item }">
          <MTag
            v-if="item.containerState"
            :closable="false"
            class="tag-primary text-ellipsis cursor-auto"
          >
            {{ item.containerState || '' }}
          </MTag>
          <span v-else />
        </template>
        <template v-slot:containerStatus="{ item }">
          <MStatusTag :status="item.containerStatus" />
        </template>
        <template v-slot:name="{ item }">
          <div class="flex flex-1 min-w-0">
            <span class="text-ellipsis" v-text="item.name" />
            <ErrorInfo
              v-if="item.status === $constants.EVENT_FAIL_STATUS"
              :error="item.errorMessage"
              size="lg"
              type="secondary-red"
            />
          </div>
        </template>
        <template v-slot:wanProbe="{ item }">
          {{ wanProbeMap[item.wanProbe] }}
        </template>
        <template v-slot:interfaceStatus="{ item }">
          <MStatusTag :status="item.interfaceStatus" />
        </template>
        <template v-slot:action="{ item }">
          <ProvisionButton
            :key="item.guid"
            :item="item"
            @start-provision="$emit('start-provision', $event)"
          />
        </template>
        <template v-slot:credentialProfile="{ item }">
          <CredentialPicker v-model="item.credentialProfile" :disabled="true" />
        </template>
      </MGrid>
    </div>
  </CredentialProvider>
</template>

<script>
import MonitorType from '@components/monitor-type.vue'
import ErrorInfo from '@components/error-info.vue'
import ProvisionButton from './provision-button.vue'
import { wanProbeMap } from './rediscover-api.js'
import CredentialPicker from '@components/data-picker/credential-picker.vue'
import CredentialProvider from '@components/data-provider/credential-provider.vue'

const CLOUD_COLUMNS = [
  {
    key: 'status',
    name: ' ',
    searchable: true,
    width: '50px',
    reorderable: false,
    cellRender: 'status',
  },
  {
    key: 'type',
    name: 'Monitor Type',
    searchable: true,
    sortable: true,
    width: '100px',
    reorderable: false,
    cellRender: 'type',
  },
  {
    key: 'name',
    name: 'Name',
    sortable: true,
    searchable: true,
    reorderable: false,
    cellRender: 'name',
  },
  {
    key: 'ip',
    name: 'IP',
    sortable: true,
    searchable: true,
    reorderable: false,
    width: '120px',
  },
  {
    key: 'objectName',
    name: 'Monitor',
    sortable: true,
    searchable: true,
    reorderable: false,
    groupable: true,
  },
  {
    key: 'action',
    name: 'Action',
    width: '150px',
    align: 'right',
    reorderable: false,
    cellRender: 'action',
  },
]

const WIRELESS_COLUMNS = [
  {
    key: 'objectName',
    name: 'Monitor',
    sortable: true,
    searchable: true,
    reorderable: false,
    groupable: true,
  },
  {
    key: 'name',
    name: 'Name',
    sortable: true,
    searchable: true,
    reorderable: false,
    cellRender: 'name',
  },
  {
    key: 'ip',
    name: 'IP Address',
    sortable: true,
    searchable: true,
    reorderable: false,
  },
  {
    key: 'mac',
    name: 'MAC Address',
    sortable: true,
    searchable: true,
    reorderable: false,
  },
  {
    key: 'interfaceStatus',
    name: 'Status',
    sortable: true,
    searchable: true,
    reorderable: false,
    cellRender: 'interfaceStatus',
  },
  {
    key: 'action',
    name: 'Action',
    align: 'right',
    reorderable: false,
    cellRender: 'action',
    width: '100px',
  },
]

const VIRTUALIZATION_COLUMNS = [
  {
    key: 'status',
    name: ' ',
    searchable: true,
    width: '50px',
    reorderable: false,
    cellRender: 'status',
  },
  {
    key: 'type',
    name: 'Monitor Type',
    searchable: true,
    sortable: true,
    width: '100px',
    reorderable: false,
    cellRender: 'type',
  },
  {
    key: 'name',
    name: 'Name',
    sortable: true,
    searchable: true,
    reorderable: false,
    cellRender: 'name',
  },
  {
    key: 'objectName',
    name: 'Monitor',
    sortable: true,
    searchable: true,
    reorderable: false,
    groupable: true,
  },
  {
    key: 'vmip',
    name: 'VM IP',
    sortable: true,
    searchable: true,
    reorderable: false,
  },
  {
    key: 'interfaceStatus',
    name: 'Status',
    sortable: true,
    searchable: true,
    reorderable: false,
    width: '150px',
    cellRender: 'interfaceStatus',
  },
  {
    key: 'action',
    name: 'Action',
    width: '150px',
    align: 'right',
    reorderable: false,
    cellRender: 'action',
  },
]
const INTERFACE_COLUMNS = [
  {
    key: 'status',
    name: ' ',
    searchable: true,
    width: '50px',
    reorderable: false,
    cellRender: 'status',
  },
  {
    key: 'name',
    name: 'Name',
    sortable: true,
    searchable: true,
    reorderable: false,
    cellRender: 'name',
  },
  {
    key: 'interfaceIndex',
    name: 'Index',
    sortable: true,
    searchable: true,
    reorderable: false,
  },
  {
    key: 'objectName',
    name: 'Monitor',
    sortable: true,
    searchable: true,
    reorderable: false,
    groupable: true,
  },
  {
    key: 'interfaceip',
    name: 'Interface IP',
    sortable: true,
    searchable: true,
    reorderable: false,
  },
  {
    key: 'alias',
    name: 'Interface Alias',
    sortable: true,
    searchable: true,
    reorderable: false,
  },
  {
    key: 'linkType',
    name: 'Link Type',
    sortable: true,
    searchable: true,
    reorderable: false,
    width: '80px',
  },
  {
    key: 'interfaceDescription',
    name: 'Description',
    sortable: true,
    searchable: true,
    reorderable: false,
  },
  {
    key: 'interfaceStatus',
    name: 'Status',
    sortable: true,
    searchable: true,
    reorderable: false,
    width: '150px',
    cellRender: 'interfaceStatus',
  },
  {
    key: 'action',
    name: 'Action',
    width: '150px',
    align: 'right',
    reorderable: false,
    cellRender: 'action',
  },
]

const PROCESS_COLUMNS = [
  {
    key: 'status',
    name: ' ',
    searchable: true,
    width: '50px',
    reorderable: false,
    cellRender: 'status',
  },
  {
    key: 'name',
    name: 'Name',
    sortable: true,
    searchable: true,
    reorderable: false,
    cellRender: 'name',
  },
  {
    key: 'objectName',
    name: 'Monitor',
    sortable: true,
    searchable: true,
    reorderable: false,
    groupable: true,
  },
  {
    key: 'hint',
    name: 'Command Line',
    sortable: true,
    searchable: true,
    reorderable: false,
  },
  {
    key: 'action',
    name: 'Action',
    width: '150px',
    align: 'right',
    reorderable: false,
    cellRender: 'action',
  },
]

const SERVICE_COLUMNS = [
  {
    key: 'status',
    name: ' ',
    searchable: true,
    width: '50px',
    reorderable: false,
    cellRender: 'status',
  },
  {
    key: 'name',
    name: 'Name',
    sortable: true,
    searchable: true,
    reorderable: false,
    cellRender: 'name',
  },
  {
    key: 'objectName',
    name: 'Monitor',
    sortable: true,
    searchable: true,
    groupable: true,
    reorderable: false,
  },
  {
    key: 'systemServiceDescription',
    name: 'Service Description',
    sortable: true,
    searchable: true,
    reorderable: false,
  },
  {
    key: 'action',
    name: 'Action',
    width: '150px',
    align: 'right',
    reorderable: false,
    cellRender: 'action',
  },
]

const FILE_COLUMNS = [
  {
    key: 'status',
    name: ' ',
    searchable: true,
    width: '50px',
    reorderable: false,
    cellRender: 'status',
  },
  {
    key: 'name',
    name: 'Path',
    sortable: true,
    searchable: true,
    reorderable: false,
    cellRender: 'name',
  },
  {
    key: 'objectName',
    name: 'Monitor',
    sortable: true,
    searchable: true,
    reorderable: false,
    groupable: true,
  },
  {
    key: 'fileType',
    name: 'Type',
    sortable: true,
    searchable: true,
    reorderable: false,
    groupable: true,
  },
  {
    key: 'action',
    name: 'Action',
    width: '150px',
    align: 'right',
    reorderable: false,
    cellRender: 'action',
  },
]

const WAN_LINK_COLUMNS = [
  {
    key: 'state',
    name: ' ',
    searchable: true,
    width: '50px',
    reorderable: false,
    cellRender: 'state',
  },
  {
    key: 'host',
    name: 'Host',
    sortable: true,
    searchable: true,
    reorderable: false,
    cellRender: 'name',
  },
  // {
  //   key: 'objectName',
  //   name: 'Monitor',
  //   sortable: true,
  //   searchable: true,
  //   reorderable: false,
  // },
  {
    key: 'wanProbe',
    name: 'WAN Probe Type',
    sortable: true,
    searchable: true,
    reorderable: false,
  },
  {
    key: 'sourceIp',
    name: 'Source IP',
    sortable: true,
    searchable: true,
    reorderable: false,
  },
  {
    key: 'destinationIp',
    name: 'Destination IP',
    sortable: true,
    searchable: true,
    reorderable: false,
  },
  {
    key: 'sourceInterface',
    name: 'Source Interface',
    sortable: true,
    searchable: true,
    reorderable: false,
  },
  {
    key: 'action',
    name: 'Action',
    width: '150px',
    align: 'right',
    reorderable: false,
    cellRender: 'action',
  },
]

const HCI_COLUMNS = [
  {
    key: 'status',
    name: ' ',
    searchable: true,
    width: '50px',
    reorderable: false,
    cellRender: 'status',
  },
  {
    key: 'name',
    name: 'Path',
    sortable: true,
    searchable: true,
    reorderable: false,
    cellRender: 'name',
  },
  {
    key: 'objectName',
    name: 'Monitor',
    sortable: true,
    searchable: true,
    reorderable: false,
    groupable: true,
  },
  {
    key: 'type',
    name: 'Type',
    searchable: true,
    sortable: true,
    width: '100px',
    reorderable: false,
    cellRender: 'type',
  },
  {
    key: 'action',
    name: 'Action',
    width: '150px',
    align: 'right',
    reorderable: false,
    cellRender: 'action',
  },
]

const CONTAINER_COLUMNS = [
  {
    key: 'state',
    name: ' ',
    searchable: true,
    width: '50px',
    reorderable: false,
    // cellRender: 'state',
  },
  {
    key: 'name',
    name: 'Container',
    sortable: true,
    searchable: true,
    reorderable: false,
    cellRender: 'name',
  },
  {
    key: 'containerStatus',
    name: 'Status',
    searchable: true,
    reorderable: false,
    cellRender: 'containerStatus',
  },
  {
    key: 'containerState',
    name: 'State',
    sortable: true,
    searchable: true,
    reorderable: false,
  },
  {
    key: 'type',
    name: 'Type',
    searchable: true,
    sortable: true,
    width: '100px',
    reorderable: false,
    align: 'center',
    cellRender: 'type',
  },
  {
    key: 'credentialProfile',
    name: 'Credential Profile',
    sortable: true,
    searchable: true,
    reorderable: false,
  },
  {
    key: 'action',
    name: 'Action',
    width: '150px',
    align: 'right',
    reorderable: false,
    cellRender: 'action',
  },
]

export default {
  name: 'RediscoveryObjectGrid',
  components: {
    MonitorType,
    ErrorInfo,
    ProvisionButton,
    CredentialPicker,
    CredentialProvider,
  },
  props: {
    type: { type: String, required: true },
    searchTerm: { type: String, default: undefined },
    items: {
      type: Array,
      default() {
        return []
      },
    },
  },
  data() {
    this.wanProbeMap = wanProbeMap
    return {}
  },
  computed: {
    columns() {
      const type = this.type
      if (type === 'cloud') {
        return CLOUD_COLUMNS
      } else if (type === 'virtualization') {
        return VIRTUALIZATION_COLUMNS
      } else if (type === 'interface') {
        return INTERFACE_COLUMNS
      } else if (type === 'wireless') {
        return WIRELESS_COLUMNS
      } else if (type === 'process') {
        return PROCESS_COLUMNS
      } else if (type === 'service') {
        return SERVICE_COLUMNS
      } else if (type === 'fileDirectory') {
        return FILE_COLUMNS
      } else if (type === 'wanLink') {
        return WAN_LINK_COLUMNS
      } else if (type === 'hci') {
        return HCI_COLUMNS
      } else if (type === 'container') {
        return CONTAINER_COLUMNS
      }
      return []
    },
    defaultGroupColumn() {
      if (this.type === 'wanLink') {
        return undefined
      }
      if (this.type === 'container') {
        return undefined
      }
      return [{ field: 'objectName' }]
    },
  },
}
</script>
