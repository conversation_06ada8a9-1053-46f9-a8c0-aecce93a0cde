import Constants from '@constants'

const APPLICATION_DISCOVERY_FIELDS = [
  {
    key: 'port',
    label: 'Port',
    paramName: 'port',
    clientKey: 'port',
    validationRules: { required: true, port: true },
    supportedProtocols: [Constants.JDBC, Constants.HTTP_HTTPS],
    supportedApplications: [Constants.IBM_MQ],
    attrs: {
      id: 'port-jdbc',
      min: 1,
      max: 65535,
      precision: 0,
      type: 'number',
    },
  },
  {
    key: 'url.protocol',
    label: 'URL Type',
    paramName: 'url.protocol',
    clientKey: 'urltype',
    validationRules: { required: true },
    supportedProtocols: [Constants.HTTP_HTTPS],
    attrs: {
      placeholder: 'URL Type',
      id: 'url-protocol-id',
    },
    inputType: 'buttonGroup',
  },
  {
    key: 'database',
    label: 'Instance/Database',
    paramName: 'database',
    clientKey: 'database',
    validationRules: { required: true },
    supportedProtocols: [Constants.JDBC],
    attrs: {
      placeholder: 'Instance/Database',
      id: 'instance-jdbc',
    },
  },
  {
    key: 'queue.manager',
    label: 'Queue Manager',
    paramName: 'queue.manager',
    clientKey: 'queueManager',
    validationRules: { required: true },
    supportedApplications: [Constants.IBM_MQ],
    attrs: {
      placeholder: 'Queue Manager',
      id: 'queue-manager',
    },
  },
  {
    key: 'channel.name',
    label: 'Channel Name',
    paramName: 'channel.name',
    clientKey: 'channel',
    validationRules: { required: true },
    supportedApplications: [Constants.IBM_MQ],
    attrs: {
      placeholder: 'Channel Name',
      id: 'channel',
    },
  },
  {
    key: 'config.file.name',
    label: 'Config file name',
    paramName: 'config.file.name',
    clientKey: 'configFileName',
    validationRules: { required: true },
    supportedApplications: [Constants.LINUX_DHCP],
    attrs: {
      placeholder: 'Config file name',
      id: 'config-file-name',
    },
  },
  {
    key: 'lease.file.name',
    label: 'Lease file name',
    paramName: 'lease.file.name',
    clientKey: 'leaseFileName',
    validationRules: { required: true },
    supportedApplications: [Constants.LINUX_DHCP],
    attrs: {
      placeholder: 'Config file name',
      id: 'lease-file-name',
    },
  },
  {
    key: 'discovery.credential.profiles',
    colSize: 12,
    label: 'Credential Profiles',
    paramName: 'discovery.credential.profiles',
    clientKey: 'credentials',
    inputType: 'credentials',
    validationRules: { required: true },
    supportedProtocols: [Constants.JMS, Constants.JDBC, Constants.HTTP_HTTPS],
  },
]
export default APPLICATION_DISCOVERY_FIELDS
