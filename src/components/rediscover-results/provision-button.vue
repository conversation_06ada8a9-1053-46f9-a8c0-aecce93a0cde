<template>
  <div
    v-if="shouldShowIcon"
    class="text-neutral cursor-pointer text-lg text-right"
    title="Provision"
    @click="startProvision"
  >
    <MIcon name="provision" size="lg" />
  </div>
  <span v-else />
</template>

<script>
export default {
  name: 'ProvisionButton',
  props: {
    item: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      isInProgress: false,
    }
  },
  computed: {
    shouldShowIcon() {
      return (
        this.item.status !== this.$constants.EVENT_SUCCESS_STATUS &&
        !this.isInProgress
      )
    },
  },
  watch: {
    'item.status': {
      handler(newValue, oldValue) {
        if (newValue !== oldValue) {
          this.isInProgress = false
        }
      },
    },
  },
  methods: {
    startProvision() {
      this.isInProgress = true
      this.$emit('start-provision', this.item)
    },
  },
}
</script>
