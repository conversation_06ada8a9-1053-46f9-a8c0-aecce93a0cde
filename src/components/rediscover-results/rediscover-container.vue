<template>
  <div
    v-if="(type || '').toLowerCase() === 'container' && isRunning"
    class="flex flex-col min-h-0 flex-1 min-w-0 items-center justify-center"
  >
    <div class="w-1/2 flex flex-col justify-center items-center -mt-4">
      <DockerIcon style="width: 50%" />
      <div class="flex items-center mt-4 mr-12">
        <!-- <MIcon name="spinner-third" class="mr-2 fa-spin" /> -->
        <Spinner
          :size="25"
          primary-color="var(--neutral-light)"
          primary-alt-color="var(--neutral-dark)"
        />
        <span>Fetching container details</span>
      </div>
    </div>
  </div>
  <div v-else class="flex flex-col min-h-0 flex-1 min-w-0">
    <div class="flex justify-between">
      <MInput
        v-model="searchTerm"
        class="search-box"
        placeholder="Search"
        name="search"
      >
        <template v-slot:prefix>
          <MIcon name="search" />
        </template>
        <template v-if="searchTerm" v-slot:suffix>
          <MIcon
            name="times-circle"
            class="text-neutral-light cursor-pointer"
            @click="searchTerm = ''"
          />
        </template>
      </MInput>
      <div class="flex">
        <div v-if="type === 'application'" class="text-center">
          Discovered Applications <br />
          <span class="text-secondary-green" style="font-size: 25px">
            {{ items.length }}
          </span>
        </div>
        <div
          v-if="isRunning"
          class="ml-4"
          :class="{ '-mt-2': type === 'application' }"
        >
          <span class="mr-2 text-primary-alt">Scanning in progress...</span>
          <MButton
            shape="circle"
            variant="transparent"
            :shadow="false"
            @click="$emit('stop')"
          >
            <MIcon name="stop-circle" class="text-secondary-red" />
          </MButton>
        </div>
      </div>
    </div>
    <template v-if="items.length">
      <ApplicationContainer
        v-if="type === 'application'"
        :items="items"
        :search-term="searchTerm"
        @start-discovery="handleStartDiscovery"
      />
      <RediscoveryObjectGrid
        v-else
        :items="items"
        :search-term="searchTerm"
        :type="type"
        @start-provision="handleStartProvision"
      />
    </template>
    <div v-else class="flex flex-1 items-center justify-center">
      <FlotoNoData />
    </div>
    <FlotoConfirmModal
      v-if="showLicenseWarning"
      variant="info"
      :hide-icon="['application', 'cloud'].indexOf(type) === -1"
      open
      :success-text="`Add ${provisioningType}`"
      cancel-text="Cancel"
      @hide="showLicenseWarning = false"
      @confirm="handleTriggerProvision"
    >
      <template v-slot:icon>
        <MonitorType
          :type="
            type === 'application'
              ? provisionData.application.application
              : provisionData.object.objectType
          "
          width="50px"
          disable-tooltip
        />
      </template>
      <template v-slot:message>
        <h4 class="text-primary m-0">
          {{
            type === 'application'
              ? provisionData.application.application
              : provisionData.name
          }}
        </h4>
        <div>
          {{ $message('rediscover_confirmation', { type: provisioningType }) }}
        </div>
        <div class="flex bg-neutral-lightest rounded p-2">
          <MIcon name="info-circle" class="text-primary mr-2 mt-1" />
          <div>
            {{ $message('rediscover_warning', { type: provisioningType }) }}
          </div>
        </div>
      </template>
    </FlotoConfirmModal>
  </div>
</template>

<script>
import MonitorType from '@components/monitor-type.vue'
import DockerIcon from '@assets/images/docker.svg'
import ApplicationContainer from './application-container.vue'
import RediscoveryObjectGrid from './rediscovery-object-grid.vue'
import {
  runApplicationDiscoveryApi,
  provisionInstanceApi,
} from './rediscover-api'
import Spinner from '../spinner.vue'

export default {
  name: 'RediscoverContainer',
  components: {
    ApplicationContainer,
    RediscoveryObjectGrid,
    MonitorType,
    Spinner,
    DockerIcon,
  },
  props: {
    type: { type: String, required: true },
    items: {
      type: Array,
      default() {
        return []
      },
    },
    isRunning: { type: Boolean, default: false },
  },
  data() {
    return {
      searchTerm: '',
      provisionData: {},
      showLicenseWarning: false,
    }
  },
  computed: {
    provisioningType() {
      if (this.currentTab === 'application') {
        return 'Application'
      }
      if (this.currentTab === 'cloud') {
        return 'Instance'
      }
      return 'Instance'
    },
  },
  methods: {
    handleStartDiscovery({ application, discovery }) {
      this.provisionData = {
        application,
        discovery,
      }
      this.showLicenseWarning = true
    },
    handleStartProvision(instance) {
      if (this.currentTab === 'cloud') {
        this.provisionData = instance
        this.showLicenseWarning = true
      } else {
        this.handleTriggerProvision(instance)
      }
    },
    handleTriggerProvision(instance) {
      this.showLicenseWarning = false
      const provisionData =
        Object.keys(this.provisionData).length > 0
          ? this.provisionData
          : instance
      if (provisionData.application) {
        const foundApplications = this.items
        // const pendingStatus = this.$constants.EVENT_PENDING_STATUS
        runApplicationDiscoveryApi(
          {
            ...provisionData.application,
            ...(provisionData.discovery.discoverAll
              ? {
                  objectId: foundApplications
                    .filter(
                      (f) =>
                        // f.status === pendingStatus &&
                        f.application === provisionData.application.application
                    )
                    .map((f) => f.objectId),
                }
              : {}),
          },
          provisionData.discovery
        ).finally(() => {
          this.provisionData = {}
        })
      } else {
        provisionInstanceApi(provisionData, this.type).finally(() => {
          this.provisionData = {}
        })
      }
    },
  },
}
</script>
