<template>
  <div
    class="p-2 rediscover-row flex flex-col flex-1 min-h-0 shadow page-background-color"
    :class="{
      'cursor-pointer': application.status !== $constants.EVENT_SUCCESS_STATUS,
      'bg critical text-secondary-red':
        application.status === $constants.EVENT_FAIL_STATUS,
      'success-row': application.status === $constants.EVENT_SUCCESS_STATUS,
    }"
    :style="
      application.status === $constants.EVENT_FAIL_STATUS
        ? { borderColor: 'var(--severity-critical)' }
        : {}
    "
    @click="$emit('start-provisioning', application)"
  >
    <div class="flex min-w-0 flex-1 min-h-0">
      <div class="mr-2">
        <MonitorType
          :type="application.application"
          disable-tooltip
          width="60px"
        />
      </div>
      <div class="flex-1 min-w-0 flex flex-col min-h-0">
        <h4 class="m-0 text-primary flex items-center">
          <MTag
            v-if="application.status === $constants.EVENT_SUCCESS_STATUS"
            :closable="false"
            class="provision cursor-auto"
          >
            P
          </MTag>

          <MIcon
            v-if="application.status === $constants.EVENT_FAIL_STATUS"
            name="times-circle"
            class="text-secondary-red mr-1"
          />

          <div class="flex-1">
            {{ application.name }}
          </div>
        </h4>
        <div
          class="description text-neutral flex min-h-0 overflow-auto"
          :class="{
            'text-secondary-red': application.errorMessage,
            'text-neutral': !application.errorMessage,
          }"
        >
          <ErrorInfo :error="application.error" class="mr-1" />
          {{ application.errorMessage || application.message }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import MonitorType from '@components/monitor-type.vue'
import ErrorInfo from '@components/error-info.vue'

export default {
  name: 'ApplicationMonitor',
  components: {
    MonitorType,
    ErrorInfo,
  },
  props: {
    application: { type: Object, required: true },
  },
}
</script>

<style lang="less" scoped>
.rediscover-row {
  border: 1px solid var(--border-color);
  border-radius: 4px;

  @apply m-2;

  &.success-row {
    opacity: 0.7;
  }
}
</style>
