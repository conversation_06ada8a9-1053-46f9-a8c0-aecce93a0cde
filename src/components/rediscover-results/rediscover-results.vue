<template>
  <div v-if="isBoxOpen">
    <FlotoDrawer
      :scrolled-content="false"
      width="90%"
      :open="!isMinimized"
      wrap-class-name="rediscover-result-drawer discovery-boat-panel"
    >
      <div class="flex flex-1 flex-col min-h-0">
        <div
          class="discovery-header flex justify-between items-center border-bot"
        >
          <div v-if="!isMinimized" class="flex-1">
            <MTab
              v-model="currentTab"
              class="no-border"
              @change="handleTabChange"
            >
              <MTabPane v-for="tab in tabs" :key="tab">
                <template v-slot:tab>
                  <div class="relative">
                    {{ tabNames[tab] }}
                    <span v-if="newDiscovered[tab]" class="new-found-mark" />
                  </div>
                </template>
              </MTabPane>
            </MTab>
          </div>
          <div>
            <MButton
              variant="transparent"
              :shadow="false"
              shape="circle"
              @click="isMinimized = !isMinimized"
            >
              <MIcon
                :name="isMinimized ? 'window-restore' : 'minus'"
                :class="{ 'text-white': isMinimized }"
              />
            </MButton>
            <MButton
              variant="transparent"
              :shadow="false"
              shape="circle"
              @click="handleCloseBox"
            >
              <MIcon name="times" :class="{ 'text-white': isMinimized }" />
            </MButton>
          </div>
        </div>
        <div class="content px-4 flex flex-1 min-h-0 mt-4">
          <RediscoverContainer
            :key="currentTab"
            :items="currentTabData"
            :is-running="runningTab.indexOf(currentTab) >= 0"
            :type="currentTab"
            @stop="handleStopScanning"
          />
        </div>
      </div>
    </FlotoDrawer>
    <div
      v-if="isMinimized"
      id="rediscovery-minimized-box"
      key="minimized-box"
      class="discovery-boat-panel minimized"
    >
      <div class="discovery-header flex justify-between items-center">
        <div class="mr-4">
          <h5 class="text-white m-0">Rediscover Result</h5>
        </div>
        <div class="action-box">
          <MButton
            variant="transparent"
            :shadow="false"
            shape="circle"
            @click="isMinimized = !isMinimized"
          >
            <MIcon
              :name="isMinimized ? 'window-restore' : 'minus'"
              :class="{ 'text-white': isMinimized }"
            />
          </MButton>
          <MButton
            id="close-rediscovery-box-id"
            variant="transparent"
            :shadow="false"
            shape="circle"
            @click="handleCloseBox"
          >
            <MIcon name="times" :class="{ 'text-white': isMinimized }" />
          </MButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Uniq from 'lodash/uniq'
import Throttle from 'lodash/throttle'
import Invert from 'lodash/invert'
import UniqBy from 'lodash/uniqBy'
import FindIndex from 'lodash/findIndex'
import PickBy from 'lodash/pickBy'
import Identity from 'lodash/identity'
import { REDISCOVER_TYPE_NAME_MAPPING } from '@modules/settings/monitoring/rediscover-setting-api'
import RediscoverContainer from './rediscover-container.vue'
import {
  transformResult,
  getRediscoverScheduleResultsApi,
} from './rediscover-api'
import Bus from '@utils/emitter'

const TAB_ORDER = Object.keys(REDISCOVER_TYPE_NAME_MAPPING)

export default {
  name: 'RediscoverResults',
  components: {
    RediscoverContainer,
  },
  data() {
    this.data = {
      application: [],
      cloud: [],
      virtualization: [],
      interface: [],
      wireless: [],
      process: [],
      service: [],
      fileDirectory: [],
      wanLink: [],
      hci: [],
      container: [],
    }
    return {
      isBoxOpen: false,
      isMinimized: false,
      currentTab: null,
      runningTab: [],
      runningSchedulerId: [],
      newDiscovered: {},
      applicationProvision: {
        isProvisioning: false,
      },
      currentTabData: [],
      tabs: [],
      // loading: true,
    }
  },
  computed: {
    tabNames() {
      return {
        application: 'Application',
        cloud: 'Cloud',
        virtualization: 'Virtualization',
        hci: 'HCI',
        interface: 'Interface',
        wireless: 'Access Point',
        process: 'Process',
        service: 'Service',
        fileDirectory: 'File/Directory',
        wanLink: 'WAN Link',
        container: 'Docker',
      }
    },
  },
  watch: {
    $route: function () {
      this.isMinimized = true
    },
    currentTab: 'updateCurrentTabDataRaw',
  },
  created() {
    Bus.$on(this.$constants.REDISCOVER_START_EVENT, this.handleStartHandler)
    Bus.$on(
      this.$constants.REDISCOVER_PROGRESS_EVENT,
      this.handleProgressHandler
    )
    Bus.$on(this.$constants.REDISCOVER_STOP_EVENT, this.handleStopHandler)
    Bus.$on(
      this.$constants.REDISCOVER_APP_PROGRESS_EVENT,
      this.handleAppProvisionResultHandler
    )
    Bus.$on(
      this.$constants.DISCOVERY_OBJECT_PROVISION_EVENT,
      this.handleInstanceProvisionResultHandler
    )
    Bus.$on(
      this.$constants.DISCOVERY_INSTANCE_PROVISION_EVENT,
      this.handleInstanceProvisionResultHandler
    )
    Bus.$on('trigger.rediscover.view.results', this.showPreviousRunResults)
    this.$once('hook:beforeDestroy', () => {
      Bus.$off(this.$constants.REDISCOVER_START_EVENT, this.handleStartHandler)
      Bus.$off(
        this.$constants.REDISCOVER_PROGRESS_EVENT,
        this.handleProgressHandler
      )
      Bus.$off(this.$constants.REDISCOVER_STOP_EVENT, this.handleStopHandler)
      Bus.$off(
        this.$constants.REDISCOVER_APP_PROGRESS_EVENT,
        this.handleAppProvisionResultHandler
      )
      Bus.$off(
        this.$constants.DISCOVERY_OBJECT_PROVISION_EVENT,
        this.handleInstanceProvisionResultHandler
      )
      Bus.$off(
        this.$constants.DISCOVERY_INSTANCE_PROVISION_EVENT,
        this.handleInstanceProvisionResultHandler
      )
      Bus.$off('trigger.rediscover.view.results', this.showPreviousRunResults)
    })
    this.updateCurrentTabData = Throttle(this.updateCurrentTabDataRaw, 1000, {
      leading: true,
    })
  },
  methods: {
    updateCurrentTabDataRaw() {
      if (this.isBoxOpen) {
        this.currentTabData = Object.freeze(this.data[this.currentTab])
      }
    },
    handleBoxOpen(payload, keepMinimized = false) {
      // don't open box if autoprovisioned on
      if (payload.autoProvisioned) {
        return
      }
      this.isBoxOpen = true
      this.isMinimized = keepMinimized
    },
    showPreviousRunResults(item) {
      getRediscoverScheduleResultsApi(item.id).then((data) => {
        const tabType = Invert(REDISCOVER_TYPE_NAME_MAPPING)[data.rediscoverJob]
        if (tabType === undefined || tabType === null) {
          return
        }
        const tabs = Uniq([...this.tabs, tabType])
        this.tabs = TAB_ORDER.filter((t) => tabs.indexOf(t) >= 0)
        if (!this.currentTab || this.currentTab !== tabType) {
          this.currentTab = tabType
        } else if (this.currentTab !== tabType && data.result.length) {
          this.newDiscovered = {
            ...this.newDiscovered,
            [tabType]: true,
          }
        }
        this.data = {
          ...this.data,
          [tabType]: Object.freeze(data.result),
        }
        this.$nextTick(() => {
          this.handleBoxOpen(item)
          this.updateCurrentTabData()
        })
      })
    },
    handleStartHandler(payload) {
      if (payload?.['auto.provision.status'] === 'yes') {
        return
      }
      const tabType = Invert(REDISCOVER_TYPE_NAME_MAPPING)[
        payload['rediscover.job']
      ]
      if (tabType === undefined || tabType === null) {
        return
      }
      this.runningSchedulerId = UniqBy(
        [
          ...this.runningSchedulerId,
          { id: payload['event.scheduler'], type: tabType },
        ],
        'id'
      )
      if (!['wanLink'].includes(tabType)) {
        this.runningTab = Uniq([...this.runningTab, tabType])
      } else {
        this.$successNotification({
          message: 'Success',
          description: payload.message,
        })
      }
      const tabs = Uniq([...this.tabs, tabType])
      this.tabs = TAB_ORDER.filter((t) => tabs.indexOf(t) >= 0)
      this.data = {
        ...this.data,
        [tabType]: [],
      }
      if (!this.currentTab) {
        this.currentTab = tabType
      }
      this.updateCurrentTabData()

      this.$nextTick(() => {
        this.handleBoxOpen(
          {
            ...payload,
            autoProvisioned:
              (payload?.['scheduler.context'] || {})[
                'auto.provision.status'
              ] === 'yes',
          },
          tabType === 'wanLink'
        )
      })
    },
    handleProgressHandler(payload) {
      // this.loading = false
      if (!payload['rediscover.job']) {
        return this.$errorNotification({
          message: 'Error',
          description: payload.message,
        })
      }

      const type = payload['rediscover.job']
      const tabType = Invert(REDISCOVER_TYPE_NAME_MAPPING)[type]

      if (
        tabType !== 'wanLink' &&
        [
          this.$constants.EVENT_TIMEOUT_STATUS,
          this.$constants.EVENT_FAIL_STATUS,
          this.$constants.EVENT_ABORT_STATUS,
        ].includes(payload.status)
      ) {
        return
      }
      const isFailedWanLink =
        payload.status === this.$constants.EVENT_FAIL_STATUS &&
        tabType === 'wanLink'

      if (tabType === undefined || tabType === null) {
        return
      }
      if (!this.tabs.includes(tabType)) {
        const tabs = Uniq([...this.tabs, tabType])
        this.tabs = TAB_ORDER.filter((t) => tabs.indexOf(t) >= 0)
      }
      let instances
      if (tabType === 'application') {
        instances = [transformResult(payload)]
      } else {
        if (payload.objects) {
          instances = payload.objects.map((i) => transformResult(i, payload))
        } else {
          instances = [transformResult(payload)]
        }
      }
      this.data = {
        ...this.data,
        [tabType]: [...this.data[tabType], ...instances],
      }
      if (
        this.currentTab !== tabType &&
        this.newDiscovered[tabType] === false
      ) {
        this.newDiscovered = {
          ...this.newDiscovered,
          [tabType]: true,
        }
      }

      if (isFailedWanLink) {
        this.$errorNotification({
          message: 'Error',
          description: payload.message,
          key: 'formError',
        })
        this.isBoxOpen = false

        return
      }

      this.$nextTick(() => {
        if (!this.isBoxOpen) {
          this.handleBoxOpen(payload, true)
        }

        if (isFailedWanLink) {
          this.isBoxOpen = false
        }
      })
      if (!this.currentTab) {
        this.currentTab = tabType
      }
      if (this.currentTab === tabType) {
        this.updateCurrentTabData()
      }
      // remove from running tab
      // if ((payload.status || '').length) {
      //   this.$nextTick(() => {
      //     const runningTab = this.runningTab
      //     if (runningTab.indexOf(tabType) >= 0) {
      //       this.runningTab = runningTab.filter((r) => r !== tabType)
      //     }
      //     this.runningSchedulerId = this.runningSchedulerId.filter(
      //       ({ type }) => type !== tabType
      //     )
      //   })
      // }
    },
    handleStopHandler(payload) {
      const tabType = Invert(REDISCOVER_TYPE_NAME_MAPPING)[
        payload['rediscover.job']
      ]
      if (tabType === undefined || tabType === null) {
        return
      }
      const runningTab = this.runningTab
      if (runningTab.indexOf(tabType) >= 0) {
        this.runningTab = runningTab.filter((r) => r !== tabType)
      }
      this.runningSchedulerId = this.runningSchedulerId.filter(
        ({ type }) => type !== tabType
      )
    },
    handleAppProvisionResultHandler(payload) {
      const applications = this.data.application
      const index = FindIndex(applications, {
        application: payload['metric.type'],
        objectId: payload['metric.object'],
        eventScheduler: payload['event.scheduler'],
      })
      if (payload.status === this.$constants.EVENT_SUCCESS_STATUS) {
        this.handleRemoveItem(index, 'application', payload)
      } else {
        this.handleUpdateItem(index, 'application', payload)
      }
    },
    handleInstanceProvisionResultHandler(payload) {
      const type = payload['rediscover.job']
      const tabType = Invert(REDISCOVER_TYPE_NAME_MAPPING)[type]
      if (tabType === undefined || tabType === null) {
        return
      }
      const instances = this.data[tabType]
      let index
      if (
        payload['object.category'] &&
        payload['object.category'] === this.$constants.CLOUD
      ) {
        index = FindIndex(instances, {
          object: PickBy(
            {
              objectAccountId: payload['object.account.id'],
              objectName: payload['object.name'],
              objectRegion: payload['object.region'],
              objectType: payload['object.type'],
              objectTarget: payload['object.target'],
              eventScheduler: payload['event.scheduler'],
            },
            Identity
          ),
        })
      } else if (tabType === 'container') {
        index = FindIndex(instances, (item) => {
          if (payload['event.scheduler']) {
            if (item.eventScheduler !== payload['event.scheduler']) {
              return false
            }
          }
          if (item.guid !== payload[this.$constants.UI_EVENT_UUID]) {
            return false
          }
          if (
            item.object['container.id'] !== payload.object['container.id'] ||
            item.object['object.name'] !== payload.object['object.name']
          ) {
            return false
          }
          return true
        })
      } else {
        index = FindIndex(instances, {
          object: payload.object,
          guid: payload[this.$constants.UI_EVENT_UUID],
          eventScheduler: payload['event.scheduler'],
        })
      }
      if (payload.status === this.$constants.EVENT_SUCCESS_STATUS) {
        this.handleRemoveItem(index, tabType, payload)
      } else {
        this.handleUpdateItem(index, tabType, payload)
      }
    },
    handleUpdateItem(index, tabType, payload) {
      const data = this.data[tabType] || []
      if (index !== -1) {
        this.data = {
          ...this.data,
          [tabType]: Object.freeze([
            ...data.slice(0, index),
            {
              ...data[index],
              ...(payload.status === this.$constants.EVENT_FAIL_STATUS
                ? {
                    error: payload.error,
                    errorMessage: payload.message || data[index].message,
                  }
                : {
                    message: payload.message || data[index].message,
                    error: undefined,
                    errorMessage: undefined,
                  }),
              status: payload.status,
            },
            ...data.slice(index + 1),
          ]),
        }
        if (this.currentTab === tabType) {
          this.updateCurrentTabData()
        }
      }
    },
    handleRemoveItem(index, tabType, payload) {
      const data = this.data[tabType] || []
      if (index !== -1) {
        this.data = {
          ...this.data,
          [tabType]: Object.freeze([
            ...data.slice(0, index),
            ...data.slice(index + 1),
          ]),
        }
        if (this.currentTab === tabType) {
          this.updateCurrentTabData()
        }
      }
    },
    handleCloseBox() {
      this.isMinimized = true
      this.$nextTick(() => {
        this.isBoxOpen = false
        this.data = {
          application: [],
          cloud: [],
          virtualization: [],
          interface: [],
          wireless: [],
          process: [],
          service: [],
          fileDirectory: [],
          wanLink: [],
          hci: [],
          container: [],
        }
        this.currentTabData = []
        this.currentTab = null
        this.runningTab = []
        this.runningSchedulerId = []
        this.tabs = []
      })
    },
    handleStopScanning() {
      const currentTab = this.currentTab
      Bus.$emit('server:event', {
        'event.type': this.$constants.REDISCOVER_STOP_EVENT,
        'event.context': {
          'rediscover.job': REDISCOVER_TYPE_NAME_MAPPING[currentTab],
          ids: this.runningSchedulerId
            .filter(({ type }) => type === currentTab)
            .map(({ id }) => id),
        },
      })
    },
    handleTabChange(key) {
      this.newDiscovered = {
        ...this.newDiscovered,
        [key]: false,
      }
    },
  },
}
</script>

<style lang="less" scoped>
.new-found-mark {
  position: absolute;
  width: 7px;
  height: 7px;
  background-color: var(--secondary-red);
  border-radius: 50%;
}
</style>
<style lang="less">
.rediscover-result-drawer {
  .ant-drawer-header:not(.visible) {
    display: none;
  }
}
</style>
