<template>
  <CredentialProvider
    v-if="credentialProfileProtocol"
    :search-params="credentialSearchParams"
  >
    <FlotoDrawerForm
      :open="open"
      @submit="handleFormSubmit"
      @cancel="closeForm"
      @reset="handleResetForm"
    >
      <template v-if="monitor" v-slot:header>
        {{ monitor.application }} - App Discovery
      </template>

      <!-- form -->
      <MRow v-if="monitor">
        <MCol :size="12" class="mb-4">
          <FlotoFormItem :label="monitorApplicationLable">
            <MSwitch
              id="run-all"
              v-model="formData.discoverAll"
              class="mr-2 mb-5"
              checked-children="ON"
              un-checked-children="OFF"
            />
          </FlotoFormItem>
        </MCol>
      </MRow>

      <MRow v-if="fields.length">
        <MCol
          v-for="field in fields"
          :key="field.key"
          :size="field.colSize || 6"
          class="material-input"
        >
          <FlotoFormItem
            v-if="field.inputType === 'dropdown'"
            :label="field.label"
            :rules="field.validationRules"
            v-bind="field.attrs"
          >
            <FlotoDropdownPicker
              v-model="formData[field.clientKey]"
              :options="field.options"
            />
          </FlotoFormItem>

          <FlotoFormItem
            v-else-if="field.inputType === 'buttonGroup'"
            v-bind="field.attrs"
            :label="field.label"
            :rules="field.validationRules"
          >
            <MRadioGroup
              v-model="formData[field.clientKey]"
              as-button
              :options="urlTypeOptions"
            />
          </FlotoFormItem>

          <FlotoFormItem
            v-else-if="field.inputType === 'credentials'"
            :label="field.label"
            :info-tooltip="$message('discovery_credential_help')"
            v-bind="field.attrs"
            :rules="{
              required:
                monitor.application === $constants.RABBITMQ ? true : false,
            }"
          >
            <CredentialPicker
              id="credentials"
              v-model="formData[field.clientKey]"
              multiple
              allow-create
              :available-protocols="credentialProfileProtocol"
              :default-form-data="{
                credentialProfileProtocol: credentialProfileProtocol.length
                  ? credentialProfileProtocol[0]
                  : undefined,
              }"
            />
          </FlotoFormItem>
          <FlotoFormItem
            v-else
            v-model="formData[field.clientKey]"
            :label="field.label"
            :rules="field.validationRules"
            v-bind="field.attrs"
          />
        </MCol>
      </MRow>

      <MRow v-if="monitor">
        <MCol :size="12">
          <FlotoFormItem label="Discover Using Agent">
            <MSwitch
              id="discovered-with-agent"
              v-model="formData.discoveryByAgent"
              class="mr-2 mb-5"
              checked-children="ON"
              un-checked-children="OFF"
            />
          </FlotoFormItem>
        </MCol>
      </MRow>

      <template v-slot:actions="{ submit, reset }">
        <MButton id="run" class="ml-2" @click="submit">Run</MButton>
        <MButton id="reset" class="ml-2" variant="default" @click="reset">
          Reset
        </MButton>
      </template>
    </FlotoDrawerForm>
  </CredentialProvider>
</template>
<script>
import Omit from 'lodash/omit'
import Intersection from 'lodash/intersection'
import DefaultPorts from '@src/statics/application-ports'
import CredentialProvider from '@components/data-provider/credential-provider.vue'
import CredentialPicker from '@components/data-picker/credential-picker.vue'
import AVAILABLE_FIELDS from './application-discovery-fields'
import { URL_TYPES } from '@data/monitor'
export default {
  name: 'ApplicationDiscoveryForm',
  components: {
    CredentialProvider,
    CredentialPicker,
  },
  inject: { applicationTypeContext: { default: { options: new Map() } } },
  props: {
    open: { type: Boolean, default: false },
    monitor: { type: Object, default: undefined },
  },
  data() {
    this.urlTypeOptions = URL_TYPES
    return {
      formData: {},
      credentialProfileProtocol: undefined,
    }
  },
  computed: {
    fields() {
      if (!this.monitor) {
        return []
      }
      const protocol = this.credentialProfileProtocol
      const application = this.monitor.application
      let fields = AVAILABLE_FIELDS.filter((field) => {
        if (
          field.supportedApplications &&
          field.supportedApplications.indexOf(application) >= 0
        ) {
          return true
        }
        if (field.supportedProtocols && field.supportedProtocols.length) {
          // if (
          //   application === this.$constants.BIND9 &&
          //   field.inputType === 'credentials'
          // ) {
          //   return false
          // } else {
          return Intersection(protocol, field.supportedProtocols).length
          // }
        }

        return false
      })
      // make credential profile optional for bind9
      if (application === this.$constants.BIND9) {
        fields = fields.map((f) => {
          if (f.inputType === 'credentials') {
            return {
              ...f,
              validationRules: { required: false },
            }
          }
          return f
        })
      }
      return fields
    },
    monitorApplicationLable() {
      return `Scan All Servers`
    },
    credentialSearchParams() {
      return {
        key: 'credential.profile.protocol',
        value: this.credentialProfileProtocol,
      }
    },
  },
  watch: {
    open(newValue) {
      if (newValue) {
        const application = this.monitor.application
        if (this.applicationTypeContext.options.has(application)) {
          const protocol =
            this.applicationTypeContext.options.get(application).protocol
          if (protocol.includes(this.$constants.HTTP)) {
            this.credentialProfileProtocol = [this.$constants.HTTP_HTTPS]
          } else {
            this.credentialProfileProtocol = protocol
          }
        }
        if (!this.credentialProfileProtocol) {
          this.$errorNotification({
            message: 'Error',
            description: `Couldn't find protocol for application ${this.monitor.application}`,
          })
          return
        }
        this.setDefaultFormData()
      }
    },
  },
  methods: {
    setDefaultFormData() {
      this.formData = {}
      if (DefaultPorts[this.monitor.application]) {
        this.formData = {
          ...this.formData,
          ...(this.hasAnyProtocol([
            this.$constants.JDBC,
            this.$constants.HTTP_HTTPS,
          ])
            ? {
                port: DefaultPorts[this.monitor.application],
                urltype: 'http',
              }
            : {}),
        }
      }
    },
    hasAnyProtocol(protocols) {
      return Intersection(protocols, this.credentialProfileProtocol).length
    },
    handleResetForm() {
      this.setDefaultFormData()
    },
    handleFormSubmit() {
      const context = {}
      const formData = this.formData
      this.fields.forEach((field) => {
        context[field.paramName] = formData[field.clientKey]
      })
      const keysToOmit = this.fields.map((f) => f.clientKey)
      setTimeout(() => {
        this.$emit('next', {
          protocol: this.credentialProfileProtocol,
          credentials: formData.credentials,
          ...Omit(formData, keysToOmit),
          context: Omit(context, ['discovery.credential.profiles']),
        })
      }, 400)
    },
    closeForm() {
      this.$emit('abort')
    },
  },
}
</script>
