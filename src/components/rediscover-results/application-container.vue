<template>
  <ApplicationTypeProvider>
    <div class="flex flex-1 min-h-0 flex-col pt-2 rounded -mx-2">
      <div class="overflow-auto flex-1 min-h-0 flex flex-col __panel">
        <RecycleScroller :items="chunkedApps" key-field="guid" :item-size="110">
          <template v-slot="{ item }">
            <MRow :gutter="0" style="height: 110px">
              <MCol
                v-for="application in item.items"
                :key="application.guid"
                class="flex flex-col h-full"
                :size="4"
              >
                <Application
                  :application="application"
                  @start-provisioning="startProvisioningApplication"
                />
              </MCol>
            </MRow>
          </template>
        </RecycleScroller>
      </div>
      <ApplicationDiscoveryForm
        :open="provisioningData.showProvisionForm"
        :monitor="provisioningData.provisioningApplication"
        @next="handleStartDiscovery"
        @abort="abortApplicationDiscovery"
      />
    </div>
  </ApplicationTypeProvider>
</template>

<script>
import { generateId } from '@utils/id'
import Filter from 'lodash/filter'
import FindIndex from 'lodash/findIndex'
import Chunk from 'lodash/chunk'
import ApplicationTypeProvider from '@components/data-provider/application-type-provider.vue'
import ApplicationDiscoveryForm from './application-discovery-form.vue'
import Application from './application.vue'

export default {
  name: 'ApplicationContainer',
  components: {
    ApplicationTypeProvider,
    ApplicationDiscoveryForm,
    Application,
  },
  props: {
    searchTerm: {
      type: String,
      default: undefined,
    },
    items: {
      type: Array,
      default() {
        return []
      },
    },
  },
  data() {
    return {
      provisioningData: {
        showProvisionForm: false,
        provisioningApplication: undefined,
      },
    }
  },
  computed: {
    filteredItems() {
      const items = this.items
      const search = (this.searchTerm || '').toLowerCase()
      if (search.length > 0) {
        return Filter(
          items,
          (item) =>
            (item.name || '').toLowerCase().indexOf(search) >= 0 ||
            (item.message || '').toLowerCase().indexOf(search) >= 0
        )
      }
      return items
    },
    chunkedApps() {
      return Chunk(this.filteredItems, 3).map((chunk) => ({
        guid: generateId(),
        items: chunk,
      }))
    },
  },
  watch: {
    items(newValue) {
      const provisioningApplication =
        this.provisioningData.provisioningApplication
      if (provisioningApplication) {
        const index = FindIndex(newValue, {
          guid: provisioningApplication.guid,
        })
        if (index === -1) {
          this.provisioningData = {
            provisioningApplication: undefined,
            showProvisionForm: false,
          }
        }
      }
    },
  },
  methods: {
    startProvisioningApplication(application) {
      if (application.status !== this.$constants.EVENT_SUCCESS_STATUS) {
        this.provisioningData = {
          provisioningApplication: application,
          showProvisionForm: true,
        }
      }
    },
    handleStartDiscovery(data) {
      this.$emit('start-discovery', {
        discovery: data,
        application: this.provisioningData.provisioningApplication,
      })
      this.provisioningData = {
        provisioningApplication: undefined,
        showProvisionForm: false,
      }
    },
    abortApplicationDiscovery() {
      this.provisioningData = {
        provisioningApplication: undefined,
        showProvisionForm: false,
      }
    },
  },
}
</script>
