import Omit from 'lodash/omit'
import CloneDeep from 'lodash/cloneDeep'
import <PERSON> from 'cytoscape'
import <PERSON>bounce from 'lodash/debounce'
import panzoom from 'cytoscape-panzoom'
import Popper from 'cytoscape-popper'
import ViewUtilities from 'cytoscape-view-utilities'
import dagre from 'cytoscape-dagre'
import nodeHtmlLabel from 'cytoscape-node-html-label'
import ExpandCollapseNode from 'cytoscape-expand-collapse'
import tippy from 'tippy.js'
import Vue from 'vue'
import Bus from '@utils/emitter'
import { DefaultStyle } from './graph-style'

ViewUtilities(Cy)
ExpandCollapseNode(Cy)
nodeHtmlLabel(Cy)
Cy.use(Popper)
Cy.use(dagre)
panzoom(Cy)

export default class Cytoscape {
  currentLayout = null

  _animateOffset = 0

  get cy() {
    return this._cy
  }

  set cy(any) {
    throw new Error(`cy is readonly`)
  }

  _cy = null

  constructor(options, tooltip) {
    this.tooltipComponent = tooltip.component
    this.tooltipTrigger = tooltip.trigger || 'click'
    this.theme = tooltip.theme
    this.parentContainer = options.container
    this.animateEdges =
      options.animateEdges === undefined ? true : options.animateEdges
    this.parent = tooltip.parent
    this.defaultZoom = options.zoom
    this.defaultPan = CloneDeep(options.pan)
    this._cy = new Cy(Omit(options, ['layout', 'pan']))
    this._cy.nodeHtmlLabel(options.nodeHtmlLabels || [])
    this._cy.panzoom()

    this._highlightUtils = this._cy.viewUtilities({
      highlightStyles: [],
      setVisibilityOnHide: false, // whether to set visibility on hide/show
      setDisplayOnHide: true, // whether to set display on hide/show
      zoomAnimationDuration: 400, // default duration for zoom animation speed
      neighbor: function (ele) {
        return ele.neighborhood()
      },
      neighborSelectTime: 100,
      lassoStyle: { lineColor: '#d67614', lineWidth: 3 },
      marqueeZoomCursor: 'se-resize',
      isShowEdgesBetweenVisibleNodes: true,
    })

    this._highlightUtils.disableLassoMode()
    this._highlightUtils.disableMarqueeZoom()

    this.expandCollapseApi = this._cy.expandCollapse({
      layoutBy: options.layout,
      animate: false,
      undoable: false,
      zIndex: 1,
      cueEnabled: false,
    })

    this.expandCollapseApi.collapseAll()

    const layout = options.layout
    if (options.zoom !== undefined) {
      layout.zoom = options.zoom
      layout.fit = false
    }
    this.currentLayout = this._cy.layout(layout)
    this.currentLayout.pon('layoutready').then((event) => {
      this.fixNodeSizeAndFonts()
      setTimeout(() => {
        if (this.defaultPan) {
          this._cy.pan(CloneDeep(options.pan))
        }
        if (this.defaultZoom) {
          this._cy.zoom(options.zoom)
        }
        this.bindEvents()
      })
    })
    this.currentLayout.run()
  }

  async setLayout(layout) {
    this._cy.stop()
    if (this.currentLayout) {
      this.currentLayout.stop()
    }
    this.currentLayout = this._cy.layout(layout)
    await this.currentLayout.run()
  }

  setStyle(stylesheet = []) {
    this._cy.style([...DefaultStyle, ...stylesheet])
  }

  updateTheme(theme) {
    this._cy.nodes().data('theme', theme)
  }

  resize() {
    const zoom = this._cy.zoom()
    this._cy.resize()
    if (this.defaultPan === undefined) {
      this._cy.fit()
    }
    this._cy.zoom(zoom)
  }

  fit() {
    this._cy.fit()
  }

  center(arg) {
    this._cy.center(arg)
  }

  panzoom(options) {
    this._cy.panzoom('destroy')
    this._cy.panzoom(options)
  }

  startAnimatedEdgesInterval = () => {
    this._animateOffset++
    const collection = this._cy.$('edge.animating')
    collection.animate(
      {
        style: { 'line-dash-offset': this._animateOffset * -1 },
      }
      // {
      //   duration: 300,
      //   complete: () => {
      //     if (this._animateOffset > 0) {
      //       this.startAnimatedEdgesInterval()
      //     }
      //   },
      // }
    )
    this._animationFrame = requestAnimationFrame(
      this.startAnimatedEdgesInterval
    )
  }

  stopAnimatedEdgesInterval = () => {
    if (this._animationFrame) {
      cancelAnimationFrame(this._animationFrame)
      this._animationFrame = null
    }
    this._animateOffset = 0
    this._cy.edges().stop()
    this._cy.edges().clearQueue()
  }

  applySearchTerm = Debounce((searchTerm) => {
    // if (this._filteredCollection) {
    //   this._filteredCollection.restore()
    // }
    const filteredNodes = this._cy.$('node').filter((node) => {
      if (node.selected()) {
        return true
      }
      if (
        node.data('name').toLowerCase().indexOf(searchTerm.toLowerCase()) >=
          0 ||
        node
          .data('monitorName')
          .toLowerCase()
          .indexOf(searchTerm.toLowerCase()) >= 0 ||
        (node.data('ip') || '')
          .toLowerCase()
          .indexOf(searchTerm.toLowerCase()) >= 0
      ) {
        return true
      }
      return false
    })
    this._cy.elements().removeClass('semitransparent')
    this._filteredCollection = this._cy
      .elements()
      .difference(filteredNodes)
      .difference(filteredNodes.outgoers())
      .difference(filteredNodes.incomers())
    this._filteredCollection.addClass('semitransparent')
    this._highlightUtils.zoomToSelected(filteredNodes)
  }, 700)

  removeSearchTerm = Debounce(() => {
    // this._filteredCollection.restore()
    // this._filteredCollection.removeClass('semitransparent')
    this._cy.elements().removeClass('semitransparent')
    this._filteredCollection = null
    this._cy.animate({
      fit: true,
      center: true,
      easing: 'cubic-bezier(.19,1.02,.61,1.03)',
      duration: 700,
    })
  }, 700)

  bindEvents() {
    this._cy.on('mouseover', 'node', (evt) => {
      if (!evt.target.data().disabled) {
        document.body.style.cursor = 'pointer'
      }
      this.highlightNode(evt.target)
      if (this.tooltipTrigger !== 'click') {
        if (evt.target.data().tooltip) {
          // if (!this.isNodeZoomed(evt.target.source())) {
          //   return
          // }
          this._makeTooltip(evt.target, {
            component: this.tooltipComponent,
            theme: this.theme,
            parent: this.parent,
          })
        }
      }
    })

    this._cy.on('mouseout', 'node', (evt) => {
      document.body.style.cursor = 'default'

      if (evt.target.selected()) {
        return
      }
      if (this.tooltipTrigger !== 'click') {
        this.removeTooltip()
      }
      this.clearHighlight()
    })

    this._cy.on('mouseover', 'edge', (evt) => {
      if (!evt.target.isEdge()) {
        return
      }
      if (evt.target.hasClass('cy-expand-collapse-meta-edge')) {
        return
      }
      this._cy.elements().addClass('semitransparent')
      evt.target.source().addClass('highlight animating')
      evt.target.targets().addClass('highlight animating')
      evt.target.addClass('highlight animating')
      this.stopAnimatedEdgesInterval()
      this.startAnimatedEdgesInterval()
    })

    this._cy.on('mouseout', 'edge', (evt) => {
      if (!evt.target.isEdge()) {
        return
      }
      this.clearHighlight()

      if (this.tooltipTrigger !== 'click') {
        this.removeTooltip()
      }
    })

    this._cy.on(
      this.tooltipTrigger === 'click' ? 'click' : 'mouseover',
      'edge',
      (evt) => {
        if (!evt.target.isEdge()) {
          return
        }
        if (evt.target.hasClass('cy-expand-collapse-meta-edge')) {
          return
        }
        if (evt.target.data().tooltip) {
          // if (!this.isNodeZoomed(evt.target.source())) {
          //   return
          // }
          this._makeTooltip(evt.target, {
            component: this.tooltipComponent,
            theme: this.theme,
            parent: this.parent,
          })
        }
      }
    )

    this._cy.nodes().on('unselect', () => {
      this._cy.emit('node:selection:clear')
      this.clearHighlight()
    })

    this._cy.on('click', (e) => {
      if (e.target === this._cy) {
        this.clearSelection()
      }
    })
    this._cy.on('layoutstop', () => {
      requestAnimationFrame(() => {
        this.fixNodeSizeAndFonts()
      })
    })
    this._cy.on('zoom', () => {
      requestAnimationFrame(() => {
        this.fixNodeSizeAndFonts()
        this.saveViewPort()
      })
    })

    this._cy.on('pan', () => {
      requestAnimationFrame(() => {
        this.fixNodeSizeAndFonts()
        this.saveViewPort()
      })
    })

    this._cy.nodes().on('dragfreeon', (e) => {
      this.savePostion(e.target)
    })
  }

  saveViewPort() {
    Bus.$emit('topology:zoom:update', {
      zoom: this._cy.zoom(),
      pan: CloneDeep(this._cy.pan()),
    })
  }

  savePostion(element) {
    const item = element.json()
    if (item && item.position) {
      Bus.$emit('topology:position:update', {
        id: item.data.rawId,
        ...item.position,
      })
    }
  }

  fixNodeSizeAndFonts() {
    let currentZoom = this._cy.zoom()
    let zoomFactor = 1 / currentZoom
    let fontSize = 12 * zoomFactor
    let textMarginY = 5 * zoomFactor

    const w = this._cy
      .elements('node')
      .difference(this._cy.elements('node.root'))
      .difference(this._cy.elements('node.selected'))
      .first()
      .renderedBoundingBox().h
    if (Math.floor(w) <= 30) {
      fontSize = 0
    }

    this._cy
      .style()
      .selector('edge')
      .style('font-size', fontSize)
      .selector('node')
      .style('font-size', fontSize)
      .style('text-margin-y', textMarginY)
      .update()

    this._cy.elements().data({ zoom: currentZoom })
  }

  getZoomedNode() {
    return this._cy.nodes().filter(this.isNodeZoomed)
  }

  isNodeZoomed(node) {
    const renderedBoundingBox = node.renderedBoundingBox()
    return renderedBoundingBox.w > 40 || renderedBoundingBox.h > 40
  }

  addNode(node) {
    const cyNode = Cytoscape.buildNode(node)
    this._cy.add(cyNode)
    const connections = node.connections || []
    connections.forEach((edge) => {
      const registeredNode = this._cy.nodes(
        `#${String(edge.target).replace(/[.\s]/g, '')}`
      )
      if (registeredNode.size() > 0) {
        this.addEdge(
          cyNode.data.id,
          String(edge.target).replace(/[.\s]/g, ''),
          edge.options
        )
      } else {
        this._cy.pon(
          'add',
          `#${String(edge.target).replace(/[.\s]/g, '')}`,
          () => {
            this.addEdge(
              cyNode.data.id,
              String(edge.target).replace(/[.\s]/g, ''),
              edge.options
            )
          }
        )
      }
    })
  }

  getNodeById(id) {
    const cyNode = this._cy.$(`#${String(id).replace(/[.\s]/g, '')}`)
    if (cyNode.size() > 0) {
      return cyNode.first()
    }
    return null
  }

  updateNode(node) {
    const cyNode = this._cy.$(`#${String(node.id).replace(/[.\s]/g, '')}`)
    if (cyNode.size() > 0) {
      const currentNodeJson = cyNode.first().json()
      const classes = currentNodeJson.classes
        .split(' ')
        .filter(
          (c) => c !== (currentNodeJson.data.severity || '').toLowerCase()
        )
      const updatedNode = Cytoscape.buildNode({
        ...node,
        classes: `${node.classes || ''} ${classes.join(' ')}`,
      })
      cyNode.first().json(updatedNode)
    }
  }

  removeNode(node) {
    const cyNode = this._cy.$(`#${String(node.id).replace(/[.\s]/g, '')}`)
    this._cy.remove(cyNode)
  }

  highlightNode(cyNode) {
    this._cy.elements().addClass('semitransparent')
    cyNode.addClass('highlight')
    const dashedEdges = cyNode.neighborhood().filter((e) => e.data('dashed'))
    cyNode.neighborhood().difference(dashedEdges).addClass('highlight')
    dashedEdges.addClass('dashhighlight')
    if (this.animateEdges) {
      cyNode.neighborhood().difference(dashedEdges).addClass('animating')
      dashedEdges.addClass('animating')
      this.stopAnimatedEdgesInterval()
      this.startAnimatedEdgesInterval()
    }
  }

  clearHighlight() {
    this.stopAnimatedEdgesInterval()
    const collection = this._cy.elements()
    collection.removeClass('semitransparent')
    collection.removeClass('highlight animating')
    collection.removeClass('dashhighlight animating')
    if (this._filteredCollection) {
      this._filteredCollection.addClass('semitransparent')
    }
    requestAnimationFrame(() => {
      if (this._cy.nodes('.selected').length) {
        this.highlightNode(this._cy.nodes('.selected').first())
      }
    })
  }

  selectNodeById(id) {
    const cyNodeCollection = this._cy.$(`#${id}`)
    if (cyNodeCollection.size() > 0) {
      const cyNode = cyNodeCollection.eq(0)
      if (cyNode.selected()) {
        return
      }
      this._cy.batch(() => {
        this.highlightNode(cyNode, false)
        cyNode.select()
        cyNode.addClass('selected')
        requestAnimationFrame(() => {
          this.fixNodeSizeAndFonts()
        })
      })
    }
  }

  clearSelection() {
    this._cy.batch(() => {
      this._cy.collection('nodes').unselect().removeClass('selected')
      this.clearHighlight()
      requestAnimationFrame(() => {
        this.fixNodeSizeAndFonts()
      })
    })
  }

  addEdge(source, target, options = {}) {
    const straightEdge = this._cy.edges(
      `#${source}${Cytoscape.edgeSeperator}${target}`
    )
    if (straightEdge.size() > 0) {
      return
    }
    const reverseEdge = this._cy.edges(
      `#${target}${Cytoscape.edgeSeperator}${source}`
    )
    if (reverseEdge.size() > 0) {
      return
    }
    const cyEdge = Cytoscape.buildEdge(source, target, options)
    this._cy.add(cyEdge)
  }

  removeEdge(id) {
    const cyEdge = this._cy.edges(id)
    if (cyEdge.size() > 0) {
      cyEdge.remove()
    }
  }

  showDashedEdges() {
    this._cy.batch(() => {
      this._cy.edges('.dashed-edge').removeClass('hidden')
    })
  }

  hideDashedEdges() {
    this._cy.batch(() => {
      this._cy.edges('.dashed-edge').addClass('hidden')
    })
  }

  removeTooltip() {
    this._cy.elements().forEach((edge) => {
      if (edge.tippy) {
        edge.tippy.hide()
        edge.tippy.destroy()
        edge.tippy = null
      }
      if (edge.vnode) {
        edge.vnode.$destroy()
        edge.vnode = null
      }
    })
  }

  hideTooltip() {
    this._cy.edges().forEach((edge) => {
      if (edge.tippy) {
        edge.tippy.hide()
      }
    })
  }

  _makeTooltip(element, { component, theme, parent }) {
    let ref = element.popperRef()
    const renderedNode = new Vue({
      ...component,
      parent,
      propsData: {
        type: element.isEdge() ? 'edge' : 'node',
        source: element.source().data(),
        target: element.target().data(),
        options: element.data(),
      },
    }).$mount()
    element.vnode = renderedNode
    element.tippy = tippy(parent.$el, {
      getReferenceClientRect: ref.getBoundingClientRect,
      content: () => renderedNode.$el,
      appendTo: () => document.body,
      arrow: true,
      allowHTML: true,
      interactive: true,
      interactiveBorder: 10,
      duration: [100, 1000],
      trigger: 'manual',
      maxWidth: 'none',
      theme: 'tippy-tooltip',
    })
    element.tippy.show()
  }

  destroy() {
    this.tooltipComponent = null
    this.parentContainer = null
    this._cy.removeAllListeners()
    this.removeTooltip()
    if (this.currentLayout) {
      this.currentLayout.stop()
    }
    this.currentLayout = null
    this._cy.destroy()
  }
}

Cytoscape.edgeSeperator = '-medge-seperator-'

Cytoscape.buildNode = (node) => {
  return {
    data: {
      ...Omit(node, ['connections']),
      monitorId: node.id,
      zoom: 1,
      id: String(node.id).replace(/[.\s]/g, ''),
    },
    classes: `${node.type || ''} ${(node.severity || '').toLowerCase()} ${
      node.classes || ''
    }`,
  }
}
Cytoscape.buildEdge = (source, target, options = {}) => {
  return {
    group: 'edges',
    data: {
      id: `${String(source).replace(/[.\s]/g, '')}${
        Cytoscape.edgeSeperator
      }${String(target).replace(/[.\s]/g, '')}${
        options.level ? `${Cytoscape.edgeSeperator}${options.level}` : ''
      }`,
      zoom: 1,
      source: String(source).replace(/[.\s]/g, ''),
      target: String(target).replace(/[.\s]/g, ''),
      ...options,
    },
    classes: [
      ...(options.classes || []),
      ...(options.arrow ? ['has-arrow'] : []),
      ...(options.dashed ? ['dashed-edge'] : []),
    ],
  }
}
