<template>
  <div
    class="flex-1 flex-col flex h-full relative w-full"
    :class="{ 'has-bg': !disableBg }"
  >
    <div ref="graph">
      <slot />
    </div>
  </div>
</template>

<script>
import CloneDeep from 'lodash/cloneDeep'
import Debounce from 'lodash/debounce'
import Omit from 'lodash/omit'
import {
  UserPreferenceComputed,
  UserPreferenceMethods,
} from '@state/modules/user-preference'
import Cytoscape from './cytoscape'
import { DefaultStyle } from './graph-style'
import Bus from '@utils/emitter'

const defaultLayoutOptions = {
  preset: {
    name: 'preset',
    positions(node) {
      return node.data().position
    },
    animate: false,
  },
  breadthfirst: {
    name: 'breadthfirst',
    animate: false,
    spacingFactor: 0.9,
    maximal: true,
  },
  netroute: {
    name: 'dagre',
    rankDir: 'LR',
    rankSep: 100,
    fit: true,
    animate: false,
    animationDuration: 200,
    avoidOverlap: true,
    ranker: 'network-simplex',
  },
}

export default {
  name: 'Graph',
  inheritAttrs: false,
  props: {
    layout: {
      type: [String],
      default() {
        return 'random'
      },
    },
    view: {
      type: String,
      default: 'full',
    },
    hideEdgesOnViewport: {
      type: Boolean,
      // eslint-disable-next-line
      default: false,
    },
    textureOnViewport: {
      type: Boolean,
      // eslint-disable-next-line
      default: true,
    },
    motionBlur: {
      type: Boolean,
      // eslint-disable-next-line
      default: true,
    },
    center: {
      type: Boolean,
      // eslint-disable-next-line
      default: true,
    },
    fit: {
      type: Boolean,
      default: false,
    },
    tooltipComponent: {
      type: Object,
      default: undefined,
    },
    graphStyle: {
      type: Array,
      default() {
        return []
      },
    },
    nodes: {
      type: Array,
      default() {
        return []
      },
    },
    edges: {
      type: Array,
      default() {
        return []
      },
    },
    showDashedEdges: {
      type: Boolean,
      default: false,
    },
    disableBg: {
      type: Boolean,
      default: false,
    },
    searchTerm: {
      type: String,
      default: undefined,
    },
    rootKey: {
      type: String,
      required: true,
    },
    disablePreferencePersist: {
      type: Boolean,
      default: false,
    },
    omitDefaultStyle: {
      type: Boolean,
      default: false,
    },
    autoResize: {
      type: Boolean,
      default: false,
    },
    animateEdges: {
      type: Boolean,
      // eslint-disable-next-line
      default: true,
    },
    tooltipTrigger: {
      type: String,
      default: undefined,
    },
    nodeHtmlLabels: {
      type: Array,
      default: undefined,
    },
  },
  data() {
    return {
      progress: 0,
      calculatingLayout: false,
      selectedNodes: [],
    }
  },
  computed: {
    ...UserPreferenceComputed,
    progressDisplay() {
      return Math.ceil(this.progress)
    },
  },
  watch: {
    async layout(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.cy.cy.minZoom(1e-50)
        await this.runLayout()
        this.makeGraphReady()
      }
    },
    graphStyle(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.cy.setStyle(this.graphStyle)
      }
    },
    showDashedEdges(newValue) {
      if (newValue) {
        this.handleShowDashedEdges()
      } else {
        this.handleHideDashedEdges()
      }
    },
    searchTerm(newValue, oldValue) {
      if (newValue !== oldValue) {
        if (newValue) {
          this.cy.applySearchTerm(newValue)
        } else {
          this.cy.removeSearchTerm()
        }
      }
    },
  },
  async mounted() {
    let layout = defaultLayoutOptions[this.layout]
    if (!layout) {
      layout = { name: this.layout }
    }
    layout.ready = () => this.layoutReady()
    layout.tick = () => this.layoutProgress()
    layout.stop = () => this.layoutStop()

    const style = getComputedStyle(this.$el)
    this.$refs.graph.style.height = style.height
    this.$refs.graph.style.width = style.width

    this.cy = new Cytoscape(
      {
        container: this.$refs.graph,
        animateEdges: this.animateEdges,
        nodeHtmlLabels: this.nodeHtmlLabels,
        elements: {
          nodes: this.nodes,
          edges: this.edges,
        },
        hideEdgesOnViewport: this.hideEdgesOnViewport,
        textureOnViewport: this.textureOnViewport,
        motionBlur: this.motionBlur,
        style: [
          ...this.graphStyle,
          ...(this.omitDefaultStyle ? [] : DefaultStyle(this.theme)),
        ],
        layout,
        minZoom: 0.1,
        maxZoom: 3,
        pixelRatio: 1.0,
        ...(this.topologyPreference[this.rootKey] &&
        !this.disablePreferencePersist &&
        this.topologyPreference[this.rootKey].zoom !== undefined
          ? { zoom: this.topologyPreference[this.rootKey].zoom }
          : {}),
        ...(this.topologyPreference[this.rootKey] &&
        !this.disablePreferencePersist &&
        this.topologyPreference[this.rootKey].pan !== undefined
          ? { pan: CloneDeep(this.topologyPreference[this.rootKey].pan) }
          : {}),
      },
      {
        component: this.tooltipComponent,
        theme: this.theme,
        parent: this,
        trigger: this.tooltipTrigger || 'click',
      }
    )

    if (
      !this.disablePreferencePersist &&
      (this.topologyPreference[this.rootKey] || {}).pan === undefined
    ) {
      this.cy.fit()
    }

    if (this.layout === 'netroute') {
      setTimeout(() => {
        this.cy.fit()
      }, 100)
    }

    if (this.showDashedEdges) {
      this.handleShowDashedEdges()
    } else {
      this.handleHideDashedEdges()
    }

    this.cy.cy.nodes().on('unselect', (event) => {
      this.$emit('clear')
    })
    this.cy.cy.nodes().on('expandcollapse.afterexpand', function (event) {
      this.cy().fit()
      this.cy().center()
    })
    this.cy.cy.nodes().on('expandcollapse.aftercollapse', function (event) {
      this.cy().fit()
      this.cy().center()
    })
    this.cy.cy.nodes().on('click', (event) => {
      this.cy.clearSelection()
      this.cy.cy.edges().removeClass('selected')
      const data = event.target.data()
      if (data.type === 'group') {
        let api = this.cy.cy.expandCollapse('get')
        if (api.isExpandable(event.target)) {
          this.$emit('node-expanded', event.target.data())
          setTimeout(() => {
            api.expand(event.target)
          })
        }
        return
      }
      if (data.disabled) {
        event.target.unselect()
        return
      }
      const zoom = this.cy.cy.zoom()
      this.cy.cy.animate({
        zoom: zoom < 2 ? 2 : zoom,
        center: { eles: event.target },
        easing: 'ease-in-out',
        duration: 1500,
      })
      event.target.addClass('selected')
      this.cy.highlightNode(event.target, false)
      this.$emit('selected', event.target.data())
    })

    this.cy.cy.edges().on('click', (event) => {
      if (event.target.hasClass('cy-expand-collapse-meta-edge')) {
        return
      }
      const zoom = this.cy.cy.zoom()
      this.cy.cy.edges().removeClass('selected')
      event.target.addClass('selected')
      this.cy.cy.animate({
        zoom: zoom < 2 ? 2 : zoom,
        center: { eles: event.target },
        easing: 'ease-in-out',
        duration: 1500,
      })
      this.$emit('selected-edge', {
        ...event.target.data(),
        boxType: 'edge',
        source: event.target.source().data(),
        target: event.target.target().data(),
      })
    })

    this.cy.cy.on('layoutready', () => {
      this.makeGraphReady()
    })
    this.cy.cy.nodes().on('box', (event) => {
      const data = event.target.data()
      if (data.disabled) {
        event.target.unselect()
        return
      }
      event.target.addClass('selected')
      this.cy.highlightNode(event.target)
      this.selectedNodes.push(event.target.data())
    })

    this.viewPortUpdatedDebounced = Debounce(this.viewPortUpdated, 1000, {
      trailing: true,
    })
    this.positionUpdatedDebounced = Debounce(this.positionUpdated, 1000, {
      trailing: true,
    })
    Bus.$on('topology:zoom:update', this.viewPortUpdatedDebounced)
    Bus.$on('topology:position:update', this.positionUpdatedDebounced)
    Bus.$on('close-edge-tooltip', this.removeToolTip)
    if (this.autoResize) {
      this.resizeObserver = new ResizeObserver(() => {
        if (this.$refs.graph && this.cy.cy) {
          const style = getComputedStyle(this.$el)
          this.$refs.graph.style.height = style.height
          this.$refs.graph.style.width = style.width
          this.cy.cy.resize()
          this.cy.cy.fit()
        }
      })

      this.resizeObserver.observe(this.$el)
    }
  },
  beforeDestroy() {
    Bus.$off('topology:zoom:update', this.viewPortUpdatedDebounced)
    Bus.$off('topology:position:update', this.positionUpdatedDebounced)
    Bus.$off('close-edge-tooltip', this.removeToolTip)
    if (this.cy) {
      this.cy.destroy()
    }
    if (this.resizeObserver) {
      this.resizeObserver.disconnect()
      this.resizeObserver = undefined
    }
  },
  methods: {
    ...UserPreferenceMethods,
    collapseCollection(collection) {
      this.cy.expandCollapseApi.collapseRecursively(collection)
    },
    viewPortUpdated(event) {
      if (this.disablePreferencePersist) {
        return
      }
      this.updateTopologyPreference({
        id: this.rootKey,
        data: event,
        view: this.view,
      })
    },
    removeToolTip() {
      if (this.cy) {
        this.cy.hideTooltip()
      }
    },
    positionUpdated(event) {
      if (this.disablePreferencePersist) {
        return
      }
      let selectedNodesPositions = {}
      if (this.selectedNodes.length > 0) {
        this.selectedNodes.forEach((element) => {
          let node = this.cy.getNodeById(element.id)
          if (node) {
            selectedNodesPositions[element.rawId] = node.position()
          }
        })
      }
      this.updateTopologyPreference({
        id: this.rootKey,
        view: this.view,
        data: {
          ...(this.selectedNodes.length > 0
            ? selectedNodesPositions
            : { [event.id]: Omit(event, ['id']) }),
        },
      })
      this.selectedNodes = []
    },
    handleShowDashedEdges() {
      this.cy.showDashedEdges()
    },
    handleHideDashedEdges() {
      this.cy.hideDashedEdges()
    },
    layoutReady() {
      // this.calculatingLayout = true
      this.progress = 0
    },
    layoutProgress(progress) {
      this.progress = progress * 100
    },
    layoutStop() {
      this.calculatingLayout = false
      this.progress = 100
    },
    async runLayout() {
      let layout = defaultLayoutOptions[this.layout]
      if (!layout) {
        layout = { name: this.layout }
      }
      this.layoutReady()
      layout.ready = () => {
        this.layoutReady()
      }
      layout.tick = (...args) => this.layoutProgress(...args)
      layout.stop = () => this.layoutStop()
      await this.cy.setLayout(layout)
    },
    makeGraphReady() {
      if (this.center) {
        this.cy.center()
      }
      if (this.fit) {
        this.cy.fit()
      }
      this.$emit('ready')
    },
    async addNode(node) {
      this.cy.addNode(node)
    },
    removeNode(node) {
      this.cy.removeNode(node)
    },
    updateNode(node) {
      this.cy.updateNode({
        ...node,
        classes: `${node.type} ${this.filledNode ? 'filled' : ''}`,
      })
    },
    updateSeverity(monitorId, severity) {
      const node = this.cy.getNodeById(monitorId)
      if (node) {
        this.cy.updateNode({
          ...node.data(),
          severity,
        })
      }
    },
    updateTheme(theme) {
      this.cy.updateTheme(theme)
    },
    clearSelection() {
      if (this.cy) {
        this.cy.clearSelection()
      }
    },
  },
}
</script>

<style lang="less">
.has-bg {
  background-image: var(--graph-bg);
}

.cy-panzoom-reset {
  span.icon {
    position: relative;
    display: block;
    height: 100%;

    &::before {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      content: ' ';
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 448 512'%3E%3Cpath fill='%2314212d' d='M0 180V56c0-13.3 10.7-24 24-24h124c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12H64v84c0 6.6-5.4 12-12 12H12c-6.6 0-12-5.4-12-12zM288 44v40c0 6.6 5.4 12 12 12h84v84c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12V56c0-13.3-10.7-24-24-24H300c-6.6 0-12 5.4-12 12zm148 276h-40c-6.6 0-12 5.4-12 12v84h-84c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h124c13.3 0 24-10.7 24-24V332c0-6.6-5.4-12-12-12zM160 468v-40c0-6.6-5.4-12-12-12H64v-84c0-6.6-5.4-12-12-12H12c-6.6 0-12 5.4-12 12v124c0 13.3 10.7 24 24 24h124c6.6 0 12-5.4 12-12z'%3E%3C/path%3E%3C/svg%3E");
    }
  }
}

.cy-panzoom-zoom-in {
  span.icon {
    position: relative;
    display: block;
    height: 100%;

    &::before {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      content: ' ';
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 448 512'%3E%3Cpath fill='%2314212d' d='M416 208H272V64c0-17.67-14.33-32-32-32h-32c-17.67 0-32 14.33-32 32v144H32c-17.67 0-32 14.33-32 32v32c0 17.67 14.33 32 32 32h144v144c0 17.67 14.33 32 32 32h32c17.67 0 32-14.33 32-32V304h144c17.67 0 32-14.33 32-32v-32c0-17.67-14.33-32-32-32z'%3E%3C/path%3E%3C/svg%3E");
    }
  }
}

.cy-panzoom-zoom-out {
  span.icon {
    position: relative;
    display: block;
    height: 100%;

    &::before {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      content: ' ';
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 448 512'%3E%3Cpath fill='%2314212d' d='M416 208H32c-17.67 0-32 14.33-32 32v32c0 17.67 14.33 32 32 32h384c17.67 0 32-14.33 32-32v-32c0-17.67-14.33-32-32-32z'%3E%3C/path%3E%3C/svg%3E");
    }
  }
}
</style>
