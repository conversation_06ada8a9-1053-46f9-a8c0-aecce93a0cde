import Mem from 'mem'
import Color from 'color'
import { MonitorTypeLineIcons } from '@assets/icons/monitor-type-line-icons/monitor-type-line-icons'
import { colorMap } from '@data/monitor'

export const Theme = {
  white: {
    text: '#364658',
    edge: '#7b8fa5',
    nodeBackground: '#e7f5fb',
  },
  black: {
    text: ' #fff',
    edge: '#a5bad0',
    nodeBackground: 'rgb(40, 40, 40)',
  },
}

export const makeSvg = Mem(
  (ele, avoidMissing = false, ignoreRootTx = false) => {
    const zoom = ele.data('zoom')
    const type = ele.data().type
    let iconPath
    if (MonitorTypeLineIcons[type]) {
      iconPath = MonitorTypeLineIcons[type]
    } else {
      if (avoidMissing) {
        iconPath = ''
      } else {
        iconPath = MonitorTypeLineIcons.missing
      }
    }
    let size // may need to calculate this yourself
    if (Math.floor(ele.renderedBoundingBox().h) <= 30) {
      size = 0
    } else {
      const scale = 1 / zoom
      size = (ele.hasClass('selected') ? 20 : 15) * zoom * scale
    }
    let width = size
    let height = size
    let fill = ele.hasClass('selected')
      ? 'white'
      : ele.data().theme === 'black'
      ? 'white'
      : '#14212d'
    if (ele.data('root') && !ignoreRootTx) {
      fill = 'white'
    }
    const svg = `<svg height="${height}" fill="${fill}" width="${width}" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
    viewBox="0 0 512 512">${iconPath.replace('currentColor', fill)}</svg>`
    return {
      svg: 'data:image/svg+xml;base64,' + btoa(svg),
      width,
      height,
    }
  },
  {
    cacheKey: (arguments_) =>
      `${arguments_[0].data().type}-${
        arguments_[0].data().theme
      }-${arguments_[0].data('zoom')}-${arguments_[0].hasClass(
        'selected'
      )}-${arguments_[0].data('root')}`,
  }
)

// const edgeControlPointDistance = Mem(
//   (edge) => {
//     let offset = 0
//     if (edge.hasClass('dashed-edge')) {
//       offset = 30
//     }
//     const source = edge.data('source')
//     const target = `${edge.data('treeEdgeChildrenId')}`
//     const sourceNode = edge.cy().$(`#${source}`).first()
//     const childrenId = sourceNode.data('childrenIds')
//     if (childrenId.length > 0) {
//       const index = childrenId.indexOf(target)
//       const total = childrenId.length
//       if (childrenId.length > 2) {
//         if (childrenId.length % 2 === 0) {
//           let midPointIndexes = [total / 2]
//           midPointIndexes = [
//             midPointIndexes[0] - 1,
//             ...midPointIndexes,
//             midPointIndexes[0] + 1,
//           ]
//           if (index < midPointIndexes[0]) {
//             return 70 - offset
//           } else if (index > midPointIndexes[1]) {
//             return -70 - offset
//           } else {
//             return 0
//           }
//         } else {
//           const midPoint = Math.floor(total / 2)
//           if (index < midPoint) {
//             return 70 - offset
//           } else if (index > midPoint) {
//             return -70 - offset
//           } else {
//             return 0
//           }
//         }
//       } else {
//         if (index === 0) {
//           return 70 - offset
//         }
//         return -70 - offset
//       }
//     }
//     return -70
//   },
//   {
//     cacheKey: (arguments_) =>
//       `${arguments_[0].hasClass('dashed-edge')}${arguments_[0].data('id')}`,
//   }
// )

export const arrowScale = Mem(
  (ele) => (ele.data('zoom') > 1 ? 1 / ele.data('zoom') : 1),
  {
    cacheKey: (arguments_) => `${arguments_[0].data('zoom')}`,
  }
)

export const edgeWidth = Mem(
  (ele) => {
    return `${
      (ele.data('severity') === 'DOWN' ? 0.9 : 0.7) * (1 / ele.data('zoom'))
    }`
  },
  {
    cacheKey: (arguments_) =>
      `${arguments_[0].data('zoom')}-${arguments_[0].data('severity')}`,
  }
)

export const nodeSizeCalculator = Mem(
  (ele) => {
    const scale = 1 / ele.data('zoom')
    const width = 25 * ele.data('zoom')
    return `${width * scale}`
  },
  {
    cacheKey: (arguments_) => `${arguments_[0].data('zoom')}`,
  }
)

export const bgColor = Mem(
  (ele) =>
    Math.floor(ele.renderedBoundingBox().h) > 30
      ? Theme[ele.data('theme')].nodeBackground
      : colorMap[
          (ele.data('severity')
            ? ele.data('severity').toLowerCase()
            : 'unknown'
          ).toLowerCase()
        ],
  {
    cacheKey: (arguments_) =>
      `${arguments_[0].data('zoom')}-${arguments_[0].data(
        'severity'
      )}-${arguments_[0].data('theme')}`,
  }
)

export const selectedBgColor = Mem(
  (ele) => colorMap[(ele.data('severity') || 'unknown').toLowerCase()],
  {
    cacheKey: (arguments_) => `${arguments_[0].data('severity')}}`,
  }
)

export const textColor = Mem((ele) => Theme[ele.data('theme')].text, {
  cacheKey: (arguments_) => `${arguments_[0].data('theme')}`,
})

export const edgeColor = Mem((ele) => Theme[ele.data('theme')].edge, {
  cacheKey: (arguments_) => `${arguments_[0].data('theme')}`,
})

export const borderColor = Mem(
  (ele) =>
    colorMap[(ele.data('severity') || 'unknown').toLowerCase()] || '#000',
  {
    cacheKey: (arguments_) =>
      `${arguments_[0].data('zoom')}-${arguments_[0].data('severity')}`,
  }
)

export const borderWidth = Mem(
  (ele) => {
    const scale = 1 / ele.data('zoom')
    const width = 3 * ele.data('zoom')
    return `${width * scale}`
  },
  {
    cacheKey: (arguments_) =>
      `${arguments_[0].data('zoom')}${arguments_[0].data('root')}`,
  }
)

export const selectedNodeSizeCalculator = Mem(
  (ele) => {
    const scale = 1 / ele.data('zoom')
    const width = 30 * ele.data('zoom')
    return `${width * scale}`
  },
  {
    cacheKey: (arguments_) =>
      `${arguments_[0].data('zoom')}${arguments_[0].data('root')}`,
  }
)

export const DefaultStyle = (colorScheme) => {
  return [
    {
      selector: 'node',
      shape: 'circle',
      style: {
        label: 'data(name)',
        'font-family': 'Poppins',
        'text-halign': 'center',
        'text-valign': 'bottom',
        'text-max-width': 100,
        'text-wrap': 'ellipsis',
        color: textColor,
        'border-width': borderWidth,
        'background-image': (ele) => makeSvg(ele).svg,
        'border-color': borderColor,
        width: nodeSizeCalculator,
        height: nodeSizeCalculator,
      },
    },
    {
      selector: 'node.bordered',
      style: {
        'border-width': '2px',
      },
    },
    ...Object.keys(colorMap).map((key) => ({
      selector: `node.bordered.${key}`,
      style: {
        'border-color': colorMap[key],
      },
    })),
    ...Object.keys(colorMap).map((key) => ({
      selector: `node.filled.${key}`,
      style: {
        'border-color': colorMap[key],
        'background-color': Color(colorMap[key]).lighten(0.2).string(),
      },
    })),
    {
      selector: 'node.topology',
      style: {
        'background-color': bgColor,
      },
    },
    {
      selector: 'node.down',
      style: {
        'z-index': 2,
        'font-weight': 'bold',
        opacity: 1,
        'background-color': colorMap.down,
        'border-width': 0,
        'overlay-opacity': 0,
        width: selectedNodeSizeCalculator,
        height: selectedNodeSizeCalculator,
      },
    },
    {
      selector: 'node.root',
      style: {
        'background-color': '#3279be',
        'border-width': 2,
        'border-color': borderColor,
        width: selectedNodeSizeCalculator,
        height: selectedNodeSizeCalculator,
      },
    },
    {
      selector: 'node.selected',
      style: {
        'z-index': 2,
        'font-weight': 'bold',
        opacity: 1,
        'background-color': selectedBgColor,
        'border-width': 0,
        'overlay-opacity': 0,
        width: selectedNodeSizeCalculator,
        height: selectedNodeSizeCalculator,
      },
    },
    {
      selector: 'node.semitransparent',
      style: { opacity: '0.1' },
    },
    {
      selector: 'node.highlight',
      style: {
        opacity: 1,
        'border-style': 'double',
      },
    },
    {
      selector: 'edge',
      style: {
        'edge-text-rotation': 'autorotate',
        'min-zoomed-font-size': 10,
        color: textColor,
        width: edgeWidth,
        'line-style': 'solid',
        // 'curve-style': 'straight',
        'line-color': edgeColor,
        // opacity: 0.7,
        'control-point-step-size': 40,
        'curve-style': 'bezier',
        'target-arrow-color': edgeColor,
      },
    },
    // {
    //   selector: 'edge.semitransparent',
    //   style: { opacity: 0.1 },
    // },
    {
      selector: 'edge.highlight',
      style: {
        'line-color': '#099dd9',
        'source-arrow-color': '#099dd9',
        'target-arrow-color': '#099dd9',
        width: 1.2,
        opacity: 0.9,
      },
    },
    // {
    //   selector: 'edge.tree-edge',
    //   style: {
    // 'curve-style': 'unbundled-bezier',
    // 'control-point-distances': edgeControlPointDistance,
    // 'control-point-weights': 0.5,
    //   },
    // },
    {
      selector: 'edge.has-arrow',
      style: {
        'target-arrow-shape': 'triangle-backcurve',
        'arrow-scale': arrowScale,
      },
    },
    {
      selector: 'edge.has-bidirectional-arrow',
      style: {
        'target-arrow-shape': 'triangle-backcurve',
        'source-arrow-shape': 'triangle-backcurve',
        'arrow-scale': arrowScale,
      },
    },
    {
      selector: 'edge.dashed-edge',
      style: {
        'control-point-step-size': 40,
        'curve-style': 'bezier',
        'line-style': 'dashed',
        'line-dash-pattern': [3, 3],
        width: 1.2,
      },
    },
    {
      selector: 'edge.dashhighlight',
      style: {
        'control-point-step-size': 40,
        'line-dash-pattern': [3, 3],
        'curve-style': 'bezier',
        'line-style': 'dashed',
        opacity: 1,
      },
    },
    {
      selector: 'edge.bezier',
      style: {
        'curve-style': 'bezier',
      },
    },
    {
      selector: 'edge.hidden',
      style: {
        visibility: 'hidden',
      },
    },
    ...Object.keys(colorMap).map((key) => ({
      selector: `edge.${key}`,
      style: {
        'line-color': colorMap[key],
      },
    })),
    {
      selector: 'edge.down',
      style: {
        opacity: 1,
      },
    },
    {
      selector: 'edge.animating',
      style: {
        'line-style': 'dashed',
        'line-dash-pattern': [5],
        'line-color': '#099dd9',
        'source-arrow-color': '#099dd9',
        'target-arrow-color': '#099dd9',
      },
    },
    {
      selector: 'edge.animating.dashed-edge',
      style: {
        'line-color': '#3279be',
        'source-arrow-color': '#3279be',
        'target-arrow-color': '#3279be',
      },
    },
  ]
}
