<template>
  <MPopover
    ref="mPopoverRef"
    trigger="click"
    :placement="placement"
    overlay-class-name="color-picker-popover"
  >
    <template v-slot:trigger>
      <div class="items-center cursor-pointer inline-flex">
        <div
          :style="{ background: value }"
          :class="{ transparent: value === 'transparent' }"
          class="cursor-pointer mr-2 color-preview"
        >
          <div v-if="value === 'transparent'" class="line" />
        </div>
        <MIcon
          v-if="!hideArrow"
          name="chevron-down"
          class="text-neutral-light"
        />
      </div>
    </template>
    <MRow :gutter="0">
      <MCol class="color-picker-container">
        <Sketch v-model="color" :preset-colors="presetColors"></Sketch>
        <MRow class="mt-4 justify-end" :gutter="0">
          <MButton variant="default" @click="hide"> Cancel </MButton>
          <MButton variant="primary" rounded class="ml-2" @click="onSubmit">
            Apply
          </MButton>
        </MRow>
      </MCol>
    </MRow>
  </MPopover>
</template>

<script>
import { Sketch } from 'vue-color'
export default {
  name: 'ColorPicker',
  components: { Sketch },
  model: {
    event: 'change',
  },
  props: {
    value: { type: String, default: 'transparent' },
    hideArrow: { type: Boolean, default: false },
    placement: {
      type: String,
      default() {
        return 'bottomLeft'
      },
    },
  },
  data() {
    this.presetColors = [
      '#099dd9',
      '#3279be',
      '#89c540',
      '#f5bc18',
      '#f58518',
      '#f45b5b',
      '#f04e3e',
      '#8d3abc',
      '#8085E9',
      '#90ef7f',
      '#434348',
      '#f7a35c',
      '#f15c80',
      '#e4d354',
      '#2b908f',
      '#91e8e1',
    ]
    return {
      color: { hex: this.value },
    }
  },
  watch: {
    value(newValue) {
      this.color = { hex: newValue }
    },
  },
  methods: {
    hide() {
      this.$refs.mPopoverRef && this.$refs.mPopoverRef.hide()
      this.color = { hex: this.value }
    },
    onSubmit() {
      this.$emit('change', this.color.hex)
      this.hide()
    },
  },
}
</script>

<style lang="less" scope>
.@{ant-prefix}-popover {
  &.color-picker-popover {
    z-index: 9999 !important;
  }
}

.color-preview {
  width: 20px;
  height: 20px;

  @apply flex items-center justify-center;

  &.transparent {
    border: 1px solid var(--border-color);
  }

  .line {
    width: 1px;
    height: 22px;
    background: var(--secondary-red);
    transform: rotate(135deg);
  }
}

.color-picker-container {
  .vc-sketch {
    padding: 0;
    background: var(--dropdown-background);
    box-shadow: none;

    input {
      color: var(--input-text-color);
      background: transparent;
      border-color: var(--border-color);
    }
    // stylelint-disable-next-line
    .vc-input__label {
      color: var(--page-text-color);
    }
  }
}
</style>
