<template>
  <FlotoDrawer :open="open" width="70%" @hide="closeDrawer">
    <template v-slot:title> Terminal - {{ (monitor || {}).name }} </template>
    <div class="flex flex-col flex-1 min-h-0 p-2 bg-black">
      <div ref="terminalContainer" class="h-full"></div>
    </div>
  </FlotoDrawer>
</template>

<script>
import Debounce from 'lodash/debounce'
import { Terminal } from '@xterm/xterm'
import { FitAddon } from '@xterm/addon-fit'
import '@xterm/xterm/css/xterm.css'
import LocalEchoController from './local-echo-controller'
import { objectDBWorker } from '@/src/workers'
import Bus from '@utils/emitter'
import { generateId } from '@/src/utils/id'

const SSH_EVENT_NAME = 'ui.action.ssh.client.manager'
// const SSH_EVENT_NAME = 'ssh.utility'

export default {
  name: 'TerminalUI',
  props: {
    id: {
      type: Number,
      required: true,
    },
    ncmId: {
      type: Number,
      default: undefined,
    },
    askCredentials: {
      type: Boolean,
      default: false,
    },
    open: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    this.terminal = null
    return {
      username: '',
      password: '',
      port: null,
      monitor: null,
      loading: true,
      uuid: generateId(),
      prompt: null,
    }
  },
  watch: {
    open(newValue, oldValue) {
      if (newValue !== oldValue) {
        if (newValue) {
          this.reset()
          this.uuid = generateId()
          Bus.$on(SSH_EVENT_NAME, this.handleIncomingEvent)
          setTimeout(async () => {
            await this.resolveMonitor()
            if (this.monitor) {
              this.createTerminal()
            }
          }, 400)
        } else {
          Bus.$off(SSH_EVENT_NAME, this.handleIncomingEvent)
          if (this.terminal) {
            this.terminal.dispose()
          }
          this.closeDrawer()
        }
      }
    },
  },
  mounted() {
    this.takeCommand = Debounce(this.takeCommandRaw, 750, {
      leading: false,
      trailing: true,
    })
    // Bus.$on(SSH_EVENT_NAME, this.handleIncomingEvent)
    // setTimeout(async () => {
    //   await this.resolveMonitor()
    //   if (this.monitor) {
    //     this.createTerminal()
    //   }
    // }, 400)
  },
  beforeDestroy() {
    Bus.$off(SSH_EVENT_NAME, this.handleIncomingEvent)
    if (this.terminal) {
      this.terminal.dispose()
    }
    this.closeDrawer()
  },
  methods: {
    reset() {
      this.username = ''
      this.password = ''
      this.port = null
      this.uuid = generateId()
      this.prompt = null
    },
    executeCommand(command) {
      if (command.toLowerCase() === 'clear') {
        setTimeout(() => {
          this.terminal.clear()
          setTimeout(() => {
            this.takeCommand(true)
          })
        }, 200)
        return
      }
      if (command.toLowerCase() === 'exit') {
        this.closeDrawer()
        return
      }
      Bus.$emit('server:event', {
        'event.type': SSH_EVENT_NAME,
        'event.context': {
          [this.$constants.UI_EVENT_UUID]: this.uuid,
          'ssh.operation': 'EXECUTION',
          'ssh.command': command,
          'object.ip': this.monitor?.ip,
        },
      })
    },
    handleIncomingEvent(event) {
      if (event[this.$constants.UI_EVENT_UUID] === this.uuid) {
        if (event['ssh.operation'] === 'CLOSE' && !this.terminal._isDisposed) {
          this.terminal.write(
            '\r\n\r\n======================================= \r\n\r\n'
          )
          this.terminal.write(event.output)
          return
        }
        if (event.status === 'fail') {
          if (event['ssh.operation'] === 'CONNECTION') {
            this.terminal?.write('\r\n')
            if ((event.errors || [])?.length > 0) {
              this.terminal?.write(event.errors[0].message)
            } else {
              this.terminal?.write('Failed to connect to server')
            }
          }
        } else {
          if (event['ssh.operation'] === 'CONNECTION') {
            this.terminal.clear()
          }
          if (event.output) {
            if (!this.prompt) {
              this.prompt = '$ '
              this.takeCommand(true)
            } else {
              this.terminal.write(`${event.output}\n\r`, this.takeCommand)
            }
          }
        }
      }
    },
    takeCommandRaw(immediate = false) {
      this.localEcho
        .read(this.prompt)
        .then((command) => this.executeCommand(command))
        .catch((error) => this.terminal.write(`Error reading: ${error}`))
    },
    initSocket() {
      Bus.$emit('server:event', {
        'event.type': SSH_EVENT_NAME,
        'event.context': {
          [this.$constants.UI_EVENT_UUID]: this.uuid,
          'ssh.operation': 'CONNECTION',
          ...(this.askCredentials
            ? {
                'object.ip': this.monitor?.ip,
                id: this.monitor?.id,
                'object.category': this.monitor?.category,
                port: this.port,
                username: this.username,
                password: this.password,
              }
            : {
                'object.ip': this.monitor?.ip,
                id: this.ncmId || this.monitor?.id,
                'object.category': this.monitor?.category,
              }),
        },
      })
    },
    closeSocketConnection() {
      if (this.monitor) {
        Bus.$emit('server:event', {
          'event.type': SSH_EVENT_NAME,
          'event.context': {
            [this.$constants.UI_EVENT_UUID]: this.uuid,
            'ssh.operation': 'CLOSE',
            'object.ip': this.monitor.ip,
          },
        })
      }
    },
    startConnection() {
      this.terminal.write(
        '\r\n\r\n=========== Connecting to Server ======== \r\n\r\n'
      )
      this.initSocket()
    },
    getCredentials() {
      this.localEcho
        .read('Login as: ', undefined, false, true)
        .then((username) => {
          this.username = username
          return this.localEcho.read('Password: ', undefined, true, true)
        })
        .then((password) => {
          this.password = password
          return this.localEcho.read('Port: ', undefined, false, true)
        })
        .then((port) => {
          this.port = parseInt(port, 10)
          this.startConnection()
        })
        .catch((error) => this.terminal.write(`Error reading: ${error}`))
    },
    createTerminal() {
      const terminalContainer = this.$refs.terminalContainer
      const term = new Terminal({ cursorBlink: true })
      const fitAddon = new FitAddon()
      this.localEcho = new LocalEchoController()
      term.loadAddon(this.localEcho)
      term.loadAddon(fitAddon)
      term.open(terminalContainer)
      fitAddon.fit()
      this.terminal = term
      this.terminal.focus()
      if (this.askCredentials) {
        this.getCredentials()
      } else {
        this.startConnection()
      }
    },
    async resolveMonitor() {
      let monitor = await objectDBWorker.getObjectById(this.id)
      if (!monitor) {
        this.$errorNotification({
          message: 'Error',
          description: `Unable to find monitor with id ${this.id}`,
        })
        return this.closeDrawer()
      }
      this.monitor = Object.freeze(monitor)
    },
    closeDrawer() {
      if (this._commandTimeout) {
        clearTimeout(this._commandTimeout)
      }
      this.closeSocketConnection()
      if (this.terminal) {
        this.terminal.dispose()
      }
      setTimeout(() => {
        this.$emit('close')
      }, 400)
    },
  },
}
</script>
