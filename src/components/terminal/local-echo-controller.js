import LocalEchoControllerParent from 'local-echo'

/**
 * The history controller provides an ring-buffer
 */
export class HistoryController {
  constructor(size) {
    this.size = size
    this.entries = []
    this.cursor = 0
  }

  /**
   * Push an entry and maintain ring buffer size
   */
  push(entry) {
    // Skip empty entries
    if (entry.trim() === '') return
    // Skip duplicate entries
    const lastEntry = this.entries[this.entries.length - 1]
    // eslint-disable-next-line
    if (entry == lastEntry) return
    // Keep track of entries
    this.entries.push(entry)
    if (this.entries.length > this.size) {
      this.entries.shift()
    }
    this.cursor = this.entries.length
  }

  /**
   * Rewind history cursor on the last entry
   */
  rewind() {
    this.cursor = this.entries.length
  }

  /**
   * Returns the previous entry
   */
  getPrevious() {
    const idx = Math.max(0, this.cursor - 1)
    this.cursor = idx
    return this.entries[idx]
  }

  /**
   * Returns the next entry
   */
  getNext() {
    const idx = Math.min(this.entries.length, this.cursor + 1)
    this.cursor = idx
    return this.entries[idx]
  }
}

export default class LocalEchoController extends LocalEchoControllerParent {
  constructor(term = null, options = {}) {
    super(term, options)
    this.history = new HistoryController(options.historySize || 10)
  }
  /**
   * Return a promise that will resolve when the user has completed
   * typing a single line
   */
  read(
    prompt,
    continuationPrompt = '> ',
    isPasswordPrompt = false,
    skipHistory = false
  ) {
    return new Promise((resolve, reject) => {
      this.term.write(prompt)
      this._activePrompt = {
        prompt,
        continuationPrompt,
        resolve,
        isPasswordPrompt,
        skipHistory,
        reject,
      }

      this._input = ''
      this._cursor = 0
      this._active = true
    })
  }

  /**
   * Handle input completion
   */
  handleReadComplete() {
    if (this.history) {
      if (!(this._activePrompt || {}).skipHistory) {
        this.history.push(this._input)
      }
    }
    if (this._activePrompt) {
      this._activePrompt.resolve(this._input)
      this._activePrompt = null
    }
    this.term.write('\r\n')
    this._active = false
  }

  /**
   * Apply prompts to the given input
   */
  applyPrompts(input) {
    const prompt = (this._activePrompt || {}).prompt || ''
    const continuationPrompt =
      (this._activePrompt || {}).continuationPrompt || ''

    const isPasswordPrompt =
      (this._activePrompt || {}).isPasswordPrompt || false

    return (
      prompt +
      (isPasswordPrompt
        ? input.replace(/\n/g, '\n' + continuationPrompt).replace(/./g, '*')
        : input.replace(/\n/g, '\n' + continuationPrompt))
    )
  }
}
