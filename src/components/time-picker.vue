<template>
  <FlotoDropdownPicker
    v-model="selectedValues"
    :disabled="disabled"
    :options="options"
    searchable
    class="w-full multi-select-group"
    :multiple="multiple"
    v-bind="$attrs"
  >
    <template v-slot:suffixIcon>
      <MIcon name="clock" />
    </template>
  </FlotoDropdownPicker>
</template>

<script>
import Moment from 'moment'
import SortBy from 'lodash/sortBy'
import Range from 'lodash/range'

export default {
  name: 'TimePicker',
  model: { event: 'change' },
  props: {
    value: { type: [Array, Object, String], default: undefined },
    disabled: { type: Boolean, default: false },
    // eslint-disable-next-line
    multiple: { type: Boolean, default: true },
    externalOptions: { type: [Array], default: undefined },
    useSeconds: { type: Boolean, default: false },
  },
  data() {
    return {}
  },
  computed: {
    options() {
      return this.externalOptions !== undefined
        ? this.externalOptions
        : Range(0, 1440, 5)
            .map((i) =>
              Moment.utc(Moment.duration(i, 'minutes').asMilliseconds()).format(
                'HH:mm'
              )
            )
            .map((k) => ({ key: k, text: k, id: k.replace(':', '') }))
            .concat([{ key: '23:59', text: '23:59', id: '2359' }])
    },

    selectedValues: {
      get() {
        if (this.multiple) {
          return SortBy(this.value).map((v) => {
            if (/^\d{1,2}:\d{1,2}:\d{1,2}$/.test(v)) {
              return v.replace(/^(\d{1,2}:\d{1,2}):\d{1,2}$/, '$1')
            }
            return v
          })
        } else {
          if (this.value) {
            if (/^\d{1,2}:\d{1,2}:\d{1,2}$/.test(this.value)) {
              return this.value.replace(/^(\d{1,2}:\d{1,2}):\d{1,2}$/, '$1')
            }
          }
          return this.value
        }
      },
      set(v) {
        if (this.useSeconds) {
          if (this.multiple) {
            v = v.map((innerValue) =>
              /^\d{1,2}:\d{1,2}$/.test(innerValue)
                ? `${innerValue}:00`
                : innerValue
            )
          } else {
            v = /^\d{1,2}:\d{1,2}$/.test(v) ? `${v}:00` : v
          }
        }
        this.$emit('change', v)
      },
    },
  },
}
</script>
