<template>
  <div class="flex flex-1 flex-col min-w-0 w-full">
    <div
      class="flex flex-1 flex-col min-w-0"
      :class="{ 'cursor-pointer': !disabled }"
      @click="handleClick"
      @mouseenter="handleMouseEnter"
      @mouseleave="handleMouseExit"
    >
      <div class="flex items-center min-w-0 h-full">
        <template v-if="disabled">
          <SelectedItemPills
            :value="(selectedItems || []).map((i) => map[i] || i)"
            :wrap="wrap"
            :use-popover="$attrs['use-popover']"
          />
        </template>
        <div v-else class="dropdown-trigger-input flex-1 min-w-0">
          <MInput
            readonly
            :placeholder="placeholder"
            :value="
              selectedItems && selectedItems.length
                ? `${(map[selectedItems[0]] || {}).text || selectedItems[0]}${
                    selectedItems.length > 1
                      ? ` (+${selectedItems.length - 1})`
                      : ''
                  }`
                : undefined
            "
          >
            <template
              v-if="$scopedSlots['prefix-text'] && selectedItem"
              v-slot:prefix
            >
              <slot name="prefix-text" :item="selectedItem" />
            </template>
            <MIcon
              v-if="isActiveHover && allowClear && !disabled"
              slot="suffix"
              name="times-circle"
              class="cursor-pointer text-neutral-light"
              @click.stop="$emit('change', [])"
            />
            <MIcon
              v-else-if="!isActiveHover && !disabled"
              slot="suffix"
              name="chevron-down"
              class="cursor-pointer text-neutral-light dropdown-icon"
              :class="{ 'is-open': isOpen }"
            />
          </MInput>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import SelectedItemPills from './selected-item-pills.vue'

export default {
  name: 'MultipleTrigger',
  components: { SelectedItemPills },
  props: {
    selectedItems: { type: [Array, Object], default: undefined },
    allowClear: { type: Boolean, default: false },
    disabled: { type: Boolean, default: false },
    placeholder: { type: String, default: '' },
    textOnly: { type: Boolean, default: false },
    options: { type: Array, required: true },
    // eslint-disable-next-line
    wrap: { type: Boolean, default: true },
    isOpen: { type: Boolean, default: false },
  },
  data() {
    return {
      map: {},
      isActiveHover: false,
    }
  },
  computed: {
    title() {
      const map = this.map
      return this.selectedItems.map((i) => (map[i] || {}).text || i).join(',')
    },
  },
  watch: {
    options: {
      immediate: true,
      handler() {
        const m = {}
        this.options.forEach(
          (o) =>
            (m[o.key || o.value] = {
              text: o.text || o.label || o.name,
              status: o.status,
            })
        )
        this.map = Object.freeze(m)
      },
    },
  },
  methods: {
    handleClick() {
      if (this.disabled) {
        return
      }
      this.$emit('click')
    },
    handleMouseExit() {
      if (this.disabled) {
        return
      }
      this.isActiveHover = false
    },
    handleMouseEnter() {
      if (this.selectedItem) {
        this.isActiveHover = true
      }
    },
    handleRemoveTag(id) {
      this.$emit(
        'change',
        this.selectedItems.filter((i) => i !== id)
      )
    },
  },
}
</script>
