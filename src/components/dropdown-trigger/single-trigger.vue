<template>
  <div
    v-if="!asInput && !textOnly"
    :class="{ disabled: disabled, 'text-ellipsis': true }"
    class="tag-user inline-flex items-center ant-tag ant-tag-has-color default rounded"
    @click="$emit('click')"
  >
    <slot name="prefix-text" :item="selectedItem" />
    <MStatusArrow
      v-if="useStatusArrow && selectedItem"
      :status="selectedItem.status"
      size="sm"
      class="mr-1 excluded-header-icon"
    />
    <span class="text-ellipsis">
      {{
        selectedItem
          ? selectedItem.text || selectedItem.name || selectedItem.label
          : placeholder
      }}
    </span>
    <slot name="suffix-text" />
  </div>
  <span v-else-if="textOnly" class="text-ellipsis" @click="$emit('click')">
    {{
      selectedItem
        ? selectedItem.text || selectedItem.name || selectedItem.label
        : placeholder
    }}
  </span>
  <div
    v-else
    class="w-full"
    :title="
      selectedItem
        ? selectedItem.text || selectedItem.name || selectedItem.label
        : undefined
    "
    @mouseenter="handleMouseEnter"
    @mouseleave="isActiveHover = false"
    @click="$emit('click')"
  >
    <MInput
      read-only
      :disabled="disabled"
      :value="
        selectedItem
          ? selectedItem.text || selectedItem.name || selectedItem.label
          : undefined
      "
      :placeholder="placeholder"
      class="dropdown-trigger-input"
      :class="{
        'use-large-padding': useLargePadding,
      }"
      data-cy="dropdown-trigger-input"
    >
      <template
        v-if="$scopedSlots['prefix-text'] && selectedItem"
        v-slot:prefix
      >
        <slot name="prefix-text" :item="selectedItem" />
      </template>
      <template v-if="!disabled">
        <MIcon
          v-if="isActiveHover && allowClear"
          slot="suffix"
          name="times-circle"
          size="sm"
          class="cursor-pointer text-neutral-light excluded-header-icon"
          @click.stop="$emit('change', undefined)"
        />
        <MIcon
          v-else
          slot="suffix"
          name="chevron-down"
          class="cursor-pointer text-neutral-light dropdown-icon excluded-header-icon"
          :class="{ 'is-open': isOpen }"
        />
      </template>
    </MInput>
  </div>
</template>

<script>
export default {
  name: 'SingleTrigger',
  props: {
    selectedItem: { type: Object, default: undefined },
    allowClear: { type: Boolean, default: false },
    disabled: { type: Boolean, default: false },
    placeholder: { type: String, default: '' },
    textOnly: { type: Boolean, default: false },
    // eslint-disable-next-line
    asInput: { type: Boolean, default: true },
    isOpen: { type: Boolean, default: false },
    useStatusArrow: { type: Boolean, default: false },
    useLargePadding: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isActiveHover: false,
    }
  },
  methods: {
    handleMouseEnter() {
      if (this.selectedItem) {
        this.isActiveHover = true
      }
    },
  },
}
</script>
