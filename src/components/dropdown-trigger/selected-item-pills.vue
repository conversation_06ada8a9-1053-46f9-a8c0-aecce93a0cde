<template functional>
  <span v-if="props.textOnly">{{ props.value.join(',') }}</span>
  <div
    v-else-if="(props.value || []).filter(Boolean).length"
    class="flex-1 min-w-0 flex"
    :class="{
      'flex-wrap': props.wrap,
      'flex-col': props.vertical,
      'loose-tags-input': props.tags,
    }"
  >
    <div
      v-for="(item, index) in props.value.slice(0, props.maxItems)"
      :key="`${index}-${item}`"
      class="mt-1 mb-1 mr-2 inline-flex min-w-0"
      :class="{ 'max-w-full': props.vertical }"
    >
      <div
        class="ant-tag ant-tag-has-color cursor-auto default rounded m-0 min-w-0"
        :class="{ 'max-w-full': props.vertical }"
        :title="typeof item === 'object' ? item.text : item"
      >
        <div class="text-ellipsis">
          {{ typeof item === 'object' ? item.text : item }}
        </div>
      </div>
    </div>
    <span v-if="props.value.slice(props.maxItems).length">
      <component
        :is="props.usePopover ? 'MPopover' : 'MPopper'"
        v-bind="
          props.usePopover
            ? {
                placement: 'bottomRight',
                transitionName: 'none',
                overlayClassName: 'readable-content-overlay picker-overlay',
              }
            : {
                overlayClassName: 'readable-content-overlay picker-overlay',
              }
        "
      >
        <div
          slot="trigger"
          class="mt-1 mb-1 mr-2 ant-tag ant-tag-has-color default rounded"
          >+{{ props.value.slice(props.maxItems).length }}</div
        >
        <div class="list">
          <div
            v-for="(item, index) in props.value.slice(props.maxItems)"
            :key="`${item}-${index}`"
            >{{ typeof item === 'object' ? item.text : item }}</div
          >
        </div>
      </component>
    </span>
  </div>
</template>

<script>
export default {
  name: 'SelectedItemPills',
  inheritAttrs: false,
  props: {
    maxItems: { type: Number, default: 1 },
    value: { type: [Array], required: true },
    // eslint-disable-next-line
    wrap: { type: Boolean, default: true },
    usePopover: { type: Boolean, default: false },
    textOnly: { type: Boolean, default: false },
    vertical: { type: Boolean, default: false },
    tags: { type: Boolean, default: false },
  },
}
</script>
