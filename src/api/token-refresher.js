class TokenRefresher {
  isRefreshing = false

  refreshCallbacks = []

  errorCallbacks = []

  _apiClient = null

  constructor(apiClient) {
    this._apiClient = apiClient
  }

  onRefresh(callback) {
    this.refreshCallbacks.push(callback)
  }

  refresh() {
    if (this.isRefreshing) {
      return
    }
    this.isRefreshing = true
    const token = JSON.parse(localStorage.getItem('auth.token'))
    const refreshToken = token.refresh_token
    this._apiClient.removeHeader('Authorization')
    return this._apiClient
      .post(
        `/token`,
        {
          'refresh.token': refreshToken,
        },
        {
          notify: false,
        }
      )
      .then((data) => {
        this.refreshTokenReceived({
          access_token: data['access.token'],
          refresh_token: data['refresh.token'],
          session_id: data['session-id'],
        })
        this.isRefreshing = false
      })
      .catch((e) => {
        this.triggerError(e)
        this.isRefreshing = false
      })
  }

  onError(cb) {
    this.errorCallbacks.push(cb)
  }

  triggerError(e) {
    this.refreshCallbacks = []
    this.errorCallbacks.forEach((cb) => cb(e))
  }

  refreshTokenReceived(token) {
    localStorage.setItem('auth.token', JSON.stringify(token))
    this.refreshCallbacks.forEach((cb) => cb(token))
    this.refreshCallbacks = []
  }
}

export default TokenRefresher
