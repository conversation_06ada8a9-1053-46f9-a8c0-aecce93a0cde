import axios from 'axios'
import Omit from 'lodash/omit'
import Bus from '@utils/emitter'
// import CryptoJS from 'crypto-js'
// import { trimParams } from '@utils/params'
import {
  successToast,
  errorToast,
  successNotification,
  errorNotification,
} from '@motadata/ui'
import TokenRefresher from './token-refresher'

/**
 * @class Api
 * @typedef {import('@/node_modules/axios/index').AxiosRequestConfig} RequestConfig
 * @typedef {import('@/node_modules/axios/index').AxiosResponse} Response
 */
class Api {
  totalRequests = 0

  tokenRefresher = null

  _store = null

  /**
   * @constructor
   */
  constructor() {
    this._client = null
    this.createApiClient()
    this.buildTokenRefresher()
  }

  buildTokenRefresher() {
    this.tokenRefresher = new TokenRefresher(this)
    this.tokenRefresher.onError((e) => {
      this.showToastNotification('Session has been expired.', 'error')
      this._store.dispatch('auth/logout')
      Bus.$emit('session:expired')
    })
  }

  setStore(store) {
    this._store = store
  }

  /**
   * show notification
   * @param {string} message
   * @param {string} alertType
   */
  showToastNotification = (message, alertType) => {
    const func = alertType === 'success' ? successToast : errorToast
    func(message, alertType === 'error' ? 5 : undefined)
  }

  showBigNotification = (notificationConfig, type) => {
    const fn = type === 'success' ? successNotification : errorNotification
    const className =
      type === 'success' ? 'success-notification' : 'error-notification'
    fn({ ...Omit(notificationConfig, ['error']), class: className })
  }

  /**
   * Destroy api client
   */
  destroyClient() {
    this._client = null
  }

  /**
   * show progress loader
   */
  showProgressLoader = () => {
    this.totalRequests++
  }

  /**
   * hide request progress loader
   */
  hideProgressLoader = () => {
    this.totalRequests--
    if (this.totalRequests < 0) {
      this.totalRequests = 0
    }
    if (this.totalRequests === 0) {
    }
  }

  /**
   * Intercept all outgoing request
   * @param {RequestConfig} config
   * @returns {RequestConfig}
   */
  requestInterceptor = (config) => {
    config = Object.assign(config, config.data.__metadata)
    delete config.data.__metadata
    if (config.data.__qs) {
      config.data = config.data.__qs
    }
    // let isEncrypted
    // if (typeof config.data === 'string' && config.data !== '') {
    //   try {
    //     const parsedConfigData = JSON.parse(config.data)
    //     if (parsedConfigData.enc) {
    //       isEncrypted = true
    //     }
    //   } catch (e) {}
    // } else if (typeof config.data === 'object') {
    //   if (config.data.enc) {
    //     isEncrypted = true
    //   }
    // }

    // if (config.data && Object.keys(config.data).length && !isEncrypted) {
    // apply param trim to all params recurssive
    // const key = CryptoJS.enc.Utf8.parse(`MIND@!23MOT@@!23`)
    // const key = CryptoJS.enc.Utf8.parse(
    //   [
    //     'A',
    //     'P',
    //     'P',
    //     'M',
    //     'I',
    //     'N',
    //     'D',
    //     'A',
    //     'R',
    //     'R',
    //     'A',
    //     'Y',
    //     '2',
    //     '0',
    //     '1',
    //     '9',
    //   ].join('')
    // )
    // const iv = CryptoJS.enc.Utf8.parse(`MIND@!23MOT@@!23`)
    // config.data = {
    //   enc: CryptoJS.AES.encrypt(
    //     CryptoJS.enc.Utf8.parse(JSON.stringify(trimParams(config.data))),
    //     key,
    //     {
    //       keySize: 128 / 8,
    //       iv,
    //       mode: CryptoJS.mode.CBC,
    //       padding: CryptoJS.pad.Pkcs7,
    //     }
    //   ).toString(),
    // }
    // config.data = {
    //   enc: CryptoJS.AES.encrypt(
    //     JSON.stringify(trimParams(config.data)),
    //     [
    //       'A',
    //       'P',
    //       'P',
    //       'M',
    //       'I',
    //       'N',
    //       'D',
    //       'A',
    //       'R',
    //       'R',
    //       'A',
    //       'Y',
    //       '2',
    //       '0',
    //       '1',
    //       '9',
    //     ].join(''),
    //     {
    //       mode: CryptoJS.mode.ECB,
    //       padding: CryptoJS.pad.NoPadding,
    //       //   padding: CryptoJS.pad.Pkcs7,
    //     }
    //   ).toString(),
    // }
    // }
    if (config.loader !== false) {
      this.showProgressLoader()
    }

    return config
  }

  /**
   * Handle success resposne
   * @param {Response<object>} response
   * @returns {object}
   */
  responseSuccessHandler = (response) => {
    if (response.config.loader !== false) {
      this.hideProgressLoader()
    }
    const message =
      response.config.message ||
      // response.data.message || // remove backend message from simple api
      (response.config.notification || {}).message
    if (
      (['post', 'put', 'delete'].indexOf(
        response.config.method.toLowerCase()
      ) >= 0 ||
        response.config.message ||
        response.config.notification) &&
      response.config.notify !== false
    ) {
      requestAnimationFrame(() => {
        if (
          response.config.notification &&
          response.config.notification.message
        ) {
          this.showBigNotification(
            {
              ...response.config.notification,
              message,
            },
            'success'
          )
        } else {
          if (message) {
            this.showToastNotification(
              message || 'The Operation has been completed.',
              'success'
            )
          }
        }
      })
    }
    return response.data
  }

  /**
   * handle response error
   * @param {Response<object>} error
   * @return {Promise<Resposne>}
   */
  responseErrorHandler = (error) => {
    if ((error.response || {}).status === 412) {
      Bus.$emit('error:disk-space-full')
      return Promise.reject(error)
    }
    if (axios.isCancel(error) || error.config.loader !== false) {
      this.hideProgressLoader()
    }
    if (axios.isCancel(error) || error.config.notify === false) {
      return Promise.reject(error)
    }
    if ((error.response || {}).status !== 401) {
      let message =
        ((error.response || {}).data || {}).message ||
        ((error.response || {}).data || {}).result ||
        'Unable to connect with server.'
      if (
        error.code === 'ECONNABORTED' &&
        error.message.indexOf('timeout') >= 0
      ) {
        message = 'Request timeout reached, Please Try again'
      }
      if ((error.config.notification || {}).error) {
        this.showBigNotification(
          {
            ...error.config.notification.error,
            description:
              (error.response.data || {}).message ||
              error.config.notification.error.description,
          },
          'error'
        )
      } else {
        requestAnimationFrame(() => {
          this.showToastNotification(message, 'error')
        })
      }
    } else {
      return new Promise((resolve, reject) => {
        const { config } = error
        // token is expired start refreshing it
        this.tokenRefresher.onRefresh((token) => {
          this.setHeaders({
            Authorization: `Bearer ${token.access_token}`,
          })
          config.headers['Authorization'] = `Bearer ${token.access_token}`
          this._client
            .request({
              ...config,
              url: config.url.replace(config.baseURL, ''),
            })
            .then(resolve, reject)
          this.buildTokenRefresher()
        })
        this.tokenRefresher.refresh()
      })
    }
    return Promise.reject(error)
  }

  // this is only for static data where /api/v1 prefix is not used
  getNewClient(baseURL) {
    const headers = {
      'Content-Type': 'application/json',
    }
    const client = axios.create({
      baseURL: baseURL,
      timeout: 120000,
      headers,
    })
    return client
  }

  /**
   * Create Api client
   */
  createApiClient() {
    const headers = {
      'Content-Type': 'application/json',
    }
    this._client = axios.create({
      baseURL: process.env.VUE_APP_API_BASE_PATH,
      timeout: 120000,
      headers,
    })
    this._client.interceptors.request.use(this.requestInterceptor)
    this._client.interceptors.response.use(
      this.responseSuccessHandler,
      this.responseErrorHandler
    )
  }

  /**
   * Apply header to all requests
   * @param {[key: string]: string} headers
   */
  setHeaders(headers) {
    if (!this._client) {
      throw new Error('no client found')
    }
    Object.keys(headers).forEach((key) => {
      this._client.defaults.headers.common[key] = headers[key]
    })
  }

  /**
   * remove header from api client
   * @param {string} headerName
   */
  removeHeader(headerName) {
    if (!this._client) {
      throw new Error('no client found')
    }
    delete this._client.defaults.headers.common[headerName]
  }

  /**
   * Send Actual request
   * @param {RequestConfig} config
   * @returns {Promise<Response>}
   */
  _sendRequest(config) {
    const fireRequest = (resolve, reject) => {
      if (!this._client) {
        try {
          this.createApiClient()
        } catch (e) {
          // eslint-disable-next-line
          console.error(e)
          return reject(e)
        }
      }
      return this._client
        .request({
          ...config,
        })
        .then(resolve, reject)
    }
    return new Promise((resolve, reject) => fireRequest(resolve, reject))
  }

  /**
   * send get request
   * @param {string} url
   * @param {RequestConfig} config
   */
  get(url, config) {
    return this._sendRequest({
      ...config,
      url,
      method: 'get',
      data: {
        __metadata: {
          ...config,
        },
      },
    })
  }

  /**
   * Send post request
   * @param {string} url
   * @param {object|any} data
   * @param {RequestConfig} config
   */
  post(url, data, config) {
    if (typeof data !== 'string' && !Array.isArray(data)) {
      if (data) {
        data.__metadata = config
      } else {
        data = {
          __metadata: config,
        }
      }
    } else {
      data = {
        __qs: data,
        __metadata: config,
      }
    }
    return this._sendRequest({
      ...config,
      url,
      data,
      method: 'post',
    })
  }

  /**
   * Send put request
   * @param {string} url
   * @param {object|any} data
   * @param {RequestConfig} config
   */
  put(url, data, config) {
    if (typeof data !== 'string') {
      if (data) {
        data.__metadata = config
      } else {
        data = {
          __metadata: config,
        }
      }
    } else {
      data = {
        __qs: data,
        __metadata: config,
      }
    }
    return this._sendRequest({
      ...config,
      url,
      data,
      method: 'put',
    })
  }

  /**
   * send delete request
   * @param {string} url
   * @param {RequestConfig} config
   */
  delete(url, config = {}) {
    return this._sendRequest({
      ...config,
      data: {
        ...(config.data || {}),
        __metadata: {
          ...config,
        },
      },
      url,
      method: 'delete',
    })
  }
}

export default Api
