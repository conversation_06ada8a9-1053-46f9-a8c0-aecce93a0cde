import Windows from './icons/windows.svg?external'
import Linux from './icons/linux.svg?external'
import IBMAIX from './icons/ibm-aix.svg?external'
import HPUX from './icons/hp-ux.svg?external'
import ActiveDirectory from './icons/active-directory.svg?external'
import ApacheHTTP from './icons/apache-http.svg?external'
import ApacheMQ from './icons/apache-mq.svg?external'
import ApacheTomcat from './icons/apache-tomcat.svg?external'
import AwsCloud from './icons/aws-cloud.svg?external'
import AmazonDynamoDB from './icons/amazon-dynamo-db.svg?external'
import AmazonEBS from './icons/amazon-ebs.svg?external'
import AmazonEC2 from './icons/amazon-ec2.svg?external'
import AwsELB from './icons/aws-elb.svg?external'
import AmazonRDS from './icons/amazon-rds.svg?external'
import AmazonS3 from './icons/amazon-s3.svg?external'
import AmazonSNS from './icons/amazon-sns.svg?external'
import AmazonCloudFront from './icons/amazon-cloud-front.svg?external'
import AwsAutoScaling from './icons/aws-auto-scaling.svg?external'
import AwsLambda from './icons/aws-lambda.svg?external'
import AmazonSQS from './icons/amazon-sqs.svg?external'
import AwsElasticBeanstalk from './icons/aws-elastic-beanstalk.svg?external'
import AmazonDocumentDB from './icons/amazon-document-db.svg?external'
import AzureCloud from './icons/azure-cloud.svg?external'
import AzureCosmosDB from './icons/azure-cosmos-db.svg?external'
import AzureSQLDatabase from './icons/azure-sql-database.svg?external'
import AzureStorage from './icons/azure-storage.svg?external'
import AzureVM from './icons/azure-vm.svg?external'
import AzureWebApp from './icons/azure-web-app.svg?external'
import AzureServiceBus from './icons/azure-service-bus.svg?external'
import AzureApplicationGateway from './icons/azure-application-gateway.svg?external'
import AzureFunction from './icons/azure-function.svg?external'
import AzureLoadBalancer from './icons/azure-load-balancer.svg?external'
import AzurePostgreSQLServer from './icons/azure-postgresql-server.svg?external'
import AzureMySQLServer from './icons/azure-mysql-server.svg?external'
import AzureVMScaleSet from './icons/azure-vm-scale-set.svg?external'
import AzureCDN from './icons/azure-cdn.svg?external'
import Bind9 from './icons/bind9.svg?external'
import MicrosoftIIS from './icons/microsoft-iis.svg?external'
import Nginx from './icons/nginx.svg?external'
import Dotnet from './icons/dotnet.svg?external'
import MSSQL from './icons/mssql.svg?external'
import IbmDb2 from './icons/ibm-db-2.svg?external'
import OracleDatabase from './icons/oracle-database.svg?external'
import MySQL from './icons/mysql.svg?external'
import MariaDB from './icons/maria-db.svg?external'
import PostgreSQL from './icons/postgresql.svg?external'
import WindowsDns from './icons/windows-dns.svg?external'
import WindowsDhcp from './icons/windows-dhcp.svg?external'
import ExchangeMailbox from './icons/exchange-mailbox.svg?external'
import ExchangeClientAccessRole from './icons/exchange-client-access-role.svg?external'
import ExchangeEdgeTransportRole from './icons/exchange-edge-transport-role.svg?external'
import ExchangeMailboxRole from './icons/exchange-mailbox-role.svg?external'
import HAProxy from './icons/haproxy.svg?external'
import IBMMQ from './icons/ibm-mq.svg?external'
import MSMQ from './icons/msmq.svg?external'
import RabbitMQ from './icons/rabbit-mq.svg?external'
import OracleWeblogic from './icons/oracle-weblogic.svg?external'
import GlassfishServer from './icons/glassfish-server.svg?external'
import LightHTTPD from './icons/light-httpd.svg?external'
import LinuxDHCP from './icons/linux-dhcp.svg?external'
import IBMWebsphere from './icons/ibm-websphere.svg?external'
import WildFly from './icons/wildfly.svg?external'
import WindowsRDP from './icons/windows-rdp.svg?external'
import Sybase from './icons/sybase.svg?external'
import SAPHANA from './icons/sap-hana.svg?external'
import SAPMaxDB from './icons/sap-max-db.svg?external'
import MicrosoftDynamicsCRM from './icons/microsoft-dynamics-crm.svg?external'
import SSLCertificate from './icons/ssl-certificate.svg?external'
import CiscoUCS from './icons/cisco-ucs.svg?external'
import CiscoWireless from './icons/cisco-wireless.svg?external'
import CitrixXen from './icons/citrix-xen.svg?external'
import CitrixXenCluster from './icons/citrix-xen-cluster.svg?external'
import DNS from './icons/dns.svg?external'
import Domain from './icons/domain.svg?external'
import Email from './icons/email.svg?external'
import Zimbra from './icons/zimbra.svg?external'
import VmwareEsxi from './icons/vmware-esxi.svg?external'
import FTP from './icons/ftp.svg?external'
import SFTP from './icons/sftp.svg?external'
import TFTP from './icons/tftp.svg?external'
import LOCAL from './icons/local.svg?external'
import Nutanix from './icons/nutanix.svg?external'
import Prism from './icons/prism.svg?external'
import NutanixHost from './icons/nutanix-host.svg?external'
import Hyperv from './icons/hyper-v.svg?external'
import HypervCluster from './icons/hyperv-cluster.svg?external'
import NTP from './icons/ntp.svg?external'
import Office365 from './icons/office-365.svg?external'
import OneDrive from './icons/onedrive.svg?external'
import Ping from './icons/ping.svg?external'
import Port from './icons/port.svg?external'
import Radius from './icons/radius.svg?external'
import RuckusWireless from './icons/ruckus-wireless.svg?external'
import SharepointOnline from './icons/sharepoint-online.svg?external'
import ExchangeOnline from './icons/exchange-online.svg?external'
import Solaris from './icons/solaris.svg?external'
import MicrosoftTeam from './icons/microsoft-team.svg?external'
import Url from './icons/url.svg?external'
import vCenter from './icons/vcenter.svg?external'
import Switch from './icons/switch.svg?external'
import Router from './icons/router.svg?external'
import Firewall from './icons/firewall.svg?external'
import Printer from './icons/printer.svg?external'
import LoadBalancer from './icons/load-balancer.svg?external'
import UPS from './icons/ups.svg?external'
import SymantecMessagingGateway from './icons/symantec-messaging-gateway.svg?external'
import IbmTapeLibrary from './icons/ibm-tape-library.svg?external'
import SNMPDevice from './icons/snmp-device.svg?external'
import WirelessController from './icons/wireless-controller.svg?external'
import vm from './icons/vm.svg?external'
import powershell from './icons/powershell.svg?external'
import ssh from './icons/ssh.svg?external'
import jdbc from './icons/jdbc.svg?external'
import python from './icons/python.svg?external'
import XenServer from './icons/xen-server.svg?external'
import ProxyServer from './icons/proxy-server.svg?external'
import Agent from './icons/agent.svg?external'
import Wireless from './icons/wireless.svg?external'
import JBoss from './icons/jboss.svg?external'
import Cloud from './icons/cloud.svg?external'
import HTTP from './icons/http.svg?external'
import ArubaWireless from './icons/aruba-wireless.svg?external'
import MacOs from './icons/mac-os.svg?external'
import Chrome from './icons/chrome.svg?external'
import MicrosoftEdge from './icons/microsoft-edge.svg?external'
import Firefox from './icons/firefox.svg?external'
import Safari from './icons/safari.svg?external'
import HardwareSensor from './icons/hardware-sensor.svg?external'
import EmailGateway from './icons/email-gateway.svg?external'
import NoIcon from './icons/no-icon.svg?external'
import Android from './icons/android.svg?external'
import Ios from './icons/ios.svg?external'
import CustomMonitorType from './icons/custom-monitor-type.svg?external'
import CiscoVManage from './icons/cisco-vmanage.svg?external'
import CiscoVBond from './icons/cisco-vbond.svg?external'
import CiscoVSmart from './icons/cisco-vsmart.svg?external'
import CiscoVEdge from './icons/cisco-vedge.svg?external'
import CiscoMeraki from './icons/cisco-meraki.svg?external'
import CiscoMerakiController from './icons/cisco-meraki-controller.svg?external'
import CiscoMerakiRadio from './icons/cisco-meraki-radio.svg?external'
import CiscoMerakiSecurity from './icons/cisco-meraki-security.svg?external'
import CiscoMerakiSwitch from './icons/cisco-meraki-switch.svg?external'
import Opera from './icons/opera.svg?external'
import Storage from './icons/storage.svg?external'
import Hpe from './icons/hpe.svg?external'
import NetApp from './icons/netapp.svg?external'
import CiscoAci from './icons/cisco-aci.svg?external'
import Constants from '@constants'
import Dockercontainer from './icons/dockercontainer.svg?external'
import DellEmcUnity from './icons/dell-emc-unity.svg?external'
import IbmAs400 from './icons/ibm-as-400.svg?external'

export const IconsMap = {
  [Constants.WINDOWS]: Windows,
  [Constants.LINUX]: Linux,
  [Constants.IBM_AIX]: IBMAIX,
  [Constants.HP_UX]: HPUX,
  [Constants.WINDOWS_CLUSTER]: Windows,
  [Constants.ACTIVE_DIRECTORY]: ActiveDirectory,
  [Constants.APACHE_HTTP]: ApacheHTTP,
  [Constants.APACHE_MQ]: ApacheMQ,
  [Constants.APACHE_TOMCAT]: ApacheTomcat,
  [Constants.AWS_CLOUD]: AwsCloud,
  [Constants.AMAZON_DYNAMO_DB]: AmazonDynamoDB,
  [Constants.AMAZON_EBS]: AmazonEBS,
  [Constants.AMAZON_EC2]: AmazonEC2,
  [Constants.AWS_ELB]: AwsELB,
  [Constants.AMAZON_RDS]: AmazonRDS,
  [Constants.AMAZON_S3]: AmazonS3,
  [Constants.AMAZON_SNS]: AmazonSNS,
  [Constants.AMAZON_CLOUD_FRONT]: AmazonCloudFront,
  [Constants.AWS_AUTO_SCALING]: AwsAutoScaling,
  [Constants.AWS_LAMBDA]: AwsLambda,
  [Constants.AMAZON_SQS]: AmazonSQS,
  [Constants.AWS_ELASTIC_BEANSTALK]: AwsElasticBeanstalk,
  [Constants.AMAZON_DOCUMENTDB]: AmazonDocumentDB,
  [Constants.AZURE_CLOUD]: AzureCloud,
  [Constants.AZURE_COSMOS_DB]: AzureCosmosDB,
  [Constants.AZURE_SQL_DATABASE]: AzureSQLDatabase,
  [Constants.AZURE_STORAGE]: AzureStorage,
  [Constants.AZURE_VM]: AzureVM,
  [Constants.AZURE_WEB_APP]: AzureWebApp,
  [Constants.AZURE_SERVICE_BUS]: AzureServiceBus,
  [Constants.AZURE_APPLICATION_GATEWAY]: AzureApplicationGateway,
  [Constants.AZURE_FUNCTION]: AzureFunction,
  [Constants.AZURE_LOAD_BALANCER]: AzureLoadBalancer,
  [Constants.AZURE_POSTGRESQL_SERVER]: AzurePostgreSQLServer,
  [Constants.AZURE_MYSQL_SERVER]: AzureMySQLServer,
  [Constants.AZURE_VM_SCALE_SET]: AzureVMScaleSet,
  [Constants.AZURE_CDN]: AzureCDN,
  [Constants.BIND9]: Bind9,
  [Constants.MICROSOFT_IIS]: MicrosoftIIS,
  [Constants.NGINX]: Nginx,
  [Constants.DOTNET]: Dotnet,
  [Constants.IBM_DB2]: IbmDb2,
  [Constants.ORACLE_DATABASE]: OracleDatabase,
  [Constants.SQL_SERVER]: MSSQL,
  [Constants.MYSQL]: MySQL,
  [Constants.MARIADB]: MariaDB,
  [Constants.POSTGRESQL]: PostgreSQL,
  [Constants.WINDOWS_DNS]: WindowsDns,
  [Constants.WINDOWS_DHCP]: WindowsDhcp,
  [Constants.EXCHANGE_MAILBOX]: ExchangeMailbox,
  [Constants.EXCHANGE_CLIENT_ACCESS_ROLE]: ExchangeClientAccessRole,
  [Constants.EXCHANGE_EDGE_TRANSPORT_ROLE]: ExchangeEdgeTransportRole,
  [Constants.EXCHANGE_MAILBOX_ROLE]: ExchangeMailboxRole,
  [Constants.HA_PROXY]: HAProxy,
  [Constants.IBM_MQ]: IBMMQ,
  [Constants.MSMQ]: MSMQ,
  [Constants.RABBITMQ]: RabbitMQ,
  [Constants.ORACLE_WEBLOGIC]: OracleWeblogic,
  [Constants.GLASS_FISH_SERVER]: GlassfishServer,
  [Constants.LIGHTTPD]: LightHTTPD,
  [Constants.LINUX_DHCP]: LinuxDHCP,
  [Constants.IBM_WEBSPHERE]: IBMWebsphere,
  [Constants.WILDFLY]: WildFly,
  [Constants.WINDOWS_RDP]: WindowsRDP,
  [Constants.SYBASE]: Sybase,
  [Constants.SAP_HANA]: SAPHANA,
  [Constants.SAP_MAXDB]: SAPMaxDB,
  [Constants.MICROSOFT_DYNAMICS_CRM]: MicrosoftDynamicsCRM,
  [Constants.SSL_CERTIFICATE]: SSLCertificate,
  [Constants.CERTIFICATE]: SSLCertificate,
  [Constants.CISCO_UCS]: CiscoUCS,
  [Constants.CISCO_WIRELESS]: CiscoWireless,
  [Constants.CITRIX_XEN]: CitrixXen,
  [Constants.CITRIX_XEN_CLUSTER]: CitrixXenCluster,
  [Constants.DNS]: DNS,
  [Constants.DOMAIN]: Domain,
  [Constants.EMAIL]: Email,
  [Constants.ZIMBRA]: Zimbra,
  [Constants.VMWARE_ESXI]: VmwareEsxi,
  [Constants.FTP]: FTP,
  [Constants.SFTP]: SFTP,
  [Constants.TFTP]: TFTP,
  [Constants.LOCAL]: LOCAL,
  [Constants.HYPER_V]: Hyperv,
  [Constants.HYPER_V_CLUSTER]: HypervCluster,
  [Constants.NTP]: NTP,
  [Constants.OFFICE_365]: Office365,
  [Constants.ONEDRIVE]: OneDrive,
  [Constants.PING]: Ping,
  [Constants.PORT]: Port,
  [Constants.RADIUS]: Radius,
  [Constants.RUCKUS_WIRELESS]: RuckusWireless,
  [Constants.SHAREPOINT_ONLINE]: SharepointOnline,
  [Constants.EXCHANGE_ONLINE]: ExchangeOnline,
  [Constants.SOLARIS]: Solaris,
  [Constants.SOLARIS_PROCESS]: Solaris,
  [Constants.MICROSOFT_TEAMS]: MicrosoftTeam,
  [Constants.URL]: Url,
  [Constants.VCENTER]: vCenter,
  [Constants.SWITCH]: Switch,
  [Constants.ROUTER]: Router,
  [Constants.FIREWALL]: Firewall,
  [Constants.PRINTER]: Printer,
  [Constants.LOAD_BALANCER]: LoadBalancer,
  [Constants.UPS]: UPS,
  [Constants.CLOUD]: Cloud,
  [Constants.SYMANTEC_MESSAGING_GATEWAY]: SymantecMessagingGateway,
  [Constants.IBM_TAPE_LIBRARY]: IbmTapeLibrary,
  [Constants.SNMP_DEVICE]: SNMPDevice,
  [Constants.WIRELESS_CONTROLLER]: WirelessController,
  [Constants.ARUBA_WIRELESS]: ArubaWireless,
  [Constants.HARDWARE_SENSOR]: HardwareSensor,
  [Constants.EMAIL_GATEWAY]: EmailGateway,
  [Constants.VIRTUAL_MACHINE]: vm,
  [Constants.JBOSS]: JBoss,
  [Constants.AGENT]: Agent,
  [Constants.MAC_OS]: MacOs,
  [Constants.CHROME]: Chrome,
  [Constants.MICROSOFT_EDGE]: MicrosoftEdge,
  [Constants.FIREFOX]: Firefox,
  [Constants.SAFARI]: Safari,
  [Constants.ANDROID]: Android,
  [Constants.IOS]: Ios,
  [Constants.OPERA]: Opera,
  [Constants.NUTANIX]: Nutanix,
  [Constants.NUTANIX_HOST]: NutanixHost,
  [Constants.PRISM]: Prism,
  [Constants.CISCO_VMANAGE]: CiscoVManage,
  [Constants.CISCO_VBOND]: CiscoVBond,
  [Constants.CISCO_VSMART]: CiscoVSmart,
  [Constants.CISCO_VEDGE]: CiscoVEdge,
  [Constants.CISCO_MERAKI]: CiscoMerakiController,
  [Constants.CISCO_MERAKI_CONTROLLER]: CiscoMeraki,
  [Constants.CISCO_MERAKI_RADIO]: CiscoMerakiRadio,
  [Constants.CISCO_MERAKI_SECURITY]: CiscoMerakiSecurity,
  [Constants.CISCO_MERAKI_SWITCH]: CiscoMerakiSwitch,
  [Constants.STORAGE]: Storage,
  [Constants.HPE_STOREONCE]: Hpe,
  [Constants.HPE_PRIMERA]: Hpe,
  [Constants.HPE_3PAR]: Hpe,
  [Constants.NETAPP]: NetApp,
  [Constants.CISCO_ACI]: CiscoAci,
  [Constants.DELL_EMC_UNITY]: DellEmcUnity,
  [Constants.IBM_AS_400]: IbmAs400,

  powershell: powershell,
  ssh: ssh,
  jdbc: jdbc,
  python: python,
  HTTP: HTTP,
  'Xen Server': XenServer,
  'Proxy Server': ProxyServer,
  Wireless: Wireless,
  Other: NoIcon,
  Custom: NoIcon,
  [Constants.CUSTOM_MONITOR_TYPE]: CustomMonitorType,
  [Constants.DOCKERCONTAINER]: Dockercontainer,
  [Constants.WINDOWS_SNMP]: Windows,
  [Constants.LINUX_SNMP]: Linux,
}

export const BlackIconMap = {}

export default function getIcon(icon, theme = 'white') {
  return (
    (theme === 'black'
      ? BlackIconMap[icon] || IconsMap[icon] || NoIcon
      : IconsMap[icon]) || NoIcon
  )
}
