export const AngleDown = {
  prefix: 'fal',
  iconName: 'angle-down',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M34.8,18.8c-.1-.3-.3-.5-.5-.7-.4-.4-.9-.6-1.5-.6s-1.1,.2-1.5,.6l-7.5,7.4-7.4-7.4c-.4-.4-.9-.6-1.5-.6s-1.1,.2-1.5,.6c-.2,.2-.4,.4-.5,.7-.1,.3-.2,.5-.2,.8s0,.5,.2,.8c.1,.3,.3,.5,.5,.7l8.8,8.8c.2,.2,.4,.4,.7,.5,.3,.1,.5,.2,.8,.2s.5,0,.8-.2c.3-.1,.5-.3,.7-.5l9-8.8c.2-.2,.4-.4,.5-.7,.1-.3,.2-.5,.2-.8s0-.5-.2-.8Z`,
  ],
}

export const AngleUp = {
  prefix: 'fal',
  iconName: 'angle-up',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M34.9,27.7c0-.3-.2-.5-.4-.7l-9-8.9c-.2-.2-.4-.4-.7-.5-.3-.1-.5-.2-.8-.2s-.5,0-.8,.2c-.3,.1-.5,.3-.7,.5l-8.9,8.9c-.2,.2-.4,.4-.5,.7-.1,.3-.2,.5-.2,.8s0,.5,.2,.8c.1,.3,.3,.5,.5,.7,.4,.4,.9,.6,1.5,.6s1.1-.2,1.5-.6l7.4-7.4,7.4,7.4c.4,.4,.9,.6,1.5,.6,.3,0,.5,0,.8-.2,.3-.1,.5-.3,.7-.5,.2-.2,.4-.4,.5-.7,.1-.3,.2-.5,.2-.8,0-.3,0-.6-.1-.8Z`,
  ],
}

export const AlertIcon = {
  prefix: 'fal',
  iconName: 'alert',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M24.2,45c2.2,0,3.9-1.7,4.1-3.9h-8.1c.1,2.1,1.9,3.9,4.1,3.9Zm16.6-10.2l-3.1-3.3v-10.8c0-6.4-3.7-12.8-12-13.6v-2.4c0-1-.8-1.8-1.8-1.8s-1.8,.8-1.8,1.8v2.4c-8.3,.8-12,7.2-12,13.6v10.8l-3.1,3.2c-.5,.5-.6,1.3-.3,1.9,.3,.6,.9,1.1,1.6,1.1h31.1c.7,0,1.3-.4,1.6-1.1,.3-.6,.1-1.4-.3-1.9Zm-28.3-.5l.7-.8c.3-.3,.5-.8,.5-1.2v-11.5c0-4.9,2.7-10.2,10.2-10.2s10.2,5.2,10.2,10.2v11.5c0,.5,.2,1,.6,1.3l.7,.7H12.5Z',
  ],
}

export const ApmIcon = {
  prefix: 'fal',
  iconName: 'apm',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M42,5.1H6c-2.2,0-4,1.8-4,4V31.3c0,2.2,1.8,4,4,4h11v4.1h-3c-1,0-1.8,.8-1.8,1.8s.8,1.8,1.8,1.8h20.1c1,0,1.8-.8,1.8-1.8s-.8-1.8-1.8-1.8h-3.2v-4.1h11.2c2.2,0,4-1.8,4-4V9.1c0-2.2-1.8-4-4-4Zm-14.7,34.2h-6.8v-4.1h6.8v4.1Zm15.1-8.1c0,.2-.2,.4-.4,.4H6c-.2,0-.4-.2-.4-.4V9.1c0-.2,.2-.4,.4-.4H42c.2,0,.4,.2,.4,.4V31.3ZM17.9,14.8c-.6-.8-1.7-1-2.5-.4l-5.1,3.8c-.5,.3-.7,.9-.7,1.4s.3,1.1,.7,1.4l5.1,3.8c.3,.2,.7,.3,1.1,.3,.5,0,1.1-.2,1.4-.7,.6-.8,.4-1.9-.4-2.5l-3.2-2.3,3.2-2.3c.8-.6,1-1.7,.4-2.5Zm20,3.4l-5.1-3.8c-.8-.6-1.9-.4-2.5,.4-.6,.8-.4,1.9,.4,2.5l3.2,2.3-3.2,2.3c-.8,.6-1,1.7-.4,2.5,.3,.5,.9,.7,1.4,.7s.7-.1,1.1-.3l5.1-3.8c.5-.3,.7-.9,.7-1.4s-.3-1.1-.7-1.4Zm-10.4-4.9c-.9-.5-1.9-.2-2.4,.7l-5.2,9.4c-.5,.9-.2,1.9,.7,2.4,.3,.2,.6,.2,.9,.2,.6,0,1.2-.3,1.5-.9l5.2-9.4c.5-.9,.2-1.9-.7-2.4Z`,
  ],
}

export const AssignMonitorIcon = {
  prefix: 'fal',
  iconName: 'assign-monitor',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M44,20.2c-1.1,0-2,.9-2,2v1.8c0,3.9-1.3,7.7-3.6,10.8s-5.6,5.4-9.3,6.5-7.7,1-11.3-.4-6.8-3.9-8.9-7.1c-2.1-3.2-3.1-7.1-2.9-11,.3-3.8,1.8-7.6,4.3-10.5s6-5,9.7-5.9c3.8-.8,7.8-.5,11.3,1.1,1,.5,2.2,0,2.6-1,.4-1,0-2.2-1-2.6-4.3-1.9-9.2-2.4-13.8-1.4-4.6,1-8.8,3.6-11.9,7.2-3.1,3.6-4.9,8.1-5.2,12.8-.3,4.7,.9,9.5,3.5,13.4,2.6,4,6.4,7,10.8,8.7,2.4,.9,5,1.4,7.6,1.4s4.2-.3,6.2-.9c4.5-1.3,8.6-4.2,11.4-7.9,2.8-3.8,4.4-8.4,4.4-13.2v-1.8c0-1.1-.9-2-2-2Zm-24.6,.4c-.8-.8-2-.8-2.8,0-.8,.8-.8,2,0,2.8l6,6c.4,.4,.9,.6,1.4,.6h0c.5,0,1-.2,1.4-.6L45.4,9.4c.8-.8,.8-2,0-2.8-.8-.8-2-.8-2.8,0L24,25.2l-4.6-4.6Z',
  ],
}

export const AuditIcon = {
  prefix: 'fal',
  iconName: 'audit',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M23.7,17c0-1-.8-1.8-1.8-1.8h-7.5c-1,0-1.8,.8-1.8,1.8s.8,1.8,1.8,1.8h7.5c1,0,1.8-.8,1.8-1.8Zm-9.2-4.3h5.2c1,0,1.8-.8,1.8-1.8s-.8-1.8-1.8-1.8h-5.2c-1,0-1.8,.8-1.8,1.8s.8,1.8,1.8,1.8Zm4.8,16.4c-.6-.7-1.7-.8-2.5-.2-.7,.6-.8,1.7-.2,2.5l3.7,4.3c.3,.4,.8,.6,1.3,.6,0,0,0,0,0,0,.5,0,.9-.2,1.2-.5l7.9-7.8c.7-.7,.7-1.8,0-2.5-.7-.7-1.8-.7-2.5,0l-6.6,6.5-2.4-2.9Zm21.9-12.8s0,0,0-.1c0,0,0-.1,0-.2,0,0,0,0,0-.1,0,0,0-.1,0-.2,0,0,0,0,0-.1,0,0,0,0,0-.1L29.5,2.8s0,0,0,0c0,0-.1-.1-.2-.1,0,0,0,0-.1,0,0,0-.1,0-.2-.1,0,0,0,0,0,0,0,0-.2,0-.3,0,0,0,0,0,0,0-.1,0-.2,0-.4,0H8.6c-1,0-1.8,.8-1.8,1.8V44c0,1,.8,1.8,1.8,1.8h30.9c1,0,1.8-.8,1.8-1.8V16.5c0-.1,0-.2,0-.3Zm-11.3-7.6l5.6,6.2h-5.6v-6.2Zm7.8,33.7H10.3V5.8H26.4v10.8c0,1,.8,1.8,1.8,1.8h9.5v24Z`,
  ],
}

export const AutoAssignIcon = {
  prefix: 'fal',
  iconName: 'auto-assignment',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M41.4,28.7l-12,11.9-4.2-4c-.8-.8-2.1-.7-2.8,0-.8,.8-.7,2.1,0,2.8l5.6,5.3c.4,.4,.9,.6,1.4,.6s1-.2,1.4-.6l13.4-13.3c.8-.8,.8-2,0-2.8-.8-.8-2-.8-2.8,0Zm3-9.6c-.6-.6-1.4-.9-2.2-.9h-3.3c0-.2-.1-.4-.2-.6l2.4-2.4c1.2-1.2,1.2-3.2,0-4.4l-3.8-3.9c-.6-.6-1.4-.9-2.2-.9h0c-.8,0-1.6,.3-2.2,.9l-2.3,2.3c-.2,0-.4-.2-.6-.3v-3.2c0-1.7-1.4-3.1-3.1-3.1h-5.4c-1.7,0-3.1,1.4-3.1,3.1v3.2c-.2,0-.3,.1-.5,.2l-2.2-2.2c-1.2-1.2-3.2-1.2-4.4,0l-3.9,3.8c-.6,.6-.9,1.4-.9,2.2,0,.8,.3,1.6,.9,2.2l2.2,2.3c0,.2-.2,.4-.3,.6h-3.2c-1.7,0-3.1,1.4-3.1,3.1v5.4c0,.8,.3,1.6,.9,2.2,.6,.6,1.4,.9,2.2,.9h3.3c0,.2,.2,.4,.2,.6l-2.3,2.3c-1.2,1.2-1.2,3.2,0,4.4l3.8,3.9c.6,.6,1.4,.9,2.2,.9,.9,0,1.6-.3,2.2-.9l2.4-2.4c0-1.5,.2-2.1,1.3-3.8,0,0-.1,0-.2,0-1.2-.6-2.6-.3-3.5,.6l-2.2,2.2-2.6-2.6,2.2-2.2c1-.9,1.2-2.4,.6-3.6-.2-.4-.4-.9-.5-1.3-.4-1.2-1.6-2.1-2.9-2.1h-3v-3.7h3c1.3,0,2.5-.8,3-2.1,.2-.5,.3-.9,.6-1.4,.6-1.2,.3-2.6-.6-3.6l-2-2.1,2.6-2.6,2,2.1c.9,.9,2.4,1.2,3.6,.6,.4-.2,.8-.4,1.3-.5,1.3-.4,2.1-1.6,2.1-2.9v-2.9h3.7v2.9c0,1.3,.8,2.5,2.1,2.9,.5,.2,.9,.4,1.4,.6,1.2,.6,2.6,.4,3.6-.6l2.1-2.1,2.6,2.6-2.2,2.2c-.9,.9-1.2,2.3-.6,3.5,.2,.4,.4,.8,.5,1.3,.4,1.3,1.6,2.1,2.9,2.1h3.1v2.2c1.3-.3,2.7-.2,4,.3v-3.4c0-.8-.3-1.6-.9-2.2Zm-12.1,4.7c0-4.6-3.7-8.3-8.3-8.3s-8.3,3.7-8.3,8.3,3.7,8.3,8.3,8.3,8.3-3.7,8.3-8.3Zm-12.7,0c0-2.4,1.9-4.3,4.3-4.3s4.3,1.9,4.3,4.3-1.9,4.3-4.3,4.3-4.3-1.9-4.3-4.3Z',
  ],
}

export const ApplicationIcon = {
  prefix: 'fal',
  iconName: 'application',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M44.1,12.8c-.2-.6-.7-1-1.3-1.2L24.8,3.3c-.5-.2-1.1-.2-1.6,0L4.9,11.7c-.6,.3-1.1,.9-1.1,1.6v21.2c0,.7,.4,1.4,1.1,1.7l18.2,8.4,.3,.2h.6c0,0,.1,0,.2,0,0,0,0,0,.1,0h.5v-.2l18.3-8.4c.7-.3,1.1-1,1.1-1.7V12.8h-.1ZM24,7.1l13.8,6.4-4.1,1.9-13.8-6.4,4.1-1.9Zm-1.9,33l-14.4-6.7V16.4l14.4,6.7v17Zm1.9-20.2l-13.8-6.4,5.2-2.4,13.8,6.4-5.2,2.4Zm16.4,13.5l-14.5,6.7V23l5.5-2.6v3.5c0,.5,.2,1,.6,1.3,.4,.3,.9,.5,1.4,.5,1,0,1.8-.8,1.8-1.9v-5.2l5.1-2.3v17Z',
  ],
}

export const AvgIcon = {
  prefix: 'fal',
  iconName: 'avg',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M38.4,21.5h-6c-1.7-7.2-4.4-13.5-8.9-13.5s-7.3,6.3-9.1,13.5h-4.7c-1.3,0-2.3,1-2.3,2.2s1,2.3,2.2,2.3c0,0,0,0,0,0h3.7c-.8,4.3-1.4,8.5-1.7,11.4-.1,1.3,.8,2.4,2,2.5,1.3,.1,2.4-.8,2.5-2,.4-4,1-7.9,1.8-11.9h10.5c.7,3.9,1.3,7.8,1.6,11.8,.1,1.3,1.3,2.2,2.5,2.1,1.3,0,2.2-1.2,2.1-2.5h0c-.2-2.9-.7-7.1-1.4-11.4h5.1c1.3,0,2.3-1.1,2.2-2.3,0-1.2-1-2.2-2.2-2.2h0Zm-19.2,0c1.5-5.5,3.1-8.9,4.3-8.9s2.7,3.5,4.1,8.9h-8.4Z',
  ],
}

export const Backspace = {
  prefix: 'fal',
  iconName: 'backspace',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M38,10H19.7c-1.6,0-3.1,.6-4.2,1.8L4.6,22.6c-.2,.2-.3,.4-.4,.7-.1,.2-.2,.5-.2,.8s0,.5,.2,.8c.1,.2,.3,.5,.4,.7l10.8,10.8c1.1,1.1,2.6,1.8,4.2,1.8h18.3c1.6,0,3.1-.6,4.2-1.8,1.1-1.1,1.8-2.7,1.8-4.2V16c0-1.6-.6-3.1-1.8-4.2-1.1-1.1-2.7-1.8-4.2-1.8Zm2,22c0,.5-.2,1-.6,1.4s-.9,.6-1.4,.6H19.7c-.5,0-1-.2-1.4-.6l-9.4-9.4,9.4-9.4c.4-.4,.9-.6,1.4-.6h18.3c.5,0,1,.2,1.4,.6,.4,.4,.6,.9,.6,1.4v16Zm-6.6-13.4c-.2-.2-.4-.3-.7-.4-.2-.1-.5-.2-.8-.2s-.5,0-.8,.2c-.2,.1-.5,.3-.7,.4l-2.6,2.6-2.6-2.6c-.4-.4-.9-.6-1.4-.6s-1,.2-1.4,.6-.6,.9-.6,1.4,.2,1,.6,1.4l2.6,2.6-2.6,2.6c-.2,.2-.3,.4-.4,.7-.1,.2-.2,.5-.2,.8s0,.5,.2,.8c.1,.2,.3,.5,.4,.7,.2,.2,.4,.3,.7,.4,.2,.1,.5,.2,.8,.2s.5,0,.8-.2c.2-.1,.5-.3,.7-.4l2.6-2.6,2.6,2.6c.2,.2,.4,.3,.7,.4,.2,.1,.5,.2,.8,.2s.5,0,.8-.2c.2-.1,.5-.3,.7-.4,.2-.2,.3-.4,.4-.7,.1-.2,.2-.5,.2-.8s0-.5-.2-.8c-.1-.2-.3-.5-.4-.7l-2.6-2.6,2.6-2.6c.2-.2,.3-.4,.4-.7,.1-.2,.2-.5,.2-.8s0-.5-.2-.8c-.1-.2-.3-.5-.4-.7Z`,
  ],
}

export const Ban = {
  prefix: 'fal',
  iconName: 'ban',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24,4C13,4,4,13,4,24s9,20,20,20,20-9,20-20S35,4,24,4Zm0,36c-8.8,0-16-7.2-16-16s1.3-7.1,3.4-9.8l22.5,22.5c-2.7,2.1-6.1,3.4-9.8,3.4Zm12.6-6.2L14.2,11.4c2.7-2.1,6.1-3.4,9.8-3.4,8.8,0,16,7.2,16,16s-1.3,7.1-3.4,9.8Z`,
  ],
}

export const Bars = {
  prefix: 'fal',
  iconName: 'bars',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M42,34H6c-1.1,0-2,.9-2,2s.9,2,2,2H42c1.1,0,2-.9,2-2s-.9-2-2-2ZM6,14H42c1.1,0,2-.9,2-2s-.9-2-2-2H6c-1.1,0-2,.9-2,2s.9,2,2,2Zm36,8H6c-1.1,0-2,.9-2,2s.9,2,2,2H42c1.1,0,2-.9,2-2s-.9-2-2-2Z`,
  ],
}

export const Bell = {
  prefix: 'fal',
  iconName: 'bell',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M43.1,32.4c0,0-5.1-3.8-5.1-16.4s-1.5-7.3-4.1-9.9c-2.6-2.6-6.2-4.1-9.9-4.1s-7.3,1.5-9.9,4.1c-2.6,2.6-4.1,6.2-4.1,9.9,0,12.5-5.1,16.3-5.1,16.3-.7,.5-1.1,1.4-.8,2.2,.3,.8,1,1.4,1.9,1.4H42c.9,0,1.6-.6,1.9-1.4s0-1.7-.8-2.2Zm-32.9-.4c1.8-3,3.7-8,3.7-16s1-5.2,2.9-7.1c1.9-1.9,4.4-2.9,7.1-2.9s5.2,1.1,7.1,2.9,2.9,4.4,2.9,7.1c0,8,1.9,13,3.7,16H10.3Zm18.2,8.3c-1-.6-2.2-.2-2.7,.7-.2,.3-.4,.6-.7,.7-.6,.3-1.4,.3-2,0-.3-.2-.6-.4-.7-.7-.6-1-1.8-1.3-2.7-.7-1,.6-1.3,1.8-.7,2.7,.5,.9,1.3,1.7,2.2,2.2,.9,.5,1.9,.8,3,.8s2.1-.3,3-.8c.9-.5,1.7-1.3,2.2-2.2,.6-1,.2-2.2-.7-2.7Z`,
  ],
}

export const Bold = {
  prefix: 'fal',
  iconName: 'bold',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M36.1,24.9c-.7-.7-1.4-1.2-2.3-1.6,0,0,.2-.1,.3-.2,1.9-1.9,2.9-4.4,2.9-7.1s-1-5.2-2.9-7.1c-1.9-1.9-4.4-2.9-7.1-2.9H11c-1.1,0-2,.9-2,2V40c0,1.1,.9,2,2,2H29c2.7,0,5.2-1,7.1-2.9s2.9-4.4,2.9-7.1-1-5.2-2.9-7.1ZM13,10h14c1.6,0,3.1,.6,4.2,1.8,1.1,1.1,1.8,2.6,1.8,4.2s-.6,3.1-1.8,4.2c-1.1,1.1-2.7,1.8-4.2,1.8H13V10Zm20.2,26.2c-1.1,1.1-2.7,1.8-4.2,1.8H13v-12H29c1.6,0,3.1,.6,4.2,1.8s1.8,2.7,1.8,4.2-.6,3.1-1.8,4.2Z`,
  ],
}

export const Brain = {
  prefix: 'fal',
  iconName: 'brain',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M44,22c0-1.4-.4-2.8-1.1-4s-1.7-2.2-2.9-2.9c0-.3,0-.7,0-1,0-1.6-.6-3.1-1.8-4.2-1.1-1.1-2.6-1.8-4.2-1.8h-.4c-.3-.9-.9-1.7-1.6-2.4-.7-.7-1.6-1.1-2.6-1.4-1-.2-1.9-.2-2.9,0-1,.2-1.8,.7-2.6,1.3-.7-.7-1.6-1.1-2.6-1.3-1-.2-1.9-.2-2.9,0-1,.2-1.8,.7-2.6,1.4-.7,.7-1.3,1.5-1.6,2.4h-.4c-1.6,0-3.1,.6-4.2,1.8-1.1,1.1-1.8,2.6-1.8,4.2,0,.3,0,.7,0,1-1.1,.6-2.1,1.5-2.8,2.6s-1.1,2.3-1.2,3.6c-.1,1.3,.1,2.6,.6,3.8,.5,1.2,1.3,2.3,2.3,3.1-.7,1.2-1,2.6-.9,4,0,1.4,.4,2.7,1.1,3.9,.7,1.2,1.7,2.2,2.9,2.8s2.6,1,4,1h.4c.3,.9,.9,1.7,1.6,2.4,.7,.7,1.6,1.1,2.6,1.4,1,.2,1.9,.2,2.9,0s1.8-.7,2.6-1.3c.7,.7,1.6,1.1,2.6,1.3s1.9,.2,2.9,0c1-.2,1.8-.7,2.6-1.4,.7-.7,1.3-1.5,1.6-2.4h.4c1.4,0,2.7-.4,3.9-1.1,1.2-.7,2.2-1.7,2.9-2.8,.7-1.2,1.1-2.5,1.1-3.9,0-1.4-.3-2.7-.9-3.9,.9-.7,1.7-1.7,2.2-2.8,.5-1.1,.8-2.3,.8-3.4Zm-22-4.9c-.4-.2-.9-.5-1.4-.6-.5-.2-1.1-.1-1.5,.1-.5,.2-.8,.7-1,1.2-.2,.5-.1,1.1,.1,1.5,.2,.5,.7,.8,1.2,1,.8,.3,1.4,.8,1.9,1.5,.5,.7,.7,1.5,.7,2.3v3.1c-.4-.2-.9-.5-1.4-.6-.5-.2-1.1-.1-1.5,.1-.5,.2-.8,.7-1,1.2-.2,.5-.1,1.1,.1,1.5,.2,.5,.7,.8,1.2,1,.8,.3,1.4,.8,1.9,1.5,.5,.7,.7,1.5,.7,2.3v4c0,.5-.2,1-.6,1.4s-.9,.6-1.4,.6c-.4,0-.8-.1-1.1-.3-.3-.2-.6-.5-.7-.9,.3-.2,.7-.4,1-.7,.2-.2,.4-.4,.5-.6,.1-.2,.2-.5,.2-.7,0-.3,0-.5,0-.8,0-.3-.2-.5-.4-.7-.2-.2-.4-.4-.6-.5-.2-.1-.5-.2-.7-.2-.3,0-.5,0-.8,0-.3,0-.5,.2-.7,.4-.7,.6-1.6,.9-2.6,.9-1.1,0-2.1-.4-2.8-1.2-.7-.7-1.2-1.8-1.2-2.8,0-.8,.2-1.5,.6-2.1,.4,0,.9,.1,1.4,.1,.5,0,1-.2,1.4-.6,.4-.4,.6-.9,.6-1.4s-.2-1-.6-1.4c-.4-.4-.9-.6-1.4-.6-.5,0-.9,0-1.4-.3-.7-.3-1.3-.7-1.8-1.3-.5-.6-.7-1.3-.8-2.1,0-.8,0-1.5,.4-2.2,.4-.7,.9-1.2,1.6-1.6,.3,.3,.6,.5,.9,.7,.5,.3,1,.3,1.5,.2,.5-.1,1-.5,1.2-.9,.3-.5,.3-1,.2-1.5-.1-.5-.5-1-.9-1.2-.3-.2-.5-.4-.7-.7-.2-.3-.3-.7-.2-1.1,0-.5,.2-1,.6-1.4s.9-.6,1.4-.6c.1,0,.3,0,.4,0,.1,.3,.2,.6,.4,.9,.1,.2,.3,.4,.5,.6,.2,.2,.4,.3,.7,.3,.3,0,.5,0,.8,0,.3,0,.5-.1,.7-.3,.4-.3,.8-.7,.9-1.2s0-1-.2-1.5c-.2-.3-.3-.6-.3-1,0-.5,.2-1,.6-1.4,.4-.4,.9-.6,1.4-.6s1,.2,1.4,.6c.4,.4,.6,.9,.6,1.4v7.1Zm15.4,8.6c-.4,.2-.9,.3-1.4,.3-.5,0-1,.2-1.4,.6s-.6,.9-.6,1.4,.2,1,.6,1.4,.9,.6,1.4,.6c.5,0,.9,0,1.4-.1,.4,.6,.6,1.4,.6,2.1,0,1.1-.4,2.1-1.2,2.8-.7,.7-1.8,1.2-2.8,1.2-.9,0-1.9-.3-2.6-.9-.4-.3-.9-.5-1.5-.5-.5,0-1,.3-1.4,.7-.3,.4-.5,.9-.5,1.5,0,.5,.3,1,.7,1.4,.3,.2,.6,.5,1,.7-.2,.4-.4,.7-.7,.9-.3,.2-.7,.3-1.1,.3-.5,0-1-.2-1.4-.6s-.6-.9-.6-1.4v-4c0-.8,.3-1.6,.7-2.3,.5-.7,1.1-1.2,1.9-1.5,.5-.2,.9-.5,1.2-1,.2-.5,.3-1,.1-1.5-.2-.5-.5-.9-1-1.2-.5-.2-1-.3-1.5-.1-.5,.2-.9,.4-1.4,.6v-3.1c0-.8,.3-1.6,.7-2.3,.5-.7,1.1-1.2,1.9-1.5,.5-.2,.9-.5,1.2-1,.2-.5,.3-1,.1-1.5-.2-.5-.5-.9-1-1.2-.5-.2-1-.3-1.5-.1-.5,.2-.9,.4-1.4,.6v-7.1c0-.5,.2-1,.6-1.4,.4-.4,.9-.6,1.4-.6s1,.2,1.4,.6c.4,.4,.6,.9,.6,1.4,0,.3,0,.7-.3,1-.1,.2-.2,.5-.3,.7,0,.3,0,.5,0,.8,0,.3,.2,.5,.3,.7,.2,.2,.4,.4,.6,.5,.2,.1,.5,.2,.7,.3,.3,0,.5,0,.8,0,.3,0,.5-.2,.7-.3,.2-.2,.4-.4,.5-.6,.1-.3,.3-.7,.4-1,.1,0,.3,0,.4,0,.5,0,1,.2,1.4,.6,.4,.4,.6,.9,.6,1.4,0,.4-.1,.8-.3,1.1-.2,.3-.4,.5-.7,.6-.5,.3-.8,.7-.9,1.2-.1,.5,0,1.1,.2,1.5,.3,.5,.7,.8,1.2,.9,.5,.1,1.1,0,1.5-.2,.4-.2,.7-.4,1-.7,.7,.4,1.2,.9,1.6,1.6,.4,.7,.5,1.4,.5,2.2,0,.8-.3,1.5-.8,2.1-.5,.6-1.1,1.1-1.8,1.3h0Z`,
  ],
}

export const Bullseye = {
  prefix: 'fal',
  iconName: 'bulls-eye',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24,10c-2.8,0-5.5,.8-7.8,2.4s-4.1,3.7-5.2,6.3c-1.1,2.6-1.3,5.4-.8,8.1,.5,2.7,1.9,5.2,3.8,7.2,2,2,4.5,3.3,7.2,3.8,2.7,.5,5.5,.3,8.1-.8,2.6-1.1,4.7-2.9,6.3-5.2,1.5-2.3,2.4-5,2.4-7.8s-1.5-7.3-4.1-9.9c-2.6-2.6-6.2-4.1-9.9-4.1Zm7.1,21.1c-1.9,1.9-4.4,2.9-7.1,2.9s-3.9-.6-5.6-1.7c-1.6-1.1-2.9-2.7-3.7-4.5-.8-1.8-1-3.8-.6-5.8,.4-1.9,1.3-3.7,2.7-5.1s3.2-2.4,5.1-2.7c1.9-.4,4-.2,5.8,.6,1.8,.8,3.4,2,4.5,3.7,1.1,1.6,1.7,3.6,1.7,5.6s-1.1,5.2-2.9,7.1Zm8.5-22.6c-4.1-4.1-9.7-6.4-15.6-6.4s-8.6,1.3-12.2,3.7S5.3,11.6,3.7,15.6c-1.7,4-2.1,8.4-1.3,12.7s2.9,8.2,6,11.3c3.1,3.1,7,5.2,11.3,6,4.3,.8,8.7,.4,12.7-1.3,4-1.7,7.5-4.5,9.9-8.1,2.4-3.6,3.7-7.9,3.7-12.2s-2.3-11.4-6.4-15.6Zm-2.8,28.3c-3.4,3.4-8,5.3-12.7,5.3s-7-1.1-10-3c-3-2-5.3-4.8-6.6-8.1-1.4-3.3-1.7-6.9-1-10.4,.7-3.5,2.4-6.7,4.9-9.2s5.7-4.2,9.2-4.9c3.5-.7,7.1-.3,10.4,1,3.3,1.4,6.1,3.7,8.1,6.6,2,3,3,6.4,3,10s-1.9,9.4-5.3,12.7Zm-12.7-18.7c-1.2,0-2.3,.4-3.3,1-1,.7-1.8,1.6-2.2,2.7-.5,1.1-.6,2.3-.3,3.5,.2,1.2,.8,2.2,1.6,3.1,.8,.8,1.9,1.4,3.1,1.6,1.2,.2,2.4,.1,3.5-.3,1.1-.5,2-1.2,2.7-2.2,.7-1,1-2.1,1-3.3s-.6-3.1-1.8-4.2c-1.1-1.1-2.7-1.8-4.2-1.8Zm1.4,7.4c-.4,.4-.9,.6-1.4,.6s-.8-.1-1.1-.3c-.3-.2-.6-.5-.7-.9-.2-.4-.2-.8-.1-1.2,0-.4,.3-.7,.5-1,.3-.3,.6-.5,1-.5,.4,0,.8,0,1.2,.1,.4,.2,.7,.4,.9,.7,.2,.3,.3,.7,.3,1.1s-.2,1-.6,1.4Z`,
  ],
}

export const CalendarAlt = {
  prefix: 'fal',
  iconName: 'calendar-alt',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M34,38c.4,0,.8-.1,1.1-.3,.3-.2,.6-.5,.7-.9s.2-.8,.1-1.2c0-.4-.3-.7-.5-1-.3-.3-.6-.5-1-.5-.4,0-.8,0-1.2,.1s-.7,.4-.9,.7c-.2,.3-.3,.7-.3,1.1s.2,1,.6,1.4,.9,.6,1.4,.6Zm0-8c.4,0,.8-.1,1.1-.3,.3-.2,.6-.5,.7-.9,.2-.4,.2-.8,.1-1.2,0-.4-.3-.7-.5-1-.3-.3-.6-.5-1-.5-.4,0-.8,0-1.2,.1s-.7,.4-.9,.7c-.2,.3-.3,.7-.3,1.1s.2,1,.6,1.4,.9,.6,1.4,.6Zm-10,0c.4,0,.8-.1,1.1-.3,.3-.2,.6-.5,.7-.9,.2-.4,.2-.8,.1-1.2,0-.4-.3-.7-.5-1-.3-.3-.6-.5-1-.5-.4,0-.8,0-1.2,.1-.4,.2-.7,.4-.9,.7-.2,.3-.3,.7-.3,1.1s.2,1,.6,1.4,.9,.6,1.4,.6Zm-10,8c.4,0,.8-.1,1.1-.3,.3-.2,.6-.5,.7-.9s.2-.8,.1-1.2c0-.4-.3-.7-.5-1-.3-.3-.6-.5-1-.5-.4,0-.8,0-1.2,.1s-.7,.4-.9,.7c-.2,.3-.3,.7-.3,1.1s.2,1,.6,1.4c.4,.4,.9,.6,1.4,.6ZM42.2,7.8c-1.1-1.1-2.7-1.8-4.2-1.8h-2V4c0-.5-.2-1-.6-1.4-.4-.4-.9-.6-1.4-.6s-1,.2-1.4,.6c-.4,.4-.6,.9-.6,1.4v2h-16V4c0-.5-.2-1-.6-1.4-.4-.4-.9-.6-1.4-.6s-1,.2-1.4,.6c-.4,.4-.6,.9-.6,1.4v2h-2c-1.6,0-3.1,.6-4.2,1.8s-1.8,2.7-1.8,4.2v28c0,1.6,.6,3.1,1.8,4.2,1.1,1.1,2.7,1.8,4.2,1.8h28c1.6,0,3.1-.6,4.2-1.8,1.1-1.1,1.8-2.7,1.8-4.2V12c0-1.6-.6-3.1-1.8-4.2Zm-2.2,32.2c0,.5-.2,1-.6,1.4s-.9,.6-1.4,.6H10c-.5,0-1-.2-1.4-.6-.4-.4-.6-.9-.6-1.4V22h32v18Zm0-22H8v-6c0-.5,.2-1,.6-1.4,.4-.4,.9-.6,1.4-.6h2v2c0,.5,.2,1,.6,1.4,.4,.4,.9,.6,1.4,.6s1-.2,1.4-.6c.4-.4,.6-.9,.6-1.4v-2h16v2c0,.5,.2,1,.6,1.4,.4,.4,.9,.6,1.4,.6s1-.2,1.4-.6c.4-.4,.6-.9,.6-1.4v-2h2c.5,0,1,.2,1.4,.6,.4,.4,.6,.9,.6,1.4v6Zm-16,20c.4,0,.8-.1,1.1-.3,.3-.2,.6-.5,.7-.9s.2-.8,.1-1.2c0-.4-.3-.7-.5-1-.3-.3-.6-.5-1-.5-.4,0-.8,0-1.2,.1-.4,.2-.7,.4-.9,.7-.2,.3-.3,.7-.3,1.1s.2,1,.6,1.4,.9,.6,1.4,.6Zm-10-8c.4,0,.8-.1,1.1-.3,.3-.2,.6-.5,.7-.9,.2-.4,.2-.8,.1-1.2,0-.4-.3-.7-.5-1-.3-.3-.6-.5-1-.5-.4,0-.8,0-1.2,.1s-.7,.4-.9,.7c-.2,.3-.3,.7-.3,1.1s.2,1,.6,1.4c.4,.4,.9,.6,1.4,.6Z`,
  ],
}

export const Check = {
  prefix: 'fal',
  iconName: 'check',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M41.3,10.5c-.8-.7-2.1-.7-2.8,.2l-19.8,22.3-9.2-10.3c-.7-.8-2-.9-2.8-.2-.8,.7-.9,2-.2,2.8l10.7,12c.4,.4,.9,.7,1.5,.7s1.1-.2,1.5-.7L41.5,13.3c.7-.8,.7-2.1-.2-2.8Z`,
  ],
}

export const CheckCircle = {
  prefix: 'fal',
  iconName: 'check-circle',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M32.3,17.6c-.4-.4-.9-.6-1.4-.6s-1,.2-1.4,.6l-8.6,8.6-3.3-3.3c-.2-.2-.4-.4-.6-.5-.2-.1-.5-.2-.8-.2-.3,0-.6,0-.8,.1-.3,.1-.5,.3-.7,.4-.2,.2-.3,.4-.4,.7-.1,.3-.1,.5-.1,.8,0,.3,0,.5,.2,.8,.1,.2,.3,.5,.5,.6l4.7,4.7c.2,.2,.4,.3,.7,.4,.2,0,.5,.2,.8,.1,.5,0,1-.2,1.4-.6l10-10c.2-.2,.3-.4,.4-.7,.1-.2,.2-.5,.2-.8s0-.5-.2-.8c-.1-.2-.3-.5-.4-.7Zm10.2-1.2c-1-2.4-2.5-4.6-4.3-6.5-1.9-1.9-4.1-3.3-6.5-4.3-2.4-1-5-1.5-7.7-1.5-4,0-7.8,1.2-11.1,3.4-3.3,2.2-5.9,5.3-7.4,9-1.5,3.7-1.9,7.7-1.1,11.6,.8,3.9,2.7,7.4,5.5,10.2,2.8,2.8,6.4,4.7,10.2,5.5,3.9,.8,7.9,.4,11.6-1.1,3.7-1.5,6.8-4.1,9-7.4,2.2-3.3,3.4-7.2,3.4-11.1s-.5-5.2-1.5-7.7Zm-7.2,19c-3,3-7.1,4.7-11.3,4.7s-6.3-.9-8.9-2.7c-2.6-1.8-4.7-4.3-5.9-7.2-1.2-2.9-1.5-6.1-.9-9.2,.6-3.1,2.1-6,4.4-8.2s5.1-3.8,8.2-4.4c3.1-.6,6.3-.3,9.2,.9,2.9,1.2,5.4,3.3,7.2,5.9,1.8,2.6,2.7,5.7,2.7,8.9s-1.7,8.3-4.7,11.3Z`,
  ],
}

export const ChevronDoubleRight = {
  prefix: 'fal',
  iconName: 'chevron-double-right',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M22.6,10.6c.8-.8,2-.8,2.8,0l12,12c.8,.8,.8,2,0,2.8l-12,12c-.8,.8-2,.8-2.8,0-.8-.8-.8-2,0-2.8l10.6-10.6-10.6-10.6c-.8-.8-.8-2,0-2.8Zm-12,0c.8-.8,2-.8,2.8,0l12,12c.4,.4,.6,.9,.6,1.4s-.2,1-.6,1.4l-12,12c-.8,.8-2,.8-2.8,0-.8-.8-.8-2,0-2.8l10.6-10.6L10.6,13.4c-.8-.8-.8-2,0-2.8Z`,
  ],
}

export const ChevronLeft = {
  prefix: 'fal',
  iconName: 'chevron-left',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M31.4,10.6c.8,.8,.8,2,0,2.8l-10.6,10.6,10.6,10.6c.8,.8,.8,2,0,2.8-.8,.8-2,.8-2.8,0l-12-12c-.8-.8-.8-2,0-2.8l12-12c.8-.8,2-.8,2.8,0Z`,
  ],
}

export const ChevronRight = {
  prefix: 'fal',
  iconName: 'chevron-right',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M31.4,22.6l-12-12c-.8-.8-2-.8-2.8,0-.8,.8-.8,2,0,2.8l10.6,10.6-10.6,10.6c-.8,.8-.8,2,0,2.8s2,.8,2.8,0l12-12c.8-.8,.8-2,0-2.8Z`,
  ],
}

export const ChevronDown = {
  prefix: 'fal',
  iconName: 'chevron-down',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M37.4,16.6c-.8-.8-2-.8-2.8,0l-10.6,10.6-10.6-10.6c-.8-.8-2-.8-2.8,0-.8,.8-.8,2,0,2.8l12,12c.8,.8,2,.8,2.8,0l12-12c.8-.8,.8-2,0-2.8Z`,
  ],
}

export const Clipboard = {
  prefix: 'fal',
  iconName: 'clipboard',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M16,6c0-1.1,.9-2,2-2h12c1.1,0,2,.9,2,2h4c2.2,0,4,1.8,4,4v30c0,2.2-1.8,4-4,4H12c-2.2,0-4-1.8-4-4V10c0-2.2,1.8-4,4-4h4Zm0,4h-4v30h24V10h-4v2c0,1.1-.9,2-2,2h-12c-1.1,0-2-.9-2-2v-2Zm12-2h-8v2h8v-2Z`,
  ],
}

export const Clock = {
  prefix: 'fal',
  iconName: 'clock',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M26,23.2V14c0-1.1-.9-2-2-2s-2,.9-2,2v10c0,.5,.2,1,.6,1.4l6,6c.8,.8,2,.8,2.8,0s.8-2,0-2.8l-5.4-5.4Zm-2-19.2C13,4,4,13,4,24s9,20,20,20,20-9,20-20S35,4,24,4Zm0,36c-8.8,0-16-7.2-16-16S15.2,8,24,8s16,7.2,16,16-7.2,16-16,16Z`,
  ],
}

export const Clone = {
  prefix: 'fal',
  iconName: 'clone',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M4,8c0-2.2,1.8-4,4-4H28c2.2,0,4,1.8,4,4v8h8c2.2,0,4,1.8,4,4v20c0,2.2-1.8,4-4,4H20c-2.2,0-4-1.8-4-4v-8H8c-2.2,0-4-1.8-4-4V8Zm16,24v8h20V20h-8v8c0,2.2-1.8,4-4,4h-8Zm8-4V8H8V28H28Z`,
  ],
}

export const Cog = {
  prefix: 'fal',
  iconName: 'cog',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M43.7,19.8c-.3-.4-.6-.6-1-.8l-3.8-1.2,1.8-3.5c.2-.4,.3-.8,.2-1.2,0-.4-.3-.8-.6-1.1l-4.3-4.3c-.3-.3-.7-.5-1.1-.6-.4,0-.9,0-1.2,.2l-3.5,1.8-1.2-3.8c0-.4-.4-.8-.8-1-.4-.3-.8-.4-1.1-.4h-6c-.4,0-.9,0-1.1,.4-.4,.3-.6,.6-.8,1l-1.2,3.8-3.5-1.8c-.4-.2-.8-.3-1.2-.2-.4,0-.8,.3-1.1,.6l-4.3,4.3c-.3,.3-.5,.7-.6,1.1,0,.4,0,.9,.2,1.2l1.8,3.5-3.8,1.2c-.4,0-.8,.4-1,.8-.3,.4-.4,.8-.4,1.1v6c0,.4,0,.9,.4,1.1,.3,.4,.6,.6,1,.8l3.8,1.2-1.8,3.5c-.2,.4-.3,.8-.2,1.2,0,.4,.3,.8,.6,1.1l4.3,4.3c.3,.3,.7,.5,1.1,.6,.4,0,.9,0,1.2-.2l3.5-1.8,1.2,3.8c0,.4,.4,.8,.8,1,.4,.3,.8,.4,1.1,.4h6c.4,0,.9,0,1.1-.4,.4-.3,.6-.6,.8-1l1.2-3.8,3.5,1.8c.4,.2,.8,.2,1.1,.2s.8-.3,1.1-.6l4.3-4.3c.3-.3,.5-.7,.6-1.1,0-.4,0-.9-.2-1.2l-1.8-3.5,3.8-1.2c.4,0,.8-.4,1-.8,.3-.4,.4-.8,.4-1.1v-6c0-.4,0-.9-.4-1.1h0Zm-3.6,5.7l-2.4,.8c-.6,.2-1.1,.5-1.5,.9-.4,.4-.8,.9-1,1.4s-.3,1.1-.3,1.7,.2,1.1,.5,1.6l1.1,2.3-2.2,2.2-2.2-1.2c-.5-.3-1.1-.4-1.6-.4s-1.1,0-1.7,.3c-.6,.2-1.1,.6-1.4,1s-.7,1-.9,1.4l-.8,2.4h-3.2l-.8-2.4c-.2-.6-.5-1.1-.9-1.5-.4-.4-.9-.8-1.4-1s-1.1-.3-1.7-.3-1.1,.2-1.6,.5l-2.3,1.1-2.2-2.2,1.2-2.2c.3-.5,.4-1.1,.5-1.6,0-.6,0-1.1-.3-1.7-.2-.6-.6-1.1-1-1.4s-1-.7-1.5-.9l-2.4-.8v-3.2l2.4-.8c.6-.2,1.1-.5,1.5-.9,.4-.4,.8-.9,1-1.4s.3-1.1,.3-1.7-.2-1.1-.5-1.6l-1.1-2.2,2.2-2.2,2.2,1.1c.5,.3,1.1,.4,1.6,.5,.6,0,1.1,0,1.7-.3,.6-.2,1.1-.6,1.4-1s.7-1,.9-1.5l.8-2.4h3.2l.8,2.4c.2,.6,.5,1.1,.9,1.5,.4,.4,.9,.8,1.4,1s1.1,.3,1.7,.3,1.1-.2,1.6-.5l2.3-1.1,2.2,2.2-1.2,2.2c-.3,.5-.4,1.1-.4,1.6s0,1.1,.3,1.7c.2,.6,.6,1.1,1,1.4s1,.7,1.4,.9l2.4,.8v3.2Zm-16.1-9.6c-1.6,0-3.2,.5-4.5,1.3-1.3,.9-2.4,2.1-3,3.6-.6,1.4-.8,3.1-.5,4.7,.3,1.5,1.1,3,2.2,4.1s2.6,1.9,4.1,2.2,3.2,.2,4.7-.5c1.4-.6,2.7-1.6,3.6-3,.9-1.3,1.3-2.9,1.3-4.5s-.9-4.2-2.4-5.6c-1.5-1.5-3.5-2.4-5.6-2.4Zm2.9,10.9c-.8,.8-1.8,1.1-2.9,1.1s-1.5-.2-2.2-.7c-.7-.5-1.1-1.1-1.4-1.8s-.4-1.5-.2-2.3c.2-.8,.6-1.5,1.1-2.1,.6-.6,1.2-1,2.1-1.1,.8-.2,1.6,0,2.3,.2,.8,.3,1.3,.9,1.8,1.4,.5,.7,.7,1.4,.7,2.2s-.4,2.1-1.1,2.9h0Z`,
  ],
}

export const CommentDots = {
  prefix: 'fal',
  iconName: 'comment-dots',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24,22c-.4,0-.8,.1-1.1,.3-.3,.2-.6,.5-.7,.9-.2,.4-.2,.8-.1,1.2,0,.4,.3,.7,.5,1,.3,.3,.6,.5,1,.5s.8,0,1.2-.1c.4-.2,.7-.4,.9-.7,.2-.3,.3-.7,.3-1.1s-.2-1-.6-1.4-.9-.6-1.4-.6Zm-8,0c-.4,0-.8,.1-1.1,.3-.3,.2-.6,.5-.7,.9-.2,.4-.2,.8-.1,1.2,0,.4,.3,.7,.5,1,.3,.3,.6,.5,1,.5s.8,0,1.2-.1c.4-.2,.7-.4,.9-.7,.2-.3,.3-.7,.3-1.1s-.2-1-.6-1.4-.9-.6-1.4-.6Zm22.1-12.1c-3.8-3.8-8.8-5.9-14.1-5.9s-5.2,.5-7.7,1.5c-2.4,1-4.6,2.5-6.5,4.3-3.8,3.8-5.9,8.8-5.9,14.1,0,4.6,1.6,9.1,4.5,12.7l-4,4c-.3,.3-.5,.6-.5,1,0,.4,0,.8,.1,1.2,.2,.4,.4,.7,.8,.9s.7,.3,1.1,.3H24c5.3,0,10.4-2.1,14.1-5.9,3.8-3.8,5.9-8.8,5.9-14.1s-2.1-10.4-5.9-14.1Zm-4,26.5c-2.9,2.4-6.4,3.6-10.1,3.6H10.8l1.9-1.9c.2-.2,.3-.4,.4-.6,.1-.2,.2-.5,.2-.8,0-.5-.2-1-.6-1.4-2.6-2.6-4.2-6.1-4.6-9.7-.4-3.7,.6-7.4,2.6-10.5,2.1-3.1,5.1-5.4,8.7-6.4s7.3-.9,10.8,.5c3.4,1.4,6.2,4,8,7.2,1.7,3.3,2.3,7,1.6,10.7-.7,3.6-2.7,6.9-5.5,9.2Zm-2.1-14.4c-.4,0-.8,.1-1.1,.3-.3,.2-.6,.5-.7,.9-.2,.4-.2,.8-.1,1.2s.3,.7,.5,1c.3,.3,.6,.5,1,.5,.4,0,.8,0,1.2-.1,.4-.2,.7-.4,.9-.7,.2-.3,.3-.7,.3-1.1s-.2-1-.6-1.4c-.4-.4-.9-.6-1.4-.6Z`,
  ],
}

export const CompressAlt = {
  prefix: 'fal',
  iconName: 'compress-alt',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M21.8,25.1c-.3-.1-.5-.1-.8-.1h-7.9c-.6,0-1,.2-1.4,.6s-.6,.9-.6,1.4,.2,1,.6,1.4c.4,.4,.9,.6,1.4,.6h3.1l-11.5,11.5c-.2,.2-.4,.4-.5,.7s-.2,.5-.2,.8,0,.6,.2,.8c0,.3,.3,.5,.5,.7s.4,.4,.7,.5h.7c.3,0,.6,0,.8-.2,.3,0,.5-.3,.7-.5l11.6-11.6v3.1c0,.6,.2,1,.6,1.4s.9,.6,1.4,.6,1-.2,1.4-.6c.4-.4,.6-.9,.6-1.4v-8c0-.3,0-.5-.2-.8-.3-.3-.7-.7-1.2-.9h0ZM43.8,5.3c0-.3-.3-.5-.5-.7s-.4-.4-.7-.5c-.2-.1-.4-.1-.7-.1s-.6,0-.8,.2c-.3,0-.5,.3-.7,.5l-11.5,11.5v-3.1c0-.6-.2-1-.6-1.4s-.9-.6-1.4-.6-1,.2-1.4,.6-.6,.9-.6,1.4v8c0,.3,0,.5,.2,.8,.2,.5,.6,.9,1,1,.3,.1,.5,.2,.8,.2h8c.6,0,1-.2,1.4-.6s.6-.9,.6-1.4-.2-1-.6-1.4-.9-.6-1.4-.6h-3.1l11.5-11.6c.2-.2,.4-.4,.5-.7,.1-.3,.2-.5,.2-.8s0-.5-.2-.7Z`,
  ],
}

export const Copy = {
  prefix: 'fal',
  iconName: 'copy',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M41.9,17.3v-.2c0-.2-.2-.4-.4-.6L29.5,4.6c-.2-.2-.4-.3-.6-.4,0,0-.1,0-.2,0-.2-.1-.4-.2-.7-.2h-8.1c-1.6,0-3.1,.6-4.2,1.8s-1.8,2.7-1.8,4.2v2h-2c-1.6,0-3.1,.6-4.2,1.8s-1.8,2.7-1.8,4.2v20c0,1.6,.6,3.1,1.8,4.2s2.7,1.8,4.2,1.8H28c1.6,0,3.1-.6,4.2-1.8,1.1-1.1,1.8-2.7,1.8-4.2v-2h2c1.6,0,3.1-.6,4.2-1.8,1.1-1.1,1.8-2.7,1.8-4.2v-12h0c0-.3,0-.5-.1-.7Zm-11.9-6.5l5.2,5.2h-3.2c-.5,0-1-.2-1.4-.6s-.6-.9-.6-1.4v-3.2Zm0,27.2c0,.5-.2,1-.6,1.4s-.9,.6-1.4,.6H12c-.5,0-1-.2-1.4-.6-.4-.4-.6-.9-.6-1.4V18c0-.5,.2-1,.6-1.4,.4-.4,.9-.6,1.4-.6h2v14c0,1.6,.6,3.1,1.8,4.2s2.7,1.8,4.2,1.8h10v2Zm8-8c0,.5-.2,1-.6,1.4s-.9,.6-1.4,.6H20c-.5,0-1-.2-1.4-.6-.4-.4-.6-.9-.6-1.4V10c0-.5,.2-1,.6-1.4,.4-.4,.9-.6,1.4-.6h6v6c0,1.6,.6,3.1,1.8,4.2s2.7,1.8,4.2,1.8h6v10Z`,
  ],
}
export const CredentialProfileIcon = {
  prefix: 'fal',
  iconName: 'credential-profile',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M24.2,26.1c-.9,0-1.7,.3-2.4,.8-.6,.6-1,1.4-1.1,2.3,0,1,.5,2,1.4,2.6v3h0v.3c0,.9,.8,1.7,1.8,1.8,0,0,0,0,.1,0,.5,0,.9-.2,1.2-.5,.4-.3,.6-.8,.6-1.3v-3.3c.8-.6,1.4-1.5,1.4-2.6,0-1.7-1.4-3-3.1-3.1Zm11.7-7.7h-2.8v-6.2c-.3-4.5-3.9-8.2-8.5-8.5-2.4-.2-4.8,.6-6.6,2.2-1.8,1.6-2.9,3.8-3.1,6.3v6.2h-2.8c-2.7,0-5,2.2-5,4.9v15.9c0,2.7,2.3,4.9,5.1,4.9h23.7c2.7,0,5-2.2,5-4.9v-15.9c0-2.7-2.3-4.9-5-4.9Zm-17.2-6.2c.2-2.6,2.3-4.7,4.9-4.9,2.9-.2,5.5,2,5.7,4.9v6.2h-10.6v-6.2Zm18.5,27c0,.4-.2,.7-.4,.9-.3,.2-.6,.4-1,.4H12.2c-.7,0-1.3-.6-1.4-1.3v-15.9c0-.4,.2-.7,.4-.9,.3-.2,.6-.4,1-.4h23.5c.3,0,.7,.1,1,.4,.3,.2,.4,.6,.4,.9v15.9Z',
  ],
}

export const ChartIcon = {
  prefix: 'fal',
  iconName: 'chart',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M42,40h-2V10c0-.5-.2-1-.6-1.4-.4-.4-.9-.6-1.4-.6s-1,.2-1.4,.6c-.4,.4-.6,.9-.6,1.4v30h-4v-14c0-.5-.2-1-.6-1.4s-.9-.6-1.4-.6-1,.2-1.4,.6-.6,.9-.6,1.4v14h-4V18c0-.5-.2-1-.6-1.4-.4-.4-.9-.6-1.4-.6s-1,.2-1.4,.6c-.4,.4-.6,.9-.6,1.4v22h-4v-6c0-.5-.2-1-.6-1.4-.4-.4-.9-.6-1.4-.6s-1,.2-1.4,.6c-.4,.4-.6,.9-.6,1.4v6h-4V6c0-.5-.2-1-.6-1.4-.4-.4-.9-.6-1.4-.6s-1,.2-1.4,.6c-.4,.4-.6,.9-.6,1.4V42c0,.5,.2,1,.6,1.4,.4,.4,.9,.6,1.4,.6H42c.5,0,1-.2,1.4-.6s.6-.9,.6-1.4-.2-1-.6-1.4-.9-.6-1.4-.6Z`,
  ],
}

export const SdnTopologyIcon = {
  prefix: 'fal',
  iconName: 'sdn-topology',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M40.5,29.9c-2.2,0-4,1.3-4.8,3.2h-2.2v-5.3h3.4c5,0,9.1-4.1,9.1-9.1s-2.8-7.7-6.7-8.8c-.9-4.6-5-8-9.8-8s-3.7,.5-5.3,1.5c-1.7-1.5-3.9-2.4-6.2-2.4-5,0-9,4-9.1,8.9-3.9,1-6.9,4.6-6.9,8.8s4.1,9.1,9.1,9.1h3.6v5.3h-2.4c-.8-1.9-2.7-3.2-4.8-3.2s-5.2,2.4-5.2,5.2,2.4,5.2,5.2,5.2,4.1-1.4,4.9-3.3h4.3c1.1,0,2-.9,2-2v-7.3h3.4v9c-1.9,.8-3.3,2.7-3.3,4.9s2.4,5.2,5.2,5.2,5.2-2.4,5.2-5.2-1.3-4-3.2-4.8v-9h3.3v7.3c0,1.1,.9,2,2,2h4.2c.8,1.9,2.7,3.3,4.9,3.3s5.2-2.4,5.2-5.2-2.4-5.2-5.2-5.2ZM7.5,36.9c-1,0-1.7-.8-1.7-1.7s.8-1.7,1.7-1.7,1.7,.8,1.7,1.7-.8,1.7-1.7,1.7Zm16.6,6.5c-1,0-1.7-.8-1.7-1.8s.8-1.7,1.7-1.7,1.7,.8,1.7,1.7-.8,1.8-1.7,1.8ZM11.1,23.9c-2.8,0-5.1-2.3-5.1-5.1s2.3-5.1,5-5.1c.6,0,1.2-.3,1.5-.8,.4-.5,.5-1.1,.4-1.7,0-.3-.1-.7-.1-1,0-2.8,2.3-5.1,5.1-5.1s3.3,.8,4.3,2.3c.3,.5,.9,.8,1.4,.9,.6,0,1.2-.1,1.6-.5,1.1-1.1,2.6-1.7,4.2-1.7,3.2,0,5.9,2.6,6,5.8,0,1,.8,1.8,1.8,1.9,2.7,.2,4.7,2.4,4.7,5.1s-2.3,5.1-5.1,5.1H11.1Zm29.3,13c-1,0-1.7-.8-1.7-1.7s.8-1.7,1.7-1.7,1.7,.8,1.7,1.7-.8,1.7-1.7,1.7Z`,
  ],
}
export const CloudTopologyIcon = {
  prefix: 'fal',
  iconName: 'cloud-topology',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M8.6,19.6c0-7.29,5.91-13.2,13.2-13.2,5.78,0,10.69,3.71,12.48,8.88,6.6.73,11.72,6.33,11.72,13.12,0,7.29-5.91,13.2-13.2,13.2H13c-6.08,0-11-4.92-11-11,0-4.52,2.73-8.41,6.63-10.1-.02-.3-.03-.6-.03-.9ZM21.8,10.8c-4.86,0-8.8,3.94-8.8,8.8,0,.67.07,1.31.21,1.93.27,1.19-.48,2.36-1.67,2.63-2.95.66-5.15,3.3-5.15,6.44,0,3.65,2.95,6.6,6.6,6.6h19.8c4.86,0,8.8-3.94,8.8-8.8s-3.94-8.8-8.8-8.8c-.06,0-.12,0-.18,0-1.06.02-1.99-.72-2.2-1.76-.81-4.02-4.37-7.04-8.62-7.04Z`,
  ],
}

export const CPUIcon = {
  prefix: 'fal',
  iconName: 'cpu',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M23.6,31.7c-1.1,0-2,.9-2,2s.9,2,2,2,2-.9,2-2-.9-2-2-2Zm4-12h-8c-1.1,0-2,.9-2,2s.9,2,2,2h8c1.1,0,2-.9,2-2s-.9-2-2-2ZM35.6,3.7H11.6c-1.1,0-2,.9-2,2V41.7c0,1.1,.9,2,2,2h24c1.1,0,2-.9,2-2V5.7c0-1.1-.9-2-2-2Zm-2,36H13.6V7.7h20V39.7ZM17.6,15.7h12c1.1,0,2-.9,2-2s-.9-2-2-2h-12c-1.1,0-2,.9-2,2s.9,2,2,2Z`,
  ],
}

export const DotCircle = {
  prefix: 'fal',
  iconName: 'dot-circle',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24,7.9c8.9,0,16.1,7.2,16.1,16.1s-7.2,16.1-16.1,16.1S7.9,32.9,7.9,24,15.1,7.9,24,7.9m0-3.9C13,4,4,13,4,24s9,20,20,20,20-9,20-20S35,4,24,4Zm0,13.5c-3.6,0-6.5,2.9-6.5,6.5s2.9,6.5,6.5,6.5,6.5-2.9,6.5-6.5-2.9-6.5-6.5-6.5Z`,
  ],
}
export const DashboardIcon = {
  prefix: 'fal',
  iconName: 'dashboard',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M19.5,26.7H3.8c-1,0-1.8,.8-1.8,1.8v15.7c0,1,.8,1.8,1.8,1.8h15.7c1,0,1.8-.8,1.8-1.8v-15.7c0-1-.8-1.8-1.8-1.8Zm-1.8,15.7H5.5v-12.2h12.2v12.2Zm26.5-15.7h-15.7c-1,0-1.8,.8-1.8,1.8v15.7c0,1,.8,1.8,1.8,1.8h15.7c1,0,1.8-.8,1.8-1.8v-15.7c0-1-.8-1.8-1.8-1.8Zm-1.8,15.7h-12.2v-12.2h12.2v12.2ZM19.5,2H3.8c-1,0-1.8,.8-1.8,1.8v15.7c0,1,.8,1.8,1.8,1.8h15.7c1,0,1.8-.8,1.8-1.8V3.8c0-1-.8-1.8-1.8-1.8Zm-1.8,15.7H5.5V5.5h12.2v12.2ZM44.2,2h-15.7c-1,0-1.8,.8-1.8,1.8v15.7c0,1,.8,1.8,1.8,1.8h15.7c1,0,1.8-.8,1.8-1.8V3.8c0-1-.8-1.8-1.8-1.8Zm-1.8,15.7h-12.2V5.5h12.2v12.2Z`,
  ],
}

export const DownloadIcon = {
  prefix: 'fal',
  iconName: 'download',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M22.6,31.4c0,0,.2,.2,.3,.2,0,0,0,0,0,0,0,0,.2,.1,.3,.1,0,0,0,0,.1,0,0,0,.2,0,.3,0,.1,0,.3,0,.4,0s.3,0,.4,0c0,0,.2,0,.3,0,0,0,0,0,.1,0,0,0,.2,0,.3-.1,0,0,0,0,0,0,.1,0,.2-.2,.3-.2l10-10c.8-.8,.8-2,0-2.8-.8-.8-2-.8-2.8,0l-6.6,6.6V6c0-1.1-.9-2-2-2s-2,.9-2,2V25.2l-6.6-6.6c-.8-.8-2-.8-2.8,0-.8,.8-.8,2,0,2.8l10,10Zm19.4-3.4c-1.1,0-2,.9-2,2v8c0,.5-.2,1-.6,1.4-.4,.4-.9,.6-1.4,.6H10c-.5,0-1-.2-1.4-.6-.4-.4-.6-.9-.6-1.4v-8c0-1.1-.9-2-2-2s-2,.9-2,2v8c0,1.6,.6,3.1,1.8,4.2,1.1,1.1,2.6,1.8,4.2,1.8h28c1.6,0,3.1-.6,4.2-1.8,1.1-1.1,1.8-2.6,1.8-4.2v-8c0-1.1-.9-2-2-2Z',
  ],
}

export const DragArrowIcon = {
  prefix: 'fal',
  iconName: 'drag-arrows',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M45.3,23.2c0-.2-.2-.5-.4-.7l-4.3-4.3c-.8-.8-2.1-.8-2.9,0-.4,.4-.6,.9-.6,1.4s.2,1.1,.6,1.4l.9,.9h-12.6V9.4l.9,.9c.8,.8,2.1,.8,2.9,0,.4-.4,.6-.9,.6-1.4s-.2-1.1-.6-1.4l-4.3-4.3c-.2-.2-.4-.3-.7-.4-.5-.2-1.1-.2-1.5,0-.2,.1-.5,.2-.7,.4l-4.3,4.3c-.4,.4-.6,.9-.6,1.4s.2,1.1,.6,1.4c.8,.8,2.1,.8,2.9,0l.9-.9v12.6H9.4l.9-.9c.4-.4,.6-.9,.6-1.4s-.2-1.1-.6-1.4c-.8-.8-2.1-.8-2.9,0l-4.3,4.3c-.2,.2-.3,.4-.4,.7-.2,.5-.2,1.1,0,1.5,0,.2,.2,.5,.4,.7l4.3,4.3c.8,.8,2.1,.8,2.9,0,.4-.4,.6-.9,.6-1.4s-.2-1.1-.6-1.4l-.9-.9h12.6v12.6l-.9-.9c-.8-.8-2.1-.8-2.9,0-.4,.4-.6,.9-.6,1.4s.2,1.1,.6,1.4l4.3,4.3c.2,.2,.4,.3,.7,.4,.3,.1,.5,.2,.8,.2s.5,0,.8-.2c.2-.1,.5-.2,.7-.4l4.3-4.3c.4-.4,.6-.9,.6-1.4s-.2-1.1-.6-1.4c-.8-.8-2.1-.8-2.9,0l-.9,.9v-12.6h12.6l-.9,.9c-.4,.4-.6,.9-.6,1.4s.2,1.1,.6,1.4h0c.8,.8,2.1,.8,2.9,0l4.3-4.3c.2-.2,.3-.4,.4-.7,.2-.5,.2-1.1,0-1.5Z',
  ],
}

export const DiskIcon = {
  prefix: 'fal',
  iconName: 'disk',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24,34h8c1.1,0,2-.9,2-2s-.9-2-2-2h-8c-1.1,0-2,.9-2,2s.9,2,2,2Zm-8,0c1.1,0,2-.9,2-2s-.9-2-2-2-2,.9-2,2,.9,2,2,2Zm25.6-11.3l-6.7-13.4c-1-2-3.1-3.3-5.4-3.3h-11c-2.3,0-4.3,1.3-5.4,3.3l-6.7,13.4c-.3,.5-.4,1.2-.4,1.8v11.5c0,3.3,2.7,6,6,6h24c3.3,0,6-2.7,6-6v-11.5c0-.6-.2-1.3-.4-1.8ZM16.7,11.1c.3-.7,1-1.1,1.8-1.1h11.1c.8,0,1.5,.4,1.8,1.1l5.4,10.9H11.2l5.5-10.9Zm21.3,24.9c0,1.1-.9,2-2,2H12c-1.1,0-2-.9-2-2v-10h28v10Z`,
  ],
}
export const EllipsisV = {
  prefix: 'fal',
  iconName: 'ellipsis-v',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24,14c2.2,0,4-1.8,4-4s-1.8-4-4-4-4,1.8-4,4,1.8,4,4,4Zm0,20c-2.2,0-4,1.8-4,4s1.8,4,4,4,4-1.8,4-4-1.8-4-4-4Zm0-14c-2.2,0-4,1.8-4,4s1.8,4,4,4,4-1.8,4-4-1.8-4-4-4Z`,
  ],
}
export const ExportIcon = {
  prefix: 'fal',
  iconName: 'monitor-export',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M19.8,12.9l2.2-2.1V27.9c0,1.1,.9,2,2,2s2-.9,2-2V10.8l2.1,2.1c.4,.4,.9,.7,1.5,.7s.8-.1,1.1-.3c.9-.6,1.2-1.9,.5-2.8,0-.1-.2-.2-.3-.3l-5.6-5.5c-.8-.8-2-.8-2.8,0l-5.6,5.5c-.8,.8-.8,2.1,0,2.8,.8,.8,2.1,.8,2.8,0Zm16.2,4h-6.2v4h6.2c.8,0,1.4,.6,1.4,1.4v16.3c0,.8-.6,1.4-1.4,1.4H12c-.8,0-1.4-.6-1.4-1.4V22.3c0-.8,.6-1.4,1.4-1.4h6.4v-4h-6.4c-3,0-5.4,2.4-5.4,5.4v16.3c0,3,2.4,5.4,5.4,5.4h24c3,0,5.4-2.4,5.4-5.4V22.3c0-3-2.4-5.4-5.4-5.4Z',
  ],
}
export const Eraser = {
  prefix: 'fal',
  iconName: 'eraser',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M42.9,25.4c1.5-1.5,1.5-3.8,0-5.3L30.4,7.6c-1.5-1.5-3.8-1.5-5.3,0L5.1,27.6c-1.5,1.5-1.5,3.8,0,5.3l7.5,7.5c.7,.7,1.7,1.1,2.7,1.1h27.8c.5,0,.9-.4,.9-.9v-1.9c0-.5-.4-.9-.9-.9h-12.5l12.3-12.3Zm-15.2-15.2l12.5,12.5-8,8-12.5-12.5,8-8Zm-12.5,27.5l-7.5-7.5,9.3-9.3,12.5,12.5-4.3,4.3H15.2Z`,
  ],
}

export const ExclamationCircle = {
  prefix: 'fal',
  iconName: 'exclamation-circle',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M25.7,30.9l-.2-.3c-.3-.3-.6-.5-1-.5-.4,0-.8,0-1.2,.1-.2,.1-.5,.2-.7,.4-.2,.2-.3,.4-.4,.7,0,.2-.2,.5-.1,.8,0,.3,0,.5,.2,.8,0,.2,.2,.5,.4,.7,.2,.2,.4,.3,.7,.4,.2,.1,.5,.2,.8,.2s.5,0,.8-.2c.2,0,.5-.2,.7-.4,.2-.2,.3-.4,.4-.7,.1-.2,.2-.5,.2-.8,0-.1,0-.3,0-.4,0-.1,0-.2-.2-.4,0-.1-.1-.2-.2-.4Zm16.8-14.5c-1-2.4-2.5-4.6-4.3-6.5s-4.1-3.3-6.5-4.3c-2.4-1-5-1.5-7.7-1.5-4,0-7.8,1.2-11.1,3.4-3.3,2.2-5.9,5.3-7.4,9-1.5,3.7-1.9,7.7-1.1,11.6,.8,3.9,2.7,7.4,5.5,10.2,2.8,2.8,6.4,4.7,10.2,5.5,3.9,.8,7.9,.4,11.6-1.1,3.7-1.5,6.8-4.1,9-7.4,2.2-3.3,3.4-7.2,3.4-11.1s-.5-5.2-1.5-7.7Zm-7.2,19c-3,3-7.1,4.7-11.3,4.7s-6.3-.9-8.9-2.7c-2.6-1.8-4.7-4.3-5.9-7.2-1.2-2.9-1.5-6.1-.9-9.2,.6-3.1,2.1-6,4.4-8.2s5.1-3.8,8.2-4.4c3.1-.6,6.3-.3,9.2,.9,2.9,1.2,5.4,3.3,7.2,5.9,1.8,2.6,2.7,5.7,2.7,8.9s-1.7,8.3-4.7,11.3ZM24,14c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4v8c0,.5,.2,1,.6,1.4s.9,.6,1.4,.6,1-.2,1.4-.6,.6-.9,.6-1.4V16c0-.5-.2-1-.6-1.4-.4-.4-.9-.6-1.4-.6Z`,
  ],
}

export const ExclamationTriangle = {
  prefix: 'fal',
  iconName: 'exclamation-triangle',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24,32.1c-.4,0-.7,.1-1.2,.3-.3,.2-.6,.5-.7,.9-.1,.3-.2,.7-.1,1.2,0,.4,.3,.7,.5,1,.3,.3,.6,.4,1,.5,.4,0,.7,0,1.2-.1,.3-.1,.6-.4,.9-.7,.2-.3,.3-.7,.3-1.2s-.2-1-.6-1.4c-.4-.4-.8-.6-1.4-.6Zm21.3,2.9L29.1,7.1c-.5-.9-1.3-1.7-2.2-2.2s-2-.8-3-.8-2.1,.3-3,.8-1.7,1.4-2.2,2.2L2.8,34.9c-.5,.9-.8,1.9-.8,2.9s.2,2.1,.7,3c.5,.9,1.3,1.7,2.2,2.2s1.9,.8,2.9,.8H39.9c1,0,2.1-.2,3-.7s1.7-1.3,2.2-2.2c.5-.9,.8-2,.8-3s-.3-2.1-.8-3h.1Zm-3.5,4.1h0c-.2,.3-.4,.5-.7,.7s-.6,.3-1,.3H8c-.3,0-.7,0-1-.3-.3-.2-.5-.4-.7-.7s-.3-.6-.3-.9,0-.7,.3-.9L22.1,9.1c.2-.3,.4-.6,.7-.8,.3-.2,.6-.3,1-.3s.7,0,1,.3c.3,.2,.5,.4,.7,.8l16,27.9c.2,.3,.3,.6,.3,1s0,.7-.2,1h0ZM24,16.1c-.5,0-1,.2-1.4,.6-.4,.4-.6,.8-.6,1.4v8c0,.5,.2,1,.6,1.4,.4,.4,.8,.6,1.4,.6s1-.2,1.4-.6c.4-.4,.6-.8,.6-1.4v-8c0-.5-.2-1-.6-1.4-.4-.4-.8-.6-1.4-.6Z`,
  ],
}

export const EnterIcon = {
  prefix: 'fal',
  iconName: 'enter',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M38,13c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4v8c0,.5-.2,1-.6,1.4s-.9,.6-1.4,.6H14.8l2.6-2.6c.4-.4,.6-.9,.6-1.4s-.2-1-.6-1.4-.9-.6-1.4-.6-1,.2-1.4,.6l-6,6c-.2,.2-.3,.4-.4,.7-.2,.5-.2,1,0,1.5,0,.2,.2,.5,.4,.7l6,6c.2,.2,.4,.3,.7,.4s.5,.2,.8,.2,.5,0,.8-.2c.2-.1,.5-.3,.7-.4,.2-.2,.3-.4,.4-.7,.1-.2,.2-.5,.2-.8s0-.5-.2-.8c-.1-.2-.3-.5-.4-.7l-2.6-2.6h19.2c1.6,0,3.1-.6,4.2-1.8,1.1-1.1,1.8-2.7,1.8-4.2V15c0-.5-.2-1-.6-1.4-.4-.4-.9-.6-1.4-.6Z',
  ],
}

export const ExportCSVIcon = {
  prefix: 'fal',
  iconName: 'export-csv',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M37.9,11.7l-6.6-6.6c-.7-.7-1.7-1.1-2.6-1.1H12.7c-2.1,0-3.7,1.7-3.7,3.8V40.3c0,2.1,1.7,3.7,3.7,3.7h22.5c2.1,0,3.8-1.7,3.8-3.7V14.3c0-1-.4-2-1.1-2.7Zm-8.9-3.6l5.9,5.9h-5.9v-5.9h0Zm6.3,32.2H12.7V7.8h12.5V15.9c0,1,.8,1.9,1.9,1.9h8.1v22.5Zm-8.8-15.6v1.6c0,2.8,1,5.4,2.8,7.4,.2,.3,.6,.4,.9,.4s.7-.1,.9-.4c1.8-2,2.8-4.6,2.8-7.4v-1.6c0-.3-.3-.6-.6-.6h-1.2c-.3,0-.6,.3-.6,.6v1.6c0,1.6-.4,3.1-1.2,4.4-.8-1.3-1.2-2.9-1.2-4.4v-1.6c0-.3-.3-.6-.6-.6h-1.2c-.3,0-.6,.3-.6,.6Zm-8.1-.6h-.6c-2.1,0-3.8,1.7-3.8,3.8v2.5c0,2.1,1.7,3.8,3.8,3.8h.6c.3,0,.6-.3,.6-.6v-1.2c0-.3-.3-.6-.6-.6h-.6c-.7,0-1.2-.6-1.2-1.2v-2.5c0-.7,.6-1.2,1.2-1.2h.6c.3,0,.6-.3,.6-.6v-1.2c0-.3-.3-.6-.6-.6Zm4.6,3.3c-.1,0-.2-.2-.2-.3,0-.2,.3-.5,.8-.5h1c.3,0,.6-.3,.6-.6v-1.2c0-.3-.3-.6-.6-.6h-1c-1.8,0-3.3,1.4-3.3,3s.4,1.6,1,2.2l1.7,1.5c.1,0,.2,.2,.2,.3,0,.2-.3,.5-.8,.5h-1c-.3,0-.6,.3-.6,.6v1.2c0,.3,.3,.6,.6,.6h1c1.8,0,3.3-1.4,3.3-3s-.4-1.6-1-2.2l-1.7-1.5h0Z`,
  ],
}

export const ExportPDFIcon = {
  prefix: 'fal',
  iconName: 'export-pdf',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M37.9,11.7l-6.6-6.6c-.7-.7-1.7-1.1-2.6-1.1H12.8c-2.1,0-3.7,1.7-3.7,3.8V40.3c0,2.1,1.7,3.7,3.7,3.7h22.5c2.1,0,3.7-1.7,3.7-3.7V14.3c0-1-.4-2-1.1-2.7Zm-3,2.4h-5.9v-5.9l5.9,5.9ZM12.8,40.3V7.8h12.5V15.9c0,1,.8,1.9,1.9,1.9h8.1v22.5H12.8Zm19.5-11.2c-1-.9-3.7-.7-5-.5-1.3-.8-2.2-2-2.9-3.6,.3-1.3,.8-3.2,.4-4.4-.3-2-3-1.8-3.3-.5-.3,1.3,0,3,.5,5.2-.8,1.9-1.9,4.4-2.8,5.8-1.6,.8-3.7,2-4,3.6-.3,1.2,2,4.3,5.9-2.4,1.7-.6,3.7-1.3,5.3-1.6,1.5,.8,3.2,1.3,4.4,1.3,2,0,2.2-2.2,1.4-3Zm-15.5,6.1c.4-1.1,1.9-2.3,2.4-2.7-1.5,2.4-2.4,2.8-2.4,2.7Zm6.4-14.9c.6,0,.5,2.5,.1,3.2-.3-1.1-.3-3.2-.1-3.2Zm-1.9,10.7c.8-1.3,1.4-2.9,1.9-4.3,.6,1.2,1.5,2.1,2.4,2.8-1.6,.3-3,1-4.3,1.5Zm10.3-.4s-.4,.5-2.9-.6c2.7-.2,3.2,.4,2.9,.6Z`,
  ],
}

export const ExternalLink = {
  prefix: 'fal',
  iconName: 'external-link',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M41.4,6.6c-.4-.4-.9-.6-1.4-.6h-12c-1.1,0-2,.9-2,2s.9,2,2,2h7.2L16.6,28.6c-.8,.8-.8,2,0,2.8,.8,.8,2,.8,2.8,0L38,12.8v7.2c0,1.1,.9,2,2,2s2-.9,2-2V8c0-.5-.2-1-.6-1.4Zm-5.4,19.4c-1.1,0-2,.9-2,2v10H10V14h10c1.1,0,2-.9,2-2s-.9-2-2-2H10c-2.2,0-4,1.8-4,4v24c0,2.2,1.8,4,4,4h24c2.2,0,4-1.8,4-4v-10c0-1.1-.9-2-2-2Z`,
  ],
}

export const Eye = {
  prefix: 'fal',
  iconName: 'eye',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M43.8,23.2c-4-9.3-11.6-15.2-19.8-15.2S8.2,13.8,4.2,23.2c0,.3-.2,.6-.2,.8s0,.6,.2,.8c4,9.3,11.6,15.2,19.8,15.2s15.8-5.8,19.8-15.2c0-.3,.2-.6,.2-.8s0-.6-.2-.8Zm-19.8,12.8c-6.4,0-12.4-4.6-15.8-12,3.4-7.4,9.4-12,15.8-12s12.4,4.6,15.8,12c-3.4,7.4-9.4,12-15.8,12Zm0-20c-1.6,0-3.1,.5-4.5,1.3-1.3,.9-2.4,2.1-3,3.6-.6,1.4-.8,3-.5,4.7,.3,1.5,1,3,2.2,4.1s2.6,1.9,4.1,2.2,3.1,.2,4.7-.5c1.4-.6,2.7-1.6,3.6-3,.9-1.3,1.3-2.9,1.3-4.5s-.9-4.2-2.4-5.6c-1.5-1.5-3.5-2.4-5.6-2.4Zm2.9,10.9c-.8,.8-1.8,1.1-2.9,1.1s-1.5-.2-2.2-.7c-.7-.5-1.1-1-1.4-1.8s-.4-1.5-.2-2.3c.2-.8,.6-1.5,1-2s1.2-1,2-1c.8-.2,1.6,0,2.3,.2,.8,.3,1.3,.9,1.8,1.4,.5,.7,.7,1.4,.7,2.2s-.4,2.1-1.1,2.9Z`,
  ],
}

export const EyeSlash = {
  prefix: 'fal',
  iconName: 'eye-slash',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M19.8,11.4c0,.2,.3,.4,.6,.6,.2,.2,.5,.3,.8,.3h.8c.7-.2,1.4-.3,2.1-.3,6.4,0,12.4,4.6,15.8,12-.6,1.1-1.1,2.2-1.8,3.2-.2,.3-.3,.7-.3,1.1s.2,.9,.4,1.2c.3,.4,.7,.6,1,.7,.4,0,.9,0,1.2,0,.4-.2,.8-.5,1-.9,1-1.4,1.7-3,2.4-4.6,0-.3,.2-.5,.2-.8s0-.6-.2-.8c-4-9.3-11.6-15.2-19.8-15.2s-1.9,0-2.8,.3c-.3,0-.5,0-.8,.3-.2,0-.4,.3-.6,.6-.2,.2-.3,.5-.3,.8v.8c0,.2,0,.5,.3,.8h0Zm23.9,29.7c0-.3-.3-.5-.5-.7L7.5,4.6c-.2-.2-.4-.4-.7-.5-.3,0-.5-.2-.8-.2s-.6,0-.8,.2c-.3,0-.5,.3-.7,.5-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4l6.2,6.2c-2.9,2.8-5,6-6.6,9.6,0,.3-.2,.6-.2,.8s0,.6,.2,.8c4,9.3,11.6,15.2,19.8,15.2s7.1-1,10.1-3l6.5,6.5c.2,.2,.4,.4,.7,.5s.5,.2,.8,.2,.6,0,.8-.2c.3,0,.5-.3,.7-.5s.4-.4,.5-.7,.2-.5,.2-.8,0-.6-.2-.8h0ZM20.1,23l4.8,4.8c-.4,0-.7,.2-1,0-1,0-2.1-.4-2.9-1.1-.8-.8-1.1-1.8-1.1-2.9s0-.7,0-1h0Zm3.9,13.1c-6.4,0-12.4-4.6-15.8-12,1.3-2.9,3.1-5.4,5.4-7.6l3.5,3.6c-.9,1.5-1.1,3.2-.9,4.9,.3,1.7,1,3.3,2.3,4.5,1.2,1.2,2.9,2,4.5,2.3,1.7,.3,3.4,0,4.9-.9l3.1,3.1c-2.2,1.2-4.7,2-7.1,2Z`,
  ],
}
export const Fullscreen = {
  prefix: 'fal',
  iconName: 'fullscreen',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M21,25c-.6,0-1,.2-1.4,.6l-11.6,11.5v-3.1c0-.6-.2-1-.6-1.4-.4-.4-.9-.6-1.4-.6s-1,.2-1.4,.6-.6,.8-.6,1.4v8c0,.3,0,.5,.2,.8,.2,.5,.6,.9,1,1,.3,.1,.5,.2,.8,.2H14c.6,0,1-.2,1.4-.6,.5-.4,.6-.8,.6-1.4s-.2-1-.6-1.4c-.3-.4-.8-.6-1.4-.6h-3.1l11.6-11.6c.4-.4,.6-.9,.6-1.4s-.2-1-.6-1.4c-.4-.5-.8-.6-1.5-.6h0ZM43.9,5.2c-.2-.5-.6-.9-1-1s-.6-.2-.9-.2h-8c-.6,0-1,.2-1.4,.6s-.6,.8-.6,1.4,.2,1,.6,1.4c.4,.4,.9,.6,1.4,.6h3.1l-11.5,11.6c-.2,.2-.4,.4-.5,.7-.1,.3-.2,.5-.2,.8s0,.6,.2,.8c.1,.3,.3,.5,.5,.7s.4,.4,.7,.5c.3,.1,.5,.2,.8,.2s.6,0,.8-.2c.3-.1,.5-.3,.7-.5l11.4-11.7v3.1c0,.6,.2,1,.6,1.4,.4,.4,.9,.6,1.4,.6s1-.2,1.4-.6c.5-.4,.6-.8,.6-1.4V6c0-.3,0-.5,0-.8Z`,
  ],
}

export const ExitFullScreenIcon = {
  prefix: 'fal',
  iconName: 'exit-fullscreen',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M21.8,25.1c-.3-.1-.5-.1-.8-.1h-7.9c-.6,0-1,.2-1.4,.6s-.6,.9-.6,1.4,.2,1,.6,1.4c.4,.4,.9,.6,1.4,.6h3.1l-11.5,11.5c-.2,.2-.4,.4-.5,.7s-.2,.5-.2,.8,0,.6,.2,.8c0,.3,.3,.5,.5,.7s.4,.4,.7,.5h.7c.3,0,.6,0,.8-.2,.3,0,.5-.3,.7-.5l11.6-11.6v3.1c0,.6,.2,1,.6,1.4s.9,.6,1.4,.6,1-.2,1.4-.6c.4-.4,.6-.9,.6-1.4v-8c0-.3,0-.5-.2-.8-.3-.3-.7-.7-1.2-.9h0ZM43.8,5.3c0-.3-.3-.5-.5-.7s-.4-.4-.7-.5c-.2-.1-.4-.1-.7-.1s-.6,0-.8,.2c-.3,0-.5,.3-.7,.5l-11.5,11.5v-3.1c0-.6-.2-1-.6-1.4s-.9-.6-1.4-.6-1,.2-1.4,.6-.6,.9-.6,1.4v8c0,.3,0,.5,.2,.8,.2,.5,.6,.9,1,1,.3,.1,.5,.2,.8,.2h8c.6,0,1-.2,1.4-.6s.6-.9,.6-1.4-.2-1-.6-1.4-.9-.6-1.4-.6h-3.1l11.5-11.6c.2-.2,.4-.4,.5-.7,.1-.3,.2-.5,.2-.8s0-.5-.2-.7Z`,
  ],
}
export const FileCertificate = {
  prefix: 'fal',
  iconName: 'file-certificate',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M23,32H15c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4c.4,.4,.9,.6,1.4,.6h8c.5,0,1-.2,1.4-.6s.6-.9,.6-1.4-.2-1-.6-1.4-.9-.6-1.4-.6Zm0-8H15c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4c.4,.4,.9,.6,1.4,.6h8c.5,0,1-.2,1.4-.6s.6-.9,.6-1.4-.2-1-.6-1.4-.9-.6-1.4-.6Zm13.1-4.3c.3-.2,.6-.5,.7-.9,.2-.4,.2-.8,.1-1.2,0-.4-.3-.7-.5-1L24.4,4.6c-.2-.2-.4-.3-.6-.4,0,0-.1,0-.2,0l-.6-.2H11c-1.6,0-3.1,.6-4.2,1.8s-1.8,2.7-1.8,4.2v28c0,1.6,.6,3.1,1.8,4.2,1.1,1.1,2.7,1.8,4.2,1.8h12c.5,0,1-.2,1.4-.6s.6-.9,.6-1.4-.2-1-.6-1.4-.9-.6-1.4-.6H11c-.5,0-1-.2-1.4-.6-.4-.4-.6-.9-.6-1.4V10c0-.5,.2-1,.6-1.4,.4-.4,.9-.6,1.4-.6h10v6c0,1.6,.6,3.1,1.8,4.2,1.1,1.1,2.7,1.8,4.2,1.8h8c.4,0,.8-.1,1.1-.3Zm-9.1-3.7c-.5,0-1-.2-1.4-.6-.4-.4-.6-.9-.6-1.4v-3.2l5.2,5.2h-3.2Zm-12,4h2c.5,0,1-.2,1.4-.6,.4-.4,.6-.9,.6-1.4s-.2-1-.6-1.4c-.4-.4-.9-.6-1.4-.6h-2c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4c.4,.4,.9,.6,1.4,.6Zm27.4,4.6c-.4-.4-.9-.6-1.4-.6h-10c-.5,0-1,.2-1.4,.6s-.6,.9-.6,1.4v16c0,.4,0,.7,.3,1,.2,.3,.5,.6,.8,.7,.3,.2,.7,.2,1,.2,.4,0,.7-.1,1-.3l3.9-2.6,4,2.6c.3,.2,.6,.3,1,.3,.3,0,.7,0,1-.2,.3-.2,.6-.4,.7-.7,.2-.3,.3-.6,.3-1V26c0-.5-.2-1-.6-1.4Zm-3.4,13.7l-1.9-1.3c-.3-.2-.7-.3-1.1-.3s-.8,.1-1.1,.3l-1.9,1.3v-10.2h6v10.2Z`,
  ],
}

export const FileCheck = {
  prefix: 'fal',
  iconName: 'file-check',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M22.99,39.99H11c-.53,0-1.04-.21-1.41-.59-.37-.37-.59-.88-.59-1.41V10c0-.53,.21-1.04,.59-1.41,.37-.37,.88-.59,1.41-.59h10v6c0,1.59,.63,3.12,1.76,4.24,1.12,1.12,2.65,1.76,4.24,1.76h6v10c0,.53,.21,1.04,.59,1.41s.88,.59,1.41,.59,1.04-.21,1.41-.59,.59-.88,.59-1.41v-12.12c-.02-.18-.06-.36-.12-.54v-.18c-.1-.21-.22-.39-.38-.56h0L24.49,4.6c-.17-.16-.35-.28-.56-.38-.07-.01-.13-.01-.2,0-.19-.1-.4-.18-.62-.22H11c-1.59,0-3.12,.63-4.24,1.76s-1.76,2.65-1.76,4.24v27.99c0,1.59,.63,3.12,1.76,4.24,1.12,1.12,2.65,1.76,4.24,1.76h12c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41-.21-1.04-.59-1.41-.88-.59-1.41-.59Zm2-29.17l5.18,5.18h-3.18c-.53,0-1.04-.21-1.41-.59-.37-.37-.59-.88-.59-1.41v-3.18ZM15,27.99h12c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41-.21-1.04-.59-1.41-.88-.59-1.41-.59H15c-.53,0-1.04,.21-1.41,.59-.37,.37-.59,.88-.59,1.41s.21,1.04,.59,1.41c.37,.37,.88,.59,1.41,.59Zm8,4h-8c-.53,0-1.04,.21-1.41,.59-.37,.37-.59,.88-.59,1.41s.21,1.04,.59,1.41c.37,.37,.88,.59,1.41,.59h8c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41-.21-1.04-.59-1.41-.88-.59-1.41-.59Zm-8-12h2c.53,0,1.04-.21,1.41-.59,.37-.37,.59-.88,.59-1.41s-.21-1.04-.59-1.41c-.37-.37-.88-.59-1.41-.59h-2c-.53,0-1.04,.21-1.41,.59-.37,.37-.59,.88-.59,1.41s.21,1.04,.59,1.41c.37,.37,.88,.59,1.41,.59Zm27.41,12.58c-.19-.19-.41-.34-.65-.44-.24-.1-.51-.15-.77-.15s-.53,.05-.77,.15c-.24,.1-.46,.25-.65,.44l-6.58,6.6-2.58-2.6c-.19-.19-.41-.33-.65-.44-.24-.1-.5-.15-.77-.15s-.52,.05-.77,.15c-.24,.1-.47,.25-.65,.44-.19,.19-.33,.41-.44,.65-.1,.24-.15,.5-.15,.77s.05,.52,.15,.77c.1,.24,.25,.47,.44,.65l4,4c.19,.19,.41,.34,.65,.44,.24,.1,.51,.15,.77,.15s.53-.05,.77-.15c.24-.1,.46-.25,.65-.44l8-8c.19-.19,.34-.41,.44-.65,.1-.24,.15-.51,.15-.77s-.05-.53-.15-.77c-.1-.24-.25-.46-.44-.65h0Z`,
  ],
}

export const FileTimes = {
  prefix: 'fal',
  iconName: 'file-times',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M29.9,26.8c.1-.2,.2-.5,.2-.8s0-.5-.2-.8c-.1-.2-.3-.5-.4-.7-.2-.2-.4-.3-.7-.4-.2-.1-.5-.2-.8-.2s-.5,0-.8,.2c-.2,.1-.5,.3-.7,.4l-2.6,2.6-2.6-2.6c-.4-.4-.9-.6-1.4-.6s-1,.2-1.4,.6-.6,.9-.6,1.4,.2,1,.6,1.4l2.6,2.6-2.6,2.6c-.2,.2-.3,.4-.4,.7-.1,.2-.2,.5-.2,.8s0,.5,.2,.8c.1,.2,.3,.5,.4,.7,.2,.2,.4,.3,.7,.4,.2,.1,.5,.2,.8,.2s.5,0,.8-.2c.2-.1,.5-.3,.7-.4l2.6-2.6,2.6,2.6c.2,.2,.4,.3,.7,.4,.2,.1,.5,.2,.8,.2s.5,0,.8-.2c.2-.1,.5-.3,.7-.4,.2-.2,.3-.4,.4-.7,.1-.2,.2-.5,.2-.8s0-.5-.2-.8c-.1-.2-.3-.5-.4-.7l-2.6-2.6,2.6-2.6c.2-.2,.3-.4,.4-.7Zm10-9.4v-.2c0-.2-.2-.4-.4-.6L27.5,4.6c-.2-.2-.4-.3-.6-.4h-.2c-.2-.1-.4-.2-.6-.2H14c-1.6,0-3.1,.6-4.2,1.8s-1.8,2.7-1.8,4.2v28c0,1.6,.6,3.1,1.8,4.2,1.1,1.1,2.7,1.8,4.2,1.8h20c1.6,0,3.1-.6,4.2-1.8,1.1-1.1,1.8-2.7,1.8-4.2V18h0c0-.3,0-.5-.1-.7Zm-11.9-6.5l5.2,5.2h-3.2c-.5,0-1-.2-1.4-.6-.4-.4-.6-.9-.6-1.4v-3.2Zm8,27.2c0,.5-.2,1-.6,1.4s-.9,.6-1.4,.6H14c-.5,0-1-.2-1.4-.6s-.6-.9-.6-1.4V10c0-.5,.2-1,.6-1.4s.9-.6,1.4-.6h10v6c0,1.6,.6,3.1,1.8,4.2s2.7,1.8,4.2,1.8h6v18Z`,
  ],
}

export const Film = {
  prefix: 'fal',
  iconName: 'film',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M39.6,2H8.4c-3.5,0-6.4,2.9-6.4,6.4v31.3c0,3.5,2.9,6.4,6.4,6.4h31.3c3.5,0,6.4-2.9,6.4-6.4V8.4c0-3.5-2.9-6.4-6.4-6.4ZM12,42h-3.6c-1.3,0-2.4-1.1-2.4-2.4v-3.6h6v6Zm0-10H6v-6h6v6Zm0-10H6v-6h6v6Zm0-10H6v-3.6c0-1.3,1.1-2.4,2.4-2.4h3.6v6Zm20,30h-16V26h16v16Zm0-20h-16V6h16V22Zm10,17.6c0,1.3-1.1,2.4-2.4,2.4h-3.6v-6h6v3.6Zm0-7.6h-6v-6h6v6Zm0-10h-6v-6h6v6Zm0-10h-6V6h3.6c1.3,0,2.4,1.1,2.4,2.4v3.6Z`,
  ],
}

export const Filter = {
  prefix: 'fal',
  iconName: 'filter',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M28,44c-.3,0-.6,0-.9-.2l-8-4c-.7-.3-1.1-1-1.1-1.8v-12.3L2.5,7.3c-.5-.6-.6-1.4-.3-2.1,.3-.7,1-1.2,1.8-1.2H44c.8,0,1.5,.5,1.8,1.2s.2,1.5-.3,2.1l-15.5,18.4v16.3c0,.7-.4,1.3-.9,1.7-.3,.2-.7,.3-1.1,.3Zm-6-7.2l4,2v-13.8c0-.5,.2-.9,.5-1.3l13.2-15.6H8.3l13.2,15.6c.3,.4,.5,.8,.5,1.3v11.8Z`,
  ],
}

export const Flag = {
  prefix: 'fal',
  iconName: 'flag',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M40.8,4.2c-.7-.3-1.6-.2-2.2,.3,0,0-1.7,1.5-6.6,1.5s-4.9-.9-7.3-1.9c-2.6-1.1-5.4-2.1-8.7-2.1-6.5,0-9,2.2-9.4,2.6s-.6,.9-.6,1.4V44c0,1.1,.9,2,2,2s2-.9,2-2v-12.9c.9-.4,2.7-1.1,6-1.1s4.9,.9,7.3,1.9c2.6,1.1,5.4,2.1,8.7,2.1,6.5,0,9-2.2,9.4-2.6s.6-.9,.6-1.4V6c0-.8-.5-1.5-1.2-1.8Zm-2.8,24.7c-.9,.4-2.7,1.1-6,1.1s-4.9-.9-7.3-1.9c-2.6-1.1-5.4-2.1-8.7-2.1s-4.6,.3-6,.8V7.1c.9-.4,2.7-1.1,6-1.1s4.9,.9,7.3,1.9c2.6,1.1,5.4,2.1,8.7,2.1s4.6-.3,6-.8V28.9Z`,
  ],
}

export const FlowSettingsIcon = {
  prefix: 'fal',
  iconName: 'flow',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M42.9,28.1h-2v-4.8c0-1-.8-1.8-1.8-1.8h-11.5c-.3-.7-.8-1.2-1.5-1.6v-3.5h9.8c2.5,0,4.6-2.1,4.6-4.6V7c0-2.5-2.1-4.6-4.6-4.6H12.3c-2.5,0-4.6,2.1-4.6,4.6v4.8c0,2.5,2.1,4.6,4.6,4.6h10.3v3.7c-.5,.4-.9,.8-1.2,1.4H9.1c-1,0-1.8,.8-1.8,1.8v4.8h-2.3c-1.7,0-3.1,1.4-3.1,3.1v5.5c0,1.7,1.4,3.1,3.1,3.1h2.2v2.3h-1.4c-1,0-1.8,.8-1.8,1.8s.8,1.8,1.8,1.8h6.3c1,0,1.8-.8,1.8-1.8s-.8-1.8-1.8-1.8h-1.4v-2.3h2.2c1.7,0,3.1-1.4,3.1-3.1v-5.5c0-1.7-1.4-3.1-3.1-3.1h-2.2v-3h11.1c.6,.7,1.5,1.2,2.6,1.2s2-.5,2.6-1.2h10.2v3h-2.4c-1.7,0-3.1,1.4-3.1,3.1v5.5c0,1.7,1.4,3.1,3.1,3.1h2.2v2.3h-1.4c-1,0-1.8,.8-1.8,1.8s.8,1.8,1.8,1.8h6.3c1,0,1.8-.8,1.8-1.8s-.8-1.8-1.8-1.8h-1.4v-2.3h2.2c1.7,0,3.1-1.4,3.1-3.1v-5.5c0-1.7-1.4-3.1-3.1-3.1Zm-30.3,3.5v4.6H5.5v-4.6h7.1Zm-.3-18.7c-.6,0-1.1-.5-1.1-1.1V7c0-.6,.5-1.1,1.1-1.1h23.6c.6,0,1.1,.5,1.1,1.1v4.8c0,.6-.5,1.1-1.1,1.1H12.3Zm30.2,23.3h-7.1v-4.6h7.1v4.6ZM17.6,7.7c-1,0-1.8,.8-1.8,1.8s.8,1.8,1.8,1.8,1.8-.8,1.8-1.8-.8-1.8-1.8-1.8Zm6.5,0c-1,0-1.8,.8-1.8,1.8s.8,1.8,1.8,1.8,1.8-.8,1.8-1.8-.8-1.8-1.8-1.8Zm6.5,0c-1,0-1.8,.8-1.8,1.8s.8,1.8,1.8,1.8,1.8-.8,1.8-1.8-.8-1.8-1.8-1.8Z',
  ],
}

export const FieldStringIcon = {
  prefix: 'fal',
  iconName: 'field-string',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M32,12c0-.5-.2-1-.6-1.4-.4-.4-.9-.6-1.4-.6H6c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4c.4,.4,.9,.6,1.4,.6h10v22c0,.5,.2,1,.6,1.4,.4,.4,.9,.6,1.4,.6s1-.2,1.4-.6c.4-.4,.6-.9,.6-1.4V14h10c.5,0,1-.2,1.4-.6,.4-.4,.6-.9,.6-1.4Zm10,10h-12c-.5,0-1,.2-1.4,.6s-.6,.9-.6,1.4,.2,1,.6,1.4,.9,.6,1.4,.6h4v10c0,.5,.2,1,.6,1.4s.9,.6,1.4,.6,1-.2,1.4-.6,.6-.9,.6-1.4v-10h4c.5,0,1-.2,1.4-.6s.6-.9,.6-1.4-.2-1-.6-1.4-.9-.6-1.4-.6Z',
  ],
}

export const FieldNumericIcon = {
  prefix: 'fal',
  iconName: 'field-numeric',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M13.7,6c-1.8,0-3.2,1.4-3.2,3.1s1.5,3.1,3.2,3.1,3.2-1.4,3.2-3.1-1.4-3.1-3.2-3.1Zm0,10c-1.8,0-3.2,1.4-3.2,3.1s1.5,3.1,3.2,3.1,3.2-1.4,3.2-3.1-1.4-3.1-3.2-3.1h0Zm20.5-3.7c1.8,0,3.2-1.4,3.2-3.1s-1.5-3.1-3.2-3.1-3.2,1.4-3.2,3.1,1.5,3.1,3.2,3.1ZM13.7,25.9c-1.8,0-3.2,1.4-3.2,3.1s1.4,3.1,3.2,3.1,3.2-1.4,3.2-3.1-1.4-3.1-3.2-3.1h0Zm10.3,9.9c-1.8,0-3.2,1.4-3.2,3.1s1.4,3.1,3.2,3.1,3.2-1.4,3.2-3.1-1.5-3.1-3.2-3.1h0Zm10.3-9.9c-1.8,0-3.2,1.4-3.2,3.1s1.4,3.1,3.2,3.1,3.2-1.4,3.2-3.1-1.5-3.1-3.2-3.1Zm0-10c-1.8,0-3.2,1.4-3.2,3.1s1.5,3.1,3.2,3.1,3.2-1.4,3.2-3.1-1.5-3.1-3.2-3.1ZM24,6c-1.8,0-3.2,1.4-3.2,3.1s1.4,3.1,3.2,3.1,3.2-1.4,3.2-3.1-1.4-3.1-3.2-3.1Zm0,19.9c-1.8,0-3.2,1.4-3.2,3.1s1.4,3.1,3.2,3.1,3.2-1.4,3.2-3.1-1.4-3.1-3.2-3.1Zm0-10c-1.8,0-3.2,1.4-3.2,3.1s1.4,3.1,3.2,3.1,3.2-1.4,3.2-3.1-1.4-3.1-3.2-3.1h0Z',
  ],
}

export const FilterIncludeIcon = {
  prefix: 'fal',
  iconName: 'filter-include',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M21.4,22.6l-5-5c-.4-.4-.9-.6-1.4-.6s-1,.2-1.4,.6-.6,.9-.6,1.4,0,.5,.2,.8c.1,.2,.2,.5,.4,.7l1.6,1.6h-7.2V14c0-.5-.2-1-.6-1.4-.4-.4-.9-.6-1.4-.6s-1,.2-1.4,.6c-.4,.4-.6,.9-.6,1.4v20c0,.5,.2,1,.6,1.4,.4,.4,.9,.6,1.4,.6s1-.2,1.4-.6c.4-.4,.6-.9,.6-1.4v-8h7.2l-1.6,1.6c-.2,.2-.3,.4-.4,.7-.1,.2-.2,.5-.2,.8s0,.5,.2,.8c.1,.2,.3,.5,.4,.7,.2,.2,.4,.3,.7,.4,.2,.1,.5,.2,.8,.2s.5,0,.8-.2c.2-.1,.5-.3,.7-.4l5-5c.2-.2,.3-.4,.4-.7,.2-.5,.2-1,0-1.5,0-.2-.2-.5-.4-.7h0Zm20.6-10.6c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4v8h-7.2l1.6-1.6c.2-.2,.3-.4,.4-.7,.1-.2,.2-.5,.2-.8s0-.5-.2-.8c-.1-.2-.2-.5-.4-.7-.2-.2-.4-.3-.7-.4-.2-.1-.5-.2-.8-.2s-.5,0-.8,.2c-.2,.1-.5,.2-.7,.4l-5,5c-.2,.2-.3,.4-.4,.7-.2,.5-.2,1,0,1.5,0,.2,.2,.5,.4,.7l5,5c.2,.2,.4,.3,.7,.4,.2,.1,.5,.2,.8,.2s.5,0,.8-.2c.2-.1,.5-.3,.7-.4,.2-.2,.3-.4,.4-.7,.1-.2,.2-.5,.2-.8s0-.5-.2-.8c-.1-.2-.3-.5-.4-.7l-1.6-1.6h7.2v8c0,.5,.2,1,.6,1.4s.9,.6,1.4,.6,1-.2,1.4-.6,.6-.9,.6-1.4V14c0-.5-.2-1-.6-1.4-.4-.4-.9-.6-1.4-.6Z',
  ],
}

export const FilterExcludeIcon = {
  prefix: 'fal',
  iconName: 'filter-exclude',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M20,12c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4v8h-7.2l1.6-1.6c.4-.4,.6-.9,.6-1.4s-.2-1-.6-1.4c-.4-.4-.9-.6-1.4-.6s-1,.2-1.4,.6l-5,5c-.2,.2-.3,.4-.4,.7-.2,.5-.2,1,0,1.5,0,.2,.2,.5,.4,.7l5,5c.2,.2,.4,.3,.7,.4,.2,.1,.5,.2,.8,.2s.5,0,.8-.2c.2-.1,.5-.3,.7-.4,.2-.2,.3-.4,.4-.7,.1-.2,.2-.5,.2-.8s0-.5-.2-.8c-.1-.2-.3-.5-.4-.7l-1.6-1.6h7.2v8c0,.5,.2,1,.6,1.4,.4,.4,.9,.6,1.4,.6s1-.2,1.4-.6,.6-.9,.6-1.4V14c0-.5-.2-1-.6-1.4-.4-.4-.9-.6-1.4-.6Zm23.9,11.2c0-.2-.2-.5-.4-.7l-5-5c-.2-.2-.4-.3-.7-.4-.2-.1-.5-.2-.8-.2-.5,0-1,.2-1.4,.6-.2,.2-.3,.4-.4,.7-.1,.2-.2,.5-.2,.8,0,.5,.2,1,.6,1.4l1.6,1.6h-7.2V14c0-.5-.2-1-.6-1.4-.4-.4-.9-.6-1.4-.6s-1,.2-1.4,.6c-.4,.4-.6,.9-.6,1.4v20c0,.5,.2,1,.6,1.4,.4,.4,.9,.6,1.4,.6s1-.2,1.4-.6,.6-.9,.6-1.4v-8h7.2l-1.6,1.6c-.2,.2-.3,.4-.4,.7-.1,.2-.2,.5-.2,.8s0,.5,.2,.8c.1,.2,.3,.5,.4,.7,.2,.2,.4,.3,.7,.4,.2,.1,.5,.2,.8,.2s.5,0,.8-.2c.2-.1,.5-.3,.7-.4l5-5c.2-.2,.3-.4,.4-.7,.2-.5,.2-1,0-1.5Z',
  ],
}

export const FlapsIcon = {
  prefix: 'fal',
  iconName: 'flaps',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M40,6H8c-2.2,0-4,1.8-4,4v28c0,2.2,1.8,4,4,4h32c2.2,0,4-1.8,4-4V10c0-2.2-1.8-4-4-4ZM16,38H8V10h8v28Zm12,0h-8V10h8v28Zm12,0h-8V10h8v28Z',
  ],
}

export const FlowMetric = {
  prefix: 'fal',
  iconName: 'flow-metric',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M43.6,20.1c-.8-3.9-2.7-7.4-5.4-10.2,0,0,0,0,0,0s0,0,0,0c-7.8-7.8-20.4-7.8-28.2,0,0,0,0,0,0,0s0,0,0,0c-7.8,7.8-7.8,20.5,0,28.3,7.8,7.8,20.5,7.8,28.3,0,4.7-4.7,6.7-11.4,5.4-18h0Zm-12.7,18.3c-6.7,3.2-14.7,1.4-19.4-4.4h8c2.2,2.5,5.9,2.7,8.4,.5,.2-.2,.4-.3,.5-.5h8c-1.5,1.9-3.4,3.4-5.5,4.4Zm-8.9-8.4c0-1.1,.9-2,2-2,1.1,0,2,.9,2,2,0,1.1-.9,2-2,2-.5,0-1-.2-1.4-.6-.4-.4-.6-.9-.6-1.4Zm14.6-4.6c.4,.4,.9,.6,1.4,.6h1.9c-.2,1.4-.5,2.7-1,4h-8.8c0-2.5-1.6-4.8-4-5.6v-6.4c0-1.1-.9-2-2-2s-2,.9-2,2v6.4c-2.4,.8-4,3.1-4,5.6H9.2c-.5-1.3-.9-2.6-1-4h1.9c1.1,0,2-.9,2-2s-.9-2-2-2h-1.9c.4-2.8,1.5-5.5,3.2-7.8l1.3,1.3c.8,.8,2.1,.8,2.8,0,.8-.8,.8-2,0-2.8l-1.3-1.3c2.3-1.8,4.9-2.9,7.8-3.2v1.9c0,1.1,.9,2,2,2s2-.9,2-2v-1.9c2.8,.4,5.5,1.5,7.8,3.2l-1.3,1.3c-.8,.8-.8,2,0,2.8s2,.8,2.8,0c0,0,0,0,0,0l1.3-1.3c1.8,2.3,2.9,5,3.2,7.8h-1.9c-1.1,0-2,.9-2,2,0,.5,.2,1,.6,1.4h0Z',
  ],
}

export const FlowAggregator = {
  prefix: 'fal',
  iconName: 'flow-aggregator',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M38.16,21.19c-1.57,0-2.99,.62-4.04,1.63-.71,.68-1.25,1.52-1.54,2.48-4.99-.26-6.8-2.38-6.8-7.69V9.12l2.55,2.74,1.28,1.38c.37,.39,.87,.59,1.37,.59,.46,0,.91-.17,1.27-.5,.75-.7,.8-1.88,.1-2.63l-4.02-4.32-3.05-3.28c-.35-.38-.85-.59-1.36-.59h0c-.52,0-1.02,.22-1.37,.6l-3.04,3.31-3.9,4.25c-.7,.76-.65,1.94,.11,2.63,.76,.7,1.94,.65,2.63-.11l1.16-1.26,2.55-2.78v8.45c0,7.35,3.48,11.09,10.62,11.42,.36,.99,.97,1.85,1.77,2.51,1.01,.83,2.3,1.34,3.71,1.34,3.22,0,5.84-2.62,5.84-5.84s-2.62-5.84-5.84-5.84Zm0,7.95c-.16,0-.31-.02-.46-.06-.94-.21-1.65-1.05-1.65-2.06,0-.95,.63-1.74,1.5-2.01,.2-.06,.4-.1,.62-.1,1.16,0,2.11,.95,2.11,2.11s-.95,2.11-2.11,2.11Zm-12.38,4.98v-4.18c-2.88-1.49-4.53-3.84-5.41-6.44-1.06,1.09-2.66,1.64-4.96,1.79-.74-2.37-2.96-4.1-5.57-4.1-3.22,0-5.84,2.62-5.84,5.84s2.62,5.84,5.84,5.84c2.52,0,4.66-1.61,5.48-3.84,2.82-.15,5.06-.84,6.73-2.09v7.18c-2.31,.78-3.98,2.96-3.98,5.53,0,3.22,2.62,5.84,5.84,5.84s5.84-2.62,5.84-5.84c0-2.57-1.67-4.75-3.98-5.53Zm-15.94-4.98c-1.16,0-2.11-.95-2.11-2.11s.95-2.11,2.11-2.11,2.11,.95,2.11,2.11-.95,2.11-2.11,2.11Zm14.08,12.62c-1.16,0-2.11-.95-2.11-2.11s.95-2.11,2.11-2.11,2.11,.95,2.11,2.11-.95,2.11-2.11,2.11Z',
  ],
}

export const FlowSource = {
  prefix: 'fal',
  iconName: 'flow-source',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M24,12c-4.4,0-8,3.6-8,8s3.6,8,8,8,8-3.6,8-8-3.6-8-8-8Zm0,12c-2.2,0-4-1.8-4-4s1.8-4,4-4,4,1.8,4,4c0,2.2-1.8,4-4,4Zm0-20c-8.8,0-16,7.2-16,16s14,23,14.6,23.6c.4,.2,1,.4,1.4,.4s1-.2,1.4-.4c.6-.6,14.6-12.8,14.6-23.6S32.8,4,24,4Zm0,35.4c-4.2-4-12-12.6-12-19.4s5.4-12,12-12,12,5.4,12,12-7.8,15.4-12,19.4Z',
  ],
}

export const ForecastIcon = {
  prefix: 'fal',
  iconName: 'forecast',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M39,42.1l-1.3-2.4H7.9V6.3c0-.5-.2-1-.6-1.4-.4-.4-.9-.6-1.4-.6-1.1,0-2,.9-2,2V41.7c0,1.1,.9,2,2,2H40.6c-.7-.4-1.3-.9-1.6-1.6ZM13.4,27.9c1.4,0,2.6-1.1,2.6-2.5h0v-.2l4.3-4.4h.6l2.5,2.5c0,.7,.3,1.3,.7,1.8,.5,.5,1.1,.7,1.8,.7,.3,0,.7,0,1-.2,.8-2,2.2-3.6,4-4.7l3.5-3.5c.5,0,1-.2,1.4-.4,.4-.3,.7-.7,.9-1.2,.4-1,.2-2-.6-2.8-.7-.8-1.8-1-2.8-.6-.5,.2-.9,.5-1.2,.9-.3,.4-.4,.9-.4,1.4v.2l-5.7,5.7h-.1l-2.7-2.8c0-1.4-1.2-2.5-2.6-2.5-.7,0-1.4,.3-1.8,.9-.5,.5-.7,1.1-.8,1.7l-4.8,4.7c-.7,0-1.3,.3-1.7,.8-.5,.5-.8,1.1-.8,1.8,0,1.4,1.2,2.6,2.6,2.6h0Zm30.4,11.5l-2.6-4.6c3-2.8,3.2-7.5,.4-10.6-2.8-3-7.5-3.2-10.6-.4-3,2.8-3.2,7.5-.4,10.6,2,2.2,5.1,3,7.9,2l2.5,4.5c.2,.4,.6,.6,1,.8,.4,.1,.8,0,1.1-.1,.4-.2,.6-.5,.7-.9,.1-.4,0-.8-.1-1.2Zm-3.5-9.1c-.6,2.3-2.9,3.7-5.2,3.2-2.3-.6-3.7-2.9-3.2-5.2,.5-2,2.3-3.4,4.4-3.3h.8c.5,.3,1.1,.5,1.5,.8,.7,.5,1.2,1.2,1.5,2,.3,.8,.4,1.7,.2,2.5Z',
  ],
}
export const FieldDateIcon = {
  prefix: 'fal',
  iconName: 'field-datetime',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M33.4,32.5h-2.7v-2.5c0-.4-.2-.8-.5-1.1-.6-.6-1.7-.6-2.3,0-.3,.3-.5,.7-.5,1.1v4.1c0,.4,.2,.8,.5,1.1,.3,.3,.7,.5,1.1,.5h4.3c.4,0,.8-.2,1.1-.5,.3-.3,.5-.7,.5-1.1s-.2-.8-.5-1.1c-.3-.3-.7-.5-1.1-.5Zm-15.7,7.4h-7c-.3,0-.5-.2-.5-.5V15.3h27.5v5.5c.9,.6,1.8,1.3,2.6,2,.4,.4,.8,.8,1.1,1.3V12.4c0-2.1-1.7-3.8-3.8-3.8h-3.8V4.6c0-.5-.4-.9-.9-.9h-3.1c-.5,0-.9,.4-.9,.9v4.1h-10V4.6c0-.5-.4-.9-.9-.9h-3.1c-.5,0-.9,.4-.9,.9v4.1h-3.8c-2.1,0-3.8,1.7-3.8,3.8v27.5c0,2.1,1.7,3.8,3.8,3.8h10.5c-.2-.2-.4-.4-.6-.6-1-1-1.7-2-2.4-3.2Zm23-11.3c-.6-1.4-1.4-2.6-2.5-3.7-1.1-1.1-2.3-1.9-3.7-2.5-3.5-1.4-7.5-1-10.6,1-1.9,1.2-3.3,3-4.2,5.1-.9,2.1-1.1,4.3-.6,6.5,.4,2.2,1.5,4.2,3.1,5.8,1.6,1.6,3.6,2.7,5.8,3.1,.7,.1,1.5,.2,2.2,.2,1.5,0,2.9-.3,4.3-.9,2.1-.9,3.8-2.3,5.1-4.2,1.2-1.9,1.9-4,1.9-6.3s-.3-2.9-.9-4.3Zm-4.8,10c-1.5,1.5-3.5,2.3-5.6,2.3s-3.1-.5-4.4-1.3c-1.3-.9-2.3-2.1-2.9-3.6-.6-1.5-.8-3.1-.5-4.6,.3-1.6,1.1-3,2.2-4.1s2.5-1.9,4.1-2.2c.5-.1,1-.2,1.6-.2,1,0,2.1,.2,3,.6,1.5,.6,2.7,1.6,3.6,2.9,.9,1.3,1.3,2.8,1.3,4.4s-.8,4.1-2.3,5.6Z',
  ],
}

export const FlowDestination = {
  prefix: 'fal',
  iconName: 'flow-destination',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M40.8,4.2c-.7-.3-1.6-.2-2.2,.3,0,0-1.7,1.5-6.6,1.5s-4.9-.9-7.3-1.9c-2.6-1.1-5.4-2.1-8.7-2.1-6.5,0-9,2.2-9.4,2.6s-.6,.9-.6,1.4V44c0,1.1,.9,2,2,2s2-.9,2-2v-12.9c.9-.4,2.7-1.1,6-1.1s4.9,.9,7.3,1.9c2.6,1.1,5.4,2.1,8.7,2.1,6.5,0,9-2.2,9.4-2.6s.6-.9,.6-1.4V6c0-.8-.5-1.5-1.2-1.8Zm-2.8,24.7c-.9,.4-2.7,1.1-6,1.1s-4.9-.9-7.3-1.9c-2.6-1.1-5.4-2.1-8.7-2.1s-4.6,.3-6,.8V7.1c.9-.4,2.7-1.1,6-1.1s4.9,.9,7.3,1.9c2.6,1.1,5.4,2.1,8.7,2.1s4.6-.3,6-.8V28.9Z',
  ],
}

export const FlowOther = {
  prefix: 'fal',
  iconName: 'flow-other',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M4,8c0-2.2,1.8-4,4-4H28c2.2,0,4,1.8,4,4v8h8c2.2,0,4,1.8,4,4v20c0,2.2-1.8,4-4,4H20c-2.2,0-4-1.8-4-4v-8H8c-2.2,0-4-1.8-4-4V8Zm16,24v8h20V20h-8v8c0,2.2-1.8,4-4,4h-8Zm8-4V8H8V28H28Z',
  ],
}
export const GroupSettingsIcon = {
  prefix: 'fal',
  iconName: 'group-settings',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M43.5,18h-3.7c-.1-.4-.2-.7-.4-1l2.6-2.6c1.1-1.1,1.2-3,0-4.2l-4-4c-1.1-1.2-3-1.2-4.2,0l-2.6,2.5c-.3-.2-.7-.3-1.1-.5v-3.6c0-1.6-1.3-2.9-2.9-2.9h-5.7c-1.6,0-2.9,1.3-2.9,2.9v3.5c-.3,.1-.6,.3-1,.4l-2.5-2.5c-.6-.6-1.3-.9-2.1-.9h0c-.8,0-1.5,.3-2.1,.8l-4.1,4c-1.2,1.1-1.2,3,0,4.2l2.5,2.5c-.2,.4-.3,.7-.4,1.1h-3.6c-1.6,0-2.9,1.3-2.9,2.9v5.7c0,1.6,1.3,2.9,2.9,3h3.7c.1,.4,.3,.7,.4,1l-2.6,2.6c-.6,.6-.9,1.3-.9,2.1s.3,1.5,.9,2.1l4,4c1.1,1.1,3,1.2,4.2,0l2.7-2.7c.3,.1,.6,.3,.9,.4v3.8c0,1.6,1.3,2.9,2.9,2.9h5.7c1.6,0,2.9-1.3,2.9-2.9v-1.6c0-1-.8-1.8-1.8-1.8s-1.8,.8-1.8,1.8v1h-4.5v-3.7c0-1.3-.8-2.4-2-2.8-.5-.2-.9-.3-1.3-.5-1.1-.5-2.5-.3-3.3,.6l-2.6,2.6-3.2-3.2,2.5-2.5c.9-.9,1.1-2.3,.6-3.4-.2-.4-.4-.9-.6-1.4-.4-1.2-1.5-2-2.8-2h-3.5v-4.5h3.4c1.3,0,2.4-.8,2.8-2,.2-.5,.4-1,.6-1.5,.5-1.1,.3-2.5-.5-3.4l-2.4-2.4,3.2-3.2,2.4,2.4c.9,.9,2.2,1.1,3.4,.6,.4-.2,.9-.4,1.4-.6,1.2-.4,2-1.5,2-2.8v-3.4h4.5v3.4c0,1.2,.8,2.4,2,2.8,.5,.2,1,.4,1.5,.6,1.1,.6,2.5,.3,3.4-.5l2.4-2.4,3.2,3.2-2.5,2.5c-.9,.9-1.1,2.2-.6,3.3,.2,.5,.4,.9,.6,1.4,.4,1.2,1.5,2,2.8,2h3.6v4.5h-1.3c-1,0-1.8,.8-1.8,1.7,0,1,.8,1.8,1.7,1.8h1.9c.8,0,1.5-.3,2.1-.8,.6-.6,.9-1.3,.9-2.1v-5.7c0-1.6-1.3-2.9-2.9-3Zm-3.4,13.9c-1.4-1.4-3.3-3.3-6.5-6.6-.3-.3-.3-.9-.3-1.8,0-1.8-.2-4.4-3.2-7-3.7-3.1-8-3.4-11.8-.6-.5,.3-.7,.9-.7,1.4s.3,1.1,.7,1.4c2.4,1.8,3.8,2.7,4.6,3.3-.2,1.3-.7,1.8-1.1,1.9l-4.4-2.6c-.5-.3-1.1-.3-1.7,0-.5,.3-.9,.8-1,1.4,0,.2-.7,5.6,3.2,8.6,2.8,2.1,6.1,1.7,7.8,1.3,1.9,1.9,7,7,8.7,8.8,.8,.9,2.8,2.9,5.2,2.9s2.2-.4,3.4-1.5c3.9-3.6,.7-7.1-.4-8.2-.8-.9-1.6-1.7-2.6-2.7Zm.6,8.3c-.7,.7-1.3,1.2-3.7-1.2-2.1-2.2-9.2-9.3-9.5-9.6-.3-.3-.8-.5-1.3-.5s-.4,0-.6,.1c0,0-3.2,1.2-5.5-.5-.8-.6-1.3-1.4-1.5-2.3l2,1.2c.3,.2,.6,.2,.9,.2h0c1.7,0,4.8-1.3,5-6.4,0-.6-.3-1.2-.8-1.6-.2-.2-1.5-1-2.8-2,1.7-.3,3.4,.2,5,1.6,1.8,1.5,1.9,2.8,1.9,4.4,0,1.3,.1,2.7,1.2,4,0,0,0,0,0,0,3.2,3.4,5.2,5.3,6.6,6.7,1,1,1.8,1.7,2.5,2.6,1.9,2,1.4,2.5,.6,3.2Z',
  ],
}

export const GridViewIcon = {
  prefix: 'fal',
  iconName: 'grid',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M8.8,35.3l1.4-1.6c.3-.3,.4-.7,.4-1.1v-.3c0-.6-.3-.9-.9-.9H5.2c-.3,0-.6,.3-.6,.6v1.2c0,.3,.3,.6,.6,.6h1.8c-.3,.3-.6,.6-.9,1l-.4,.5c-.3,.4-.4,.8-.2,1.2v.2c.3,.5,.6,.6,1,.6h.4c.8,0,1.2,.2,1.2,.7s-.3,.6-1.1,.6c-.4,0-.8,0-1.2-.2-.5-.3-.9-.3-1.2,.2l-.4,.7c-.3,.5-.3,.9,.2,1.2,.6,.4,1.6,.7,2.9,.7,2.7,0,3.8-1.8,3.8-3.4,0-1.1-.7-2.3-2.2-2.7Zm33.9-.7H17.8c-.7,0-1.2,.6-1.2,1.2v1.2c0,.7,.6,1.2,1.2,1.2h25c.7,0,1.2-.6,1.2-1.2v-1.2c0-.7-.6-1.2-1.2-1.2Zm0-25H17.8c-.7,0-1.2,.6-1.2,1.2v1.2c0,.7,.6,1.2,1.2,1.2h25c.7,0,1.2-.6,1.2-1.2v-1.2c0-.7-.6-1.2-1.2-1.2Zm0,12.5H17.8c-.7,0-1.2,.6-1.2,1.2v1.2c0,.7,.6,1.2,1.2,1.2h25c.7,0,1.2-.6,1.2-1.2v-1.2c0-.7-.6-1.2-1.2-1.2ZM5.2,16.5h5c.3,0,.6-.3,.6-.6v-1.2c0-.3-.3-.6-.6-.6h-1.2V7.1c0-.3-.3-.6-.6-.6h-1.9c-.2,0-.5,.1-.6,.3l-.6,1.2c-.2,.3,0,.7,.3,.8,0,0,.2,0,.3,0h.6v5h-1.2c-.3,0-.6,.3-.6,.6v1.2c0,.3,.3,.6,.6,.6Zm-.3,12.5h5.3c.3,0,.6-.3,.6-.6v-1.2c0-.3-.3-.6-.6-.6h-3c.3-.8,3.8-1.5,3.8-4.4s-2-3.1-3.5-3.1-2.6,.8-3.2,1.5c-.3,.4-.2,.8,.2,1.2l.7,.5c.4,.4,.9,.2,1.3-.2,.2-.2,.5-.3,.7-.3,.3,0,.7,.1,.7,.7,0,1-4,1.7-4,5.4v.3c0,.6,.4,.9,.9,.9Z',
  ],
}
export const HealthMonitoringIcon = {
  prefix: 'fal',
  iconName: 'health-monitoring',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M36.8,19h-1.6l-1.8-2.7c-.3-.5-.9-.8-1.5-.9-.6,0-1.2,.2-1.6,.7l-1.3,1.6-2.1-4.3c-.3-.7-1.1-1.1-1.8-1.1-.8,0-1.4,.6-1.7,1.3l-2.7,7.8-2-3.9c-.3-.6-.9-1-1.6-1-.7,0-1.3,.3-1.7,.9l-1.2,2h-3c-1.1,0-1.9,.9-1.9,1.9s.9,1.9,1.9,1.9h4.1c.7,0,1.3-.3,1.6-.9h0l2.5,4.8c.3,.6,1,1,1.7,1s0,0,.1,0c.8,0,1.4-.6,1.7-1.3l2.6-7.7,1.4,2.9c.3,.6,.9,1,1.5,1.1,.6,0,1.3-.2,1.7-.7l1.5-1.9,.9,1.4c.4,.5,1,.9,1.6,.9h2.6c1.1,0,1.9-.9,1.9-1.9s-.9-1.9-1.9-1.9Zm2.4-14.1H8.7c-3.2,0-5.7,2.6-5.7,5.7V29.7c0,3.2,2.6,5.7,5.7,5.7h13.4v3.8h-5.7c-1.1,0-1.9,.9-1.9,1.9s.9,1.9,1.9,1.9h15.3c1.1,0,1.9-.9,1.9-1.9s-.9-1.9-1.9-1.9h-5.7v-3.8h13.4c3.2,0,5.7-2.6,5.7-5.7V10.6c0-3.2-2.6-5.7-5.7-5.7Zm1.9,24.8c0,1.1-.9,1.9-1.9,1.9H8.7c-1.1,0-1.9-.9-1.9-1.9V10.6c0-1.1,.9-1.9,1.9-1.9h30.5c1.1,0,1.9,.9,1.9,1.9V29.7Z',
  ],
}

export const HealthLogIcon = {
  prefix: 'fal',
  iconName: 'health-log',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M28.8,32.5h-11.7c-1,0-1.8,.8-1.8,1.8s.8,1.8,1.8,1.8h11.7c1,0,1.8-.8,1.8-1.8s-.8-1.8-1.8-1.8Zm-13.5-5.7c0,1,.8,1.8,1.8,1.8h11.7c1,0,1.8-.8,1.8-1.8s-.8-1.8-1.8-1.8h-11.7c-1,0-1.8,.8-1.8,1.8Zm24.6-9.9s0,0,0-.1c0,0,0-.1,0-.2,0,0,0,0,0-.1,0,0,0-.1,0-.2,0,0,0,0,0-.1,0,0,0,0,0-.1L29.1,4.6s0,0,0,0c0,0-.1-.1-.2-.2,0,0,0,0-.1,0,0,0-.1,0-.2-.1,0,0,0,0-.1,0,0,0-.2,0-.3,0,0,0,0,0,0,0-.1,0-.2,0-.4,0H10c-1,0-1.8,.8-1.8,1.8V42.2c0,1,.8,1.8,1.8,1.8h28.1c1,0,1.8-.8,1.8-1.8V17.2c0-.1,0-.2,0-.3Zm-10.2-6.3l4.4,4.8h-4.4v-4.8Zm6.6,29.8H11.8V7.6h14.2v9.6c0,1,.8,1.8,1.8,1.8h8.4v21.3Z',
  ],
}

export const H1 = {
  prefix: 'fal',
  iconName: 'h1-01',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M25.2,12.3h-7.2c-.5,0-1,.4-1,1v1.6c0,.5,.4,1,1,1h2v6.4H9.4v-6.4h2c.5,0,1-.4,1-1v-1.4c0-.6-.5-1.2-1.2-1.2H4.2c-.6,0-1.2,.5-1.2,1.2v1.4c0,.5,.4,1,1,1h2v16.3h-2c-.5,0-1,.4-1,1v1.4c0,.6,.5,1.2,1.2,1.2h7.2c.5,0,1-.4,1-1v-1.6c0-.5-.4-1-1-1h-2v-6.4h10.5v6.4h-2c-.5,0-1,.4-1,1v1.4c0,.6,.5,1.2,1.2,1.2h7c.6,0,1.2-.5,1.2-1.2v-1.4c0-.5-.4-1-1-1h-2V15.8h2c.5,0,1-.4,1-1v-1.4c0-.6-.5-1.2-1.2-1.2Zm18.7,19.8h-4.1V14.1c0-1-.8-1.7-1.8-1.7h-1.8c-.7,0-1.3,.4-1.6,1l-1.8,3.5c-.4,.9,0,1.9,.8,2.3,.2,.1,.5,.2,.8,.2h1.8v12.8h-4.1c-.6,0-1.2,.5-1.2,1.2v1.2c0,.6,.5,1.2,1.2,1.2h11.7c.6,0,1.2-.5,1.2-1.2v-1.2c0-.6-.5-1.2-1.2-1.2Z`,
  ],
}

export const History = {
  prefix: 'fal',
  iconName: 'history',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M25.4,16.6c-.4-.4-.9-.6-1.4-.6s-1,.2-1.4,.6c-.4,.4-.6,.9-.6,1.4v6c0,.5,.2,1,.6,1.4s.9,.6,1.4,.6h4c.5,0,1-.2,1.4-.6s.6-.9,.6-1.4-.2-1-.6-1.4-.9-.6-1.4-.6h-2v-4c0-.5-.2-1-.6-1.4Zm17.1-.2c-1.5-3.7-4.1-6.8-7.4-9-3.3-2.2-7.2-3.4-11.1-3.4-5.1,0-10.1,2-13.8,5.5v-3.5c0-.5-.2-1-.6-1.4-.4-.4-.9-.6-1.4-.6s-1,.2-1.4,.6c-.4,.4-.6,.9-.6,1.4V15c0,.5,.2,1,.6,1.4,.4,.4,.9,.6,1.4,.6h9c.5,0,1-.2,1.4-.6,.4-.4,.6-.9,.6-1.4s-.2-1-.6-1.4c-.4-.4-.9-.6-1.4-.6h-4.8c2.6-2.7,6-4.4,9.7-4.8,3.7-.4,7.4,.4,10.5,2.5,3.1,2,5.4,5.1,6.6,8.6s1,7.4-.4,10.8-3.9,6.3-7.2,8.1c-3.3,1.8-7.1,2.4-10.7,1.6-3.6-.7-6.9-2.7-9.3-5.6-2.4-2.9-3.6-6.5-3.6-10.2,0-.5-.2-1-.6-1.4-.4-.4-.9-.6-1.4-.6s-1,.2-1.4,.6c-.4,.4-.6,.9-.6,1.4,0,4,1.2,7.8,3.4,11.1,2.2,3.3,5.3,5.9,9,7.4,3.7,1.5,7.7,1.9,11.6,1.1,3.9-.8,7.4-2.7,10.2-5.5,2.8-2.8,4.7-6.4,5.5-10.2,.8-3.9,.4-7.9-1.1-11.6Z`,
  ],
}

export const HomeLgAlt = {
  prefix: 'fal',
  iconName: 'home-lg-alt',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M22.7,5.5c.8-.7,1.9-.7,2.7,0l18,16c.8,.7,.9,2,.2,2.8-.7,.8-2,.9-2.8,.2l-.7-.6v15.1c0,2.2-1.8,4-4,4H12c-2.2,0-4-1.8-4-4v-15.1l-.7,.6c-.8,.7-2.1,.7-2.8-.2-.7-.8-.7-2.1,.2-2.8L22.7,5.5Zm-10.7,14.8v18.7h6v-10c0-1.1,.9-2,2-2h8c1.1,0,2,.9,2,2v10h6V20.3l-12-10.7-12,10.7Zm14,18.7v-8h-4v8h4Z`,
  ],
}

export const HorizontalRule = {
  prefix: 'fal',
  iconName: 'horizontal-rule',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M44.5,26c1,0,1.5-.7,1.5-1.4v-1.2c0-.7-.6-1.4-1.5-1.4H3.5c-1,0-1.5,.7-1.5,1.4v1.2c0,.7,.6,1.4,1.5,1.4H44.5Z`,
  ],
}

export const HealthPacketIcon = {
  prefix: 'fal',
  iconName: 'health-packet',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M45.7,33.1c-.3-.7-1-1.2-1.8-1.2h0c-.4-.3-.8-2.1-1-3-.7-2.9-1.7-7.4-6.4-7.4s-2.3,.5-3.2,1.3c-.4-3.5-1.4-6.9-5.1-7.2-2.4-.2-4.1,1.2-5.6,3.3-.7-4.1-1.9-7.9-5.6-8.2-5.1-.3-6,4.6-6.7,9.1-.3,1.7-.6,3.4-1,5-2,7-5.6,8.3-5.8,8.4-.5,.1-.9,.5-1.1,.9h0c0,.1,0,.2-.1,.3,0,0,0,0,0,.1,0,0,0,.2,0,.2h0c0,0,0,.1,0,.2,0,0,0,.1,0,.2,0,0,0,.1,0,.2,0,0,0,0,0,.1h0c0,.2,.1,.4,.2,.5,0,0,0,0,0,0,.1,.1,.2,.3,.4,.4,0,0,0,0,0,0,.1,0,.3,.2,.4,.2,0,0,0,0,0,0,.1,0,.3,0,.4,0,.4,0,9.7,.9,15-4.8,.7-.7,1.3-1.6,1.9-2.4,.9,2.4,2.3,4.1,5,4.1s3.6-1.1,5-2.5c.6,2,1.6,3.8,3.4,4.8,1.1,.7,2.4,1,3.9,1s4.1-.5,6.8-1.6h0s0,0,0,0c.9-.4,1.4-1.5,1-2.4Zm-29.7-3.6c-1.8,1.9-4.2,2.8-6.4,3.3,1.2-1.6,2.4-3.8,3.3-6.9,.5-1.9,.8-3.8,1.1-5.4,.8-4.9,1.3-6,2.7-5.9,1.6,.1,2.2,4.7,2.6,8,0,.6,.2,1.2,.2,1.8-1,1.9-2.1,3.7-3.5,5.2Zm9.6,.5c-1.2,0-1.8-2.4-2.2-5,0,0,0-.2,.1-.3,1.4-2.7,2.9-5.5,4.3-5.4,1.2,.1,1.5,2.6,1.9,6,0,.4,0,.8,.1,1.1-1.3,1.7-2.9,3.6-4.1,3.6Zm10.3,2.9c-1.3-.8-1.9-2.9-2.2-5.3,1-1.2,2.1-2.4,2.7-2.4,1.5,0,2,1.1,2.7,4.4,.2,1.1,.5,2.3,1,3.4-1.9,.4-3.3,.4-4.2-.1Z',
  ],
}

export const HelpSupportIcon = {
  prefix: 'fal',
  iconName: 'help',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M25.4,30.6c-.2-.2-.4-.3-.7-.4-.2-.1-.5-.2-.8-.2s-.5,0-.8,.2c-.2,.1-.5,.3-.7,.4,0,0-.2,.2-.2,.3,0,.1-.1,.2-.2,.4,0,.1,0,.2-.1,.4,0,.1,0,.3,0,.4,0,.3,0,.5,.2,.8,0,.2,.2,.5,.4,.7s.4,.3,.7,.4c.2,.1,.5,.2,.8,.2s.5,0,.8-.2c.2,0,.5-.2,.7-.4,.2-.2,.3-.4,.4-.7,0-.2,.1-.5,.1-.8,0-.3,0-.5-.1-.8,0-.2-.2-.5-.4-.7Zm2.4-15.2c-1.1-.9-2.4-1.4-3.8-1.4-1.1,0-2.1,.3-3,.8s-1.7,1.3-2.2,2.2c-.1,.2-.2,.5-.3,.7,0,.3,0,.5,0,.8,0,.3,.2,.5,.3,.7,.2,.2,.4,.4,.6,.5,.2,.1,.5,.2,.8,.3,.3,0,.5,0,.8,0,.3,0,.5-.2,.7-.4,.2-.2,.4-.4,.5-.6,.2-.3,.4-.6,.7-.7,.3-.2,.7-.3,1-.3,.5,0,1,.2,1.4,.6,.4,.4,.6,.9,.6,1.4s-.2,1-.6,1.4-.9,.6-1.4,.6-1,.2-1.4,.6-.6,.9-.6,1.4v2c0,.5,.2,1,.6,1.4s.9,.6,1.4,.6,1-.2,1.4-.6,.6-.9,.6-1.4v-.4c1.3-.5,2.4-1.4,3.1-2.6,.7-1.2,1-2.6,.7-4-.2-1.4-1-2.6-2-3.6Zm14.6,.9c-1-2.4-2.5-4.6-4.3-6.5s-4.1-3.3-6.5-4.3c-2.4-1-5-1.5-7.7-1.5-4,0-7.8,1.2-11.1,3.4-3.3,2.2-5.9,5.3-7.4,9-1.5,3.7-1.9,7.7-1.1,11.6,.8,3.9,2.7,7.4,5.5,10.2,2.8,2.8,6.4,4.7,10.2,5.5,3.9,.8,7.9,.4,11.6-1.1,3.7-1.5,6.8-4.1,9-7.4,2.2-3.3,3.4-7.2,3.4-11.1s-.5-5.2-1.5-7.7Zm-7.2,19c-3,3-7.1,4.7-11.3,4.7s-6.3-.9-8.9-2.7c-2.6-1.8-4.7-4.3-5.9-7.2-1.2-2.9-1.5-6.1-.9-9.2,.6-3.1,2.1-6,4.4-8.2s5.1-3.8,8.2-4.4c3.1-.6,6.3-.3,9.2,.9,2.9,1.2,5.4,3.3,7.2,5.9,1.8,2.6,2.7,5.7,2.7,8.9s-1.7,8.3-4.7,11.3Z`,
  ],
}

export const ImportIcon = {
  prefix: 'fal',
  iconName: 'monitor-import',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M28.1,23.5l-2.2,2.1V7.5c0-1.1-.9-2-2-2s-2,.9-2,2V25.6l-2.1-2.1c-.4-.4-.9-.7-1.5-.7s-.8,.1-1.1,.3c-.9,.6-1.2,1.9-.5,2.8,0,.1,.2,.2,.3,.3l5.6,5.5c.8,.8,2,.8,2.8,0l5.6-5.5c.8-.8,.8-2.1,0-2.8-.8-.8-2.1-.8-2.8,0h0Zm7.9-8.1h-6.2v4h6.2c.8,0,1.4,.6,1.4,1.4v16.3c0,.8-.6,1.4-1.4,1.4H12c-.8,0-1.4-.6-1.4-1.4V20.7c0-.8,.6-1.4,1.4-1.4h6.4v-4h-6.4c-3,0-5.4,2.4-5.4,5.4v16.3c0,3,2.4,5.4,5.4,5.4h24c3,0,5.4-2.4,5.4-5.4V20.7c0-3-2.4-5.4-5.4-5.4Z',
  ],
}

export const pushConfigurationIcon = {
  prefix: 'fal',
  iconName: 'push-config',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M21.41,10.59c-.78-.78-2.05-.78-2.83,0-.78.78-.78,2.05,0,2.83l10.59,10.59-10.59,10.59c-.78.78-.78,2.05,0,2.83s2.05.78,2.83,0l12-12c.38-.38.59-.88.59-1.41s-.21-1.04-.59-1.41l-12-12ZM21.41,22.59l-12-12c-.78-.78-2.05-.78-2.83,0-.78.78-.78,2.05,0,2.83l10.59,10.59-10.59,10.59c-.78.78-.78,2.05,0,2.83s2.05.78,2.83,0l12-12c.78-.78.78-2.05,0-2.83ZM39.98,10.03c-1.1,0-2,.9-2,2v24c0,1.1.9,2,2,2s2-.9,2-2V12.03c0-1.1-.9-2-2-2Z',
  ],
}

export const InventoryIcon = {
  prefix: 'fal',
  iconName: 'inventory',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M42,5.3H6c-2.2,0-4,1.8-4,4V31.5c0,2.2,1.8,4,4,4h11v3.7h-3c-1,0-1.8,.8-1.8,1.8s.8,1.8,1.8,1.8h20.1c1,0,1.8-.8,1.8-1.8s-.8-1.8-1.8-1.8h-3.2v-3.7h11.2c2.2,0,4-1.8,4-4V9.3c0-2.2-1.8-4-4-4Zm-14.7,33.8h-6.8v-3.7h6.8v3.7Zm15.1-7.7c0,.2-.2,.4-.4,.4H6c-.2,0-.4-.2-.4-.4V9.3c0-.2,.2-.4,.4-.4H42c.2,0,.4,.2,.4,.4V31.5Z',
  ],
}

export const InfrastructureIcon = {
  prefix: 'fal',
  iconName: 'infrastructure',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M30,34c-.4,0-.8,.1-1.1,.3-.3,.2-.6,.5-.7,.9-.2,.4-.2,.8-.1,1.2,0,.4,.3,.7,.5,1,.3,.3,.6,.5,1,.5,.4,0,.8,0,1.2-.1,.4-.2,.7-.4,.9-.7,.2-.3,.3-.7,.3-1.1s-.2-1-.6-1.4-.9-.6-1.4-.6Zm-12,0h-6c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4c.4,.4,.9,.6,1.4,.6h6c.5,0,1-.2,1.4-.6,.4-.4,.6-.9,.6-1.4s-.2-1-.6-1.4c-.4-.4-.9-.6-1.4-.6Zm18,0c-.4,0-.8,.1-1.1,.3-.3,.2-.6,.5-.7,.9-.2,.4-.2,.8-.1,1.2,0,.4,.3,.7,.5,1,.3,.3,.6,.5,1,.5,.4,0,.8,0,1.2-.1,.4-.2,.7-.4,.9-.7,.2-.3,.3-.7,.3-1.1s-.2-1-.6-1.4-.9-.6-1.4-.6Zm-6-12c-.4,0-.8,.1-1.1,.3-.3,.2-.6,.5-.7,.9-.2,.4-.2,.8-.1,1.2s.3,.7,.5,1c.3,.3,.6,.5,1,.5s.8,0,1.2-.1c.4-.2,.7-.4,.9-.7,.2-.3,.3-.7,.3-1.1s-.2-1-.6-1.4-.9-.6-1.4-.6Zm-12,0h-6c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4c.4,.4,.9,.6,1.4,.6h6c.5,0,1-.2,1.4-.6,.4-.4,.6-.9,.6-1.4s-.2-1-.6-1.4c-.4-.4-.9-.6-1.4-.6Zm18-12c-.4,0-.8,.1-1.1,.3-.3,.2-.6,.5-.7,.9-.2,.4-.2,.8-.1,1.2,0,.4,.3,.7,.5,1,.3,.3,.6,.5,1,.5,.4,0,.8,0,1.2-.1,.4-.2,.7-.4,.9-.7,.2-.3,.3-.7,.3-1.1s-.2-1-.6-1.4c-.4-.4-.9-.6-1.4-.6Zm0,12c-.4,0-.8,.1-1.1,.3-.3,.2-.6,.5-.7,.9-.2,.4-.2,.8-.1,1.2,0,.4,.3,.7,.5,1,.3,.3,.6,.5,1,.5,.4,0,.8,0,1.2-.1,.4-.2,.7-.4,.9-.7,.2-.3,.3-.7,.3-1.1s-.2-1-.6-1.4-.9-.6-1.4-.6Zm8-12c0-1.6-.6-3.1-1.8-4.2-1.1-1.1-2.7-1.8-4.2-1.8H10c-1.6,0-3.1,.6-4.2,1.8s-1.8,2.7-1.8,4.2v4c0,1.5,.6,2.9,1.6,4-1,1.1-1.6,2.5-1.6,4v4c0,1.5,.6,2.9,1.6,4-1,1.1-1.6,2.5-1.6,4v4c0,1.6,.6,3.1,1.8,4.2,1.1,1.1,2.7,1.8,4.2,1.8h28c1.6,0,3.1-.6,4.2-1.8,1.1-1.1,1.8-2.7,1.8-4.2v-4c0-1.5-.6-2.9-1.6-4,1-1.1,1.6-2.5,1.6-4v-4c0-1.5-.6-2.9-1.6-4,1-1.1,1.6-2.5,1.6-4v-4Zm-4,28c0,.5-.2,1-.6,1.4s-.9,.6-1.4,.6H10c-.5,0-1-.2-1.4-.6-.4-.4-.6-.9-.6-1.4v-4c0-.5,.2-1,.6-1.4,.4-.4,.9-.6,1.4-.6h28c.5,0,1,.2,1.4,.6s.6,.9,.6,1.4v4Zm0-12c0,.5-.2,1-.6,1.4s-.9,.6-1.4,.6H10c-.5,0-1-.2-1.4-.6-.4-.4-.6-.9-.6-1.4v-4c0-.5,.2-1,.6-1.4,.4-.4,.9-.6,1.4-.6h28c.5,0,1,.2,1.4,.6s.6,.9,.6,1.4v4Zm0-12c0,.5-.2,1-.6,1.4-.4,.4-.9,.6-1.4,.6H10c-.5,0-1-.2-1.4-.6-.4-.4-.6-.9-.6-1.4v-4c0-.5,.2-1,.6-1.4,.4-.4,.9-.6,1.4-.6h28c.5,0,1,.2,1.4,.6,.4,.4,.6,.9,.6,1.4v4Zm-10-4c-.4,0-.8,.1-1.1,.3-.3,.2-.6,.5-.7,.9-.2,.4-.2,.8-.1,1.2,0,.4,.3,.7,.5,1,.3,.3,.6,.5,1,.5,.4,0,.8,0,1.2-.1,.4-.2,.7-.4,.9-.7,.2-.3,.3-.7,.3-1.1s-.2-1-.6-1.4c-.4-.4-.9-.6-1.4-.6Zm-12,0h-6c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4c.4,.4,.9,.6,1.4,.6h6c.5,0,1-.2,1.4-.6,.4-.4,.6-.9,.6-1.4s-.2-1-.6-1.4c-.4-.4-.9-.6-1.4-.6Z',
  ],
}
export const Image = {
  prefix: 'fal',
  iconName: 'image',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M31,20c1.7,0,3-1.3,3-3s-1.3-3-3-3-3,1.3-3,3,1.3,3,3,3Zm7-14H10c-2.2,0-4,1.8-4,4v28c0,2.2,1.8,4,4,4h28c2.2,0,4-1.8,4-4V10c0-2.2-1.8-4-4-4Zm0,32H10v-7l8-6.4,8.8,7c.8,.6,1.9,.6,2.7-.1l2.6-2.6,6,6v3.2Zm0-8.8l-4.6-4.6c-.8-.8-2-.8-2.8,0l-2.7,2.7-8.6-6.9c-.7-.6-1.8-.6-2.5,0l-6.8,5.4V10h28V29.2Z`,
  ],
}

export const InfoCircle = {
  prefix: 'fal',
  iconName: 'info-circle',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24,22c-.6,0-1.1,.2-1.4,.6s-.6,.9-.6,1.4v8c0,.6,.2,1.1,.6,1.4s.9,.6,1.4,.6,1.1-.2,1.4-.6,.6-.9,.6-1.4v-8c0-.6-.2-1.1-.6-1.4s-.9-.6-1.4-.6Zm.8-7.8c-.5-.2-1.1-.2-1.5,0-.3,0-.5,.2-.7,.4s-.3,.4-.4,.7c0,.2-.2,.5-.2,.8s0,.6,.2,.8c0,.3,.3,.5,.5,.7s.4,.3,.7,.4c.3,0,.7,.2,1,0,.3,0,.7-.2,1-.3,.3-.2,.5-.4,.7-.8,.2-.3,.2-.6,.3-1,0-.6-.2-1.1-.6-1.4-.2-.2-.4-.3-.7-.4h-.2Zm17.8,2.3c-1.1-2.4-2.5-4.7-4.4-6.5-1.9-1.9-4.1-3.3-6.5-4.4s-5.1-1.5-7.6-1.5c-4,0-7.8,1.1-11.2,3.3s-5.8,5.3-7.4,9-1.9,7.7-1.1,11.6c.8,3.9,2.7,7.4,5.4,10.3,2.8,2.8,6.4,4.7,10.3,5.4,3.9,.8,7.9,.4,11.6-1.1,3.6-1.5,6.8-4.1,9-7.4,2.2-3.3,3.3-7.2,3.3-11.2s-.5-5.3-1.5-7.6h0Zm-7.2,19.1c-3.1,3.1-7.1,4.7-11.4,4.7s-6.3-1-8.9-2.7c-2.7-1.7-4.7-4.3-5.9-7.2-1.2-3-1.5-6.1-1-9.3,.6-3.2,2.1-6,4.4-8.2s5.1-3.7,8.2-4.4c3.2-.6,6.3-.3,9.3,1,3,1.2,5.4,3.2,7.2,5.9s2.7,5.7,2.7,8.9-1.7,8.3-4.7,11.4h0Z`,
  ],
}

export const Link = {
  prefix: 'fal',
  iconName: 'link',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M21.3,34.8c-.5,0-1,.1-1.4,.5l-3.6,3.5c-.9,.9-2.2,1.5-3.5,1.5s-2.6-.5-3.5-1.5c-.9-.9-1.5-2.2-1.5-3.5s.5-2.6,1.5-3.5l9.1-9.2c.9-.9,2.1-1.4,3.4-1.5,1.3,0,2.5,.4,3.4,1.3l.2,.2c.4,.4,.9,.6,1.4,.6,.5,0,1-.2,1.4-.6,.4-.4,.6-.9,.6-1.4,0-.5-.2-1-.6-1.4-.1-.1-.2-.3-.4-.4-1.7-1.5-3.9-2.3-6.2-2.2s-4.4,1-6,2.6L6.4,28.9c-1.6,1.7-2.4,4-2.4,6.3,0,2.3,1,4.5,2.6,6.2,1.6,1.6,3.9,2.6,6.2,2.6,2.3,0,4.6-.8,6.3-2.4l3.5-3.4c.3-.4,.5-.9,.5-1.4,0-.5-.2-1-.5-1.4-.3-.4-.8-.6-1.3-.6ZM41.4,6.6c-1.7-1.7-4-2.6-6.4-2.6s-4.7,.9-6.4,2.6l-3.5,3.4c-.3,.4-.5,.9-.5,1.4,0,.5,.2,1,.5,1.4s.8,.6,1.3,.6c.5,0,1-.1,1.4-.5l3.5-3.5c.9-.9,2.2-1.5,3.5-1.5s2.6,.5,3.5,1.5c.9,.9,1.5,2.2,1.5,3.5s-.5,2.6-1.5,3.5l-9.1,9.2c-.9,.9-2.1,1.4-3.4,1.5-1.3,0-2.5-.4-3.4-1.3l-.2-.2c-.4-.4-.9-.6-1.4-.6-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4,0,.5,.2,1,.6,1.4,.1,.1,.3,.3,.5,.4,1.7,1.5,3.9,2.3,6.2,2.2,2.3,0,4.4-1,6-2.6l9.2-9.2c1.7-1.7,2.7-4,2.7-6.4,0-2.4-.9-4.7-2.6-6.4Z`,
  ],
}

export const ListOl = {
  prefix: 'fal',
  iconName: 'list-ol',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M8.8,35.3l1.4-1.6c.3-.3,.4-.7,.4-1.1v-.3c0-.6-.3-.9-.9-.9H5.2c-.3,0-.6,.3-.6,.6v1.2c0,.3,.3,.6,.6,.6h1.8c-.3,.3-.6,.6-.9,1l-.4,.5c-.3,.4-.4,.8-.2,1.2v.2c.3,.5,.6,.6,1,.6h.4c.8,0,1.2,.2,1.2,.7s-.3,.6-1.1,.6c-.4,0-.8,0-1.2-.2-.5-.3-.9-.3-1.2,.2l-.4,.7c-.3,.5-.3,.9,.2,1.2,.6,.4,1.6,.7,2.9,.7,2.7,0,3.8-1.8,3.8-3.4,0-1.1-.7-2.3-2.2-2.7Zm33.9-.7H17.8c-.7,0-1.2,.6-1.2,1.2v1.2c0,.7,.6,1.2,1.2,1.2h25c.7,0,1.2-.6,1.2-1.2v-1.2c0-.7-.6-1.2-1.2-1.2Zm0-25H17.8c-.7,0-1.2,.6-1.2,1.2v1.2c0,.7,.6,1.2,1.2,1.2h25c.7,0,1.2-.6,1.2-1.2v-1.2c0-.7-.6-1.2-1.2-1.2Zm0,12.5H17.8c-.7,0-1.2,.6-1.2,1.2v1.2c0,.7,.6,1.2,1.2,1.2h25c.7,0,1.2-.6,1.2-1.2v-1.2c0-.7-.6-1.2-1.2-1.2ZM5.2,16.5h5c.3,0,.6-.3,.6-.6v-1.2c0-.3-.3-.6-.6-.6h-1.2V7.1c0-.3-.3-.6-.6-.6h-1.9c-.2,0-.5,.1-.6,.3l-.6,1.2c-.2,.3,0,.7,.3,.8,0,0,.2,0,.3,0h.6v5h-1.2c-.3,0-.6,.3-.6,.6v1.2c0,.3,.3,.6,.6,.6Zm-.3,12.5h5.3c.3,0,.6-.3,.6-.6v-1.2c0-.3-.3-.6-.6-.6h-3c.3-.8,3.8-1.5,3.8-4.4s-2-3.1-3.5-3.1-2.6,.8-3.2,1.5c-.3,.4-.2,.8,.2,1.2l.7,.5c.4,.4,.9,.2,1.3-.2,.2-.2,.5-.3,.7-.3,.3,0,.7,.1,.7,.7,0,1-4,1.7-4,5.4v.3c0,.6,.4,.9,.9,.9Z`,
  ],
}
export const LockAlt = {
  prefix: 'fal',
  iconName: 'lock-alt',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M38.3,19.7c-1.1-1.1-2.7-1.7-4.3-1.7v-4c0-2.7-1-5.2-3-7-1.9-1.9-4.4-3-7-3s-5.2,1-7,3c-1.9,1.9-3,4.4-3,7v4c-1.6,0-3.1,.7-4.3,1.7s-1.7,2.7-1.7,4.3v14c0,1.6,.7,3.1,1.7,4.3s2.7,1.7,4.3,1.7h20c1.6,0,3.1-.7,4.3-1.7s1.7-2.7,1.7-4.3v-14c0-1.6-.7-3.1-1.7-4.3Zm-20.3-5.7c0-1.6,.7-3.1,1.7-4.3s2.7-1.7,4.3-1.7,3.1,.7,4.3,1.7c1.1,1.1,1.7,2.7,1.7,4.3v4h-12v-4Zm18,24c0,.6-.2,1-.6,1.4-.4,.4-.9,.6-1.4,.6H14c-.6,0-1-.2-1.4-.6-.4-.4-.6-.9-.6-1.4v-14c0-.6,.2-1,.6-1.4s.9-.6,1.4-.6h20c.6,0,1,.2,1.4,.6s.6,.9,.6,1.4v14Zm-12-12c-.6,0-1,.2-1.4,.6s-.6,.9-.6,1.4v6c0,.6,.2,1,.6,1.4s.9,.6,1.4,.6,1-.2,1.4-.6c.4-.4,.6-.9,.6-1.4v-6c0-.6-.2-1-.6-1.4s-.9-.6-1.4-.6Z`,
  ],
}
export const LongArrowDown = {
  prefix: 'fal',
  iconName: 'long-arrow-down',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24,40c-.5,0-1-.2-1.4-.6l-12-12c-.8-.8-.8-2,0-2.8,.8-.8,2-.8,2.8,0l8.6,8.6V10c0-1.1,.9-2,2-2s2,.9,2,2v23.2l8.6-8.6c.8-.8,2-.8,2.8,0,.8,.8,.8,2,0,2.8l-12,12c-.4,.4-.9,.6-1.4,.6Z`,
  ],
}
export const LongArrowLeft = {
  prefix: 'fal',
  iconName: 'long-arrow-left',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M23.4,10.6c.8,.8,.8,2,0,2.8l-8.6,8.6h23.2c1.1,0,2,.9,2,2s-.9,2-2,2H14.8l8.6,8.6c.8,.8,.8,2,0,2.8-.8,.8-2,.8-2.8,0l-12-12c-.4-.4-.6-.9-.6-1.4s.2-1,.6-1.4l12-12c.8-.8,2-.8,2.8,0Z`,
  ],
}
export const LongArrowRight = {
  prefix: 'fal',
  iconName: 'long-arrow-right',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M40,24c0,.5-.2,1-.6,1.4l-12,12c-.8,.8-2,.8-2.8,0-.8-.8-.8-2,0-2.8l8.6-8.6H10c-1.1,0-2-.9-2-2s.9-2,2-2h23.2l-8.6-8.6c-.8-.8-.8-2,0-2.8,.8-.8,2-.8,2.8,0l12,12c.4,.4,.6,.9,.6,1.4Z`,
  ],
}

export const LongArrowUp = {
  prefix: 'fal',
  iconName: 'long-arrow-up',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24,8c.5,0,1,.2,1.4,.6l12,12c.8,.8,.8,2,0,2.8-.8,.8-2,.8-2.8,0l-8.6-8.6v23.2c0,1.1-.9,2-2,2s-2-.9-2-2V14.8l-8.6,8.6c-.8,.8-2,.8-2.8,0-.8-.8-.8-2,0-2.8l12-12c.4-.4,.9-.6,1.4-.6Z`,
  ],
}
export const LiveTrailIcon = {
  prefix: 'fal',
  iconName: 'live-tail',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M11.5,21.1h3.8c.5,0,1-.2,1.3-.5l.4-.3,2.8,3c.4,.4,.9,.6,1.4,.6s.1,0,.2,0c.6,0,1.2-.4,1.5-.9l2.7-4.5,1.6,1.9c.7,.8,1.8,.9,2.6,.3l1.9-1.4,1.2,1c.4,.3,.8,.5,1.3,.5h2.2c1.1,0,2-.9,2-2s-.9-2-2-2h-1.4l-1.8-1.6c-.7-.6-1.7-.6-2.4-.1l-1.7,1.2-2.2-2.6c-.4-.5-1-.7-1.7-.7-.6,0-1.2,.4-1.5,.9l-2.8,4.6-2.3-2.5c-.7-.8-2-.8-2.8-.1l-1.2,1.1h-3.1c-1.1,0-2,.9-2,2s.9,2,2,2ZM43,4H5c-1.1,0-2,.9-2,2V31.1c0,1.1,.9,2,2,2H22v3.1c-.7,.4-1.3,1-1.7,1.7H9.3c-1.1,0-2,.9-2,2s.9,2,2,2h11.1c.7,1.3,2.1,2.2,3.6,2.2s2.9-.9,3.6-2.2h11.1c1.1,0,2-.9,2-2s-.9-2-2-2h-11.1c-.4-.7-1-1.3-1.7-1.7v-3.1h17.1c1.1,0,2-.9,2-2V5.9c0-1.1-.9-2-2-2Zm-2,24.6H6.9V7.9H41.1V28.5Z',
  ],
}

export const LogLicenseIcon = {
  prefix: 'fal',
  iconName: 'log-license',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M24.6,33.4c-.6-.3-1.4-.3-2,0l-7.5,4.4,2.9-11.4c1.7,1,3.8,1.6,5.9,1.6s4.2-.6,5.9-1.6l3,11.6-8.3-4.6Zm-.6-25.4c4.4,0,8,3.6,8,8s-3.6,8-8,8-8-3.6-8-8,3.6-8,8-8Zm13.9,33.5l-4.6-18c1.7-2.1,2.7-4.7,2.7-7.5,0-6.6-5.4-12-12-12s-12,5.4-12,12,1,5.5,2.7,7.5l-4.6,18c-.2,.8,0,1.6,.7,2.1,.6,.5,1.5,.5,2.2,.1l10.7-6.3,11.4,6.3c.3,.2,.6,.2,1,.2s.9-.1,1.2-.4c.6-.5,.9-1.3,.7-2.1Zm-13.3-8.1c-.6-.3-1.4-.3-2,0l-7.5,4.4,2.9-11.4c1.7,1,3.8,1.6,5.9,1.6s4.2-.6,5.9-1.6l3,11.6-8.3-4.6Zm-.6-25.4c4.4,0,8,3.6,8,8s-3.6,8-8,8-8-3.6-8-8,3.6-8,8-8Zm13.9,33.5l-4.6-18c1.7-2.1,2.7-4.7,2.7-7.5,0-6.6-5.4-12-12-12s-12,5.4-12,12,1,5.5,2.7,7.5l-4.6,18c-.2,.8,0,1.6,.7,2.1,.6,.5,1.5,.5,2.2,.1l10.7-6.3,11.4,6.3c.3,.2,.6,.2,1,.2s.9-.1,1.2-.4c.6-.5,.9-1.3,.7-2.1Z',
  ],
}

export const LogSettingsIcon = {
  prefix: 'fal',
  iconName: 'log',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M41.4,16.1s0,0,0-.1c0,0,0-.1,0-.2,0,0,0,0,0-.1,0,0,0-.1,0-.2,0,0,0,0,0-.1,0,0,0,0,0-.1L29.5,2.6s0,0,0,0c0,0-.1-.1-.2-.1,0,0,0,0-.1,0,0,0-.1,0-.2-.1,0,0,0,0,0,0,0,0-.2,0-.3,0,0,0,0,0,0,0-.1,0-.2,0-.4,0H8.4c-1,0-1.8,.8-1.8,1.8V44.2c0,1,.8,1.8,1.8,1.8h31.2c1,0,1.8-.8,1.8-1.8V16.4c0-.1,0-.2,0-.3Zm-11.4-7.7l5.7,6.3h-5.7v-6.3Zm7.9,34.1H10.1V5.5H26.4v10.9c0,1,.8,1.8,1.8,1.8h9.6v24.2ZM15.5,27.9c0,1,.8,1.8,1.8,1.8h12c1,0,1.8-.8,1.8-1.8s-.8-1.8-1.8-1.8h-12c-1,0-1.8,.8-1.8,1.8Zm13.8,5.9h-12c-1,0-1.8,.8-1.8,1.8s.8,1.8,1.8,1.8h12c1,0,1.8-.8,1.8-1.8s-.8-1.8-1.8-1.8Z',
  ],
}

export const LineIcon = {
  prefix: 'fal',
  iconName: 'line',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M15,32c.8,0,1.6-.3,2.1-.9,.6-.6,.9-1.3,.9-2.1,0,0,0-.2,0-.3l5.6-5.6h.9l3.2,3.2v.2c0,.8,.3,1.6,.9,2.1s1.3,.9,2.1,.9,1.6-.3,2.1-.9c.6-.6,.9-1.3,.9-2.1v-.2l7.3-7.3c.6,0,1.2-.2,1.7-.5,.5-.3,.9-.8,1.1-1.3,.2-.5,.3-1.2,.2-1.7-.1-.6-.4-1.1-.8-1.5-.4-.4-1-.7-1.5-.8-.6-.1-1.2,0-1.7,.2-.5,.2-1,.6-1.3,1.1-.3,.5-.5,1.1-.5,1.7,0,0,0,.2,0,.3l-7.2,7.2h-.3l-3.5-3.5c0-.8-.3-1.6-.9-2.1-.6-.6-1.3-.9-2.1-.9s-1.6,.3-2.1,.9c-.6,.6-.9,1.3-.9,2.1l-6,6c-.8,0-1.6,.3-2.1,.9-.6,.6-.9,1.3-.9,2.1s.3,1.6,.9,2.1c.6,.6,1.3,.9,2.1,.9h0Zm27,8H8V6c0-.5-.2-1-.6-1.4-.4-.4-.9-.6-1.4-.6s-1,.2-1.4,.6c-.4,.4-.6,.9-.6,1.4V42c0,.5,.2,1,.6,1.4,.4,.4,.9,.6,1.4,.6H42c.5,0,1-.2,1.4-.6s.6-.9,.6-1.4-.2-1-.6-1.4-.9-.6-1.4-.6Z',
  ],
}

export const LinkTopologyIcon = {
  prefix: 'fal',
  iconName: 'topology-link',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M18.8,22.7h2c.2,0,.4-.2,.4-.4v-3.2c0-1.6,1.3-2.9,2.9-2.9s2.9,1.3,2.9,2.9v3.2c0,.2,.2,.4,.4,.4h2c.2,0,.4-.2,.4-.4v-3.2c0-3.1-2.5-5.6-5.6-5.6h0c-3.1,0-5.6,2.5-5.6,5.6v3.2c0,.2,.2,.4,.4,.4h-.2Zm-3-4.2l-2.1-2.1h-.2c-.1,0-.4,0-.5,.1-4.2,4.2-4.1,11.1,0,15.3,0,0,.2,.1,.3,.1s.2,0,.3-.1l2.1-2.1c.2-.2,.2-.4,0-.6-2.7-2.7-2.7-7.2,0-10,.2-.2,.2-.4,0-.6h.1Zm-5.2-3.6s.1-.2,.1-.3,0-.2-.1-.3l-2.1-2.1c-.1-.1-.4-.1-.6,0-6.5,6.5-6.5,17.2,0,23.7h.6l2.1-2.1c.2-.2,.2-.4,0-.6-5.1-5.1-5.1-13.3,0-18.4h0Zm21.7,3.7c-.2,.2-.2,.4,0,.6,2.7,2.8,2.7,7.3,0,10-.2,.2-.2,.4,0,.6l2.1,2.1c.1,.1,.2,.1,.3,.1s.3-.1,.3-.1c4.1-4.2,4.2-11.1,0-15.3-.1-.1-.4-.1-.5-.1h-.2l-2.1,2.1h.1Zm5.1-3.7h0c5.1,5.1,5.1,13.3,0,18.4-.2,.2-.2,.4,0,.6l2.1,2.1h.6c6.5-6.5,6.5-17.2,0-23.7-.2-.1-.5-.1-.6,0l-2.1,2.1c-.1,.1-.1,.2-.1,.3s.1,.3,.1,.3h0Zm-14.4,13.8h2.2v-8.4h-2.2v8.4Zm6.3-2.7h-2c-.2,0-.4,.2-.4,.4v3.2c0,1.6-1.3,2.9-2.9,2.9s-2.9-1.3-2.9-2.9v-3.2c0-.2-.2-.4-.4-.4h-2c-.2,0-.4,.2-.4,.4v3.2c0,3.1,2.5,5.6,5.6,5.6s5.6-2.5,5.7-5.6v-3.2c0-.2-.2-.4-.4-.4h0Z`,
  ],
}

export const LogScreenIcon = {
  prefix: 'fal',
  iconName: 'log',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M41.4,16.1s0,0,0-.1c0,0,0-.1,0-.2,0,0,0,0,0-.1,0,0,0-.1,0-.2,0,0,0,0,0-.1,0,0,0,0,0-.1L29.5,2.6s0,0,0,0c0,0-.1-.1-.2-.1,0,0,0,0-.1,0,0,0-.1,0-.2-.1,0,0,0,0,0,0,0,0-.2,0-.3,0,0,0,0,0,0,0-.1,0-.2,0-.4,0H8.4c-1,0-1.8,.8-1.8,1.8V44.2c0,1,.8,1.8,1.8,1.8h31.2c1,0,1.8-.8,1.8-1.8V16.4c0-.1,0-.2,0-.3Zm-11.4-7.7l5.7,6.3h-5.7v-6.3Zm7.9,34.1H10.1V5.5H26.4v10.9c0,1,.8,1.8,1.8,1.8h9.6v24.2ZM15.5,27.9c0,1,.8,1.8,1.8,1.8h12c1,0,1.8-.8,1.8-1.8s-.8-1.8-1.8-1.8h-12c-1,0-1.8,.8-1.8,1.8Zm13.8,5.9h-12c-1,0-1.8,.8-1.8,1.8s.8,1.8,1.8,1.8h12c1,0,1.8-.8,1.8-1.8s-.8-1.8-1.8-1.8Z',
  ],
}
export const MergeChartIcon = {
  prefix: 'fal',
  iconName: 'merge-chart',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M17.8,7.2s0,0,0,0h2.5c.5,0,1-.2,1.3-.6,.4-.4,.5-.9,.5-1.4,0-1-.8-1.8-1.9-1.9h-2.5c-1,0-1.9,.8-2,1.9,0,.5,.2,1,.5,1.4,.4,.4,.8,.6,1.3,.6Zm8,0s0,0,0,0c.5,0,.9,.4,1.1,.8,.3,.7,1,1.2,1.8,1.2s.5,0,.7-.1c1-.4,1.4-1.5,1-2.5-.8-1.9-2.6-3.1-4.7-3.2-1.1,0-1.9,.9-1.9,1.9,0,1,.8,1.9,1.8,1.9Zm3.2,4.4h0c-1.1,0-1.9,.9-1.9,1.9v.9c0,1.1,.9,1.9,1.9,1.9s1.9-.9,1.9-1.9v-.9c0-1.1-.9-1.9-1.9-1.9ZM9.4,7.3c.1,0,.2,0,.3,0h0c0,0,.2,0,.3,0h1.6c1.1,0,1.9-.9,1.9-1.9h0c0-1.1-.9-1.9-1.9-1.9h-1.6c-.3,0-.7,0-1,.1-.5,.1-.9,.4-1.2,.8s-.4,.9-.3,1.4c.2,.9,1,1.5,1.9,1.5Zm-4,6.7h0c1.1,0,1.9-.9,1.9-1.9v-2.2c0-.5-.2-1-.5-1.4-.4-.4-.8-.6-1.3-.6-.5,0-1,.2-1.4,.5-.4,.4-.6,.8-.6,1.4v2.2c0,1,.9,1.9,1.9,1.9Zm34.2,3.1s0,0,0,0h-10.9c-1.1,0-1.9,.9-1.9,1.9s.9,1.9,1.9,1.9h10.9c.7,0,1.3,.6,1.3,1.3v17.2c0,.7-.6,1.3-1.3,1.3H22.3c-.7,0-1.3-.6-1.3-1.3v-15.8l8.6,8.5c.4,.3,.9,.5,1.4,.5,.5,0,1-.2,1.3-.6,.7-.7,.7-1.9,0-2.6l-10.7-10.6c-.1-.2-.3-.3-.4-.4l-3.8-3.7h3.2c.5,0,1-.2,1.4-.5,.4-.4,.6-.8,.6-1.3,0-.5-.2-1-.5-1.4-.4-.4-.8-.6-1.4-.6h-7.8c-1,0-1.9,.9-1.9,1.9v7.6c0,.5,.2,1,.5,1.4,.4,.4,.8,.6,1.3,.6,.5,0,1-.2,1.4-.5,.4-.4,.6-.8,.6-1.4v-2.9l2.8,2.8c-.2,.6-.4,1.3-.4,1.9v4.9h-2.2c-1.1,0-1.9,.9-1.9,1.9,0,1.1,.9,1.9,1.9,1.9h2.2v8.6c0,2.8,2.3,5.1,5.1,5.1h17.2c2.8,0,5.1-2.3,5.1-5.1V22.2c0-2.8-2.3-5.1-5.1-5.1ZM8.7,27.1h-.2c-.5,0-.9-.3-1.2-.7-.2-.5-.6-.8-1.1-1-.5-.2-1-.1-1.5,0-.5,.2-.8,.6-1,1.1-.2,.5-.1,1,.1,1.5,.9,1.7,2.6,2.8,4.6,2.8h.2c1.1,0,1.9-.9,1.9-1.9,0-1.1-.9-1.9-1.9-1.9Zm-3.4-4.1h0c1.1,0,1.9-.9,1.9-1.9v-2.7c0-1.1-.8-1.9-1.9-2-.5,0-1,.2-1.4,.5-.4,.4-.6,.8-.6,1.4v2.7c0,1,.9,1.9,1.9,1.9Z`,
  ],
}

export const MetricDetailIcon = {
  prefix: 'fal',
  iconName: 'metric-detail',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M25.1,4.7h-.2c-8.6,0-16.1,5.5-18.6,13.8,0,.2,0,.3,0,.4,0,.1,.2,.2,.4,.2h3.2c.2,0,.4-.1,.5-.3,2.8-7.7,11.1-11.9,18.9-9.5,3.9,1.2,7.2,3.8,9.1,7.5s2.4,7.8,1.2,11.7c-2,6.6-7.9,11-14.8,11-6.5,0-12.2-4-14.5-10.1,0-.2-.3-.3-.5-.3h-3.2c-.2,0-.3,0-.4,.2,0,.1-.1,.3,0,.4,2.6,8.3,10.3,13.7,18.6,13.7s3.3-.2,5-.6c8.6-2.2,14.6-10,14.6-18.9,0-10.6-8.7-19.2-19.4-19.1Zm-8,24.4c-.4,.3-.6,.8-.6,1.3,0,.5,.1,1,.5,1.4,0,0,.1,.1,.2,.2,.4,.4,.9,.6,1.4,.6s1-.2,1.4-.6l6.3-6.5c.7-.8,.7-2,0-2.8l-6.3-6.1c-.4-.4-.9-.6-1.4-.6h0c-.5,0-1,.2-1.4,.6s-.6,.9-.6,1.4c0,.5,.2,1,.6,1.4l2.7,2.7H5.5c-1.1,0-2,.8-2,1.9,0,.5,.2,1,.5,1.4s.9,.6,1.5,.6h14.7l-3.1,3.1Zm13.3-7.5h0c0,0,.1,0,.2,0s.1,0,.1,0h4.4c1.1,0,2-.9,2-2s-.9-2-2-2h-4.4c-1.1,0-1.9,.8-2,1.9-.1,1,.6,1.9,1.6,2Zm-1.2,7.9c.3,.4,.7,.6,1.2,.7,0,0,0,0,0,0,0,0,.1,0,.2,0s.1,0,.1,0h4.4c1.1,0,2-.9,2-2s-.9-2-2-2h-4.4c-1.1,0-2,.8-2,1.9,0,.5,0,1,.4,1.3Z`,
  ],
}
export const MapMarker = {
  prefix: 'fal',
  iconName: 'map-marker',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24,4c-4.2,0-8.3,1.7-11.3,4.7-3,3-4.7,7.1-4.7,11.3,0,10.8,14.1,23,14.7,23.5,.4,.3,.8,.5,1.3,.5s.9-.2,1.3-.5c.7-.5,14.7-12.7,14.7-23.5s-1.7-8.3-4.7-11.3c-3-3-7.1-4.7-11.3-4.7Zm0,35.3c-4.3-4-12-12.6-12-19.3s1.3-6.2,3.5-8.5,5.3-3.5,8.5-3.5,6.2,1.3,8.5,3.5c2.3,2.3,3.5,5.3,3.5,8.5,0,6.7-7.7,15.3-12,19.3Zm0-27.3c-1.6,0-3.1,.5-4.4,1.3-1.3,.9-2.3,2.1-2.9,3.6-.6,1.5-.8,3.1-.5,4.6,.3,1.6,1.1,3,2.2,4.1,1.1,1.1,2.5,1.9,4.1,2.2,1.6,.3,3.2,.2,4.6-.5,1.5-.6,2.7-1.6,3.6-2.9,.9-1.3,1.3-2.9,1.3-4.4s-.8-4.2-2.3-5.7c-1.5-1.5-3.5-2.3-5.7-2.3Zm0,12c-.8,0-1.6-.2-2.2-.7-.7-.4-1.2-1.1-1.5-1.8-.3-.7-.4-1.5-.2-2.3,.2-.8,.5-1.5,1.1-2,.6-.6,1.3-.9,2-1.1,.8-.2,1.6,0,2.3,.2,.7,.3,1.4,.8,1.8,1.5,.4,.7,.7,1.4,.7,2.2s-.4,2.1-1.2,2.8-1.8,1.2-2.8,1.2Z`,
  ],
}

export const Minus = {
  prefix: 'fal',
  iconName: 'minus',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M39.4,22.6c-.4-.4-.9-.6-1.4-.6H10c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4c.4,.4,.9,.6,1.4,.6h28c.5,0,1-.2,1.4-.6s.6-.9,.6-1.4-.2-1-.6-1.4Z`,
  ],
}

export const ManageRpeIcon = {
  prefix: 'fal',
  iconName: 'manage-rpe',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M13.6,11h0c-1,0-1.8,.8-1.8,1.8s.8,1.8,1.8,1.8,1.8-.8,1.8-1.8-.8-1.8-1.8-1.8Zm7.3,0h0c-1,0-1.8,.8-1.8,1.8s.8,1.8,1.8,1.8,1.8-.8,1.8-1.8-.8-1.8-1.8-1.8Zm0,25.5c1,0,1.8-.8,1.8-1.8s-.8-1.8-1.8-1.8h0c-1,0-1.8,.8-1.8,1.8s.8,1.8,1.8,1.8Zm18.2-14.5c3,0,5.5-2.4,5.5-5.5v-7.3c0-3-2.4-5.5-5.5-5.5H9.9c-3,0-5.5,2.4-5.5,5.5v7.3c0,3,2.4,5.5,5.5,5.5h1.6v3.6h-1.6c-3,0-5.5,2.4-5.5,5.5v7.3c0,3,2.4,5.5,5.5,5.5h29.1c3,0,5.5-2.4,5.5-5.5v-7.3c0-3-2.4-5.5-5.5-5.5h-1.6v-3.6h1.6Zm-29.1-3.6c-1,0-1.8-.8-1.8-1.8v-7.3c0-1,.8-1.8,1.8-1.8H30.3v10.9H9.9Zm20.3,21.8H9.9c-1,0-1.8-.8-1.8-1.8v-7.3c0-1,.8-1.8,1.8-1.8H30.3v10.9Zm3.5-14.5H15.2v-3.6h18.6v3.6Zm5.2,3.6c1,0,1.8,.8,1.8,1.8v7.3c0,1-.8,1.8-1.8,1.8h-5.1v-10.9h5.1Zm-5.1-10.9V7.3h5.1c1,0,1.8,.8,1.8,1.8v7.3c0,1-.8,1.8-1.8,1.8h-5.1ZM13.6,36.4c1,0,1.8-.8,1.8-1.8s-.8-1.8-1.8-1.8h0c-1,0-1.8,.8-1.8,1.8s.8,1.8,1.8,1.8Z`,
  ],
}

export const MetricCollectionTimeIcon = {
  prefix: 'fal',
  iconName: 'metric-collection-time',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M32,22h-6v-8c0-.5-.2-1-.6-1.4-.4-.4-.9-.6-1.4-.6s-1,.2-1.4,.6c-.4,.4-.6,.9-.6,1.4v10c0,.5,.2,1,.6,1.4s.9,.6,1.4,.6h8c.5,0,1-.2,1.4-.6s.6-.9,.6-1.4-.2-1-.6-1.4-.9-.6-1.4-.6ZM24,4c-4,0-7.8,1.2-11.1,3.4-3.3,2.2-5.9,5.3-7.4,9-1.5,3.7-1.9,7.7-1.1,11.6,.8,3.9,2.7,7.4,5.5,10.2,2.8,2.8,6.4,4.7,10.2,5.5,3.9,.8,7.9,.4,11.6-1.1,3.7-1.5,6.8-4.1,9-7.4,2.2-3.3,3.4-7.2,3.4-11.1s-.5-5.2-1.5-7.7c-1-2.4-2.5-4.6-4.3-6.5-1.9-1.9-4.1-3.3-6.5-4.3-2.4-1-5-1.5-7.7-1.5Zm0,36c-3.2,0-6.3-.9-8.9-2.7-2.6-1.8-4.7-4.3-5.9-7.2-1.2-2.9-1.5-6.1-.9-9.2,.6-3.1,2.1-6,4.4-8.2,2.2-2.2,5.1-3.8,8.2-4.4,3.1-.6,6.3-.3,9.2,.9,2.9,1.2,5.4,3.3,7.2,5.9,1.8,2.6,2.7,5.7,2.7,8.9s-1.7,8.3-4.7,11.3c-3,3-7.1,4.7-11.3,4.7Z`,
  ],
}

export const MetricExplorerIcon = {
  prefix: 'fal',
  iconName: 'metric-explorer',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M7.1,27.2l3.3,1.1-1.5,3.2c-1.9,.3-3.3,2.1-2.9,4.2s1.3,2.3,2.6,2.6c2.3,.5,4.3-1.2,4.3-3.4s-.3-1.5-.7-2.1l1.6-3.5,7.9,2.6c.2,0,.4,0,.5,0,.7,0,1.3-.4,1.6-1.1l3-6.8,5.8,2.7c.4,1.8,2.2,3,4.2,2.5s2.4-1.4,2.6-2.7-.2-2.5-1-3.2l1.5-6.6,2.9-.2c.8,0,1.5-.6,1.6-1.3,.3-1.2-.7-2.3-1.9-2.2l-1.9,.2,.7-3.2c1.5-.6,2.6-2.2,2.2-4s-1.3-2.4-2.6-2.7c-2.3-.5-4.3,1.2-4.3,3.4s.5,2,1.3,2.7l-.9,4.1-8.6,.7c-.6,0-1.2,.5-1.5,1l-1.9,4.2-5-2.3c-.5-1.8-2.4-3.1-4.5-2.5s-1.9,1.1-2.2,2.1-.1,2.6,.7,3.5l-2.2,4.7-3.8-1.2c-1.1-.4-2.3,.4-2.3,1.7s.6,1.4,1.3,1.7Zm22.7-9.6l6.5-.5-1.2,5.5c-.5,.2-.9,.4-1.3,.8l-5.4-2.5,1.5-3.3Zm-12.3,3.8c.6-.1,1.2-.4,1.6-.9l4.5,2.1-2.4,5.4-6-1.9,2.2-4.7Zm26.7,19.9H3.8c-.8,0-1.5,.5-1.8,1.2-.4,1.2,.5,2.3,1.7,2.3H44.2c.8,0,1.5-.5,1.8-1.2,.4-1.2-.5-2.3-1.7-2.3Z`,
  ],
}
export const MonitorIcon = {
  prefix: 'fal',
  iconName: 'monitor',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M4.6,16.6H43.4c1,0,1.8-.8,1.8-1.8V3.5c0-1-.8-1.8-1.8-1.8H4.6c-1,0-1.8,.8-1.8,1.8V14.9c0,1,.8,1.8,1.8,1.8ZM37.9,7.9h-1.2l-2.1-1.3c-.4-.3-1-.3-1.4,0l-2.4,1.3-2.7-2.4s0,0,0,0h13.6v7.9H23.2c.4,0,.7-.2,.9-.5l3.2-4.2,2.4,2.1c.4,.4,1.1,.5,1.6,.2l2.5-1.3,1.7,1.1c.2,.1,.5,.2,.8,.2h1.7c.8,0,1.4-.6,1.4-1.4s-.6-1.4-1.4-1.4ZM6.4,5.3H26.3c0,0-.2,.2-.3,.3l-3.3,4.2-3.1-2.6c-.5-.4-1.2-.4-1.7,0l-1.5,1h-5.8c-.8,0-1.4,.6-1.4,1.4s.6,1.4,1.4,1.4h6.2c.3,0,.6,0,.8-.2l.9-.6,3.4,2.9c.2,.2,.5,.3,.8,.3H6.4V5.3ZM44,42.7h-13.8v-4.3c0-1-.8-1.8-1.8-1.8h-2.7v-2.7h17.6c1,0,1.8-.8,1.8-1.8v-11.4c0-1-.8-1.8-1.8-1.8H4.6c-1,0-1.8,.8-1.8,1.8v11.4c0,1,.8,1.8,1.8,1.8H22.2v2.7h-2.7c-1,0-1.8,.8-1.8,1.8v4.3H4c-1,0-1.8,.8-1.8,1.8s.8,1.8,1.8,1.8H44c1,0,1.8-.8,1.8-1.8s-.8-1.8-1.8-1.8Zm-6.1-17.5h-1.2l-2.1-1.3c-.4-.3-1-.3-1.4,0l-2.4,1.3-2.7-2.4s0,0,0,0h13.6v7.9H23.2c.4,0,.7-.2,.9-.5l3.2-4.2,2.4,2.1c.4,.4,1.1,.5,1.6,.2l2.5-1.3,1.7,1.1c.2,.1,.5,.2,.8,.2h1.7c.8,0,1.4-.6,1.4-1.4s-.6-1.4-1.4-1.4ZM6.4,30.5v-7.9H26.3c0,0-.2,.2-.3,.3l-3.3,4.2-3.1-2.6c-.5-.4-1.2-.4-1.7,0l-1.5,1h-5.8c-.8,0-1.4,.6-1.4,1.4s.6,1.4,1.4,1.4h6.2c.3,0,.6,0,.8-.2l.9-.6,3.4,2.9c.2,.2,.5,.3,.8,.3H6.4Zm20.4,12.3h-5.5v-2.5h5.5v2.5Z`,
  ],
}
export const MonitorDisableIcon = {
  prefix: 'fal',
  iconName: 'monitor-disable',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24,4C13,4,4,13,4,24s9,20,20,20,20-9,20-20S35,4,24,4Zm0,36c-8.8,0-16-7.2-16-16s1.3-7.1,3.4-9.8l22.5,22.5c-2.7,2.1-6.1,3.4-9.8,3.4Zm12.6-6.2L14.2,11.4c2.7-2.1,6.1-3.4,9.8-3.4,8.8,0,16,7.2,16,16s-1.3,7.1-3.4,9.8Z`,
  ],
}

export const MonitorEnableIcon = {
  prefix: 'fal',
  iconName: 'monitor-enable',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M15.8,20.2l-2.8,2.8,9,9L42,12l-2.8-2.8L22,26.4l-6.2-6.2Zm24.2,3.8c0,8.8-7.2,16-16,16S8,32.8,8,24,15.1,8,24,8h0c1.5,0,3,.2,4.4,.6l3.2-3.2c-2.4-.9-5-1.3-7.6-1.4C13,4,4,13,4,24s9,20,20,20,20-9,20-20h-4Z`,
  ],
}
export const MyAccountIcon = {
  prefix: 'fal',
  iconName: 'my-account',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M15.3,31.4c1,0,1.8-.8,1.8-1.7,0-3.8,3.1-6.8,6.9-6.9,0,0,0,0,0,0s0,0,0,0c3.8,0,6.8,3.1,6.9,6.9,0,1,.8,1.7,1.8,1.7h0c1,0,1.7-.8,1.7-1.8,0-3.9-2.2-7.3-5.4-9.1,1.1-1.2,1.8-2.9,1.8-4.7,0-3.8-3.1-6.9-6.9-6.9s-6.9,3.1-6.9,6.9,.7,3.4,1.8,4.7c-3.2,1.8-5.4,5.2-5.4,9.1,0,1,.8,1.8,1.7,1.8ZM24,12.6c1.9,0,3.4,1.5,3.4,3.4s-1.5,3.3-3.3,3.4c0,0,0,0,0,0s0,0,0,0c-1.8,0-3.3-1.5-3.3-3.4s1.5-3.4,3.4-3.4ZM38.3,2.2H9.7c-2.7,0-4.9,2.2-4.9,4.9V40.9c0,2.7,2.2,4.9,4.9,4.9h28.6c2.7,0,4.9-2.2,4.9-4.9V7.1c0-2.7-2.2-4.9-4.9-4.9Zm1.4,38.6c0,.8-.6,1.4-1.4,1.4H9.7c-.8,0-1.4-.6-1.4-1.4V7.1c0-.8,.6-1.4,1.4-1.4h28.6c.8,0,1.4,.6,1.4,1.4V40.9Zm-5.7-6.6H14c-1,0-1.8,.8-1.8,1.8s.8,1.8,1.8,1.8h20.1c1,0,1.8-.8,1.8-1.8s-.8-1.8-1.8-1.8Z`,
  ],
}

export const MyProfileIcon = {
  prefix: 'fal',
  iconName: 'my-profile',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24,27.4c6.5,0,11.7-5.2,11.7-11.7s-5.2-11.7-11.7-11.7-11.7,5.2-11.7,11.7,5.2,11.7,11.7,11.7Zm0-19.5c4.3,0,7.8,3.5,7.8,7.8s-3.5,7.8-7.8,7.8-7.8-3.5-7.8-7.8,3.5-7.8,7.8-7.8Zm7.8,23.4h-15.6c-5.4,0-9.7,4.4-9.7,9.7s.8,1.9,1.9,1.9,1.9-.8,1.9-1.9c0-3.2,2.6-5.8,5.8-5.8h15.6c3.2,0,5.8,2.6,5.8,5.8s.8,1.9,1.9,1.9,1.9-.8,1.9-1.9c0-5.4-4.4-9.7-9.7-9.7Z`,
  ],
}
export const NetworkDiscoveryIcon = {
  prefix: 'fal',
  iconName: 'network-discovery',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M40.3,32.7c-.5,0-.9,0-1.4,.2l-1.1-1.7c1.4-1.4,2.3-3.3,2.3-5.4,0-3.6-2.5-6.7-5.9-7.5v-3.2c2.7-.7,4.8-3.2,4.8-6.1s-2.8-6.3-6.3-6.3-6.3,2.8-6.3,6.3,1.9,5.2,4.4,6v3.3c-1.1,.3-2.1,.7-2.9,1.4L15.7,9.8c.2-.5,.3-1.1,.3-1.7,0-3-2.5-5.5-5.5-5.5s-5.5,2.5-5.5,5.5,2.5,5.5,5.5,5.5,2.2-.4,3.1-1l11.9,9.8c-.2,.4-.3,.8-.5,1.2l-11,2.6c-1.1-1.8-3.1-3-5.4-3-3.5,0-6.3,2.8-6.3,6.3s2.8,6.3,6.3,6.3,6.3-2.8,6.3-6.3l9.9-2.3c.2,1.2,.7,2.2,1.4,3.1l-4,4.5c-.6-.2-1.2-.4-1.9-.4-3,0-5.5,2.5-5.5,5.5s2.5,5.5,5.5,5.5,5.5-2.5,5.5-5.5-.3-2.1-.9-2.9l4-4.5c1,.5,2.2,.8,3.4,.8s1.7-.1,2.4-.4l1.2,1.8c-.8,.9-1.2,2.1-1.2,3.4,0,3,2.5,5.5,5.5,5.5s5.5-2.5,5.5-5.5-2.5-5.5-5.5-5.5ZM10.5,10c-1.1,0-2-.9-2-2s.9-2,2-2,2,.9,2,2-.9,2-2,2Zm-1.9,22.3c-1.6,0-2.8-1.3-2.8-2.8s1.3-2.8,2.8-2.8,2.8,1.3,2.8,2.8-1.3,2.8-2.8,2.8Zm11.7,9.7c-1.1,0-2-.9-2-2s.9-2,2-2,2,.9,2,2-.9,2-2,2ZM29.7,8.9c0-1.6,1.3-2.8,2.8-2.8s2.8,1.3,2.8,2.8-1.3,2.8-2.8,2.8-2.8-1.3-2.8-2.8Zm2.7,21c-2.3,0-4.2-1.9-4.2-4.2s1.9-4.2,4.2-4.2,4.2,1.9,4.2,4.2-1.9,4.2-4.2,4.2Zm7.9,10.3c-1.1,0-2-.9-2-2s.9-2,2-2,2,.9,2,2-.9,2-2,2Z`,
  ],
}
export const NextHopIcon = {
  prefix: 'fal',
  iconName: 'next-hop',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M40.4,24.4h-7.3c-2.1,0-3.8,1.7-3.8,3.8v7.3c0,2.1,1.7,3.8,3.8,3.8h7.3c2.1,0,3.8-1.7,3.8-3.8v-7.3c0-2.1-1.7-3.8-3.8-3.8Zm-.2,10.9h-6.9v-6.9h6.9v6.9ZM14.9,24.4H7.6c-2.1,0-3.8,1.7-3.8,3.8v7.3c0,2.1,1.7,3.8,3.8,3.8h7.3c2.1,0,3.8-1.7,3.8-3.8v-7.3c0-2.1-1.7-3.8-3.8-3.8Zm-.2,10.9H7.8v-6.9h6.9v6.9Zm23-14.5h0c.5,0,1-.2,1.4-.6,.4-.4,.6-.9,.6-1.4v-7.3c0-.5-.1-1-.5-1.4-.4-.4-.9-.6-1.4-.6h0c-1.1,0-2,.9-2,2v2.5c-7.1-6.9-18.5-7-25.7,0-1.1,1.1-2.1,2.4-2.9,3.7-.3,.5-.3,1-.2,1.5,.1,.5,.5,.9,1,1.2,.9,.5,2.1,.2,2.6-.7,4.1-6.8,12.9-9,19.7-5,.9,.6,1.8,1.2,2.6,2h-2.5c-1.1,0-2,.9-2,2,0,.5,.2,1,.6,1.4,.4,.4,.9,.6,1.4,.6h7.3Z',
  ],
}

export const NetworkTopologyIcon = {
  prefix: 'fal',
  iconName: 'network-topology',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M42,17.33h-11.33v-2.23c0-.18.03-.35.1-.51s.17-.31.29-.43l1.89-1.89c.85.41,1.83.5,2.74.27.92-.24,1.72-.79,2.27-1.57.55-.77.8-1.72.72-2.66-.08-.94-.49-1.83-1.16-2.5-.67-.67-1.56-1.08-2.5-1.16-.94-.08-1.89.18-2.66.72-.77.55-1.33,1.35-1.57,2.27-.24.92-.14,1.89.27,2.74l-1.89,1.89c-.37.37-.67.81-.87,1.3-.2.49-.3,1.01-.3,1.53v2.23h-2.67v-8.9c.89-.31,1.64-.93,2.12-1.75.48-.81.65-1.77.49-2.7s-.64-1.77-1.36-2.38c-.72-.61-1.63-.94-2.58-.94s-1.86.33-2.58.94c-.72.61-1.2,1.45-1.36,2.38-.16.93.01,1.89.49,2.7.48.81,1.23,1.43,2.12,1.75v8.9h-2.67v-2.23c0-.53-.1-1.05-.3-1.53-.2-.49-.5-.93-.87-1.3l-1.89-1.89c.41-.85.5-1.83.27-2.74-.24-.92-.79-1.72-1.57-2.27-.77-.55-1.72-.8-2.66-.72-.94.08-1.83.49-2.5,1.16-.67.67-1.08,1.56-1.16,2.5-.08.94.18,1.89.72,2.66.55.77,1.35,1.33,2.27,1.57.92.24,1.89.14,2.74-.27l1.89,1.89c.12.12.22.27.29.43.07.16.1.34.1.51v2.23H6c-1.06,0-2.08.42-2.83,1.17-.75.75-1.17,1.77-1.17,2.83v5.33c0,1.06.42,2.08,1.17,2.83.75.75,1.77,1.17,2.83,1.17h11.33v2.23c0,.18-.03.35-.1.51-.07.16-.17.31-.29.43l-1.89,1.89c-.85-.41-1.83-.5-2.74-.27-.92.24-1.72.79-2.27,1.57-.55.77-.8,1.72-.72,2.66.08.94.49,1.83,1.16,2.5.67.67,1.56,1.08,2.5,1.16.94.08,1.89-.18,2.66-.72.77-.55,1.33-1.35,1.57-2.27.24-.92.14-1.89-.27-2.74l1.89-1.89c.37-.37.67-.81.87-1.3s.3-1.01.3-1.53v-2.23h2.67v8.9c-.89.31-1.64.93-2.12,1.75-.48.81-.65,1.77-.49,2.7.16.93.64,1.77,1.36,2.38.72.61,1.63.94,2.58.94s1.86-.33,2.58-.94c.72-.61,1.2-1.45,1.36-2.38.16-.93-.01-1.89-.49-2.7-.48-.81-1.23-1.43-2.12-1.75v-8.9h2.67v2.23c0,.53.1,1.05.3,1.53.2.49.5.93.87,1.3l1.89,1.89c-.41.85-.5,1.83-.27,2.74.24.92.79,1.72,1.57,2.27.77.55,1.72.8,2.66.72.94-.08,1.83-.49,2.5-1.16.67-.67,1.08-1.56,1.16-2.5.08-.94-.18-1.89-.72-2.66-.55-.77-1.35-1.33-2.27-1.57-.92-.24-1.89-.14-2.74.27l-1.89-1.89c-.12-.12-.22-.27-.29-.43-.07-.16-.1-.34-.1-.51v-2.23h11.33c1.06,0,2.08-.42,2.83-1.17.75-.75,1.17-1.77,1.17-2.83v-5.33c0-1.06-.42-2.08-1.17-2.83-.75-.75-1.77-1.17-2.83-1.17ZM34.67,7.33c.26,0,.52.08.74.22.22.15.39.35.49.6.1.24.13.51.08.77-.05.26-.18.5-.36.68-.19.19-.42.31-.68.37-.26.05-.53.03-.77-.08-.24-.1-.45-.27-.6-.49-.15-.22-.22-.48-.22-.74,0-.35.14-.69.39-.94.25-.25.59-.39.94-.39ZM13.33,10c-.26,0-.52-.08-.74-.22-.22-.15-.39-.35-.49-.6-.1-.24-.13-.51-.08-.77.05-.26.18-.5.37-.68.19-.19.42-.31.68-.37.26-.05.53-.02.77.08.24.1.45.27.6.49.15.22.22.48.22.74,0,.35-.14.69-.39.94-.25.25-.59.39-.94.39ZM13.33,40.66c-.26,0-.52-.08-.74-.22-.22-.15-.39-.35-.49-.6-.1-.24-.13-.51-.08-.77.05-.26.18-.5.37-.68.19-.19.43-.31.68-.36.26-.05.53-.02.77.08.24.1.45.27.6.49s.22.48.22.74c0,.35-.14.69-.39.94-.25.25-.59.39-.94.39h0ZM34.67,38c.26,0,.52.08.74.22.22.15.39.35.49.6.1.24.13.51.08.77-.05.26-.18.5-.37.68-.19.19-.42.31-.68.37-.26.05-.53.02-.77-.08-.24-.1-.45-.27-.6-.49-.15-.22-.22-.48-.22-.74,0-.35.14-.69.39-.94s.59-.39.94-.39ZM24,3.33c.26,0,.52.08.74.22.22.15.39.35.49.6.1.24.13.51.08.77-.05.26-.18.5-.36.68-.19.19-.42.31-.68.36-.26.05-.53.03-.77-.08-.24-.1-.45-.27-.6-.49-.15-.22-.22-.48-.22-.74,0-.35.14-.69.39-.94.25-.25.59-.39.94-.39ZM24,44.67c-.26,0-.52-.08-.74-.22-.22-.15-.39-.35-.49-.6-.1-.24-.13-.51-.08-.77.05-.26.18-.5.36-.68.19-.19.42-.31.68-.36.26-.05.53-.03.77.08.24.1.45.27.6.49.15.22.22.48.22.74,0,.35-.14.69-.39.94-.25.25-.59.39-.94.39ZM43.33,26.67c0,.35-.14.69-.39.94-.25.25-.59.39-.94.39H6c-.35,0-.69-.14-.94-.39-.25-.25-.39-.59-.39-.94v-5.33c0-.35.14-.69.39-.94.25-.25.59-.39.94-.39h36c.35,0,.69.14.94.39.25.25.39.59.39.94v5.33ZM39.33,22.67c-.26,0-.52.08-.74.22-.22.15-.39.35-.49.6-.1.24-.13.51-.08.77.05.26.18.5.36.68.19.19.42.31.68.36.26.05.53.03.77-.08.24-.1.45-.27.6-.49.15-.22.22-.48.22-.74,0-.35-.14-.69-.39-.94-.25-.25-.59-.39-.94-.39ZM31.33,22.67c-.26,0-.52.08-.74.22-.22.15-.39.35-.49.6s-.13.51-.08.77c.05.26.18.5.36.68.19.19.42.31.68.36.26.05.53.03.77-.08.24-.1.45-.27.6-.49.15-.22.22-.48.22-.74,0-.35-.14-.69-.39-.94-.25-.25-.59-.39-.94-.39ZM35.33,22.67c-.26,0-.52.08-.74.22-.22.15-.39.35-.49.6-.1.24-.13.51-.08.77.05.26.18.5.36.68.19.19.42.31.68.36.26.05.53.03.77-.08.24-.1.45-.27.6-.49.15-.22.22-.48.22-.74,0-.35-.14-.69-.39-.94-.25-.25-.59-.39-.94-.39Z`,
  ],
}
export const OtherTopologyIcon = {
  prefix: 'fal',
  iconName: 'other-topology',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M45.9,13.4c0-3.1-2.5-5.6-5.6-5.6s-5.6,2.5-5.6,5.6,1.5,4.4,3.7,5.2v3.3h-10.1v-3.2c2.1-.8,3.7-2.8,3.7-5.2s-2.5-5.6-5.6-5.6-5.6,2.5-5.6,5.6,1.5,4.4,3.7,5.2v3.2H14.3v-3.2c2.1-.8,3.7-2.8,3.7-5.2s-2.5-5.6-5.6-5.6-5.6,2.5-5.6,5.6,1.5,4.4,3.7,5.2v3.2H5.8v7.5c-2.1,.8-3.7,2.8-3.7,5.2s2.5,5.6,5.6,5.6,5.6-2.5,5.6-5.6-1.5-4.4-3.7-5.2v-3.7h10.1v3.7c-2.1,.8-3.7,2.8-3.7,5.2s2.5,5.6,5.6,5.6,5.6-2.5,5.6-5.6-1.5-4.4-3.7-5.2v-3.7h10.2v3.7c-2.1,.8-3.7,2.8-3.7,5.2s2.5,5.6,5.6,5.6,5.6-2.5,5.6-5.6-1.5-4.4-3.7-5.2v-3.7h4.7v-7.1c2.1-.8,3.7-2.8,3.7-5.2Zm-19.5-1.7c1,0,1.8,.8,1.8,1.8s-.8,1.8-1.8,1.8-1.8-.8-1.8-1.8,.8-1.8,1.8-1.8Zm-14,0c1,0,1.8,.8,1.8,1.8s-.8,1.8-1.8,1.8-1.8-.8-1.8-1.8,.8-1.8,1.8-1.8Zm-4.7,24.7c-1,0-1.8-.8-1.8-1.8s.8-1.8,1.8-1.8,1.8,.8,1.8,1.8-.8,1.8-1.8,1.8Zm14,0c-1,0-1.8-.8-1.8-1.8s.8-1.8,1.8-1.8,1.8,.8,1.8,1.8-.8,1.8-1.8,1.8Zm14,0c-1,0-1.8-.8-1.8-1.8s.8-1.8,1.8-1.8,1.8,.8,1.8,1.8-.8,1.8-1.8,1.8Zm4.7-24.7c1,0,1.8,.8,1.8,1.8s-.8,1.8-1.8,1.8-1.8-.8-1.8-1.8,.8-1.8,1.8-1.8Z`,
  ],
}

export const OutlierIcon = {
  prefix: 'fal',
  iconName: 'outlier',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M43.9,15.5c-.2-1.1-.7-2.2-1.7-3.1s-1.8-1.5-3.1-1.7c-.4,0-.7-.2-1.1-.2-.7,0-1.5,.2-2.2,.4-1.1,.4-2,1.3-2.8,2.2-.4,.7-.7,1.5-.9,2.2l-3.7,3.7-.4-.4c-.2-1.1-.7-2-1.5-2.8-1.1-1.1-2.6-1.8-4.2-1.8s-3.1,.6-4.2,1.8c-.7,.9-1.3,1.8-1.5,2.9l-4,4c-.9,.2-2,.7-2.8,1.5-1.1,1.1-1.8,2.6-1.8,4.2s.6,3.1,1.8,4.2c1.1,1.1,2.6,1.8,4.2,1.8s3.1-.6,4.2-1.8c.9-.9,1.5-1.8,1.7-3.1l2.4-2.4,.4,.4c.2,1.1,.7,2,1.7,2.9s2.6,1.8,4.2,1.8,3.1-.6,4.2-1.8c.7-.7,1.3-1.8,1.7-2.9l5.2-5.2c.6-.2,1.3-.4,1.8-.7,.9-.7,1.8-1.7,2.2-2.8,.2-1.3,.4-2.4,.2-3.5Zm-3.3,2.2c-.2,.6-.6,.9-1.1,1.3-.4,.4-.9,.6-1.5,.6l-6.6,6.8v.2c0,.7-.4,1.5-.7,2-.6,.6-1.3,.7-2,.7s-1.5-.4-2-.7c-.6-.6-.7-1.3-.7-2v-.2l-2.9-2.9h-.9l-5.2,4.8v.4c0,.7-.4,1.5-.7,2-.6,.4-1.3,.7-2,.7s-1.5-.4-2-.7c-.4-.6-.7-1.3-.7-2s.4-1.5,.7-2c.6-.4,1.3-.7,2-.7l5.5-5.5c0-.7,.4-1.5,.7-2,.6-.4,1.3-.7,2-.7s1.5,.4,2,.7c.6,.6,.7,1.3,.7,2l3.1,3.3h.4l6.6-6.6v-.4c0-.6,.2-1.1,.6-1.5s.7-.7,1.3-1.1c.6-.2,1.1-.2,1.7-.2,.6,.2,1.1,.4,1.5,.7s.7,.9,.7,1.5c-.2,.6-.2,1.1-.4,1.7h0Zm-1.7,21H7.7V7.4c0-.6-.2-.9-.6-1.3s-.7-.6-1.3-.6-.9,.2-1.3,.6-.6,.7-.6,1.3V40.6c0,.6,.2,.9,.6,1.3,.4,.4,.7,.6,1.3,.6H39c.6,0,.9-.2,1.3-.6s.6-.7,.6-1.3-.2-.9-.6-1.3-.7-.6-1.3-.6Z',
  ],
}

export const ProvisionIcon = {
  prefix: 'fal',
  iconName: 'provision',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M43.1,4.6h-7.7c-1,0-1.9,.8-1.9,1.9v11.1c0,1,.8,1.9,1.9,1.9h2v3.5h-11.6v-3.5h2c1,0,1.9-.8,1.9-1.9V6.5c0-1-.8-1.9-1.9-1.9h-7.7c-1,0-1.9,.8-1.9,1.9v11.1c0,1,.8,1.9,1.9,1.9h2v3.5H10.6v-3.5h2c1,0,1.9-.8,1.9-1.9V6.5c0-1-.8-1.9-1.9-1.9H4.9c-1,0-1.9,.8-1.9,1.9v11.1c0,1,.8,1.9,1.9,1.9h1.9v5.4c0,1,.8,1.9,1.9,1.9h13.4v2.5c-3.1,.8-5.4,3.6-5.4,6.9s3.2,7.2,7.2,7.2,7.2-3.2,7.2-7.2-2.2-6-5.2-6.9v-2.6h13.4c1,0,1.9-.8,1.9-1.9v-5.4h2c1,0,1.9-.8,1.9-1.9V6.5c0-1-.8-1.9-1.9-1.9ZM6.7,15.7v-7.4h3.9v7.4h-3.9Zm20.6,20.4c0,1.9-1.5,3.4-3.4,3.4s-3.4-1.5-3.4-3.4,1.5-3.4,3.4-3.4,3.4,1.5,3.4,3.4Zm-5.3-20.4v-7.4h3.9v7.4h-3.9Zm19.2,0h-3.9v-7.4h3.9v7.4Z`,
  ],
}
export const Paperclip = {
  prefix: 'fal',
  iconName: 'paper-clip',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M40.5,25.1c-.2-.2-.4-.3-.7-.4-.2-.1-.5-.2-.8-.2s-.5,0-.8,.2c-.2,.1-.5,.3-.7,.4h0l-12.6,12.6c-1.6,1.5-3.8,2.2-6,2.2-2.2,0-4.3-1-5.9-2.5-1.6-1.6-2.5-3.7-2.5-5.9,0-2.2,.7-4.4,2.2-6L29.1,9.3c1-.9,2.3-1.4,3.6-1.4s2.6,.5,3.6,1.4c.9,1,1.5,2.3,1.5,3.6s-.5,2.6-1.5,3.6l-14,14c-.1,.1-.3,.3-.5,.4-.2,0-.4,.1-.6,.1-.2,0-.4,0-.6,0-.2,0-.4-.2-.5-.3-.1-.1-.3-.3-.4-.5,0-.2-.1-.4-.1-.6,0-.2,0-.4,0-.6,0-.2,.2-.4,.3-.5l10.4-10.4c.4-.4,.6-.9,.6-1.4s-.2-1.1-.6-1.4c-.4-.4-.9-.6-1.4-.6s-1.1,.2-1.4,.6l-10.4,10.5c-.5,.5-.9,1.1-1.2,1.8s-.4,1.4-.4,2.1,.1,1.5,.4,2.1,.7,1.3,1.2,1.8c1.1,1,2.5,1.6,4,1.6s2.9-.6,4-1.6l14-14c1.6-1.7,2.5-4,2.5-6.4,0-2.4-1-4.6-2.7-6.3-1.7-1.7-3.9-2.6-6.3-2.7-2.4,0-4.7,.8-6.4,2.5L9.8,22.7c-2.2,2.4-3.4,5.6-3.3,8.9,0,3.3,1.4,6.4,3.8,8.7s5.5,3.6,8.7,3.7c3.3,0,6.4-1.2,8.9-3.4l12.6-12.6c.2-.2,.3-.4,.4-.7,.1-.2,.2-.5,.2-.8s0-.5-.2-.8c-.1-.2-.3-.5-.4-.7Z`,
  ],
}

export const Pause = {
  prefix: 'fal',
  iconName: 'pause',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M18,12c1.1,0,2,.9,2,2v20c0,1.1-.9,2-2,2s-2-.9-2-2V14c0-1.1,.9-2,2-2Zm12,0c1.1,0,2,.9,2,2v20c0,1.1-.9,2-2,2s-2-.9-2-2V14c0-1.1,.9-2,2-2Z`,
  ],
}

export const Pencil = {
  prefix: 'fal',
  iconName: 'pencil',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M42.4,13.6l-8-8c-.8-.8-2.1-.8-2.8,0l-6,6L5.5,31.6c-.4,.4-.5,.9-.5,1.4v7.9c0,1.1,.9,2,2,2H15c.5,0,1-.2,1.4-.5l25.9-26c.8-.8,.8-2.1,0-2.8ZM14.1,39h-5.1v-5.1L26.9,15.9l5.1,5.1L14.1,39Zm20.8-20.8l-5.1-5.1,3.2-3.2,5.1,5.1-3.2,3.2Z`,
  ],
}
export const Play = {
  prefix: 'fal',
  iconName: 'play',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M11,13.5c0-3.1,3.3-5,6-3.5l18,10.5c2.6,1.5,2.6,5.4,0,6.9l-18,10.5c-2.7,1.6-6-.4-6-3.5V13.5Zm22,10.5L15,13.5v21l18-10.5Z`,
  ],
}
export const PlayCircle = {
  prefix: 'fal',
  iconName: 'play-circle',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M42.5,16.3c-1-2.4-2.5-4.6-4.3-6.5s-4.1-3.3-6.5-4.3c-2.4-1-5-1.5-7.7-1.5-4,0-7.8,1.2-11.1,3.4-3.3,2.2-5.9,5.3-7.4,8.9-1.5,3.7-1.9,7.7-1.1,11.6,.8,3.9,2.7,7.4,5.5,10.2,2.8,2.8,6.4,4.7,10.2,5.5,3.9,.8,7.9,.4,11.6-1.1s6.8-4.1,9-7.4c2.2-3.3,3.4-7.2,3.4-11.1s-.5-5.2-1.5-7.7h-.1Zm-7.2,19c-3,3-7.1,4.7-11.3,4.7s-6.3-.9-8.9-2.7c-2.6-1.8-4.7-4.3-5.9-7.2-1.2-2.9-1.5-6.1-.9-9.2s2.1-6,4.4-8.2c2.3-2.2,5.1-3.8,8.2-4.4s6.3-.3,9.2,.9c2.9,1.2,5.4,3.3,7.2,5.9,1.8,2.6,2.7,5.7,2.7,8.9s-1.7,8.3-4.7,11.3Zm-3.3-14.8l-10-5.8c-.6-.4-1.3-.5-2-.5s-1.4,.2-2,.5c-.6,.4-1.1,.9-1.5,1.5s-.5,1.3-.5,2v11.6c0,.7,.2,1.4,.5,2,.4,.6,.9,1.1,1.5,1.5s1.3,.5,2,.5,1.4-.2,2-.5l10-5.8c.6-.4,1.1-.9,1.5-1.5,.3-.6,.5-1.3,.5-2s-.2-1.4-.5-2-.9-1.1-1.5-1.5Zm-12,9.2v-11.5l10,5.8-10,5.8h0Z`,
  ],
}
export const LoaderIcon = {
  prefix: 'fal',
  iconName: 'loader-new',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24,8.25c-4.18,0-8.18,1.66-11.13,4.61-2.95,2.95-4.61,6.96-4.61,11.14,0,1.24-1.01,2.25-2.25,2.25s-2.25-1.01-2.25-2.25c0-5.37,2.13-10.52,5.93-14.32,3.8-3.8,8.95-5.93,14.32-5.93h0c5.61.02,11,2.21,15.04,6.11l.03.03h0s.68.68.68.68v-4.57c0-1.24,1.01-2.25,2.25-2.25s2.25,1.01,2.25,2.25v10c0,1.24-1.01,2.25-2.25,2.25h-10c-1.24,0-2.25-1.01-2.25-2.25s1.01-2.25,2.25-2.25h4.57l-.67-.67c-3.2-3.08-7.46-4.82-11.91-4.83ZM42,21.75c1.24,0,2.25,1.01,2.25,2.25,0,5.37-2.13,10.52-5.93,14.32s-8.95,5.93-14.32,5.93h0c-5.61-.02-11-2.21-15.04-6.11l-.03-.03h0s-.68-.68-.68-.68v4.57c0,1.24-1.01,2.25-2.25,2.25s-2.25-1.01-2.25-2.25v-10c0-1.24,1.01-2.25,2.25-2.25h10c1.24,0,2.25,1.01,2.25,2.25s-1.01,2.25-2.25,2.25h-4.57l.67.67c3.2,3.08,7.46,4.82,11.91,4.83,4.18,0,8.18-1.66,11.13-4.61,2.95-2.95,4.61-6.96,4.61-11.14,0-1.24,1.01-2.25,2.25-2.25Z`,
  ],
}

export const Plug = {
  prefix: 'fal',
  iconName: 'plug',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M38,12h-6V6c0-.5-.2-1-.6-1.4-.4-.4-.9-.6-1.4-.6s-1,.2-1.4,.6c-.4,.4-.6,.9-.6,1.4v6h-8V6c0-.5-.2-1-.6-1.4-.4-.4-.9-.6-1.4-.6s-1,.2-1.4,.6c-.4,.4-.6,.9-.6,1.4v6h-6c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4c.4,.4,.9,.6,1.4,.6h2v10c0,.3,0,.5,.1,.8,0,.2,.2,.5,.4,.7l5.4,5.4v9.2c0,.5,.2,1,.6,1.4,.4,.4,.9,.6,1.4,.6s1-.2,1.4-.6,.6-.9,.6-1.4v-8h4v8c0,.5,.2,1,.6,1.4s.9,.6,1.4,.6,1-.2,1.4-.6,.6-.9,.6-1.4v-9.2l5.4-5.4c.2-.2,.3-.4,.4-.7,0-.2,.1-.5,.1-.8v-10h2c.5,0,1-.2,1.4-.6,.4-.4,.6-.9,.6-1.4s-.2-1-.6-1.4c-.4-.4-.9-.6-1.4-.6Zm-6,13.2l-4.8,4.8h-6.4l-4.8-4.8v-9.2h16v9.2Zm-10,.8h4c.5,0,1-.2,1.4-.6s.6-.9,.6-1.4-.2-1-.6-1.4-.9-.6-1.4-.6h-4c-.5,0-1,.2-1.4,.6s-.6,.9-.6,1.4,.2,1,.6,1.4,.9,.6,1.4,.6Z`,
  ],
}
export const Plus = {
  prefix: 'fal',
  iconName: 'plus',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M38,22h-12V10c0-.5-.2-1-.6-1.4-.4-.4-.9-.6-1.4-.6s-1,.2-1.4,.6c-.4,.4-.6,.9-.6,1.4v12H10c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4c.4,.4,.9,.6,1.4,.6h12v12c0,.5,.2,1,.6,1.4s.9,.6,1.4,.6,1-.2,1.4-.6,.6-.9,.6-1.4v-12h12c.5,0,1-.2,1.4-.6s.6-.9,.6-1.4-.2-1-.6-1.4-.9-.6-1.4-.6Z`,
  ],
}
export const PlusCircle = {
  prefix: 'fal',
  iconName: 'plus-circle',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M42.5,16.3c-1-2.4-2.5-4.6-4.3-6.5s-4.1-3.3-6.5-4.3c-2.4-1-5-1.5-7.7-1.5-4,0-7.8,1.2-11.1,3.4-3.3,2.2-5.9,5.3-7.4,9-1.5,3.7-1.9,7.7-1.1,11.6,.8,3.9,2.7,7.4,5.5,10.2,2.8,2.8,6.4,4.7,10.2,5.5,3.9,.8,7.9,.4,11.6-1.1,3.7-1.5,6.8-4.1,9-7.4,2.2-3.3,3.4-7.2,3.4-11.1s-.5-5.2-1.5-7.7Zm-7.2,19c-3,3-7.1,4.7-11.3,4.7s-6.3-.9-8.9-2.7c-2.6-1.8-4.7-4.3-5.9-7.2-1.2-2.9-1.5-6.1-.9-9.2,.6-3.1,2.1-6,4.4-8.2s5.1-3.8,8.2-4.4c3.1-.6,6.3-.3,9.2,.9,2.9,1.2,5.4,3.3,7.2,5.9,1.8,2.6,2.7,5.7,2.7,8.9s-1.7,8.3-4.7,11.3Zm-3.3-13.3h-6v-6c0-.5-.2-1-.6-1.4-.4-.4-.9-.6-1.4-.6s-1,.2-1.4,.6c-.4,.4-.6,.9-.6,1.4v6h-6c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4c.4,.4,.9,.6,1.4,.6h6v6c0,.5,.2,1,.6,1.4s.9,.6,1.4,.6,1-.2,1.4-.6,.6-.9,.6-1.4v-6h6c.5,0,1-.2,1.4-.6s.6-.9,.6-1.4-.2-1-.6-1.4-.9-.6-1.4-.6Z`,
  ],
}
export const PowerOff = {
  prefix: 'fal',
  iconName: 'power-off',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24,25.9c1.1,0,1.9-.9,1.9-1.9V4.9c0-1.1-.9-1.9-1.9-1.9s-1.9,.9-1.9,1.9V24c0,1.1,.9,1.9,1.9,1.9Zm18.7-3.7c-.7-3.7-2.5-7.1-5.2-9.8-.7-.7-2-.7-2.7,0-.7,.7-.7,2,0,2.7,2.1,2.1,3.6,4.8,4.2,7.8,.6,3,.3,6-.9,8.8-1.2,2.8-3.1,5.2-5.6,6.9-5,3.4-11.9,3.4-17,0-2.5-1.7-4.5-4.1-5.6-6.9-1.2-2.8-1.5-5.8-.9-8.8,.6-3,2-5.7,4.2-7.8,.7-.7,.7-2,0-2.7-.7-.7-2-.7-2.7,0-2.7,2.7-4.5,6.1-5.2,9.8-.7,3.7-.4,7.5,1.1,11,1.4,3.5,3.9,6.5,7,8.6,3.1,2.1,6.8,3.2,10.6,3.2s7.5-1.1,10.6-3.2c3.2-2.1,5.6-5.1,7-8.6,1.5-3.5,1.8-7.3,1.1-11Z`,
  ],
}
export const PluginLibraryIcon = {
  prefix: 'fal',
  iconName: 'plugin-library',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M39.3,21.6c-.6,0-1.3,0-1.9,.3v-4.1c0-1.7-1.4-3.1-3.1-3.1h-3.8c.1-.5,.2-1.1,.2-1.6,0-3.7-3-6.7-6.7-6.7s-6.7,3-6.7,6.7,0,1.1,.2,1.6h-3.9c-1.7,0-3.1,1.4-3.1,3.1v4.1c-.6-.2-1.2-.2-1.8-.2-3.7,0-6.7,3-6.7,6.7s3,6.7,6.7,6.7,1.2,0,1.8-.2v3.8c0,1.7,1.4,3.1,3.1,3.1h6.7c.7,0,1.3-.4,1.6-1,.3-.6,.2-1.4-.3-1.9-.5-.6-.8-1.3-.8-2.1,0-1.7,1.4-3.2,3.2-3.2s3.2,1.4,3.2,3.2-.3,1.5-.8,2.1c-.5,.5-.6,1.3-.3,1.9,.3,.6,.9,1,1.6,1h6.6c1.7,0,3.1-1.4,3.1-3.1v-3.8c.6,.2,1.2,.3,1.9,.3,3.7,0,6.7-3,6.7-6.7s-3-6.7-6.7-6.7Zm0,9.9c-.9,0-1.7-.4-2.3-1-.5-.5-1.3-.7-1.9-.5s-1.1,.9-1.1,1.6v6.5h-3.3c.1-.5,.2-1,.2-1.5,0-3.7-3-6.7-6.7-6.7s-6.7,3-6.7,6.7,0,1,.2,1.5h-3.5v-6.4c0-.7-.4-1.4-1.1-1.6-.7-.3-1.4-.1-1.9,.4-.6,.6-1.4,1-2.3,1-1.7,0-3.2-1.4-3.2-3.2s1.4-3.2,3.2-3.2,1.7,.3,2.3,1c.5,.5,1.3,.7,1.9,.4,.7-.3,1.1-.9,1.1-1.6v-6.7h6.5c.7,0,1.3-.4,1.6-1.1,.3-.6,.1-1.4-.3-1.9-.6-.6-.9-1.4-.9-2.2,0-1.7,1.4-3.2,3.2-3.2s3.2,1.4,3.2,3.2-.3,1.6-.9,2.2c-.5,.5-.6,1.3-.3,1.9,.3,.7,.9,1.1,1.6,1.1h6.3v6.8c0,.7,.4,1.4,1.1,1.7s1.5,0,1.9-.5c.6-.7,1.4-1,2.3-1,1.7,0,3.2,1.4,3.2,3.2s-1.4,3.2-3.2,3.2Z`,
  ],
}

export const PolicySettingsIcon = {
  prefix: 'fal',
  iconName: 'policy-settings',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M3.8,10.6h6.5c.8,2.6,3.1,4.4,5.9,4.4s6.2-2.8,6.2-6.2-2.8-6.2-6.2-6.2-5.2,1.9-5.9,4.4H3.8c-1,0-1.8,.8-1.8,1.8s.8,1.8,1.8,1.8Zm12.4-4.4c1.5,0,2.6,1.2,2.6,2.6s-1.2,2.6-2.6,2.6-2.6-1.2-2.6-2.6,1.2-2.6,2.6-2.6Zm12.1,4.4h16c1,0,1.8-.8,1.8-1.8s-.8-1.8-1.8-1.8h-16c-1,0-1.8,.8-1.8,1.8s.8,1.8,1.8,1.8Zm-7.3,11.6H3.8c-1,0-1.8,.8-1.8,1.8s.8,1.8,1.8,1.8H21c1,0,1.8-.8,1.8-1.8s-.8-1.8-1.8-1.8Zm23.2,15.3H27c-1,0-1.8,.8-1.8,1.8s.8,1.8,1.8,1.8h17.2c1,0,1.8-.8,1.8-1.8s-.8-1.8-1.8-1.8Zm0-15.3h-5.1c-.8-2.6-3.1-4.4-5.9-4.4s-6.2,2.8-6.2,6.2,2.8,6.2,6.2,6.2,5.2-1.9,5.9-4.4h5.1c1,0,1.8-.8,1.8-1.8s-.8-1.8-1.8-1.8Zm-11.1,4.4c-1.5,0-2.6-1.2-2.6-2.6s1.2-2.6,2.6-2.6,2.6,1.2,2.6,2.6-1.2,2.6-2.6,2.6Zm-18.3,6.4c-2.8,0-5.2,1.9-5.9,4.4H3.8c-1,0-1.8,.8-1.8,1.8s.8,1.8,1.8,1.8h5.1c.8,2.6,3.1,4.4,5.9,4.4s6.2-2.8,6.2-6.2-2.8-6.2-6.2-6.2Zm0,8.8c-1.5,0-2.6-1.2-2.6-2.6s1.2-2.6,2.6-2.6,2.6,1.2,2.6,2.6-1.2,2.6-2.6,2.6Z`,
  ],
}

export const PauseCircleIcon = {
  prefix: 'fal',
  iconName: 'pause-circle',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M20,16c-1.1,0-2,.9-2,2v12c0,1.1,.9,2,2,2s2-.9,2-2v-12c0-1.1-.9-2-2-2Zm8,0c-1.1,0-2,.9-2,2v12c0,1.1,.9,2,2,2s2-.9,2-2v-12c0-1.1-.9-2-2-2Zm-4-14C11.9,2,2,11.9,2,24s9.9,22,22,22,22-9.9,22-22S36.1,2,24,2Zm0,40c-9.9,0-18-8.1-18-18S14.1,6,24,6s18,8.1,18,18-8.1,18-18,18Z`,
  ],
}

export const Question = {
  prefix: 'fal',
  iconName: 'question',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24.1,4c-5.8,0-10.2,2.4-13.5,7.2-1.5,2.2-1,5.2,1.1,6.8l2.9,2.2c.8,.6,1.9,1,2.9,1-.9,1.5-1.4,3.2-1.4,5v.9c0,1.3,.5,2.4,1.3,3.3-1.3,1.4-2.1,3.3-2.1,5.4,0,4.5,3.6,8.1,8.1,8.1s8.1-3.6,8.1-8.1-.8-4-2.1-5.4c.7-.8,1.1-1.7,1.2-2.8,2.2-1.3,7.5-3.8,7.5-10.8s-7.1-12.9-14.2-12.9Zm-.6,36.2c-2.4,0-4.4-2-4.4-4.4s2-4.4,4.4-4.4,4.4,2,4.4,4.4-2,4.4-4.4,4.4Zm3.6-13.8v.7c0,.7-.6,1.2-1.2,1.2h-4.7c-.7,0-1.2-.6-1.2-1.2v-.9c0-3.7,2.8-5.2,4.9-6.3,1.8-1,2.9-1.7,2.9-3s-2.3-3-4.1-3-3.5,1.1-5,3.1c-.4,.5-1.2,.6-1.7,.2l-2.9-2.2c-.5-.4-.6-1.1-.3-1.7,2.4-3.6,5.5-5.6,10.4-5.6s10.5,3.9,10.5,9.2-7.5,7-7.5,9.6h0Z`,
  ],
}
export const Redo = {
  prefix: 'fal',
  iconName: 'redo',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M43.8,5.2c-.2-.5-.6-.9-1.1-1.1-.2-.1-.5-.2-.8-.2h-12c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4c.4,.4,.9,.6,1.4,.6h7.2L4.6,40.6c-.2,.2-.3,.4-.4,.7-.1,.2-.2,.5-.2,.8s0,.5,.2,.8c.1,.2,.3,.5,.4,.7,.2,.2,.4,.3,.7,.4,.2,.1,.5,.2,.8,.2s.5,0,.8-.2c.2-.1,.5-.3,.7-.4L40,10.8v7.2c0,.5,.2,1,.6,1.4,.4,.4,.9,.6,1.4,.6s1-.2,1.4-.6c.4-.4,.6-.9,.6-1.4V6c0-.3,0-.5-.2-.8Zm-26.4,14.8c.4,.4,.9,.6,1.4,.6s1-.2,1.4-.6c.4-.4,.6-.9,.6-1.4s-.2-1-.6-1.4L7.7,4.6c-.4-.4-.9-.6-1.4-.6s-1,.2-1.4,.6c-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4l12.6,12.6Zm24.6,8c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4v7.2l-9.1-9.2c-.4-.4-.9-.6-1.4-.6s-1.1,.2-1.4,.6c-.4,.4-.6,.9-.6,1.4s.2,1.1,.6,1.4l9.2,9.1h-7.2c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4c.4,.4,.9,.6,1.4,.6h12c.3,0,.5,0,.8-.2,.5-.2,.9-.6,1.1-1.1,.1-.2,.2-.5,.2-.8v-12c0-.5-.2-1-.6-1.4-.4-.4-.9-.6-1.4-.6Z`,
  ],
}

export const PublicIcon = {
  prefix: 'fal',
  iconName: 'public',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M34.4998 19.8299H17.8998V15.7699C17.8998 12.4099 20.6398 9.66992 23.9998 9.66992C25.8198 9.66992 27.5298 10.4699 28.6898 11.8699C29.3998 12.7199 30.6598 12.8299 31.5098 12.1299C32.3598 11.4199 32.4698 10.1599 31.7698 9.30992C29.8398 6.99992 27.0098 5.66992 24.0098 5.66992C18.4398 5.66992 13.9098 10.1999 13.9098 15.7699V19.8299H13.5098C10.3398 19.8299 7.75977 22.4099 7.75977 25.5799V36.5799C7.75977 39.7499 10.3398 42.3299 13.5098 42.3299H34.5098C37.6798 42.3299 40.2598 39.7499 40.2598 36.5799V25.5799C40.2598 22.4099 37.6798 19.8299 34.5098 19.8299H34.4998ZM36.2498 36.5799C36.2498 37.5399 35.4598 38.3299 34.4998 38.3299H13.4998C12.5398 38.3299 11.7498 37.5399 11.7498 36.5799V25.5799C11.7498 24.6199 12.5398 23.8299 13.4998 23.8299H34.4998C35.4598 23.8299 36.2498 24.6099 36.2498 25.5799V36.5799ZM23.9998 27.5899C22.8998 27.5899 21.9998 28.4899 21.9998 29.5899V33.1799C21.9998 34.2799 22.8998 35.1799 23.9998 35.1799C25.0998 35.1799 25.9998 34.2799 25.9998 33.1799V29.5899C25.9998 28.4899 25.0998 27.5899 23.9998 27.5899Z`,
  ],
}

export const PrivateIcon = {
  prefix: 'fal',
  iconName: 'private',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M38.3,19.7c-1.1-1.1-2.7-1.7-4.3-1.7v-4c0-2.7-1-5.2-3-7-1.9-1.9-4.4-3-7-3s-5.2,1-7,3c-1.9,1.9-3,4.4-3,7v4c-1.6,0-3.1,.7-4.3,1.7s-1.7,2.7-1.7,4.3v14c0,1.6,.7,3.1,1.7,4.3s2.7,1.7,4.3,1.7h20c1.6,0,3.1-.7,4.3-1.7s1.7-2.7,1.7-4.3v-14c0-1.6-.7-3.1-1.7-4.3Zm-20.3-5.7c0-1.6,.7-3.1,1.7-4.3s2.7-1.7,4.3-1.7,3.1,.7,4.3,1.7c1.1,1.1,1.7,2.7,1.7,4.3v4h-12v-4Zm18,24c0,.6-.2,1-.6,1.4-.4,.4-.9,.6-1.4,.6H14c-.6,0-1-.2-1.4-.6-.4-.4-.6-.9-.6-1.4v-14c0-.6,.2-1,.6-1.4s.9-.6,1.4-.6h20c.6,0,1,.2,1.4,.6s.6,.9,.6,1.4v14Zm-12-12c-.6,0-1,.2-1.4,.6s-.6,.9-.6,1.4v6c0,.6,.2,1,.6,1.4s.9,.6,1.4,.6,1-.2,1.4-.6c.4-.4,.6-.9,.6-1.4v-6c0-.6-.2-1-.6-1.4s-.9-.6-1.4-.6Z`,
  ],
}

export const PortIcon = {
  prefix: 'fal',
  iconName: 'port',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M44,12.2h-8v-2.8c0-1-.8-1.8-1.8-1.8h-1.4v-2.6c0-1-.8-1.8-1.8-1.8h-14c-1,0-1.8,.8-1.8,1.8v2.6h-1.8c-1,0-1.8,.8-1.8,1.8v2.8H4c-1,0-1.8,.8-1.8,1.8v29c0,1,.8,1.8,1.8,1.8H44c1,0,1.8-.8,1.8-1.8V14c0-1-.8-1.8-1.8-1.8Zm-1.8,29H5.8V15.8h7.6c1,0,1.8-.8,1.8-1.8v-2.8h1.8c1,0,1.8-.8,1.8-1.8v-2.6h10.4v2.6c0,1,.8,1.8,1.8,1.8h1.4v2.8c0,1,.8,1.8,1.8,1.8h8v25.4ZM9.8,22.2v13.2c0,1,.8,1.8,1.8,1.8h25.2c1,0,1.8-.8,1.8-1.8v-13.2c0-1-.8-1.8-1.8-1.8H11.6c-1,0-1.8,.8-1.8,1.8Zm3.6,1.8h21.6v9.6h-4.4v-3.2c0-1-.8-1.8-1.8-1.8s-1.8,.8-1.8,1.8v3.2h-5.4v-3.2c0-1-.8-1.8-1.8-1.8s-1.8,.8-1.8,1.8v3.2h-4.6v-9.6Z',
  ],
}
export const RestartIcon = {
  prefix: 'fal',
  iconName: 'restart',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M21.7,14.3c-.6-.4-1.4-.4-2.1,0-.7,.3-1.1,1-1.1,1.8v16c0,.7,.4,1.4,1.1,1.8,.3,.2,.6,.2,.9,.2s.8-.1,1.1-.3l12-8c.6-.4,.9-1,.9-1.7s-.3-1.3-.9-1.7l-12-8Zm.9,13.9v-8.5l6.4,4.3-6.4,4.3Zm20.8-5.6c-.4-.4-.9-.6-1.4-.6s-1,.2-1.4,.6-.6,.9-.6,1.4c0,3.7-1.3,7.4-3.6,10.3-2.4,2.9-5.7,4.9-9.4,5.6-3.7,.7-7.5,.1-10.8-1.6-3.3-1.8-5.9-4.7-7.3-8.1-1.4-3.5-1.5-7.3-.4-10.9,1.1-3.6,3.5-6.6,6.6-8.7,3.1-2,6.9-2.9,10.6-2.5,3.7,.4,7.2,2.2,9.8,4.9h-4.1c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4c.4,.4,.9,.6,1.4,.6h8.4c.5,0,1-.2,1.4-.6,.4-.4,.6-.9,.6-1.4V6.3c0-.5-.2-1-.6-1.4-.4-.4-.9-.6-1.4-.6s-1,.2-1.4,.6c-.4,.4-.6,.9-.6,1.4v3.2c-3.3-3.2-7.7-5.1-12.3-5.5-4.6-.4-9.2,.9-13,3.5-3.8,2.6-6.6,6.5-7.8,10.9-1.3,4.4-1,9.2,.8,13.4,1.8,4.2,5,7.7,9.1,9.9,4.1,2.1,8.8,2.8,13.3,1.9,4.5-.9,8.6-3.4,11.5-6.9,2.9-3.6,4.5-8,4.5-12.6,0-.5-.2-1-.6-1.4Z`,
  ],
}

export const RotationIcon = {
  prefix: 'fal',
  iconName: 'rotation',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M39.3,25.1c-.4-2.7-1.4-5.3-3.1-7.5l-2.7,2.7c1,1.4,1.7,3.1,1.9,4.8h3.9Zm-8.5-10.6L22.2,5.8v6c-8.4,1-14.4,8.6-13.4,16.9,.8,7.1,6.4,12.6,13.4,13.4v-3.8c-6.3-1-10.5-7-9.4-13.2,.8-4.8,4.6-8.6,9.4-9.4v7.5l8.6-8.6Zm-4.8,23.8v3.8c2.7-.4,5.3-1.4,7.5-3.1l-2.7-2.7c-1.4,1-3.1,1.7-4.8,1.9Zm7.5-4.6l2.7,2.7c1.7-2.2,2.8-4.8,3.1-7.5h-3.9c-.3,1.7-.9,3.4-1.9,4.8Z`,
  ],
}

export const ReportIcon = {
  prefix: 'fal',
  iconName: 'report',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M16.6,31.8h14.6c1,0,1.8-.8,1.8-1.8s-.8-1.8-1.8-1.8h-14.6c-1,0-1.8,.8-1.8,1.8s.8,1.8,1.8,1.8Zm7.7-6.5c4.7,0,8.6-3.9,8.6-8.6s-.1-1.6-.3-2.4c-.1-.4-.4-.8-.9-1-.4-.2-.9-.3-1.3-.1l-2.2,.7,1.2-2c.5-.8,.2-1.9-.6-2.4-1.3-.8-2.9-1.2-4.4-1.2-4.7,0-8.6,3.9-8.6,8.6s3.9,8.6,8.6,8.6Zm0-13.7c.3,0,.7,0,1,0l-2.5,4.2c-.4,.6-.3,1.4,.1,2,.4,.6,1.2,.8,1.9,.6l4.6-1.4c-.2,2.6-2.4,4.7-5.1,4.7s-5.1-2.3-5.1-5.1,2.3-5.1,5.1-5.1Zm-7.7,26.8h14.6c1,0,1.8-.8,1.8-1.8s-.8-1.8-1.8-1.8h-14.6c-1,0-1.8,.8-1.8,1.8s.8,1.8,1.8,1.8ZM37.7,2.2H10.3c-1,0-1.8,.8-1.8,1.8V44c0,1,.8,1.8,1.8,1.8h27.4c1,0,1.8-.8,1.8-1.8V4c0-1-.8-1.8-1.8-1.8Zm-1.8,40H12V5.8h23.9V42.2Z',
  ],
}
export const Save = {
  prefix: 'fal',
  iconName: 'save',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M43.4,14.6l-10-10c-.4-.4-.9-.6-1.4-.6H10c-1.6,0-3.1,.6-4.2,1.8-1.1,1.1-1.8,2.6-1.8,4.2v28c0,1.6,.6,3.1,1.8,4.2,1.1,1.1,2.6,1.8,4.2,1.8h28c1.6,0,3.1-.6,4.2-1.8s1.8-2.6,1.8-4.2V16c0-.5-.2-1-.6-1.4Zm-11.4,25.4H16v-12h16v12Zm8-2c0,.5-.2,1-.6,1.4s-.9,.6-1.4,.6h-2v-14c0-1.1-.9-2-2-2H14c-1.1,0-2,.9-2,2v14h-2c-.5,0-1-.2-1.4-.6-.4-.4-.6-.9-.6-1.4V10c0-.5,.2-1,.6-1.4,.4-.4,.9-.6,1.4-.6h2v8c0,1.1,.9,2,2,2H30c1.1,0,2-.9,2-2s-.9-2-2-2h-14v-6h15.2l8.8,8.8v21.2Z`,
  ],
}
export const Search = {
  prefix: 'fal',
  iconName: 'search',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M43.4,40.6l-7.4-7.3c2.9-3.6,4.3-8.2,3.9-12.8-.4-4.6-2.5-8.9-5.9-11.9-3.4-3-7.9-4.7-12.5-4.6s-9,2-12.2,5.2c-3.2,3.2-5.2,7.7-5.3,12.3s1.5,9,4.6,12.5c3,3.4,7.3,5.5,11.9,5.9s9.1-1,12.8-3.9l7.3,7.3c.2,.2,.4,.4,.7,.5s.5,.2,.8,.2,.6,0,.8-.2c.3,0,.5-.3,.7-.5,.4-.4,.6-.9,.6-1.4s-.2-1-.6-1.4h0Zm-11.5-8.6c-2.7,2.7-6.2,4.1-9.9,4.1s-5.4-.9-7.8-2.4c-2.3-1.5-4.1-3.7-5.1-6.3-1-2.6-1.3-5.3-.8-8.1,.6-2.7,1.9-5.2,3.8-7.1,2-2,4.5-3.3,7.1-3.8,2.7-.6,5.5-.3,8.1,.8s4.8,2.9,6.3,5.1c1.5,2.3,2.4,5,2.4,7.8s-1.4,7.2-4.1,9.9Z`,
  ],
}

export const ShareAlt = {
  prefix: 'fal',
  iconName: 'share-alt',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M36,28c-1.2,0-2.3,.3-3.4,.8-1.1,.5-2,1.3-2.7,2.2l-10.2-4.7c.5-1.5,.5-3.1,0-4.5l10.2-4.7c1.2,1.5,2.9,2.4,4.7,2.8,1.9,.3,3.8,0,5.4-.9,1.6-.9,2.9-2.4,3.5-4.2,.6-1.8,.6-3.7,0-5.5-.6-1.8-1.9-3.3-3.5-4.2-1.6-.9-3.5-1.3-5.4-.9-1.9,.3-3.5,1.3-4.7,2.8-1.2,1.5-1.9,3.3-1.8,5.2,0,.5,0,1,.1,1.4l-10.6,4.9c-1.1-1.1-2.6-1.8-4.1-2.1-1.5-.3-3.1-.1-4.6,.5-1.5,.6-2.7,1.6-3.6,2.9-.9,1.3-1.3,2.9-1.3,4.4s.5,3.1,1.3,4.4c.9,1.3,2.1,2.3,3.6,2.9,1.5,.6,3,.8,4.6,.5,1.5-.3,3-1,4.1-2.1l10.6,4.9c0,.5-.1,.9-.1,1.4,0,1.6,.5,3.1,1.3,4.4,.9,1.3,2.1,2.3,3.6,2.9,1.5,.6,3.1,.8,4.6,.5,1.6-.3,3-1.1,4.1-2.2,1.1-1.1,1.9-2.5,2.2-4.1,.3-1.6,.2-3.2-.5-4.6-.6-1.5-1.6-2.7-2.9-3.6-1.3-.9-2.9-1.3-4.4-1.3Zm0-20c.8,0,1.6,.2,2.2,.7,.7,.4,1.2,1.1,1.5,1.8,.3,.7,.4,1.5,.2,2.3s-.5,1.5-1.1,2c-.6,.6-1.3,.9-2,1.1s-1.6,0-2.3-.2c-.7-.3-1.4-.8-1.8-1.5-.4-.7-.7-1.4-.7-2.2s.4-2.1,1.2-2.8c.7-.7,1.8-1.2,2.8-1.2ZM12,28c-.8,0-1.6-.2-2.2-.7-.7-.4-1.2-1.1-1.5-1.8-.3-.7-.4-1.5-.2-2.3,.2-.8,.5-1.5,1.1-2,.6-.6,1.3-.9,2-1.1,.8-.2,1.6,0,2.3,.2,.7,.3,1.4,.8,1.8,1.5,.4,.7,.7,1.4,.7,2.2s-.4,2.1-1.2,2.8-1.8,1.2-2.8,1.2Zm24,12c-.8,0-1.6-.2-2.2-.7-.7-.4-1.2-1.1-1.5-1.8-.3-.7-.4-1.5-.2-2.3,.2-.8,.5-1.5,1.1-2s1.3-.9,2-1.1c.8-.2,1.6,0,2.3,.2,.7,.3,1.4,.8,1.8,1.5,.4,.7,.7,1.4,.7,2.2s-.4,2.1-1.2,2.8c-.7,.7-1.8,1.2-2.8,1.2Z`,
  ],
}

export const SignOutAlt = {
  prefix: 'fal',
  iconName: 'sign-out-alt',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M25.2,26l-4.6,4.6c-.2,.2-.3,.4-.4,.7-.1,.2-.2,.5-.2,.8s0,.5,.2,.8c.1,.2,.3,.5,.4,.7,.2,.2,.4,.3,.7,.4,.2,.1,.5,.2,.8,.2s.5,0,.8-.2c.2-.1,.5-.3,.7-.4l8-8c.2-.2,.3-.4,.4-.7,.2-.5,.2-1,0-1.5,0-.2-.2-.5-.4-.7l-8-8c-.2-.2-.4-.3-.7-.4-.2-.1-.5-.2-.8-.2s-.5,0-.8,.2c-.2,.1-.5,.2-.7,.4-.2,.2-.3,.4-.4,.7-.1,.2-.2,.5-.2,.8s0,.5,.2,.8c.1,.2,.2,.5,.4,.7l4.6,4.6H6c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4c.4,.4,.9,.6,1.4,.6H25.2Zm-1.2-22c-3.7,0-7.4,1-10.6,3-3.2,2-5.7,4.8-7.4,8.1-.2,.5-.3,1-.1,1.5,.2,.5,.5,.9,1,1.2,.5,.2,1,.3,1.5,.1,.5-.2,.9-.5,1.2-1,1.3-2.6,3.2-4.7,5.6-6.3,2.4-1.6,5.1-2.5,8-2.6,2.8-.1,5.7,.5,8.2,1.8,2.5,1.3,4.6,3.3,6.2,5.7,1.5,2.4,2.3,5.2,2.4,8s-.6,5.7-2,8.2c-1.4,2.5-3.4,4.6-5.8,6-2.5,1.5-5.2,2.2-8.1,2.2-3,0-5.9-.8-8.4-2.4-2.5-1.6-4.6-3.8-5.9-6.5-.2-.5-.7-.8-1.2-1-.5-.2-1.1-.1-1.5,.1-.5,.2-.8,.7-1,1.2-.2,.5-.1,1.1,.1,1.5,1.6,3.2,4,5.9,7,7.8,3,1.9,6.4,3.1,10,3.2,3.6,.2,7.1-.6,10.3-2.3,3.2-1.7,5.8-4.1,7.7-7.2,1.9-3,2.9-6.5,3-10.1,0-3.6-.8-7.1-2.5-10.2-1.7-3.1-4.3-5.7-7.3-7.5-3.1-1.8-6.6-2.8-10.1-2.8Z`,
  ],
}
export const SpinnerThird = {
  prefix: 'fal',
  iconName: 'spinner-third',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M40.6,32.4l-1.6-.9c-.4-.2-.6-.8-.4-1.2,2.3-4.7,2.2-10.2-.4-14.8-2.6-4.5-7.4-7.5-12.6-7.7-.5,0-.9-.4-.9-.9v-1.9c0-.5,.5-1,1-.9,6.5,.3,12.5,4,15.7,9.6,3.3,5.7,3.4,12.6,.5,18.4-.2,.5-.8,.7-1.3,.4Z`,
  ],
}
export const Star = {
  prefix: 'fal',
  iconName: 'star',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M43.9,19.4c-.1-.4-.4-.7-.7-.9-.3-.2-.7-.4-1.1-.4l-11.4-1.7-5.1-10.3c-.2-.3-.4-.6-.7-.8-.3-.2-.7-.3-1.1-.3s-.7,.1-1.1,.3c-.3,.2-.6,.5-.7,.8l-5.1,10.3-11.4,1.7c-.4,0-.7,.2-1,.4-.3,.2-.5,.6-.6,.9-.1,.3-.1,.7,0,1.1,0,.4,.3,.7,.5,.9l8.2,8-2,11.3c0,.4,0,.8,.1,1.1,.1,.4,.4,.7,.7,.9,.3,.2,.7,.3,1,.4,.4,0,.7,0,1.1-.2l10.2-5.3,10.2,5.4c.3,.2,.6,.2,.9,.2,.4,0,.8-.1,1.2-.4,.3-.2,.5-.5,.7-.9,.1-.4,.2-.7,.1-1.1l-2-11.3,8.2-8c.3-.2,.5-.6,.6-.9,.1-.4,.1-.7,0-1.1Zm-12.3,8c-.2,.2-.4,.5-.5,.8-.1,.3-.1,.6,0,1l1.4,8.4-7.5-4c-.3-.2-.6-.2-.9-.2s-.6,0-.9,.2l-7.5,4,1.4-8.4c0-.3,0-.7,0-1-.1-.3-.3-.6-.5-.8l-6-6,8.4-1.2c.3,0,.6-.2,.9-.4,.3-.2,.5-.4,.6-.7l3.6-7.6,3.8,7.6c.1,.3,.4,.5,.6,.7,.3,.2,.6,.3,.9,.4l8.4,1.2-6,6Z`,
  ],
}

export const Stop = {
  prefix: 'fal',
  iconName: 'stop',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M10,14c0-2.2,1.8-4,4-4h20c2.2,0,4,1.8,4,4v20c0,2.2-1.8,4-4,4H14c-2.2,0-4-1.8-4-4V14Zm24,0H14v20h20V14Z`,
  ],
}
export const Sync = {
  prefix: 'fal',
  iconName: 'sync',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M42.5,16.3c-1-2.4-2.5-4.6-4.3-6.5-1.9-1.9-4.1-3.3-6.5-4.3-2.4-1-5-1.5-7.7-1.5-5.1,0-10.1,2-13.8,5.5v-3.5c0-.5-.2-1-.6-1.4-.4-.4-.9-.6-1.4-.6s-1,.2-1.4,.6-.6,.9-.6,1.4V15c0,.5,.2,1,.6,1.4s.9,.6,1.4,.6h9c.5,0,1-.2,1.4-.6,.4-.4,.6-.9,.6-1.4s-.2-1-.6-1.4c-.4-.4-.9-.6-1.4-.6h-4.8c2.2-2.3,5-3.9,8.2-4.6,3.1-.7,6.4-.4,9.3,.8,3,1.2,5.5,3.2,7.3,5.9,1.8,2.6,2.8,5.7,2.8,8.9,0,.5,.2,1,.6,1.4s.9,.6,1.4,.6,1-.2,1.4-.6,.6-.9,.6-1.4c0-2.6-.5-5.2-1.5-7.7Zm-2.7,14.7h-9.1c-.5,0-1,.2-1.4,.6s-.6,.9-.6,1.4,.2,1,.6,1.4,.9,.6,1.4,.6h4.8c-2.2,2.3-5.1,3.9-8.2,4.6-3.1,.7-6.4,.4-9.3-.8-3-1.2-5.5-3.2-7.3-5.9-1.8-2.6-2.8-5.8-2.8-8.9,0-.5-.2-1-.6-1.4-.4-.4-.9-.6-1.4-.6s-1,.2-1.4,.6c-.4,.4-.6,.9-.6,1.4,0,3.9,1.2,7.7,3.3,11s5.2,5.8,8.8,7.4c3.6,1.5,7.6,2,11.4,1.3,3.8-.7,7.4-2.5,10.2-5.2v3.5c0,.5,.2,1,.6,1.4s.9,.6,1.4,.6,1-.2,1.4-.6,.6-.9,.6-1.4v-9c0-.5-.2-1-.6-1.4-.4-.4-.9-.6-1.4-.6Z`,
  ],
}
export const Stopwatch = {
  prefix: 'fal',
  iconName: 'stopwatch',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M36.6,17.2l1.8-1.8c.4-.4,.6-.9,.6-1.4s-.2-1-.6-1.4c-.4-.4-.9-.6-1.4-.6s-1,.2-1.4,.6l-1.8,1.8c-2.8-2.2-6.2-3.3-9.8-3.3s-7,1.2-9.8,3.3l-1.8-1.8c-.4-.4-.9-.6-1.4-.6-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4,0,.5,.2,1,.6,1.4l1.8,1.8c-2.2,2.8-3.4,6.2-3.4,9.8,0,2.6,.6,5.1,1.8,7.3,1.2,2.3,2.9,4.2,4.9,5.7,2.1,1.5,4.5,2.5,7,2.8,2.5,.4,5.1,.1,7.5-.7,2.4-.8,4.6-2.2,6.4-4.1,1.8-1.8,3.1-4.1,3.8-6.5,.7-2.4,.9-5,.4-7.5-.5-2.5-1.5-4.9-3.1-6.9Zm-12.6,21.8c-2.4,0-4.7-.7-6.7-2-2-1.3-3.5-3.2-4.4-5.4-.9-2.2-1.1-4.6-.7-6.9,.5-2.3,1.6-4.5,3.3-6.1,1.7-1.7,3.8-2.8,6.1-3.3,2.3-.5,4.7-.2,6.9,.7,2.2,.9,4.1,2.4,5.4,4.4,1.3,2,2,4.3,2,6.7s-1.3,6.2-3.5,8.5c-2.3,2.3-5.3,3.5-8.5,3.5Zm-4-30h8c.5,0,1-.2,1.4-.6,.4-.4,.6-.9,.6-1.4s-.2-1-.6-1.4c-.4-.4-.9-.6-1.4-.6h-8c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4c.4,.4,.9,.6,1.4,.6Zm6,12c0-.5-.2-1-.6-1.4-.4-.4-.9-.6-1.4-.6s-1,.2-1.4,.6c-.4,.4-.6,.9-.6,1.4v3.8c-.5,.4-.8,.9-.9,1.5-.1,.6-.1,1.2,.1,1.8,.2,.6,.6,1.1,1.1,1.4,.5,.3,1.1,.5,1.7,.5s1.2-.2,1.7-.5c.5-.3,.9-.8,1.1-1.4,.2-.6,.3-1.2,.1-1.8-.1-.6-.5-1.1-.9-1.5v-3.8Z`,
  ],
}
export const ScheduleIcon = {
  prefix: 'fal',
  iconName: 'schedule',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M33.4,32.5h-2.7v-2.5c0-.4-.2-.8-.5-1.1-.6-.6-1.7-.6-2.3,0-.3,.3-.5,.7-.5,1.1v4.1c0,.4,.2,.8,.5,1.1,.3,.3,.7,.5,1.1,.5h4.3c.4,0,.8-.2,1.1-.5,.3-.3,.5-.7,.5-1.1s-.2-.8-.5-1.1c-.3-.3-.7-.5-1.1-.5Zm-15.7,7.4h-7c-.3,0-.5-.2-.5-.5V15.3h27.5v5.5c.9,.6,1.8,1.3,2.6,2,.4,.4,.8,.8,1.1,1.3V12.4c0-2.1-1.7-3.8-3.8-3.8h-3.8V4.6c0-.5-.4-.9-.9-.9h-3.1c-.5,0-.9,.4-.9,.9v4.1h-10V4.6c0-.5-.4-.9-.9-.9h-3.1c-.5,0-.9,.4-.9,.9v4.1h-3.8c-2.1,0-3.8,1.7-3.8,3.8v27.5c0,2.1,1.7,3.8,3.8,3.8h10.5c-.2-.2-.4-.4-.6-.6-1-1-1.7-2-2.4-3.2Zm23-11.3c-.6-1.4-1.4-2.6-2.5-3.7-1.1-1.1-2.3-1.9-3.7-2.5-3.5-1.4-7.5-1-10.6,1-1.9,1.2-3.3,3-4.2,5.1-.9,2.1-1.1,4.3-.6,6.5,.4,2.2,1.5,4.2,3.1,5.8,1.6,1.6,3.6,2.7,5.8,3.1,.7,.1,1.5,.2,2.2,.2,1.5,0,2.9-.3,4.3-.9,2.1-.9,3.8-2.3,5.1-4.2,1.2-1.9,1.9-4,1.9-6.3s-.3-2.9-.9-4.3Zm-4.8,10c-1.5,1.5-3.5,2.3-5.6,2.3s-3.1-.5-4.4-1.3c-1.3-.9-2.3-2.1-2.9-3.6-.6-1.5-.8-3.1-.5-4.6,.3-1.6,1.1-3,2.2-4.1s2.5-1.9,4.1-2.2c.5-.1,1-.2,1.6-.2,1,0,2.1,.2,3,.6,1.5,.6,2.7,1.6,3.6,2.9,.9,1.3,1.3,2.8,1.3,4.4s-.8,4.1-2.3,5.6Z`,
  ],
}

export const Settings = {
  prefix: 'fal',
  iconName: 'settings',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24,16.55c-4.11,0-7.45,3.34-7.45,7.45s3.34,7.45,7.45,7.45,7.45-3.34,7.45-7.45-3.34-7.45-7.45-7.45Zm0,10.91c-1.9,0-3.45-1.55-3.45-3.45s1.55-3.45,3.45-3.45,3.45,1.55,3.45,3.45-1.55,3.45-3.45,3.45Zm15.29,2.79c.08-.18,.21-.33,.37-.44,.16-.11,.35-.17,.54-.17h.16c1.51,0,2.92-.59,3.98-1.65,1.07-1.06,1.65-2.48,1.65-3.99s-.59-2.92-1.65-3.98c-1.06-1.07-2.48-1.65-3.99-1.65h-.3c-.2,0-.38-.06-.55-.17-.1-.07-.19-.15-.25-.24-.03-.12-.07-.24-.12-.36-.08-.18-.1-.39-.07-.58,.04-.2,.13-.38,.25-.51l.11-.11c.53-.53,.94-1.14,1.22-1.83,.29-.69,.43-1.41,.43-2.16s-.14-1.47-.43-2.16-.7-1.3-1.22-1.83c-.53-.53-1.14-.94-1.83-1.22-1.38-.57-2.94-.57-4.32,0-.69,.29-1.3,.7-1.83,1.22l-.09,.09c-.14,.14-.32,.23-.52,.27-.2,.03-.4,.01-.6-.08-.18-.08-.33-.2-.44-.37-.11-.16-.17-.35-.17-.54v-.16c0-1.48-.6-2.94-1.65-3.98-1.06-1.06-2.48-1.65-3.99-1.65s-2.92,.59-3.98,1.65c-1.06,1.06-1.65,2.48-1.65,3.99v.3c0,.2-.06,.39-.17,.55-.07,.1-.15,.19-.24,.26-.12,.03-.24,.07-.36,.12-.18,.08-.39,.11-.58,.07-.2-.04-.38-.13-.5-.25l-.11-.11c-.53-.53-1.14-.94-1.83-1.22-1.38-.57-2.94-.57-4.32,0-.69,.28-1.3,.7-1.83,1.22-.53,.53-.94,1.14-1.22,1.83-.28,.69-.43,1.41-.43,2.16s.14,1.47,.43,2.16c.29,.69,.7,1.3,1.22,1.83l.1,.1c.14,.14,.23,.32,.27,.52,.04,.2,.01,.4-.07,.58-.02,.04-.03,.08-.05,.12-.07,.19-.19,.35-.36,.47-.16,.12-.36,.18-.51,.19h-.16c-1.51,0-2.92,.59-3.99,1.65-1.06,1.06-1.65,2.48-1.65,3.98s.59,2.92,1.65,3.99,2.48,1.65,3.99,1.65h.3c.19,0,.38,.06,.55,.17,.16,.11,.29,.26,.37,.46,.08,.18,.1,.38,.07,.58-.04,.2-.13,.38-.26,.51l-.1,.11c-.53,.53-.94,1.14-1.22,1.83-.28,.69-.43,1.41-.43,2.16s.14,1.47,.43,2.16c.29,.69,.7,1.3,1.22,1.83,.53,.53,1.14,.94,1.83,1.22,1.37,.57,2.95,.57,4.32,0,.69-.28,1.3-.7,1.83-1.23l.09-.09c.14-.14,.32-.23,.52-.27,.2-.03,.4-.01,.58,.07,.04,.02,.08,.03,.12,.05,.19,.07,.35,.19,.47,.36,.12,.16,.18,.36,.19,.51v.16c0,1.5,.59,2.92,1.65,3.99,1.06,1.06,2.48,1.65,3.98,1.65s2.92-.59,3.99-1.65c1.06-1.07,1.65-2.48,1.65-3.98v-.3c0-.2,.06-.38,.17-.55,.11-.16,.26-.29,.46-.38,.18-.08,.38-.1,.58-.07,.2,.04,.38,.13,.5,.25l.11,.11c.53,.53,1.14,.94,1.83,1.22,1.38,.57,2.95,.57,4.32,0,.69-.28,1.3-.7,1.83-1.22s.94-1.14,1.22-1.83c.29-.69,.43-1.41,.43-2.16s-.14-1.47-.43-2.16c-.28-.69-.7-1.3-1.22-1.83l-.09-.09c-.14-.14-.23-.33-.27-.52-.04-.2-.01-.4,.07-.58v-.02Zm-1.83-3.78c-.81,.54-1.45,1.29-1.83,2.19-.4,.92-.52,1.92-.34,2.9,.18,.99,.64,1.89,1.36,2.62l.11,.11c.15,.15,.27,.33,.35,.53,.08,.2,.13,.42,.13,.63s-.04,.43-.12,.63c-.08,.2-.2,.38-.36,.53s-.33,.27-.54,.36c-.39,.16-.85,.17-1.25,0-.2-.08-.38-.2-.53-.36l-.13-.13c-.72-.7-1.62-1.17-2.6-1.34-.99-.18-1.99-.06-2.89,.34-.9,.38-1.66,1.02-2.2,1.83-.54,.82-.83,1.76-.83,2.75v.31c0,.43-.17,.85-.48,1.16-.61,.61-1.7,.61-2.31,0-.31-.31-.48-.72-.48-1.16v-.21c-.02-1.01-.35-1.97-.93-2.79-.57-.8-1.36-1.41-2.28-1.76-.9-.39-1.89-.5-2.85-.32-.99,.18-1.89,.64-2.62,1.36l-.11,.11c-.15,.15-.33,.27-.53,.36-.4,.16-.85,.17-1.25,0-.2-.08-.38-.2-.53-.36-.15-.15-.27-.33-.35-.53-.08-.2-.12-.41-.12-.63s.04-.43,.12-.63c.08-.2,.2-.38,.36-.54l.12-.12c.7-.72,1.17-1.62,1.35-2.6,.18-.99,.06-1.99-.34-2.89-.39-.9-1.02-1.66-1.83-2.2-.82-.54-1.77-.83-2.75-.83h-.31c-.44,0-.85-.17-1.16-.48-.31-.31-.48-.72-.48-1.16s.17-.85,.48-1.16c.31-.31,.72-.48,1.16-.48h.21c1.01-.02,1.97-.35,2.79-.93,.8-.57,1.41-1.36,1.76-2.28,.38-.9,.5-1.89,.32-2.85-.18-.99-.64-1.89-1.36-2.62l-.11-.11c-.15-.15-.27-.33-.35-.53-.08-.2-.12-.41-.12-.63s.04-.43,.12-.63c.08-.2,.2-.38,.36-.53,.15-.15,.33-.27,.53-.36,.4-.17,.85-.17,1.25,0,.2,.08,.38,.2,.53,.35l.13,.13c.72,.7,1.62,1.17,2.6,1.34,.85,.16,1.72,.09,2.53-.19,.18-.02,.35-.07,.51-.14,.9-.39,1.66-1.02,2.2-1.83,.54-.82,.83-1.76,.83-2.75v-.31c0-.43,.17-.85,.48-1.16,.61-.61,1.7-.61,2.31,0,.3,.31,.48,.73,.48,1.16v.17c0,.98,.29,1.92,.83,2.74,.54,.81,1.3,1.45,2.18,1.83,.92,.4,1.92,.52,2.91,.34,.99-.18,1.89-.64,2.62-1.36l.11-.11c.15-.15,.33-.27,.53-.35,.4-.17,.85-.17,1.25,0,.2,.08,.38,.2,.53,.36,.15,.15,.27,.33,.36,.53,.08,.2,.12,.41,.12,.63s-.04,.43-.12,.63c-.08,.2-.2,.38-.36,.53l-.12,.12c-.7,.72-1.17,1.62-1.35,2.61-.15,.85-.09,1.72,.2,2.53,.02,.17,.07,.35,.14,.51,.38,.9,1.02,1.66,1.83,2.2,.82,.54,1.76,.83,2.75,.83h.31c.44,0,.85,.17,1.16,.48s.48,.72,.48,1.16-.17,.85-.48,1.16-.72,.48-1.16,.48h-.17c-.98,0-1.93,.29-2.74,.83Z`,
  ],
}

export const SystemSettingsIcon = {
  prefix: 'fal',
  iconName: 'system-settings',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M44.2,27.7c-1,0-1.8,.8-1.8,1.8v2.3c0,.2-.3,.2-.4,.2h-11.4c-.7-3-3.3-5.8-6.7-5.8s-5.9,2.8-6.7,5.8H6c-.1,0-.4,0-.4-.2v-11.7c0-1-.8-1.8-1.8-1.8s-1.8,.8-1.8,1.8v11.7c0,2.1,1.7,3.8,4,3.8h11v3.3h-3c-1,0-1.8,.8-1.8,1.8s.8,1.8,1.8,1.8h20.1c1,0,1.8-.8,1.8-1.8s-.8-1.8-1.8-1.8h-3.2v-3.3h11.2c2.3,0,4-1.6,4-3.8v-2.3c0-1-.8-1.8-1.8-1.8Zm-16.9,11.1h-6.8v-4.7c0-.1,0-.3,0-.4,0-1.8,1.5-4.1,3.4-4.1s3.4,2.2,3.4,4.1,0,.2,0,.4v4.7ZM42,5.6H6c-2.2,0-4,1.8-4,4v2.4c0,1,.8,1.8,1.8,1.8s1.8-.8,1.8-1.8v-2.4c0-.2,.2-.4,.4-.4H42c.2,0,.4,.2,.4,.4v11.2c0,1,.8,1.8,1.8,1.8s1.8-.8,1.8-1.8V9.6c0-2.2-1.8-4-4-4ZM10.3,26.5c-1,0-1.8,.8-1.8,1.8,0,1,.8,1.8,1.8,1.8h2.1c1.1,0,2.2-.7,2.5-1.8,.1-.4,.3-.7,.5-1.1,.5-1,.3-2.2-.5-3.1l-1.6-1.6,1.9-1.9,1.5,1.6c.8,.8,2,1,3.1,.5,.3-.2,.7-.3,1-.4,1.1-.4,1.8-1.4,1.8-2.5v-2.2h2.7v2.2c0,1.1,.7,2.1,1.8,2.5,.4,.1,.7,.3,1.1,.5,1,.5,2.3,.3,3.1-.5l1.6-1.6,1.9,1.9-1.7,1.6c-.8,.8-1,2-.5,3,.2,.3,.3,.7,.4,1,.4,1.1,1.4,1.8,2.5,1.8h2.2c1,0,1.8-.8,1.8-1.7,0-1-.8-1.8-1.8-1.8h-1.6c0-.1,0-.2-.1-.3l1.8-1.8c.5-.5,.8-1.2,.8-1.9,0-.7-.3-1.4-.8-1.9l-3.1-3.1c-1-1-2.7-1.1-3.8,0l-1.8,1.8c-.1,0-.2,0-.3-.1v-2.5c0-1.5-1.2-2.7-2.7-2.7h-4.4c-1.5,0-2.7,1.2-2.7,2.7v2.5c0,0-.2,0-.2,0l-1.7-1.8c-.5-.5-1.2-.8-1.9-.8-.7,0-1.4,.3-1.9,.8l-3.1,3.1c-.5,.5-.8,1.2-.8,1.9,0,.7,.3,1.4,.8,1.9l1.7,1.8c0,.1,0,.2-.1,.3h-1.5Z`,
  ],
}

export const StopCircleIcon = {
  prefix: 'fal',
  iconName: 'stop-circle',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M29.3,18h-10.5c-.4,0-.7,.3-.7,.7v10.5c0,.4,.3,.7,.7,.7h10.5c.4,0,.7-.3,.7-.7v-10.5c0-.4-.3-.7-.7-.7ZM24,2C11.9,2,2,11.9,2,24s9.9,22,22,22,22-9.9,22-22S36.1,2,24,2Zm0,40c-9.9,0-18-8.1-18-18S14.1,6,24,6s18,8.1,18,18-8.1,18-18,18Z`,
  ],
}

export const ServiceIcon = {
  prefix: 'fal',
  iconName: 'service',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M43.7,19.8c-.3-.4-.6-.6-1-.8l-3.8-1.2,1.8-3.5c.2-.4,.3-.8,.2-1.2,0-.4-.3-.8-.6-1.1l-4.3-4.3c-.3-.3-.7-.5-1.1-.6-.4,0-.9,0-1.2,.2l-3.5,1.8-1.2-3.8c0-.4-.4-.8-.8-1-.4-.3-.8-.4-1.1-.4h-6c-.4,0-.9,0-1.1,.4-.4,.3-.6,.6-.8,1l-1.2,3.8-3.5-1.8c-.4-.2-.8-.3-1.2-.2-.4,0-.8,.3-1.1,.6l-4.3,4.3c-.3,.3-.5,.7-.6,1.1,0,.4,0,.9,.2,1.2l1.8,3.5-3.8,1.2c-.4,0-.8,.4-1,.8-.3,.4-.4,.8-.4,1.1v6c0,.4,0,.9,.4,1.1,.3,.4,.6,.6,1,.8l3.8,1.2-1.8,3.5c-.2,.4-.3,.8-.2,1.2,0,.4,.3,.8,.6,1.1l4.3,4.3c.3,.3,.7,.5,1.1,.6,.4,0,.9,0,1.2-.2l3.5-1.8,1.2,3.8c0,.4,.4,.8,.8,1,.4,.3,.8,.4,1.1,.4h6c.4,0,.9,0,1.1-.4,.4-.3,.6-.6,.8-1l1.2-3.8,3.5,1.8c.4,.2,.8,.2,1.1,.2s.8-.3,1.1-.6l4.3-4.3c.3-.3,.5-.7,.6-1.1,0-.4,0-.9-.2-1.2l-1.8-3.5,3.8-1.2c.4,0,.8-.4,1-.8,.3-.4,.4-.8,.4-1.1v-6c0-.4,0-.9-.4-1.1h0Zm-3.6,5.7l-2.4,.8c-.6,.2-1.1,.5-1.5,.9-.4,.4-.8,.9-1,1.4s-.3,1.1-.3,1.7,.2,1.1,.5,1.6l1.1,2.3-2.2,2.2-2.2-1.2c-.5-.3-1.1-.4-1.6-.4s-1.1,0-1.7,.3c-.6,.2-1.1,.6-1.4,1s-.7,1-.9,1.4l-.8,2.4h-3.2l-.8-2.4c-.2-.6-.5-1.1-.9-1.5-.4-.4-.9-.8-1.4-1s-1.1-.3-1.7-.3-1.1,.2-1.6,.5l-2.3,1.1-2.2-2.2,1.2-2.2c.3-.5,.4-1.1,.5-1.6,0-.6,0-1.1-.3-1.7-.2-.6-.6-1.1-1-1.4s-1-.7-1.5-.9l-2.4-.8v-3.2l2.4-.8c.6-.2,1.1-.5,1.5-.9,.4-.4,.8-.9,1-1.4s.3-1.1,.3-1.7-.2-1.1-.5-1.6l-1.1-2.2,2.2-2.2,2.2,1.1c.5,.3,1.1,.4,1.6,.5,.6,0,1.1,0,1.7-.3,.6-.2,1.1-.6,1.4-1s.7-1,.9-1.5l.8-2.4h3.2l.8,2.4c.2,.6,.5,1.1,.9,1.5,.4,.4,.9,.8,1.4,1s1.1,.3,1.7,.3,1.1-.2,1.6-.5l2.3-1.1,2.2,2.2-1.2,2.2c-.3,.5-.4,1.1-.4,1.6s0,1.1,.3,1.7c.2,.6,.6,1.1,1,1.4s1,.7,1.4,.9l2.4,.8v3.2Zm-16.1-9.6c-1.6,0-3.2,.5-4.5,1.3-1.3,.9-2.4,2.1-3,3.6-.6,1.4-.8,3.1-.5,4.7,.3,1.5,1.1,3,2.2,4.1s2.6,1.9,4.1,2.2,3.2,.2,4.7-.5c1.4-.6,2.7-1.6,3.6-3,.9-1.3,1.3-2.9,1.3-4.5s-.9-4.2-2.4-5.6c-1.5-1.5-3.5-2.4-5.6-2.4Zm2.9,10.9c-.8,.8-1.8,1.1-2.9,1.1s-1.5-.2-2.2-.7c-.7-.5-1.1-1.1-1.4-1.8s-.4-1.5-.2-2.3c.2-.8,.6-1.5,1.1-2.1,.6-.6,1.2-1,2.1-1.1,.8-.2,1.6,0,2.3,.2,.8,.3,1.3,.9,1.8,1.4,.5,.7,.7,1.4,.7,2.2s-.4,2.1-1.1,2.9h0Z',
  ],
}
export const SyncIcon = {
  prefix: 'fal',
  iconName: 'ldap-sync',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24.1,19.8c-2.2,0-4,1.8-4,4s1.8,4,4,4,4-1.8,4-4-1.8-4-4-4Zm-.2,15.1l-5.1-3.4c-1.3-.9-3.1,0-3.1,1.7v1.4h-5c-1.5,0-2.7-1.2-2.7-2.7V15.8c0-1.5,1.2-2.7,2.7-2.7h5.8c1.1,0,2-.9,2-2s-.9-2-2-2h-5.8c-3.7,0-6.7,3-6.7,6.7V32c0,3.7,3,6.7,6.7,6.7h5v1.3c0,1.6,1.8,2.6,3.1,1.7l5.1-3.4c1.2-.8,1.2-2.6,0-3.4Zm13.5-25.9h-4.9v-1.1c0-1.6-1.8-2.6-3.1-1.7l-5.1,3.4c-1.2,.8-1.2,2.6,0,3.4l5.1,3.4c1.3,.9,3.1,0,3.1-1.7v-1.6h4.9c1.5,0,2.7,1.2,2.7,2.7V32c0,1.5-1.2,2.7-2.7,2.7h-5.8c-1.1,0-2,.9-2,2s.9,2,2,2h5.8c3.7,0,6.7-3,6.7-6.7V15.8c0-3.7-3-6.7-6.7-6.7Z`,
  ],
}

export const SNMPTrapIcon = {
  prefix: 'fal',
  iconName: 'snmp-trap-settings',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M43.9,25.4c-.4-.9-4.9-10.1-6.2-12.6-.6-1.2-1.7-2.4-3.3-2.9-.3-1.6-1.3-2.3-1.9-2.6-1.6-.7-3.5,0-4.6,1.2-.6,.7-.6,1.6-.3,2.3l.3,.8c-.8,.9-1.2,2-1.3,3.2h-5c0-1.2-.5-2.2-1.2-3.1l.4-.8h0c.2-.8,.3-2.1-.8-2.9-1.1-.8-3-1.5-4.5-.7-.7,.4-1.5,1.1-1.7,2.6-1.6,.5-2.6,1.8-3.3,2.9-1.4,2.5-5.8,11.7-6.2,12.6-1.2,1.6-1.9,3.6-1.9,5.8,0,5.5,4.5,10,10,10s8.1-2.9,9.5-6.8h4.5c1.3,3.9,5.1,6.8,9.5,6.8s10-4.5,10-10-.7-4.1-1.9-5.8ZM13.4,14.5c.8-1.5,1.7-1.6,2.9-1.3,.7,.2,1.6,.6,1.7,1.8,0,1,.2,4.8,.4,8.2-1.7-1.3-3.8-2.1-6.1-2.1s-1.6,.1-2.4,.3c1.3-2.8,2.8-5.8,3.5-7Zm-1.1,23.2c-3.6,0-6.5-2.9-6.5-6.5s2.9-6.5,6.5-6.5,6.5,2.9,6.5,6.5-2.9,6.5-6.5,6.5Zm13.6-8.5c-.1,.5-.2,1.1-.2,1.7h-3.5c0-.6,0-1.1-.2-1.7h0c0-.1,0-.7,0-1.7h3.9c0,1,0,1.6,0,1.7h0Zm.2-5.2h-4.2c0-1.9-.2-4-.2-5.7h4.7c0,1.7-.2,3.8-.2,5.7Zm3.9-9c0-1.2,1-1.6,1.7-1.8,1.2-.3,2.1-.2,2.9,1.3,.7,1.2,2.1,4.2,3.5,7-.8-.2-1.6-.3-2.4-.3-2.3,0-4.4,.8-6.1,2.1,.1-3.4,.3-7.2,.4-8.2Zm5.7,22.7c-3.6,0-6.5-2.9-6.5-6.5s2.9-6.5,6.5-6.5,6.5,2.9,6.5,6.5-2.9,6.5-6.5,6.5Z',
  ],
}
export const Table = {
  prefix: 'fal',
  iconName: 'table',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M4,11c0-2.21,1.79-4,4-4H40c2.21,0,4,1.79,4,4v26c0,2.21-1.79,4-4,4H8c-2.21,0-4-1.79-4-4V11Zm18,0H8v6h14v-6Zm4,0v6h14v-6h-14Zm14,10h-14v6h14v-6Zm0,10h-14v6h14v-6Zm-18,6v-6H8v6h14ZM8,27h14v-6H8v6Z`,
  ],
}
export const ThumbsDown = {
  prefix: 'fal',
  iconName: 'thumbs-down',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M2.6,27.7c.2,.8,.7,1.6,1.2,2.3,.6,.6,1.3,1.2,2.1,1.5,.8,.4,1.6,.5,2.5,.5h9.3v6c0,2.1,.8,4.1,2.3,5.7,1.5,1.5,3.5,2.3,5.7,2.3s1.5-.5,1.8-1.2l7.5-16.8h4.7c1.6,0,3.1-.6,4.2-1.8,1.1-1.1,1.8-2.6,1.8-4.2V8c0-1.6-.6-3.1-1.8-4.2-1.1-1.1-2.6-1.8-4.2-1.8H11.1c-1.4,0-2.8,.5-3.9,1.4-1.1,.9-1.8,2.2-2,3.7L2.4,25.1c-.1,.9,0,1.7,.2,2.6ZM35.7,6h4c.5,0,1,.2,1.4,.6,.4,.4,.6,.9,.6,1.4v14c0,.5-.2,1-.6,1.4-.4,.4-.9,.6-1.4,.6h-4V6Zm-26.6,1.7c0-.5,.3-.9,.7-1.2,.4-.3,.9-.5,1.3-.5H31.7V25.6l-7.2,16.2c-.6-.2-1.2-.5-1.6-1-.7-.7-1.2-1.8-1.2-2.8v-8c0-1.1-.9-2-2-2H8.3c-.3,0-.6,0-.8-.2-.3-.1-.5-.3-.7-.5-.2-.2-.3-.5-.4-.8,0-.3,0-.6,0-.9L9.1,7.7Z`,
  ],
}
export const ThLarge = {
  prefix: 'fal',
  iconName: 'th-large',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M42,4H6c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4V42c0,.5,.2,1,.6,1.4,.4,.4,.9,.6,1.4,.6H42c.5,0,1-.2,1.4-.6s.6-.9,.6-1.4V6c0-.5-.2-1-.6-1.4-.4-.4-.9-.6-1.4-.6ZM22,40H8v-14h14v14Zm0-18H8V8h14v14Zm18,18h-14v-14h14v14Zm0-18h-14V8h14v14Z`,
  ],
}
export const ThumbsUp = {
  prefix: 'fal',
  iconName: 'thumbs-up',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M45.4,20.3c-.2-.8-.7-1.6-1.2-2.3-.6-.6-1.3-1.2-2.1-1.5-.8-.4-1.6-.5-2.5-.5h-9.3v-6c0-2.1-.8-4.1-2.3-5.7-1.5-1.5-3.5-2.3-5.7-2.3s-1.5,.5-1.8,1.2l-7.5,16.8h-4.7c-1.6,0-3.1,.6-4.2,1.8-1.1,1.1-1.8,2.6-1.8,4.2v14c0,1.6,.6,3.1,1.8,4.2,1.1,1.1,2.6,1.8,4.2,1.8h28.6c1.4,0,2.8-.5,3.9-1.4,1.1-.9,1.8-2.2,2-3.7l2.8-18c.1-.9,0-1.7-.2-2.6ZM12.3,42h-4c-.5,0-1-.2-1.4-.6-.4-.4-.6-.9-.6-1.4v-14c0-.5,.2-1,.6-1.4,.4-.4,.9-.6,1.4-.6h4v18Zm26.6-1.7c0,.5-.3,.9-.7,1.2-.4,.3-.9,.5-1.3,.5H16.3V22.4L23.5,6.2c.6,.2,1.2,.5,1.6,1,.7,.7,1.2,1.8,1.2,2.8v8c0,1.1,.9,2,2,2h11.3c.3,0,.6,0,.8,.2,.3,.1,.5,.3,.7,.5,.2,.2,.3,.5,.4,.8,0,.3,0,.6,0,.9l-2.8,18Z`,
  ],
}
export const Times = {
  prefix: 'fal',
  iconName: 'times',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M35.4,32.6l-8.6-8.6,8.6-8.6c.4-.4,.6-.9,.6-1.4s-.2-1-.6-1.4c-.4-.4-.9-.6-1.4-.6s-1,.2-1.4,.6l-8.6,8.6L15.5,12.6c-.4-.4-.9-.6-1.4-.6s-1,.2-1.4,.6c-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4l8.6,8.6-8.6,8.6c-.2,.2-.3,.4-.5,.6-.1,.2-.2,.5-.2,.7s0,.5,.2,.7c.1,.2,.2,.5,.5,.6,.2,.2,.4,.3,.6,.5,.2,.1,.5,.2,.7,.2s.5,0,.7-.2c.2-.1,.5-.2,.6-.5l8.6-8.6,8.6,8.6c.2,.2,.4,.3,.6,.5,.2,.1,.5,.2,.7,.2s.5,0,.7-.2c.2-.1,.5-.2,.6-.5,.2-.2,.3-.4,.5-.6,.1-.2,.2-.5,.2-.7s0-.5-.2-.7h.2c-.1-.2-.2-.5-.5-.6Z`,
  ],
}
export const TimesCircle = {
  prefix: 'fal',
  iconName: 'times-circle',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M31.8,18.8c.1-.2,.2-.5,.2-.8s0-.5-.2-.8c-.1-.2-.2-.5-.4-.6-.2-.2-.4-.3-.6-.4s-.5-.2-.8-.2-.5,0-.8,.2c-.2,.1-.5,.2-.6,.4l-4.5,4.5-4.5-4.5c-.4-.4-.9-.6-1.4-.6s-1,.2-1.4,.6c-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4l4.5,4.5-4.5,4.5c-.2,.2-.3,.4-.4,.6-.1,.2-.2,.5-.2,.8s0,.5,.2,.8c.1,.2,.2,.5,.4,.6,.2,.2,.4,.3,.6,.4s.5,.2,.8,.2,.5,0,.8-.2c.2-.1,.5-.2,.6-.4l4.5-4.5,4.5,4.5c.2,.2,.4,.3,.6,.4,.2,.1,.5,.2,.8,.2s.5,0,.8-.2,.5-.2,.6-.4c.2-.2,.3-.4,.4-.6,.1-.2,.2-.5,.2-.8s0-.5-.2-.8c-.1-.2-.2-.5-.4-.6l-4.5-4.5,4.5-4.5c.2-.2,.3-.4,.4-.6Zm10.6-2.4c-1-2.4-2.5-4.6-4.4-6.4-1.8-1.9-4-3.4-6.4-4.4-2.4-1-5-1.6-7.6-1.6s-5.2,.5-7.7,1.5-4.6,2.5-6.5,4.3-3.3,4.1-4.3,6.5-1.5,5-1.5,7.7,.6,5.2,1.6,7.6c1,2.4,2.5,4.6,4.4,6.4,1.8,1.9,4,3.4,6.4,4.4,2.4,1,5,1.6,7.6,1.6,2.6,0,5.2-.5,7.7-1.5,2.4-1,4.6-2.5,6.5-4.3,1.9-1.9,3.3-4.1,4.3-6.5,1-2.4,1.5-5,1.5-7.7,0-2.6-.6-5.2-1.6-7.6Zm-3.8,13.6c-.8,1.9-2,3.7-3.4,5.1-2.6,2.6-6,4.2-9.6,4.6-3.6,.4-7.3-.6-10.3-2.6s-5.3-5.1-6.3-8.5c-1.1-3.5-.9-7.3,.5-10.6,1.4-3.4,3.9-6.2,7.1-7.9,3.2-1.7,6.9-2.3,10.5-1.6,3.6,.7,6.8,2.6,9.1,5.5,2.3,2.8,3.6,6.4,3.6,10,0,2.1-.4,4.1-1.2,6.1Z`,
  ],
}
export const Trash = {
  prefix: 'fal',
  iconName: 'trash',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M41.4,12.6c-.4-.4-.9-.6-1.4-.6h-8v-2c0-1.6-.7-3.1-1.7-4.3-1.1-1.1-2.7-1.7-4.3-1.7h-4c-1.6,0-3.1,.7-4.3,1.7-1.1,1.1-1.7,2.7-1.7,4.3v2H8c-.6,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4,.9,.6,1.4,.6h2v22c0,1.6,.7,3.1,1.7,4.3,1.1,1.1,2.7,1.7,4.3,1.7h16c1.6,0,3.1-.7,4.3-1.7,1.1-1.1,1.7-2.7,1.7-4.3V16h2c.6,0,1-.2,1.4-.6,.4-.4,.6-.9,.6-1.4s-.2-1-.6-1.4Zm-21.4-2.6c0-.6,.2-1,.6-1.4,.4-.4,.9-.6,1.4-.6h4c.6,0,1,.2,1.4,.6,.4,.4,.6,.9,.6,1.4v2h-8v-2Zm14,28c0,.6-.2,1-.6,1.4s-.9,.6-1.4,.6H16c-.6,0-1-.2-1.4-.6-.4-.4-.6-.9-.6-1.4V16h20v22Z`,
  ],
}
export const TrashAlt = {
  prefix: 'fal',
  iconName: 'trash-alt',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M28,36c.6,0,1-.2,1.4-.6s.6-.9,.6-1.4v-12c0-.6-.2-1-.6-1.4s-.9-.6-1.4-.6-1,.2-1.4,.6-.6,.9-.6,1.4v12c0,.6,.2,1,.6,1.4s.9,.6,1.4,.6Zm-8,0c.6,0,1-.2,1.4-.6s.6-.9,.6-1.4v-12c0-.6-.2-1-.6-1.4s-.9-.6-1.4-.6-1,.2-1.4,.6-.6,.9-.6,1.4v12c0,.6,.2,1,.6,1.4s.9,.6,1.4,.6ZM41.4,12.6c-.4-.4-.9-.6-1.4-.6h-8v-2c0-1.6-.7-3.1-1.7-4.3-1.1-1.1-2.7-1.7-4.3-1.7h-4c-1.6,0-3.1,.7-4.3,1.7-1.1,1.1-1.7,2.7-1.7,4.3v2H8c-.6,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4,.9,.6,1.4,.6h2v22c0,1.6,.7,3.1,1.7,4.3,1.1,1.1,2.7,1.7,4.3,1.7h16c1.6,0,3.1-.7,4.3-1.7,1.1-1.1,1.7-2.7,1.7-4.3V16h2c.6,0,1-.2,1.4-.6,.4-.4,.6-.9,.6-1.4s-.2-1-.6-1.4Zm-21.4-2.6c0-.6,.2-1,.6-1.4s.9-.6,1.4-.6h4c.6,0,1,.2,1.4,.6,.4,.4,.6,.9,.6,1.4v2h-8v-2Zm14,28c0,.6-.2,1-.6,1.4s-.9,.6-1.4,.6H16c-.6,0-1-.2-1.4-.6-.4-.4-.6-.9-.6-1.4V16h20v22Z`,
  ],
}
export const TestCredentialIcon = {
  prefix: 'fal',
  iconName: 'test-credentials',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M38.2,23.4c-1,0-1.9,.3-2.6,.7l-3.8-3.5c.4-.9,.6-1.9,.6-2.9s-.1-1.5-.3-2.2l2.2-1.7c.9,.6,1.9,1,3.1,1,3,0,5.4-2.4,5.4-5.4s-2.4-5.4-5.4-5.4-5.4,2.4-5.4,5.4,0,.8,.1,1.2l-2.1,1.6c-1.3-1.2-3.1-1.9-5-1.9s-3.6,.7-4.9,1.9l-2.3-1.4c.7-2.6-.4-5.4-2.8-6.8h0c-2.9-1.8-6.7-.8-8.5,2.1-.9,1.4-1.1,3.1-.7,4.7s1.4,3,2.8,3.8c1,.6,2.1,.9,3.2,.9s2.8-.5,3.9-1.4l2.2,1.3c-.2,.7-.4,1.5-.4,2.3,0,1.5,.5,3,1.3,4.1l-7.4,6.5c-.5-.2-1.1-.3-1.6-.3-3,0-5.4,2.4-5.4,5.4s2.4,5.4,5.4,5.4,5.4-2.4,5.4-5.4-.3-1.9-.7-2.7l7.4-6.5c.2,.1,.5,.2,.7,.3l.7,8.4c-2.3,.9-3.9,3.1-3.9,5.8s2.8,6.2,6.2,6.2,6.2-2.8,6.2-6.2-2-5.3-4.6-6l-.6-7.9c1.1-.2,2.1-.7,2.9-1.3l3.7,3.5c-.2,.5-.3,1.1-.3,1.7,0,3,2.4,5.4,5.4,5.4s5.4-2.4,5.4-5.4-2.4-5.4-5.4-5.4Zm-.8-15.6c.8,0,1.5,.7,1.5,1.5s-.7,1.5-1.5,1.5-1.5-.7-1.5-1.5,.7-1.5,1.5-1.5Zm-12.4,6.3c1.9,0,3.5,1.6,3.5,3.5s-1.6,3.5-3.5,3.5-3.5-1.6-3.5-3.5,1.6-3.5,3.5-3.5Zm-14.4-2.9c-.5-.3-.9-.8-1-1.4-.1-.6,0-1.2,.3-1.7,.4-.7,1.2-1.1,2-1.1s.8,.1,1.2,.3c.5,.3,.9,.8,1,1.4,.1,.6,0,1.2-.3,1.7-.3,.5-.8,.9-1.4,1-.6,.1-1.2,0-1.7-.3Zm-.9,23.8c-.8,0-1.5-.7-1.5-1.5s.7-1.5,1.5-1.5,1.5,.7,1.5,1.5-.7,1.5-1.5,1.5Zm15.7,6.1c-1.3,0-2.3-1-2.3-2.3s1-2.3,2.3-2.3,2.3,1,2.3,2.3-1,2.3-2.3,2.3Zm12.7-10.8c-.8,0-1.5-.7-1.5-1.5s.7-1.5,1.5-1.5,1.5,.7,1.5,1.5-.7,1.5-1.5,1.5Z`,
  ],
}
export const TopologyIcon = {
  prefix: 'fal',
  iconName: 'topology',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M44.2,33h-3.1v-2.9c0-3.8-1.9-5.8-5.5-5.8h-5.3v-2.5c0-1-.8-1.8-1.8-1.8h-2.7v-4.9h3.4c1,0,1.8-.8,1.8-1.8V4c0-1-.8-1.8-1.8-1.8h-10.2c-1,0-1.8,.8-1.8,1.8V13.3c0,1,.8,1.8,1.8,1.8h3.3v4.9h-2.5c-1,0-1.8,.8-1.8,1.8v2.5h-5.4c-3.7,0-5.5,1.9-5.5,5.8v2.9H3.8c-1,0-1.8,.8-1.8,1.8v9.2c0,1,.8,1.8,1.8,1.8H14c1,0,1.8-.8,1.8-1.8v-9.2c0-1-.8-1.8-1.8-1.8h-3.3v-2.9c0-2,.4-2.2,2-2.2h5.4v1.7c0,1,.8,1.8,1.8,1.8h8.6c1,0,1.8-.8,1.8-1.8v-1.7h5.3c1.6,0,2,.2,2,2.2v2.9h-3.6c-1,0-1.8,.8-1.8,1.8v9.2c0,1,.8,1.8,1.8,1.8h10.2c1,0,1.8-.8,1.8-1.8v-9.2c0-1-.8-1.8-1.8-1.8Zm-32,3.5v5.7H5.5v-5.7h6.7ZM20.8,11.5V5.8h6.7v5.7h-6.7Zm6,16.2h-5.1v-4.3h5.1v4.3Zm15.7,14.4h-6.7v-5.7h6.7v5.7Z`,
  ],
}

export const TrapViewerScreenIcon = {
  prefix: 'fal',
  iconName: 'trap-viewer',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M43.9,25.4c-.4-.9-4.9-10.1-6.2-12.6-.6-1.2-1.7-2.4-3.3-2.9-.3-1.6-1.3-2.3-1.9-2.6-1.6-.7-3.5,0-4.6,1.2-.6,.7-.6,1.6-.3,2.3l.3,.8c-.8,.9-1.2,2-1.3,3.2h-5c0-1.2-.5-2.2-1.2-3.1l.4-.8h0c.2-.8,.3-2.1-.8-2.9-1.1-.8-3-1.5-4.5-.7-.7,.4-1.5,1.1-1.7,2.6-1.6,.5-2.6,1.8-3.3,2.9-1.4,2.5-5.8,11.7-6.2,12.6-1.2,1.6-1.9,3.6-1.9,5.8,0,5.5,4.5,10,10,10s8.1-2.9,9.5-6.8h4.5c1.3,3.9,5.1,6.8,9.5,6.8s10-4.5,10-10-.7-4.1-1.9-5.8ZM13.4,14.5c.8-1.5,1.7-1.6,2.9-1.3,.7,.2,1.6,.6,1.7,1.8,0,1,.2,4.8,.4,8.2-1.7-1.3-3.8-2.1-6.1-2.1s-1.6,.1-2.4,.3c1.3-2.8,2.8-5.8,3.5-7Zm-1.1,23.2c-3.6,0-6.5-2.9-6.5-6.5s2.9-6.5,6.5-6.5,6.5,2.9,6.5,6.5-2.9,6.5-6.5,6.5Zm13.6-8.5c-.1,.5-.2,1.1-.2,1.7h-3.5c0-.6,0-1.1-.2-1.7h0c0-.1,0-.7,0-1.7h3.9c0,1,0,1.6,0,1.7h0Zm.2-5.2h-4.2c0-1.9-.2-4-.2-5.7h4.7c0,1.7-.2,3.8-.2,5.7Zm3.9-9c0-1.2,1-1.6,1.7-1.8,1.2-.3,2.1-.2,2.9,1.3,.7,1.2,2.1,4.2,3.5,7-.8-.2-1.6-.3-2.4-.3-2.3,0-4.4,.8-6.1,2.1,.1-3.4,.3-7.2,.4-8.2Zm5.7,22.7c-3.6,0-6.5-2.9-6.5-6.5s2.9-6.5,6.5-6.5,6.5,2.9,6.5,6.5-2.9,6.5-6.5,6.5Z`,
  ],
}
export const TourIcon = {
  prefix: 'fal',
  iconName: 'tour',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M38,4H18c-1.6,0-3.1,.6-4.2,1.8-1.1,1.1-1.8,2.7-1.8,4.2v2h-2c-1.6,0-3.1,.6-4.2,1.8-1.1,1.1-1.8,2.7-1.8,4.2v20c0,1.6,.6,3.1,1.8,4.2,1.1,1.1,2.7,1.8,4.2,1.8H30c1.6,0,3.1-.6,4.2-1.8,1.1-1.1,1.8-2.7,1.8-4.2v-2h2c1.6,0,3.1-.6,4.2-1.8,1.1-1.1,1.8-2.7,1.8-4.2V10c0-1.6-.6-3.1-1.8-4.2-1.1-1.1-2.7-1.8-4.2-1.8Zm-6,34c0,.5-.2,1-.6,1.4-.4,.4-.9,.6-1.4,.6H10c-.5,0-1-.2-1.4-.6-.4-.4-.6-.9-.6-1.4v-14H32v14Zm0-18H8v-2c0-.5,.2-1,.6-1.4,.4-.4,.9-.6,1.4-.6H30c.5,0,1,.2,1.4,.6,.4,.4,.6,.9,.6,1.4v2Zm8,10c0,.5-.2,1-.6,1.4-.4,.4-.9,.6-1.4,.6h-2v-14c0-.7-.1-1.4-.4-2h4.4v14Zm0-18H16v-2c0-.5,.2-1,.6-1.4,.4-.4,.9-.6,1.4-.6h20c.5,0,1,.2,1.4,.6,.4,.4,.6,.9,.6,1.4v2Z`,
  ],
}

export const TrafficIcon = {
  prefix: 'fal',
  iconName: 'traffic',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M10,16h23.2l-4.6,4.6c-.8,.8-.8,2,0,2.8,.8,.8,2,.8,2.8,0h0l8-8c.8-.8,.8-2,0-2.8h0l-8-8c-.8-.8-2-.8-2.8,0-.8,.8-.8,2,0,2.8h0l4.6,4.6H10c-1.1,0-2,.9-2,2s.9,2,2,2Zm28,16H14.8l4.6-4.6c.8-.8,.8-2,0-2.8-.8-.8-2-.8-2.8,0l-8,8c-.8,.8-.8,2,0,2.8h0l8,8c.8,.8,2,.8,2.8,0,.8-.8,.8-2,0-2.8h0l-4.6-4.6h23.2c1.1,0,2-.9,2-2s-.9-2-2-2Z',
  ],
}

export const TreeViewIcon = {
  prefix: 'fal',
  iconName: 'tree-view',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M44,30h-4v-6c0-.5-.2-1-.6-1.4s-.9-.6-1.4-.6h-12v-4h4c.5,0,1-.2,1.4-.6,.4-.4,.6-.9,.6-1.4V4c0-.5-.2-1-.6-1.4-.4-.4-.9-.6-1.4-.6h-12c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4v12c0,.5,.2,1,.6,1.4,.4,.4,.9,.6,1.4,.6h4v4H10c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4v6H4c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4v12c0,.5,.2,1,.6,1.4,.4,.4,.9,.6,1.4,.6h12c.5,0,1-.2,1.4-.6,.4-.4,.6-.9,.6-1.4v-12c0-.5-.2-1-.6-1.4-.4-.4-.9-.6-1.4-.6h-4v-4h24v4h-4c-.5,0-1,.2-1.4,.6s-.6,.9-.6,1.4v12c0,.5,.2,1,.6,1.4s.9,.6,1.4,.6h12c.5,0,1-.2,1.4-.6s.6-.9,.6-1.4v-12c0-.5-.2-1-.6-1.4s-.9-.6-1.4-.6Zm-30,4v8H6v-8H14Zm6-20V6h8V14h-8Zm22,28h-8v-8h8v8Z`,
  ],
}

export const TaskManagerIcon = {
  prefix: 'fal',
  iconName: 'task-manager',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M32.6,21.6c-.9-.6-2-.4-2.6,.5l-6.7,9.8c-.2,.2-.6,.3-.8,0,0,0,0,0,0,0l-4-3.9c-.8-.7-2-.7-2.7,0-.7,.7-.7,1.9,0,2.6l4,3.9c.8,.8,1.9,1.2,3.1,1.2h.3c1.2,0,2.4-.7,3.1-1.7,0,0,0,0,0,0l6.7-9.8c.5-.8,.4-1.8-.2-2.5,0,0,0,0-.1-.1h-.1Zm6.4,9.6c1,0,1.9-.8,1.9-1.9V13.6c0-3.7-3-6.7-6.7-6.7h-3.8v-2.1c0-1-.8-1.9-1.9-1.9h-8.4c-1,0-1.9,.9-1.9,1.9v2.1h-3.8c-3.7,0-6.7,3-6.7,6.7v24.3c0,3.7,3,6.7,6.7,6.7h19.8c3.7,0,6.7-3,6.7-6.7,0-1-.8-1.9-1.8-1.9-1,0-1.9,.8-1.9,1.9,0,1.6-1.3,2.9-2.9,2.9H14.4c-1.6,0-2.9-1.3-2.9-2.9V13.6c0-1.6,1.3-2.9,2.9-2.9h3.8v1.5c0,1.5,.5,2.7,1.6,3.6,1.1,1,2.6,1.5,4.5,1.5,4.5,0,6.1-2.6,6.1-5.1v-1.5h3.8c1.6,0,2.9,1.3,2.9,2.9v15.9c0,1,.8,1.9,1.9,1.9ZM26.6,12.1c0,.4,0,1.4-2.3,1.4s-2.3-.2-2.3-1.3V6.6h4.6v5.5Z`,
  ],
}

export const TaskSettingsIcon = {
  prefix: 'fal',
  iconName: 'task-settings',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M32.4,27.2h-1.9c-.1-.5-.3-.9-.5-1.3l1.3-1.3c.8-.8,.8-2,0-2.8-.8-.8-2-.8-2.8,0l-1.3,1.3c-.4-.2-.8-.4-1.3-.5v-1.9c0-1.1-.9-2-2-2s-2,.9-2,2v1.9c-.5,.1-.9,.3-1.3,.5l-1.3-1.3c-.8-.8-2-.8-2.8,0-.8,.8-.8,2,0,2.8l1.3,1.3c-.2,.4-.4,.8-.5,1.3h-1.9c-1.1,0-2,.9-2,2s.9,2,2,2h1.9c.1,.5,.3,.9,.5,1.3l-1.3,1.3c-.8,.8-.8,2,0,2.8,.4,.4,.9,.6,1.4,.6s1-.2,1.4-.6l1.3-1.3c.4,.2,.8,.4,1.3,.5v1.9c0,1.1,.9,2,2,2s2-.9,2-2v-1.9c.5-.1,.9-.3,1.3-.5l1.3,1.3c.4,.4,.9,.6,1.4,.6s1-.2,1.4-.6c.8-.8,.8-2,0-2.8l-1.3-1.3c.2-.4,.4-.8,.5-1.3h1.9c1.1,0,2-.9,2-2s-.9-2-2-2Zm-8.5,4.9c-1.6,0-2.9-1.3-2.9-2.9s1.3-2.9,2.9-2.9,2.9,1.3,2.9,2.9-1.3,2.9-2.9,2.9ZM35.9,5.2h-1.7v-1.2c0-1.1-.9-2-2-2H15.8c-1.1,0-2,.9-2,2v1.2h-1.7c-2.7,0-4.9,2.2-4.9,4.9v31c0,2.7,2.2,4.9,4.9,4.9h23.8c2.7,0,4.9-2.2,4.9-4.9V10.1c0-2.7-2.2-4.9-4.9-4.9Zm-18.1,.8h12.5v2.8h-2.7c-1,0-1.8,.8-2,1.7-.1,.9-.8,1.5-1.7,1.5s-1.6-.6-1.7-1.5c-.1-1-1-1.7-2-1.7h-2.5v-2.8Zm19.1,35.1c0,.5-.4,1-1,1H12.1c-.5,0-1-.4-1-1V10.1c0-.5,.4-1,1-1h1.7v1.6c0,1.1,.9,2,2,2h3c.9,1.9,2.9,3.3,5.1,3.3s4.2-1.3,5.1-3.3h3.2c1.1,0,2-.9,2-2v-1.6h1.7c.5,0,1,.4,1,1v31Z`,
  ],
}
export const Undo = {
  prefix: 'fal',
  iconName: 'undo',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M36.6,12.1c-6.5-6.5-17.4-6.5-23.9,0l-2.2,2.2v-3.6c0-1.1-.9-2-2-2s-2,.9-2,2v8.4c0,1.1,.9,2,2,2h8.4c1.1,0,2-.9,2-2s-.9-2-2-2h-3.6l2.2-2.2c5-5,13.3-5,18.3,0,5,5,5,13.3,0,18.3-5,5-13.3,5-18.3,0s-2-.8-2.8,0c-.8,.8-.8,2,0,2.8,6.5,6.5,17.4,6.5,23.9,0,6.5-6.5,6.5-17.4,0-23.9Z`,
  ],
}
export const UnlockAlt = {
  prefix: 'fal',
  iconName: 'unlock-alt',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M36.6,12c-6.5-6.5-17.4-6.5-23.9,0l-2.8-2.8s0,0,0,0h0c-.4-.4-.8-.6-1.3-.6-1.1,0-2,.9-2,2v8.4c0,1.1,.9,2,2,2h8.4c1.1,0,2-.9,2-2s-.2-.9-.5-1.3h0c0-.1,0-.2-.1-.2l-2.7-2.7c5-5,13.3-5,18.3,0s5,13.3,0,18.3-13.3,5-18.3,0c-1.7-1.7-2.4-.7-3.2,.1s-.4,1.9,.4,2.7c6.5,6.5,17.4,6.5,23.9,0s6.5-17.4,0-23.9Z`,
  ],
}
export const Upload = {
  prefix: 'fal',
  iconName: 'upload',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M40.2,19.8c-1.1-1.1-2.7-1.8-4.2-1.8h-4c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4c.4,.4,.9,.6,1.4,.6h4c.5,0,1,.2,1.4,.6,.4,.4,.6,.9,.6,1.4v14c0,.5-.2,1-.6,1.4-.4,.4-.9,.6-1.4,.6H12c-.5,0-1-.2-1.4-.6s-.6-.9-.6-1.4v-14c0-.5,.2-1,.6-1.4,.4-.4,.9-.6,1.4-.6h4c.5,0,1-.2,1.4-.6,.4-.4,.6-.9,.6-1.4s-.2-1-.6-1.4-.9-.6-1.4-.6h-4c-1.6,0-3.1,.6-4.2,1.8-1.1,1.1-1.8,2.7-1.8,4.2v14c0,1.6,.6,3.1,1.8,4.2,1.1,1.1,2.7,1.8,4.2,1.8h24c1.6,0,3.1-.6,4.2-1.8,1.1-1.1,1.8-2.7,1.8-4.2v-14c0-1.6-.6-3.1-1.8-4.2Zm-22.2-5.7c.5,0,1-.2,1.4-.6l2.6-2.6v23.2c0,.5,.2,1,.6,1.4,.4,.4,.9,.6,1.4,.6s1-.2,1.4-.6c.4-.4,.6-.9,.6-1.4V10.8l2.6,2.6c.2,.2,.4,.3,.7,.4,.2,.1,.5,.2,.8,.2s.5,0,.8-.2c.2-.1,.5-.3,.7-.4,.2-.2,.3-.4,.4-.7,.1-.2,.2-.5,.2-.8s0-.5-.2-.8c-.1-.2-.3-.5-.4-.7l-6-6c-.2-.2-.4-.3-.7-.4-.2-.1-.5-.2-.8-.2s-.5,0-.8,.2c-.2,.1-.5,.3-.7,.4l-6,6c-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4,.9,.6,1.4,.6Z`,
  ],
}
export const User = {
  prefix: 'fal',
  iconName: 'user',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24,27.4c6.5,0,11.7-5.2,11.7-11.7s-5.2-11.7-11.7-11.7-11.7,5.2-11.7,11.7,5.2,11.7,11.7,11.7Zm0-19.5c4.3,0,7.8,3.5,7.8,7.8s-3.5,7.8-7.8,7.8-7.8-3.5-7.8-7.8,3.5-7.8,7.8-7.8Zm7.8,23.4h-15.6c-5.4,0-9.7,4.4-9.7,9.7s.8,1.9,1.9,1.9,1.9-.8,1.9-1.9c0-3.2,2.6-5.8,5.8-5.8h15.6c3.2,0,5.8,2.6,5.8,5.8s.8,1.9,1.9,1.9,1.9-.8,1.9-1.9c0-5.4-4.4-9.7-9.7-9.7Z`,
  ],
}
export const UserSettingsIcon = {
  prefix: 'fal',
  iconName: 'user-settings',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M19.7,35.4c-.4-.2-.9-.3-1.3-.5-1.1-.5-2.4-.3-3.3,.6l-2.5,2.5-3.1-3.1,2.4-2.4c.9-.9,1.1-2.2,.6-3.4-.2-.4-.4-.9-.6-1.4-.4-1.2-1.5-1.9-2.7-1.9h-3.4v-4.4h3.3c1.2,0,2.4-.8,2.8-2,.2-.5,.4-1,.6-1.4,.5-1.1,.3-2.4-.5-3.3l-2.3-2.3,3.1-3.1,2.3,2.3c.9,.9,2.2,1.1,3.3,.6,.4-.2,.9-.4,1.3-.5,1.2-.4,2-1.5,2-2.8v-3.3h4.4v3.3c0,1.2,.8,2.3,1.9,2.8,.5,.2,1,.4,1.4,.6,1.1,.6,2.5,.3,3.4-.5l2.4-2.3,3.1,3.1-2.4,2.4c-.9,.9-1.1,2.2-.6,3.3,.2,.4,.3,.7,.4,1.1,2.2,.1,4.3,1,5.9,2.3l.4,.3c1.6,1.4,2.8,3.4,3.4,5.5,0-.3,.1-.6,.1-.9v-5.6c0-1.6-1.3-2.9-2.9-2.9h-3.6c-.1-.3-.2-.6-.4-.9l2.6-2.6c.6-.6,.9-1.3,.9-2.1,0-.8-.3-1.5-.8-2.1l-4-4c-.6-.6-1.3-.9-2.1-.9-.8,0-1.5,.3-2.1,.8l-2.5,2.5c-.3-.2-.7-.3-1-.4v-3.5c0-1.6-1.3-2.9-2.9-2.9h-5.6c-1.6,0-2.9,1.3-2.9,2.9v3.4c-.3,.1-.6,.2-.9,.4l-2.4-2.5c-.5-.6-1.3-.9-2.1-.9-.8,0-1.5,.3-2.1,.8l-4,3.9c-1.1,1.1-1.2,3,0,4.1l2.4,2.5c-.2,.3-.3,.7-.4,1h-3.5c-1.6,0-2.9,1.3-2.9,2.9v5.6c0,1.6,1.3,2.9,2.9,2.9h3.6c.1,.3,.3,.6,.4,1l-2.6,2.5c-.6,.6-.9,1.3-.9,2.1,0,.8,.3,1.5,.8,2.1l4,4c1.1,1.1,3,1.1,4.1,0l2.6-2.6c.3,.1,.6,.2,.8,.3v3.7c0,1.5,1.1,2.7,2.5,2.9,0-.3,0-.7,0-1,0-1.9,.4-3.7,1.1-5.4v-.7c0-1.2-.8-2.4-2-2.8Zm20.4-.6c1.2-1.3,2-3,2-4.9,0-3.9-3.2-7.1-7.1-7.1s-7.1,3.2-7.1,7.1,.7,3.6,2,4.9c-3.4,1.8-5.7,5.4-5.7,9.5,0,.5,.2,.9,.5,1.3,.3,.3,.8,.5,1.3,.5h18.1c.5,0,.9-.2,1.3-.5,.3-.3,.5-.8,.5-1.3,0-4.1-2.3-7.6-5.7-9.5Zm-5.1-8.4c2,0,3.6,1.6,3.6,3.6s-1.6,3.5-3.5,3.6c0,0-.1,0-.2,0-1.9,0-3.5-1.6-3.5-3.6s1.6-3.6,3.6-3.6Zm-7.1,16.1c.8-3.1,3.6-5.4,6.9-5.5,0,0,0,0,0,0,0,0,0,0,0,0s0,0,0,0c0,0,0,0,0,0,3.3,0,6.1,2.4,6.9,5.5h-14.1Zm-3.6-12.6c0-.6,0-1.2,.2-1.7-.2,0-.5,0-.7,0-2.6,0-4.7-2.1-4.7-4.7s2.1-4.7,4.7-4.7,3.7,1.2,4.4,3c.9-.8,2-1.4,3.1-1.8-1.3-2.8-4.2-4.7-7.5-4.7s-8.3,3.7-8.3,8.3,3.7,8.3,8.3,8.3,.5,0,.7,0c-.1-.6-.2-1.2-.2-1.8Z`,
  ],
}
export const VirtualizationTopologyIcon = {
  prefix: 'fal',
  iconName: 'virtualization-topology',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M10.9,27.8c0,.7,.6,1.3,1.3,1.3s1.3-.6,1.3-1.3-.6-1.3-1.3-1.3-1.3,.6-1.3,1.3Zm-3.1-7.7v-3.1c0-.7,.5-1.2,1.2-1.2H43.4l-.5-1c0-.1,0-.2-.1-.3,0,0,0-.1,0-.2l-4.8-7.7c-.9-1.5-2.5-2.4-4.2-2.4H13.8c-1.7,0-3.3,.9-4.2,2.4l-4.9,7.8v.2h0c-.4,.7-.6,1.6-.6,2.4v3.1c0,.8,.2,1.6,.6,2.3,.2,.3,.3,.6,.5,.8-.2,.3-.4,.5-.5,.8-.4,.7-.6,1.5-.6,2.3v3.1c0,.8,.2,1.6,.6,2.3,.2,.3,.3,.6,.5,.8-.2,.3-.4,.5-.5,.8-.4,.7-.6,1.5-.6,2.3v3.1c0,2.8,2.2,5,5,5h7.4v-3.8h-7.4c-.7,0-1.2-.5-1.2-1.2v-3.1c0-.7,.5-1.2,1.2-1.2h7.4v-3.8h-7.4c-.7,0-1.2-.5-1.2-1.2v-3.1c0-.7,.5-1.2,1.2-1.2h7.4v-3.8h-7.4c-.7,0-1.2-.5-1.2-1.2Zm5-11.5h0c.2-.4,.6-.6,1-.6h19.8c.4,0,.8,.2,1,.6l2.1,3.4H10.7l2.1-3.4Zm29.3,9.9h-14.5c-1.1,0-1.9,.9-1.9,1.9v4.9h-4.7c-1.1,0-1.9,.9-1.9,1.9v14.5c0,1.1,.9,1.9,1.9,1.9h14.5c1.1,0,1.9-.9,1.9-1.9v-4.9h4.7c1.1,0,1.9-.9,1.9-1.9v-14.5c0-1.1-.9-1.9-1.9-1.9Zm-8.6,21.4h-10.7v-10.7h2.8v5.8c0,1.1,.9,1.9,1.9,1.9h6v3Zm0-6.8h-4v-3.8h4v3.8Zm6.6,0h-2.8v-5.8c0-1.1-.9-1.9-1.9-1.9h-6v-3h10.7v10.7ZM10.9,18.5c0,.7,.6,1.3,1.3,1.3s1.3-.6,1.3-1.3-.6-1.3-1.3-1.3-1.3,.6-1.3,1.3Zm0,18.7c0,.7,.6,1.3,1.3,1.3s1.3-.6,1.3-1.3-.6-1.3-1.3-1.3-1.3,.6-1.3,1.3Z`,
  ],
}

export const WidgetViewIcon = {
  prefix: 'fal',
  iconName: 'widget-view',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M44,3.4H26.8c-.3,0-.6,.3-.6,.6v13.3c0,.3,.3,.6,.6,.6h17.2c.3,0,.6-.3,.6-.6V4c0-.3-.3-.6-.6-.6Zm-3.4,10.5h-10.5V7.4h10.5v6.5ZM21.2,30.1H4c-.3,0-.6,.3-.6,.6v13.3c0,.3,.3,.6,.6,.6H21.2c.3,0,.6-.3,.6-.6v-13.3c0-.3-.3-.6-.6-.6Zm-3.4,10.5H7.4v-6.5h10.5v6.5Zm26.1-18.9H26.8c-.3,0-.6,.3-.6,.6v21.7c0,.3,.3,.6,.6,.6h17.2c.3,0,.6-.3,.6-.6V22.3c0-.3-.3-.6-.6-.6Zm-3.4,18.9h-10.5v-15h10.5v15ZM21.2,3.4H4c-.3,0-.6,.3-.6,.6V25.7c0,.3,.3,.6,.6,.6H21.2c.3,0,.6-.3,.6-.6V4c0-.3-.3-.6-.6-.6Zm-3.4,18.9H7.4V7.4h10.5v15Z',
  ],
}

export const WindowRestore = {
  prefix: 'fal',
  iconName: 'window-restore',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M40.2,4H15.2c-2.1,0-3.8,1.7-3.8,3.8v3.8h-3.8c-2.1,0-3.8,1.7-3.8,3.8v25c0,2.1,1.7,3.8,3.8,3.8h25c2.1,0,3.8-1.7,3.8-3.8v-3.8h3.8c2.1,0,3.8-1.7,3.8-3.8V7.8c0-2.1-1.7-3.8-3.8-3.8Zm-7.5,36.2H7.8V24h25v16.2Zm7.5-7.5h-3.8V15.2c0-2.1-1.7-3.8-3.8-3.8H15.2v-3.8h25v25Z`,
  ],
}

// template icons
export const RTT = {
  prefix: 'fal',
  iconName: 'rtt',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M26.14,26.86h-9l2.06-1.71c.57-.47,.68-1.37,.26-2-.43-.63-1.23-.76-1.8-.29l-5.14,4.29c-.32,.27-.51,.69-.51,1.13,0,.45,.18,.87,.5,1.14l4.97,4.29c.23,.2,.51,.3,.79,.3,.38,0,.76-.19,1.02-.55,.44-.62,.33-1.52-.23-2l-2-1.73h9.11c.71,0,1.29-.64,1.29-1.43s-.58-1.43-1.29-1.43Zm9.35-8.27l-4.97-4.29c-.56-.48-1.37-.37-1.8,.25-.44,.62-.33,1.52,.23,2l2,1.73h-7.82c-.71,0-1.29,.64-1.29,1.43s.58,1.43,1.29,1.43h7.71l-2.06,1.71c-.57,.47-.68,1.37-.26,2,.25,.37,.64,.57,1.03,.57,.27,0,.54-.09,.77-.29l5.14-4.29c.32-.27,.51-.69,.51-1.13,0-.45-.18-.87-.5-1.14ZM24,4C12.97,4,4,12.97,4,24s8.97,20,20,20,20-8.97,20-20S35.03,4,24,4Zm0,36c-8.82,0-16-7.18-16-16S15.18,8,24,8s16,7.18,16,16-7.18,16-16,16Z`,
  ],
}

export const Network = {
  prefix: 'fal',
  iconName: 'network',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M39.98,32.01c-1.24,0-2.45,.4-3.46,1.12l-4.9-2.9c.23-.72,.35-1.47,.36-2.22,0-1.77-.6-3.48-1.68-4.88-1.09-1.4-2.6-2.39-4.31-2.84v-4.64c1.33-.47,2.46-1.4,3.17-2.62,.72-1.22,.98-2.65,.74-4.05-.24-1.39-.96-2.66-2.05-3.57-1.08-.91-2.45-1.41-3.87-1.41s-2.78,.5-3.87,1.41c-1.08,.91-1.81,2.18-2.05,3.57-.24,1.39,.02,2.83,.74,4.05,.72,1.22,1.84,2.15,3.17,2.62v4.64c-1.71,.44-3.23,1.44-4.31,2.84-1.09,1.4-1.68,3.11-1.68,4.88,0,.75,.13,1.5,.36,2.22l-4.9,2.9c-1.01-.72-2.22-1.11-3.46-1.12-1.19,0-2.35,.35-3.33,1.01-.99,.66-1.75,1.6-2.21,2.69-.45,1.1-.57,2.3-.34,3.47,.23,1.16,.8,2.23,1.64,3.07,.84,.84,1.91,1.41,3.07,1.64,1.16,.23,2.37,.11,3.47-.34,1.1-.45,2.03-1.22,2.69-2.21,.66-.99,1.01-2.15,1.01-3.33,0-.54-.09-1.08-.24-1.6l4.6-2.74c1.5,1.49,3.53,2.33,5.64,2.33s4.14-.84,5.64-2.33l4.6,2.74c-.34,1.21-.28,2.5,.15,3.67,.43,1.18,1.23,2.19,2.27,2.89,1.04,.7,2.28,1.06,3.53,1.03,1.26-.04,2.47-.47,3.47-1.23,1-.76,1.73-1.82,2.1-3.02s.34-2.49-.06-3.68c-.4-1.19-1.17-2.22-2.2-2.95-1.02-.73-2.25-1.12-3.5-1.11h0Zm-31.99,8c-.4,0-.78-.12-1.11-.34-.33-.22-.58-.53-.74-.9-.15-.37-.19-.77-.11-1.16,.08-.39,.27-.74,.55-1.02,.28-.28,.64-.47,1.02-.55,.39-.08,.79-.04,1.16,.11,.37,.15,.68,.41,.9,.74,.22,.33,.34,.72,.34,1.11,0,.53-.21,1.04-.59,1.41-.37,.38-.88,.59-1.41,.59ZM23.99,8.02c.4,0,.78,.12,1.11,.34,.33,.22,.58,.53,.74,.9,.15,.37,.19,.77,.11,1.16-.08,.39-.27,.74-.55,1.02-.28,.28-.64,.47-1.02,.55s-.79,.04-1.16-.11c-.37-.15-.68-.41-.9-.74-.22-.33-.34-.72-.34-1.11,0-.53,.21-1.04,.59-1.41,.37-.37,.88-.59,1.41-.59h0Zm0,23.99c-.79,0-1.56-.23-2.22-.67-.66-.44-1.17-1.06-1.47-1.79-.3-.73-.38-1.53-.23-2.31,.15-.78,.54-1.49,1.09-2.05s1.27-.94,2.05-1.09c.78-.15,1.58-.07,2.31,.23,.73,.3,1.36,.82,1.79,1.47,.44,.66,.67,1.43,.67,2.22,0,1.06-.42,2.08-1.17,2.83-.75,.75-1.77,1.17-2.83,1.17Zm15.99,8c-.4,0-.78-.12-1.11-.34-.33-.22-.58-.53-.74-.9-.15-.37-.19-.77-.11-1.16,.08-.39,.27-.74,.55-1.02,.28-.28,.64-.47,1.02-.55,.39-.08,.79-.04,1.16,.11,.37,.15,.68,.41,.9,.74,.22,.33,.34,.72,.34,1.11,0,.53-.21,1.04-.59,1.41-.37,.38-.88,.59-1.41,.59Z`,
  ],
}

export const ResponseTime = {
  prefix: 'fal',
  iconName: 'response-time',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24,8c-8.84,0-16,7.16-16,16s7.16,16,16,16,16-7.16,16-16-7.16-16-16-16ZM4,24C4,12.95,12.95,4,24,4s20,8.95,20,20-8.95,20-20,20S4,35.05,4,24ZM24,12c1.1,0,2,.9,2,2v9.17l5.41,5.41c.78,.78,.78,2.05,0,2.83-.78,.78-2.05,.78-2.83,0l-6-6c-.38-.38-.59-.88-.59-1.41V14c0-1.1,.9-2,2-2Z`,
  ],
}

export const NodeCount = {
  prefix: 'fal',
  iconName: 'node-count',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M42.89,18.58c.52,0,1.02-.21,1.39-.58,.37-.37,.58-.87,.58-1.39V4.97c0-.52-.21-1.02-.58-1.39-.37-.37-.87-.58-1.39-.58h-11.52c-.52,0-1.02,.21-1.39,.58-.37,.37-.58,.87-.58,1.39v3.27h-10.8v-3.27c0-.52-.21-1.02-.58-1.39-.37-.37-.87-.58-1.39-.58H5.11c-.52,0-1.02,.21-1.39,.58-.37,.37-.58,.87-.58,1.39v11.64c0,.52,.21,1.02,.58,1.39,.37,.37,.87,.58,1.39,.58h3.59v10.83h-3.59c-.52,0-1.02,.21-1.39,.58-.37,.37-.58,.87-.58,1.39v11.64c0,.52,.21,1.02,.58,1.39,.37,.37,.87,.58,1.39,.58h11.52c.52,0,1.02-.21,1.39-.58,.37-.37,.58-.87,.58-1.39v-3.27h10.8v3.27c0,.52,.21,1.02,.58,1.39,.37,.37,.87,.58,1.39,.58h11.52c.52,0,1.02-.21,1.39-.58,.37-.37,.58-.87,.58-1.39v-11.64c0-.52-.21-1.02-.58-1.39-.37-.37-.87-.58-1.39-.58h-3.59v-10.83h3.59ZM7.08,14.64V6.94h7.58v7.7H7.08Zm7.58,26.42H7.08v-7.7h7.58v7.7Zm14.74-9.67v4.43h-10.8v-4.43c0-.52-.21-1.02-.58-1.39-.37-.37-.87-.58-1.39-.58h-3.94v-10.83h3.94c.52,0,1.02-.21,1.39-.58,.37-.37,.58-.87,.58-1.39v-4.43h10.8v4.43c0,.52,.21,1.02,.58,1.39,.37,.37,.87,.58,1.39,.58h3.94v10.83h-3.94c-.52,0-1.02,.21-1.39,.58-.37,.37-.58,.87-.58,1.39Zm11.52,1.97v7.7h-7.58v-7.7h7.58Zm-7.58-18.71V6.94h7.58v7.7h-7.58Z`,
  ],
}

export const Messages = {
  prefix: 'fal',
  iconName: 'messages',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M38,8H10c-1.59,0-3.12,.63-4.24,1.76-1.13,1.13-1.76,2.65-1.76,4.24v20c0,1.59,.63,3.12,1.76,4.24,1.13,1.13,2.65,1.76,4.24,1.76h28c1.59,0,3.12-.63,4.24-1.76,1.13-1.13,1.76-2.65,1.76-4.24V14c0-1.59-.63-3.12-1.76-4.24-1.13-1.13-2.65-1.76-4.24-1.76Zm-.82,4l-11.76,11.76c-.19,.19-.41,.34-.65,.44-.24,.1-.51,.15-.77,.15s-.53-.05-.77-.15c-.24-.1-.46-.25-.65-.44L10.82,12h26.36Zm2.82,22c0,.53-.21,1.04-.59,1.41s-.88,.59-1.41,.59H10c-.53,0-1.04-.21-1.41-.59-.38-.38-.59-.88-.59-1.41V14.82l11.76,11.76c1.12,1.12,2.65,1.75,4.24,1.75s3.11-.63,4.24-1.75l11.76-11.76v19.18Z`,
  ],
}

export const Processes = {
  prefix: 'fal',
  iconName: 'processes',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M14,29c-.26,0-.52,.05-.77,.15-.24,.1-.46,.25-.65,.43-.19,.19-.33,.41-.43,.65-.1,.24-.15,.5-.15,.77v3.56c-2.58-2.92-4-6.67-4-10.56,0-.59,.03-1.18,.11-1.76,.03-.26,.01-.53-.06-.78-.07-.25-.19-.49-.35-.7-.16-.21-.36-.38-.59-.51-.23-.13-.48-.21-.74-.24-.26-.03-.53-.01-.78,.06-.25,.07-.49,.19-.7,.35-.21,.16-.38,.36-.51,.59-.13,.23-.21,.48-.24,.74-.09,.74-.14,1.49-.13,2.24,0,4.77,1.72,9.39,4.83,13h-2.83c-.53,0-1.04,.21-1.41,.59-.38,.37-.59,.88-.59,1.41s.21,1.04,.59,1.41c.38,.37,.88,.59,1.41,.59h8c.31,0,.62-.08,.9-.23,.28-.15,.51-.36,.69-.61,.02-.03,.05-.05,.07-.09,.01-.02,.01-.04,.03-.06,.1-.16,.17-.34,.21-.52,.03-.1,.05-.21,.06-.31,0-.06,.04-.12,.04-.18v-8c0-.26-.05-.52-.15-.77-.1-.24-.25-.46-.43-.65-.19-.19-.41-.33-.65-.43-.24-.1-.5-.15-.77-.15h0Zm3-17h-3.56c2.92-2.58,6.67-4,10.56-4,.59,0,1.18,.03,1.76,.11,.26,.03,.53,.01,.78-.06,.25-.07,.49-.19,.7-.35,.21-.16,.38-.36,.51-.59,.13-.23,.21-.48,.24-.74,.03-.26,.01-.53-.06-.78s-.19-.49-.35-.7c-.16-.21-.36-.38-.59-.51-.23-.13-.48-.21-.74-.24-.74-.09-1.49-.13-2.24-.13-4.77,0-9.39,1.72-13,4.83v-2.83c0-.53-.21-1.04-.59-1.41-.38-.38-.88-.59-1.41-.59s-1.04,.21-1.41,.59c-.38,.38-.59,.88-.59,1.41v8c.01,.13,.04,.26,.08,.39h0c.05,.25,.14,.48,.28,.69l.02,.03c.13,.19,.3,.36,.49,.49,.02,.02,.03,.04,.06,.06,.03,.02,.06,.02,.09,.04,.11,.07,.23,.12,.36,.17,.17,.06,.35,.09,.52,.11,.03,0,.06,.02,.1,.02h8c.53,0,1.04-.21,1.41-.59,.38-.38,.59-.88,.59-1.41s-.21-1.04-.59-1.41-.88-.59-1.41-.59h0Zm23.92,21.61c-.05-.25-.14-.48-.28-.69l-.02-.03c-.13-.19-.3-.36-.49-.49-.02-.02-.03-.04-.06-.06-.02-.01-.05-.01-.07-.03-.25-.14-.53-.23-.82-.27-.06,0-.12-.04-.18-.04h-8c-.53,0-1.04,.21-1.41,.59s-.59,.88-.59,1.41,.21,1.04,.59,1.41c.37,.38,.88,.59,1.41,.59h3.56c-2.92,2.58-6.67,4-10.56,4-.59,0-1.18-.03-1.76-.11-.53-.06-1.06,.08-1.48,.41-.42,.33-.69,.81-.75,1.33-.06,.53,.08,1.06,.41,1.48,.33,.42,.81,.69,1.33,.75,.74,.09,1.49,.13,2.24,.13,4.77,0,9.39-1.72,13-4.83v2.83c0,.53,.21,1.04,.59,1.41,.37,.37,.88,.59,1.41,.59s1.04-.21,1.41-.59c.37-.38,.59-.88,.59-1.41v-8c-.01-.13-.04-.26-.08-.39h0Zm1.08-22.61c.53,0,1.04-.21,1.41-.59,.37-.38,.59-.88,.59-1.41s-.21-1.04-.59-1.41c-.37-.38-.88-.59-1.41-.59h-8c-.13,.01-.26,.04-.38,.08h-.01c-.24,.05-.47,.15-.68,.28l-.04,.03c-.19,.13-.36,.3-.49,.49-.02,.02-.04,.03-.05,.05-.01,.02-.01,.05-.03,.07-.14,.26-.24,.54-.27,.83,0,.06-.04,.11-.04,.18v8c0,.53,.21,1.04,.59,1.41,.37,.38,.88,.59,1.41,.59s1.04-.21,1.41-.59c.37-.38,.59-.88,.59-1.41v-3.57c2.58,2.92,4,6.67,4,10.57,0,.59-.03,1.18-.11,1.76-.06,.53,.08,1.06,.41,1.47s.81,.69,1.33,.75c.08,0,.16,.01,.24,.01,.49,0,.96-.18,1.32-.5,.37-.32,.6-.77,.66-1.26,.09-.74,.14-1.49,.13-2.24,0-4.77-1.72-9.39-4.83-13h2.83Z`,
  ],
}

export const ReconnectCount = {
  prefix: 'fal',
  iconName: 'reconnect-count',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24,4c-5.13,.01-10.05,2-13.76,5.54v-3.54c0-.53-.21-1.04-.59-1.41-.38-.38-.88-.59-1.41-.59s-1.04,.21-1.41,.59c-.38,.38-.59,.88-.59,1.41V15c0,.53,.21,1.04,.59,1.41,.38,.38,.88,.59,1.41,.59h9c.53,0,1.04-.21,1.41-.59,.38-.38,.59-.88,.59-1.41s-.21-1.04-.59-1.41c-.38-.38-.88-.59-1.41-.59h-4.8c2.57-2.69,6-4.39,9.69-4.83,3.69-.43,7.42,.43,10.55,2.45,3.12,2.02,5.45,5.06,6.57,8.61,1.12,3.55,.98,7.37-.42,10.82-1.39,3.45-3.94,6.31-7.21,8.08-3.27,1.77-7.06,2.35-10.71,1.63-3.65-.72-6.94-2.68-9.29-5.56-2.36-2.88-3.64-6.48-3.63-10.2,0-.53-.21-1.04-.59-1.41-.38-.37-.88-.59-1.41-.59s-1.04,.21-1.41,.59c-.38,.37-.59,.88-.59,1.41,0,3.96,1.17,7.82,3.37,11.11,2.2,3.29,5.32,5.85,8.98,7.37,3.65,1.51,7.68,1.91,11.56,1.14,3.88-.77,7.44-2.68,10.24-5.47,2.8-2.8,4.7-6.36,5.47-10.24,.77-3.88,.38-7.9-1.14-11.56-1.51-3.65-4.08-6.78-7.37-8.98-3.29-2.2-7.16-3.37-11.11-3.37Zm0,12c-.53,0-1.04,.21-1.41,.59-.37,.38-.59,.88-.59,1.41v6c0,.53,.21,1.04,.59,1.41s.88,.59,1.41,.59h4c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41-.21-1.04-.59-1.41-.88-.59-1.41-.59h-2v-4c0-.53-.21-1.04-.59-1.41-.37-.38-.88-.59-1.41-.59Z`,
  ],
}

export const Queue = {
  prefix: 'fal',
  iconName: 'queue',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M14.6,10.48h19.46c1.1,0,2-.9,2-2s-.9-2-2-2H14.6c-1.1,0-2,.9-2,2s.88,2,2,2Zm0,7.24h19.46c1.1,0,2-.9,2-2s-.9-2-2-2H14.6c-1.1,0-2,.9-2,2s.88,2,2,2Zm0,7.24h19.46c1.1,0,2-.9,2-2s-.9-2-2-2H14.6c-1.1,0-2,.9-2,2s.88,2,2,2Zm25.38-6.1v15.9c0,1.1-.54,1.8-1.96,1.8H10.1c-1.02,0-1.98-.18-1.98-1.94v-15.76c-2.64-.1-4.12,1.06-4.12,3.66v15.18c0,2.6,1.38,3.82,4.42,3.82h31.46c3.5,0,4.12-1.96,4.12-4v-15.28c0-1.18-.32-3.38-4.02-3.38Zm-25.38,13.32h19.46c1.1,0,2-.9,2-2s-.9-2-2-2H14.6c-1.1,0-2,.9-2,2s.88,2,2,2Z`,
  ],
}

export const Sessions = {
  prefix: 'fal',
  iconName: 'sessions',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M19.11,22.98c0-1-.8-1.8-1.8-1.8H5.8c-1-.02-1.8,.8-1.8,1.8s.8,1.8,1.8,1.8h11.49c1,0,1.82-.8,1.82-1.8Zm.25,5.64c0-1-.8-1.8-1.8-1.8H8.24c-1,0-1.8,.8-1.8,1.8s.8,1.8,1.8,1.8h9.33c1,0,1.8-.8,1.8-1.8ZM29.8,11.38v-3.05h1.36c1,0,1.8-.8,1.8-1.8s-.8-1.82-1.8-1.82h-6.33c-1,0-1.8,.8-1.8,1.8s.8,1.8,1.8,1.8h1.36v3.07c-3.31,.38-6.44,1.76-8.93,4.02-.75,.67-.8,1.82-.13,2.55,.67,.75,1.82,.8,2.55,.13,2.27-2.05,5.22-3.18,8.29-3.18,6.84,0,12.4,5.56,12.4,12.4s-5.56,12.4-12.4,12.4c-4.44,0-8.56-2.4-10.78-6.27-.47-.87-1.58-1.18-2.45-.67-.87,.49-1.16,1.6-.67,2.45,2.85,4.98,8.18,8.09,13.91,8.09,8.84,0,16.02-7.18,16.02-16,0-8.22-6.22-15-14.2-15.91Zm5.49,12.95c.75-.65,.82-1.8,.16-2.55-.65-.75-1.8-.82-2.55-.16l-5.51,4.84-2.02-2.07c-.71-.71-1.84-.73-2.55-.04-.71,.69-.73,1.84-.04,2.55l4.42,4.51,8.07-7.07Z`,
  ],
}

export const Memory = {
  prefix: 'fal',
  iconName: 'memory',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M40,4H14.09c-.57,0-1.11,.24-1.49,.66l-6.09,6.77c-.33,.37-.51,.84-.51,1.34v29.23c0,1.1,.9,2,2,2H40c1.1,0,2-.9,2-2V6c0-1.1-.9-2-2-2Zm-2,36H10V13.53l4.98-5.53h3.02v4c0,1.1,.9,2,2,2s2-.9,2-2v-4h2v4c0,1.1,.9,2,2,2s2-.9,2-2v-4h2v4c0,1.1,.9,2,2,2s2-.9,2-2v-4h4V40Zm-8-20h-12c-1.1,0-2,.9-2,2v12c0,1.1,.9,2,2,2h12c1.1,0,2-.9,2-2v-12c0-1.1-.9-2-2-2Zm-2,12h-8v-8h8v8Z`,
  ],
}

export const Database = {
  prefix: 'fal',
  iconName: 'database',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M16.67,31.87c-.36,0-.72,.1-1.02,.3-.3,.19-.54,.47-.68,.79-.14,.32-.18,.67-.1,1.01,.07,.34,.25,.65,.5,.9s.58,.41,.94,.48c.36,.07,.72,.03,1.06-.1,.34-.13,.62-.36,.82-.64,.2-.29,.31-.63,.31-.97,0-.46-.19-.91-.54-1.24s-.81-.51-1.3-.51ZM24,6.5c-7.33,0-14.67,2.4-14.67,7v21c0,4.6,7.33,7,14.67,7s14.67-2.4,14.67-7V13.5c0-4.6-7.33-7-14.67-7Zm11,28c0,1.24-4.18,3.5-11,3.5s-11-2.26-11-3.5v-5.72c3.43,1.56,7.2,2.32,11,2.22,3.8,.1,7.57-.67,11-2.22v5.72Zm0-10.5c0,1.24-4.18,3.5-11,3.5s-11-2.26-11-3.5v-5.72c3.43,1.56,7.2,2.32,11,2.22,3.8,.1,7.57-.67,11-2.22v5.72Zm-11-7c-6.82,0-11-2.26-11-3.5s4.18-3.5,11-3.5,11,2.26,11,3.5-4.18,3.5-11,3.5Zm-7.33,4.38c-.36,0-.72,.1-1.02,.29-.3,.19-.54,.47-.68,.79-.14,.32-.18,.67-.1,1.01,.07,.34,.25,.65,.5,.9s.58,.41,.94,.48c.36,.07,.72,.03,1.06-.1s.62-.36,.82-.64c.2-.29,.31-.63,.31-.97,0-.46-.19-.91-.54-1.24-.34-.33-.81-.51-1.3-.51Z`,
  ],
}

export const Cache = {
  prefix: 'fal',
  iconName: 'cache',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M9.07,36.67c-.95,0-1.72,.77-1.72,1.72s.77,1.72,1.72,1.72,1.72-.77,1.72-1.72-.77-1.72-1.72-1.72ZM45.04,16.76l-2.19-2.19c-.74-.74-1.79-1.01-2.75-.77,.23-.97-.04-2.01-.77-2.75l-4.75-4.75c-.44-.44-.99-.71-1.58-.81-.1-.58-.38-1.14-.81-1.58l-1.49-1.49c-1.14-1.14-2.99-1.14-4.12,0l-14.92,14.92c-1.14,1.14-1.14,2.98,0,4.12l3.18,3.18c.3,.3,.44,.71,.4,1.13-.05,.42-.28,.79-.63,1.02-9.47,6.04-10.36,6.93-10.74,7.31-2.62,2.62-2.62,6.89,0,9.51,2.62,2.62,6.89,2.62,9.51,0,.38-.38,1.27-1.27,7.31-10.74,.23-.35,.6-.58,1.02-.63,.42-.05,.83,.1,1.13,.39l3.18,3.18c1.14,1.14,2.98,1.14,4.12,0l14.92-14.92c1.14-1.14,1.14-2.98,0-4.12Zm-16.98,16.98l-3.18-3.18c-.92-.92-2.21-1.37-3.51-1.23-1.3,.14-2.46,.86-3.16,1.96-2.68,4.2-6.05,9.38-6.91,10.25-1.49,1.49-3.9,1.49-5.38,0-1.49-1.49-1.49-3.9,0-5.38,.86-.86,6.05-4.23,10.25-6.91,1.1-.7,1.82-1.86,1.96-3.16,.14-1.3-.31-2.59-1.23-3.51l-3.18-3.18,2.32-2.32,14.34,14.34-2.32,2.32Zm4.38-4.38l-14.34-14.34L28.64,4.48l1.49,1.49-1.66,3.69c-.06,.13-.03,.27,.07,.37h0c.1,.1,.25,.12,.38,.06l3.61-1.74,4.75,4.75-6.35,9.46c-.09,.13-.07,.31,.04,.42h0c.11,.11,.29,.13,.42,.04l9.41-6.39,2.19,2.19-10.54,10.54Z`,
  ],
}

export const Transactions = {
  prefix: 'fal',
  iconName: 'transactions',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M35.42,21.41c.38-.38,.59-.88,.59-1.41s-.21-1.04-.59-1.41c-.38-.38-.88-.59-1.41-.59H10.82l4.6-4.58c.38-.38,.59-.89,.59-1.42s-.21-1.04-.59-1.42c-.38-.38-.89-.59-1.42-.59s-1.04,.21-1.42,.59L4.58,18.58c-.28,.28-.47,.64-.54,1.03s-.03,.79,.12,1.15c.15,.37,.4,.68,.73,.9,.33,.22,.71,.34,1.11,.34h28.01c.53,0,1.04-.21,1.41-.59Zm8.43,5.83c-.15-.37-.4-.68-.73-.9s-.71-.34-1.11-.34H14c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41s.21,1.04,.59,1.41c.38,.38,.88,.59,1.41,.59h23.18l-4.6,4.58c-.19,.19-.34,.41-.44,.65-.1,.24-.15,.51-.15,.77s.05,.53,.15,.77c.1,.24,.25,.46,.44,.65,.19,.19,.41,.34,.65,.44,.24,.1,.51,.15,.77,.15s.53-.05,.77-.15c.24-.1,.47-.25,.65-.44l8-8c.28-.28,.47-.64,.54-1.03,.07-.39,.03-.79-.12-1.15Z`,
  ],
}

export const Query = {
  prefix: 'fal',
  iconName: 'query',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M23.99,39.99H10c-.53,0-1.04-.21-1.41-.59-.37-.37-.59-.88-.59-1.41V10c0-.53,.21-1.04,.59-1.41,.37-.37,.88-.59,1.41-.59h10v6c0,1.59,.63,3.12,1.76,4.24,1.12,1.12,2.65,1.76,4.24,1.76h6v2c0,.53,.21,1.04,.59,1.41s.88,.59,1.41,.59,1.04-.21,1.41-.59,.59-.88,.59-1.41v-4.12c-.02-.18-.06-.36-.12-.54v-.18c-.1-.21-.22-.39-.38-.56h0L23.49,4.6c-.17-.16-.35-.28-.56-.38-.06,0-.12,0-.18,0-.2-.12-.43-.19-.66-.22H10c-1.59,0-3.12,.63-4.24,1.76s-1.76,2.65-1.76,4.24v27.99c0,1.59,.63,3.12,1.76,4.24,1.12,1.12,2.65,1.76,4.24,1.76h14c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41-.21-1.04-.59-1.41-.88-.59-1.41-.59Zm0-29.17l5.18,5.18h-3.18c-.53,0-1.04-.21-1.41-.59-.37-.37-.59-.88-.59-1.41v-3.18Zm-10,5.18c-.53,0-1.04,.21-1.41,.59-.37,.37-.59,.88-.59,1.41s.21,1.04,.59,1.41c.37,.37,.88,.59,1.41,.59h2c.53,0,1.04-.21,1.41-.59,.37-.37,.59-.88,.59-1.41s-.21-1.04-.59-1.41c-.37-.37-.88-.59-1.41-.59h-2Zm29.41,24.57l-2.34-2.32c.75-1.31,1.05-2.82,.85-4.31-.19-1.49-.87-2.88-1.93-3.95h0c-.97-1.01-2.23-1.7-3.6-1.99-1.37-.28-2.8-.15-4.09,.39-1.29,.54-2.4,1.45-3.16,2.63-.77,1.17-1.16,2.55-1.14,3.95,0,1.21,.3,2.39,.89,3.44,.59,1.05,1.45,1.93,2.49,2.54,1.04,.62,2.22,.95,3.42,.97,1.21,.02,2.4-.28,3.45-.86l2.32,2.34c.19,.19,.41,.34,.65,.44,.24,.1,.51,.15,.77,.15s.53-.05,.77-.15c.24-.1,.46-.25,.65-.44,.19-.19,.34-.41,.44-.65,.1-.24,.15-.51,.15-.77s-.05-.53-.15-.77c-.1-.24-.25-.46-.44-.65h0Zm-6.34-3.5c-.57,.54-1.33,.84-2.11,.84s-1.54-.3-2.11-.84c-.55-.56-.86-1.31-.86-2.1,0-.39,.07-.79,.22-1.15,.15-.36,.37-.69,.66-.97,.53-.53,1.25-.84,2-.86,.4-.02,.81,.03,1.19,.17,.38,.14,.73,.36,1.02,.64s.52,.62,.68,.99c.16,.37,.23,.78,.23,1.18-.02,.79-.35,1.55-.92,2.1h0Zm-11.08-13.08H14c-.53,0-1.04,.21-1.41,.59-.37,.37-.59,.88-.59,1.41s.21,1.04,.59,1.41c.37,.37,.88,.59,1.41,.59h12c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41-.21-1.04-.59-1.41-.88-.59-1.41-.59Zm-4,12c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41-.21-1.04-.59-1.41-.88-.59-1.41-.59h-8c-.53,0-1.04,.21-1.41,.59-.37,.37-.59,.88-.59,1.41s.21,1.04,.59,1.41c.37,.37,.88,.59,1.41,.59h8Z`,
  ],
}

export const LocationPin = {
  prefix: 'fal',
  iconName: 'location-pin',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24,21.58c.79,0,1.56-.23,2.22-.67,.66-.44,1.17-1.06,1.47-1.8,.3-.73,.38-1.54,.23-2.31-.15-.78-.54-1.49-1.09-2.05-.56-.56-1.27-.94-2.05-1.09-.78-.15-1.58-.08-2.31,.23-.73,.3-1.36,.82-1.8,1.47-.44,.66-.67,1.43-.67,2.22,0,1.06,.42,2.08,1.17,2.83,.75,.75,1.77,1.17,2.83,1.17Zm-1.42,13.82c.19,.19,.41,.34,.65,.44,.24,.1,.51,.15,.77,.15s.53-.05,.77-.15c.24-.1,.46-.25,.65-.44l8.18-8.2c1.9-1.9,3.19-4.32,3.72-6.95,.52-2.63,.26-5.37-.77-7.85-1.03-2.48-2.77-4.6-5-6.1-2.23-1.49-4.86-2.29-7.55-2.29s-5.31,.8-7.55,2.29c-2.23,1.49-3.97,3.61-5,6.1-1.03,2.48-1.3,5.21-.77,7.85,.52,2.63,1.82,5.05,3.72,6.95l8.18,8.2ZM14.46,16.66c.14-1.43,.59-2.8,1.33-4.03,.74-1.23,1.74-2.28,2.93-3.07,1.57-1.03,3.4-1.58,5.28-1.58s3.71,.55,5.28,1.58c1.19,.79,2.18,1.83,2.92,3.05,.74,1.22,1.19,2.59,1.33,4,.14,1.42-.03,2.85-.5,4.19-.48,1.34-1.24,2.56-2.25,3.57l-6.78,6.78-6.78-6.78c-1-1-1.77-2.21-2.25-3.55-.48-1.34-.65-2.76-.51-4.17Zm23.54,23.32H10c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41s.21,1.04,.59,1.41c.38,.38,.88,.59,1.41,.59h28c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41-.21-1.04-.59-1.41c-.38-.38-.88-.59-1.41-.59Z`,
  ],
}

export const Secure = {
  prefix: 'fal',
  iconName: 'secure',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M39.26,7.3c-.23-.19-.5-.32-.79-.39-.29-.07-.59-.07-.89,0-2.14,.45-4.34,.45-6.48,.02-2.14-.44-4.17-1.31-5.96-2.56-.33-.23-.73-.36-1.14-.36s-.81,.12-1.14,.36c-1.79,1.25-3.82,2.12-5.96,2.56-2.14,.44-4.35,.43-6.48-.02-.29-.06-.59-.06-.89,0-.29,.07-.56,.2-.79,.39-.23,.19-.42,.43-.55,.7-.13,.27-.19,.57-.19,.86v14.9c0,2.87,.68,5.7,1.99,8.25,1.31,2.55,3.21,4.75,5.55,6.42l7.3,5.2c.34,.24,.74,.37,1.16,.37s.82-.13,1.16-.37l7.3-5.2c2.33-1.67,4.24-3.87,5.55-6.42,1.31-2.55,2-5.38,1.99-8.25V8.86c0-.3-.07-.59-.19-.86-.13-.27-.32-.51-.55-.7Zm-3.26,16.47c0,2.23-.53,4.43-1.55,6.41-1.02,1.98-2.5,3.69-4.31,4.99l-6.14,4.38-6.14-4.38c-1.81-1.3-3.29-3.01-4.31-4.99-1.02-1.98-1.55-4.18-1.55-6.41V11.16c4.19,.36,8.39-.61,12-2.78,3.61,2.17,7.81,3.14,12,2.78v12.6Zm-8.92-4.58l-5.38,5.4-1.78-1.8c-.38-.38-.89-.59-1.42-.59s-1.04,.21-1.42,.59c-.38,.38-.59,.89-.59,1.42s.21,1.04,.59,1.42l3.2,3.2c.19,.19,.41,.34,.65,.44,.24,.1,.51,.15,.77,.15s.53-.05,.77-.15c.24-.1,.46-.25,.65-.44l6.88-6.82c.38-.38,.59-.89,.59-1.42s-.21-1.04-.59-1.42c-.38-.38-.89-.59-1.42-.59s-1.04,.21-1.42,.59l-.08,.02Z`,
  ],
}

export const ActiveConnections = {
  prefix: 'fal',
  iconName: 'active-connections',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M43.41,36.61c-.38-.38-.88-.59-1.41-.59h-12.36c-.3-.84-.78-1.6-1.41-2.23-.63-.63-1.39-1.11-2.23-1.41v-6.36h14c.53,0,1.04-.21,1.41-.59,.38-.38,.59-.88,.59-1.41s-.21-1.04-.59-1.41c-.38-.38-.88-.59-1.41-.59h-2V10.02c0-1.59-.63-3.12-1.76-4.24-1.13-1.13-2.65-1.76-4.24-1.76h-16c-1.59,0-3.12,.63-4.24,1.76s-1.76,2.65-1.76,4.24v12h-2c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41s.21,1.04,.59,1.41c.38,.38,.88,.59,1.41,.59h14v6.36c-.84,.3-1.6,.78-2.23,1.41-.63,.63-1.11,1.39-1.41,2.23H6c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41s.21,1.04,.59,1.41c.38,.38,.88,.59,1.41,.59h12.36c.42,1.16,1.19,2.16,2.2,2.87,1.01,.71,2.21,1.09,3.44,1.09s2.43-.38,3.44-1.09c1.01-.71,1.78-1.71,2.2-2.87h12.36c.53,0,1.04-.21,1.41-.59,.38-.38,.59-.88,.59-1.41s-.21-1.04-.59-1.41ZM14,22.02V10.02c0-.53,.21-1.04,.59-1.41s.88-.59,1.41-.59h16c.53,0,1.04,.21,1.41,.59,.38,.38,.59,.88,.59,1.41v12H14Zm11.41,17.41c-.38,.38-.88,.59-1.41,.59-.4,0-.78-.12-1.11-.34-.33-.22-.59-.53-.74-.9-.15-.37-.19-.77-.11-1.16,.08-.39,.27-.74,.55-1.02,.28-.28,.64-.47,1.02-.55,.39-.08,.79-.04,1.16,.11s.68,.41,.9,.74c.22,.33,.34,.72,.34,1.11,0,.53-.21,1.04-.59,1.41Z`,
  ],
}

export const Code = {
  prefix: 'fal',
  iconName: 'code',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M27.84,9.65c-.98-.26-1.98,.32-2.24,1.3l-6.68,25.16c-.26,.98,.32,1.98,1.3,2.24,.13,.03,.26,.05,.38,.06,.03,0,.07,0,.1,0,.8,0,1.54-.56,1.76-1.38l6.68-25.14c.26-.98-.32-1.98-1.3-2.24ZM6.42,24.07L14.76,15.75c.72-.72,.72-1.88,0-2.6-.72-.72-1.88-.72-2.6,0L2.54,22.77c-.72,.72-.72,1.88,0,2.6l9.82,9.82c.36,.36,.84,.54,1.3,.54s.94-.18,1.28-.54c.72-.72,.72-1.88,0-2.6L6.42,24.07Zm39.04-1.32l-9.62-9.62c-.72-.72-1.88-.72-2.6,0-.72,.72-.72,1.88,0,2.6l8.34,8.34-8.54,8.52c-.72,.72-.72,1.88,0,2.6,.36,.36,.84,.54,1.3,.54s.94-.18,1.3-.54l9.82-9.82c.34-.34,.54-.8,.54-1.3s-.2-.96-.54-1.32Z`,
  ],
}

export const ErrorConnections = {
  prefix: 'fal',
  iconName: 'error-connections',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M33.58,5.79c-1.06,0-1.92,.86-1.92,1.92v4.79c0,1.06,.86,1.92,1.92,1.92s1.92-.86,1.92-1.92V7.71c0-1.06-.86-1.92-1.92-1.92Zm11.5,6.72c0-6.36-5.15-11.51-11.5-11.51-5.01,0-9.27,3.2-10.85,7.67H14.42c-1.53,0-2.99,.61-4.07,1.69-1.08,1.08-1.68,2.54-1.68,4.07v11.51h-1.92c-.51,0-1,.2-1.36,.56-.36,.36-.56,.85-.56,1.36s.2,1,.56,1.36c.36,.36,.85,.56,1.36,.56h13.42v6.1c-.8,.29-1.53,.75-2.14,1.35-.6,.6-1.06,1.33-1.35,2.14H4.83c-.51,0-1,.2-1.36,.56s-.56,.85-.56,1.36,.2,1,.56,1.36c.36,.36,.85,.56,1.36,.56h11.85c.4,1.11,1.14,2.07,2.1,2.75,.97,.68,2.12,1.04,3.3,1.04s2.33-.36,3.3-1.04c.97-.68,1.7-1.64,2.1-2.75h11.84c.51,0,1-.2,1.36-.56,.36-.36,.56-.85,.56-1.36s-.2-1-.56-1.36c-.36-.36-.85-.56-1.36-.56h-11.84c-.29-.8-.75-1.54-1.35-2.14-.6-.6-1.33-1.07-2.14-1.35v-6.1h13.42c.51,0,1-.2,1.36-.56,.36-.36,.56-.85,.56-1.36s-.2-1-.56-1.36c-.36-.36-.85-.56-1.36-.56h-1.92v-2.08c5.44-.91,9.58-5.65,9.58-11.35Zm-21.64,30.13c-.36,.36-.85,.56-1.36,.56-.38,0-.75-.11-1.06-.32-.32-.21-.56-.51-.71-.86-.15-.35-.18-.74-.11-1.11,.07-.37,.26-.71,.52-.98,.27-.27,.61-.45,.98-.53,.37-.07,.76-.04,1.11,.11,.35,.15,.65,.39,.86,.71,.21,.32,.32,.69,.32,1.07,0,.51-.2,1-.56,1.36Zm8.23-16.7H12.5V14.43c0-.51,.2-1,.56-1.36s.85-.56,1.36-.56h7.67c0,5.7,4.14,10.44,9.58,11.35v2.08Zm1.92-5.76c-4.23,0-7.67-3.44-7.67-7.67s3.43-7.67,7.67-7.67,7.67,3.44,7.67,7.67-3.43,7.67-7.67,7.67Zm0-4.81c-1.06,0-1.92,.86-1.92,1.92s.86,1.92,1.92,1.92,1.92-.86,1.92-1.92-.86-1.92-1.92-1.92Z`,
  ],
}

export const TotalIP = {
  prefix: 'fal',
  iconName: 'total-ip',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M25.37,21.17h2.19c1.46,0,2.74-.37,3.65-1.1,.91-.73,1.28-1.83,1.28-2.92,0-.91-.18-1.64-.55-2.19-.37-.73-.91-1.1-1.64-1.46-.73-.37-1.64-.55-2.56-.55h-5.11v12.97h2.74v-4.75Zm0-6.21h2.37c.73,0,1.1,.18,1.46,.55,.37,.37,.55,.91,.55,1.46s-.18,1.1-.55,1.46c-.37,.37-.91,.55-1.64,.55h-2.19v-4.02Zm12.42,14.61c0-.91-.73-1.64-1.64-1.64h-9.68c-.73,0-1.28,.37-1.46,.91-.18,.55-.18,1.28,.37,1.83l4.93,5.3-4.93,5.3c-.37,.55-.55,1.1-.37,1.83,.18,.55,.91,.91,1.46,.91h9.68c.91,0,1.64-.73,1.64-1.64s-.73-1.83-1.64-1.83h-6.03l3.29-3.65c.55-.55,.55-1.64,0-2.19l-3.29-3.65h6.03c.91,.18,1.64-.55,1.64-1.46Zm-1.46-20.64c-3.11-3.11-7.49-4.93-12.24-4.93s-9.5,1.83-12.6,4.93c-2.92,3.11-4.2,7.12-4.02,11.87,.37,7.49,10.23,17.72,14.06,21.19,0-1.28,.55-2.37,1.28-3.29-4.57-4.38-11.69-12.79-12.05-18.08-.18-3.84,.91-6.94,3.11-9.32,2.37-2.56,6.03-4.02,10.05-4.02s7.49,1.46,10.05,4.02c2.37,2.37,3.47,5.66,3.29,9.5,0,1.1-.37,2.56-1.1,3.84,1.1,0,2.19,.37,3.11,1.1,.73-1.64,1.28-3.29,1.28-4.75,.18-4.93-1.28-8.95-4.2-12.05ZM19.34,25.74V12.77h-2.56v12.97h2.56Z`,
  ],
}

export const LeaseIP = {
  prefix: 'fal',
  iconName: 'lease-ip',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M22.64,9.23c-.72-.36-1.63-.54-2.53-.54h-5.07v12.85h2.71v-4.52h2.17c1.45,0,2.71-.36,3.62-1.09,.9-.72,1.27-1.81,1.27-2.9,0-.9-.18-1.63-.54-2.17-.36-.72-.9-1.27-1.63-1.63Zm-1.09,5.25c-.36,.36-.9,.54-1.63,.54h-2.17v-4.16h2.35c.72,0,1.09,.18,1.45,.54s.54,.9,.54,1.45c.18,.72,0,1.27-.54,1.63Zm-.72,18.1l-1.63,.18c-1.09,1.09-1.99,1.99-2.9,2.9C11.96,31.49,3.64,22.26,3.27,16.47c-.18-3.8,.9-6.88,3.08-9.23,2.35-2.53,5.97-3.98,9.95-3.98s7.42,1.45,9.95,3.98c1.81,1.99,2.9,4.52,3.26,7.42l2.17-1.27c.36-.18,.54-.36,.91-.36-.54-3.08-1.81-5.79-3.98-7.96C25.54,1.81,21.19,0,16.49,0S7.08,1.81,4,4.89C1.1,7.96-.16,11.95,.02,16.65c.54,8.87,14.66,21.9,15.2,22.44l1.09,.9,1.09-.9c.36-.36,4.71-4.16,8.69-9.05l-3.62,2.17c-.54,.18-1.09,.36-1.63,.36Zm-8.87-11.04V8.69h-2.71v12.85h2.71Zm23.89-3.98c-.54-.72-1.45-1.09-2.17-.54l-1.63,1.09,1.81,2.71,1.63-.9c.72-.72,.91-1.63,.36-2.35Zm-17.01,8.14l-2.53,3.44,4.16-.54,11.76-7.06-1.81-2.71-11.58,6.88Z`,
  ],
}

export const AvailableIP = {
  prefix: 'fal',
  iconName: 'available-ip',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M19.57,25.54V12.69h-2.71v12.85h2.71Zm16.83-.36l-7.24,6.52-1.81-2.53c-.54-.72-1.63-.9-2.35-.36-.72,.54-.9,1.63-.36,2.35l2.9,4.16c.36,.36,.72,.72,1.27,.72h.18c.36,0,.9-.18,1.09-.36l8.69-7.78c.72-.72,.72-1.81,.18-2.53-.72-.72-1.81-.9-2.53-.18Zm-11.04-4.16h2.17c1.45,0,2.71-.36,3.62-1.09,.9-.72,1.27-1.81,1.27-2.9,0-.9-.18-1.63-.54-2.17-.36-.72-.9-1.27-1.63-1.63-.72-.36-1.63-.54-2.53-.54h-5.07v12.85h2.71v-4.52Zm0-6.15h2.35c.72,0,1.09,.18,1.45,.54s.54,.9,.54,1.45c.18,.72,0,1.27-.54,1.63-.36,.36-.9,.54-1.63,.54h-2.17v-4.16Zm10.86-5.79c-3.08-3.26-7.42-5.07-12.13-5.07s-9.41,1.81-12.49,4.89c-2.9,3.08-4.16,7.06-3.98,11.76,.52,8.41,13.25,20.57,15.02,22.26,.1,.09,.16,.15,.19,.18l1.09,.9,1.09-.9s.1-.09,.21-.2c.63-.6,2.55-2.4,4.86-4.87-.36,.18-.9,.18-1.27,.18h-.54c-.72-.18-1.45-.36-1.99-.9-.9,.9-1.63,1.63-2.35,2.17-4.34-3.98-12.67-13.21-13.03-19-.18-3.8,.9-6.88,3.08-9.23,2.35-2.53,5.97-3.98,9.95-3.98s7.42,1.45,9.95,3.98c2.35,2.35,3.44,5.61,3.26,9.41,0,.54-.18,1.27-.36,1.99,1.09-.18,2.17,0,3.26,.72,.03-.13,.05-.26,.08-.39,.15-.73,.28-1.38,.28-2.15,.18-4.71-1.27-8.69-4.16-11.76Z`,
  ],
}

export const Requests = {
  prefix: 'fal',
  iconName: 'requests',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M12,4c-4.41,0-8,3.59-8,8,0,3.72,2.56,6.82,6,7.72v22.28c0,1.11,.9,2,2,2s2-.89,2-2V19.72c3.44-.89,6-4,6-7.72,0-4.41-3.59-8-8-8Zm0,12c-2.21,0-4-1.79-4-4s1.79-4,4-4,4,1.79,4,4-1.79,4-4,4Zm26,12.28v-12.28c0-1.6-.62-3.11-1.76-4.24s-2.64-1.76-4.24-1.76h-6c-1.1,0-2,.9-2,2s.9,2,2,2h6c.54,0,1.04,.21,1.41,.58,.38,.38,.59,.88,.59,1.42v12.28c-3.44,.89-6,4-6,7.72,0,4.41,3.59,8,8,8s8-3.59,8-8c0-3.72-2.56-6.82-6-7.72Zm-2,11.72c-2.21,0-4-1.79-4-4s1.79-4,4-4,4,1.79,4,4-1.79,4-4,4Z`,
  ],
}

export const Files = {
  prefix: 'fal',
  iconName: 'files',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M46,19.88c-.02-.18-.06-.36-.12-.54v-.18c-.1-.21-.22-.39-.38-.56l-12-12c-.17-.16-.35-.28-.56-.38h-.18c-.2-.12-.43-.19-.66-.22H16c-1.59,0-3.12,.63-4.24,1.76-1.13,1.13-1.76,2.65-1.76,4.24v2h-2c-1.59,0-3.12,.63-4.24,1.76-1.13,1.13-1.76,2.65-1.76,4.24v16c0,1.59,.63,3.12,1.76,4.24,1.13,1.13,2.65,1.76,4.24,1.76h24c1.59,0,3.12-.63,4.24-1.76,1.13-1.13,1.76-2.65,1.76-4.24v-2h2c1.59,0,3.12-.63,4.24-1.76,1.13-1.13,1.76-2.65,1.76-4.24v-8.12Zm-12-7.06l5.18,5.18h-3.18c-.53,0-1.04-.21-1.41-.59-.38-.38-.59-.88-.59-1.41v-3.18Zm0,23.18c0,.53-.21,1.04-.59,1.41s-.88,.59-1.41,.59H8c-.53,0-1.04-.21-1.41-.59-.38-.38-.59-.88-.59-1.41V20c0-.53,.21-1.04,.59-1.41,.38-.38,.88-.59,1.41-.59h2v10c0,1.59,.63,3.12,1.76,4.24,1.13,1.13,2.65,1.76,4.24,1.76h18v2Zm8-8c0,.53-.21,1.04-.59,1.41s-.88,.59-1.41,.59H16c-.53,0-1.04-.21-1.41-.59-.38-.38-.59-.88-.59-1.41V12c0-.53,.21-1.04,.59-1.41,.38-.38,.88-.59,1.41-.59h14v6c0,1.59,.63,3.12,1.76,4.24,1.13,1.13,2.65,1.76,4.24,1.76h6v6Z`,
  ],
}

export const Errors = {
  prefix: 'fal',
  iconName: 'errors',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24,8c-8.84,0-16,7.16-16,16s7.16,16,16,16,16-7.16,16-16-7.16-16-16-16ZM4,24C4,12.95,12.95,4,24,4s20,8.95,20,20-8.95,20-20,20S4,35.05,4,24ZM15.59,15.59c.78-.78,2.05-.78,2.83,0l5.59,5.59,5.59-5.59c.78-.78,2.05-.78,2.83,0,.78,.78,.78,2.05,0,2.83l-5.59,5.59,5.59,5.59c.78,.78,.78,2.05,0,2.83-.78,.78-2.05,.78-2.83,0l-5.59-5.59-5.59,5.59c-.78,.78-2.05,.78-2.83,0-.78-.78-.78-2.05,0-2.83l5.59-5.59-5.59-5.59c-.78-.78-.78-2.05,0-2.83Z`,
  ],
}

export const Java = {
  prefix: 'fal',
  iconName: 'java',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M23.55,18.19c-.02,1.85,.67,3.63,1.92,4.99,.28,.3,.51,.65,.69,1.02,.08,.99-.13,1.99-.6,2.86-.03,.07-.04,.15-.03,.23,.02,.08,.06,.15,.12,.2,.06,.05,.14,.08,.22,.09,.08,0,.16-.02,.22-.06,.14-.1,3.43-2.41,2.82-5.19-.22-.91-.63-1.77-1.2-2.52h0c-.71-1.06-1.22-1.82-.43-3.23,1.48-1.81,3.19-3.42,5.07-4.8,.07-.05,.12-.13,.14-.21,.02-.09,0-.18-.04-.25-.04-.08-.11-.14-.2-.17-.08-.03-.18-.03-.26,0-.35,.13-8.46,3.26-8.46,7.03Zm-.67,7.69c.06,.05,.14,.08,.23,.08,.08,0,.16-.03,.22-.08,.06-.05,.11-.12,.12-.21,.02-.08,0-.16-.03-.24-1.28-2.44-2.05-5.12-2.28-7.87,.03-1.14,1.62-2.43,3.31-3.79,1.66-1.2,3.12-2.66,4.32-4.33,.63-1.12,.94-2.4,.89-3.68-.05-1.29-.45-2.53-1.17-3.6-.05-.07-.12-.12-.2-.14-.08-.02-.17-.02-.24,.02-.08,.03-.14,.09-.18,.17-.04,.07-.05,.16-.03,.24,.25,1.67-.05,3.37-.86,4.85-.86,1.17-1.95,2.14-3.2,2.87-2.34,1.37-4.27,3.35-5.57,5.73-2.14,4.68,4.39,9.78,4.67,10Zm-11.46,2.97c.88,1.22,3.99,1.89,8.77,1.89,.65,0,1.33,0,2.03-.04,3.73,.22,7.43-.75,10.59-2.75,.06-.05,.11-.13,.12-.21,.02-.08,0-.16-.04-.24-.04-.07-.1-.13-.18-.16-.08-.03-.16-.04-.24-.02-3.69,.73-7.44,1.06-11.2,.99-2.14,.11-4.28-.05-6.37-.49,1.12-.71,2.38-1.17,3.69-1.33,.09-.01,.17-.06,.23-.13,.06-.07,.08-.16,.07-.26,0-.09-.05-.18-.12-.24-.07-.06-.16-.09-.25-.09-1.12-.02-6.72,.07-7.31,1.86-.07,.2-.09,.41-.06,.62,.03,.21,.12,.41,.24,.57Zm21.58,4.95c-.06,.04-.11,.1-.14,.18-.03,.07-.03,.15,0,.23,.02,.07,.07,.14,.13,.19,.06,.05,.14,.07,.21,.07h.08c.32-.07,7.77-1.71,6.98-6.05-.03-.89-.42-1.72-1.07-2.33-.65-.6-1.51-.93-2.4-.89-1.09,.04-2.16,.32-3.13,.82-.07,.04-.13,.1-.16,.17-.03,.07-.04,.16-.02,.23,.02,.08,.06,.15,.13,.2,.06,.05,.14,.08,.22,.08,.03,0,3.36,.03,3.66,1.93,.27,1.64-3.15,4.29-4.48,5.18Zm-11.72,10.04c10.04,0,15.43-1.8,16.47-2.92,.14-.13,.24-.29,.31-.47,.07-.18,.09-.37,.07-.55-.04-.21-.12-.4-.24-.58-.12-.17-.27-.32-.45-.44-.07-.05-.16-.07-.25-.06-.09,.01-.17,.05-.22,.12-.06,.06-.09,.15-.09,.23s.03,.17,.09,.23c.06,.08,.1,.22-.09,.4-3.72,1.29-7.63,1.94-11.57,1.92-.96,.05-1.95,.07-2.97,.08-3.85,.21-7.71-.24-11.41-1.35,1.09-.64,2.31-1.03,3.57-1.16,.09-.01,.18-.07,.24-.14,.06-.08,.08-.17,.06-.27-.01-.09-.07-.18-.14-.24-.08-.06-.17-.08-.27-.06l-.4,.03c-2.83,.17-6.11,.56-6.28,2.02-.03,.22,0,.44,.06,.64,.07,.21,.18,.4,.33,.56,.77,.86,2.97,2,13.19,2h0Zm18.44-2.6c-.07-.04-.15-.05-.23-.03-.08,.01-.15,.05-.2,.11-1.75,1.39-3.85,2.26-6.07,2.52-3.21,.44-6.45,.62-9.7,.52-4.72,0-9.18-.18-9.22-.19-.09,0-.18,.02-.25,.08-.07,.06-.12,.14-.13,.23s0,.18,.06,.26c.05,.07,.13,.13,.22,.15,3.67,.76,7.4,1.13,11.14,1.11,2.98,0,5.96-.24,8.9-.74,5.27-.93,5.64-3.54,5.65-3.65,.01-.07,0-.15-.03-.21-.03-.07-.09-.12-.15-.16Zm-8.13-7.8s.07-.11,.09-.18c.01-.07,0-.14-.02-.2-.03-.06-.07-.12-.13-.16l-1.79-1.25s-.09-.05-.14-.06c-.05,0-.11,0-.16,0-1.51,.37-3.05,.63-4.6,.8-1.16,.13-2.32,.19-3.49,.18-1.6,.12-3.21-.07-4.74-.55-.03-.06-.02-.09,0-.11,.11-.16,.27-.28,.46-.35,.09-.03,.17-.09,.21-.18,.05-.08,.05-.18,.03-.27-.03-.09-.09-.17-.18-.21-.08-.04-.18-.05-.27-.03-1.79,.43-2.67,1.04-2.61,1.8,.11,1.35,3.24,2.04,5.87,2.22,.38,.03,.79,.04,1.22,.04,3.4-.13,6.77-.6,10.07-1.39,.06-.02,.12-.06,.17-.11Zm-13.39,2.71c.07-.04,.12-.11,.15-.18,.03-.07,.03-.16,0-.23s-.07-.14-.14-.19c-.06-.05-.14-.07-.22-.07-.24,0-2.35,.1-2.5,1.44-.02,.2,0,.39,.06,.58,.06,.19,.16,.36,.29,.51,.76,.9,2.8,1.43,6.25,1.63,.43,.03,.82,.04,1.23,.04,2.56,.04,5.1-.45,7.46-1.43,.06-.03,.11-.07,.15-.12,.04-.05,.06-.12,.06-.18,0-.07-.01-.13-.04-.19-.03-.06-.08-.11-.13-.14l-2.26-1.38c-.08-.05-.17-.06-.26-.05,0,0-1.45,.3-3.61,.6-.5,.06-1.01,.09-1.52,.09-1.69,.03-3.38-.16-5.02-.58,0-.03,0-.06,.01-.08,.01-.03,.03-.05,.05-.06Z`,
  ],
}

export const Tachometer = {
  prefix: 'fal',
  iconName: 'tacho-meter',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M30.58,20.58l-5.56,5.56c-.33-.09-.68-.14-1.02-.14-1.06,0-2.08,.42-2.83,1.17s-1.17,1.77-1.17,2.83c0,.34,.05,.69,.14,1.02l-1.56,1.56c-.19,.19-.34,.41-.44,.65-.1,.24-.15,.51-.15,.77s.05,.53,.15,.77c.1,.24,.25,.46,.44,.65,.19,.19,.41,.34,.65,.44,.24,.1,.51,.15,.77,.15s.53-.05,.77-.15c.24-.1,.46-.25,.65-.44l1.56-1.56c.33,.09,.68,.14,1.02,.14,1.06,0,2.08-.42,2.83-1.17,.75-.75,1.17-1.77,1.17-2.83,0-.34-.05-.69-.14-1.02l5.56-5.56c.38-.38,.59-.89,.59-1.42s-.21-1.04-.59-1.42c-.38-.38-.89-.59-1.42-.59s-1.04,.21-1.42,.59h0Zm-6.58-12.58c-2.63,0-5.23,.52-7.65,1.52-2.43,1.01-4.63,2.48-6.49,4.34-3.75,3.75-5.86,8.84-5.86,14.14,0,3.96,1.17,7.83,3.38,11.12,.3,.44,.76,.74,1.28,.85,.52,.1,1.06,0,1.5-.31s.74-.76,.85-1.28c.1-.52,0-1.06-.31-1.5-1.61-2.41-2.54-5.21-2.68-8.1-.14-2.89,.5-5.77,1.87-8.33,1.37-2.56,3.4-4.69,5.89-6.18,2.49-1.49,5.33-2.28,8.23-2.28s5.74,.79,8.23,2.28c2.49,1.49,4.52,3.63,5.89,6.18,1.37,2.56,2.01,5.43,1.87,8.33-.14,2.89-1.07,5.7-2.68,8.1-.15,.22-.25,.46-.3,.72-.05,.26-.05,.52,0,.78,.05,.26,.15,.5,.3,.72,.15,.22,.33,.41,.55,.55,.33,.22,.72,.34,1.12,.34,.33,0,.65-.08,.94-.23,.29-.15,.54-.38,.72-.65,2.21-3.29,3.39-7.16,3.38-11.12,0-5.3-2.11-10.39-5.86-14.14-3.75-3.75-8.84-5.86-14.14-5.86h0Z`,
  ],
}

export const Server = {
  prefix: 'fal',
  iconName: 'server',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M16,12.02c.4,0,.78-.12,1.11-.34,.33-.22,.59-.53,.74-.9,.15-.37,.19-.77,.11-1.16-.08-.39-.27-.74-.55-1.02-.28-.28-.64-.47-1.02-.55-.39-.08-.79-.04-1.16,.11-.37,.15-.68,.41-.9,.74-.22,.33-.34,.72-.34,1.11,0,.53,.21,1.04,.59,1.41,.38,.38,.88,.59,1.41,.59Zm26,26h-12.36c-.3-.84-.78-1.6-1.41-2.23-.63-.63-1.39-1.11-2.23-1.41v-4.36h8c1.59,0,3.12-.63,4.24-1.76,1.13-1.13,1.76-2.65,1.76-4.24v-4c0-1.48-.56-2.9-1.56-4,1-1.1,1.55-2.52,1.56-4v-4c0-1.59-.63-3.12-1.76-4.24-1.13-1.13-2.65-1.76-4.24-1.76H14c-1.59,0-3.12,.63-4.24,1.76-1.13,1.13-1.76,2.65-1.76,4.24v4c0,1.48,.56,2.9,1.56,4-1,1.1-1.55,2.52-1.56,4v4c0,1.59,.63,3.12,1.76,4.24,1.13,1.13,2.65,1.76,4.24,1.76h8v4.36c-.84,.3-1.6,.78-2.23,1.41-.63,.63-1.11,1.39-1.41,2.23H6c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41s.21,1.04,.59,1.41c.38,.38,.88,.59,1.41,.59h12.36c.42,1.16,1.19,2.16,2.2,2.87,1.01,.71,2.21,1.09,3.44,1.09s2.43-.38,3.44-1.09c1.01-.71,1.78-1.71,2.2-2.87h12.36c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41-.21-1.04-.59-1.41-.88-.59-1.41-.59ZM12,8.02c0-.53,.21-1.04,.59-1.41,.38-.38,.88-.59,1.41-.59h20c.53,0,1.04,.21,1.41,.59,.38,.38,.59,.88,.59,1.41v4c0,.53-.21,1.04-.59,1.41-.38,.38-.88,.59-1.41,.59H14c-.53,0-1.04-.21-1.41-.59-.38-.38-.59-.88-.59-1.41v-4Zm2,18c-.53,0-1.04-.21-1.41-.59-.38-.38-.59-.88-.59-1.41v-4c0-.53,.21-1.04,.59-1.41,.38-.38,.88-.59,1.41-.59h20c.53,0,1.04,.21,1.41,.59,.38,.38,.59,.88,.59,1.41v4c0,.53-.21,1.04-.59,1.41s-.88,.59-1.41,.59H14Zm10,16c-.4,0-.78-.12-1.11-.34-.33-.22-.59-.53-.74-.9-.15-.37-.19-.77-.11-1.16,.08-.39,.27-.74,.55-1.02,.28-.28,.64-.47,1.02-.55,.39-.08,.79-.04,1.16,.11,.37,.15,.68,.41,.9,.74,.22,.33,.34,.72,.34,1.11,0,.53-.21,1.04-.59,1.41s-.88,.59-1.41,.59Zm-8-22c-.4,0-.78,.12-1.11,.34-.33,.22-.59,.53-.74,.9-.15,.37-.19,.77-.11,1.16,.08,.39,.27,.74,.55,1.02,.28,.28,.64,.47,1.02,.55,.39,.08,.79,.04,1.16-.11,.37-.15,.68-.41,.9-.74,.22-.33,.34-.72,.34-1.11,0-.53-.21-1.04-.59-1.41-.38-.38-.88-.59-1.41-.59h0Z`,
  ],
}

export const Processor = {
  prefix: 'fal',
  iconName: 'processor',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M28,18h-8c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41v8c0,.53,.21,1.04,.59,1.41,.38,.38,.88,.59,1.41,.59h8c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41v-8c0-.53-.21-1.04-.59-1.41s-.88-.59-1.41-.59Zm-2,8h-4v-4h4v4Zm16,0c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41-.21-1.04-.59-1.41-.88-.59-1.41-.59h-4v-4h4c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41-.21-1.04-.59-1.41-.88-.59-1.41-.59h-4.36c-.3-.84-.78-1.6-1.41-2.23-.63-.63-1.39-1.11-2.23-1.41V6c0-.53-.21-1.04-.59-1.41s-.88-.59-1.41-.59-1.04,.21-1.41,.59-.59,.88-.59,1.41v4h-4V6c0-.53-.21-1.04-.59-1.41s-.88-.59-1.41-.59-1.04,.21-1.41,.59-.59,.88-.59,1.41v4h-4V6c0-.53-.21-1.04-.59-1.41-.38-.38-.88-.59-1.41-.59s-1.04,.21-1.41,.59c-.38,.38-.59,.88-.59,1.41v4.36c-.84,.3-1.6,.78-2.23,1.41s-1.11,1.39-1.41,2.23H6c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41s.21,1.04,.59,1.41c.38,.38,.88,.59,1.41,.59h4v4H6c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41s.21,1.04,.59,1.41c.38,.38,.88,.59,1.41,.59h4v4H6c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41s.21,1.04,.59,1.41c.38,.38,.88,.59,1.41,.59h4.36c.3,.84,.78,1.6,1.41,2.23,.63,.63,1.39,1.11,2.23,1.41v4.36c0,.53,.21,1.04,.59,1.41,.38,.38,.88,.59,1.41,.59s1.04-.21,1.41-.59c.38-.38,.59-.88,.59-1.41v-4h4v4c0,.53,.21,1.04,.59,1.41s.88,.59,1.41,.59,1.04-.21,1.41-.59,.59-.88,.59-1.41v-4h4v4c0,.53,.21,1.04,.59,1.41s.88,.59,1.41,.59,1.04-.21,1.41-.59,.59-.88,.59-1.41v-4.36c.84-.3,1.6-.78,2.23-1.41s1.11-1.39,1.41-2.23h4.36c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41-.21-1.04-.59-1.41-.88-.59-1.41-.59h-4v-4h4Zm-8,6c0,.53-.21,1.04-.59,1.41s-.88,.59-1.41,.59H16c-.53,0-1.04-.21-1.41-.59-.38-.38-.59-.88-.59-1.41V16c0-.53,.21-1.04,.59-1.41,.38-.38,.88-.59,1.41-.59h16c.53,0,1.04,.21,1.41,.59s.59,.88,.59,1.41v16Z`,
  ],
}

export const Backup = {
  prefix: 'fal',
  iconName: 'backup',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M44.02,28.74c0-1.01-.4-1.98-1.12-2.7-.72-.72-1.69-1.12-2.7-1.12h-11.45c-1.01,0-1.98,.4-2.7,1.12-.72,.72-1.12,1.69-1.12,2.7h-5.72v-5.72h4.44c1.66,0,3.26-.58,4.53-1.66,1.26-1.07,2.11-2.56,2.37-4.19,.27-1.64-.05-3.31-.91-4.73-.86-1.42-2.19-2.49-3.76-3.02-.87-1.82-2.3-3.31-4.07-4.27-1.78-.96-3.81-1.33-5.81-1.06-2,.27-3.86,1.17-5.32,2.57s-2.43,3.22-2.79,5.2c-1.29,.42-2.39,1.3-3.1,2.46-.71,1.16-.98,2.54-.77,3.88,.21,1.34,.9,2.57,1.93,3.45,1.03,.88,2.35,1.37,3.71,1.37h5.72v15.26c0,.51,.2,.99,.56,1.35,.36,.36,.84,.56,1.35,.56h7.63c0,1.01,.4,1.98,1.12,2.7,.72,.72,1.69,1.12,2.7,1.12h11.45c1.01,0,1.98-.4,2.7-1.12s1.12-1.69,1.12-2.7v-3.82c0-.67-.19-1.33-.53-1.91,.34-.58,.53-1.24,.53-1.91v-3.82ZM9.69,19.2c-.51,0-.99-.2-1.35-.56s-.56-.84-.56-1.35,.2-.99,.56-1.35c.36-.36,.84-.56,1.35-.56s.99-.2,1.35-.56c.36-.36,.56-.84,.56-1.35-.02-1.36,.46-2.68,1.33-3.73,.87-1.05,2.09-1.75,3.43-1.98,1.34-.23,2.72,.03,3.89,.72,1.17,.7,2.05,1.79,2.49,3.08,.11,.32,.31,.61,.57,.83,.26,.22,.58,.37,.92,.43,.75,.12,1.43,.51,1.91,1.1,.48,.59,.74,1.33,.72,2.09,0,.84-.34,1.66-.93,2.25-.6,.6-1.41,.93-2.25,.93H9.69Zm15.26,17.17h-5.72v-3.82h5.72c0,.67,.19,1.33,.53,1.91-.34,.58-.53,1.24-.53,1.91h0Zm3.82,3.82v-3.82h11.45v3.82h-11.45Zm0-7.63v-3.82h11.45v3.82h-11.45Z`,
  ],
}

export const Licence = {
  prefix: 'fal',
  iconName: 'licence',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M33.55,22.09h1.91c.51,0,.99-.2,1.35-.56s.56-.84,.56-1.35v-1.91c0-.51-.2-.99-.56-1.35-.36-.36-.84-.56-1.35-.56h-1.91c-.51,0-.99,.2-1.35,.56-.36,.36-.56,.84-.56,1.35v1.91c0,.51,.2,.99,.56,1.35s.84,.56,1.35,.56Zm-21,1.91h9.55c.51,0,.99-.2,1.35-.56s.56-.84,.56-1.35-.2-.99-.56-1.35-.84-.56-1.35-.56H12.55c-.51,0-.99,.2-1.35,.56-.36,.36-.56,.84-.56,1.35s.2,.99,.56,1.35c.36,.36,.84,.56,1.35,.56Zm30.55-15.27H4.91c-.51,0-.99,.2-1.35,.56-.36,.36-.56,.84-.56,1.35v26.73c0,.51,.2,.99,.56,1.35,.36,.36,.84,.56,1.35,.56H43.09c.51,0,.99-.2,1.35-.56s.56-.84,.56-1.35V10.64c0-.51-.2-.99-.56-1.35-.36-.36-.84-.56-1.35-.56Zm-1.91,26.73H6.82V12.55H41.18v22.91Zm-28.64-3.82h9.55c.51,0,.99-.2,1.35-.56s.56-.84,.56-1.35-.2-.99-.56-1.35-.84-.56-1.35-.56H12.55c-.51,0-.99,.2-1.35,.56-.36,.36-.56,.84-.56,1.35s.2,.99,.56,1.35c.36,.36,.84,.56,1.35,.56Z`,
  ],
}

export const ChartLine = {
  prefix: 'fal',
  iconName: 'chart-line',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M15,32c.8,0,1.56-.32,2.12-.88,.56-.56,.88-1.33,.88-2.12,0-.1,0-.2,0-.3l5.58-5.58h.92l3.22,3.22v.16c0,.8,.32,1.56,.88,2.12s1.33,.88,2.12,.88,1.56-.32,2.12-.88c.56-.56,.88-1.33,.88-2.12v-.16l7.28-7.34c.59,0,1.17-.18,1.67-.51,.49-.33,.88-.8,1.1-1.35,.23-.55,.29-1.15,.17-1.73-.12-.58-.4-1.12-.82-1.54-.42-.42-.95-.71-1.54-.82-.58-.12-1.19-.06-1.73,.17-.55,.23-1.02,.61-1.35,1.1-.33,.49-.51,1.07-.51,1.67,0,.1,0,.2,0,.3l-7.22,7.22h-.32l-3.46-3.52c0-.8-.32-1.56-.88-2.12-.56-.56-1.33-.88-2.12-.88s-1.56,.32-2.12,.88c-.56,.56-.88,1.33-.88,2.12l-6,6c-.8,0-1.56,.32-2.12,.88-.56,.56-.88,1.33-.88,2.12s.32,1.56,.88,2.12c.56,.56,1.33,.88,2.12,.88h0Zm27,8H8V6c0-.53-.21-1.04-.59-1.41-.38-.38-.88-.59-1.41-.59s-1.04,.21-1.41,.59c-.38,.38-.59,.88-.59,1.41V42c0,.53,.21,1.04,.59,1.41,.38,.37,.88,.59,1.41,.59H42c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41-.21-1.04-.59-1.41-.88-.59-1.41-.59Z`,
  ],
}

export const Key = {
  prefix: 'fal',
  iconName: 'key',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M41.99,8.83l1.42-1.4c.38-.38,.59-.89,.59-1.42s-.21-1.04-.59-1.42c-.38-.38-.89-.59-1.42-.59s-1.04,.21-1.42,.59l-2.8,2.82-5.66,5.66-12.62,12.6c-2.07-1.36-4.57-1.91-7.02-1.54-2.45,.37-4.67,1.64-6.24,3.56-1.57,1.92-2.36,4.35-2.24,6.82,.13,2.47,1.16,4.81,2.92,6.56,1.75,1.75,4.09,2.79,6.56,2.92,2.47,.13,4.91-.67,6.82-2.24,1.92-1.57,3.19-3.79,3.56-6.24,.37-2.45-.17-4.95-1.54-7.01l11.18-11.2,4.24,4.26c.19,.19,.41,.33,.65,.43,.24,.1,.5,.15,.77,.15,.26,0,.52-.05,.77-.15,.24-.1,.46-.25,.65-.44,.19-.19,.33-.41,.43-.65,.1-.24,.15-.5,.15-.77,0-.26-.05-.52-.16-.76-.1-.24-.25-.46-.44-.65l-4.24-4.24,2.84-2.82,1.4,1.4c.19,.19,.41,.33,.65,.44,.24,.1,.5,.15,.77,.15,.26,0,.52-.05,.77-.15,.24-.1,.46-.25,.65-.43,.19-.19,.33-.41,.44-.65,.1-.24,.15-.5,.16-.76,0-.26-.05-.52-.15-.77-.1-.24-.25-.46-.43-.65l-1.42-1.42ZM14,39.99c-1.19,0-2.35-.35-3.33-1.01-.99-.66-1.76-1.6-2.21-2.69-.45-1.1-.57-2.3-.34-3.47,.23-1.16,.8-2.23,1.64-3.07,.84-.84,1.91-1.41,3.07-1.64,1.16-.23,2.37-.11,3.47,.34,1.1,.45,2.03,1.22,2.69,2.21,.66,.99,1.01,2.15,1.01,3.33,0,1.59-.63,3.12-1.76,4.24s-2.65,1.76-4.24,1.76Z`,
  ],
}

export const InnoDB = {
  prefix: 'fal',
  iconName: 'inno-db',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M41.78,18.51c.5,0,.97-.2,1.33-.55,.35-.35,.55-.83,.55-1.33V5.54c0-.5-.2-.97-.55-1.33-.35-.35-.83-.55-1.33-.55h-10.98c-.5,0-.97,.2-1.33,.55-.35,.35-.55,.83-.55,1.33v3.11h-10.28v-3.11c0-.5-.2-.97-.55-1.33-.35-.35-.83-.55-1.33-.55H5.8c-.5,0-.97,.2-1.33,.55-.35,.35-.55,.83-.55,1.33v11.09c0,.5,.2,.97,.55,1.33,.35,.35,.83,.55,1.33,.55h3.41v10.32h-3.41c-.5,0-.97,.2-1.33,.55-.35,.35-.55,.83-.55,1.33v11.09c0,.5,.2,.97,.55,1.33,.35,.35,.83,.55,1.33,.55h10.98c.5,0,.97-.2,1.33-.55,.35-.35,.55-.83,.55-1.33v-3.11h10.28v3.11c0,.5,.2,.97,.55,1.33,.35,.35,.83,.55,1.33,.55h10.98c.5,0,.97-.2,1.33-.55,.35-.35,.55-.83,.55-1.33v-11.09c0-.5-.2-.97-.55-1.33-.35-.35-.83-.55-1.33-.55h-3.41v-10.32h3.41ZM7.67,14.75V7.42h7.22v7.34H7.67Zm7.22,25.16H7.67v-7.34h7.22v7.34Zm14.03-9.21v4.22h-10.28v-4.22c0-.5-.2-.97-.55-1.33-.35-.35-.83-.55-1.33-.55h-3.75v-10.32h3.75c.5,0,.97-.2,1.33-.55,.35-.35,.55-.83,.55-1.33v-4.22h10.28v4.22c0,.5,.2,.97,.55,1.33,.35,.35,.83,.55,1.33,.55h3.75v10.32h-3.75c-.5,0-.97,.2-1.33,.55-.35,.35-.55,.83-.55,1.33Zm10.98,1.88v7.34h-7.22v-7.34h7.22Zm-7.22-17.82V7.42h7.22v7.34h-7.22Z`,
  ],
}

export const CheckPoint = {
  prefix: 'fal',
  iconName: 'check-point',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M19.83,20.92c-.71-.71-1.86-.71-2.57,0-.71,.71-.71,1.86,0,2.57l5.46,5.46c.34,.34,.8,.53,1.29,.53s.95-.19,1.29-.53L43.47,10.74c.71-.71,.71-1.86,0-2.57-.71-.71-1.86-.71-2.57,0L24,25.09l-4.17-4.17Zm22.36-.4c-1.01,0-1.82,.81-1.82,1.82v1.67c0,3.51-1.16,6.98-3.26,9.79-2.1,2.81-5.11,4.9-8.47,5.9-3.36,1-7.02,.87-10.31-.34-3.29-1.21-6.15-3.5-8.06-6.44-1.91-2.94-2.83-6.49-2.6-9.99,.23-3.5,1.61-6.89,3.89-9.56,2.28-2.67,5.42-4.56,8.84-5.33,3.42-.77,7.07-.41,10.27,1.02,.92,.41,1.99,0,2.4-.92,.41-.92,0-1.99-.92-2.4-3.91-1.74-8.37-2.18-12.55-1.24-4.18,.94-8.02,3.26-10.8,6.52-2.78,3.26-4.47,7.41-4.76,11.68-.28,4.27,.85,8.61,3.18,12.2,2.33,3.6,5.83,6.39,9.85,7.88,2.22,.82,4.58,1.23,6.94,1.23,1.91,0,3.82-.27,5.66-.82,4.11-1.21,7.78-3.77,10.35-7.21,2.57-3.43,3.98-7.68,3.98-11.97v-1.67c0-1-.81-1.82-1.82-1.82Z`,
  ],
}

export const Locks = {
  prefix: 'fal',
  iconName: 'locks',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M34,18v-4c0-2.65-1.05-5.2-2.93-7.07-1.88-1.88-4.42-2.93-7.07-2.93s-5.2,1.05-7.07,2.93c-1.88,1.88-2.93,4.42-2.93,7.07v4c-1.59,0-3.12,.63-4.24,1.76-1.13,1.13-1.76,2.65-1.76,4.24v14c0,1.59,.63,3.12,1.76,4.24,1.13,1.13,2.65,1.76,4.24,1.76h20c1.59,0,3.12-.63,4.24-1.76s1.76-2.65,1.76-4.24v-14c0-1.59-.63-3.12-1.76-4.24-1.13-1.13-2.65-1.76-4.24-1.76Zm-16-4c0-1.59,.63-3.12,1.76-4.24,1.13-1.13,2.65-1.76,4.24-1.76s3.12,.63,4.24,1.76,1.76,2.65,1.76,4.24v4h-12v-4Zm18,24c0,.53-.21,1.04-.59,1.41-.37,.37-.88,.59-1.41,.59H14c-.53,0-1.04-.21-1.41-.59-.38-.38-.59-.88-.59-1.41v-14c0-.53,.21-1.04,.59-1.41,.38-.38,.88-.59,1.41-.59h20c.53,0,1.04,.21,1.41,.59,.37,.37,.59,.88,.59,1.41v14Z`,
  ],
}

export const Agent = {
  prefix: 'fal',
  iconName: 'agent',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M2.91,15.88H26.27c2.37-1.28,4.93-2.01,7.85-2.01,2.01,0,3.83,.37,5.66,1.1V6.75c0-.55-.37-.91-.91-.91H2.91c-.55,0-.91,.37-.91,.91V14.97c0,.55,.37,.91,.91,.91Zm2.37-6.75h31.21v3.47H5.29v-3.47ZM2.91,27.56h15.33c.18-1.28,.37-2.37,.91-3.47H5.29v-3.29h15.51c.91-1.28,1.83-2.37,3.1-3.29H2.91c-.55,0-.91,.37-.91,.73v8.4c0,.55,.37,.91,.91,.91Zm2.37,8.21v-3.47h12.96c-.18-.73-.18-1.64-.18-2.37v-.91H2.91c-.55,0-.91,.37-.91,.91v8.4c0,.55,.37,.91,.91,.91H20.98c-.73-1.1-1.28-2.19-1.83-3.29H5.29v-.18Zm28.84-10.22c-2.37,0-4.2,2.01-4.2,4.2,0,2.37,1.83,4.2,4.2,4.2s4.2-1.83,4.2-4.2c0-2.19-2.01-4.2-4.2-4.2Zm-.18,6.21c-1.1,0-2.01-.91-2.01-1.83,0-1.1,.91-2.01,2.01-2.01s2.01,.91,2.01,2.01-.91,1.83-2.01,1.83Zm9.67-.55c0-.37,.18-.91,0-1.28v-1.28l1.83-1.28c.55-.37,.55-.91,.37-1.46l-2.37-4.2c-.37-.55-.91-.73-1.46-.37l-2.01,.91c-.73-.55-1.46-.91-2.19-1.28l-.18-2.19c0-.55-.55-1.1-1.1-1.1h-4.93c-.55,0-1.1,.37-1.1,.91l-.18,2.19c-.73,.37-1.46,.73-2.19,1.28l-2.01-.91c-.55-.18-1.1,0-1.46,.37l-2.37,4.38c-.37,.55-.18,1.1,.37,1.46l1.83,1.28v2.56l-1.83,1.28c-.55,.37-.55,.91-.37,1.46l2.37,4.2c.37,.55,.91,.73,1.46,.37l2.01-.91c.73,.55,1.46,.91,2.19,1.28l.18,2.19c0,.55,.55,1.1,1.1,1.1h4.93c.55,0,1.1-.37,1.1-.91l.18-2.19c.73-.37,1.46-.73,2.19-1.28l2.01,.91c.55,.18,1.1,0,1.46-.37l2.37-4.2c.37-.55,.18-1.1-.18-1.46l-2.01-1.46Zm-2.74-1.46c0,.55,0,.91-.18,1.46,0,.37,0,.73,.37,1.1l1.46,.91-1.28,2.37-1.46-.73c-.37-.18-.73-.18-1.1,.18-.73,.55-1.46,1.1-2.37,1.46-.37,.18-.55,.37-.73,.91l-.18,1.64h-2.56l-.18-1.64c0-.37-.37-.73-.73-.91-.91-.37-1.64-.73-2.37-1.46-.18-.18-.37-.18-.73-.18-.18,0-.37,0-.37,.18l-1.64,.55-1.28-2.37,1.46-.91c.37-.18,.55-.55,.37-1.1-.18-.91-.18-1.83,0-2.74,0-.37,0-.73-.37-.91l-1.46-.91,1.28-2.19,1.46,.73c.37,.18,.73,.18,1.1-.18,.73-.55,1.46-1.1,2.37-1.46,.37-.18,.73-.37,.73-.91l.18-1.64h2.56l.18,1.64c0,.37,.37,.73,.73,.91,.91,.37,1.83,.73,2.37,1.46,.37,.18,.73,.37,1.1,.18l1.64-.73,1.28,2.19-1.46,.91c-.37,.18-.55,.55-.37,1.1,0,.18,.18,.73,.18,1.1Z`,
  ],
}

export const Accesses = {
  prefix: 'fal',
  iconName: 'accesses',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M42,4h-12c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41s.21,1.04,.59,1.41c.38,.38,.88,.59,1.41,.59h10v10c0,.53,.21,1.04,.59,1.41,.38,.38,.88,.59,1.41,.59s1.04-.21,1.41-.59c.38-.38,.59-.88,.59-1.41V6c0-.53-.21-1.04-.59-1.41-.38-.38-.88-.59-1.41-.59h0Zm0,24c-.53,0-1.04,.21-1.41,.59s-.59,.88-.59,1.41v10h-10c-.53,0-1.04,.21-1.41,.59s-.59,.88-.59,1.41,.21,1.04,.59,1.41,.88,.59,1.41,.59h12c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41v-12c0-.53-.21-1.04-.59-1.41s-.88-.59-1.41-.59ZM24,12c-1.59,0-3.12,.63-4.24,1.76-1.13,1.13-1.76,2.65-1.76,4.24v2c-1.06,0-2.08,.42-2.83,1.17-.75,.75-1.17,1.77-1.17,2.83v8c0,1.06,.42,2.08,1.17,2.83,.75,.75,1.77,1.17,2.83,1.17h12c1.06,0,2.08-.42,2.83-1.17,.75-.75,1.17-1.77,1.17-2.83v-8c0-1.06-.42-2.08-1.17-2.83-.75-.75-1.77-1.17-2.83-1.17v-2c0-1.59-.63-3.12-1.76-4.24-1.13-1.13-2.65-1.76-4.24-1.76Zm-2,6c0-.53,.21-1.04,.59-1.41,.38-.38,.88-.59,1.41-.59s1.04,.21,1.41,.59c.38,.38,.59,.88,.59,1.41v2h-4v-2Zm8,14h-12v-8h12v8ZM6,20c.53,0,1.04-.21,1.41-.59,.38-.38,.59-.88,.59-1.41V8h10c.53,0,1.04-.21,1.41-.59,.38-.38,.59-.88,.59-1.41s-.21-1.04-.59-1.41c-.38-.38-.88-.59-1.41-.59H6c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41v12c0,.53,.21,1.04,.59,1.41,.38,.38,.88,.59,1.41,.59Zm12,20H8v-10c0-.53-.21-1.04-.59-1.41-.38-.38-.88-.59-1.41-.59s-1.04,.21-1.41,.59c-.38,.38-.59,.88-.59,1.41v12c0,.53,.21,1.04,.59,1.41,.38,.38,.88,.59,1.41,.59h12c.53,0,1.04-.21,1.41-.59,.38-.38,.59-.88,.59-1.41s-.21-1.04-.59-1.41c-.38-.38-.88-.59-1.41-.59Z`,
  ],
}

export const CircleRight = {
  prefix: 'fal',
  iconName: 'circle-right',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24,8c-8.84,0-16,7.16-16,16s7.16,16,16,16,16-7.16,16-16-7.16-16-16-16ZM4,24C4,12.95,12.95,4,24,4s20,8.95,20,20-8.95,20-20,20S4,35.05,4,24ZM24.59,14.59c.78-.78,2.05-.78,2.83,0l8,8c.78,.78,.78,2.05,0,2.83l-8,8c-.78,.78-2.05,.78-2.83,0-.78-.78-.78-2.05,0-2.83l4.59-4.59H14c-1.1,0-2-.9-2-2s.9-2,2-2h15.17l-4.59-4.59c-.78-.78-.78-2.05,0-2.83Z`,
  ],
}

export const RejectedConnections = {
  prefix: 'fal',
  iconName: 'rejected-connections',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M46,12.01c0-6.63-5.37-12.01-12-12.01-5.22,0-9.67,3.34-11.32,8.01H14c-1.59,0-3.12,.63-4.24,1.76-1.13,1.13-1.76,2.65-1.76,4.25v12.01h-2c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.42s.21,1.04,.59,1.42c.38,.38,.88,.59,1.41,.59h14v6.37c-.84,.3-1.6,.78-2.23,1.41s-1.11,1.39-1.41,2.23H4c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.42s.21,1.04,.59,1.42c.38,.38,.88,.59,1.41,.59h12.36c.42,1.16,1.19,2.16,2.2,2.87,1.01,.71,2.21,1.09,3.44,1.09s2.43-.38,3.44-1.09c1.01-.71,1.78-1.71,2.2-2.87h12.36c.53,0,1.04-.21,1.41-.59,.38-.38,.59-.88,.59-1.42s-.21-1.04-.59-1.42-.88-.59-1.41-.59h-12.36c-.3-.84-.78-1.6-1.41-2.23s-1.39-1.11-2.23-1.41v-6.37h14c.53,0,1.04-.21,1.41-.59,.38-.38,.59-.88,.59-1.42s-.21-1.04-.59-1.42c-.38-.38-.88-.59-1.41-.59h-2v-2.17c5.68-.95,10-5.89,10-11.85Zm-4,0c0,1.49-.41,2.88-1.11,4.07l-4.06-4.07,4.06-4.07c.71,1.19,1.11,2.58,1.11,4.07Zm-8.01-8.01c1.48,0,2.87,.41,4.06,1.11l-4.06,4.07-4.06-4.07c1.19-.71,2.58-1.11,4.06-1.11Zm-6.89,3.94l4.06,4.07-4.06,4.07c-.7-1.19-1.11-2.58-1.11-4.07s.4-2.88,1.11-4.07Zm-3.7,35.51c-.37,.38-.88,.59-1.41,.59-.39,0-.78-.12-1.11-.34h-.01c-.33-.21-.59-.52-.74-.89-.15-.37-.19-.77-.11-1.16,.08-.39,.27-.75,.55-1.03s.63-.47,1.02-.55c.39-.08,.79-.04,1.16,.11s.68,.41,.9,.74c.22,.33,.34,.71,.34,1.11,0,.54-.21,1.04-.59,1.42Zm8.59-17.42H12V14.01c0-.53,.21-1.04,.59-1.42,.38-.38,.88-.59,1.41-.59h7.99c0,5.96,4.32,10.9,10,11.86v2.17Zm2.01-6.01c-1.48,0-2.87-.41-4.06-1.11l4.06-4.07,4.06,4.07c-1.19,.71-2.58,1.11-4.06,1.11Z`,
  ],
}

export const WaitingConnections = {
  prefix: 'fal',
  iconName: 'waiting-connections',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M34,7.99c-2.21,0-4,1.79-4,4s1.79,4,4,4,4-1.79,4-4-1.79-4-4-4Zm12,4.02c0-6.63-5.37-12.01-12-12.01-5.22,0-9.67,3.34-11.32,8.01H14c-1.59,0-3.12,.63-4.24,1.76-1.13,1.13-1.76,2.65-1.76,4.25v12.01h-2c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.42s.21,1.04,.59,1.42c.38,.38,.88,.59,1.41,.59h14v6.37c-.84,.3-1.6,.78-2.23,1.41s-1.11,1.39-1.41,2.23H4c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.42s.21,1.04,.59,1.42c.38,.38,.88,.59,1.41,.59h12.36c.42,1.16,1.19,2.16,2.2,2.87,1.01,.71,2.21,1.09,3.44,1.09s2.43-.38,3.44-1.09c1.01-.71,1.78-1.71,2.2-2.87h12.36c.53,0,1.04-.21,1.41-.59,.38-.38,.59-.88,.59-1.42s-.21-1.04-.59-1.42-.88-.59-1.41-.59h-12.36c-.3-.84-.78-1.6-1.41-2.23s-1.39-1.11-2.23-1.41v-6.37h14c.53,0,1.04-.21,1.41-.59,.38-.38,.59-.88,.59-1.42s-.21-1.04-.59-1.42c-.38-.38-.88-.59-1.41-.59h-2v-2.17c5.68-.95,10-5.89,10-11.85Zm-22.59,31.44c-.37,.38-.88,.59-1.41,.59-.39,0-.78-.12-1.11-.34h-.01c-.33-.21-.59-.52-.74-.89-.15-.37-.19-.77-.11-1.16,.08-.39,.27-.75,.55-1.03s.63-.47,1.02-.55c.39-.08,.79-.04,1.16,.11s.68,.41,.9,.74c.22,.33,.34,.71,.34,1.11,0,.54-.21,1.04-.59,1.42Zm8.59-17.42H12V14.01c0-.53,.21-1.04,.59-1.42,.38-.38,.88-.59,1.41-.59h8c0,5.96,4.32,10.9,10,11.86v2.17Zm2-6.01c-4.42,0-8-3.59-8-8.01s3.58-8.01,8-8.01,8,3.59,8,8.01-3.58,8.01-8,8.01Z`,
  ],
}

export const ReadingConnections = {
  prefix: 'fal',
  iconName: 'reading-connections',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M30,11.01h6.01c1.11,0,2-.9,2-2s-.9-2-2-2h-6.01c-1.11,0-2,.9-2,2s.9,2,2,2Zm16.03,1c0-6.63-5.38-12.01-12.01-12.01h-.01c-5.23,0-9.68,3.34-11.33,8.01H13.99c-1.59,0-3.12,.63-4.25,1.76s-1.76,2.65-1.76,4.25v12.01h-2c-.53,0-1.04,.21-1.42,.59s-.59,.88-.59,1.42,.21,1.04,.59,1.42,.88,.59,1.42,.59h14.01v6.37c-.84,.3-1.6,.78-2.23,1.41s-1.11,1.39-1.41,2.23H3.98c-.53,0-1.04,.21-1.42,.59s-.59,.88-.59,1.42,.21,1.04,.59,1.42,.88,.59,1.42,.59h12.37c.42,1.16,1.19,2.16,2.2,2.87,1.01,.71,2.21,1.09,3.45,1.09s2.44-.38,3.45-1.09c1.01-.71,1.78-1.71,2.2-2.87h12.37c.53,0,1.04-.21,1.42-.59s.59-.88,.59-1.42-.21-1.04-.59-1.42c-.38-.38-.88-.59-1.42-.59h-12.37c-.3-.84-.78-1.6-1.41-2.23s-1.39-1.11-2.23-1.41v-6.37h14.01c.53,0,1.04-.21,1.42-.59s.59-.88,.59-1.42-.21-1.04-.59-1.42-.88-.59-1.42-.59h-2v-2.17c5.68-.95,10.01-5.89,10.01-11.85Zm-22.61,31.44c-.38,.38-.89,.59-1.42,.59-.39,0-.78-.12-1.11-.34h-.01c-.33-.21-.59-.52-.74-.89-.15-.37-.19-.77-.11-1.16,.08-.39,.27-.75,.55-1.03,.29-.28,.64-.47,1.03-.55s.79-.04,1.16,.11,.68,.41,.9,.74c.22,.33,.34,.71,.34,1.11,0,.54-.21,1.04-.59,1.42Zm8.59-17.42H11.99V14.01c0-.53,.21-1.04,.59-1.42,.38-.38,.88-.59,1.42-.59h8c0,5.96,4.33,10.9,10.01,11.86v2.17Zm2-6.01c-4.42,0-8.01-3.59-8.01-8.01s3.59-8.01,8.01-8.01,8.01,3.59,8.01,8.01-3.59,8.01-8.01,8.01Zm4-7h-8.01c-1.11,0-2,.9-2,2s.9,2,2,2h8.01c1.11,0,2-.9,2-2s-.9-2-2-2Z`,
  ],
}

export const WritingConnections = {
  prefix: 'fal',
  iconName: 'writing-connections',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M35.59,7.59l-6,6.01c-.78,.78-.78,2.05,0,2.83,.78,.78,2.05,.78,2.83,0l6-6.01c.78-.78,.78-2.05,0-2.83s-2.05-.78-2.83,0Zm10.41,4.42c0-6.63-5.37-12.01-12-12.01-5.22,0-9.67,3.34-11.32,8.01H14c-1.59,0-3.12,.63-4.24,1.76s-1.76,2.65-1.76,4.25v12.01h-2c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.42s.21,1.04,.59,1.42c.38,.38,.88,.59,1.41,.59h14v6.37c-.84,.3-1.6,.78-2.23,1.41-.63,.63-1.11,1.39-1.41,2.23H4c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.42s.21,1.04,.59,1.42c.38,.38,.88,.59,1.41,.59h12.36c.42,1.16,1.19,2.16,2.2,2.87,1.01,.71,2.21,1.09,3.44,1.09s2.43-.38,3.44-1.09c1.01-.71,1.78-1.71,2.2-2.87h12.36c.53,0,1.04-.21,1.41-.59,.38-.38,.59-.88,.59-1.42s-.21-1.04-.59-1.42c-.38-.38-.88-.59-1.41-.59h-12.36c-.3-.84-.78-1.6-1.41-2.23-.63-.63-1.39-1.11-2.23-1.41v-6.37h14c.53,0,1.04-.21,1.41-.59,.38-.38,.59-.88,.59-1.42s-.21-1.04-.59-1.42c-.38-.38-.88-.59-1.41-.59h-2v-2.17c5.68-.95,10-5.89,10-11.85Zm-22.59,31.45c-.38,.38-.88,.59-1.41,.59-.4,0-.78-.12-1.11-.34-.33-.22-.59-.53-.74-.9-.15-.37-.19-.77-.11-1.16,.08-.39,.27-.74,.55-1.02,.28-.28,.64-.47,1.02-.55,.39-.08,.79-.04,1.16,.11,.37,.15,.68,.41,.9,.74,.22,.33,.34,.72,.34,1.11,0,.53-.21,1.04-.59,1.42Zm8.59-17.43H12V14.01c0-.53,.21-1.04,.59-1.42,.38-.38,.88-.59,1.41-.59h8c0,5.95,4.32,10.89,10,11.85v2.17Zm2-6.01c-4.42,0-8-3.59-8-8.01s3.58-8.01,8-8.01,8,3.59,8,8.01-3.58,8.01-8,8.01Z`,
  ],
}

export const Utilization = {
  prefix: 'fal',
  iconName: 'utilization',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M38.17,9.91s-.02-.04-.03-.05c-.02-.02-.04-.02-.05-.03-3.74-3.73-8.8-5.82-14.08-5.82s-10.34,2.09-14.08,5.82c-.02,.01-.04,.02-.05,.03s-.02,.04-.03,.05c-2.79,2.8-4.68,6.36-5.44,10.24-.76,3.88-.36,7.89,1.15,11.54,1.52,3.65,4.08,6.76,7.36,8.96,3.29,2.19,7.15,3.36,11.1,3.36s7.81-1.17,11.1-3.36c3.29-2.19,5.85-5.31,7.36-8.96,1.52-3.65,1.92-7.66,1.15-11.54-.76-3.88-2.66-7.44-5.44-10.24h0Zm-14.17,30.08c-2.39,0-4.76-.54-6.92-1.58-2.16-1.04-4.05-2.55-5.55-4.42h8.03c.56,.63,1.24,1.13,2,1.48,.76,.34,1.59,.52,2.43,.52s1.67-.18,2.43-.52c.77-.34,1.45-.85,2-1.48h8.03c-1.49,1.87-3.39,3.38-5.55,4.42-2.16,1.04-4.52,1.58-6.92,1.58h0Zm-2-10c0-.4,.12-.78,.34-1.11,.22-.33,.53-.58,.9-.74,.37-.15,.77-.19,1.15-.11,.39,.08,.74,.27,1.02,.55s.47,.64,.55,1.02c.08,.39,.04,.79-.11,1.16-.15,.37-.41,.68-.74,.9-.33,.22-.72,.34-1.11,.34-.53,0-1.04-.21-1.41-.59-.37-.37-.59-.88-.59-1.41h0Zm16.81,0h-8.82c0-1.24-.39-2.45-1.11-3.45-.72-1.01-1.73-1.77-2.89-2.18v-6.37c0-.53-.21-1.04-.59-1.41-.38-.37-.88-.59-1.41-.59s-1.04,.21-1.41,.59c-.37,.37-.59,.88-.59,1.41v6.37c-1.17,.41-2.18,1.17-2.89,2.18-.72,1.01-1.1,2.21-1.11,3.45H9.19c-.52-1.28-.87-2.62-1.04-4h1.86c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41-.21-1.04-.59-1.41-.88-.59-1.41-.59h-1.86c.36-2.84,1.48-5.53,3.24-7.79l1.31,1.31c.19,.19,.41,.33,.65,.43,.24,.1,.5,.15,.76,.15s.52-.05,.76-.15c.24-.1,.46-.25,.65-.43s.33-.41,.43-.65c.1-.24,.15-.5,.15-.76s-.05-.52-.15-.76c-.1-.24-.25-.46-.43-.65l-1.31-1.31c2.26-1.76,4.95-2.88,7.79-3.24v1.86c0,.53,.21,1.04,.59,1.41,.38,.37,.88,.59,1.41,.59s1.04-.21,1.41-.59c.37-.37,.59-.88,.59-1.41v-1.86c2.84,.36,5.53,1.48,7.79,3.24l-1.31,1.31c-.37,.37-.59,.88-.59,1.41s.21,1.04,.59,1.41c.37,.37,.88,.59,1.41,.59s1.04-.21,1.41-.59l1.31-1.31c1.76,2.26,2.88,4.95,3.24,7.79h-1.86c-.53,0-1.04,.21-1.41,.59-.37,.37-.59,.88-.59,1.41s.21,1.04,.59,1.41c.38,.37,.88,.59,1.41,.59h1.86c-.17,1.37-.52,2.72-1.04,4h0Z`,
  ],
}

export const IP = {
  prefix: 'fal',
  iconName: 'ip',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M31.47,14.57c-.77-.38-1.71-.58-2.82-.58h-5.51v13.95h3.4v-5.01h2.11c1.14,0,2.09-.21,2.86-.62s1.34-.95,1.71-1.61v.02c.37-.68,.56-1.42,.56-2.23,0-.87-.19-1.65-.58-2.33-.38-.68-.96-1.21-1.73-1.59Zm-1.63,5.22c-.32,.3-.8,.46-1.45,.46h-1.85v-3.5h1.85c.65,0,1.13,.15,1.45,.46,.32,.3,.48,.74,.48,1.29s-.16,.99-.48,1.29Zm-12.56,8.17h3.4V14.01h-3.4v13.95Zm23.54-8.68c-.28-2.91-1.31-5.69-2.98-8.08h-.01c-1.67-2.38-3.93-4.3-6.57-5.55s-5.55-1.81-8.46-1.6c-2.91,.2-5.72,1.16-8.15,2.77-2.08,1.39-3.84,3.24-5.13,5.39-1.29,2.16-2.09,4.57-2.34,7.07-.25,2.48,.06,4.99,.91,7.34,.85,2.35,2.21,4.48,3.98,6.24l10.54,10.56c.19,.18,.41,.33,.65,.43,.24,.1,.5,.15,.76,.15s.52-.05,.76-.15c.25-.1,.47-.24,.65-.43l10.5-10.56c1.78-1.76,3.13-3.89,3.98-6.24,.84-2.35,1.15-4.86,.91-7.34Zm-4.65,5.98c-.64,1.8-1.68,3.44-3.03,4.79l-9.14,9.14h-.01l-9.14-9.14c-1.35-1.35-2.38-2.98-3.02-4.77-.64-1.8-.88-3.71-.69-5.61,.18-1.93,.8-3.79,1.79-5.45,.99-1.66,2.34-3.08,3.95-4.15,2.11-1.4,4.58-2.15,7.12-2.15s5.01,.75,7.12,2.15c1.6,1.07,2.95,2.49,3.94,4.14,.99,1.65,1.61,3.5,1.8,5.42,.19,1.91-.05,3.83-.69,5.63Z`,
  ],
}

export const IncomingMessages = {
  prefix: 'fal',
  iconName: 'incoming-messages',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M44.85,15.23c-.1-.24-.25-.46-.44-.65-.19-.19-.41-.34-.65-.44-.24-.1-.51-.15-.77-.15s-.53,.05-.77,.15c-.24,.1-.46,.25-.65,.44l-2.58,2.6V6c0-.53-.21-1.04-.59-1.41s-.88-.59-1.41-.59-1.04,.21-1.41,.59-.59,.88-.59,1.41v11.18l-2.58-2.6c-.19-.19-.41-.33-.65-.44-.24-.1-.5-.15-.77-.15-.53,0-1.04,.21-1.42,.59-.19,.19-.33,.41-.44,.65-.1,.24-.15,.5-.15,.77,0,.53,.21,1.04,.59,1.42l6,6c.19,.18,.41,.32,.66,.42,.24,.11,.5,.16,.76,.16s.52-.05,.76-.16c.25-.1,.47-.24,.66-.42l6-6c.19-.19,.34-.41,.44-.65,.1-.24,.15-.51,.15-.77s-.05-.53-.15-.77Zm-3.86,12.77c-.53,0-1.04,.21-1.41,.59s-.59,.88-.59,1.41v8c0,.53-.21,1.04-.59,1.41s-.88,.59-1.41,.59H8.99c-.53,0-1.04-.21-1.41-.59-.38-.38-.59-.88-.59-1.41V18.82l11.76,11.78c1.12,1.12,2.65,1.75,4.24,1.75s3.11-.63,4.24-1.75l3.28-3.28c.38-.38,.59-.89,.59-1.42s-.21-1.04-.59-1.42c-.38-.38-.89-.59-1.42-.59s-1.04,.21-1.42,.59l-3.28,3.28c-.37,.37-.88,.57-1.4,.57s-1.03-.21-1.4-.57l-11.78-11.76h13.18c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41-.21-1.04-.59-1.41-.88-.59-1.41-.59H8.99c-1.59,0-3.12,.63-4.24,1.76s-1.76,2.65-1.76,4.24v20c0,1.59,.63,3.12,1.76,4.24s2.65,1.76,4.24,1.76h28c1.59,0,3.12-.63,4.24-1.76,1.13-1.13,1.76-2.65,1.76-4.24v-8c0-.53-.21-1.04-.59-1.41s-.88-.59-1.41-.59Z`,
  ],
}

export const OutgoingMessages = {
  prefix: 'fal',
  iconName: 'outgoing-messages',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M29.13,12.77c.1,.24,.25,.46,.44,.65s.41,.34,.65,.44c.24,.1,.51,.15,.77,.15s.53-.05,.77-.15c.24-.1,.46-.25,.65-.44l2.58-2.6v11.18c0,.53,.21,1.04,.59,1.41s.88,.59,1.41,.59,1.04-.21,1.41-.59,.59-.88,.59-1.41V10.82l2.58,2.6c.19,.19,.41,.33,.65,.44,.24,.1,.5,.15,.77,.15,.53,0,1.04-.21,1.42-.59,.19-.19,.33-.41,.44-.65,.1-.24,.15-.5,.15-.77,0-.53-.21-1.04-.59-1.42l-6-6c-.19-.18-.41-.32-.66-.42-.24-.11-.5-.16-.76-.16s-.52,.05-.76,.16c-.25,.1-.47,.24-.66,.42l-6,6c-.19,.19-.34,.41-.44,.65s-.15,.51-.15,.77,.05,.53,.15,.77Zm11.86,15.23c-.53,0-1.04,.21-1.41,.59s-.59,.88-.59,1.41v8c0,.53-.21,1.04-.59,1.41s-.88,.59-1.41,.59H9c-.53,0-1.04-.21-1.41-.59-.38-.38-.59-.88-.59-1.41V18.82l11.76,11.78c1.12,1.12,2.65,1.75,4.24,1.75s3.11-.63,4.24-1.75l3.28-3.28c.38-.38,.59-.89,.59-1.42s-.21-1.04-.59-1.42c-.38-.38-.89-.59-1.42-.59s-1.04,.21-1.42,.59l-3.28,3.28c-.37,.37-.88,.57-1.4,.57s-1.03-.21-1.4-.57l-11.78-11.76h13.18c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41-.21-1.04-.59-1.41-.88-.59-1.41-.59H9c-1.59,0-3.12,.63-4.24,1.76s-1.76,2.65-1.76,4.24v20c0,1.59,.63,3.12,1.76,4.24s2.65,1.76,4.24,1.76h28c1.59,0,3.12-.63,4.24-1.76,1.13-1.13,1.76-2.65,1.76-4.24v-8c0-.53-.21-1.04-.59-1.41s-.88-.59-1.41-.59Z`,
  ],
}

export const PendingMessages = {
  prefix: 'fal',
  iconName: 'pending-messages',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M41,27c-.53,0-1.04,.21-1.41,.59s-.59,.88-.59,1.41v8c0,.53-.21,1.04-.59,1.41-.38,.38-.88,.59-1.41,.59H9c-.53,0-1.04-.21-1.41-.59-.38-.38-.59-.88-.59-1.41V17.82l11.76,11.76c1.12,1.12,2.64,1.75,4.22,1.76,.81,0,1.6-.16,2.35-.47,.74-.31,1.41-.77,1.97-1.35,.36-.37,.56-.87,.56-1.39s-.2-1.02-.56-1.39c-.19-.19-.41-.34-.65-.45-.25-.1-.51-.16-.78-.16s-.53,.05-.78,.16c-.25,.1-.47,.25-.65,.45-.37,.37-.88,.57-1.4,.57s-1.03-.21-1.4-.57L9.82,15h9.18c.53,0,1.04-.21,1.41-.59,.38-.38,.59-.88,.59-1.41s-.21-1.04-.59-1.41c-.38-.38-.88-.59-1.41-.59H9c-1.59,0-3.12,.63-4.24,1.76-1.13,1.13-1.76,2.65-1.76,4.24v20c0,1.59,.63,3.12,1.76,4.24,1.13,1.13,2.65,1.76,4.24,1.76h28c1.59,0,3.12-.63,4.24-1.76,1.13-1.13,1.76-2.65,1.76-4.24v-8c0-.53-.21-1.04-.59-1.41s-.88-.59-1.41-.59Zm2-22c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41-1.27-.95-2.74-1.59-4.3-1.86-1.56-.27-3.16-.16-4.68,.31-1.51,.47-2.89,1.3-4.02,2.41-1.13,1.11-1.98,2.47-2.48,3.98-.5,1.5-.64,3.1-.4,4.67,.24,1.57,.85,3.05,1.77,4.34,.93,1.29,2.15,2.33,3.56,3.05,1.41,.72,2.97,1.1,4.56,1.1,2.43,0,4.78-.91,6.6-2.52,.4-.35,.65-.84,.68-1.38,.04-.53-.14-1.05-.49-1.45-.35-.4-.85-.65-1.38-.68-.53-.04-1.05,.14-1.45,.49-.89,.79-1.99,1.29-3.16,1.45-1.17,.16-2.37-.03-3.44-.55-1.07-.52-1.96-1.34-2.56-2.36-.6-1.02-.88-2.2-.81-3.39,.07-1.18,.49-2.32,1.2-3.27,.72-.95,1.7-1.66,2.82-2.05,1.12-.39,2.33-.44,3.48-.14,1.15,.3,2.18,.93,2.97,1.82h-2.46c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41s.21,1.04,.59,1.41c.38,.38,.88,.59,1.41,.59h6c.53,0,1.04-.21,1.41-.59,.38-.38,.59-.88,.59-1.41V6.98c0-.53-.22-1.03-.59-1.4-.37-.37-.88-.58-1.41-.58Z`,
  ],
}

export const Users = {
  prefix: 'fal',
  iconName: 'users',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M20,8c-4.42,0-8,3.58-8,8s3.58,8,8,8,8-3.58,8-8-3.58-8-8-8Zm-12,8c0-6.63,5.37-12,12-12s12,5.37,12,12-5.37,12-12,12-12-5.37-12-12ZM33.66,7.51c.78-.78,2.05-.78,2.83,0,4.69,4.69,4.69,12.28,0,16.97-.78,.78-2.05,.78-2.83,0-.78-.78-.78-2.05,0-2.83,3.12-3.12,3.12-8.19,0-11.31-.78-.78-.78-2.05,0-2.83Zm1.4,26c.27-1.07,1.35-1.72,2.43-1.46,2.66,.66,4.34,2.36,5.3,4.3,.94,1.87,1.21,3.96,1.21,5.64,0,1.1-.9,2-2,2s-2-.9-2-2c0-1.31-.22-2.73-.79-3.86-.53-1.07-1.35-1.87-2.7-2.2-1.07-.27-1.72-1.35-1.46-2.43Zm-22.06,2.49c-2.52,0-5,2.43-5,6,0,1.1-.9,2-2,2s-2-.9-2-2c0-5.26,3.79-10,9-10h14c5.21,0,9,4.74,9,10,0,1.1-.9,2-2,2s-2-.9-2-2c0-3.57-2.48-6-5-6H13Z`,
  ],
}

export const Comments = {
  prefix: 'fal',
  iconName: 'comments',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M34,14H14c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41s.21,1.04,.59,1.41c.38,.38,.88,.59,1.41,.59h20c.53,0,1.04-.21,1.41-.59,.38-.38,.59-.88,.59-1.41s-.21-1.04-.59-1.41c-.38-.38-.88-.59-1.41-.59Zm0,8H14c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41s.21,1.04,.59,1.41c.38,.38,.88,.59,1.41,.59h20c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41-.21-1.04-.59-1.41-.88-.59-1.41-.59ZM38,4H10c-1.59,0-3.12,.63-4.24,1.76s-1.76,2.65-1.76,4.24V30c0,1.59,.63,3.12,1.76,4.24,1.13,1.13,2.65,1.76,4.24,1.76h23.18l7.4,7.42c.19,.19,.41,.33,.65,.43,.24,.1,.5,.15,.77,.15,.26,0,.52-.05,.76-.16,.37-.15,.68-.4,.9-.73,.22-.33,.34-.71,.34-1.11V10c0-1.59-.63-3.12-1.76-4.24-1.13-1.13-2.65-1.76-4.24-1.76Zm2,33.18l-4.58-4.6c-.19-.19-.41-.33-.65-.43-.24-.1-.5-.15-.77-.15H10c-.53,0-1.04-.21-1.41-.59-.38-.38-.59-.88-.59-1.41V10c0-.53,.21-1.04,.59-1.41,.38-.38,.88-.59,1.41-.59h28c.53,0,1.04,.21,1.41,.59,.38,.38,.59,.88,.59,1.41v27.18Z`,
  ],
}

export const Listeners = {
  prefix: 'fal',
  iconName: 'listeners',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M21,6c-3.71,0-7.27,1.47-9.9,4.1-2.63,2.63-4.1,6.19-4.1,9.9,0,.53,.21,1.04,.59,1.41,.38,.38,.88,.59,1.41,.59s1.04-.21,1.41-.59c.38-.38,.59-.88,.59-1.41,0-2.65,1.05-5.2,2.93-7.07,1.88-1.88,4.42-2.93,7.07-2.93s5.2,1.05,7.07,2.93c1.88,1.88,2.93,4.42,2.93,7.07,0,1.81-.5,3.58-1.42,5.14l-7.12,14.86c-.35,.61-.85,1.11-1.46,1.46-.61,.35-1.3,.54-2,.54-1.06,0-2.08-.42-2.83-1.17-.75-.75-1.17-1.77-1.17-2.83,0-.53-.21-1.04-.59-1.41-.38-.38-.88-.59-1.41-.59s-1.04,.21-1.41,.59c-.38,.38-.59,.88-.59,1.41,0,2.12,.84,4.16,2.34,5.66,1.5,1.5,3.54,2.34,5.66,2.34,1.43,0,2.83-.39,4.07-1.11,1.23-.73,2.24-1.77,2.93-3.03l7.12-14.86c1.24-2.13,1.89-4.54,1.88-7,0-3.71-1.47-7.27-4.1-9.9-2.63-2.63-6.19-4.1-9.9-4.1ZM7,30c-.4,0-.78,.12-1.11,.34-.33,.22-.59,.53-.74,.9-.15,.37-.19,.77-.11,1.16,.08,.39,.27,.74,.55,1.02,.28,.28,.64,.47,1.02,.55,.39,.08,.79,.04,1.16-.11,.37-.15,.68-.41,.9-.74,.22-.33,.34-.72,.34-1.11,0-.53-.21-1.04-.59-1.41-.38-.38-.88-.59-1.41-.59ZM34.18,2.4c-.21-.16-.45-.27-.7-.34-.25-.07-.52-.08-.78-.04-.26,.04-.51,.13-.74,.26-.23,.13-.42,.31-.58,.52s-.27,.45-.34,.7c-.07,.25-.08,.52-.04,.78,.04,.26,.13,.51,.26,.74s.31,.42,.52,.58c2.24,1.67,4.06,3.85,5.31,6.35s1.91,5.26,1.91,8.05c0,.53,.21,1.04,.59,1.41s.88,.59,1.41,.59,1.04-.21,1.41-.59,.59-.88,.59-1.41c-.01-3.42-.81-6.78-2.34-9.84-1.53-3.05-3.75-5.71-6.48-7.76Zm-13.18,15.6c.53,0,1.04,.21,1.41,.59,.38,.38,.59,.88,.59,1.41s.21,1.04,.59,1.41,.88,.59,1.41,.59,1.04-.21,1.41-.59,.59-.88,.59-1.41c0-1.59-.63-3.12-1.76-4.24-1.13-1.13-2.65-1.76-4.24-1.76s-3.12,.63-4.24,1.76c-1.13,1.13-1.76,2.65-1.76,4.24,0,1.2,.35,2.37,1.02,3.36,.28,.39,.59,.75,.94,1.08l.4,.44c.22,.33,.34,.72,.34,1.11s-.12,.78-.34,1.11c-.3,.44-.42,.97-.32,1.49,.09,.52,.39,.98,.82,1.29,.33,.23,.73,.36,1.14,.36,.32,0,.64-.08,.93-.23,.29-.15,.53-.37,.71-.63,.68-1,1.05-2.18,1.05-3.39s-.37-2.39-1.05-3.39c-.21-.3-.44-.58-.7-.84l-.28-.28c-.12-.11-.22-.23-.32-.36-.22-.33-.34-.72-.34-1.12,0-.53,.21-1.04,.59-1.41,.38-.38,.88-.59,1.41-.59Zm-8,12c.4,0,.78-.12,1.11-.34,.33-.22,.59-.53,.74-.9,.15-.37,.19-.77,.11-1.16-.08-.39-.27-.74-.55-1.02-.28-.28-.64-.47-1.02-.55-.39-.08-.79-.04-1.16,.11-.37,.15-.68,.41-.9,.74-.22,.33-.34,.72-.34,1.11,0,.53,.21,1.04,.59,1.41,.38,.38,.88,.59,1.41,.59Z`,
  ],
}

export const Delete = {
  prefix: 'fal',
  iconName: 'delete',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M42,12h-3.98s-.02,0-.04,0h-3.98v-4c0-2.21-1.79-4-4-4h-12c-2.21,0-4,1.79-4,4v4h-3.98s-.02,0-.04,0h-3.98c-1.1,0-2,.9-2,2s.9,2,2,2h2.14l1.73,24.28c.15,2.09,1.89,3.72,3.99,3.72h20.28c2.1,0,3.84-1.62,3.99-3.72l1.73-24.28h2.14c1.1,0,2-.9,2-2s-.9-2-2-2Zm-24-4h12v4h-12v-4Zm16.14,32H13.86l-1.71-24h23.7l-1.71,24Zm-14.14-4c1.1,0,2-.9,2-2v-12c0-1.1-.9-2-2-2s-2,.9-2,2v12c0,1.1,.9,2,2,2Zm8,0c1.1,0,2-.9,2-2v-12c0-1.1-.9-2-2-2s-2,.9-2,2v12c0,1.1,.9,2,2,2Z`,
  ],
}

export const Repeat = {
  prefix: 'fal',
  iconName: 'repeat',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M11,35h-3V13h15.6l-1.6,1.58c-.38,.37-.59,.88-.59,1.41,0,.53,.21,1.04,.58,1.42,.37,.38,.88,.59,1.41,.59,.53,0,1.04-.21,1.42-.58l5-5c.19-.19,.34-.41,.44-.65,.1-.24,.15-.51,.15-.77s-.05-.53-.15-.77c-.1-.24-.25-.46-.44-.65l-5-5c-.37-.37-.88-.58-1.41-.58s-1.04,.21-1.41,.58c-.19,.19-.34,.41-.44,.65-.1,.24-.15,.51-.15,.77s.05,.53,.15,.77c.1,.24,.25,.46,.44,.65l1.58,1.58H6c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41v26c0,.53,.21,1.04,.59,1.41,.38,.38,.88,.59,1.41,.59h5c.53,0,1.04-.21,1.41-.59,.38-.38,.59-.88,.59-1.41s-.21-1.04-.59-1.41c-.38-.38-.88-.59-1.41-.59ZM42,9h-5c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41s.21,1.04,.59,1.41,.88,.59,1.41,.59h3v22H23.26l1.58-1.58c.19-.19,.34-.41,.44-.65,.1-.24,.15-.51,.15-.77s-.05-.53-.15-.77c-.1-.24-.25-.46-.44-.65-.37-.37-.88-.58-1.41-.58s-1.04,.21-1.41,.58l-5,5c-.19,.19-.34,.41-.44,.65-.1,.24-.15,.51-.15,.77s.05,.53,.15,.77c.1,.24,.25,.46,.44,.65l5,5c.38,.37,.89,.58,1.42,.58,.53,0,1.04-.21,1.41-.59,.37-.38,.58-.89,.58-1.42,0-.53-.21-1.04-.59-1.41l-1.58-1.58h18.74c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41V11c0-.53-.21-1.04-.59-1.41-.38-.38-.88-.59-1.41-.59Z`,
  ],
}

export const BlockMessages = {
  prefix: 'fal',
  iconName: 'block-messages',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M40.96,27c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41v8c0,.53-.21,1.04-.59,1.41-.38,.38-.88,.59-1.41,.59H8.96c-.53,0-1.04-.21-1.41-.59-.38-.38-.59-.88-.59-1.41V17.82l11.76,11.76c1.12,1.12,2.64,1.75,4.22,1.76,.81,0,1.6-.16,2.34-.47,.74-.31,1.41-.77,1.97-1.35,.36-.37,.56-.87,.56-1.39s-.2-1.02-.56-1.39c-.19-.19-.41-.34-.65-.45-.25-.1-.51-.16-.78-.16s-.53,.05-.78,.16c-.25,.1-.47,.25-.65,.45-.37,.37-.88,.57-1.4,.57s-1.03-.21-1.4-.57L9.78,15h9.18c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41-.21-1.04-.59-1.41-.88-.59-1.41-.59H8.96c-1.59,0-3.12,.63-4.24,1.76s-1.76,2.65-1.76,4.24v20c0,1.59,.63,3.12,1.76,4.24,1.13,1.13,2.65,1.76,4.24,1.76h28c1.59,0,3.12-.63,4.24-1.76,1.13-1.13,1.76-2.65,1.76-4.24v-8c0-.53-.21-1.04-.59-1.41-.38-.38-.88-.59-1.41-.59Zm2.93-16.66c-1.08-2.05-2.84-3.66-4.97-4.56-2.14-.9-4.52-1.02-6.74-.36s-4.14,2.08-5.43,4c-1.29,1.92-1.88,4.23-1.66,6.54,.22,2.31,1.24,4.46,2.87,6.1,1.86,1.86,4.37,2.92,7,2.94,2.32,.02,4.57-.77,6.37-2.22,1.8-1.46,3.04-3.49,3.51-5.76,.47-2.27,.13-4.63-.95-6.68Zm-14.86,3.65c.21-1.23,.81-2.35,1.69-3.23,1.12-1.13,2.65-1.76,4.24-1.76,.89,0,1.76,.21,2.56,.6l-8,8c-.53-1.12-.71-2.39-.49-3.61Zm11.86,2.02c-.21,1.23-.81,2.35-1.69,3.23-.88,.87-2,1.45-3.22,1.65-1.22,.21-2.47,.03-3.58-.49l8-8c.53,1.12,.71,2.39,.49,3.61Z`,
  ],
}

export const VM = {
  prefix: 'fal',
  iconName: 'vm',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M43.32,13.31h-.01c-.04-.15-.11-.29-.19-.43-.09-.15-.2-.3-.33-.42-.49-.69-1.13-1.26-1.86-1.69L26.99,2.8c-.91-.52-1.94-.8-2.99-.8s-2.08,.28-2.99,.8L7.06,10.77c-.75,.43-1.39,1.02-1.88,1.71-.12,.12-.22,.25-.3,.4-.07,.13-.13,.26-.18,.4-.41,.83-.63,1.74-.63,2.67v15.95c0,1.05,.28,2.08,.8,2.99,.52,.91,1.28,1.66,2.19,2.19l13.95,7.97c.63,.36,1.32,.61,2.03,.72,.29,.16,.62,.24,.95,.24s.66-.08,.95-.24c.72-.11,1.41-.36,2.05-.72l13.95-7.96c.91-.52,1.66-1.28,2.19-2.19,.52-.91,.8-1.94,.8-2.99V15.95c0-.92-.21-1.82-.61-2.64Zm-21.33,27.71h0l-12.94-7.4c-.3-.18-.55-.43-.73-.73-.17-.3-.27-.65-.27-1v-14.87l13.94,8.07v15.92h0Zm2.02-19.37h-.01L10.13,13.61l12.88-7.36c.3-.18,.65-.27,1-.27s.7,.09,1,.27l12.87,7.36-13.87,8.03Zm15.65,11.25c-.17,.3-.43,.55-.73,.73l-12.95,7.4v-15.93l13.94-8.07v14.87h.01c0,.35-.09,.69-.27,1Z`,
  ],
}

export const DataStore = {
  prefix: 'fal',
  iconName: 'datastore',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M16,34c-.4,0-.78,.12-1.11,.34-.33,.22-.59,.53-.74,.9-.15,.37-.19,.77-.11,1.16,.08,.39,.27,.74,.55,1.02,.28,.28,.64,.47,1.02,.55,.39,.08,.79,.04,1.16-.11,.37-.15,.68-.41,.9-.74,.22-.33,.34-.72,.34-1.11,0-.53-.21-1.04-.59-1.41-.38-.38-.88-.59-1.41-.59Zm0-12c-.4,0-.78,.12-1.11,.34-.33,.22-.59,.53-.74,.9-.15,.37-.19,.77-.11,1.16,.08,.39,.27,.74,.55,1.02,.28,.28,.64,.47,1.02,.55,.39,.08,.79,.04,1.16-.11,.37-.15,.68-.41,.9-.74,.22-.33,.34-.72,.34-1.11,0-.53-.21-1.04-.59-1.41-.38-.38-.88-.59-1.41-.59ZM32,4H16c-2.12,0-4.16,.84-5.66,2.34-1.5,1.5-2.34,3.54-2.34,5.66v24c0,2.12,.84,4.16,2.34,5.66,1.5,1.5,3.54,2.34,5.66,2.34h16c2.12,0,4.16-.84,5.66-2.34,1.5-1.5,2.34-3.53,2.34-5.66V12c0-2.12-.84-4.16-2.34-5.66-1.5-1.5-3.53-2.34-5.66-2.34Zm4,32c0,1.06-.42,2.08-1.17,2.83-.75,.75-1.77,1.17-2.83,1.17H16c-1.06,0-2.08-.42-2.83-1.17-.75-.75-1.17-1.77-1.17-2.83v-5.12c1.21,.73,2.59,1.11,4,1.12h16c1.41,0,2.79-.39,4-1.12v5.12Zm0-12c0,1.06-.42,2.08-1.17,2.83-.75,.75-1.77,1.17-2.83,1.17H16c-1.06,0-2.08-.42-2.83-1.17-.75-.75-1.17-1.77-1.17-2.83v-5.12c1.21,.73,2.59,1.11,4,1.12h16c1.41,0,2.79-.39,4-1.12v5.12Zm-4-8H16c-1.06,0-2.08-.42-2.83-1.17-.75-.75-1.17-1.77-1.17-2.83s.42-2.08,1.17-2.83c.75-.75,1.77-1.17,2.83-1.17h16c1.06,0,2.08,.42,2.83,1.17,.75,.75,1.17,1.77,1.17,2.83s-.42,2.08-1.17,2.83c-.75,.75-1.77,1.17-2.83,1.17Z`,
  ],
}

export const Swap = {
  prefix: 'fal',
  iconName: 'swap',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M14,24.39H6c-2.21,0-4,1.79-4,4v8c0,2.21,1.79,4,4,4H14c2.21,0,4-1.79,4-4v-8c0-2.21-1.79-4-4-4Zm0,12H6v-8H14v8Zm25.15-15.93c1.1,0,2.01-.88,2.01-1.99l.05-8c0-1.1-.88-2.01-1.99-2.01s-2.01,.88-2.01,1.99l-.02,3.17c-2.52-2.58-5.7-4.44-9.22-5.36-4.19-1.1-8.63-.81-12.65,.83-4.01,1.64-7.39,4.54-9.61,8.26-.57,.95-.26,2.18,.69,2.74,.95,.57,2.18,.26,2.74-.69,1.78-2.98,4.48-5.3,7.69-6.61,3.21-1.31,6.76-1.55,10.12-.66,2.82,.74,5.37,2.23,7.39,4.3l-3.17-.02c-1.1,0-2.01,.88-2.01,1.99,0,1.1,.88,2.01,1.99,2.01l8,.05Zm2.85,3.93h-8c-2.21,0-4,1.79-4,4v8c0,2.21,1.79,4,4,4h8c2.21,0,4-1.79,4-4v-8c0-2.21-1.79-4-4-4Zm0,12h-8v-8h8v8Z`,
  ],
}

export const CirclePlay = {
  prefix: 'fal',
  iconName: 'circle-play',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M32,20.54l-10-5.78c-.61-.35-1.3-.54-2-.54-.7,0-1.39,.18-2,.53-.61,.35-1.11,.85-1.46,1.46-.35,.61-.54,1.3-.54,2v11.56c0,.7,.19,1.39,.54,2,.35,.61,.86,1.11,1.46,1.46,.61,.35,1.3,.54,2,.54s1.39-.18,2-.54l10-5.78c.61-.35,1.11-.86,1.46-1.46,.35-.61,.53-1.3,.53-2s-.18-1.39-.53-2c-.35-.61-.85-1.11-1.46-1.46h0Zm-2,3.46l-10,5.78v-11.56l10,5.78ZM24,4c-3.96,0-7.82,1.17-11.11,3.37-3.29,2.2-5.85,5.32-7.37,8.98-1.51,3.65-1.91,7.68-1.14,11.56,.77,3.88,2.68,7.44,5.47,10.24,2.8,2.8,6.36,4.7,10.24,5.47,3.88,.77,7.9,.38,11.56-1.14,3.65-1.51,6.78-4.08,8.98-7.37,2.2-3.29,3.37-7.16,3.37-11.11,0-2.63-.52-5.23-1.52-7.65-1.01-2.43-2.48-4.63-4.34-6.49-1.86-1.86-4.06-3.33-6.49-4.34-2.43-1.01-5.03-1.52-7.65-1.52h0Zm0,36c-3.16,0-6.26-.94-8.89-2.7-2.63-1.76-4.68-4.26-5.89-7.18-1.21-2.92-1.53-6.14-.91-9.24,.62-3.1,2.14-5.95,4.38-8.19,2.24-2.24,5.09-3.76,8.19-4.38,3.1-.62,6.32-.3,9.24,.91,2.92,1.21,5.42,3.26,7.18,5.89,1.76,2.63,2.7,5.72,2.7,8.89,0,4.24-1.69,8.31-4.69,11.31-3,3-7.07,4.69-11.31,4.69h0Z`,
  ],
}

export const CirclePause = {
  prefix: 'fal',
  iconName: 'circle-pause',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M20,14c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41v16c0,.53,.21,1.04,.59,1.41s.88,.59,1.41,.59,1.04-.21,1.41-.59,.59-.88,.59-1.41V16c0-.53-.21-1.04-.59-1.41-.38-.38-.88-.59-1.41-.59Zm8,0c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41v16c0,.53,.21,1.04,.59,1.41s.88,.59,1.41,.59,1.04-.21,1.41-.59,.59-.88,.59-1.41V16c0-.53-.21-1.04-.59-1.41-.38-.38-.88-.59-1.41-.59Zm14.48,2.35c-1.01-2.43-2.48-4.63-4.34-6.49-1.86-1.86-4.06-3.33-6.49-4.34-2.43-1.01-5.03-1.52-7.65-1.52-3.96,0-7.82,1.17-11.11,3.37-3.29,2.2-5.85,5.32-7.37,8.98-1.51,3.65-1.91,7.68-1.14,11.56,.77,3.88,2.68,7.44,5.47,10.24,2.8,2.8,6.36,4.7,10.24,5.47,3.88,.77,7.9,.38,11.56-1.14,3.65-1.51,6.78-4.08,8.98-7.37,2.2-3.29,3.37-7.16,3.37-11.11,0-2.63-.52-5.23-1.52-7.65Zm-7.16,18.97c-3,3-7.07,4.69-11.31,4.69-3.16,0-6.26-.94-8.89-2.7-2.63-1.76-4.68-4.26-5.89-7.18-1.21-2.92-1.53-6.14-.91-9.24,.62-3.1,2.14-5.95,4.38-8.19,2.24-2.24,5.09-3.76,8.19-4.38,3.1-.62,6.32-.3,9.24,.91s5.42,3.26,7.18,5.89c1.76,2.63,2.7,5.72,2.7,8.89,0,4.24-1.69,8.31-4.69,11.31Z`,
  ],
}

export const Interface = {
  prefix: 'fal',
  iconName: 'interface',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M44,12.2h-8v-2.8c0-1-.8-1.8-1.8-1.8h-1.4v-2.6c0-1-.8-1.8-1.8-1.8h-14c-1,0-1.8,.8-1.8,1.8v2.6h-1.8c-1,0-1.8,.8-1.8,1.8v2.8H4c-1,0-1.8,.8-1.8,1.8v29c0,1,.8,1.8,1.8,1.8H44c1,0,1.8-.8,1.8-1.8V14c0-1-.8-1.8-1.8-1.8Zm-1.8,29H5.8V15.8h7.6c1,0,1.8-.8,1.8-1.8v-2.8h1.8c1,0,1.8-.8,1.8-1.8v-2.6h10.4v2.6c0,1,.8,1.8,1.8,1.8h1.4v2.8c0,1,.8,1.8,1.8,1.8h8v25.4ZM9.8,22.2v13.2c0,1,.8,1.8,1.8,1.8h25.2c1,0,1.8-.8,1.8-1.8v-13.2c0-1-.8-1.8-1.8-1.8H11.6c-1,0-1.8,.8-1.8,1.8Zm3.6,1.8h21.6v9.6h-4.4v-3.2c0-1-.8-1.8-1.8-1.8s-1.8,.8-1.8,1.8v3.2h-5.4v-3.2c0-1-.8-1.8-1.8-1.8s-1.8,.8-1.8,1.8v3.2h-4.6v-9.6Z`,
  ],
}

export const BufferMiss = {
  prefix: 'fal',
  iconName: 'buffer-miss',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M40.97,27.88l-.68,.4c-.46,.27-.79,.71-.92,1.22-.13,.51-.05,1.06,.22,1.51,.18,.3,.43,.55,.73,.72,.3,.17,.64,.26,.99,.26,.36,0,.71-.1,1.02-.28l.68-.4c.4-.29,.68-.72,.78-1.2,.1-.48,.02-.99-.23-1.42-.25-.43-.65-.75-1.12-.9-.47-.15-.98-.12-1.43,.08h-.04ZM23.3,12.72l.7-.42,13.99,8-3.52,2c-.39,.22-.69,.56-.86,.97s-.2,.86-.09,1.29c.11,.43,.37,.81,.72,1.07,.35,.27,.78,.41,1.23,.41,.35,0,.69-.09,1-.26l6.52-3.78c.31-.17,.56-.43,.74-.73,.18-.31,.27-.65,.27-1.01s-.09-.7-.27-1.01c-.18-.31-.43-.56-.74-.73L25,8.15c-.3-.18-.65-.27-1-.27s-.7,.09-1,.27l-1.7,.98c-.46,.27-.8,.7-.94,1.22-.14,.51-.07,1.06,.2,1.52,.27,.46,.7,.8,1.22,.94,.51,.14,1.06,.07,1.52-.2v.12ZM7.43,4.59c-.19-.19-.41-.33-.65-.44-.24-.1-.5-.15-.77-.15s-.52,.05-.77,.15c-.24,.1-.46,.25-.65,.44-.38,.38-.59,.89-.59,1.42s.21,1.04,.59,1.42l7.28,7.26-6.86,4c-.31,.17-.56,.43-.74,.73-.18,.31-.27,.65-.27,1.01s.09,.7,.27,1.01c.18,.31,.43,.56,.74,.73l17.99,10.39c.31,.17,.65,.25,1,.26,.35,0,.69-.1,1-.26l3-1.76,2.9,2.92-6.88,4L7.01,27.86c-.46-.27-1.01-.34-1.52-.2-.51,.14-.95,.48-1.22,.94-.27,.46-.34,1.01-.2,1.52s.48,.95,.94,1.22l17.99,10.39c.3,.18,.65,.27,1,.27s.7-.09,1-.27l8.82-5.1,6.76,6.78c.19,.19,.41,.34,.65,.44,.24,.1,.5,.15,.77,.15s.53-.05,.77-.15c.24-.1,.46-.25,.65-.44,.19-.19,.34-.41,.44-.65,.1-.24,.15-.5,.15-.77s-.05-.53-.15-.77c-.1-.24-.25-.46-.44-.65L7.43,4.59ZM24,28.58l-13.99-8.2,4.8-2.76,10.23,10.25-1.04,.7Z`,
  ],
}

export const Router = {
  prefix: 'fal',
  iconName: 'router',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M17.84,33.88c-.39,0-.77,.12-1.1,.33-.33,.22-.58,.53-.73,.89-.15,.36-.19,.76-.11,1.14,.08,.38,.26,.74,.54,1.01,.28,.28,.63,.46,1.01,.54,.38,.08,.78,.04,1.14-.11,.36-.15,.67-.4,.89-.73,.22-.33,.33-.71,.33-1.1,0-.52-.21-1.03-.58-1.4-.37-.37-.87-.58-1.4-.58Zm-5.93,0c-.39,0-.77,.12-1.1,.33-.33,.22-.58,.53-.73,.89-.15,.36-.19,.76-.11,1.14,.08,.38,.26,.74,.54,1.01,.28,.28,.63,.46,1.01,.54,.38,.08,.78,.04,1.14-.11,.36-.15,.67-.4,.89-.73,.22-.33,.33-.71,.33-1.1,0-.52-.21-1.03-.58-1.4-.37-.37-.87-.58-1.4-.58Zm11.87,0c-.39,0-.77,.12-1.1,.33-.33,.22-.58,.53-.73,.89-.15,.36-.19,.76-.11,1.14s.26,.74,.54,1.01c.28,.28,.63,.46,1.01,.54,.38,.08,.78,.04,1.14-.11,.36-.15,.67-.4,.89-.73,.22-.33,.33-.71,.33-1.1,0-.52-.21-1.03-.58-1.4-.37-.37-.87-.58-1.4-.58Zm6.13-16.81c.17-.3,.42-.55,.72-.72,.3-.17,.64-.26,.99-.26s.69,.09,.99,.26c.3,.17,.55,.42,.72,.72,.26,.45,.69,.78,1.19,.92,.5,.14,1.04,.07,1.5-.19,.23-.12,.44-.29,.61-.49,.17-.2,.29-.44,.37-.69,.08-.25,.1-.52,.07-.78-.03-.26-.11-.52-.24-.75-.52-.9-1.27-1.65-2.17-2.18-.9-.52-1.93-.8-2.97-.8s-2.07,.27-2.97,.8c-.9,.52-1.65,1.27-2.17,2.18-.17,.44-.17,.92-.02,1.36,.15,.44,.46,.81,.86,1.05,.4,.24,.88,.32,1.34,.24,.46-.08,.88-.32,1.18-.68Zm13.84-5.93c-1.22-2.1-2.96-3.85-5.07-5.06-2.1-1.21-4.49-1.85-6.92-1.85s-4.81,.64-6.92,1.85c-2.1,1.21-3.85,2.96-5.07,5.06-.13,.23-.22,.48-.25,.73-.03,.26-.02,.52,.05,.77,.07,.25,.19,.49,.34,.69,.16,.21,.36,.38,.58,.51,.27,.15,.58,.24,.89,.26,.35,0,.69-.09,.99-.26,.3-.17,.55-.42,.73-.73,.87-1.5,2.12-2.75,3.62-3.62,1.5-.87,3.21-1.32,4.94-1.32s3.44,.46,4.94,1.32c1.5,.87,2.75,2.12,3.62,3.62,.13,.23,.3,.43,.51,.58,.21,.16,.44,.28,.69,.34s.51,.09,.77,.05c.26-.03,.51-.12,.73-.25,.23-.12,.44-.29,.6-.5,.17-.2,.29-.44,.36-.69,.07-.25,.1-.52,.07-.78-.03-.26-.11-.51-.24-.74h0Zm-8.11,16.81h-1.98v-5.93c0-.52-.21-1.03-.58-1.4-.37-.37-.87-.58-1.4-.58s-1.03,.21-1.4,.58c-.37,.37-.58,.87-.58,1.4v5.93H9.93c-1.57,0-3.08,.63-4.19,1.74-1.11,1.11-1.74,2.62-1.74,4.19v3.96c0,1.57,.63,3.08,1.74,4.2,1.11,1.11,2.62,1.74,4.19,1.74h25.71c1.57,0,3.08-.62,4.19-1.74,1.11-1.11,1.74-2.62,1.74-4.2v-3.96c0-1.57-.63-3.08-1.74-4.19-1.11-1.11-2.62-1.74-4.19-1.74Zm1.98,9.89c0,.52-.21,1.03-.58,1.4-.37,.37-.87,.58-1.4,.58H9.93c-.52,0-1.03-.21-1.4-.58-.37-.37-.58-.87-.58-1.4v-3.96c0-.52,.21-1.03,.58-1.4,.37-.37,.87-.58,1.4-.58h25.71c.52,0,1.03,.21,1.4,.58,.37,.37,.58,.87,.58,1.4v3.96Z`,
  ],
}

// export const HardwareSensor = {
//   prefix: 'fal',
//   iconName: 'hardware-sensor',
//   icon: [
//     48,
//     48,
//     [],
//     'f0000',
//     `M16 34C15.6044 34 15.2178 34.1173 14.8889 34.3371C14.56 34.5568 14.3036 34.8692 14.1522 35.2346C14.0009 35.6001 13.9613 36.0022 14.0384 36.3902C14.1156 36.7781 14.3061 37.1345 14.5858 37.4142C14.8655 37.6939 15.2219 37.8844 15.6098 37.9616C15.9978 38.0387 16.3999 37.9991 16.7654 37.8478C17.1308 37.6964 17.4432 37.44 17.6629 37.1111C17.8827 36.7822 18 36.3956 18 36C18 35.4696 17.7893 34.9609 17.4142 34.5858C17.0391 34.2107 16.5304 34 16 34V34ZM16 22C15.6044 22 15.2178 22.1173 14.8889 22.3371C14.56 22.5568 14.3036 22.8692 14.1522 23.2346C14.0009 23.6001 13.9613 24.0022 14.0384 24.3902C14.1156 24.7781 14.3061 25.1345 14.5858 25.4142C14.8655 25.6939 15.2219 25.8844 15.6098 25.9616C15.9978 26.0387 16.3999 25.9991 16.7654 25.8478C17.1308 25.6964 17.4432 25.44 17.6629 25.1111C17.8827 24.7822 18 24.3956 18 24C18 23.4696 17.7893 22.9609 17.4142 22.5858C17.0391 22.2107 16.5304 22 16 22V22ZM16 10C15.6044 10 15.2178 10.1173 14.8889 10.3371C14.56 10.5568 14.3036 10.8692 14.1522 11.2346C14.0009 11.6001 13.9613 12.0022 14.0384 12.3902C14.1156 12.7781 14.3061 13.1345 14.5858 13.4142C14.8655 13.6939 15.2219 13.8844 15.6098 13.9616C15.9978 14.0387 16.3999 13.9991 16.7654 13.8478C17.1308 13.6964 17.4432 13.44 17.6629 13.1111C17.8827 12.7822 18 12.3956 18 12C18 11.4696 17.7893 10.9609 17.4142 10.5858C17.0391 10.2107 16.5304 10 16 10V10ZM40 10C40 8.4087 39.3679 6.88258 38.2426 5.75736C37.1174 4.63214 35.5913 4 34 4H14C12.4087 4 10.8826 4.63214 9.75736 5.75736C8.63214 6.88258 8 8.4087 8 10V14C8.00882 15.4801 8.56442 16.9047 9.56 18C8.56442 19.0953 8.00882 20.5199 8 22V26C8.00882 27.4801 8.56442 28.9047 9.56 30C8.56442 31.0953 8.00882 32.5199 8 34V38C8 39.5913 8.63214 41.1174 9.75736 42.2426C10.8826 43.3679 12.4087 44 14 44H34C35.5913 44 37.1174 43.3679 38.2426 42.2426C39.3679 41.1174 40 39.5913 40 38V34C39.9912 32.5199 39.4356 31.0953 38.44 30C39.4356 28.9047 39.9912 27.4801 40 26V22C39.9912 20.5199 39.4356 19.0953 38.44 18C39.4356 16.9047 39.9912 15.4801 40 14V10ZM36 38C36 38.5304 35.7893 39.0391 35.4142 39.4142C35.0391 39.7893 34.5304 40 34 40H14C13.4696 40 12.9609 39.7893 12.5858 39.4142C12.2107 39.0391 12 38.5304 12 38V34C12 33.4696 12.2107 32.9609 12.5858 32.5858C12.9609 32.2107 13.4696 32 14 32H34C34.5304 32 35.0391 32.2107 35.4142 32.5858C35.7893 32.9609 36 33.4696 36 34V38ZM36 26C36 26.5304 35.7893 27.0391 35.4142 27.4142C35.0391 27.7893 34.5304 28 34 28H14C13.4696 28 12.9609 27.7893 12.5858 27.4142C12.2107 27.0391 12 26.5304 12 26V22C12 21.4696 12.2107 20.9609 12.5858 20.5858C12.9609 20.2107 13.4696 20 14 20H34C34.5304 20 35.0391 20.2107 35.4142 20.5858C35.7893 20.9609 36 21.4696 36 22V26ZM36 14C36 14.5304 35.7893 15.0391 35.4142 15.4142C35.0391 15.7893 34.5304 16 34 16H14C13.4696 16 12.9609 15.7893 12.5858 15.4142C12.2107 15.0391 12 14.5304 12 14V10C12 9.46957 12.2107 8.96086 12.5858 8.58579C12.9609 8.21071 13.4696 8 14 8H34C34.5304 8 35.0391 8.21071 35.4142 8.58579C35.7893 8.96086 36 9.46957 36 10V14Z`,
//   ],
// }

export const CloudWifi = {
  prefix: 'fal',
  iconName: 'cloud-wifi',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M17.04,11.86c2.13-1.21,4.54-1.84,7-1.84s4.86,.64,7,1.84c.23,.13,.48,.22,.74,.25,.26,.03,.53,.02,.78-.05,.25-.07,.49-.19,.7-.35,.21-.16,.38-.36,.51-.59,.26-.46,.33-1,.19-1.51-.14-.51-.47-.94-.93-1.21-2.74-1.55-5.84-2.37-9-2.37s-6.25,.82-9,2.37c-.46,.26-.79,.7-.93,1.21-.14,.51-.07,1.05,.19,1.51,.13,.23,.31,.43,.51,.59,.21,.16,.45,.28,.7,.35s.52,.09,.78,.05c.26-.03,.51-.12,.74-.25Zm19.83,10.53c-.21-.49-.45-.96-.72-1.42-.14-.23-.32-.42-.53-.58-.21-.16-.45-.27-.71-.33-.26-.06-.52-.07-.78-.03s-.51,.13-.73,.27c-.45,.28-.77,.72-.89,1.23-.12,.51-.04,1.05,.23,1.51,.31,.53,.56,1.1,.76,1.68,.11,.34,.32,.65,.59,.89,.27,.24,.61,.39,.96,.45,1.39,.24,2.65,.96,3.56,2.04,.91,1.08,1.41,2.44,1.41,3.86,0,1.59-.63,3.12-1.76,4.24-1.12,1.12-2.65,1.76-4.24,1.76H11.37c-.82-.13-1.57-.5-2.17-1.07-.59-.57-1-1.31-1.15-2.13-.12-.59-.11-1.19,.04-1.78,.15-.58,.42-1.12,.81-1.58s.87-.83,1.41-1.08c.55-.25,1.14-.37,1.74-.36,.53,0,1.04-.21,1.41-.59,.37-.37,.59-.88,.59-1.41,0-1.75,.46-3.47,1.34-4.98,.13-.23,.22-.48,.25-.74,.03-.26,.02-.53-.05-.78-.07-.25-.19-.49-.35-.7-.16-.21-.36-.38-.59-.51-.46-.26-1-.33-1.51-.19-.51,.14-.94,.47-1.21,.93-.94,1.59-1.53,3.36-1.76,5.2-1.67,.41-3.16,1.34-4.26,2.66-.93,1.1-1.55,2.42-1.79,3.84-.24,1.42-.1,2.88,.42,4.22,.52,1.34,1.39,2.52,2.52,3.41,1.13,.89,2.48,1.46,3.91,1.64h23.07c2.4,0,4.71-.87,6.52-2.45,1.81-1.57,3-3.74,3.34-6.11,.34-2.37-.18-4.79-1.47-6.81-1.29-2.02-3.26-3.51-5.56-4.2h0Zm-7.1-4.42v-.12c.1-.12,.19-.26,.26-.4,.19-.49,.18-1.04-.04-1.52-.21-.48-.61-.86-1.1-1.05-1.12-.42-2.29-.67-3.48-.76h-.6c-.54-.04-1.08-.04-1.62,0-.21-.01-.41-.01-.62,0-1.19,.09-2.36,.34-3.48,.76-.48,.21-.86,.59-1.05,1.07-.2,.48-.2,1.02,0,1.5,.07,.13,.15,.26,.24,.38,0,.07,0,.15,0,.22,.13,.23,.31,.43,.52,.59s.45,.28,.7,.34c.25,.07,.52,.08,.78,.05,.26-.04,.51-.12,.74-.26,.92-.51,1.95-.78,3-.78s2.08,.27,3,.78c.23,.13,.49,.22,.76,.25,.27,.03,.54,0,.79-.06,.26-.07,.5-.2,.7-.37,.21-.17,.38-.38,.5-.61h0Zm-7.74,6c0,.4,.12,.78,.34,1.11,.22,.33,.53,.59,.9,.74,.37,.15,.77,.19,1.15,.11,.39-.08,.74-.27,1.02-.55,.28-.28,.47-.64,.55-1.02,.08-.39,.04-.79-.11-1.15s-.41-.68-.74-.9c-.33-.22-.72-.34-1.11-.34-.53,0-1.04,.21-1.41,.59s-.59,.88-.59,1.41Z`,
  ],
}

export const Wifi = {
  prefix: 'fal',
  iconName: 'wifi',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24,30c-1.19,0-2.35,.35-3.33,1.01-.99,.66-1.76,1.6-2.21,2.69-.45,1.1-.57,2.3-.34,3.47,.23,1.16,.8,2.23,1.64,3.07,.84,.84,1.91,1.41,3.07,1.64,1.16,.23,2.37,.11,3.47-.34,1.1-.45,2.03-1.22,2.69-2.21,.66-.99,1.01-2.15,1.01-3.33,0-1.59-.63-3.12-1.76-4.24-1.12-1.12-2.65-1.76-4.24-1.76Zm0,8c-.4,0-.78-.12-1.11-.34-.33-.22-.58-.53-.74-.9-.15-.37-.19-.77-.11-1.16,.08-.39,.27-.74,.55-1.02,.28-.28,.64-.47,1.02-.55,.39-.08,.79-.04,1.16,.11,.37,.15,.68,.41,.9,.74,.22,.33,.34,.72,.34,1.11,0,.53-.21,1.04-.59,1.41-.37,.38-.88,.59-1.41,.59Zm0-15.99c-3.71,.01-7.26,1.49-9.9,4.1-.37,.37-.58,.88-.58,1.41s.21,1.04,.58,1.41c.37,.37,.88,.58,1.41,.58s1.03-.21,1.41-.58c.93-.93,2.03-1.67,3.25-2.17,1.21-.5,2.52-.76,3.83-.76s2.62,.26,3.83,.76c1.21,.5,2.32,1.24,3.25,2.17,.37,.38,.87,.59,1.4,.6,.41,.02,.81-.08,1.16-.29,.35-.21,.62-.52,.79-.9,.16-.37,.21-.78,.14-1.18-.08-.4-.27-.77-.56-1.05-1.32-1.31-2.88-2.34-4.59-3.04-1.72-.7-3.55-1.06-5.41-1.06Zm0-8c-5.83,.02-11.42,2.33-15.55,6.44-.19,.19-.33,.41-.44,.65-.1,.24-.15,.5-.15,.77,0,.53,.21,1.04,.59,1.42,.38,.38,.89,.59,1.42,.59s1.04-.21,1.42-.59c3.37-3.37,7.95-5.26,12.72-5.26s9.34,1.89,12.72,5.26c.19,.19,.41,.33,.65,.43s.5,.15,.77,.15c.26,0,.52-.05,.77-.15,.24-.1,.47-.25,.65-.43,.19-.19,.34-.41,.44-.65,.1-.24,.15-.5,.15-.77s-.05-.53-.15-.77c-.1-.24-.25-.46-.44-.65-4.14-4.11-9.72-6.42-15.55-6.44Zm21.21,.78c-2.78-2.79-6.09-5-9.73-6.51-3.64-1.51-7.54-2.28-11.48-2.28s-7.84,.78-11.48,2.28c-3.64,1.51-6.95,3.72-9.73,6.51-.38,.38-.59,.89-.59,1.42s.21,1.04,.59,1.42c.38,.38,.89,.59,1.42,.59s1.04-.21,1.42-.59c4.87-4.87,11.48-7.61,18.37-7.61s13.5,2.74,18.37,7.61c.19,.19,.41,.34,.65,.44,.24,.1,.51,.15,.77,.15s.53-.05,.77-.15c.24-.1,.46-.25,.65-.44,.19-.19,.34-.41,.44-.65,.1-.24,.15-.5,.15-.77s-.05-.53-.15-.77c-.1-.24-.25-.46-.44-.65Z`,
  ],
}

export const Instances = {
  prefix: 'fal',
  iconName: 'instances',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M6,4H30c.53,0,1.04,.21,1.41,.59,.38,.38,.59,.88,.59,1.41V14h4c.53,0,1.04,.21,1.41,.59,.38,.38,.59,.88,.59,1.41v8h4c.53,0,1.04,.21,1.41,.59s.59,.88,.59,1.41v16c0,.53-.21,1.04-.59,1.41s-.88,.59-1.41,.59H26c-.53,0-1.04-.21-1.41-.59s-.59-.88-.59-1.41v-4h-8c-.53,0-1.04-.21-1.41-.59-.38-.38-.59-.88-.59-1.41v-4H6c-.53,0-1.04-.21-1.41-.59-.38-.38-.59-.88-.59-1.41V6c0-.53,.21-1.04,.59-1.41,.38-.38,.88-.59,1.41-.59h0ZM28,40h12v-12h-12v12Zm-10-6h6v-8c0-.53,.21-1.04,.59-1.41s.88-.59,1.41-.59h8v-6h-16v16Zm-10-6h6v-12c0-.53,.21-1.04,.59-1.41,.38-.38,.88-.59,1.41-.59h12V8H8V28Z`,
  ],
}

export const CircleStop = {
  prefix: 'fal',
  iconName: 'circle-stop',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M30,16h-12c-.53,0-1.04,.21-1.41,.59s-.59,.88-.59,1.41v12c0,.53,.21,1.04,.59,1.41,.38,.37,.88,.59,1.41,.59h12c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41v-12c0-.53-.21-1.04-.59-1.41-.37-.38-.88-.59-1.41-.59Zm-2,12h-8v-8h8v8ZM24,4c-3.96,0-7.82,1.17-11.11,3.37-3.29,2.2-5.85,5.32-7.37,8.98-1.51,3.65-1.91,7.68-1.14,11.56,.77,3.88,2.68,7.44,5.47,10.24,2.8,2.8,6.36,4.7,10.24,5.47,3.88,.77,7.9,.38,11.56-1.14,3.65-1.51,6.78-4.08,8.98-7.37,2.2-3.29,3.37-7.16,3.37-11.11,0-2.63-.52-5.23-1.52-7.65-1.01-2.43-2.48-4.63-4.34-6.49-1.86-1.86-4.06-3.33-6.49-4.34-2.43-1.01-5.03-1.52-7.65-1.52h0Zm0,36c-3.16,0-6.26-.94-8.89-2.7-2.63-1.76-4.68-4.26-5.89-7.18-1.21-2.92-1.53-6.14-.91-9.24,.62-3.1,2.14-5.95,4.38-8.19,2.24-2.24,5.09-3.76,8.19-4.38,3.1-.62,6.32-.3,9.24,.91,2.92,1.21,5.42,3.26,7.18,5.89,1.76,2.63,2.7,5.72,2.7,8.89,0,4.24-1.69,8.31-4.69,11.31-3,3-7.07,4.69-11.31,4.69h0Z`,
  ],
}

export const BucketObject = {
  prefix: 'fal',
  iconName: 'bucket-object',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M18.73,29.05c-1.73,0-3.14,1.4-3.14,3.12s1.41,3.12,3.14,3.12,3.14-1.4,3.14-3.12-1.41-3.12-3.14-3.12Zm13.26,5.89l-3.15-5.81-3.15,5.81h6.29Zm-11.28-8.32h6.37v-6.32h-6.37v6.32Zm3.28-20.92c-3.35,0-19.99,.3-19.99,6.14,0,.38,.07,.73,.2,1.06l5.85,24.98c.42,4,9.84,4.44,13.94,4.44s13.53-.44,13.95-4.44l5.85-24.98c.13-.33,.2-.68,.2-1.06,0-5.84-16.64-6.14-19.99-6.14Zm10.29,31.33c-.93,.59-4.53,1.52-10.28,1.52s-9.34-.93-10.27-1.52l-4.89-20.87c5.19,1.69,12.99,1.82,15.16,1.82s9.98-.13,15.17-1.82l-4.89,20.87ZM24,14.19c-6.13,0-10.63-.69-13.35-1.43-.12-.03-.23-.06-.34-.1-.09-.03-.18-.05-.27-.08-.84-.26-1.47-.52-1.88-.75,1.7-.97,7.14-2.37,15.83-2.37s14.12,1.39,15.83,2.37c-1.7,.97-7.14,2.36-15.82,2.36Z`,
  ],
}

export const CloudDownload = {
  prefix: 'fal',
  iconName: 'cloud-download',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M22,8c-4.42,0-8,3.58-8,8,0,.06,0,.13,0,.2,.02,.93-.6,1.75-1.5,1.98-2.59,.67-4.5,3.02-4.5,5.81,0,3.31,2.69,6,6,6h2c1.1,0,2,.9,2,2s-.9,2-2,2h-2c-5.52,0-10-4.48-10-10,0-4.13,2.5-7.66,6.06-9.19,.6-6.07,5.72-10.81,11.94-10.81,4.45,0,8.33,2.42,10.4,6.01,6.44,.21,11.6,5.5,11.6,11.99s-5.37,12-12,12c-1.1,0-2-.9-2-2s.9-2,2-2c4.42,0,8-3.58,8-8s-3.58-8-8-8c-.22,0-.44,0-.66,.03-.85,.07-1.66-.41-2-1.2-1.23-2.84-4.06-4.83-7.35-4.83Zm2,12c1.1,0,2,.9,2,2v15.17l.59-.59c.78-.78,2.05-.78,2.83,0,.78,.78,.78,2.05,0,2.83l-4,4c-.38,.38-.88,.59-1.41,.59s-1.04-.21-1.41-.59l-4-4c-.78-.78-.78-2.05,0-2.83,.78-.78,2.05-.78,2.83,0l.59,.59v-15.17c0-1.1,.9-2,2-2Z`,
  ],
}

export const CloudUpload = {
  prefix: 'fal',
  iconName: 'cloud-upload',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M22,9c-4.42,0-8,3.58-8,8,0,.06,0,.13,0,.2,.02,.93-.6,1.75-1.5,1.98-2.59,.67-4.5,3.02-4.5,5.81,0,3.31,2.69,6,6,6h2c1.1,0,2,.9,2,2s-.9,2-2,2h-2c-5.52,0-10-4.48-10-10,0-4.13,2.5-7.66,6.06-9.19,.6-6.07,5.72-10.81,11.94-10.81,4.45,0,8.33,2.42,10.4,6.01,6.44,.21,11.6,5.5,11.6,11.99s-5.37,12-12,12c-1.1,0-2-.9-2-2s.9-2,2-2c4.42,0,8-3.58,8-8s-3.58-8-8-8c-.22,0-.44,0-.66,.03-.85,.07-1.66-.41-2-1.2-1.23-2.84-4.06-4.83-7.35-4.83Zm.59,10.59c.78-.78,2.05-.78,2.83,0l4,4c.78,.78,.78,2.05,0,2.83-.78,.78-2.05,.78-2.83,0l-.59-.59v15.17c0,1.1-.9,2-2,2s-2-.9-2-2v-15.17l-.59,.59c-.78,.78-2.05,.78-2.83,0-.78-.78-.78-2.05,0-2.83l4-4Z`,
  ],
}

export const Buffer = {
  prefix: 'fal',
  iconName: 'buffer',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M5,17.72l18,10.4c.3,.18,.65,.27,1,.27s.7-.09,1-.27l18-10.4c.3-.17,.55-.43,.73-.73s.27-.64,.27-.99c0-.35-.09-.7-.27-1-.18-.31-.43-.56-.73-.74L25,3.88c-.3-.18-.65-.27-1-.27s-.7,.09-1,.27L5,14.26c-.31,.18-.56,.43-.73,.74-.18,.31-.27,.65-.27,1,0,.35,.1,.69,.27,.99,.18,.3,.43,.55,.73,.73ZM24,8l14,8-14,8-14-8,14-8Zm17,14.34l-17,9.66L7,22.26c-.23-.13-.48-.22-.74-.25-.26-.03-.53-.02-.78,.05s-.49,.19-.7,.35c-.21,.16-.38,.36-.51,.59-.26,.46-.33,1-.19,1.51,.14,.51,.47,.94,.93,1.21l18,10.4c.3,.18,.65,.27,1,.27s.7-.09,1-.27l18-10.4c.46-.26,.79-.7,.93-1.21,.14-.51,.07-1.05-.19-1.51-.13-.23-.31-.43-.51-.59-.21-.16-.45-.28-.7-.35-.25-.07-.52-.09-.78-.05-.26,.03-.51,.12-.74,.25v.08Zm0,8l-17,9.66L7,30.26c-.23-.13-.48-.22-.74-.25-.26-.03-.53-.02-.78,.05s-.49,.19-.7,.35c-.21,.16-.38,.36-.51,.59-.26,.46-.33,1-.19,1.51,.14,.51,.47,.94,.93,1.21l18,10.4c.3,.18,.65,.27,1,.27s.7-.09,1-.27l18-10.4c.46-.26,.79-.7,.93-1.21,.14-.51,.07-1.05-.19-1.51-.13-.23-.31-.43-.51-.59-.21-.16-.45-.28-.7-.35-.25-.07-.52-.09-.78-.05-.26,.03-.51,.12-.74,.25v.08Z`,
  ],
}

export const InServiceInstances = {
  prefix: 'fal',
  iconName: 'in-service-instances',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M35.54,31.87l-2.37,2.37v-10.24c0-1.01-.82-1.83-1.83-1.83s-1.83,.82-1.83,1.83v10.24l-2.37-2.37c-.72-.72-1.88-.72-2.59,0-.72,.72-.72,1.88,0,2.59l5.5,5.5c.72,.72,1.88,.72,2.59,0l5.5-5.5c.72-.72,.72-1.88,0-2.59-.72-.72-1.88-.72-2.59,0Zm-4.2-15.2c-.62,0-1.23,.04-1.83,.11v-1.95c0-2.03-1.64-3.67-3.67-3.67h-5.5V5.67c0-2.03-1.64-3.67-3.67-3.67H5.67c-2.03,0-3.67,1.64-3.67,3.67v11c0,2.03,1.64,3.67,3.67,3.67h5.5v5.5c0,2.03,1.64,3.67,3.67,3.67h1.95c-.07,.6-.11,1.21-.11,1.83,0,8.1,6.57,14.67,14.67,14.67s14.67-6.57,14.67-14.67-6.57-14.67-14.67-14.67Zm-20.17-1.83v1.83H5.67V5.67h11v5.5h-1.83c-2.03,0-3.67,1.64-3.67,3.67Zm3.67,11V14.83h11v2.9c-3.68,1.49-6.61,4.42-8.1,8.1h-2.9Zm16.5,16.5c-3.32,0-6.3-1.47-8.32-3.8-1.67-1.93-2.68-4.45-2.68-7.2,0-6.08,4.92-11,11-11,2.75,0,5.27,1.01,7.2,2.68,2.33,2.02,3.8,4.99,3.8,8.32,0,6.08-4.92,11-11,11Z`,
  ],
}

export const DesiredCapacity = {
  prefix: 'fal',
  iconName: 'desired-capacity',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M13.62,34.37c-.28-.28-.65-.48-1.04-.56-.4-.08-.8-.04-1.18,.12-.37,.15-.69,.42-.91,.75-.22,.33-.34,.73-.34,1.13,0,.54,.22,1.06,.6,1.44,.38,.38,.9,.6,1.44,.6,.4,0,.8-.12,1.13-.34,.33-.22,.6-.54,.75-.91,.15-.37,.19-.78,.12-1.18-.08-.4-.27-.76-.56-1.04ZM31.33,11.17c-3.04,0-5.5,2.46-5.5,5.5s2.46,5.5,5.5,5.5,5.5-2.46,5.5-5.5-2.46-5.5-5.5-5.5Zm-2.85,22.61h-8.15c-.54,0-1.06,.21-1.44,.6-.38,.38-.6,.9-.6,1.44s.21,1.06,.6,1.44c.38,.38,.9,.6,1.44,.6h8.15c.54,0,1.06-.21,1.44-.6s.6-.9,.6-1.44-.21-1.06-.6-1.44c-.38-.38-.9-.6-1.44-.6ZM31.33,2c-5.43,0-10.17,2.95-12.7,7.33h-3.93c-1.14,0-2.25,.31-3.22,.91-.97,.6-1.75,1.45-2.25,2.47L2.43,26.33c-.28,.57-.43,1.19-.43,1.82v11.73c0,1.62,.64,3.18,1.79,4.32,1.15,1.15,2.7,1.79,4.32,1.8h24.45c1.62,0,3.17-.65,4.32-1.79,1.15-1.15,1.79-2.7,1.79-4.32v-10.52c4.38-2.54,7.33-7.28,7.33-12.7,0-8.1-6.57-14.67-14.67-14.67ZM12.88,14.53c.17-.34,.43-.63,.75-.82,.32-.2,.69-.3,1.07-.3h2.33c-.24,1.05-.36,2.14-.36,3.26,0,3.38,1.14,6.48,3.06,8.96H7.33l5.55-11.1Zm21.71,25.35c0,.54-.22,1.06-.6,1.44-.38,.38-.9,.6-1.44,.6H8.11c-.54,0-1.06-.22-1.44-.6-.38-.38-.6-.9-.6-1.44v-10.18H24.61c2.01,1.04,4.3,1.63,6.73,1.63,1.12,0,2.21-.13,3.26-.36v8.92Zm-3.26-12.22c-6.08,0-11-4.92-11-11s4.92-11,11-11,11,4.92,11,11-4.92,11-11,11Z`,
  ],
}

export const PendingInstances = {
  prefix: 'fal',
  iconName: 'pending-instances',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M31.33,16.67c-.62,0-1.23,.04-1.83,.11v-1.95c0-2.03-1.64-3.67-3.67-3.67h-5.5V5.67c0-2.03-1.64-3.67-3.67-3.67H5.67c-2.03,0-3.67,1.64-3.67,3.67v11c0,2.03,1.64,3.67,3.67,3.67h5.5v5.5c0,2.02,1.64,3.67,3.67,3.67h1.95c-.07,.6-.11,1.21-.11,1.83,0,8.1,6.57,14.67,14.67,14.67s14.67-6.57,14.67-14.67-6.57-14.67-14.67-14.67Zm-20.17-1.83v1.83H5.67V5.67h11v5.5h-1.83c-2.03,0-3.67,1.64-3.67,3.67Zm3.67,11V14.83h11v2.9c-3.68,1.49-6.61,4.42-8.1,8.1h-2.9Zm16.5,16.5c-3.32,0-6.3-1.47-8.32-3.8-1.67-1.93-2.68-4.45-2.68-7.2,0-6.08,4.92-11,11-11,2.75,0,5.27,1.01,7.2,2.68,2.33,2.02,3.8,4.99,3.8,8.32,0,6.08-4.92,11-11,11Zm0-20.17c-1.01,0-1.83,.82-1.83,1.83v9.17c0,1.01,.82,1.83,1.83,1.83s1.83-.82,1.83-1.83v-9.17c0-1.01-.82-1.83-1.83-1.83Zm0,14.67c-1.01,0-1.83,.82-1.83,1.83s.82,1.83,1.83,1.83,1.83-.82,1.83-1.83-.82-1.83-1.83-1.83Z`,
  ],
}

export const StoppedInstances = {
  prefix: 'fal',
  iconName: 'stopped-instances',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M31.33,16.67c-.62,0-1.23,.04-1.83,.11v-1.95c0-2.03-1.64-3.67-3.67-3.67h-5.5V5.67c0-2.03-1.64-3.67-3.67-3.67H5.67c-2.03,0-3.67,1.64-3.67,3.67v11c0,2.03,1.64,3.67,3.67,3.67h5.5v5.5c0,2.02,1.64,3.67,3.67,3.67h1.95c-.07,.6-.11,1.21-.11,1.83,0,8.1,6.57,14.67,14.67,14.67s14.67-6.57,14.67-14.67-6.57-14.67-14.67-14.67Zm-20.17-1.83v1.83H5.67V5.67h11v5.5h-1.83c-2.03,0-3.67,1.64-3.67,3.67Zm3.67,11V14.83h11v2.9c-3.68,1.49-6.61,4.42-8.1,8.1h-2.9Zm16.5,16.5c-3.32,0-6.3-1.47-8.32-3.8-1.67-1.93-2.68-4.45-2.68-7.2,0-6.08,4.92-11,11-11,2.75,0,5.27,1.01,7.2,2.68,2.33,2.02,3.8,4.99,3.8,8.32,0,6.08-4.92,11-11,11Zm6.8-17.8c-.72-.72-1.88-.72-2.59,0l-4.2,4.2-4.2-4.2c-.72-.72-1.88-.72-2.59,0s-.72,1.88,0,2.59l4.2,4.2-4.2,4.2c-.72,.72-.72,1.88,0,2.59,.72,.72,1.88,.72,2.59,0l4.2-4.2,4.2,4.2c.72,.72,1.88,.72,2.59,0,.72-.72,.72-1.88,0-2.59l-4.2-4.2,4.2-4.2c.72-.72,.72-1.88,0-2.59Z`,
  ],
}

export const UserError = {
  prefix: 'fal',
  iconName: 'user-error',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24,28H10c-2.67,0-5.18,1.04-7.07,2.93C1.04,32.82,0,35.33,0,38v4c0,1.11,.9,2,2,2s2-.89,2-2v-4c0-1.58,.64-3.13,1.76-4.24,1.13-1.13,2.64-1.76,4.24-1.76h14c1.6,0,3.11,.63,4.24,1.76,1.13,1.13,1.76,2.64,1.76,4.24v4c0,1.11,.89,2,2,2s2-.89,2-2v-4c0-2.67-1.04-5.18-2.93-7.07-1.89-1.89-4.4-2.93-7.07-2.93Zm19.83-7l3.59-3.59c.78-.78,.78-2.05,0-2.83s-2.05-.78-2.83,0l-3.59,3.59-3.59-3.59c-.78-.78-2.05-.78-2.83,0s-.78,2.05,0,2.83l3.59,3.59-3.59,3.59c-.78,.78-.78,2.05,0,2.83,.39,.39,.9,.58,1.41,.58s1.02-.19,1.41-.58l3.59-3.59,3.59,3.59c.39,.39,.9,.58,1.41,.58s1.02-.19,1.41-.58c.78-.78,.78-2.05,0-2.83l-3.59-3.59Zm-26.83,3c5.51,0,10-4.49,10-10s-4.49-10-10-10S7,8.49,7,14s4.49,10,10,10Zm0-16c3.31,0,6,2.69,6,6s-2.69,6-6,6-6-2.69-6-6,2.69-6,6-6Z`,
  ],
}

export const EmptyMessage = {
  prefix: 'fal',
  iconName: 'empty-message',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M34.68,24.73c2.32,.02,4.57-.77,6.37-2.22,1.8-1.46,3.04-3.49,3.51-5.76,.47-2.27,.13-4.63-.95-6.68-1.08-2.05-2.84-3.66-4.97-4.56-2.14-.9-4.52-1.02-6.74-.36-2.22,.66-4.14,2.08-5.43,4-1.29,1.92-1.88,4.23-1.66,6.54,.22,2.31,1.24,4.46,2.87,6.1,1.86,1.86,4.37,2.92,7,2.94Zm4.24-5.76c-.88,.87-2,1.45-3.22,1.65-1.22,.21-2.47,.03-3.58-.49l8-8c.53,1.12,.71,2.39,.49,3.61-.21,1.23-.81,2.35-1.69,3.23Zm-8.48-8.48c1.12-1.13,2.65-1.76,4.24-1.76,.89,0,1.76,.21,2.56,.6l-8,8c-.53-1.12-.71-2.39-.49-3.61,.21-1.23,.81-2.35,1.69-3.23Zm10.24,16.24c-.53,0-1.04,.21-1.41,.59-.37,.37-.59,.88-.59,1.41v8c0,.53-.21,1.04-.59,1.41s-.88,.59-1.41,.59H8.68c-.53,0-1.04-.21-1.41-.59-.38-.37-.59-.88-.59-1.41V17.55l11.76,11.76c1.12,1.12,2.64,1.75,4.22,1.76,.81,0,1.6-.16,2.34-.47,.74-.31,1.41-.77,1.97-1.35,.36-.37,.56-.87,.56-1.39s-.2-1.02-.56-1.39c-.19-.19-.41-.34-.65-.45-.25-.1-.51-.16-.78-.16s-.53,.05-.78,.16c-.25,.1-.47,.25-.65,.45-.37,.37-.88,.57-1.4,.57s-1.03-.21-1.4-.57L9.5,14.73h9.18c.53,0,1.04-.21,1.41-.59,.37-.38,.59-.88,.59-1.41s-.21-1.04-.59-1.41c-.37-.38-.88-.59-1.41-.59H8.68c-1.59,0-3.12,.63-4.24,1.76-1.13,1.13-1.76,2.65-1.76,4.24v20c0,1.59,.63,3.12,1.76,4.24,1.13,1.13,2.65,1.76,4.24,1.76h28c1.59,0,3.12-.63,4.24-1.76,1.13-1.13,1.76-2.65,1.76-4.24v-8c0-.53-.21-1.04-.59-1.41-.37-.38-.88-.59-1.41-.59Z`,
  ],
}

export const DeleteMessage = {
  prefix: 'fal',
  iconName: 'delete-message',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M41.79,21.67c-.53,0-1.04,.21-1.41,.59-.37,.37-.59,.88-.59,1.41v12c0,.53-.21,1.04-.59,1.41s-.88,.59-1.41,.59H9.79c-.53,0-1.04-.21-1.41-.59-.38-.37-.59-.88-.59-1.41V16.49l11.76,11.78c1.12,1.11,2.64,1.74,4.22,1.74,1.62,0,3.17-.66,4.32-1.8l3.44-3.44c.38-.38,.59-.89,.59-1.42s-.21-1.04-.59-1.42c-.38-.38-.89-.59-1.42-.59s-1.04,.21-1.42,.59l-3.5,3.5c-.37,.37-.88,.57-1.4,.57s-1.03-.21-1.4-.57L10.61,13.67h15.18c.53,0,1.04-.21,1.41-.59,.37-.38,.59-.88,.59-1.41s-.21-1.04-.59-1.41-.88-.59-1.41-.59H9.79c-1.59,0-3.12,.63-4.24,1.76-1.13,1.13-1.76,2.65-1.76,4.24v20c0,1.59,.63,3.12,1.76,4.24s2.65,1.76,4.24,1.76h28c1.59,0,3.12-.63,4.24-1.76,1.13-1.13,1.76-2.65,1.76-4.24v-12c0-.53-.21-1.04-.59-1.41-.37-.38-.88-.59-1.41-.59Zm-1.18-10l2.6-2.58c.38-.38,.59-.89,.59-1.42s-.21-1.04-.59-1.42c-.38-.38-.89-.59-1.42-.59s-1.04,.21-1.42,.59l-2.58,2.6-2.58-2.6c-.19-.19-.41-.33-.65-.44-.24-.1-.5-.15-.77-.15-.53,0-1.04,.21-1.42,.59-.38,.38-.59,.89-.59,1.42s.21,1.04,.59,1.42l2.6,2.58-2.6,2.58c-.19,.19-.34,.41-.44,.65s-.15,.51-.15,.77,.05,.53,.15,.77c.1,.24,.25,.46,.44,.65,.19,.19,.41,.34,.65,.44,.24,.1,.51,.15,.77,.15s.53-.05,.77-.15c.24-.1,.46-.25,.65-.44l2.58-2.6,2.58,2.6c.19,.19,.41,.34,.65,.44,.24,.1,.51,.15,.77,.15s.53-.05,.77-.15c.24-.1,.46-.25,.65-.44,.19-.19,.34-.41,.44-.65,.1-.24,.15-.51,.15-.77s-.05-.53-.15-.77-.25-.46-.44-.65l-2.6-2.58Z`,
  ],
}

export const DelayedMessage = {
  prefix: 'fal',
  iconName: 'delayed-message',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M42,27.01c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41v8c0,.53-.21,1.04-.59,1.41-.38,.38-.88,.59-1.41,.59H10c-.53,0-1.04-.21-1.41-.59-.38-.38-.59-.88-.59-1.41V17.83l11.76,11.76c1.13,1.12,2.65,1.75,4.24,1.75s3.12-.63,4.24-1.75l7.18-7.16c.38-.38,.59-.89,.59-1.42s-.21-1.04-.59-1.42c-.38-.38-.89-.59-1.42-.59s-1.04,.21-1.42,.59l-7.16,7.18c-.19,.19-.41,.34-.65,.44-.24,.1-.51,.15-.77,.15s-.53-.05-.77-.15c-.24-.1-.46-.25-.65-.44L10.82,15.01h23.18c.53,0,1.04-.21,1.41-.59,.38-.38,.59-.88,.59-1.41s-.21-1.04-.59-1.41c-.38-.38-.88-.59-1.41-.59H10c-1.59,0-3.12,.63-4.24,1.76-1.13,1.13-1.76,2.65-1.76,4.24v20c0,1.59,.63,3.12,1.76,4.24,1.13,1.13,2.65,1.76,4.24,1.76h28c1.59,0,3.12-.63,4.24-1.76,1.13-1.13,1.76-2.65,1.76-4.24v-8c0-.53-.21-1.04-.59-1.41-.38-.38-.88-.59-1.41-.59Zm0-18c.53,0,1.04-.21,1.42-.58l.24-.32c.08-.1,.14-.22,.18-.34,.07-.11,.13-.23,.16-.36,0-.13,0-.27,0-.4,0-.53-.21-1.04-.58-1.42-.19-.19-.41-.34-.65-.44-.24-.1-.51-.15-.77-.15s-.53,.05-.77,.15c-.24,.1-.46,.25-.65,.44-.19,.19-.34,.41-.44,.65s-.15,.51-.15,.77,.05,.53,.15,.77c.1,.24,.25,.46,.44,.65,.38,.37,.89,.57,1.42,.58Zm0,2c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41v8c0,.53,.21,1.04,.59,1.41,.38,.38,.88,.59,1.41,.59s1.04-.21,1.41-.59c.38-.38,.59-.88,.59-1.41v-8c0-.53-.21-1.04-.59-1.41-.38-.38-.88-.59-1.41-.59Z`,
  ],
}

export const MessageSize = {
  prefix: 'fal',
  iconName: 'message-size',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M38,38c0,.53-.21,1.04-.59,1.41s-.88,.59-1.41,.59H8c-.53,0-1.04-.21-1.41-.59-.38-.38-.59-.88-.59-1.41V18.82l11.76,11.76c1.12,1.12,2.65,1.75,4.24,1.75s3.11-.63,4.24-1.75l.38-.38c-1.21-.63-2.33-1.41-3.33-2.32-.16,.13-.33,.24-.52,.31-.24,.1-.51,.15-.77,.15s-.53-.05-.77-.15c-.24-.1-.46-.25-.65-.44l-11.76-11.76h9.18c0-1.38,.18-2.72,.5-4H8c-1.59,0-3.12,.63-4.24,1.76s-1.76,2.65-1.76,4.24v20c0,1.59,.63,3.12,1.76,4.24,1.13,1.13,2.65,1.76,4.24,1.76h28c1.59,0,3.12-.63,4.24-1.76,1.13-1.13,1.76-2.65,1.76-4.24v-8.14c-1.24,.72-2.58,1.27-4,1.64v6.5ZM29,14c0,1.1,.89,2,2,2h3v3c0,1.1,.89,2,2,2s2-.9,2-2v-7h-7c-1.11,0-2,.9-2,2Zm5-10c-6.62,0-12,5.38-12,12s5.38,12,12,12,12-5.38,12-12-5.38-12-12-12Zm0,20c-4.41,0-8-3.59-8-8s3.59-8,8-8,8,3.59,8,8-3.59,8-8,8Z`,
  ],
}

export const DeliveredNotifications = {
  prefix: 'fal',
  iconName: 'delivered-notifications',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M37.09,10.99c-.74-.75-1.95-.75-2.69,0l-5.89,5.66-1.51-1.52c-.74-.75-1.95-.75-2.69,0s-.74,1.96,0,2.71l2.86,2.87c.74,.75,1.95,.75,2.69,0l7.24-7.01c.74-.75,.74-1.96,0-2.71Zm6.91,4.4c0-7.4-5.97-13.39-13.33-13.39-4.7,0-8.83,2.44-11.21,6.13-.83,.41-1.5,1.12-1.85,2-5.65,1.55-9.8,6.75-9.8,12.92v8.99l-3.49,5.26c-.39,.59-.43,1.34-.09,1.96,.33,.62,.98,1.01,1.68,1.01H14.54c.46,3.24,3.24,5.74,6.6,5.74s6.14-2.49,6.6-5.74h8.64c.7,0,1.35-.39,1.68-1.01,.33-.62,.29-1.38-.09-1.96l-3.49-5.26v-3.8c5.51-1.65,9.52-6.77,9.52-12.84Zm-22.86,26.78c-1.24,0-2.3-.8-2.69-1.91h5.39c-.39,1.11-1.45,1.91-2.69,1.91Zm-11.68-5.74l1.84-2.77c.21-.31,.32-.68,.32-1.06v-9.57c0-3.94,2.37-7.32,5.76-8.79-.03,.38-.05,.75-.05,1.14,0,7.4,5.97,13.39,13.33,13.39v3.83c0,.38,.11,.75,.32,1.06l1.84,2.77H9.46Zm21.2-11.48c-5.26,0-9.52-4.28-9.52-9.57s4.26-9.57,9.52-9.57,9.52,4.28,9.52,9.57-4.26,9.57-9.52,9.57Z`,
  ],
}

export const FailedNotifications = {
  prefix: 'fal',
  iconName: 'failed-notifications',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M35.82,10.21c-.74-.75-1.95-.75-2.69,0l-2.46,2.47-2.46-2.47c-.74-.75-1.95-.75-2.69,0-.74,.75-.74,1.96,0,2.71l2.46,2.47-2.46,2.47c-.74,.75-.74,1.96,0,2.71,.74,.75,1.95,.75,2.69,0l2.46-2.47,2.46,2.47c.74,.75,1.95,.75,2.69,0,.74-.75,.74-1.96,0-2.71l-2.46-2.47,2.46-2.47c.74-.75,.74-1.96,0-2.71Zm8.18,5.18c0-7.4-5.97-13.39-13.33-13.39-4.7,0-8.83,2.44-11.21,6.13-.83,.41-1.5,1.12-1.85,2-5.65,1.55-9.8,6.75-9.8,12.92v8.99l-3.49,5.26c-.39,.59-.43,1.34-.09,1.96,.33,.62,.98,1.01,1.68,1.01H14.54c.46,3.24,3.24,5.74,6.6,5.74s6.14-2.49,6.6-5.74h8.64c.7,0,1.35-.39,1.68-1.01,.33-.62,.29-1.38-.09-1.96l-3.49-5.26v-3.8c5.51-1.65,9.52-6.77,9.52-12.84Zm-22.86,26.78c-1.24,0-2.3-.8-2.69-1.91h5.39c-.39,1.11-1.45,1.91-2.69,1.91Zm-11.68-5.74l1.84-2.76c.21-.31,.32-.68,.32-1.06v-9.57c0-3.94,2.37-7.32,5.76-8.79-.03,.38-.05,.75-.05,1.14,0,7.4,5.97,13.39,13.33,13.39v3.83c0,.38,.11,.75,.32,1.06l1.84,2.76H9.46Zm21.2-11.48c-5.26,0-9.52-4.28-9.52-9.57s4.26-9.57,9.52-9.57,9.52,4.28,9.52,9.57-4.26,9.57-9.52,9.57Z`,
  ],
}

export const SuccessMessage = {
  prefix: 'fal',
  iconName: 'success-message',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M32.37,17.09c.19,.19,.41,.34,.65,.44,.24,.1,.51,.15,.77,.15s.53-.05,.77-.15c.24-.1,.46-.25,.65-.44l8-8c.38-.38,.59-.89,.59-1.42s-.21-1.04-.59-1.42c-.38-.38-.89-.59-1.42-.59s-1.04,.21-1.42,.59l-6.58,6.6-2.58-2.6c-.19-.19-.41-.33-.65-.44-.24-.1-.5-.15-.77-.15-.53,0-1.04,.21-1.42,.59-.19,.19-.33,.41-.44,.65-.1,.24-.15,.5-.15,.77,0,.53,.21,1.04,.59,1.42l4,4Zm9.42-1.42c-.53,0-1.04,.21-1.41,.59-.37,.38-.59,.88-.59,1.41v18c0,.53-.21,1.04-.59,1.41-.37,.38-.88,.59-1.41,.59H9.79c-.53,0-1.04-.21-1.41-.59-.37-.37-.59-.88-.59-1.41V16.49l11.76,11.78c1.12,1.11,2.64,1.74,4.22,1.74,1.62,0,3.17-.65,4.32-1.8l3.44-3.44c.38-.38,.59-.89,.59-1.42s-.21-1.04-.59-1.42c-.38-.38-.89-.59-1.42-.59s-1.04,.21-1.42,.59l-3.5,3.5c-.37,.37-.88,.57-1.4,.57s-1.03-.21-1.4-.57L10.61,13.67h11.18c.53,0,1.04-.21,1.41-.59,.37-.38,.59-.88,.59-1.41s-.21-1.04-.59-1.41c-.37-.38-.88-.59-1.41-.59H9.79c-1.59,0-3.12,.63-4.24,1.76s-1.76,2.65-1.76,4.24v20c0,1.59,.63,3.12,1.76,4.24,1.12,1.12,2.65,1.76,4.24,1.76h27.99c1.59,0,3.12-.63,4.24-1.76s1.76-2.65,1.76-4.24V17.67c0-.53-.21-1.04-.59-1.41-.37-.38-.88-.59-1.41-.59Z`,
  ],
}

export const SuccessRate = {
  prefix: 'fal',
  iconName: 'success-rate',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M43.84,13.24c-.2-.49-.59-.88-1.08-1.08-.24-.1-.5-.16-.76-.16h-10c-.53,0-1.04,.21-1.41,.59-.37,.37-.59,.88-.59,1.41s.21,1.04,.59,1.41c.37,.37,.88,.59,1.41,.59h5.18l-11.18,11.18-6.58-6.6c-.19-.19-.41-.34-.65-.44-.24-.1-.5-.15-.77-.15s-.53,.05-.77,.15c-.24,.1-.46,.25-.65,.44l-12,12c-.19,.19-.34,.41-.44,.65-.1,.24-.15,.51-.15,.77s.05,.53,.15,.77c.1,.24,.25,.46,.44,.65,.19,.19,.41,.34,.65,.44,.24,.1,.5,.15,.77,.15s.53-.05,.77-.15c.24-.1,.46-.25,.65-.44l10.58-10.6,6.58,6.6c.19,.19,.41,.34,.65,.44,.24,.1,.51,.15,.77,.15s.53-.05,.77-.15c.24-.1,.46-.25,.65-.44l12.58-12.6v5.18c0,.53,.21,1.04,.59,1.41s.88,.59,1.41,.59,1.04-.21,1.41-.59,.59-.88,.59-1.41V14c0-.26-.06-.52-.16-.76Z`,
  ],
}

export const DollarSign = {
  prefix: 'fal',
  iconName: 'dollar-sign',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M28,22h-8c-1.06,0-2.08-.42-2.83-1.17-.75-.75-1.17-1.77-1.17-2.83s.42-2.08,1.17-2.83c.75-.75,1.77-1.17,2.83-1.17h10c.53,0,1.04,.21,1.41,.59,.38,.38,.59,.88,.59,1.41s.21,1.04,.59,1.41c.38,.38,.88,.59,1.41,.59s1.04-.21,1.41-.59c.38-.38,.59-.88,.59-1.41,0-1.59-.63-3.12-1.76-4.24-1.13-1.13-2.65-1.76-4.24-1.76h-4V6c0-.53-.21-1.04-.59-1.41-.38-.38-.88-.59-1.41-.59s-1.04,.21-1.41,.59c-.38,.38-.59,.88-.59,1.41v4h-2c-2.12,0-4.16,.84-5.66,2.34-1.5,1.5-2.34,3.54-2.34,5.66s.84,4.16,2.34,5.66c1.5,1.5,3.54,2.34,5.66,2.34h8c1.06,0,2.08,.42,2.83,1.17s1.17,1.77,1.17,2.83-.42,2.08-1.17,2.83c-.75,.75-1.77,1.17-2.83,1.17h-10c-.53,0-1.04-.21-1.41-.59-.38-.38-.59-.88-.59-1.41s-.21-1.04-.59-1.41c-.38-.38-.88-.59-1.41-.59s-1.04,.21-1.41,.59c-.38,.38-.59,.88-.59,1.41,0,1.59,.63,3.12,1.76,4.24,1.13,1.13,2.65,1.76,4.24,1.76h4v4c0,.53,.21,1.04,.59,1.41s.88,.59,1.41,.59,1.04-.21,1.41-.59,.59-.88,.59-1.41v-4h2c2.12,0,4.16-.84,5.66-2.34,1.5-1.5,2.34-3.53,2.34-5.66s-.84-4.16-2.34-5.66c-1.5-1.5-3.53-2.34-5.66-2.34Z`,
  ],
}

export const Health = {
  prefix: 'fal',
  iconName: 'health',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M42,22h-7.88c-.14-.02-.28-.02-.42,0h-.34c-.1,.06-.2,.13-.3,.2-.11,.07-.22,.15-.32,.24-.07,.08-.13,.17-.18,.26-.09,.13-.17,.26-.24,.4h0l-3.2,8.82L20.78,9.32c-.14-.39-.4-.72-.73-.96-.34-.24-.74-.36-1.15-.36s-.81,.13-1.15,.36c-.34,.24-.59,.57-.73,.96l-4.62,12.68H6c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41s.21,1.04,.59,1.41c.38,.38,.88,.59,1.41,.59H14.6c.11-.05,.22-.12,.32-.2,.11-.07,.22-.15,.32-.24l.18-.26c.1-.12,.18-.26,.24-.4l3.24-9.06,8.32,22.84c.14,.39,.4,.72,.73,.96,.34,.24,.74,.36,1.15,.36,.41,0,.81-.13,1.15-.36,.34-.24,.59-.57,.73-.96l4.6-12.68h6.42c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41-.21-1.04-.59-1.41-.88-.59-1.41-.59Z`,
  ],
}

export const ActiveFlow = {
  prefix: 'fal',
  iconName: 'active-flow',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M34,32h-9.98c.51,0,1-.2,1.39-.59l6-6c.78-.78,.78-2.05,0-2.83-.78-.78-2.05-.78-2.83,0l-2.59,2.59v-9.17h8c2.21,0,4-1.79,4-4V6c0-2.21-1.79-4-4-4H14c-2.21,0-4,1.79-4,4v6c0,2.21,1.79,4,4,4h8v9.17l-2.59-2.59c-.78-.78-2.05-.78-2.83,0-.78,.78-.78,2.05,0,2.83l6,6c.39,.39,.88,.58,1.39,.59H14c-2.21,0-4,1.79-4,4v6c0,2.21,1.79,4,4,4h20c2.21,0,4-1.79,4-4v-6c0-2.21-1.79-4-4-4ZM14,12V6h20v6H14Zm20,30H14v-6h20v6Z`,
  ],
}

export const ConsumedLcus = {
  prefix: 'fal',
  iconName: 'consumed-lcus',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M22.58,34.59l-2.58,2.58V13.99c0-.53-.21-1.04-.59-1.41-.38-.38-.88-.59-1.41-.59s-1.04,.21-1.41,.59c-.38,.38-.59,.88-.59,1.41v23.2l-2.58-2.6c-.19-.19-.41-.34-.65-.44s-.51-.15-.77-.15-.53,.05-.77,.15-.46,.25-.65,.44c-.37,.37-.58,.88-.58,1.41s.21,1.04,.58,1.41l6,6c.19,.19,.41,.34,.65,.44,.24,.1,.51,.15,.77,.15s.53-.05,.77-.15c.24-.1,.46-.25,.65-.44l6-6c.37-.37,.58-.88,.58-1.41s-.21-1.04-.58-1.41c-.19-.19-.41-.34-.65-.44-.24-.1-.51-.15-.77-.15s-.53,.05-.77,.15c-.24,.1-.46,.25-.65,.44ZM44,5.99c0-.53-.21-1.04-.59-1.41-.38-.38-.88-.59-1.41-.59H6c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41v12c0,.53,.21,1.04,.59,1.41,.38,.38,.88,.59,1.41,.59h4c.53,0,1.04-.21,1.41-.59,.38-.38,.59-.88,.59-1.41s-.21-1.04-.59-1.41c-.38-.38-.88-.59-1.41-.59h-2V7.99H40V15.99h-14c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41s.21,1.04,.59,1.41c.38,.38,.88,.59,1.41,.59h2v10c0,.53,.21,1.04,.59,1.41s.88,.59,1.41,.59h12c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41V5.99Zm-4,22h-8v-8h8v8Z`,
  ],
}

export const Sitemap = {
  prefix: 'fal',
  iconName: 'sitemap',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M44,30h-4v-6c0-.53-.21-1.04-.59-1.41s-.88-.59-1.41-.59h-12v-4h4c.53,0,1.04-.21,1.41-.59,.38-.38,.59-.88,.59-1.41V4c0-.53-.21-1.04-.59-1.41-.38-.38-.88-.59-1.41-.59h-12c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41v12c0,.53,.21,1.04,.59,1.41,.38,.38,.88,.59,1.41,.59h4v4H10c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41v6H4c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41v12c0,.53,.21,1.04,.59,1.41,.38,.38,.88,.59,1.41,.59H16c.53,0,1.04-.21,1.41-.59,.38-.38,.59-.88,.59-1.41v-12c0-.53-.21-1.04-.59-1.41-.38-.38-.88-.59-1.41-.59h-4v-4h24v4h-4c-.53,0-1.04,.21-1.41,.59s-.59,.88-.59,1.41v12c0,.53,.21,1.04,.59,1.41s.88,.59,1.41,.59h12c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41v-12c0-.53-.21-1.04-.59-1.41s-.88-.59-1.41-.59Zm-30,4v8H6v-8H14Zm6-20V6h8V14h-8Zm22,28h-8v-8h8v8Z`,
  ],
}

export const Host = {
  prefix: 'fal',
  iconName: 'host',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M42,39.98h-6c-.53,0-1.04-.21-1.41-.59-.37-.37-.59-.88-.59-1.41v-6c2.39-.02,4.69-.9,6.48-2.47,1.8-1.57,2.97-3.73,3.32-6.09,.34-2.36-.17-4.77-1.45-6.78-1.28-2.02-3.23-3.51-5.51-4.21-1.22-2.81-3.33-5.14-6-6.63-2.67-1.49-5.76-2.07-8.79-1.64-3.03,.43-5.84,1.84-7.99,4.02-2.15,2.18-3.53,5-3.93,8.03-1.91,.46-3.58,1.6-4.7,3.21-1.12,1.61-1.61,3.57-1.38,5.52,.23,1.95,1.17,3.74,2.64,5.04,1.47,1.3,3.36,2.02,5.32,2.01h2v6c0,.53-.21,1.04-.59,1.41s-.88,.59-1.41,.59H6.02c-.53,0-1.04,.21-1.41,.59-.37,.37-.59,.88-.59,1.41s.21,1.04,.59,1.41c.37,.37,.88,.59,1.41,.59h6c1.59,0,3.12-.63,4.24-1.76,1.12-1.12,1.76-2.65,1.76-4.24v-6h4v10c0,.53,.21,1.04,.59,1.41,.38,.37,.88,.59,1.41,.59s1.04-.21,1.41-.59c.38-.37,.59-.88,.59-1.41v-10h4v6c0,1.59,.63,3.12,1.76,4.24,1.12,1.12,2.65,1.76,4.24,1.76h6c.53,0,1.04-.21,1.41-.59,.38-.37,.59-.88,.59-1.41s-.21-1.04-.59-1.41c-.37-.37-.88-.59-1.41-.59ZM12.01,27.99c-1.06,0-2.08-.42-2.83-1.17s-1.17-1.77-1.17-2.83,.42-2.08,1.17-2.83,1.77-1.17,2.83-1.17c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41c0-2.36,.85-4.65,2.38-6.45,1.53-1.8,3.65-3,5.98-3.39,2.33-.39,4.73,.07,6.76,1.28,2.03,1.21,3.56,3.1,4.33,5.34,.11,.34,.32,.65,.59,.89,.27,.24,.61,.39,.96,.45,1.39,.24,2.65,.96,3.56,2.03,.91,1.07,1.41,2.44,1.42,3.84,0,1.59-.63,3.12-1.76,4.24-1.12,1.12-2.65,1.76-4.24,1.76H12.01Z`,
  ],
}

export const Availability = {
  prefix: 'fal',
  iconName: 'availability',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M27.9,6.56c.71,0,1.28-.57,1.28-1.28s-.57-1.28-1.28-1.28-1.28,.57-1.28,1.28,.57,1.28,1.28,1.28Zm5.19,29.48c-.95-.11-8.47-1.08-8.47-1.08-.6-.03-.63,.38-.38,1.04,.09,.22,.4,.97,.79,1.91-.23,.01-.48,.02-.74,.02-6.43,0-15.77-5.59-15.77-16.86,0-8.04,5.71-13.66,10.31-15.61,.9-.36,.66-1.26,.06-1.09-6.52,1.4-14.55,7.68-14.55,18.37s8.95,19.01,19.45,19.01c1.07,0,2.04-.2,2.72-.32,.32,.75,.59,1.37,.72,1.67,.5,1.13,.79,1.06,1.26,.48s4.49-5.88,4.8-6.28,.77-1.15-.22-1.26Zm6.84-17.18c1.43,0,2.59-1.16,2.59-2.59s-1.16-2.59-2.59-2.59-2.59,1.16-2.59,2.59,1.16,2.59,2.59,2.59Zm-4.63-7.55c1.1,0,2-.89,2-2s-.89-2-2-2-2,.89-2,2,.89,2,2,2Zm-4.32,3.29c-1.86,1.31-5.28,4.09-8.87,8.9-1.98-2.45-4.12-4.12-4.24-4.21-.83-.64-2.02-.49-2.66,.34-.64,.83-.49,2.02,.34,2.66,.03,.02,3.12,2.44,4.93,5.47,.33,.56,.93,.91,1.58,.93h.05c.63,0,1.23-.32,1.58-.85,3.78-5.69,7.56-8.79,9.47-10.14,.86-.6,1.07-1.79,.46-2.65-.6-.86-1.79-1.07-2.65-.46Zm9.85,7.23c-1.56,0-2.83,1.27-2.83,2.83s1.27,2.83,2.83,2.83,2.83-1.27,2.83-2.83-1.27-2.83-2.83-2.83Zm-3.12,7.78c-1.56,0-2.83,1.27-2.83,2.83s1.27,2.83,2.83,2.83,2.83-1.27,2.83-2.83-1.27-2.83-2.83-2.83Z`,
  ],
}

export const FolderNetwork = {
  prefix: 'fal',
  iconName: 'folder-network',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M42,36.02h-12.36c-.3-.84-.78-1.6-1.41-2.23-.63-.63-1.39-1.11-2.23-1.41v-4.36h7.34c1.23,0,2.42-.5,3.29-1.37,.87-.87,1.37-2.05,1.37-3.29V12.68c0-1.23-.5-2.42-1.37-3.29-.87-.87-2.05-1.37-3.29-1.37h-8l-.26-.82c-.31-.91-.89-1.71-1.67-2.28-.78-.57-1.71-.88-2.67-.9h-6c-.62,0-1.23,.11-1.8,.34-.57,.23-1.1,.57-1.54,1.01-.44,.43-.79,.95-1.03,1.52s-.37,1.18-.37,1.8v14.68c0,1.23,.5,2.42,1.37,3.29,.87,.87,2.05,1.37,3.29,1.37h7.34v4.36c-.84,.3-1.6,.78-2.23,1.41-.63,.63-1.11,1.39-1.41,2.23H6c-.53,0-1.04,.21-1.41,.59-.38,.37-.59,.88-.59,1.41s.21,1.04,.59,1.41c.38,.37,.88,.59,1.41,.59h12.36c.42,1.16,1.19,2.16,2.2,2.87,1.01,.71,2.21,1.09,3.44,1.09s2.43-.38,3.44-1.09c1.01-.71,1.78-1.71,2.2-2.87h12.36c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41-.21-1.04-.59-1.41c-.37-.38-.88-.59-1.41-.59ZM14.66,24.02c-.18,0-.34-.07-.47-.19-.12-.12-.19-.29-.19-.47V8.68c0-.18,.07-.34,.19-.47,.12-.12,.29-.19,.47-.19h6c.14,0,.28,.04,.4,.12,.12,.08,.2,.2,.24,.34l.72,2.18c.14,.41,.41,.77,.76,1.01,.36,.25,.78,.37,1.22,.35h9.34c.17,0,.34,.07,.47,.19,.12,.12,.19,.29,.19,.47v10.68c0,.17-.07,.34-.19,.47-.12,.12-.29,.19-.47,.19H14.66Zm9.34,16c-.4,0-.78-.12-1.11-.34-.33-.22-.59-.53-.74-.9-.15-.37-.19-.77-.11-1.16s.27-.74,.55-1.02c.28-.28,.64-.47,1.02-.55,.39-.08,.79-.04,1.16,.11,.37,.15,.68,.41,.9,.74,.22,.33,.34,.72,.34,1.11,0,.53-.21,1.04-.59,1.41s-.88,.59-1.41,.59Z`,
  ],
}

export const ViewMore = {
  prefix: 'fal',
  iconName: 'view-more',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24.68,13c0-1.09,.89-1.98,1.98-1.98h19.36c1.09,0,1.98,.89,1.98,1.98s-.89,1.98-1.98,1.98H26.65c-1.09,0-1.98-.89-1.98-1.98h.01Zm-7.4-.94c-.7-.84-1.94-.95-2.78-.25s-.95,1.94-.25,2.78l6.06,7.23H1.98c-1.09,0-1.98,.89-1.98,1.98s.89,1.98,1.98,1.98H20.32l-6.07,7.28c-.7,.84-.59,2.09,.25,2.78,.84,.7,2.09,.59,2.78-.25l8.77-10.52c.61-.73,.61-1.8,0-2.53l-8.77-10.47h-.01Zm14.52,6.27c-1.09,0-1.98,.89-1.98,1.98s.89,1.98,1.98,1.98h14.22c1.09,0,1.98-.89,1.98-1.98s-.89-1.98-1.98-1.98h-14.22Zm-1.98,9.31c0-1.09,.89-1.98,1.98-1.98h14.22c1.09,0,1.98,.89,1.98,1.98s-.89,1.98-1.98,1.98h-14.22c-1.09,0-1.98-.89-1.98-1.98Zm-3.17,5.36c-1.09,0-1.98,.89-1.98,1.98s.89,1.98,1.98,1.98h19.36c1.09,0,1.98-.89,1.98-1.98s-.89-1.98-1.98-1.98H26.65Z`,
  ],
}

export const NetworkSearch = {
  prefix: 'fal',
  iconName: 'network-search',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M45.44,42.06l-.22,.11-8.1-7.99c3.17-3.94,4.71-8.97,4.27-14.01-.44-5.03-2.74-9.74-6.46-13.02-3.72-3.28-8.65-5.14-13.68-5.03-5.03,.11-9.85,2.19-13.35,5.69S2.21,16.23,2.1,21.26c-.11,5.03,1.64,9.85,5.03,13.68,3.28,3.72,7.99,6.02,13.02,6.46,5.03,.44,9.96-1.09,14.01-4.27l7.99,7.99c.22,.22,.44,.44,.77,.55s.55,.22,.88,.22,.66,0,.88-.22c.33,0,.55-.33,.77-.55,.44-.44,.66-.98,.66-1.53s-.22-1.09-.66-1.53Zm-12.8-9.3c-2.95,2.95-6.79,4.49-10.83,4.49s-5.91-.98-8.54-2.63c-2.52-1.64-4.49-4.05-5.58-6.89-1.09-2.85-1.42-5.8-.88-8.86,.66-2.95,2.08-5.69,4.16-7.77,2.19-2.19,4.92-3.61,7.77-4.16,2.95-.66,6.02-.33,8.86,.88s5.25,3.17,6.89,5.58c1.64,2.52,2.63,5.47,2.63,8.54s-1.53,7.88-4.49,10.83Zm-.84-10.11h-1.56v-2.55c0-.37-.15-.72-.41-.98-.27-.26-.61-.4-.97-.4h-5.5v-1.17h1.57c.37,0,.72-.15,.97-.41,.26-.26,.41-.61,.41-.98v-5.9c0-.37-.15-.73-.41-.98-.26-.26-.61-.41-.98-.41h-5.9c-.37,0-.73,.15-.98,.41-.26,.26-.41,.61-.41,.98v5.9c0,.37,.15,.73,.41,.98,.26,.26,.61,.41,.98,.41h1.57v1.17h-5.5c-.37,0-.72,.15-.98,.41-.26,.26-.41,.61-.41,.98v2.55h-1.57c-.37,0-.73,.15-.98,.41-.26,.26-.41,.61-.41,.98v5.9c0,.37,.15,.73,.41,.98,.26,.26,.61,.41,.98,.41h5.9c.37,0,.73-.15,.98-.41,.26-.26,.41-.61,.41-.98v-5.9c0-.37-.15-.73-.41-.98-.26-.26-.61-.41-.98-.41h-1.57v-1.17h10.99v1.17h-1.57c-.37,0-.73,.15-.98,.41-.26,.27-.4,.61-.4,.97v5.9c0,.37,.15,.72,.41,.98,.27,.26,.61,.4,.97,.4h5.9c.37,0,.72-.15,.98-.41,.26-.27,.4-.61,.4-.97v-5.9c0-.37-.15-.73-.41-.98-.27-.26-.61-.4-.97-.4Zm-15.14,2.77v3.13h-3.13v-3.13h3.13Zm3.75-10.63v-3.13h3.13v3.13h-3.13Zm10.01,13.76h-3.13v-3.13h3.13v3.13Z`,
  ],
}

export const ServerBusy = {
  prefix: 'fal',
  iconName: 'server-busy',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M10.38,31.3c-.41,0-.82,.12-1.16,.35-.34,.23-.61,.56-.77,.94-.16,.38-.2,.8-.12,1.21,.08,.4,.28,.78,.57,1.07,.29,.29,.67,.49,1.07,.57,.41,.08,.83,.04,1.21-.12,.38-.16,.71-.43,.94-.77s.35-.75,.35-1.16c0-.55-.22-1.08-.61-1.48-.39-.39-.93-.61-1.48-.61Zm0-12.52c-.41,0-.82,.12-1.16,.35-.34,.23-.61,.56-.77,.94-.16,.38-.2,.8-.12,1.21,.08,.4,.28,.78,.57,1.07,.29,.29,.67,.49,1.07,.57,.41,.08,.83,.04,1.21-.12,.38-.16,.71-.43,.94-.77,.23-.34,.35-.75,.35-1.16,0-.55-.22-1.08-.61-1.48-.39-.39-.93-.61-1.48-.61Zm0-12.52c-.41,0-.82,.12-1.16,.35s-.61,.56-.77,.94-.2,.8-.12,1.21c.08,.4,.28,.78,.57,1.07,.29,.29,.67,.49,1.07,.57,.41,.08,.83,.04,1.21-.12,.38-.16,.71-.43,.94-.77,.23-.34,.35-.75,.35-1.16,0-.55-.22-1.08-.61-1.48-.39-.39-.93-.61-1.48-.61Zm25.14,0c0-1.66-.66-3.25-1.84-4.43-1.18-1.17-2.78-1.83-4.44-1.83H8.29c-1.67,0-3.27,.66-4.44,1.83-1.18,1.17-1.84,2.77-1.84,4.43v4.17c0,1.54,.59,3.03,1.63,4.17-1.04,1.14-1.63,2.63-1.63,4.17v4.17c0,1.54,.59,3.03,1.63,4.17-1.04,1.14-1.63,2.63-1.63,4.17v4.17c0,1.66,.66,3.25,1.84,4.43,1.18,1.17,2.78,1.83,4.44,1.83h14.25c2.17,3.74,6.24,6.26,10.89,6.26,6.94,0,12.57-5.61,12.57-12.52,0-6.21-4.53-11.36-10.48-12.35,0-.06,0-.11,0-.17v-4.17c0-1.54-.59-3.03-1.63-4.17,1.04-1.14,1.62-2.63,1.63-4.17V6.26Zm-12.98,22.96H8.29c-.56,0-1.09,.22-1.48,.61s-.61,.92-.61,1.48v4.17c0,.55,.22,1.08,.61,1.48,.39,.39,.93,.61,1.48,.61h12.75c-.11-.68-.17-1.38-.17-2.09,0-2.28,.61-4.42,1.68-6.26Zm3.94-4.17H8.29c-.56,0-1.09-.22-1.48-.61-.39-.39-.61-.92-.61-1.48v-4.17c0-.55,.22-1.08,.61-1.48s.93-.61,1.48-.61H29.24c.56,0,1.09,.22,1.48,.61,.39,.39,.61,.92,.61,1.48v4.17c0,.06,0,.12,0,.17-1.77,.3-3.41,.96-4.85,1.91Zm4.86-14.61c0,.55-.22,1.08-.61,1.48-.39,.39-.93,.61-1.48,.61H8.29c-.56,0-1.09-.22-1.48-.61-.39-.39-.61-.92-.61-1.48V6.26c0-.55,.22-1.08,.61-1.48s.93-.61,1.48-.61H29.24c.56,0,1.09,.22,1.48,.61,.39,.39,.61,.92,.61,1.48v4.17Zm2.1,16.7c-4.63,0-8.38,3.74-8.38,8.35s3.75,8.35,8.38,8.35,8.38-3.74,8.38-8.35-3.75-8.35-8.38-8.35Zm4.19,8.35c0,2.31-1.88,4.17-4.19,4.17s-4.19-1.87-4.19-4.17,1.88-4.17,4.19-4.17,4.19,1.87,4.19,4.17Z`,
  ],
}

export const ServerTime = {
  prefix: 'fal',
  iconName: 'server-time',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M9.05,30.99c.34-.22,.74-.34,1.14-.34,.54,0,1.06,.22,1.44,.6,.38,.38,.6,.9,.6,1.44,0,.4-.12,.8-.34,1.14s-.54,.6-.92,.76-.78,.2-1.18,.12c-.4-.08-.76-.28-1.04-.56-.28-.28-.48-.64-.56-1.04-.08-.4-.04-.8,.12-1.18,.16-.38,.42-.7,.76-.92l-.02-.02Zm0-12.25c.34-.22,.74-.34,1.14-.34,.54,0,1.06,.22,1.44,.6,.38,.38,.6,.9,.6,1.44,0,.4-.12,.8-.34,1.14s-.54,.6-.92,.76-.78,.2-1.18,.12c-.4-.08-.76-.28-1.04-.56-.28-.28-.48-.64-.56-1.04-.08-.4-.04-.8,.12-1.18,.16-.38,.42-.7,.76-.92l-.02-.02Zm0-12.25c.34-.22,.74-.34,1.14-.34,.54,0,1.06,.22,1.44,.6,.38,.38,.6,.9,.6,1.44,0,.4-.12,.8-.34,1.14s-.54,.6-.92,.76-.78,.2-1.18,.12c-.4-.08-.76-.28-1.04-.56-.28-.28-.48-.64-.56-1.04-.08-.4-.04-.8,.12-1.18,.16-.38,.42-.7,.76-.92l-.02-.02ZM32.94,1.82c1.16,1.14,1.8,2.7,1.8,4.34v4.08c0,1.52-.58,2.96-1.6,4.08,1.02,1.12,1.58,2.58,1.6,4.08v3.22c6.38,.98,11.26,6.48,11.26,13.11,0,7.34-5.96,13.27-13.29,13.27-5.14,0-9.58-2.9-11.8-7.14H8.15c-1.62,0-3.18-.64-4.34-1.8-1.16-1.14-1.8-2.7-1.8-4.34v-4.08c0-1.52,.58-2.96,1.6-4.08-1.02-1.12-1.58-2.58-1.6-4.08v-4.08c0-1.52,.58-2.96,1.6-4.08-1.02-1.12-1.58-2.58-1.6-4.08V6.14c0-1.62,.64-3.18,1.8-4.34,1.16-1.14,2.72-1.8,4.34-1.8H28.6c1.62,0,3.18,.64,4.34,1.8v.02ZM8.15,28.61h12.75c-.96,1.84-1.5,3.92-1.5,6.12,0,.7,.06,1.38,.16,2.04H8.15c-.54,0-1.06-.22-1.44-.6-.38-.38-.6-.9-.6-1.44v-4.08c0-.54,.22-1.06,.6-1.44,.38-.38,.9-.6,1.44-.6Zm0-4.08H24.2c1.8-1.5,4.02-2.54,6.46-2.9v-3.22c0-.54-.22-1.06-.6-1.44s-.9-.6-1.44-.6H8.15c-.54,0-1.06,.22-1.44,.6-.38,.38-.6,.9-.6,1.44v4.08c0,.54,.22,1.06,.6,1.44,.38,.38,.9,.6,1.44,.6ZM30.06,11.68c.38-.38,.6-.9,.6-1.44V6.14c0-.54-.22-1.06-.6-1.44-.38-.38-.9-.6-1.44-.6H8.15c-.54,0-1.06,.22-1.44,.6s-.6,.9-.6,1.44v4.08c0,.54,.22,1.06,.6,1.44,.38,.38,.9,.6,1.44,.6H28.6c.54,0,1.06-.22,1.44-.6l.02,.02Zm2.64,14.21c-4.9,0-8.86,3.96-8.86,8.86s3.98,8.86,8.86,8.86,8.86-3.96,8.86-8.86-3.98-8.86-8.86-8.86Zm1.02,3.74c0-1.12-.92-2.04-2.04-2.04s-2.04,.92-2.04,2.04v6.12c0,1.12,.92,2.04,2.04,2.04h6.14c1.14,0,2.04-.92,2.04-2.04s-.92-2.04-2.04-2.04h-4.1v-4.08Z`,
  ],
}

export const TransientIp = {
  prefix: 'fal',
  iconName: 'transient-ip',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M31.01,29.06c-1.1,0-2,.9-2,2v12c0,1.1,.9,2,2,2s2-.9,2-2v-12c0-1.1-.9-2-2-2Zm-13.42,1.58c-.78,.78-.78,2.04,0,2.82l3.58,3.58-3.58,3.58c-.78,.78-.78,2.04,0,2.82,.4,.4,.9,.58,1.42,.58s1.02-.2,1.42-.58l6.42-6.42-6.42-6.42c-.78-.78-2.04-.78-2.82,0l-.02,.04Zm12.26-13.1c0-.88-.2-1.66-.58-2.34s-.96-1.22-1.74-1.6-1.72-.58-2.84-.58h-5.54v14.04h3.42v-5.04h2.12c1.14,0,2.1-.2,2.88-.62s1.34-.96,1.72-1.62c.38-.68,.56-1.42,.56-2.24Zm-3.96,1.3c-.32,.3-.8,.46-1.46,.46h-1.86v-3.52h1.86c.66,0,1.14,.16,1.46,.46s.48,.74,.48,1.3-.16,1-.48,1.3Zm14.94,18.22l3.58-3.58c.78-.78,.78-2.04,0-2.82s-2.04-.78-2.82,0l-6.42,6.42,6.42,6.42c.4,.4,.9,.58,1.42,.58s1.02-.2,1.42-.58c.78-.78,.78-2.04,0-2.82l-3.58-3.58-.02-.04Zm-3.9-18.74c-.28-2.92-1.32-5.72-3-8.12-1.68-2.4-3.96-4.34-6.62-5.6-2.66-1.26-5.58-1.82-8.52-1.62-2.92,.2-5.76,1.16-8.2,2.8-2.1,1.4-3.86,3.26-5.16,5.42s-2.1,4.6-2.36,7.12c-.24,2.5,.06,5.02,.92,7.38,.86,2.36,2.22,4.5,4,6.28l7.38,7.4c.12-.18,.28-.34,.44-.5l1.82-1.82-1.82-1.82c-.66-.66-1.06-1.46-1.24-2.3l-3.78-3.78c-1.36-1.36-2.4-3-3.04-4.8-.64-1.8-.88-3.74-.7-5.64,.18-1.94,.8-3.8,1.8-5.48s2.36-3.1,3.98-4.18c2.12-1.4,4.62-2.16,7.16-2.16s5.04,.76,7.16,2.16c1.62,1.08,2.96,2.5,3.96,4.16s1.62,3.52,1.82,5.46c.2,1.92-.04,3.84-.68,5.66-.28,.78-.62,1.52-1.04,2.22,1.44,.06,2.72,.82,3.48,1.92,.52-.88,.96-1.82,1.3-2.8,.86-2.36,1.16-4.88,.92-7.38l.02,.02Zm-20.26,8.74V13.02h-3.42v14.04h3.42Z`,
  ],
}

export const CloudDataSharing = {
  prefix: 'fal',
  iconName: 'cloud-data-sharing',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M44.96,10.89c-.9-1.49-2.3-2.62-3.95-3.17-.82-1.72-2.13-3.17-3.75-4.18-1.62-1.01-3.5-1.53-5.41-1.5-1.91,.03-3.77,.6-5.37,1.65-1.59,1.05-2.85,2.54-3.63,4.29H13.98c-1.59,0-3.12,.63-4.24,1.76s-1.76,2.65-1.76,4.24v8c0,.53,.21,1.04,.59,1.41,.37,.37,.88,.59,1.41,.59s1.04-.21,1.41-.59c.37-.38,.59-.88,.59-1.41v-8c0-.53,.21-1.04,.59-1.41s.88-.59,1.41-.59h5.56c-1,1.09-1.55,2.52-1.56,4,0,1.59,.63,3.12,1.76,4.24s2.65,1.76,4.24,1.76h14.65c1.74,.01,3.43-.6,4.76-1.72,1.33-1.12,2.22-2.68,2.51-4.4,.29-1.72-.05-3.48-.94-4.97Zm-3.24,5.03c-.17,.41-.41,.77-.72,1.08-.31,.31-.68,.56-1.08,.72-.41,.17-.84,.25-1.28,.25h-14.65c-.53,0-1.04-.21-1.41-.59s-.59-.88-.59-1.41,.21-1.04,.59-1.41c.37-.37,.88-.59,1.41-.59s1.04-.21,1.41-.59c.37-.37,.59-.88,.59-1.41-.02-1.43,.48-2.81,1.39-3.91,.91-1.1,2.19-1.83,3.59-2.07,1.41-.24,2.85,.03,4.08,.76,1.23,.73,2.15,1.87,2.61,3.22,.11,.34,.32,.65,.59,.89,.27,.24,.61,.39,.97,.45,.78,.13,1.49,.54,2,1.15s.78,1.38,.76,2.17c0,.44-.09,.87-.25,1.28Zm-3.74,10.06c-.53,0-1.04,.21-1.41,.59-.37,.37-.59,.88-.59,1.41v6c0,.53-.21,1.04-.59,1.41-.37,.37-.88,.59-1.41,.59h-4.52c-.39-.99-.98-1.88-1.75-2.62-.77-.74-1.68-1.3-2.69-1.64-.91-1.92-2.4-3.51-4.27-4.52-1.87-1.01-4.01-1.41-6.12-1.12-2.11,.29-4.07,1.23-5.6,2.71-1.53,1.48-2.55,3.4-2.91,5.49-1.35,.44-2.51,1.36-3.25,2.57-.74,1.22-1.03,2.66-.8,4.07,.22,1.41,.94,2.69,2.02,3.62,1.08,.93,2.46,1.44,3.89,1.44h14.65c1.71,0,3.37-.6,4.68-1.7,1.31-1.1,2.2-2.62,2.51-4.3h4.14c1.59,0,3.12-.63,4.24-1.76,1.12-1.12,1.76-2.65,1.76-4.24v-6c0-.53-.21-1.04-.59-1.41-.37-.38-.88-.59-1.41-.59Zm-12.25,13.93c-.17,.41-.41,.77-.72,1.08s-.68,.56-1.08,.72c-.41,.17-.84,.25-1.28,.25H7.98c-.53,0-1.04-.21-1.41-.59-.37-.38-.59-.88-.59-1.41s.21-1.04,.59-1.41c.37-.38,.88-.59,1.41-.59s1.04-.21,1.41-.59c.37-.38,.59-.88,.59-1.41-.02-1.43,.48-2.81,1.39-3.91,.91-1.1,2.19-1.83,3.59-2.07,1.41-.24,2.85,.03,4.08,.76,1.23,.73,2.15,1.87,2.61,3.22,.11,.34,.32,.65,.59,.89,.27,.24,.61,.39,.97,.45,.78,.13,1.49,.54,2,1.15,.51,.61,.78,1.38,.76,2.17,0,.44-.09,.87-.25,1.28Z`,
  ],
}

export const StandbyCapacity = {
  prefix: 'fal',
  iconName: 'standby-capacity',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M13.62,34.37c-.28-.28-.65-.48-1.04-.56s-.8-.04-1.18,.12-.69,.42-.91,.75c-.22,.33-.34,.73-.34,1.13,0,.54,.22,1.06,.6,1.44,.38,.38,.9,.6,1.44,.6,.4,0,.8-.12,1.13-.34,.33-.22,.6-.54,.75-.91s.19-.78,.12-1.18c-.08-.39-.27-.76-.56-1.04ZM31.33,7.5c-1.01,0-1.83,.82-1.83,1.83v9.17c0,1.01,.82,1.83,1.83,1.83s1.83-.82,1.83-1.83V9.33c0-1.01-.82-1.83-1.83-1.83Zm-2.85,26.28h-8.15c-.54,0-1.06,.21-1.44,.6-.38,.38-.6,.9-.6,1.44s.21,1.06,.6,1.44c.38,.38,.9,.6,1.44,.6h8.15c.54,0,1.06-.21,1.44-.6,.38-.38,.6-.9,.6-1.44s-.21-1.06-.6-1.44c-.38-.38-.9-.6-1.44-.6ZM31.33,2c-5.43,0-10.17,2.95-12.7,7.33h-3.93c-1.14,0-2.25,.31-3.22,.91-.97,.6-1.75,1.45-2.25,2.47L2.43,26.33c-.28,.57-.43,1.19-.43,1.82v11.73c0,1.62,.64,3.18,1.79,4.32,1.15,1.15,2.7,1.79,4.32,1.79h24.45c1.62,0,3.17-.65,4.32-1.79,1.15-1.15,1.79-2.7,1.79-4.32v-10.52c4.38-2.54,7.33-7.28,7.33-12.7,0-8.1-6.57-14.67-14.67-14.67ZM12.88,14.53c.17-.34,.43-.63,.75-.82s.69-.3,1.07-.3h2.33c-.24,1.05-.36,2.14-.36,3.26,0,3.38,1.14,6.48,3.06,8.96H7.33l5.55-11.1Zm21.71,25.35c0,.54-.22,1.06-.6,1.44s-.9,.6-1.44,.6H8.11c-.54,0-1.06-.22-1.44-.6-.38-.38-.6-.9-.6-1.44v-10.18H24.61c2.01,1.04,4.3,1.63,6.73,1.63,1.12,0,2.21-.13,3.26-.36v8.92Zm-3.26-12.22c-6.08,0-11-4.92-11-11s4.92-11,11-11,11,4.92,11,11-4.92,11-11,11Zm0-5.5c-1.01,0-1.83,.82-1.83,1.83s.82,1.83,1.83,1.83,1.83-.82,1.83-1.83-.82-1.83-1.83-1.83Z`,
  ],
}

export const FileClose = {
  prefix: 'fal',
  iconName: 'file-close',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M27.99,23.99H16c-.53,0-1.04,.21-1.41,.59-.37,.37-.59,.88-.59,1.41s.21,1.04,.59,1.41c.37,.37,.88,.59,1.41,.59h12c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41-.21-1.04-.59-1.41-.88-.59-1.41-.59Zm10.82,14l2.6-2.58c.38-.38,.59-.89,.59-1.42s-.21-1.04-.59-1.42c-.38-.38-.89-.59-1.42-.59s-1.04,.21-1.42,.59l-2.58,2.6-2.58-2.6c-.38-.38-.89-.59-1.42-.59s-1.04,.21-1.42,.59c-.38,.38-.59,.89-.59,1.42s.21,1.04,.59,1.42l2.6,2.58-2.6,2.58c-.19,.19-.34,.41-.44,.65-.1,.24-.15,.51-.15,.77s.05,.53,.15,.77c.1,.24,.25,.46,.44,.65,.19,.19,.41,.34,.65,.44,.24,.1,.51,.15,.77,.15s.53-.05,.77-.15c.24-.1,.46-.25,.65-.44l2.58-2.6,2.58,2.6c.19,.19,.41,.34,.65,.44,.24,.1,.51,.15,.77,.15s.53-.05,.77-.15c.24-.1,.46-.25,.65-.44,.19-.19,.34-.41,.44-.65,.1-.24,.15-.51,.15-.77s-.05-.53-.15-.77c-.1-.24-.25-.46-.44-.65l-2.6-2.58Zm-14.82,2H12c-.53,0-1.04-.21-1.41-.59-.37-.37-.59-.88-.59-1.41V10c0-.53,.21-1.04,.59-1.41,.37-.37,.88-.59,1.41-.59h10v6c0,1.59,.63,3.12,1.76,4.24,1.12,1.12,2.65,1.76,4.24,1.76h6v6c0,.53,.21,1.04,.59,1.41s.88,.59,1.41,.59,1.04-.21,1.41-.59,.59-.88,.59-1.41v-8.12c-.02-.18-.06-.36-.12-.54v-.18c-.1-.21-.22-.39-.38-.56L25.49,4.6c-.17-.16-.35-.28-.56-.38-.07-.01-.13-.01-.2,0-.19-.1-.4-.18-.62-.22H12c-1.59,0-3.12,.63-4.24,1.76s-1.76,2.65-1.76,4.24v27.99c0,1.59,.63,3.12,1.76,4.24,1.12,1.12,2.65,1.76,4.24,1.76h12c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41-.21-1.04-.59-1.41-.88-.59-1.41-.59Zm2-29.17l5.18,5.18h-3.18c-.53,0-1.04-.21-1.41-.59-.37-.37-.59-.88-.59-1.41v-3.18Zm-10,5.18c-.53,0-1.04,.21-1.41,.59-.37,.37-.59,.88-.59,1.41s.21,1.04,.59,1.41c.37,.37,.88,.59,1.41,.59h2c.53,0,1.04-.21,1.41-.59,.37-.37,.59-.88,.59-1.41s-.21-1.04-.59-1.41c-.37-.37-.88-.59-1.41-.59h-2Zm8,16h-8c-.53,0-1.04,.21-1.41,.59-.37,.37-.59,.88-.59,1.41s.21,1.04,.59,1.41c.37,.37,.88,.59,1.41,.59h8c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41-.21-1.04-.59-1.41-.88-.59-1.41-.59Z`,
  ],
}

export const Function = {
  prefix: 'fal',
  iconName: 'function',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M31.24,18.72c0-1.1-.9-2-2-2h-4.25c1.82-7.3,3.39-10.72,4.93-10.72,.44,0,1.48,0,2.22,3.53,.23,1.08,1.28,1.78,2.37,1.55,1.08-.23,1.78-1.29,1.55-2.37-.93-4.46-2.99-6.72-6.13-6.72-4.62,0-6.7,4.96-9.05,14.72h-5.12c-1.1,0-2,.9-2,2s.9,2,2,2h4.17c-2.15,9.03-3.74,15.61-4.87,19.18-.39,1.23-1.32,2.07-2.31,2.1-1.2,.06-2.45-1.2-3.27-3.32-.4-1.03-1.56-1.54-2.59-1.14-1.03,.4-1.54,1.56-1.14,2.59,2.03,5.23,5.42,5.88,6.96,5.88,.06,0,.12,0,.17,0,2.69-.09,5.1-2.05,6-4.89,1.21-3.82,2.88-10.79,5.16-20.38h5.21c1.1,0,2-.9,2-2Zm10.7,19.07l-3.94-4.71,3.9-4.74c.7-.85,.58-2.11-.27-2.82-.85-.7-2.11-.58-2.82,.27l-3.43,4.16-3.46-4.14c-.71-.85-1.97-.96-2.82-.25-.85,.71-.96,1.97-.25,2.82l3.94,4.71-3.9,4.74c-.7,.85-.58,2.11,.27,2.82,.37,.31,.82,.46,1.27,.46,.58,0,1.15-.25,1.54-.73l3.43-4.16,3.46,4.14c.4,.47,.96,.72,1.54,.72,.45,0,.91-.15,1.28-.47,.85-.71,.96-1.97,.25-2.82Z`,
  ],
}

export const Document = {
  prefix: 'fal',
  iconName: 'document',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M30,32h-12c-1.1,0-2,.9-2,2s.9,2,2,2h12c1.1,0,2-.9,2-2s-.9-2-2-2Zm-14-6c0,1.1,.9,2,2,2h12c1.1,0,2-.9,2-2s-.9-2-2-2h-12c-1.1,0-2,.9-2,2Zm23.41-11.41L29.41,4.59c-.38-.38-.88-.59-1.41-.59H12c-2.21,0-4,1.79-4,4V40c0,2.21,1.79,4,4,4h24c2.21,0,4-1.79,4-4V16c0-.53-.21-1.04-.59-1.41Zm-11.41-5.76l7.17,7.17h-7.17v-7.17Zm8,31.17H12V8h12v10c0,1.1,.9,2,2,2h10v20Z`,
  ],
}

export const PieChart = {
  prefix: 'fal',
  iconName: 'pie-chart',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M38,24h-14V10c0-1.2-.8-2-2-2-10,0-18,8-18,18s8,18,18,18,18-8,18-18c0-1.2-.8-2-2-2Zm-14,15.8c-7.6,1.2-14.8-4.2-15.8-11.8-1.2-7.6,4.2-14.8,11.8-15.8v13.8c0,1.2,.8,2,2,2h13.8c-.8,6.2-5.6,11-11.8,11.8ZM30,4c-1.2,0-2,.8-2,2v12c0,1.2,.8,2,2,2h12c1.2,0,2-.8,2-2,0-7.8-6.2-14-14-14Zm2,12v-7.8c4,.8,7,3.8,7.8,7.8h-7.8Z`,
  ],
}

export const Index = {
  prefix: 'fal',
  iconName: 'index',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M43.4,43.05v-7.7c0-1-.8-1.9-1.9-1.9h-11.1c-1,0-1.9,.8-1.9,1.9v2h-3.5v-11.6h3.5v2c0,1,.8,1.9,1.9,1.9h11.1c1,0,1.9-.8,1.9-1.9v-7.7c0-1-.8-1.9-1.9-1.9h-11.1c-1,0-1.9,.8-1.9,1.9v2h-3.5V10.55h3.5v2c0,1,.8,1.9,1.9,1.9h11.1c1,0,1.9-.8,1.9-1.9V4.85c0-1-.8-1.9-1.9-1.9h-11.1c-1,0-1.9,.8-1.9,1.9v1.9h-5.4c-1,0-1.9,.8-1.9,1.9v13.4h-2.5c-.8-3.1-3.6-5.4-6.9-5.4s-7.2,3.2-7.2,7.2,3.2,7.2,7.2,7.2,6-2.2,6.9-5.2h2.6v13.4c0,1,.8,1.9,1.9,1.9h5.4v2c0,1,.8,1.9,1.9,1.9h11c1,0,1.9-.8,1.9-1.9v-.1ZM32.3,6.65h7.4v3.9h-7.4v-3.9ZM11.9,27.25c-1.9,0-3.4-1.5-3.4-3.4s1.5-3.4,3.4-3.4,3.4,1.5,3.4,3.4-1.5,3.4-3.4,3.4Zm20.4-5.3h7.4v3.9h-7.4v-3.9Zm0,19.2v-3.9h7.4v3.9h-7.4Z`,
  ],
}

export const Synchronization = {
  prefix: 'fal',
  iconName: 'synchronization',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M39.82,31.02h-9.06c-.53,0-1.04,.21-1.41,.59s-.59,.88-.59,1.41,.21,1.04,.59,1.41,.88,.59,1.41,.59h4.8c-2.21,2.31-5.05,3.9-8.17,4.57-3.12,.68-6.37,.4-9.33-.78-2.96-1.18-5.5-3.23-7.3-5.87-1.79-2.64-2.75-5.76-2.76-8.95,0-.53-.21-1.04-.59-1.41-.38-.38-.88-.59-1.41-.59s-1.04,.21-1.41,.59c-.38,.38-.59,.88-.59,1.41,.01,3.91,1.16,7.72,3.32,10.98,2.15,3.26,5.22,5.81,8.81,7.35,3.59,1.54,7.55,1.99,11.4,1.3,3.84-.69,7.4-2.49,10.24-5.17v3.54c0,.53,.21,1.04,.59,1.41s.88,.59,1.41,.59,1.04-.21,1.41-.59,.59-.88,.59-1.41v-9c0-.52-.21-1.01-.57-1.38-.36-.37-.85-.58-1.37-.6Zm-9.82-7.02c0-1.19-.35-2.35-1.01-3.33-.66-.99-1.6-1.76-2.69-2.21-1.1-.45-2.3-.57-3.47-.34-1.16,.23-2.23,.8-3.07,1.64-.84,.84-1.41,1.91-1.64,3.07-.23,1.16-.11,2.37,.34,3.47,.45,1.1,1.22,2.03,2.21,2.69,.99,.66,2.15,1.01,3.33,1.01,1.59,0,3.12-.63,4.24-1.76s1.76-2.65,1.76-4.24Zm-8,0c0-.4,.12-.78,.34-1.11,.22-.33,.53-.59,.9-.74,.37-.15,.77-.19,1.16-.11s.74,.27,1.02,.55c.28,.28,.47,.64,.55,1.02s.04,.79-.11,1.16c-.15,.37-.41,.68-.74,.9-.33,.22-.72,.34-1.11,.34-.53,0-1.04-.21-1.41-.59s-.59-.88-.59-1.41Zm2-20c-5.13,.01-10.05,2-13.76,5.54v-3.54c0-.53-.21-1.04-.59-1.41-.38-.38-.88-.59-1.41-.59s-1.04,.21-1.41,.59c-.38,.38-.59,.88-.59,1.41V15c0,.53,.21,1.04,.59,1.41,.38,.38,.88,.59,1.41,.59h9c.53,0,1.04-.21,1.41-.59,.38-.38,.59-.88,.59-1.41s-.21-1.04-.59-1.41c-.38-.38-.88-.59-1.41-.59h-4.8c2.2-2.3,5.05-3.9,8.16-4.57,3.12-.68,6.36-.41,9.33,.78,2.96,1.18,5.5,3.22,7.3,5.86,1.79,2.64,2.76,5.75,2.77,8.94,0,.53,.21,1.04,.59,1.41s.88,.59,1.41,.59,1.04-.21,1.41-.59,.59-.88,.59-1.41c0-2.63-.52-5.23-1.52-7.65-1.01-2.43-2.48-4.63-4.34-6.49-1.86-1.86-4.06-3.33-6.49-4.34-2.43-1.01-5.03-1.52-7.65-1.52h0Z`,
  ],
}

export const Blob = {
  prefix: 'fal',
  iconName: 'blob',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M39.88,17.34v-.18c-.1-.21-.22-.39-.38-.56L27.5,4.6c-.17-.16-.35-.28-.56-.38h-.18l-.64-.22H14c-1.59,0-3.12,.63-4.24,1.76-1.13,1.13-1.76,2.65-1.76,4.24v28c0,1.59,.63,3.12,1.76,4.24,1.13,1.13,2.65,1.76,4.24,1.76h20c1.59,0,3.12-.63,4.24-1.76,1.13-1.13,1.76-2.65,1.76-4.24V17.88c-.02-.18-.06-.36-.12-.54Zm-11.88-6.52l5.18,5.18h-5.18v-5.18Zm8,27.18c0,.53-.21,1.04-.59,1.41-.38,.38-.88,.59-1.41,.59H14c-.53,0-1.04-.21-1.41-.59-.38-.38-.59-.88-.59-1.41V10c0-.53,.21-1.04,.59-1.41,.38-.38,.88-.59,1.41-.59h10v10c0,.53,.21,1.04,.59,1.41,.38,.38,.88,.59,1.41,.59h10v18Zm-6.87-1.77h3.69v-13.16h-5.54v3.31h1.85v9.85Zm-9.21-13.43c-3.78,0-5.22,2.77-5.22,6.66s1.44,6.7,5.22,6.7,5.24-2.75,5.24-6.7-1.46-6.66-5.24-6.66Zm0,10.03c-1.57,0-1.75-1.49-1.75-3.37s.18-3.33,1.75-3.33,1.75,1.51,1.75,3.33-.18,3.37-1.75,3.37Z`,
  ],
}

export const Iops = {
  prefix: 'fal',
  iconName: 'iops',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M39.88,17.34v-.18c-.1-.21-.22-.39-.38-.56L27.5,4.6c-.17-.16-.35-.28-.56-.38h-.18l-.64-.22H14c-1.59,0-3.12,.63-4.24,1.76-1.13,1.13-1.76,2.65-1.76,4.24v28c0,1.59,.63,3.12,1.76,4.24,1.13,1.13,2.65,1.76,4.24,1.76h20c1.59,0,3.12-.63,4.24-1.76,1.13-1.13,1.76-2.65,1.76-4.24V17.88c-.02-.18-.06-.36-.12-.54Zm-11.88-6.52l5.18,5.18h-5.18v-5.18Zm8,27.18c0,.53-.21,1.04-.59,1.41-.38,.38-.88,.59-1.41,.59H14c-.53,0-1.04-.21-1.41-.59-.38-.38-.59-.88-.59-1.41V10c0-.53,.21-1.04,.59-1.41,.38-.38,.88-.59,1.41-.59h10v10c0,.53,.21,1.04,.59,1.41,.38,.38,.88,.59,1.41,.59h10v18Zm-6.87-1.77h3.69v-13.16h-5.54v3.31h1.85v9.85Zm-9.21-13.43c-3.78,0-5.22,2.77-5.22,6.66s1.44,6.7,5.22,6.7,5.24-2.75,5.24-6.7-1.46-6.66-5.24-6.66Zm0,10.03c-1.57,0-1.75-1.49-1.75-3.37s.18-3.33,1.75-3.33,1.75,1.51,1.75,3.33-.18,3.37-1.75,3.37Z`,
  ],
}

export const TargetPoint = {
  prefix: 'fal',
  iconName: 'target-point',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24,2C11.87,2,2,11.87,2,24s9.87,22,22,22,22-9.87,22-22S36.13,2,24,2Zm0,40c-9.93,0-18-8.07-18-18S14.07,6,24,6s18,8.07,18,18-8.08,18-18,18Zm0-24c-3.31,0-6,2.69-6,6s2.69,6,6,6,6-2.69,6-6-2.69-6-6-6Zm0,8c-1.1,0-2-.9-2-2s.9-2,2-2,2,.9,2,2-.9,2-2,2Zm0-16c-7.72,0-14,6.28-14,14s6.28,14,14,14,14-6.28,14-14-6.28-14-14-14Zm0,24c-5.51,0-10-4.49-10-10s4.49-10,10-10,10,4.49,10,10-4.49,10-10,10Z`,
  ],
}

export const EndPoint = {
  prefix: 'fal',
  iconName: 'end-point',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M35.42,22.58l-5-5c-.38-.38-.89-.59-1.42-.59s-1.04,.21-1.42,.59-.59,.89-.59,1.42,.21,1.04,.59,1.42l1.6,1.58h-10.36l1.6-1.58c.38-.38,.59-.89,.59-1.42s-.21-1.04-.59-1.42-.89-.59-1.42-.59-1.04,.21-1.42,.59l-5,5c-.18,.19-.32,.41-.42,.66-.2,.49-.2,1.03,0,1.52,.1,.25,.24,.47,.42,.66l5,5c.19,.19,.41,.34,.65,.44,.24,.1,.51,.15,.77,.15s.53-.05,.77-.15c.24-.1,.46-.25,.65-.44,.19-.19,.34-.41,.44-.65,.1-.24,.15-.51,.15-.77s-.05-.53-.15-.77c-.1-.24-.25-.46-.44-.65l-1.6-1.58h10.36l-1.6,1.58c-.19,.19-.34,.41-.44,.65-.1,.24-.15,.51-.15,.77s.05,.53,.15,.77c.1,.24,.25,.46,.44,.65,.19,.19,.41,.34,.65,.44,.24,.1,.51,.15,.77,.15s.53-.05,.77-.15c.24-.1,.46-.25,.65-.44l5-5c.18-.19,.32-.41,.42-.66,.2-.49,.2-1.03,0-1.52-.1-.25-.24-.47-.42-.66ZM6,12c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41v20c0,.53,.21,1.04,.59,1.41,.38,.38,.88,.59,1.41,.59s1.04-.21,1.41-.59c.38-.38,.59-.88,.59-1.41V14c0-.53-.21-1.04-.59-1.41-.38-.38-.88-.59-1.41-.59Zm36,0c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41v20c0,.53,.21,1.04,.59,1.41s.88,.59,1.41,.59,1.04-.21,1.41-.59,.59-.88,.59-1.41V14c0-.53-.21-1.04-.59-1.41-.38-.38-.88-.59-1.41-.59Z`,
  ],
}

export const Call = {
  prefix: 'fal',
  iconName: 'call',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M39.04,26.06c-.44,0-.9-.14-1.34-.24-.89-.2-1.77-.46-2.63-.78-.93-.34-1.95-.32-2.87,.05-.92,.37-1.67,1.07-2.1,1.96l-.44,.9c-1.96-1.09-3.75-2.44-5.34-4.01-1.58-1.59-2.93-3.38-4.01-5.34l.84-.56c.89-.44,1.59-1.19,1.96-2.1,.37-.92,.39-1.94,.05-2.87-.32-.86-.58-1.74-.78-2.63-.1-.44-.18-.9-.24-1.36-.24-1.41-.98-2.69-2.09-3.61-1.1-.92-2.5-1.41-3.93-1.39h-6.02c-.87,0-1.72,.17-2.51,.52-.79,.35-1.49,.87-2.07,1.52-.57,.65-1,1.41-1.25,2.24-.25,.83-.32,1.7-.2,2.56,1.07,8.41,4.91,16.22,10.91,22.21,6,5.98,13.83,9.79,22.24,10.83h.76c1.48,0,2.91-.54,4.01-1.53,.64-.57,1.14-1.26,1.49-2.04,.35-.78,.52-1.62,.52-2.47v-6.02c-.02-1.39-.53-2.74-1.44-3.8-.9-1.06-2.15-1.78-3.52-2.02Zm1,12.04c0,.28-.06,.57-.18,.83-.12,.26-.29,.49-.5,.68-.22,.19-.49,.34-.77,.42-.28,.09-.58,.11-.88,.08-7.52-.96-14.5-4.4-19.85-9.77-5.35-5.37-8.75-12.37-9.68-19.89-.03-.29,0-.59,.08-.87,.09-.28,.23-.55,.42-.77,.19-.21,.42-.39,.68-.5,.26-.12,.54-.18,.83-.18h6.02c.47-.01,.92,.14,1.29,.43,.37,.29,.62,.7,.72,1.15,.08,.55,.18,1.09,.3,1.63,.23,1.06,.54,2.1,.92,3.11l-2.81,1.3c-.24,.11-.46,.27-.64,.46-.18,.19-.32,.42-.41,.67-.09,.25-.13,.51-.12,.78,.01,.26,.07,.52,.18,.76,2.89,6.19,7.86,11.16,14.05,14.05,.49,.2,1.04,.2,1.53,0,.25-.09,.48-.23,.68-.41,.2-.18,.36-.4,.47-.64l1.24-2.81c1.04,.37,2.1,.68,3.17,.92,.54,.12,1.08,.22,1.63,.3,.46,.1,.86,.35,1.15,.72s.44,.82,.43,1.29l.04,6.26Z`,
  ],
}

export const Meeting = {
  prefix: 'fal',
  iconName: 'meeting',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M14,19.99h4c.53,0,1.04-.21,1.41-.59,.38-.38,.59-.88,.59-1.41s-.21-1.04-.59-1.41c-.38-.38-.88-.59-1.41-.59h-4c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41s.21,1.04,.59,1.41c.38,.38,.88,.59,1.41,.59ZM42,7.99H26v-2c0-.53-.21-1.04-.59-1.41-.38-.38-.88-.59-1.41-.59s-1.04,.21-1.41,.59c-.38,.38-.59,.88-.59,1.41v2H6c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41V29.99c0,1.59,.63,3.12,1.76,4.24,1.13,1.13,2.65,1.76,4.24,1.76h9.18l-4.6,4.58c-.19,.19-.34,.41-.44,.65-.1,.24-.15,.51-.15,.77s.05,.53,.15,.77c.1,.24,.25,.46,.44,.65,.19,.19,.41,.34,.65,.44s.51,.15,.77,.15,.53-.05,.77-.15c.24-.1,.46-.25,.65-.44l4.58-4.6v3.18c0,.53,.21,1.04,.59,1.41s.88,.59,1.41,.59,1.04-.21,1.41-.59,.59-.88,.59-1.41v-3.18l4.58,4.6c.19,.19,.41,.34,.65,.44,.24,.1,.51,.15,.77,.15s.53-.05,.77-.15c.24-.1,.46-.25,.65-.44,.19-.19,.34-.41,.44-.65,.1-.24,.15-.51,.15-.77s-.05-.53-.15-.77c-.1-.24-.25-.46-.44-.65l-4.6-4.58h9.18c1.59,0,3.12-.63,4.24-1.76,1.13-1.13,1.76-2.65,1.76-4.24V9.99c0-.53-.21-1.04-.59-1.41-.38-.38-.88-.59-1.41-.59Zm-2,22c0,.53-.21,1.04-.59,1.41s-.88,.59-1.41,.59H10c-.53,0-1.04-.21-1.41-.59-.38-.38-.59-.88-.59-1.41V11.99H40V29.99Zm-26-2h12c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41-.21-1.04-.59-1.41-.88-.59-1.41-.59H14c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41s.21,1.04,.59,1.41c.38,.38,.88,.59,1.41,.59Z`,
  ],
}

export const Mailbox = {
  prefix: 'fal',
  iconName: 'mailbox',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M34,40H10c-1.59,0-3.12-.63-4.24-1.76-1.13-1.13-1.76-2.65-1.76-4.24V18c0-.53-.21-1.04-.59-1.41-.38-.38-.88-.59-1.41-.59s-1.04,.21-1.41,.59c-.38,.38-.59,.88-.59,1.41v16c0,2.65,1.05,5.2,2.93,7.07,1.88,1.88,4.42,2.93,7.07,2.93h24c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41-.21-1.04-.59-1.41c-.38-.38-.88-.59-1.41-.59ZM42,4H14c-1.59,0-3.12,.63-4.24,1.76-1.13,1.13-1.76,2.65-1.76,4.24V30c0,1.59,.63,3.12,1.76,4.24,1.13,1.13,2.65,1.76,4.24,1.76h28c1.59,0,3.12-.63,4.24-1.76,1.13-1.13,1.76-2.65,1.76-4.24V10c0-1.59-.63-3.12-1.76-4.24-1.13-1.13-2.65-1.76-4.24-1.76h0Zm-.82,4l-11.76,11.76c-.19,.19-.41,.34-.65,.44-.24,.1-.51,.15-.77,.15s-.53-.05-.77-.15c-.24-.1-.46-.25-.65-.44L14.82,8h26.36Zm2.82,22c0,.53-.21,1.04-.59,1.41s-.88,.59-1.41,.59H14c-.53,0-1.04-.21-1.41-.59-.38-.38-.59-.88-.59-1.41V10.74l11.76,11.76c1.12,1.12,2.65,1.75,4.24,1.75s3.11-.63,4.24-1.75l11.76-11.76V30Z`,
  ],
}

export const MessageStorage = {
  prefix: 'fal',
  iconName: 'message-storage',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M43.41,5.59c-.38-.38-.88-.59-1.41-.59h-10c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41V23c0,.36,.1,.72,.29,1.03,.19,.31,.46,.57,.78,.73,.32,.17,.69,.25,1.05,.22,3.35-.21,6.66-.04,9.88,0,.28,0,.56-.06,.82-.18,.35-.16,.65-.41,.86-.74,.21-.32,.32-.7,.32-1.08V7c0-.53-.21-1.04-.59-1.41Zm-3.41,14.89h-6v-4h6v4Zm0-8h-6v-3.48h6v3.48Zm2,16.52c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41v6c0,.53-.21,1.04-.59,1.41-.38,.38-.88,.59-1.41,.59H10c-.53,0-1.04-.21-1.41-.59-.38-.38-.59-.88-.59-1.41V17.82l11.76,11.78c1.12,1.11,2.63,1.73,4.2,1.74,1,0,1.98-.25,2.86-.72,.23-.13,.44-.3,.6-.5,.17-.21,.29-.44,.36-.69,.07-.25,.1-.52,.07-.78-.03-.26-.11-.52-.23-.75-.13-.23-.3-.44-.5-.6-.2-.17-.44-.29-.69-.36-.25-.07-.52-.1-.78-.07-.26,.03-.52,.11-.75,.23-.37,.19-.79,.26-1.2,.2-.41-.06-.8-.25-1.1-.54L10.82,15h13.18c.53,0,1.04-.21,1.41-.59,.38-.38,.59-.88,.59-1.41s-.21-1.04-.59-1.41c-.38-.38-.88-.59-1.41-.59H10c-1.59,0-3.12,.63-4.24,1.76-1.13,1.13-1.76,2.65-1.76,4.24v20c0,1.59,.63,3.12,1.76,4.24s2.65,1.76,4.24,1.76h28c1.59,0,3.12-.63,4.24-1.76,1.13-1.13,1.76-2.65,1.76-4.24v-6c0-.53-.21-1.04-.59-1.41-.38-.38-.88-.59-1.41-.59Z`,
  ],
}

export const MessageRead = {
  prefix: 'fal',
  iconName: 'message-read',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M40.42,17.64L28,5.56c-1.05-1-2.45-1.56-3.9-1.56s-2.85,.56-3.9,1.56L7.78,17.56c-.55,.48-1,1.07-1.3,1.74-.31,.67-.47,1.39-.48,2.12v17.16c.02,1.46,.62,2.84,1.66,3.86,1.04,1.02,2.44,1.58,3.9,1.56h24.88c1.46,.02,2.86-.55,3.9-1.56,1.04-1.02,1.64-2.4,1.66-3.86V21.42c0-.7-.14-1.4-.41-2.05-.27-.65-.67-1.24-1.17-1.73ZM22.88,8.44c.31-.28,.71-.43,1.12-.43s.81,.16,1.12,.43l10.88,10.56-10.94,10.56c-.31,.28-.71,.43-1.12,.43s-.81-.16-1.12-.43l-10.82-10.56,10.88-10.56Zm15.12,30.14c-.03,.39-.2,.76-.49,1.02-.29,.26-.67,.41-1.07,.4H11.56c-.39,.01-.78-.13-1.07-.4-.29-.26-.47-.63-.49-1.02v-15.88l8.1,7.8-3.32,3.2c-.37,.37-.58,.88-.58,1.41s.21,1.04,.58,1.41c.19,.2,.41,.35,.66,.46,.25,.11,.51,.16,.78,.16,.51,0,1.01-.2,1.38-.56l3.54-3.4c.88,.54,1.89,.82,2.92,.82s2.04-.28,2.92-.82l3.54,3.4c.37,.36,.87,.56,1.38,.56,.27,0,.54-.06,.78-.16,.25-.11,.47-.26,.66-.46,.37-.37,.58-.88,.58-1.41s-.21-1.04-.58-1.41l-3.34-3.2,8-7.8v15.88Z`,
  ],
}

export const Globe = {
  prefix: 'fal',
  iconName: 'globe',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24,2C11.85,2,2,11.85,2,24s9.85,22,22,22,22-9.85,22-22S36.15,2,24,2Zm17.89,20h-7.99c-.45-5.53-2.3-10.83-5.35-15.42,7.11,1.85,12.51,7.95,13.34,15.42ZM24.01,7.1c3.33,4.3,5.38,9.47,5.88,14.9h-11.76c.5-5.43,2.54-10.6,5.88-14.9Zm-4.56-.52c-3.05,4.59-4.9,9.89-5.35,15.42H6.11c.82-7.47,6.23-13.57,13.34-15.42ZM6.11,26h7.99c.45,5.53,2.3,10.83,5.35,15.42-7.11-1.85-12.51-7.95-13.34-15.42Zm12.02,0h11.76c-.5,5.43-2.55,10.6-5.88,14.9-3.34-4.3-5.38-9.47-5.88-14.9Zm10.43,15.42c3.05-4.59,4.9-9.89,5.35-15.42h7.99c-.83,7.47-6.23,13.57-13.34,15.42Z`,
  ],
}

export const UserAccount = {
  prefix: 'fal',
  iconName: 'user-account',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M29.62,24.56c1.27-1.36,1.98-3.14,2-5,0-2.01-.8-3.93-2.21-5.35-1.42-1.42-3.34-2.21-5.35-2.21s-3.93,.8-5.35,2.21c-1.42,1.42-2.21,3.34-2.21,5.35,.02,1.86,.73,3.64,2,5-1.76,.88-3.28,2.18-4.41,3.79-1.13,1.61-1.85,3.48-2.09,5.43-.06,.53,.1,1.06,.43,1.48,.33,.42,.82,.68,1.35,.74,.53,.06,1.06-.1,1.48-.43,.42-.33,.68-.82,.74-1.35,.24-1.94,1.18-3.72,2.64-5.01,1.46-1.29,3.35-2.01,5.3-2.01s3.84,.71,5.3,2.01c1.46,1.29,2.4,3.08,2.64,5.01,.06,.51,.31,.98,.7,1.32,.4,.33,.9,.5,1.42,.46h.22c.52-.06,1-.33,1.33-.74,.33-.41,.48-.94,.43-1.46-.22-1.94-.91-3.81-2.02-5.42-1.11-1.61-2.6-2.92-4.34-3.82Zm-5.62-1.44c-.7,0-1.39-.21-1.98-.6-.59-.39-1.04-.95-1.31-1.6-.27-.65-.34-1.37-.2-2.06s.48-1.32,.97-1.82c.5-.5,1.13-.84,1.82-.97,.69-.14,1.41-.07,2.06,.2,.65,.27,1.21,.73,1.6,1.31,.39,.59,.6,1.27,.6,1.98,0,.94-.38,1.85-1.04,2.52-.67,.67-1.57,1.04-2.52,1.04ZM38,4H10c-1.59,0-3.12,.63-4.24,1.76s-1.76,2.65-1.76,4.24v28c0,1.59,.63,3.12,1.76,4.24,1.13,1.13,2.65,1.76,4.24,1.76h28c1.59,0,3.12-.63,4.24-1.76,1.13-1.13,1.76-2.65,1.76-4.24V10c0-1.59-.63-3.12-1.76-4.24-1.13-1.13-2.65-1.76-4.24-1.76Zm2,34c0,.53-.21,1.04-.59,1.41s-.88,.59-1.41,.59H10c-.53,0-1.04-.21-1.41-.59-.38-.38-.59-.88-.59-1.41V10c0-.53,.21-1.04,.59-1.41,.38-.38,.88-.59,1.41-.59h28c.53,0,1.04,.21,1.41,.59,.38,.38,.59,.88,.59,1.41v28Z`,
  ],
}

export const FilesShared = {
  prefix: 'fal',
  iconName: 'files-shared',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M27.4,27.31c.37-.38,.59-.88,.59-1.41s-.21-1.04-.59-1.41-.88-.59-1.41-.59H15.99c-.53,0-1.04,.21-1.41,.59-.38,.37-.59,.88-.59,1.41s.21,1.04,.59,1.41,.88,.59,1.41,.59h10c.53,0,1.04-.21,1.41-.59Zm-5.41,12.69v-.1H11.99c-.53,0-1.04-.21-1.41-.59-.38-.37-.59-.88-.59-1.41V9.9c0-.53,.21-1.04,.59-1.41s.88-.59,1.41-.59h10v6c0,1.59,.63,3.12,1.76,4.24,1.13,1.13,2.65,1.76,4.24,1.76h6c0,.53,.21,1.04,.59,1.41s.88,.59,1.41,.59,1.04-.21,1.41-.59,.59-.88,.59-1.41v-2.12c-.02-.18-.06-.36-.12-.54v-.18c-.1-.21-.22-.39-.38-.56L25.49,4.5c-.17-.16-.35-.28-.56-.38-.07-.01-.13-.01-.2,0-.17-.06-.34-.1-.52-.12H11.99c-1.59,0-3.12,.63-4.24,1.76-1.13,1.13-1.76,2.65-1.76,4.24v28c0,1.59,.63,3.12,1.76,4.24,1.13,1.13,2.65,1.76,4.24,1.76h10c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41-.21-1.04-.59-1.41c-.38-.37-.88-.59-1.41-.59Zm4-29.28l5.18,5.18h-3.18c-.53,0-1.04-.21-1.41-.59s-.59-.88-.59-1.41v-3.18Zm-10,9.18h2c.53,0,1.04-.21,1.41-.59,.38-.38,.59-.88,.59-1.41s-.21-1.04-.59-1.41c-.38-.38-.88-.59-1.41-.59h-2c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41s.21,1.04,.59,1.41c.38,.38,.88,.59,1.41,.59Zm0,12c-.53,0-1.04,.21-1.41,.59-.38,.37-.59,.88-.59,1.41s.21,1.04,.59,1.41,.88,.59,1.41,.59h2c.53,0,1.04-.21,1.41-.59,.38-.37,.59-.88,.59-1.41s-.21-1.04-.59-1.41-.88-.59-1.41-.59h-2Zm25.68,6.46c-.31-.7-.82-1.3-1.46-1.73-.64-.42-1.4-.65-2.17-.65l-.04-.06v-.02c-.86,0-1.69,.28-2.37,.78l-3.5-1.6,3.82-1.76c.6,.37,1.29,.57,2,.58,.79,0,1.56-.23,2.22-.67,.66-.44,1.17-1.06,1.47-1.8,.3-.73,.38-1.54,.23-2.31-.15-.78-.54-1.49-1.09-2.05-.56-.56-1.27-.94-2.05-1.09-.78-.15-1.58-.08-2.31,.23-.73,.3-1.36,.82-1.79,1.47-.44,.66-.67,1.43-.67,2.22l-3.78,1.74c-.52-.37-1.12-.61-1.74-.7-.63-.09-1.27-.03-1.87,.18-.6,.21-1.14,.55-1.58,1.01s-.77,1.01-.95,1.62-.22,1.25-.1,1.88c.12,.62,.38,1.21,.76,1.72,.39,.5,.89,.91,1.46,1.18,.57,.27,1.21,.4,1.84,.38,.64,0,1.28-.17,1.84-.48l4.2,2c.09,.77,.4,1.49,.89,2.08s1.14,1.04,1.87,1.27c.73,.23,1.52,.25,2.26,.05s1.41-.62,1.93-1.19c.51-.57,.85-1.28,.97-2.04,.12-.76,.02-1.54-.29-2.24Z`,
  ],
}

// light
export const Exchange = {
  prefix: 'fal',
  iconName: 'exchange',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M34,26H6c-.4,0-.8,.1-1.1,.3-.3,.2-.6,.5-.7,.9-.2,.4-.2,.8-.1,1.2,0,.4,.3,.7,.5,1l8,8c.2,.2,.4,.3,.7,.4,.2,.1,.5,.2,.8,.2s.5,0,.8-.2c.2-.1,.5-.3,.7-.4,.2-.2,.3-.4,.4-.7,.1-.2,.2-.5,.2-.8s0-.5-.2-.8c-.1-.2-.3-.5-.4-.7l-4.6-4.6h23.2c.5,0,1-.2,1.4-.6s.6-.9,.6-1.4-.2-1-.6-1.4-.9-.6-1.4-.6Zm10-6.4c0-.4-.3-.7-.5-1l-8-8c-.2-.2-.4-.3-.7-.4-.2-.1-.5-.2-.8-.2-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4l4.6,4.6H14c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4c.4,.4,.9,.6,1.4,.6h28c.4,0,.8-.1,1.1-.3,.3-.2,.6-.5,.7-.9,.2-.4,.2-.8,.1-1.2Z`,
  ],
}

export const QuestionCircle = {
  prefix: 'fal',
  iconName: 'question-circle',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M25.4,30.6c-.2-.2-.4-.3-.7-.4-.2-.1-.5-.2-.8-.2s-.5,0-.8,.2c-.2,.1-.5,.3-.7,.4,0,0-.2,.2-.2,.3,0,.1-.1,.2-.2,.4,0,.1,0,.2-.1,.4,0,.1,0,.3,0,.4,0,.3,0,.5,.2,.8,0,.2,.2,.5,.4,.7s.4,.3,.7,.4c.2,.1,.5,.2,.8,.2s.5,0,.8-.2c.2,0,.5-.2,.7-.4,.2-.2,.3-.4,.4-.7,0-.2,.1-.5,.1-.8,0-.3,0-.5-.1-.8,0-.2-.2-.5-.4-.7Zm2.4-15.2c-1.1-.9-2.4-1.4-3.8-1.4-1.1,0-2.1,.3-3,.8s-1.7,1.3-2.2,2.2c-.1,.2-.2,.5-.3,.7,0,.3,0,.5,0,.8,0,.3,.2,.5,.3,.7,.2,.2,.4,.4,.6,.5,.2,.1,.5,.2,.8,.3,.3,0,.5,0,.8,0,.3,0,.5-.2,.7-.4,.2-.2,.4-.4,.5-.6,.2-.3,.4-.6,.7-.7,.3-.2,.7-.3,1-.3,.5,0,1,.2,1.4,.6,.4,.4,.6,.9,.6,1.4s-.2,1-.6,1.4-.9,.6-1.4,.6-1,.2-1.4,.6-.6,.9-.6,1.4v2c0,.5,.2,1,.6,1.4s.9,.6,1.4,.6,1-.2,1.4-.6,.6-.9,.6-1.4v-.4c1.3-.5,2.4-1.4,3.1-2.6,.7-1.2,1-2.6,.7-4-.2-1.4-1-2.6-2-3.6Zm14.6,.9c-1-2.4-2.5-4.6-4.3-6.5s-4.1-3.3-6.5-4.3c-2.4-1-5-1.5-7.7-1.5-4,0-7.8,1.2-11.1,3.4-3.3,2.2-5.9,5.3-7.4,9-1.5,3.7-1.9,7.7-1.1,11.6,.8,3.9,2.7,7.4,5.5,10.2,2.8,2.8,6.4,4.7,10.2,5.5,3.9,.8,7.9,.4,11.6-1.1,3.7-1.5,6.8-4.1,9-7.4,2.2-3.3,3.4-7.2,3.4-11.1s-.5-5.2-1.5-7.7Zm-7.2,19c-3,3-7.1,4.7-11.3,4.7s-6.3-.9-8.9-2.7c-2.6-1.8-4.7-4.3-5.9-7.2-1.2-2.9-1.5-6.1-.9-9.2,.6-3.1,2.1-6,4.4-8.2s5.1-3.8,8.2-4.4c3.1-.6,6.3-.3,9.2,.9,2.9,1.2,5.4,3.3,7.2,5.9,1.8,2.6,2.7,5.7,2.7,8.9s-1.7,8.3-4.7,11.3Z`,
  ],
}

export const Circle = {
  prefix: 'fal',
  iconName: 'circle',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M42.5,16.3c-1-2.4-2.5-4.6-4.3-6.5-1.9-1.9-4.1-3.3-6.5-4.3-2.4-1-5-1.5-7.7-1.5-4,0-7.8,1.2-11.1,3.4-3.3,2.2-5.9,5.3-7.4,9-1.5,3.7-1.9,7.7-1.1,11.6,.8,3.9,2.7,7.4,5.5,10.2,2.8,2.8,6.4,4.7,10.2,5.5,3.9,.8,7.9,.4,11.6-1.1,3.7-1.5,6.8-4.1,9-7.4,2.2-3.3,3.4-7.2,3.4-11.1s-.5-5.2-1.5-7.7Zm-7.2,19c-3,3-7.1,4.7-11.3,4.7s-6.3-.9-8.9-2.7c-2.6-1.8-4.7-4.3-5.9-7.2-1.2-2.9-1.5-6.1-.9-9.2,.6-3.1,2.1-6,4.4-8.2s5.1-3.8,8.2-4.4c3.1-.6,6.3-.3,9.2,.9,2.9,1.2,5.4,3.3,7.2,5.9,1.8,2.6,2.7,5.7,2.7,8.9s-1.7,8.3-4.7,11.3Z`,
  ],
}

export const FileAlt = {
  prefix: 'fal',
  iconName: 'file-alt',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M18,20h2c.5,0,1-.2,1.4-.6,.4-.4,.6-.9,.6-1.4s-.2-1-.6-1.4c-.4-.4-.9-.6-1.4-.6h-2c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4c.4,.4,.9,.6,1.4,.6Zm12,12h-12c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4c.4,.4,.9,.6,1.4,.6h12c.5,0,1-.2,1.4-.6s.6-.9,.6-1.4-.2-1-.6-1.4-.9-.6-1.4-.6Zm9.9-14.7v-.2c0-.2-.2-.4-.4-.6L27.5,4.6c-.2-.2-.4-.3-.6-.4,0,0-.1,0-.2,0-.2-.1-.4-.2-.7-.2H14c-1.6,0-3.1,.6-4.2,1.8s-1.8,2.7-1.8,4.2v28c0,1.6,.6,3.1,1.8,4.2,1.1,1.1,2.7,1.8,4.2,1.8h20c1.6,0,3.1-.6,4.2-1.8,1.1-1.1,1.8-2.7,1.8-4.2V18h0c0-.3,0-.5-.1-.7Zm-11.9-6.5l5.2,5.2h-3.2c-.5,0-1-.2-1.4-.6-.4-.4-.6-.9-.6-1.4v-3.2Zm8,27.2c0,.5-.2,1-.6,1.4s-.9,.6-1.4,.6H14c-.5,0-1-.2-1.4-.6-.4-.4-.6-.9-.6-1.4V10c0-.5,.2-1,.6-1.4,.4-.4,.9-.6,1.4-.6h10v6c0,1.6,.6,3.1,1.8,4.2,1.1,1.1,2.7,1.8,4.2,1.8h6v18Zm-18-14c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4c.4,.4,.9,.6,1.4,.6h12c.5,0,1-.2,1.4-.6s.6-.9,.6-1.4-.2-1-.6-1.4-.9-.6-1.4-.6h-12Z`,
  ],
}

export const Desktop = {
  prefix: 'fal',
  iconName: 'desktop',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M38,6H10c-1.6,0-3.1,.6-4.2,1.8s-1.8,2.7-1.8,4.2V28c0,1.6,.6,3.1,1.8,4.2,1.1,1.1,2.7,1.8,4.2,1.8h12v4H14c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4c.4,.4,.9,.6,1.4,.6h20c.5,0,1-.2,1.4-.6s.6-.9,.6-1.4-.2-1-.6-1.4-.9-.6-1.4-.6h-8v-4h12c1.6,0,3.1-.6,4.2-1.8,1.1-1.1,1.8-2.7,1.8-4.2V12c0-1.6-.6-3.1-1.8-4.2-1.1-1.1-2.7-1.8-4.2-1.8Zm2,22c0,.5-.2,1-.6,1.4s-.9,.6-1.4,.6H10c-.5,0-1-.2-1.4-.6-.4-.4-.6-.9-.6-1.4V12c0-.5,.2-1,.6-1.4,.4-.4,.9-.6,1.4-.6h28c.5,0,1,.2,1.4,.6,.4,.4,.6,.9,.6,1.4V28Z`,
  ],
}

export const Columns = {
  prefix: 'fal',
  iconName: 'columns',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M43.4,4.6c-.4-.4-.9-.6-1.4-.6H6c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4V42c0,.5,.2,1,.6,1.4,.4,.4,.9,.6,1.4,.6H42c.5,0,1-.2,1.4-.6s.6-.9,.6-1.4V6c0-.5-.2-1-.6-1.4ZM22,40H8V8h14V40Zm18,0h-14V8h14V40Z`,
  ],
}

export const Hdd = {
  prefix: 'fal',
  iconName: 'hdd',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M14,32c0-1.1,.9-2,2-2s2,.9,2,2-.9,2-2,2-2-.9-2-2Zm10-2h8c1.1,0,2,.9,2,2s-.9,2-2,2h-8c-1.1,0-2-.9-2-2s.9-2,2-2Zm12,8H12c-1.1,0-2-.9-2-2v-10h28v10c0,1.1-.9,2-2,2ZM16.7,11.1c.3-.7,1-1.1,1.8-1.1h11.1c.8,0,1.4,.4,1.8,1.1l5.4,10.9H11.2l5.4-10.9Zm24.9,11.6l-6.7-13.4c-1-2-3.1-3.3-5.4-3.3h-11.1c-2.3,0-4.3,1.3-5.4,3.3l-6.7,13.4c-.3,.6-.4,1.2-.4,1.8v11.5c0,3.3,2.7,6,6,6h24c3.3,0,6-2.7,6-6v-11.5c0-.6-.1-1.2-.4-1.8Z`,
  ],
}

export const Cube = {
  prefix: 'fal',
  iconName: 'cube',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M40.8,14.3c0,0,0-.1-.1-.2,0,0-.1-.2-.2-.2h-.2l-.3-.3L24.9,4.3c-.3-.2-.7-.3-1.1-.3s-.7,.1-1.1,.3L7.9,13.6l-.2,.2h-.2c0,.2-.1,.3-.2,.4,0,0,0,.1-.1,.2v.5c-.1,.2-.1,.3-.1,.5v17.5c0,.3,0,.7,.3,1,.2,.3,.4,.5,.7,.7l15,9.3c0,0,.2,0,.3,.1h.2c.3,.1,.7,.1,1,0h.2c.1,0,.2,0,.3-.1l14.9-9.3c.3-.2,.5-.4,.7-.7,.2-.3,.3-.6,.3-1V15.3c0-.2,0-.3,0-.5v-.5Zm-19,24.2l-11-6.8v-12.8l11,6.8v12.8Zm2-16.3l-11.2-6.9,11.2-6.9,11.2,6.9-11.2,6.9Zm13,9.5l-11,6.8v-12.8l11-6.8v12.8Z`,
  ],
}

export const LayerGroup = {
  prefix: 'fal',
  iconName: 'layer-group',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M5,17.7l18,10.4c.3,.2,.6,.3,1,.3s.7,0,1-.3l18-10.4c.3-.2,.6-.4,.7-.7,.2-.3,.3-.6,.3-1,0-.4,0-.7-.3-1-.2-.3-.4-.6-.7-.7L25,3.9c-.3-.2-.6-.3-1-.3s-.7,0-1,.3L5,14.3c-.3,.2-.6,.4-.7,.7-.2,.3-.3,.7-.3,1,0,.3,0,.7,.3,1,.2,.3,.4,.6,.7,.7ZM24,8l14,8-14,8-14-8,14-8Zm17,14.3l-17,9.7L7,22.3c-.2-.1-.5-.2-.7-.3-.3,0-.5,0-.8,0-.3,0-.5,.2-.7,.3-.2,.2-.4,.4-.5,.6-.3,.5-.3,1-.2,1.5s.5,.9,.9,1.2l18,10.4c.3,.2,.6,.3,1,.3s.7,0,1-.3l18-10.4c.5-.3,.8-.7,.9-1.2,.1-.5,0-1.1-.2-1.5-.1-.2-.3-.4-.5-.6-.2-.2-.4-.3-.7-.3-.3,0-.5,0-.8,0-.3,0-.5,.1-.7,.3h0Zm0,8.1l-17,9.7L7,30.3c-.2-.1-.5-.2-.7-.3-.3,0-.5,0-.8,0-.3,0-.5,.2-.7,.3-.2,.2-.4,.4-.5,.6-.3,.5-.3,1-.2,1.5s.5,.9,.9,1.2l18,10.4c.3,.2,.6,.3,1,.3s.7,0,1-.3l18-10.4c.5-.3,.8-.7,.9-1.2,.1-.5,0-1.1-.2-1.5-.1-.2-.3-.4-.5-.6-.2-.2-.4-.3-.7-.3-.3,0-.5,0-.8,0-.3,0-.5,.1-.7,.3h0Z`,
  ],
}

export const FileSearch = {
  prefix: 'fal',
  iconName: 'file-search',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24,40H10c-.5,0-1-.2-1.4-.6-.4-.4-.6-.9-.6-1.4V10c0-.5,.2-1,.6-1.4,.4-.4,.9-.6,1.4-.6h10v6c0,1.6,.6,3.1,1.8,4.2,1.1,1.1,2.7,1.8,4.2,1.8h6v2c0,.5,.2,1,.6,1.4s.9,.6,1.4,.6,1-.2,1.4-.6,.6-.9,.6-1.4v-4h0c0-.3,0-.5-.1-.7v-.2c0-.2-.2-.4-.4-.6L23.5,4.6c-.2-.2-.4-.3-.6-.4,0,0-.1,0-.2,0-.2-.1-.4-.2-.7-.2H10c-1.6,0-3.1,.6-4.2,1.8s-1.8,2.7-1.8,4.2v28c0,1.6,.6,3.1,1.8,4.2,1.1,1.1,2.7,1.8,4.2,1.8h14c.5,0,1-.2,1.4-.6s.6-.9,.6-1.4-.2-1-.6-1.4-.9-.6-1.4-.6Zm0-29.2l5.2,5.2h-3.2c-.5,0-1-.2-1.4-.6-.4-.4-.6-.9-.6-1.4v-3.2Zm-10,5.2c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4c.4,.4,.9,.6,1.4,.6h2c.5,0,1-.2,1.4-.6,.4-.4,.6-.9,.6-1.4s-.2-1-.6-1.4c-.4-.4-.9-.6-1.4-.6h-2Zm29.4,24.6l-2.3-2.3c.7-1.3,1-2.8,.9-4.3-.2-1.5-.9-2.9-1.9-3.9-1-1-2.2-1.7-3.6-2-1.4-.3-2.8-.1-4.1,.4-1.3,.5-2.4,1.5-3.2,2.6-.8,1.2-1.2,2.6-1.1,4,0,1.2,.3,2.4,.9,3.4,.6,1.1,1.5,1.9,2.5,2.5,1,.6,2.2,1,3.4,1,1.2,0,2.4-.3,3.5-.9l2.3,2.3c.2,.2,.4,.3,.7,.4,.2,.1,.5,.2,.8,.2s.5,0,.8-.2c.2-.1,.5-.3,.7-.4,.2-.2,.3-.4,.4-.7,.1-.2,.2-.5,.2-.8s0-.5-.2-.8c-.1-.2-.3-.5-.4-.7Zm-6.3-3.5c-.6,.5-1.3,.8-2.1,.8s-1.5-.3-2.1-.8c-.6-.6-.9-1.3-.9-2.1,0-.4,0-.8,.2-1.1,.2-.4,.4-.7,.7-1,.5-.5,1.2-.8,2-.9,.4,0,.8,0,1.2,.2,.4,.1,.7,.4,1,.6s.5,.6,.7,1c.2,.4,.2,.8,.2,1.2,0,.8-.3,1.5-.9,2.1Zm-11.1-13.1H14c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4c.4,.4,.9,.6,1.4,.6h12c.5,0,1-.2,1.4-.6s.6-.9,.6-1.4-.2-1-.6-1.4-.9-.6-1.4-.6Zm-4,12c.5,0,1-.2,1.4-.6s.6-.9,.6-1.4-.2-1-.6-1.4-.9-.6-1.4-.6h-8c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4c.4,.4,.9,.6,1.4,.6h8Z`,
  ],
}

export const UserCircle = {
  prefix: 'fal',
  iconName: 'user-circle',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24,4c-3.9,0-7.7,1.1-10.9,3.3-3.2,2.1-5.8,5.2-7.4,8.7-1.6,3.6-2.1,7.5-1.4,11.3,.6,3.8,2.4,7.4,5,10.3,1.9,2,4.2,3.7,6.7,4.8,2.5,1.1,5.3,1.7,8,1.7s5.5-.6,8-1.7c2.5-1.1,4.8-2.7,6.7-4.8,2.6-2.9,4.4-6.4,5-10.3,.6-3.8,.1-7.8-1.4-11.3-1.6-3.6-4.1-6.6-7.4-8.7-3.2-2.1-7-3.3-10.9-3.3Zm0,36c-4.1,0-8.1-1.6-11.1-4.5,.9-2.2,2.4-4.1,4.4-5.4,2-1.3,4.3-2,6.7-2s4.7,.7,6.7,2c2,1.3,3.5,3.2,4.4,5.4-3,2.9-7,4.5-11.1,4.5Zm-4-20c0-.8,.2-1.6,.7-2.2,.4-.7,1.1-1.2,1.8-1.5,.7-.3,1.5-.4,2.3-.2,.8,.2,1.5,.5,2.1,1.1,.6,.6,.9,1.3,1.1,2.1,.2,.8,0,1.6-.2,2.3-.3,.7-.8,1.4-1.5,1.8-.7,.4-1.4,.7-2.2,.7s-2.1-.4-2.8-1.2c-.8-.8-1.2-1.8-1.2-2.8Zm17.8,12c-1.8-3.1-4.5-5.4-7.8-6.8,1-1.2,1.7-2.6,1.9-4.1,.2-1.5,0-3.1-.6-4.5-.6-1.4-1.7-2.6-3-3.4-1.3-.8-2.8-1.3-4.3-1.3s-3,.4-4.3,1.3c-1.3,.8-2.3,2-3,3.4-.6,1.4-.8,3-.6,4.5,.2,1.5,.9,2.9,1.9,4.1-3.3,1.3-6,3.7-7.8,6.8-1.4-2.4-2.2-5.2-2.2-8,0-4.2,1.7-8.3,4.7-11.3,3-3,7.1-4.7,11.3-4.7s8.3,1.7,11.3,4.7c3,3,4.7,7.1,4.7,11.3,0,2.8-.8,5.6-2.2,8Z`,
  ],
}

export const UserFriends = {
  prefix: 'fal',
  iconName: 'user-friends',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24.6,24.4c1.1-.9,1.9-2.1,2.5-3.4,.6-1.3,.9-2.7,.9-4.1,0-2.7-1.1-5.2-2.9-7.1-1.9-1.9-4.4-2.9-7.1-2.9s-5.2,1.1-7.1,2.9c-1.9,1.9-2.9,4.4-2.9,7.1,0,1.4,.3,2.8,.9,4.1,.6,1.3,1.4,2.4,2.5,3.4-2.8,1.3-5.2,3.3-6.8,5.9-1.7,2.6-2.6,5.6-2.6,8.7,0,.5,.2,1,.6,1.4,.4,.4,.9,.6,1.4,.6s1-.2,1.4-.6c.4-.4,.6-.9,.6-1.4,0-3.2,1.3-6.2,3.5-8.5,2.3-2.3,5.3-3.5,8.5-3.5s6.2,1.3,8.5,3.5c2.3,2.3,3.5,5.3,3.5,8.5s.2,1,.6,1.4,.9,.6,1.4,.6,1-.2,1.4-.6,.6-.9,.6-1.4c0-3.1-.9-6.1-2.6-8.7-1.7-2.6-4-4.6-6.8-5.9Zm-6.6-1.4c-1.2,0-2.3-.4-3.3-1-1-.7-1.8-1.6-2.2-2.7-.5-1.1-.6-2.3-.3-3.5,.2-1.2,.8-2.2,1.6-3.1,.8-.8,1.9-1.4,3.1-1.6,1.2-.2,2.4-.1,3.5,.3,1.1,.5,2,1.2,2.7,2.2,.7,1,1,2.1,1,3.3s-.6,3.1-1.8,4.2-2.7,1.8-4.2,1.8Zm19.5,.6c1.3-1.4,2.1-3.2,2.4-5.1,.3-1.9,0-3.9-.8-5.6-.8-1.8-2.1-3.3-3.7-4.3-1.6-1-3.5-1.6-5.4-1.6-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4c.4,.4,.9,.6,1.4,.6,1.6,0,3.1,.6,4.2,1.8,1.1,1.1,1.8,2.7,1.8,4.2,0,1.1-.3,2.1-.8,3-.5,.9-1.3,1.7-2.2,2.2-.3,.2-.5,.4-.7,.7-.2,.3-.3,.6-.3,1,0,.3,0,.7,.2,1,.2,.3,.4,.6,.7,.7l.8,.5h.3c2.4,1.3,4.4,3.1,5.9,5.4,1.4,2.3,2.2,4.9,2.1,7.5,0,.5,.2,1,.6,1.4s.9,.6,1.4,.6,1-.2,1.4-.6,.6-.9,.6-1.4c0-3.1-.8-6.1-2.2-8.8-1.5-2.7-3.6-5-6.2-6.6Z`,
  ],
}

export const Portrait = {
  prefix: 'fal',
  iconName: 'portrait',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M35.2,4H12.8c-2.1,0-3.8,1.7-3.8,3.8V40.2c0,2.1,1.7,3.8,3.8,3.8h22.5c2.1,0,3.8-1.7,3.8-3.8V7.8c0-2.1-1.7-3.8-3.8-3.8Zm0,36.2H12.8V7.8h22.5V40.2Zm-11.2-16.2c2.8,0,5-2.2,5-5s-2.2-5-5-5-5,2.2-5,5,2.2,5,5,5Zm-7,10h14c1,0,1.7-.7,1.7-1.5v-1.5c0-2.5-2.4-4.5-5.2-4.5h-.4c-1,.4-2,.6-3.1,.6s-2.2-.2-3.1-.6h-.4c-2.9,0-5.2,2-5.2,4.5v1.5c0,.8,.8,1.5,1.8,1.5Z`,
  ],
}

export const Random = {
  prefix: 'fal',
  iconName: 'random',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M43.8,5.2c-.2-.5-.6-.9-1.1-1.1-.2-.1-.5-.2-.8-.2h-12c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4c.4,.4,.9,.6,1.4,.6h7.2L4.6,40.6c-.2,.2-.3,.4-.4,.7-.1,.2-.2,.5-.2,.8s0,.5,.2,.8c.1,.2,.3,.5,.4,.7,.2,.2,.4,.3,.7,.4,.2,.1,.5,.2,.8,.2s.5,0,.8-.2c.2-.1,.5-.3,.7-.4L40,10.8v7.2c0,.5,.2,1,.6,1.4,.4,.4,.9,.6,1.4,.6s1-.2,1.4-.6c.4-.4,.6-.9,.6-1.4V6c0-.3,0-.5-.2-.8Zm-26.4,14.8c.4,.4,.9,.6,1.4,.6s1-.2,1.4-.6c.4-.4,.6-.9,.6-1.4s-.2-1-.6-1.4L7.7,4.6c-.4-.4-.9-.6-1.4-.6s-1,.2-1.4,.6c-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4l12.6,12.6Zm24.6,8c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4v7.2l-9.1-9.2c-.4-.4-.9-.6-1.4-.6s-1.1,.2-1.4,.6c-.4,.4-.6,.9-.6,1.4s.2,1.1,.6,1.4l9.2,9.1h-7.2c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4c.4,.4,.9,.6,1.4,.6h12c.3,0,.5,0,.8-.2,.5-.2,.9-.6,1.1-1.1,.1-.2,.2-.5,.2-.8v-12c0-.5-.2-1-.6-1.4-.4-.4-.9-.6-1.4-.6Z`,
  ],
}

export const CommentAltLines = {
  prefix: 'fal',
  iconName: 'comment-alt-lines',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M26,22H14c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4c.4,.4,.9,.6,1.4,.6h12c.5,0,1-.2,1.4-.6s.6-.9,.6-1.4-.2-1-.6-1.4-.9-.6-1.4-.6Zm8-8H14c-.5,0-1,.2-1.4,.6-.4,.4-.6,.9-.6,1.4s.2,1,.6,1.4c.4,.4,.9,.6,1.4,.6h20c.5,0,1-.2,1.4-.6,.4-.4,.6-.9,.6-1.4s-.2-1-.6-1.4c-.4-.4-.9-.6-1.4-.6Zm4-10H10c-1.6,0-3.1,.6-4.2,1.8s-1.8,2.7-1.8,4.2V30c0,1.6,.6,3.1,1.8,4.2,1.1,1.1,2.7,1.8,4.2,1.8h23.2l7.4,7.4c.2,.2,.4,.3,.7,.4,.2,0,.5,.1,.8,.1,.3,0,.5,0,.8-.2,.4-.2,.7-.4,.9-.7,.2-.3,.3-.7,.3-1.1V10c0-1.6-.6-3.1-1.8-4.2-1.1-1.1-2.7-1.8-4.2-1.8Zm2,33.2l-4.6-4.6c-.2-.2-.4-.3-.7-.4-.2,0-.5-.1-.8-.1H10c-.5,0-1-.2-1.4-.6-.4-.4-.6-.9-.6-1.4V10c0-.5,.2-1,.6-1.4,.4-.4,.9-.6,1.4-.6h28c.5,0,1,.2,1.4,.6,.4,.4,.6,.9,.6,1.4v27.2Z`,
  ],
}

export const ProjectDiagram = {
  prefix: 'fal',
  iconName: 'project-diagram',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M42.7,7.2h-8.3c-1.3,0-2.3,1-2.3,2.3v1.9H15.9v-1.9c0-1.3-1-2.3-2.3-2.3H5.3c-1.3,0-2.3,1-2.3,2.3v8.3c0,1.3,1,2.3,2.3,2.3h6.1l6.2,10.8v7.7c0,1.3,1,2.3,2.3,2.3h8.3c1.3,0,2.3-1,2.3-2.3v-8.3c0-1.3-1-2.3-2.3-2.3h-7l-5.3-9.3v-2.8h16.3v1.9c0,1.3,1,2.3,2.3,2.3h8.3c1.3,0,2.3-1,2.3-2.3V9.4c0-1.3-1-2.3-2.3-2.3ZM12.4,16.5H6.5v-5.9h5.9v5.9Zm8.7,14.9h5.9v5.9h-5.9v-5.9Zm20.4-14.9h-5.9v-5.9h5.9v5.9Z`,
  ],
}

export const List = {
  prefix: 'fal',
  iconName: 'list',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M12,22h-2c-1.1,0-2,.9-2,2s.9,2,2,2h2c1.1,0,2-.9,2-2s-.9-2-2-2Zm0,10h-2c-1.1,0-2,.9-2,2s.9,2,2,2h2c1.1,0,2-.9,2-2s-.9-2-2-2Zm8-16h18c1.1,0,2-.9,2-2s-.9-2-2-2H20c-1.1,0-2,.9-2,2s.9,2,2,2Zm18,16H20c-1.1,0-2,.9-2,2s.9,2,2,2h18c1.1,0,2-.9,2-2s-.9-2-2-2Zm0-10H20c-1.1,0-2,.9-2,2s.9,2,2,2h18c1.1,0,2-.9,2-2s-.9-2-2-2ZM12,12h-2c-1.1,0-2,.9-2,2s.9,2,2,2h2c1.1,0,2-.9,2-2s-.9-2-2-2Z`,
  ],
}

export const ExchangeAlt = {
  prefix: 'fal',
  iconName: 'exchange-alt',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M14,22h28c.4,0,.8-.1,1.1-.3,.3-.2,.6-.5,.7-.9,.2-.4,.2-.8,0-1.2,0-.4-.3-.7-.5-1l-8-8c-.2-.2-.4-.3-.7-.4-.2,0-.5-.2-.8-.2s-.5,0-.8,.2c-.2,0-.5,.3-.7,.4-.2,.2-.3,.4-.4,.7-.1,.2-.2,.5-.2,.8s0,.5,.2,.8c.1,.2,.3,.5,.4,.7l4.6,4.6H13.8c-.5,0-1,.2-1.4,.6s-.6,.9-.6,1.4,.2,1,.6,1.4,.9,.6,1.4,.6l.2-.2Zm-10,6.4c0,.4,.3,.7,.5,1l8,8c.2,.2,.4,.3,.7,.4,.2,.1,.5,.2,.8,.2,.5,0,1-.2,1.4-.6,.4-.4,.6-.9,.6-1.4s-.2-1-.6-1.4l-4.6-4.6h23.2c.5,0,1-.2,1.4-.6s.6-.9,.6-1.4-.2-1-.6-1.4c-.4-.4-.9-.6-1.4-.6H6c-.4,0-.8,.1-1.1,.3-.3,.2-.6,.5-.7,.9-.2,.4-.2,.8,0,1.2h-.1Z`,
  ],
}

export const IdBadge = {
  prefix: 'fal',
  iconName: 'id-badge',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M35.2,4H12.8c-2.1,0-3.8,1.7-3.8,3.8V40.2c0,2.1,1.7,3.8,3.8,3.8h22.5c2.1,0,3.8-1.7,3.8-3.8V7.8c0-2.1-1.7-3.8-3.8-3.8Zm0,36.2H12.8V7.8h22.5V40.2ZM20.2,12.8h7.5c.7,0,1.2-.6,1.2-1.2s-.6-1.2-1.2-1.2h-7.5c-.7,0-1.2,.6-1.2,1.2s.6,1.2,1.2,1.2Zm3.8,13.8c2.8,0,5-2.2,5-5s-2.2-5-5-5-5,2.2-5,5,2.2,5,5,5Zm-7,10h14c1,0,1.7-.7,1.7-1.5v-1.5c0-2.5-2.4-4.5-5.2-4.5s-1.5,.6-3.5,.6-2.6-.6-3.5-.6c-2.9,0-5.2,2-5.2,4.5v1.5c0,.8,.8,1.5,1.8,1.5Z`,
  ],
}

export const Mobile = {
  prefix: 'fal',
  iconName: 'mobile',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M25.4,32.6l-.3-.2c-.1,0-.2-.1-.4-.2l-.4-.2c-.3,0-.7,0-1,0-.3,0-.6,.3-.8,.5-.2,.2-.3,.4-.4,.7-.2,.4-.2,.8-.1,1.2,0,.4,.3,.7,.5,1,.3,.3,.6,.5,1,.6,.4,0,.8,0,1.2-.1,.2-.1,.5-.3,.7-.4,.3-.3,.5-.6,.5-1,0-.4,0-.8-.1-1.2,0-.2-.2-.4-.4-.6Zm6.6-28.6h-16c-1.6,0-3.1,.6-4.2,1.8-1.1,1.1-1.8,2.7-1.8,4.2v28c0,1.6,.6,3.1,1.8,4.2,1.1,1.1,2.7,1.8,4.2,1.8h16c1.6,0,3.1-.6,4.2-1.8,1.1-1.1,1.8-2.7,1.8-4.2V10c0-1.6-.6-3.1-1.8-4.2-1.1-1.1-2.7-1.8-4.2-1.8Zm2,34c0,.5-.2,1-.6,1.4s-.9,.6-1.4,.6h-16c-.5,0-1-.2-1.4-.6-.4-.4-.6-.9-.6-1.4V10c0-.5,.2-1,.6-1.4,.4-.4,.9-.6,1.4-.6h16c.5,0,1,.2,1.4,.6,.4,.4,.6,.9,.6,1.4v28Z`,
  ],
}

export const SdCard = {
  prefix: 'fal',
  iconName: 'sd-card',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M40,4H14.1c-.6,0-1.1,.2-1.5,.7l-6.1,6.8c-.3,.4-.5,.8-.5,1.3v29.2c0,1.1,.9,2,2,2H40c1.1,0,2-.9,2-2V6c0-1.1-.9-2-2-2Zm-2,36H10V13.5l5-5.5h3v4c0,1.1,.9,2,2,2s2-.9,2-2v-4h2v4c0,1.1,.9,2,2,2s2-.9,2-2v-4h2v4c0,1.1,.9,2,2,2s2-.9,2-2v-4h4V40Z`,
  ],
}

export const ShieldCheck = {
  prefix: 'fal',
  iconName: 'shield-check',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M28.6,18.6c-.6,0-1,.2-1.4,.6h0l-5.4,5.4-1.8-1.8c-.4-.4-.9-.6-1.4-.6s-1,.2-1.4,.6-.6,.9-.6,1.4,.2,1,.6,1.4l3.2,3.2c.2,.2,.4,.4,.7,.5,.3,0,.5,.2,.8,.2s.6,0,.8-.2c.3,0,.5-.3,.7-.5l6.9-6.9c.4-.4,.6-.9,.6-1.4s-.2-1-.6-1.4-.9-.6-1.4-.6Zm11.2-10.6c0-.3-.3-.5-.6-.7-.2-.2-.5-.3-.8-.4h-.9c-2.1,.5-4.4,.5-6.5,0s-4.2-1.3-6-2.6c-.4-.2-.8-.4-1.1-.4s-.8,0-1.1,.4c-1.8,1.2-3.8,2.1-6,2.6-2.1,.5-4.4,.5-6.5,0h-.9c-.3,0-.6,.2-.8,.4-.2,.2-.4,.4-.6,.7s-.2,.6-.2,.9v14.9c0,2.9,.7,5.7,2,8.3,1.3,2.6,3.2,4.8,5.5,6.4l7.3,5.2c.4,.3,.8,.4,1.1,.4s.9,0,1.1-.4l7.3-5.2c2.4-1.7,4.2-3.9,5.5-6.4,1.3-2.6,2-5.3,2-8.3V8.9c0-.3,0-.6-.2-.9Zm-3.8,15.8c0,2.2-.6,4.5-1.5,6.4-1,2-2.5,3.7-4.3,5l-6.1,4.4-6.1-4.4c-1.8-1.3-3.3-3-4.3-5-1-2-1.5-4.2-1.5-6.4V11.1c4.2,.4,8.4-.6,12-2.8,3.6,2.2,7.8,3.1,12,2.8v12.6h-.2Z`,
  ],
}

export const MapMarkerAlt = {
  prefix: 'fal',
  iconName: 'map-marker-alt',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M40.8,19.3c-.3-2.9-1.3-5.7-3-8.1-1.7-2.4-3.9-4.3-6.6-5.6-2.6-1.3-5.5-1.8-8.5-1.6-2.9,.2-5.7,1.2-8.1,2.8-2.1,1.4-3.8,3.2-5.1,5.4-1.3,2.2-2.1,4.6-2.3,7.1-.2,2.5,0,5,.9,7.3,.8,2.3,2.2,4.5,4,6.2l10.5,10.6c.2,.2,.4,.3,.6,.4,.2,.1,.5,.2,.8,.2s.5,0,.8-.2c.2-.1,.5-.2,.6-.4l10.5-10.6c1.8-1.8,3.1-3.9,4-6.2,.8-2.3,1.2-4.9,.9-7.3Zm-7.7,10.8l-9.1,9.1-9.1-9.1c-1.3-1.3-2.4-3-3-4.8-.6-1.8-.9-3.7-.7-5.6,.2-1.9,.8-3.8,1.8-5.4,1-1.7,2.3-3.1,4-4.2,2.1-1.4,4.6-2.1,7.1-2.1s5,.7,7.1,2.1c1.6,1.1,3,2.5,3.9,4.1,1,1.7,1.6,3.5,1.8,5.4,.2,1.9,0,3.8-.7,5.6-.6,1.8-1.7,3.4-3,4.8ZM24,12.1c-1.8,0-3.5,.5-5,1.5-1.5,1-2.6,2.4-3.3,4-.7,1.6-.9,3.4-.5,5.2,.3,1.7,1.2,3.3,2.4,4.6,1.3,1.3,2.8,2.1,4.6,2.4,1.7,.3,3.5,.2,5.2-.5,1.6-.7,3-1.8,4-3.3,1-1.5,1.5-3.2,1.5-5,0-2.4-.9-4.6-2.6-6.3-1.7-1.7-3.9-2.6-6.3-2.6Zm0,13.9c-1,0-1.9-.3-2.8-.8-.8-.5-1.5-1.3-1.8-2.2-.4-.9-.5-1.9-.3-2.9,.2-1,.7-1.8,1.4-2.5,.7-.7,1.6-1.2,2.5-1.4,1-.2,2,0,2.9,.3,.9,.4,1.7,1,2.2,1.8,.5,.8,.8,1.8,.8,2.8s-.5,2.6-1.5,3.5c-.9,.9-2.2,1.5-3.5,1.5Z`,
  ],
}

export const CodeMerge = {
  prefix: 'fal',
  iconName: 'code-merge',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M39.9,17.1c-.2-1.4-.9-2.7-2-3.6s-2.4-1.5-3.8-1.5c-1.4,0-2.7,.5-3.8,1.4-1.1,.9-1.8,2.1-2.1,3.5-.3,1.4,0,2.8,.6,4,.7,1.2,1.7,2.2,3,2.7-.3,.7-.8,1.4-1.5,1.8-.7,.4-1.4,.7-2.2,.7h-8c-1.4,0-2.8,.4-4,1.1V15.7c1.3-.5,2.5-1.4,3.2-2.6,.7-1.2,1-2.7,.7-4-.2-1.4-1-2.7-2-3.6-1.1-.9-2.5-1.4-3.9-1.4s-2.8,.5-3.9,1.4c-1.1,.9-1.8,2.2-2,3.6-.2,1.4,0,2.8,.7,4,.7,1.2,1.8,2.1,3.2,2.6v16.7c-1.3,.5-2.4,1.4-3.1,2.6-.7,1.2-1,2.6-.8,4,.2,1.4,.9,2.6,2,3.6s2.4,1.4,3.8,1.5c1.4,0,2.8-.4,3.9-1.3,1.1-.9,1.9-2.1,2.1-3.5,.3-1.4,0-2.8-.6-4-.7-1.2-1.7-2.2-3-2.7,.3-.7,.8-1.3,1.5-1.8s1.4-.7,2.2-.7h8c1.8,0,3.6-.6,5-1.8,1.4-1.1,2.4-2.8,2.8-4.5,1.3-.5,2.5-1.4,3.2-2.6,.7-1.2,1-2.6,.8-4ZM12.6,8.6c.4-.4,.9-.6,1.4-.6s.8,.1,1.1,.3c.3,.2,.6,.5,.7,.9,.2,.4,.2,.8,.1,1.2,0,.4-.3,.7-.5,1-.3,.3-.6,.5-1,.5-.4,0-.8,0-1.2-.1s-.7-.4-.9-.7c-.2-.3-.3-.7-.3-1.1s.2-1,.6-1.4Zm2.8,30.8c-.4,.4-.9,.6-1.4,.6s-.8-.1-1.1-.3c-.3-.2-.6-.5-.7-.9-.2-.4-.2-.8-.1-1.2,0-.4,.3-.7,.5-1,.3-.3,.6-.5,1-.5,.4,0,.8,0,1.2,.1,.4,.2,.7,.4,.9,.7,.2,.3,.3,.7,.3,1.1s-.2,1-.6,1.4Zm20-20c-.4,.4-.9,.6-1.4,.6s-.8-.1-1.1-.3c-.3-.2-.6-.5-.7-.9-.2-.4-.2-.8-.1-1.2,0-.4,.3-.7,.5-1,.3-.3,.6-.5,1-.5,.4,0,.8,0,1.2,.1,.4,.2,.7,.4,.9,.7,.2,.3,.3,.7,.3,1.1s-.2,1-.6,1.4Z`,
  ],
}

export const Folders = {
  prefix: 'fal',
  iconName: 'folders',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M38,11h-12.6l-.6-2c-.4-1.2-1.2-2.2-2.2-2.9-1-.7-2.2-1.1-3.5-1.1H10c-1.6,0-3.1,.6-4.2,1.8s-1.8,2.7-1.8,4.2v26c0,1.6,.6,3.1,1.8,4.2,1.1,1.1,2.7,1.8,4.2,1.8h28c1.6,0,3.1-.6,4.2-1.8,1.1-1.1,1.8-2.7,1.8-4.2V17c0-1.6-.6-3.1-1.8-4.2-1.1-1.1-2.7-1.8-4.2-1.8Zm2,26c0,.5-.2,1-.6,1.4-.4,.4-.9,.6-1.4,.6H10c-.5,0-1-.2-1.4-.6-.4-.4-.6-.9-.6-1.4V11c0-.5,.2-1,.6-1.4s.9-.6,1.4-.6h9.1c.4,0,.8,.1,1.2,.4,.3,.2,.6,.6,.7,1l1.1,3.3c.1,.4,.4,.7,.7,1,.3,.2,.8,.4,1.2,.4h14c.5,0,1,.2,1.4,.6,.4,.4,.6,.9,.6,1.4v20Z`,
  ],
}

export const AddMessages = {
  prefix: 'fal',
  iconName: 'add-messages',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M34,14h2v2c0,.53,.21,1.04,.59,1.41s.88,.59,1.41,.59,1.04-.21,1.41-.59,.59-.88,.59-1.41v-2h2c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41-.21-1.04-.59-1.41-.88-.59-1.41-.59h-2v-2c0-.53-.21-1.04-.59-1.41s-.88-.59-1.41-.59-1.04,.21-1.41,.59-.59,.88-.59,1.41v2h-2c-.53,0-1.04,.21-1.41,.59s-.59,.88-.59,1.41,.21,1.04,.59,1.41,.88,.59,1.41,.59Zm8,8c-.53,0-1.04,.21-1.41,.59s-.59,.88-.59,1.41v12c0,.53-.21,1.04-.59,1.41s-.88,.59-1.41,.59H10c-.53,0-1.04-.21-1.41-.59-.38-.38-.59-.88-.59-1.41V16.82l11.76,11.78c1.12,1.12,2.65,1.75,4.24,1.75s3.11-.63,4.24-1.75l4.94-4.94c.38-.38,.59-.89,.59-1.42s-.21-1.04-.59-1.42c-.38-.38-.89-.59-1.42-.59s-1.04,.21-1.42,.59l-4.94,4.94c-.37,.37-.88,.57-1.4,.57s-1.03-.21-1.4-.57L10.82,14h15.18c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41-.21-1.04-.59-1.41-.88-.59-1.41-.59H10c-1.59,0-3.12,.63-4.24,1.76s-1.76,2.65-1.76,4.24v20c0,1.59,.63,3.12,1.76,4.24s2.65,1.76,4.24,1.76h28c1.59,0,3.12-.63,4.24-1.76,1.13-1.13,1.76-2.65,1.76-4.24v-12c0-.53-.21-1.04-.59-1.41s-.88-.59-1.41-.59Z`,
  ],
}

export const ServerAlt = {
  prefix: 'fal',
  iconName: 'server-alt',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M30,34c-.4,0-.78,.12-1.11,.34-.33,.22-.59,.53-.74,.9-.15,.37-.19,.77-.11,1.16,.08,.39,.27,.74,.55,1.02,.28,.28,.64,.47,1.02,.55,.39,.08,.79,.04,1.16-.11,.37-.15,.68-.41,.9-.74,.22-.33,.34-.72,.34-1.11,0-.53-.21-1.04-.59-1.41s-.88-.59-1.41-.59Zm-12,0h-6c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41s.21,1.04,.59,1.41c.38,.38,.88,.59,1.41,.59h6c.53,0,1.04-.21,1.41-.59,.38-.38,.59-.88,.59-1.41s-.21-1.04-.59-1.41c-.38-.38-.88-.59-1.41-.59Zm18,0c-.4,0-.78,.12-1.11,.34-.33,.22-.59,.53-.74,.9-.15,.37-.19,.77-.11,1.16,.08,.39,.27,.74,.55,1.02,.28,.28,.64,.47,1.02,.55,.39,.08,.79,.04,1.16-.11,.37-.15,.68-.41,.9-.74,.22-.33,.34-.72,.34-1.11,0-.53-.21-1.04-.59-1.41s-.88-.59-1.41-.59Zm-6-12c-.4,0-.78,.12-1.11,.34-.33,.22-.59,.53-.74,.9-.15,.37-.19,.77-.11,1.16s.27,.74,.55,1.02c.28,.28,.64,.47,1.02,.55s.79,.04,1.16-.11c.37-.15,.68-.41,.9-.74,.22-.33,.34-.72,.34-1.11,0-.53-.21-1.04-.59-1.41s-.88-.59-1.41-.59Zm-12,0h-6c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41s.21,1.04,.59,1.41c.38,.38,.88,.59,1.41,.59h6c.53,0,1.04-.21,1.41-.59,.38-.38,.59-.88,.59-1.41s-.21-1.04-.59-1.41c-.38-.38-.88-.59-1.41-.59Zm18-12c-.4,0-.78,.12-1.11,.34-.33,.22-.59,.53-.74,.9-.15,.37-.19,.77-.11,1.16,.08,.39,.27,.74,.55,1.02,.28,.28,.64,.47,1.02,.55,.39,.08,.79,.04,1.16-.11,.37-.15,.68-.41,.9-.74,.22-.33,.34-.72,.34-1.11,0-.53-.21-1.04-.59-1.41-.38-.38-.88-.59-1.41-.59Zm0,12c-.4,0-.78,.12-1.11,.34-.33,.22-.59,.53-.74,.9-.15,.37-.19,.77-.11,1.16,.08,.39,.27,.74,.55,1.02,.28,.28,.64,.47,1.02,.55,.39,.08,.79,.04,1.16-.11,.37-.15,.68-.41,.9-.74,.22-.33,.34-.72,.34-1.11,0-.53-.21-1.04-.59-1.41s-.88-.59-1.41-.59Zm8-12c0-1.59-.63-3.12-1.76-4.24-1.13-1.13-2.65-1.76-4.24-1.76H10c-1.59,0-3.12,.63-4.24,1.76s-1.76,2.65-1.76,4.24v4c0,1.48,.56,2.9,1.56,4-1,1.1-1.55,2.52-1.56,4v4c0,1.48,.56,2.9,1.56,4-1,1.1-1.55,2.52-1.56,4v4c0,1.59,.63,3.12,1.76,4.24,1.13,1.13,2.65,1.76,4.24,1.76h28c1.59,0,3.12-.63,4.24-1.76,1.13-1.13,1.76-2.65,1.76-4.24v-4c0-1.48-.56-2.9-1.56-4,1-1.1,1.55-2.52,1.56-4v-4c0-1.48-.56-2.9-1.56-4,1-1.1,1.55-2.52,1.56-4v-4Zm-4,28c0,.53-.21,1.04-.59,1.41s-.88,.59-1.41,.59H10c-.53,0-1.04-.21-1.41-.59-.38-.38-.59-.88-.59-1.41v-4c0-.53,.21-1.04,.59-1.41,.38-.38,.88-.59,1.41-.59h28c.53,0,1.04,.21,1.41,.59s.59,.88,.59,1.41v4Zm0-12c0,.53-.21,1.04-.59,1.41s-.88,.59-1.41,.59H10c-.53,0-1.04-.21-1.41-.59-.38-.38-.59-.88-.59-1.41v-4c0-.53,.21-1.04,.59-1.41,.38-.38,.88-.59,1.41-.59h28c.53,0,1.04,.21,1.41,.59s.59,.88,.59,1.41v4Zm0-12c0,.53-.21,1.04-.59,1.41-.38,.38-.88,.59-1.41,.59H10c-.53,0-1.04-.21-1.41-.59-.38-.38-.59-.88-.59-1.41v-4c0-.53,.21-1.04,.59-1.41,.38-.38,.88-.59,1.41-.59h28c.53,0,1.04,.21,1.41,.59,.38,.38,.59,.88,.59,1.41v4Zm-10-4c-.4,0-.78,.12-1.11,.34-.33,.22-.59,.53-.74,.9-.15,.37-.19,.77-.11,1.16,.08,.39,.27,.74,.55,1.02,.28,.28,.64,.47,1.02,.55,.39,.08,.79,.04,1.16-.11,.37-.15,.68-.41,.9-.74,.22-.33,.34-.72,.34-1.11,0-.53-.21-1.04-.59-1.41-.38-.38-.88-.59-1.41-.59Zm-12,0h-6c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41s.21,1.04,.59,1.41c.38,.38,.88,.59,1.41,.59h6c.53,0,1.04-.21,1.41-.59,.38-.38,.59-.88,.59-1.41s-.21-1.04-.59-1.41c-.38-.38-.88-.59-1.41-.59Z`,
  ],
}

export const UpTime = {
  prefix: 'fal',
  iconName: 'up-time',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M25.98,9.12s.02-.07,.02-.12v-2h2c1.1,0,2-.89,2-2s-.9-2-2-2h-8c-1.1,0-2,.89-2,2s.9,2,2,2h2v2s.02,.07,.02,.12c-9,.99-16.02,8.63-16.02,17.88,0,9.93,8.08,18,18,18s18-8.07,18-18c0-9.26-7.03-16.9-16.02-17.88Zm-1.98,31.38c-7.44,0-13.5-6.06-13.5-13.5s6.06-13.5,13.5-13.5,13.5,6.06,13.5,13.5-6.06,13.5-13.5,13.5Zm6-15.5h-4v-4c0-1.11-.9-2-2-2s-2,.89-2,2v6c0,1.11,.9,2,2,2h6c1.1,0,2-.89,2-2s-.9-2-2-2Z`,
  ],
}

export const FileEdit = {
  prefix: 'fal',
  iconName: 'file-edit',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M14.58,27.41c.38,.38,.88,.59,1.41,.59h12c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41-.21-1.04-.59-1.41-.88-.59-1.41-.59H15.99c-.53,0-1.04,.21-1.41,.59s-.59,.88-.59,1.41,.21,1.04,.59,1.41Zm5.41,12.59H11.99c-.53,0-1.04-.21-1.41-.59s-.59-.88-.59-1.41V10c0-.53,.21-1.04,.59-1.41s.88-.59,1.41-.59h10v6c0,1.59,.63,3.12,1.76,4.24s2.65,1.76,4.24,1.76h6v2c0,.53,.21,1.04,.59,1.41s.88,.59,1.41,.59,1.04-.21,1.41-.59,.59-.88,.59-1.41v-4.12c-.02-.18-.06-.36-.12-.54v-.18c-.1-.21-.22-.39-.38-.56L25.49,4.6c-.17-.16-.35-.28-.56-.38-.06,0-.12,0-.18,0l-.64-.22H11.99c-1.59,0-3.12,.63-4.24,1.76s-1.76,2.65-1.76,4.24v28c0,1.59,.63,3.12,1.76,4.24,1.13,1.13,2.65,1.76,4.24,1.76h8c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41-.21-1.04-.59-1.41-.88-.59-1.41-.59Zm6-29.18l5.18,5.18h-3.18c-.53,0-1.04-.21-1.41-.59s-.59-.88-.59-1.41v-3.18Zm-10,9.18h2c.53,0,1.04-.21,1.41-.59,.38-.38,.59-.88,.59-1.41s-.21-1.04-.59-1.41c-.38-.38-.88-.59-1.41-.59h-2c-.53,0-1.04,.21-1.41,.59s-.59,.88-.59,1.41,.21,1.04,.59,1.41,.88,.59,1.41,.59Zm25.86,14.07c-.1-.24-.25-.46-.44-.65l-4.84-4.84c-.19-.19-.41-.34-.65-.44-.24-.1-.51-.15-.77-.15s-.53,.05-.77,.15c-.24,.1-.46,.25-.65,.44l-7.16,7.16c-.19,.19-.33,.41-.43,.65-.1,.24-.15,.5-.15,.77v4.84c0,.53,.21,1.04,.59,1.41s.88,.59,1.41,.59h4.84c.26,0,.52-.05,.77-.15,.24-.1,.47-.25,.65-.43l7.16-7.16c.19-.19,.34-.41,.44-.65,.1-.24,.15-.51,.15-.77s-.05-.53-.15-.77Zm-9.86,5.93h-2v-2l5.16-5.16,2,2-5.16,5.16Zm-12-8h-4c-.53,0-1.04,.21-1.41,.59s-.59,.88-.59,1.41,.21,1.04,.59,1.41,.88,.59,1.41,.59h4c.53,0,1.04-.21,1.41-.59s.59-.88,.59-1.41-.21-1.04-.59-1.41-.88-.59-1.41-.59Z`,
  ],
}

// service level icon
export const ReservedInstances = {
  prefix: 'fal',
  iconName: 'reserved-instances',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M23.73,16.9c-3.7,0-6.7,3-6.7,6.7s3,6.7,6.7,6.7,6.7-3,6.7-6.7-3-6.7-6.7-6.7Zm0,9.4c-1.49,0-2.7-1.21-2.7-2.7s1.21-2.7,2.7-2.7,2.7,1.21,2.7,2.7-1.21,2.7-2.7,2.7Zm17.05-4.63l-3.56-8.6,2.13-2.13-2.83-2.83-2.18,2.18-8.54-3.54V3.67h-4v3.14l-8.51,3.52-2.22-2.22-2.83,2.83,2.22,2.22-3.62,8.73v-.22H3.79v4h3.05v-.1l3.58,8.64-2.18,2.18,2.83,2.83,2.13-2.13,8.6,3.56v3.01h4v-2.96l8.63-3.58,2.09,2.09,2.83-2.83-2.09-2.09,3.58-8.63h2.96v-4h-3.01Zm-6.59,9.57l-2.34-2.34c-.74,1.13-1.7,2.09-2.83,2.83l2.34,2.34-7.51,3.11-7.6-3.15,2.25-2.25c-1.13-.73-2.11-1.68-2.85-2.8l-2.17,2.17-3.07-7.42,3.11-7.51,2.09,2.09c.74-1.13,1.7-2.09,2.83-2.83l-2.09-2.09,7.51-3.11,7.42,3.07-2.17,2.17c1.12,.75,2.08,1.72,2.8,2.85l2.25-2.25,3.15,7.6-3.11,7.51Z`,
  ],
}

export const EBS = {
  prefix: 'fal',
  iconName: 'ebs',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M34.41,4H13.77l-5.22,5.4h30.9l-5.04-5.4ZM9.99,44h28.07V13.27H9.99v30.73Zm3.97-26.76h20.14v22.79H13.96V17.24Z`,
  ],
}

export const Snapshots = {
  prefix: 'fal',
  iconName: 'snapshots',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M41.2,17c0-2.66-3.91-4.79-8.9-5.95l-.27-4.23-1.45-1.71h-12.62l-1.98,1.71-.27,4.23c-4.98,1.15-8.9,3.28-8.9,5.95,0,.41,.09,.81,.25,1.18l4.84,20.64c.39,3.54,7.71,4.08,12.12,4.08s11.73-.54,12.12-4.08l4.84-20.64c.16-.38,.25-.77,.25-1.18h-.03Zm-8.95,20.77c-.92,.46-3.84,1.13-8.26,1.13s-7.34-.68-8.26-1.13l-3.74-15.94c3.49,1.31,8.14,1.98,11.99,1.98s8.5-.68,12-1.98l-3.73,15.94Zm-8.26-18.08h0c-6.16,.01-11.01-1.7-12.71-2.77,1.69-1.12,6.47-2.81,12.71-2.81s10.98,1.68,12.69,2.79c-1.76,1.12-6.58,2.79-12.69,2.79Z`,
  ],
}

export const RDS = {
  prefix: 'fal',
  iconName: 'rds',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M42.83,13.04s-.02-.07-.03-.11c-.02-.08-.04-.16-.06-.23-.02-.04-.04-.08-.06-.12-.03-.07-.06-.14-.1-.2-.02-.04-.05-.07-.08-.11-.04-.06-.07-.12-.12-.17l-3.92-4.59c-.17-.22-.39-.4-.66-.53l-7.55-3.76s-.03,0-.05-.02c-.14-.06-.28-.12-.43-.15h0c-.13-.03-.27-.04-.41-.04h-10.74s-.05,0-.07,0c-.14,0-.27,.03-.4,.06-.04,.01-.08,.03-.13,.04-.12,.04-.24,.09-.34,.15-.02,.01-.05,.02-.07,.03l-8.85,5.37c-.17,.1-.32,.24-.45,.38,0,0-.01,0-.02,.01l-2.64,3.01c-.32,.36-.5,.83-.5,1.32v21.21c0,.48,.17,.94,.49,1.31l2.64,3.06s.03,.03,.05,.04c.04,.04,.08,.08,.13,.12,.08,.07,.16,.14,.24,.19,.02,.02,.04,.04,.06,.05l8.85,5.33s.06,.02,.1,.04c.1,.06,.21,.1,.32,.14,.05,.02,.1,.03,.14,.04,.15,.04,.3,.06,.46,.06h10.75s.02,0,.03,0c.13,0,.27-.02,.39-.05,.05-.01,.1-.03,.15-.05,.09-.03,.17-.05,.26-.09,.02,0,.04-.01,.07-.02l7.54-3.77c.06-.03,.12-.07,.17-.11,.05-.03,.1-.06,.15-.09,.11-.09,.22-.18,.31-.29h0l3.95-4.63c.07-.09,.14-.18,.19-.27h0c.05-.09,.09-.18,.12-.27,0-.02,.02-.04,.03-.06,.03-.09,.06-.18,.08-.28,0-.02,.01-.04,.02-.06,.02-.12,.03-.23,.03-.35h0V13.39c0-.12-.01-.24-.03-.36Zm-26.19,26.42l-4.85-2.92V11.5l4.85-2.94v30.91Zm10.73,1.54h-6.73V7h6.73V41Zm8.86-4.04l-.56,.66-4.29,2.14V8.23l4.27,2.13,.58,.67v25.92Z`,
  ],
}

export const ELB = {
  prefix: 'fal',
  iconName: 'elb',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M46,13.41h-9.9v-2.75l-3.02,4.4-1.38-1.93-14.03,9.35h11.83v-2.2l6.6,2.75v-3.85h9.9v9.62h-9.9v-3.85l-6.6,2.47v-2.2h-11.83l14.3,9.35,1.1-1.92,3.02,4.12v-2.2h9.9v9.9h-9.9v-4.4l-6.87-1.38,1.1-1.92L13,25.51v6.6H2V15.61H13v6.6L30.05,10.94l-1.1-1.65,7.15-1.65V3.51h9.9V13.41Z`,
  ],
}

export const Bucket = {
  prefix: 'fal',
  iconName: 'bucket',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24,5.69c-3.34,0-20,.3-20,6.14,0,.38,.07,.73,.2,1.06l5.85,24.98c.42,4,9.84,4.44,13.94,4.44s13.53-.44,13.95-4.44l5.85-24.98c.13-.33,.2-.68,.2-1.06,0-5.84-16.66-6.14-20-6.14Zm10.28,31.33c-.93,.59-4.53,1.52-10.28,1.52s-9.34-.93-10.27-1.52l-4.89-20.87c5.19,1.69,12.99,1.82,15.16,1.82s9.98-.13,15.17-1.82l-4.89,20.87ZM24,14.19c-6.13,0-10.63-.69-13.35-1.43-.12-.03-.23-.06-.34-.1-.09-.03-.18-.05-.27-.08-.84-.26-1.47-.52-1.88-.75,1.7-.97,7.14-2.37,15.83-2.37s14.12,1.39,15.83,2.37c-1.7,.97-7.14,2.36-15.83,2.36Z`,
  ],
}

export const Autoscale = {
  prefix: 'fal',
  iconName: 'autoscale',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M20.29,23.73L10.96,14.47v4.68H3v9.06h7.96v4.68l9.34-9.16Zm9.41,4.9l-4.64-4.95,4.64-4.83v-6.27h3.44L23.98,3.25,14.72,12.58h3.44v6.11l4.84,4.99-4.84,5.03v6.71h-3.44l9.17,9.34,9.26-9.34h-3.44v-6.79Zm7.35-9.48v-4.68l-9.34,9.16,9.34,9.26v-4.68h7.96v-9.06h-7.96Z`,
  ],
}

export const Cluster = {
  prefix: 'fal',
  iconName: 'cluster',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M42.06,20.96c1.07,0,1.94-.88,1.94-1.97v-5.61c0-1.09-.87-1.97-1.94-1.97h-5.51c-.48,0-.91,.18-1.25,.47l-3.04-1.91c.02-.12,.04-.25,.04-.38V3.97c0-1.09-.87-1.97-1.94-1.97h-5.51c-1.07,0-1.94,.88-1.94,1.97v5.61c0,.36,.09,.69,.25,.98l-5.64,6.53H5.94c-1.07,0-1.94,.88-1.94,1.97v12.16c0,1.09,.87,1.97,1.94,1.97h10.16l6.81,6.73v4.1c0,1.09,.87,1.97,1.94,1.97h5.51c1.07,0,1.94-.88,1.94-1.97v-5.27l3.16-2.5c.31,.22,.69,.34,1.09,.34h5.51c1.07,0,1.94-.88,1.94-1.97v-5.61c0-1.09-.87-1.97-1.94-1.97h-1.62v-6.08h1.62Zm-11.96-9.41l4.51,2.84v1.84l-3.76,1.42-3.72-6.1h2.97Zm4.27,7.44l-1.19,2.49-1.01-1.66,2.2-.83Zm-2.48,5.18l-2.17,4.55-9.9-2.07v-2.16l9.97-3.77,2.1,3.45Zm-7.11-11.67l3.69,6.05-8.65,3.27v-2.76c0-.24-.04-.47-.12-.68l5.08-5.88Zm1.27,23.95h-1.2c-.52,0-.99,.21-1.34,.55h0l-4.34-4.3c.4-.36,.66-.89,.66-1.48v-2.01l8.79,1.84-2.57,5.4Zm8.56-2.71l-3.58,2.83c-.21-.08-.43-.12-.67-.12h-1.54l2.32-4.87,3.47,.73v1.43Zm0-4.73v.74l-2.37-.5,1.21-2.53,1.19,1.95c-.02,.11-.03,.22-.03,.34Zm3.33-1.97h-1.37l-1.83-3.01,1.48-3.1c.11,.02,.22,.03,.33,.03h1.39v6.08Z`,
  ],
}

export const SQS = {
  prefix: 'fal',
  iconName: 'sqs',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M10.16,17.93c2.34-5.29,7.64-9,13.79-9s11.48,3.73,13.81,9.05c.99-.55,2.13-.87,3.35-.87,.21,0,.42,.01,.62,.03-2.76-7.13-9.68-12.21-17.78-12.21S8.93,10.01,6.17,17.14c.24-.03,.48-.04,.73-.04,1.18,0,2.29,.3,3.26,.82Zm27.6,12.1c-2.33,5.32-7.64,9.05-13.81,9.05s-11.45-3.71-13.79-9c-.97,.52-2.08,.82-3.26,.82-.25,0-.49-.01-.73-.04,2.76,7.14,9.68,12.22,17.78,12.22s15.01-5.08,17.78-12.21c-.2,.02-.41,.03-.62,.03-1.21,0-2.35-.32-3.35-.87Zm3.35-8.92c-.82,0-1.56,.35-2.09,.9h-8.08c.36-1.5,1.08-2.9,2.13-4.06,.33-.37,.52-.87,.52-1.36,0-.8-.48-1.52-1.21-1.84-.74-.31-1.59-.16-2.17,.39-1.73,1.65-4,2.56-6.39,2.56s-4.49-.84-6.2-2.38c-.78-.71-1.98-.68-2.74,.06-.75,.74-.81,1.94-.12,2.74,.97,1.13,1.64,2.47,1.99,3.9h-7.76c-.53-.55-1.27-.9-2.09-.9-1.6,0-2.9,1.3-2.9,2.9s1.3,2.9,2.9,2.9c.82,0,1.56-.35,2.09-.9h7.83c-.29,1.44-.91,2.79-1.86,3.97-.65,.8-.58,1.97,.16,2.69,.74,.72,1.91,.76,2.69,.09,1.67-1.42,3.8-2.21,6-2.21s4.49,.84,6.19,2.37c.38,.34,.86,.51,1.34,.51,.5,0,1.01-.19,1.39-.56,.75-.73,.81-1.92,.13-2.73-1.01-1.19-1.68-2.62-1.99-4.13h8.15c.53,.55,1.27,.9,2.09,.9,1.6,0,2.9-1.3,2.9-2.9s-1.3-2.9-2.9-2.9Zm-14.14,5.81c-1.02-.25-2.08-.38-3.16-.38s-2.09,.12-3.09,.37c.19-.9,.29-1.82,.29-2.75s-.1-1.91-.31-2.83c2.04,.49,4.25,.48,6.28-.01-.2,.93-.31,1.88-.31,2.84s.1,1.86,.29,2.77Z`,
  ],
}

export const Topics = {
  prefix: 'fal',
  iconName: 'topics',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M4,44.06H44v-11.04H4v11.04Zm4-7.04H40v3.04H8v-3.04ZM4,3.94V14.84H44V3.94H4Zm0,25.58H44v-11.04H4v11.04Zm4-7.04H40v3.04H8v-3.04Z`,
  ],
}

export const MessageStar = {
  prefix: 'fal',
  iconName: 'message-star',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M27.28,17.94l-.58,3.46c-.1,.58-.03,1.18,.2,1.72,.23,.54,.61,1.01,1.09,1.34,.45,.33,.99,.52,1.55,.56,.56,.03,1.12-.1,1.61-.38l2.84-1.62,2.88,1.58c.48,.27,1.03,.41,1.58,.38,.55-.02,1.09-.21,1.54-.52,.47-.34,.84-.8,1.06-1.33,.22-.53,.29-1.12,.2-1.69l-.58-3.46,2.4-2.44c.4-.4,.67-.91,.8-1.46,.13-.55,.1-1.12-.07-1.66-.18-.54-.5-1.01-.92-1.38-.43-.37-.95-.61-1.51-.7l-3.24-.48-1.46-3.1c-.24-.52-.63-.96-1.11-1.26-.48-.31-1.04-.47-1.61-.47s-1.13,.16-1.61,.47-.87,.75-1.11,1.26l-1.46,3.1-3.24,.48c-.56,.09-1.08,.33-1.51,.7-.43,.37-.75,.85-.92,1.38-.18,.54-.2,1.11-.07,1.66,.13,.55,.41,1.05,.8,1.46l2.44,2.4Zm3.66-4.26c.49-.07,.95-.27,1.35-.56,.4-.3,.72-.69,.93-1.14l.78-1.66,.78,1.66c.21,.45,.53,.84,.93,1.14,.4,.3,.86,.5,1.35,.58l2,.28-1.46,1.48c-.34,.35-.59,.78-.74,1.24-.15,.47-.18,.96-.1,1.44l.32,2-1.58-.86c-.44-.25-.94-.37-1.44-.37s-1,.13-1.44,.37l-1.58,.86,.32-2c.08-.48,.04-.97-.1-1.43-.15-.46-.4-.88-.74-1.23l-1.46-1.5,1.88-.3Zm11.06,15.3c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.41v6c0,.53-.21,1.04-.59,1.41-.38,.38-.88,.59-1.41,.59H10c-.53,0-1.04-.21-1.41-.59-.38-.38-.59-.88-.59-1.41V17.8l11.76,11.76c.55,.56,1.2,1,1.92,1.3,.72,.3,1.5,.46,2.28,.46h.54c.53-.05,1.02-.3,1.36-.7,.34-.4,.51-.93,.46-1.46-.02-.26-.1-.52-.22-.75-.12-.23-.29-.44-.49-.61-.2-.17-.44-.3-.69-.37-.25-.08-.52-.11-.78-.08-.28,.03-.57,0-.83-.1-.27-.1-.51-.25-.71-.46L10.82,14.98h7.18c.53,0,1.04-.21,1.41-.59,.38-.38,.59-.88,.59-1.41s-.21-1.04-.59-1.41-.88-.59-1.41-.59H10c-1.59,0-3.12,.63-4.24,1.76-1.13,1.13-1.76,2.65-1.76,4.24v20c0,1.59,.63,3.12,1.76,4.24s2.65,1.76,4.24,1.76h28c1.59,0,3.12-.63,4.24-1.76,1.13-1.13,1.76-2.65,1.76-4.24v-6c0-.53-.21-1.04-.59-1.41-.38-.38-.88-.59-1.41-.59Z`,
  ],
}

export const LambdaFunction = {
  prefix: 'fal',
  iconName: 'lambda-function',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M43.96,41.86c0,1.18-.96,2.14-2.06,2.14h-5.79c-.79,0-1.51-.43-1.89-1.12L19.38,15.25l-11.21,27.41c-.34,.84-1.14,1.34-1.98,1.34-.27,0-.55-.05-.81-.16-1.09-.45-1.62-1.7-1.17-2.8L16.75,10.38l-1.13-2.09H6.18c-1.18,0-2.06-.96-2.06-2.14s.88-2.14,2.06-2.14h10.63c.79,0,1.51,.43,1.89,1.12l18.76,34.59h4.44c1.18,0,2.06,.96,2.06,2.14Z`,
  ],
}

export const Process = {
  prefix: 'fal',
  iconName: 'process',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M19,24.4c-1.49,0-2.91,.59-3.96,1.64-1.05,1.05-1.64,2.47-1.64,3.96s.59,2.91,1.64,3.96c1.05,1.05,2.47,1.64,3.96,1.64s2.91-.59,3.96-1.64c1.05-1.05,1.64-2.47,1.64-3.96s-.59-2.91-1.64-3.96c-1.05-1.05-2.47-1.64-3.96-1.64Zm1.98,7.58c-.53,.53-1.24,.82-1.98,.82s-1.45-.29-1.98-.82c-.53-.53-.82-1.24-.82-1.98s.3-1.45,.82-1.98c.53-.53,1.24-.82,1.98-.82s1.45,.3,1.98,.82c.53,.52,.82,1.24,.82,1.98s-.3,1.45-.82,1.98Zm8.13-6.17c1.42-1.65,1.35-4.15-.21-5.71-1.57-1.57-4.06-1.64-5.71-.21-.16-2.17-1.97-3.89-4.19-3.89s-4.03,1.71-4.19,3.89c-1.65-1.42-4.14-1.35-5.71,.21-1.57,1.57-1.64,4.06-.21,5.71-2.17,.16-3.89,1.97-3.89,4.19s1.71,4.03,3.89,4.19c-1.42,1.65-1.35,4.14,.21,5.71,1.57,1.57,4.06,1.64,5.71,.21,.16,2.17,1.97,3.89,4.19,3.89s4.03-1.71,4.19-3.89c1.65,1.42,4.14,1.35,5.71-.21s1.64-4.06,.21-5.71c2.17-.16,3.89-1.97,3.89-4.19s-1.71-4.03-3.89-4.19Zm-.31,5.59c-2.37,0-3.56,2.86-1.88,4.54,.55,.55,.55,1.43,0,1.98-.55,.55-1.43,.55-1.98,0-1.68-1.68-4.54-.49-4.54,1.88,0,.77-.63,1.4-1.4,1.4s-1.4-.63-1.4-1.4c0-2.37-2.86-3.56-4.54-1.88-.55,.55-1.43,.55-1.98,0-.55-.55-.55-1.43,0-1.98,1.68-1.68,.49-4.54-1.88-4.54-.77,0-1.4-.63-1.4-1.4s.63-1.4,1.4-1.4c2.37,0,3.56-2.86,1.88-4.54-.55-.55-.55-1.43,0-1.98,.55-.55,1.43-.55,1.98,0,1.68,1.68,4.54,.49,4.54-1.88,0-.77,.63-1.4,1.4-1.4s1.4,.63,1.4,1.4c0,2.37,2.86,3.56,4.54,1.88,.55-.55,1.43-.55,1.98,0,.55,.55,.55,1.43,0,1.98-1.68,1.68-.49,4.54,1.88,4.54,.77,0,1.4,.63,1.4,1.4s-.63,1.4-1.4,1.4Zm14.2-19.58c0-1.24-.96-2.25-2.17-2.34,.8-.92,.76-2.32-.12-3.19-.88-.88-2.27-.91-3.19-.12-.09-1.21-1.1-2.17-2.34-2.17s-2.25,.96-2.34,2.17c-.92-.8-2.32-.76-3.19,.12-.88,.88-.91,2.27-.12,3.19-1.21,.09-2.17,1.1-2.17,2.34s.96,2.25,2.17,2.34c-.8,.92-.76,2.32,.12,3.19,.88,.88,2.27,.91,3.19,.12,.09,1.21,1.1,2.17,2.34,2.17s2.25-.96,2.34-2.17c.92,.8,2.32,.76,3.19-.12,.88-.88,.91-2.27,.12-3.19,1.21-.09,2.17-1.1,2.17-2.34Zm-2.35,.78c-1.32,0-1.99,1.6-1.05,2.54,.31,.31,.31,.8,0,1.11-.31,.31-.8,.31-1.11,0-.94-.94-2.54-.27-2.54,1.05,0,.43-.35,.78-.78,.78s-.78-.35-.78-.78c0-1.32-1.6-1.99-2.54-1.05-.31,.31-.8,.31-1.11,0-.31-.31-.31-.8,0-1.11,.94-.94,.27-2.54-1.05-2.54-.43,0-.78-.35-.78-.78s.35-.78,.78-.78c1.32,0,1.99-1.6,1.05-2.54-.31-.31-.31-.8,0-1.11,.31-.31,.8-.31,1.11,0,.94,.94,2.54,.27,2.54-1.05,0-.43,.35-.78,.78-.78s.78,.35,.78,.78c0,1.32,1.6,1.99,2.54,1.05,.31-.31,.8-.31,1.11,0,.31,.31,.31,.8,0,1.11-.94,.94-.27,2.54,1.05,2.54,.43,0,.78,.35,.78,.78s-.35,.78-.78,.78Zm-5.48-3.91c-.83,0-1.63,.33-2.21,.92-.59,.59-.92,1.38-.92,2.21s.33,1.63,.92,2.21c.59,.59,1.38,.92,2.21,.92s1.63-.33,2.21-.92c.59-.59,.92-1.38,.92-2.21s-.33-1.63-.92-2.21c-.59-.59-1.38-.92-2.21-.92Zm1.11,4.24c-.29,.29-.69,.46-1.11,.46s-.81-.16-1.11-.46c-.29-.29-.46-.69-.46-1.11s.17-.81,.46-1.11,.69-.46,1.11-.46,.81,.16,1.11,.46,.46,.69,.46,1.11-.16,.81-.46,1.11Z`,
  ],
}

export const RunningInstance = {
  prefix: 'fal',
  iconName: 'running-instance',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M32,16c-.68,0-1.34,.04-2,.12v-2.12c0-2.21-1.79-4-4-4h-6V4c0-2.21-1.79-4-4-4H4C1.79,0,0,1.79,0,4v12c0,2.21,1.79,4,4,4h6v6c0,2.21,1.79,4,4,4h2.12c-.08,.66-.12,1.32-.12,2,0,8.84,7.16,16,16,16s16-7.16,16-16-7.16-16-16-16Zm-22-2v2H4V4h12v6h-2c-2.21,0-4,1.79-4,4Zm4,12V14h12v3.16c-4.01,1.62-7.21,4.83-8.84,8.84h-3.16Zm18,18c-3.62,0-6.87-1.61-9.07-4.15-1.82-2.1-2.93-4.85-2.93-7.85,0-6.63,5.37-12,12-12,3,0,5.75,1.1,7.85,2.93,2.54,2.2,4.15,5.45,4.15,9.07,0,6.63-5.37,12-12,12Zm4.5-19.32l-7.84,10.32-1.17-2.32c-.73-.83-2-.9-2.82-.17s-.9,2-.17,2.82l2.67,4c.38,.43,.92,.67,1.49,.67s1.12-.24,1.5-.67l9.33-12c.73-.83,.66-2.09-.17-2.82s-2.09-.66-2.82,.17Z`,
  ],
}

export const NameSpaces = {
  prefix: 'fal',
  iconName: 'name-spaces',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M38.27,3.04H9.73c-3.71,0-6.73,3.02-6.73,6.73V29.99c0,3.71,3.02,6.73,6.73,6.73h9.3l7.89,7.67c.37,.36,.87,.56,1.37,.56,.26,0,.52-.05,.77-.16,.73-.31,1.2-1.03,1.2-1.81v-6.26h8c3.71,0,6.73-3.02,6.73-6.73V9.78c0-3.71-3.02-6.73-6.73-6.73Zm2.8,19.05H11.44l9.59,8.18v-4.12h20.03v3.82c0,1.54-1.25,2.79-2.79,2.79h-9.98c-1.08,0-1.97,.89-1.97,1.97v3.56l-5.12-4.98c-.36-.35-.86-.56-1.37-.56H9.73c-1.54,0-2.79-1.25-2.79-2.79v-11.86h28.69l-9.59-8.17v4.12H6.95v-4.31c0-1.54,1.25-2.79,2.79-2.79h28.54c1.54,0,2.79,1.25,2.79,2.79v12.34Z`,
  ],
}

export const OpenConnection = {
  prefix: 'fal',
  iconName: 'open-connection',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M46,12.01c0-6.63-5.37-12.01-12-12.01-5.22,0-9.67,3.34-11.32,8.01H14c-1.59,0-3.12,.63-4.24,1.76-1.13,1.13-1.76,2.65-1.76,4.25v12.01h-2c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.42s.21,1.04,.59,1.42c.38,.38,.88,.59,1.41,.59h14v6.37c-.84,.3-1.6,.78-2.23,1.41-.63,.63-1.11,1.39-1.41,2.23H4c-.53,0-1.04,.21-1.41,.59-.38,.38-.59,.88-.59,1.42s.21,1.04,.59,1.42c.38,.38,.88,.59,1.41,.59h12.36c.42,1.16,1.19,2.16,2.2,2.87,1.01,.71,2.21,1.09,3.44,1.09s2.43-.38,3.44-1.09c1.01-.71,1.78-1.71,2.2-2.87h12.36c.53,0,1.04-.21,1.41-.59,.38-.38,.59-.88,.59-1.42s-.21-1.04-.59-1.42c-.38-.38-.88-.59-1.41-.59h-12.36c-.3-.84-.78-1.6-1.41-2.23s-1.39-1.11-2.23-1.41v-6.37h14c.53,0,1.04-.21,1.41-.59,.38-.38,.59-.88,.59-1.42s-.21-1.04-.59-1.42c-.38-.38-.88-.59-1.41-.59h-2v-2.17c5.68-.95,10-5.89,10-11.85Zm-22.59,31.45c-.38,.38-.88,.59-1.41,.59-.4,0-.78-.12-1.11-.34-.33-.22-.59-.53-.74-.9-.15-.37-.19-.77-.11-1.16,.08-.39,.27-.74,.55-1.03,.28-.28,.64-.47,1.02-.55,.39-.08,.79-.04,1.16,.11s.68,.41,.9,.74c.22,.33,.34,.72,.34,1.11,0,.53-.21,1.04-.59,1.42Zm8.59-17.43H12V14.01c0-.53,.21-1.04,.59-1.42,.38-.38,.88-.59,1.41-.59h8c0,5.95,4.32,10.89,10,11.85v2.17Zm2-6.01c-4.42,0-8-3.59-8-8.01s3.58-8.01,8-8.01,8,3.59,8,8.01-3.58,8.01-8,8.01Zm5.28-12.56c-.85-.71-2.11-.59-2.82,.26l-3.6,4.32-1.45-1.45c-.78-.78-2.05-.78-2.83,0s-.78,2.05,0,2.83l3,3c.4,.4,.94,.61,1.5,.58s1.09-.29,1.45-.72l5-6c.71-.85,.59-2.11-.26-2.82Z`,
  ],
}

export const AzureVM = {
  prefix: 'fal',
  iconName: 'azure-vm',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M23.8,10.62c-.23-.13-.62-.22-.82-.11-2.15,1.2-4.28,2.45-6.53,3.74,2.22,1.3,4.29,2.52,6.38,3.71,.21,.12,.61,.14,.81,.02,2.08-1.22,4.13-2.46,6.35-3.79-2.19-1.27-4.18-2.44-6.2-3.57h0Zm-1.53,9.42c-.07-.11-.15-.21-.25-.27-2-1.19-4.02-2.37-6.27-3.67,0,2.26,.09,4.27-.03,6.26-.08,1.2,.32,1.83,1.37,2.37,1.77,.91,3.46,1.98,5.33,3.07,0-2.6,0-4.99-.01-7.39-.01-.12-.05-.25-.11-.36h-.02Zm8.68-4c-2.29,1.34-4.39,2.55-6.47,3.81-.18,.11-.3,.45-.3,.68-.02,2.34-.01,4.69-.01,7.28,1.76-1.02,3.3-2.06,4.95-2.84,1.46-.69,2.01-1.59,1.87-3.21-.16-1.82-.04-3.67-.04-5.73ZM43.21,1.97H4.08C1.84,1.97,.8,3.01,.8,5.23v27.91c0,2.27,1.01,3.28,3.26,3.28h2.19c3.34,0,6.68,0,10.02,.02,.3,0,.61,.1,.67,.15,.58,.93,.69,2.05,.28,3-.38,.89-1.16,1.51-2.15,1.7-2.08,.41-4.2,.51-5.81,.56-1.54,.04-1.55,1.11-1.55,1.66v.33c-.03,.49,.07,.87,.31,1.12,.33,.36,.81,.41,1.28,.41,9.08-.01,18.14-.01,27.21,0,.42,0,.93-.04,1.26-.42,.24-.27,.33-.65,.27-1.16-.01-.12,0-.24,0-.36,0-.36,.02-.82-.32-1.17-.25-.26-.62-.39-1.15-.39-2.06-.04-3.72-.19-5.23-.46-.85-.15-1.42-.46-1.75-.92-.34-.49-.42-1.22-.24-2.16,.03-.14,.07-.3,.11-.44,.07-.25,.13-.5,.16-.77,.05-.54,.23-.72,.95-.71,3.46,.03,6.91,.02,10.37,.02h2.34c2.37,0,3.51-1.16,3.51-3.54V5.57c0-2.45-1.14-3.6-3.57-3.6h-.01Zm-1.43,30.21H19.22c-4.54,0-9.08,0-13.61,.02-.47,0-.62-.05-.68-.11-.04-.04-.12-.19-.11-.65,.03-8.18,.03-16.47,0-24.65,0-.44,.07-.59,.12-.65,.04-.04,.18-.12,.6-.12,12.12,.02,24.24,.02,36.36,0,.58,0,.66,.02,.66,.69-.03,6.43-.02,12.87-.02,19.3v5.34h0c0,.36,0,.73,.02,.77-.09,.04-.45,.04-.79,.04Z`,
  ],
}

export const RunningVM = {
  prefix: 'fal',
  iconName: 'running-vm',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M35.92,1.94c-2.26,0-4.37,.64-6.18,1.72L24.94,.92c-.99-.57-2.11-.87-3.25-.87s-2.26,.3-3.25,.87L3.25,9.6c-.81,.47-1.51,1.11-2.05,1.87-.13,.13-.24,.27-.33,.43-.08,.14-.14,.28-.19,.43C.23,13.23,0,14.22,0,15.23v17.36c0,1.14,.3,2.27,.88,3.26s1.4,1.81,2.39,2.38l15.18,8.67c.68,.39,1.43,.66,2.21,.79,.32,.17,.67,.26,1.04,.26s.72-.09,1.03-.26c.78-.12,1.54-.39,2.23-.79l15.18-8.66c.99-.57,1.81-1.39,2.38-2.38s.87-2.11,.87-3.25v-9.09c2.81-2.21,4.62-5.64,4.62-9.48,0-6.66-5.42-12.08-12.08-12.08ZM19.52,42.53l-14.1-8.06c-.33-.19-.6-.47-.79-.79-.19-.33-.29-.7-.29-1.08V16.41l15.18,8.78v17.34Zm2.17-21.09L6.59,12.69,20.61,4.69c.33-.19,.7-.29,1.09-.29,.38,0,.76,.1,1.09,.29l3.54,2.02c-1.55,2.03-2.49,4.56-2.49,7.31,0,1.95,.47,3.79,1.3,5.42l-3.44,1.99Zm17.34,11.16c0,.38-.1,.75-.29,1.08-.19,.33-.46,.6-.79,.79l-14.09,8.05V25.19l3.92-2.27c2.15,1.97,5,3.18,8.14,3.18,1.08,0,2.12-.16,3.11-.42v6.91Zm-3.11-10.52c-4.44,0-8.05-3.61-8.05-8.05s3.61-8.05,8.05-8.05,8.05,3.61,8.05,8.05-3.61,8.05-8.05,8.05Zm2.49-11.12l-3.33,3.8-1.66-1.39c-.85-.71-2.1-.59-2.81,.26-.71,.85-.59,2.11,.26,2.81l3.16,2.63c.37,.31,.82,.46,1.28,.46,.56,0,1.11-.23,1.5-.68l4.62-5.27c.73-.83,.64-2.09-.19-2.82-.83-.72-2.09-.65-2.82,.19Z`,
  ],
}

export const StoppedVM = {
  prefix: 'fal',
  iconName: 'stopped-vm',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M35.81,1.86c-2.31,0-4.47,.66-6.31,1.78L24.87,.99c-.99-.57-2.1-.87-3.24-.87s-2.25,.3-3.24,.87L3.24,9.64c-.81,.47-1.51,1.1-2.04,1.86-.13,.13-.23,.27-.33,.43-.08,.14-.14,.28-.19,.43C.23,13.26,0,14.25,0,15.26v17.3c0,1.14,.3,2.26,.87,3.25s1.39,1.8,2.38,2.37l15.14,8.65c.68,.39,1.43,.66,2.2,.78,.31,.17,.67,.26,1.03,.26s.71-.09,1.03-.26c.78-.12,1.53-.39,2.22-.79l15.13-8.64c.99-.57,1.81-1.39,2.37-2.38,.57-.99,.87-2.11,.87-3.24v-8.88c2.88-2.23,4.75-5.72,4.75-9.64,0-6.72-5.47-12.19-12.19-12.19ZM19.46,42.47l-14.05-8.03c-.33-.19-.6-.46-.79-.79-.19-.33-.29-.7-.29-1.08V16.43l15.13,8.75v17.29Zm2.17-21.02L6.57,12.73l13.97-7.99c.33-.19,.7-.29,1.08-.29,.38,0,.76,.1,1.09,.29l3.4,1.95c-1.56,2.05-2.5,4.6-2.5,7.36,0,1.97,.48,3.83,1.31,5.48l-3.31,1.91Zm17.29,11.13c0,.38-.1,.75-.29,1.08-.19,.33-.46,.6-.79,.79l-14.05,8.03V25.19l3.77-2.18c2.17,2,5.07,3.24,8.25,3.24,1.07,0,2.11-.15,3.1-.42v6.74Zm-3.1-10.39c-4.48,0-8.13-3.65-8.13-8.13s3.65-8.13,8.13-8.13,8.13,3.65,8.13,8.13-3.65,8.13-8.13,8.13Zm4.75-12.88c-.76-.76-2-.76-2.77,0l-1.99,1.99-1.99-1.99c-.76-.76-2-.76-2.77,0-.76,.76-.76,2,0,2.77l1.99,1.99-1.99,1.99c-.76,.76-.76,2,0,2.77,.38,.38,.88,.57,1.38,.57s1-.19,1.38-.57l1.99-1.99,1.99,1.99c.38,.38,.88,.57,1.38,.57s1-.19,1.38-.57c.76-.76,.76-2,0-2.77l-1.99-1.99,1.99-1.99c.76-.76,.76-2,0-2.77Z`,
  ],
}

export const VMScaleset = {
  prefix: 'fal',
  iconName: 'vm-scaleset',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M31.59,28.12v6.45c0,.06,.03,.12,.09,.15,.02,.02,.05,.03,.08,.02,.03,0,.06,0,.09-.01l5.53-3.22c.05-.03,.09-.09,.09-.15v-6.45c0-.07-.03-.13-.09-.16-.06-.02-.12-.02-.17,0l-5.53,3.22c-.06,.03-.09,.09-.1,.15Zm-1.94,6.62s.06,.03,.09,.02c.03,0,.06,0,.08-.01,.06-.03,.09-.1,.09-.16v-6.51c0-.06-.04-.12-.09-.15l-5.68-3.23c-.06-.03-.13-.03-.19,0-.05,.03-.08,.09-.08,.15v6.51c0,.06,.03,.11,.08,.15l5.69,3.23Zm-4.73-11.46l5.83,3.34s.06,.02,.09,.02c.03,0,.06,0,.08-.02l5.66-3.22c.06-.03,.1-.09,.09-.15,0-.06-.04-.12-.09-.15l-5.82-3.33c-.05-.03-.12-.03-.17,0l-5.66,3.21c-.05,.03-.08,.09-.08,.15,0,.06,.02,.12,.08,.15Zm19.71-8.44h-2.51v-4.13c0-1.23-1.18-2.35-2.48-2.35h-2.72V4.24c0-.57-.23-1.12-.68-1.57-.48-.49-1.16-.78-1.8-.78H3.15C1.91,1.89,.87,2.96,.87,4.24V24.51c0,1.27,1.04,2.34,2.28,2.34h3.92v4.13c0,1.27,1.04,2.34,2.28,2.34h3.71v4.14c0,1.27,1.04,2.34,2.27,2.34h10.29c.3,1.18,.28,1.91-.05,2.35-.65,.86-2.8,1.03-6.31,1.03h-.48v2.95h22.59v-2.95h-.48c-3.17,0-5.12-.14-5.64-.84-.34-.45-.21-1.34,.12-2.54h9.26c1.3,0,2.48-1.11,2.48-2.34V17.19c0-1.21-1.2-2.35-2.48-2.35ZM7.06,10.71v12.65h-2.73V5.38l29.1-.03v3.02h-9.48l-3.48-1.99c-.17-.07-.37-.07-.6,.03l-3.43,1.96h-7.11c-1.24,0-2.28,1.08-2.28,2.35Zm5.99,6.48v12.64h-2.52V11.84l28.09-.03v3.03h-9.43l-3.42-1.96c-.21-.14-.49-.14-.68-.02l-3.46,1.98h-6.3c-1.23,0-2.27,1.08-2.27,2.35Zm30.83,19.12H16.78V18.34l27.1-.05v18.02Z`,
  ],
}

export const Pages = {
  prefix: 'fal',
  iconName: 'pages',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M40,17.88c-.02-.18-.06-.36-.12-.54v-.18c-.1-.21-.22-.39-.38-.56L27.5,4.6c-.17-.16-.35-.28-.56-.38h-.18l-.64-.22H14c-1.59,0-3.12,.63-4.24,1.76-1.13,1.13-1.76,2.65-1.76,4.24v28c0,1.59,.63,3.12,1.76,4.24,1.13,1.13,2.65,1.76,4.24,1.76h20c1.59,0,3.12-.63,4.24-1.76s1.76-2.65,1.76-4.24V17.88Zm-12-7.06l5.18,5.18h-5.18v-5.18Zm8,27.18c0,.53-.21,1.04-.59,1.41-.38,.37-.88,.59-1.41,.59H14c-.53,0-1.04-.21-1.41-.59-.38-.38-.59-.88-.59-1.41V10c0-.53,.21-1.04,.59-1.41,.38-.38,.88-.59,1.41-.59h10v10c0,.53,.21,1.04,.59,1.41s.88,.59,1.41,.59h10v18Z`,
  ],
}

export const BusyServer = {
  prefix: 'fal',
  iconName: 'busy-server',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M10.16,11c.27,.27,.61,.45,.98,.52s.76,.04,1.11-.11c.35-.14,.65-.39,.86-.7,.21-.31,.32-.68,.32-1.06,0-.51-.2-.99-.56-1.35s-.85-.56-1.36-.56c-.38,0-.75,.11-1.07,.32-.32,.21-.56,.51-.71,.86-.15,.35-.18,.73-.11,1.11s.26,.71,.53,.98Zm22.49,19.69c-2.12,0-3.84,1.71-3.84,3.83s1.72,3.83,3.84,3.83,3.84-1.71,3.84-3.83-1.72-3.83-3.84-3.83ZM11.52,19.22c-.38,0-.75,.11-1.07,.32-.32,.21-.56,.51-.71,.86-.15,.35-.18,.73-.11,1.11,.07,.37,.26,.71,.53,.98,.27,.27,.61,.45,.98,.52,.37,.07,.76,.04,1.11-.11,.35-.14,.65-.39,.86-.7,.21-.31,.32-.68,.32-1.06,0-.51-.2-.99-.56-1.35s-.85-.56-1.36-.56Zm-1.36,14.74c.27,.27,.61,.45,.98,.52,.37,.07,.76,.04,1.11-.11s.65-.39,.86-.7c.21-.31,.32-.68,.32-1.06,0-.51-.2-.99-.56-1.35-.36-.36-.85-.56-1.36-.56-.38,0-.75,.11-1.07,.32-.32,.21-.56,.51-.71,.86-.15,.35-.18,.73-.11,1.11,.07,.37,.26,.71,.53,.98Zm24.4-10.76c0-.05,0-.11,0-.16v-3.83c0-1.42-.54-2.78-1.5-3.83,.96-1.05,1.49-2.41,1.5-3.83v-3.83c0-1.52-.61-2.98-1.69-4.06s-2.55-1.68-4.07-1.68H9.6c-1.53,0-2.99,.6-4.07,1.68s-1.69,2.54-1.69,4.06v3.83c0,1.42,.54,2.78,1.5,3.83-.96,1.05-1.49,2.41-1.5,3.83v3.83c0,1.42,.54,2.78,1.5,3.83-.96,1.05-1.49,2.41-1.5,3.83v3.83c0,1.52,.61,2.98,1.69,4.06s2.55,1.68,4.07,1.68h13.07c1.99,3.43,5.72,5.74,9.98,5.74,6.36,0,11.52-5.14,11.52-11.48,0-5.69-4.15-10.41-9.61-11.32ZM7.67,7.74c0-.51,.2-.99,.56-1.35,.36-.36,.85-.56,1.36-.56H28.8c.51,0,1,.2,1.36,.56,.36,.36,.56,.85,.56,1.35v3.83c0,.51-.2,.99-.56,1.35-.36,.36-.85,.56-1.36,.56H9.6c-.51,0-1-.2-1.36-.56-.36-.36-.56-.85-.56-1.35v-3.83Zm13.44,26.78c0,.65,.05,1.29,.16,1.91H9.6c-.51,0-1-.2-1.36-.56-.36-.36-.56-.85-.56-1.35v-3.83c0-.51,.2-.99,.56-1.35,.36-.36,.85-.56,1.36-.56h13.07c-.98,1.69-1.54,3.65-1.54,5.74Zm-11.52-9.57c-.51,0-1-.2-1.36-.56-.36-.36-.56-.85-.56-1.35v-3.83c0-.51,.2-.99,.56-1.35,.36-.36,.85-.56,1.36-.56H28.8c.51,0,1,.2,1.36,.56,.36,.36,.56,.85,.56,1.35v3.83c0,.05,0,.11,0,.16-1.62,.27-3.13,.88-4.44,1.75H9.6Zm23.05,17.22c-4.24,0-7.68-3.43-7.68-7.65s3.44-7.65,7.68-7.65,7.68,3.43,7.68,7.65-3.44,7.65-7.68,7.65Z`,
  ],
}

export const OutgoingSession = {
  prefix: 'fal',
  iconName: 'outgoing-session',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M15.79,28.74c0-1.03-.82-1.85-1.85-1.85H4.35c-1.03,0-1.85,.82-1.85,1.85s.82,1.85,1.85,1.85H13.94c1.03,0,1.85-.82,1.85-1.85Zm-.26-5.79c0-1.03-.82-1.85-1.85-1.85H1.85C.82,21.09,0,21.93,0,22.95s.82,1.85,1.85,1.85H13.66c1.03,0,1.87-.82,1.87-1.85Zm16.63,1.38c.77-.67,.84-1.85,.17-2.62-.67-.77-1.85-.84-2.62-.17l-5.66,4.97-2.07-2.13c-.73-.73-1.89-.75-2.62-.04-.73,.71-.75,1.89-.04,2.62l4.54,4.63,8.29-7.27Zm15.71-16.01c-.19-.51-.57-.92-1.07-1.15-.24-.11-.51-.18-.77-.19l-8.21-.31c-.54-.02-1.08,.18-1.47,.55-.4,.37-.64,.88-.66,1.43-.02,.54,.18,1.08,.55,1.47,.37,.4,.88,.64,1.43,.66l3.24,.12-4.97,4.52c-2.52-2.38-5.79-3.97-9.42-4.38v-3.14h1.4c1.03,0,1.85-.82,1.85-1.85s-.82-1.87-1.85-1.87h-6.5c-1.03,0-1.85,.82-1.85,1.85s.82,1.85,1.85,1.85h1.4v3.16c-3.4,.39-6.61,1.81-9.17,4.13-.77,.69-.82,1.87-.13,2.62,.69,.77,1.87,.82,2.62,.13,2.34-2.11,5.36-3.27,8.52-3.27,7.02,0,12.74,5.72,12.74,12.74s-5.72,12.74-12.74,12.74c-4.56,0-8.8-2.47-11.08-6.44-.49-.9-1.63-1.21-2.52-.69-.9,.5-1.2,1.64-.69,2.52,2.93,5.12,8.41,8.31,14.29,8.31,9.08,0,16.46-7.38,16.46-16.44,0-3.24-.94-6.26-2.56-8.8l5.16-4.65-.12,3.24c-.02,.54,.18,1.08,.55,1.47,.37,.4,.88,.64,1.43,.66,.54,.02,1.08-.18,1.47-.55,.4-.37,.64-.88,.66-1.43l.31-8.21c0-.27-.04-.54-.13-.79Z`,
  ],
}

export const Email = {
  prefix: 'fal',
  iconName: 'email',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M40.2,5.78H7.8c-3.35,0-6.07,2.72-6.07,6.07v24.3c0,3.35,2.72,6.07,6.07,6.07H40.2c3.35,0,6.07-2.72,6.07-6.07V11.85c0-3.35-2.72-6.07-6.07-6.07ZM7.8,9.83H40.2c.79,0,1.46,.47,1.79,1.13l-17.99,12.6L6.01,10.96c.33-.66,1-1.13,1.79-1.13Zm34.42,26.32c0,1.12-.91,2.02-2.02,2.02H7.8c-1.12,0-2.02-.91-2.02-2.02V15.74l17.06,11.94c.35,.24,.75,.37,1.16,.37s.81-.12,1.16-.37l17.06-11.94v20.41Z`,
  ],
}

export const SevereInstance = {
  prefix: 'fal',
  iconName: 'severe-instance',
  icon: [
    48,
    48,
    [],
    'f0000',
    `m32,16c-.68,0-1.34.04-2,.12v-2.12c0-2.21-1.79-4-4-4h-6v-6c0-2.21-1.79-4-4-4H4C1.79,0,0,1.79,0,4v12c0,2.21,1.79,4,4,4h6v6c0,2.21,1.79,4,4,4h2.12c-.08.66-.12,1.32-.12,2,0,8.84,7.16,16,16,16s16-7.16,16-16-7.16-16-16-16Zm-22-2v2h-6V4h12v6h-2c-2.21,0-4,1.79-4,4Zm4,12v-12h12v3.16c-4.01,1.62-7.21,4.83-8.84,8.84h-3.16Zm18,18c-3.62,0-6.87-1.61-9.07-4.15-1.82-2.1-2.93-4.85-2.93-7.85,0-6.63,5.37-12,12-12,3,0,5.75,1.1,7.85,2.93,2.54,2.2,4.15,5.45,4.15,9.07,0,6.63-5.37,12-12,12Zm0-22c-1.1,0-2,.9-2,2v10c0,1.1.9,2,2,2s2-.9,2-2v-10c0-1.1-.9-2-2-2Zm0,16c-1.1,0-2,.9-2,2s.9,2,2,2,2-.9,2-2-.9-2-2-2Z`,
  ],
}

export const MacOs = {
  prefix: 'fal',
  iconName: 'mac-os',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M35.7014 25.2507C35.7576 31.304 41.0117 33.3184 41.07 33.3441C41.0255 33.4862 40.2304 36.2148 38.3018 39.0332C36.6347 41.4699 34.9043 43.8977 32.1786 43.948C29.5002 43.9974 28.639 42.3598 25.5769 42.3598C22.5158 42.3598 21.5589 43.8977 19.0235 43.9974C16.3925 44.0969 14.389 41.3624 12.7079 38.9346C9.27294 33.9685 6.64789 24.9016 10.1727 18.7812C11.9237 15.7418 15.0529 13.8172 18.4494 13.7678C21.0331 13.7185 23.4717 15.506 25.0511 15.506C26.6295 15.506 29.593 13.3564 32.7083 13.6721C34.0125 13.7264 37.6735 14.1989 40.0242 17.6399C39.8348 17.7573 35.656 20.19 35.7014 25.2507M30.6678 10.3866C32.0647 8.69578 33.0048 6.342 32.7483 4C30.7349 4.08092 28.3002 5.3417 26.856 7.03159C25.5617 8.52807 24.4282 10.9233 24.734 13.2189C26.9783 13.3926 29.2709 12.0785 30.6678 10.3866`,
  ],
}

export const Cisco = {
  prefix: 'fal',
  iconName: 'cisco',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M12.672 40.274H14.777V30.826H12.672V40.274ZM2.00107 35.5491C1.98936 36.2079 2.11289 36.8621 2.36404 37.4713C2.61519 38.0805 2.98863 38.6317 3.46124 39.0909C3.93385 39.55 4.49561 39.9074 5.1118 40.1409C5.72799 40.3743 6.38549 40.4789 7.04371 40.4482C7.74515 40.4429 8.44254 40.3411 9.11628 40.1459V37.945C8.52659 38.2128 7.88657 38.3518 7.23892 38.3527H7.22169C6.84404 38.3666 6.46745 38.3043 6.11444 38.1694C5.76143 38.0345 5.43924 37.8298 5.16713 37.5675C4.89503 37.3053 4.67859 36.9909 4.53075 36.6431C4.38292 36.2953 4.30672 35.9213 4.30672 35.5434C4.30672 35.1655 4.38292 34.7914 4.53075 34.4436C4.67859 34.0958 4.89503 33.7814 5.16713 33.5191C5.43924 33.2569 5.76143 33.0522 6.11444 32.9173C6.46745 32.7824 6.84404 32.7201 7.22169 32.734C7.87488 32.7349 8.52051 32.8738 9.11628 33.1416V30.9466C8.44361 30.7462 7.74558 30.6438 7.04371 30.6423C6.38465 30.6101 5.72604 30.7139 5.10882 30.9472C4.4916 31.1805 3.929 31.5383 3.45601 31.9984C2.98302 32.4584 2.60976 33.0109 2.35945 33.6215C2.10915 34.232 1.98716 34.8875 2.00107 35.5472V35.5491ZM27.5052 21.0526C27.6426 21.1968 27.8316 21.2806 28.0307 21.2857C28.2298 21.2907 28.4228 21.2165 28.5673 21.0795C29.1753 20.5011 29.6607 19.8064 29.9948 19.0367C30.3289 18.2669 30.5048 17.4379 30.5122 16.5988C30.5195 15.7598 30.358 14.9278 30.0374 14.1523C29.7168 13.3769 29.2435 12.6738 28.6458 12.085C28.5017 11.9572 28.314 11.8896 28.1215 11.896C27.929 11.9025 27.7463 11.9825 27.6111 12.1197C27.4759 12.2569 27.3985 12.4407 27.3948 12.6333C27.3911 12.8259 27.4614 13.0125 27.5913 13.1548C28.0454 13.6023 28.4049 14.1365 28.6484 14.7257C28.8919 15.3149 29.0145 15.9471 29.0088 16.5847C29.0031 17.2222 28.8692 17.8521 28.6152 18.4369C28.3612 19.0216 27.9921 19.5493 27.5301 19.9886C27.3861 20.1263 27.3027 20.3154 27.298 20.5145C27.2933 20.7136 27.3679 20.9065 27.5052 21.0507V21.0526ZM19.9403 21.0795C20.0865 21.206 20.2758 21.2714 20.469 21.262C20.6621 21.2526 20.8442 21.1691 20.9774 21.029C21.1107 20.8889 21.1849 20.7028 21.1845 20.5094C21.1842 20.316 21.1093 20.1302 20.9756 19.9905C20.5157 19.5533 20.1479 19.0284 19.894 18.4468C19.6401 17.8652 19.5052 17.2388 19.4972 16.6042C19.4891 15.9697 19.6082 15.3399 19.8473 14.7521C20.0864 14.1643 20.4408 13.6303 20.8895 13.1815C20.9654 13.1132 21.0267 13.0302 21.0695 12.9375C21.1123 12.8448 21.1358 12.7443 21.1385 12.6422C21.1412 12.5401 21.1231 12.4385 21.0853 12.3437C21.0475 12.2488 20.9907 12.1626 20.9185 12.0904C20.8463 12.0182 20.7601 11.9614 20.6652 11.9236C20.5704 11.8858 20.4688 11.8677 20.3667 11.8704C20.2646 11.8731 20.1641 11.8966 20.0714 11.9394C19.9787 11.9822 19.8956 12.0435 19.8274 12.1194C19.2369 12.7099 18.7705 13.4126 18.4558 14.1861C18.1412 14.9596 17.9844 15.7883 17.995 16.6233C18.0055 17.4583 18.183 18.2827 18.5171 19.048C18.8512 19.8133 19.3351 20.504 19.9403 21.0795V21.0795ZM33.0722 16.5439C33.0765 17.7338 32.8378 18.9121 32.3709 20.0065C31.904 21.101 31.2186 22.0886 30.3566 22.909C30.2798 22.9757 30.2172 23.0574 30.1729 23.1491C30.1285 23.2408 30.1032 23.3405 30.0985 23.4423C30.0939 23.544 30.1099 23.6456 30.1457 23.741C30.1814 23.8363 30.2362 23.9234 30.3066 23.997C30.3771 24.0705 30.4617 24.129 30.5554 24.1689C30.6492 24.2087 30.75 24.2291 30.8518 24.2288C30.9537 24.2286 31.0544 24.2077 31.1479 24.1673C31.2414 24.1269 31.3257 24.068 31.3958 23.9941C32.3872 23.0434 33.1789 21.9044 33.7245 20.6439C34.27 19.3833 34.5585 18.0265 34.5729 16.653C34.5874 15.2796 34.3275 13.9171 33.8086 12.6453C33.2896 11.3736 32.522 10.2182 31.5508 9.2469C31.4076 9.11819 31.2205 9.04929 31.028 9.05441C30.8355 9.05953 30.6523 9.13832 30.5162 9.27447C30.38 9.41062 30.3013 9.59377 30.2962 9.78625C30.2911 9.97873 30.36 10.1659 30.4887 10.3091C31.3101 11.1259 31.9615 12.0975 32.4049 13.1677C32.8484 14.2379 33.0752 15.3855 33.0722 16.5439V16.5439ZM11.34 16.5439C11.3346 14.8183 11.6776 13.1093 12.3484 11.5194C13.0193 9.92946 14.0041 8.49135 15.244 7.29114C15.3802 7.15063 15.4563 6.96254 15.4561 6.76686C15.4559 6.57118 15.3794 6.38327 15.2429 6.24305C15.1064 6.10283 14.9207 6.02136 14.7251 6.01587C14.5295 6.01038 14.3394 6.08133 14.1953 6.21368C12.8126 7.55991 11.7143 9.17009 10.9655 10.9487C10.2166 12.7273 9.83255 14.6381 9.83594 16.5679C9.83933 18.4977 10.2301 20.4072 10.9852 22.1831C11.7403 23.9591 12.8442 25.5654 14.2316 26.9068C14.3012 26.9813 14.3852 27.0409 14.4786 27.082C14.5719 27.123 14.6726 27.1447 14.7746 27.1456C14.8766 27.1465 14.9776 27.1267 15.0717 27.0874C15.1658 27.048 15.2509 26.9899 15.3218 26.9167C15.3927 26.8434 15.448 26.7565 15.4843 26.6612C15.5206 26.5659 15.5371 26.4642 15.5329 26.3623C15.5287 26.2604 15.5037 26.1605 15.4597 26.0685C15.4156 25.9765 15.3533 25.8945 15.2765 25.8274C14.0264 24.6268 13.0329 23.1851 12.3563 21.5894C11.6796 19.9936 11.3339 18.2772 11.34 16.5439V16.5439ZM34.3047 6.20988C34.1605 6.07753 33.9705 6.00653 33.7749 6.01201C33.5793 6.0175 33.3935 6.09904 33.257 6.23926C33.1205 6.37948 33.0441 6.56739 33.0439 6.76307C33.0437 6.95874 33.1197 7.14678 33.2559 7.28728C34.4945 8.49322 35.4783 9.93558 36.149 11.5288C36.8198 13.122 37.1639 14.8336 37.1608 16.5623C37.1578 18.2909 36.8077 20.0014 36.1314 21.5923C35.455 23.1831 34.4662 24.622 33.2234 25.8236C33.1466 25.8907 33.0843 25.9727 33.0402 26.0647C32.9962 26.1566 32.9713 26.2566 32.967 26.3585C32.9628 26.4604 32.9793 26.5621 33.0156 26.6574C33.0519 26.7527 33.1072 26.8396 33.1781 26.9128C33.249 26.9861 33.3341 27.0442 33.4282 27.0835C33.5223 27.1229 33.6234 27.1427 33.7253 27.1418C33.8273 27.1408 33.928 27.1192 34.0213 27.0781C34.1147 27.037 34.1987 26.9774 34.2683 26.9029C35.6557 25.5615 36.7596 23.9552 37.5147 22.1793C38.2698 20.4033 38.6606 18.4939 38.664 16.5641C38.6674 14.6343 38.2833 12.7234 37.5345 10.9448C36.7856 9.16623 35.6873 7.55612 34.3047 6.20988V6.20988ZM18.1433 22.9032C17.2984 22.093 16.6233 21.1227 16.1573 20.0488C15.6914 18.975 15.4439 17.8191 15.4293 16.6486C15.4147 15.4781 15.6333 14.3164 16.0723 13.2313C16.5113 12.1461 17.162 11.1592 17.9864 10.3282C18.0621 10.2596 18.123 10.1764 18.1655 10.0835C18.2079 9.99064 18.2311 9.8901 18.2334 9.788C18.2357 9.6859 18.2173 9.58435 18.1791 9.48962C18.1409 9.3949 18.0839 9.30899 18.0114 9.23703C17.9389 9.16508 17.8525 9.1086 17.7575 9.07111C17.6625 9.03363 17.5609 9.01588 17.4588 9.01896C17.3568 9.02204 17.2564 9.04591 17.1638 9.08904C17.0712 9.13218 16.9884 9.19373 16.9204 9.26992C15.9559 10.2431 15.1948 11.3985 14.6816 12.6689C14.1683 13.9393 13.9132 15.2992 13.931 16.6692C13.9488 18.0393 14.2392 19.392 14.7853 20.6486C15.3315 21.9052 16.1223 23.0405 17.1118 23.9883C17.1818 24.0622 17.2662 24.1212 17.3597 24.1616C17.4532 24.2019 17.5539 24.2228 17.6557 24.2231C17.7576 24.2234 17.8584 24.203 17.9521 24.1631C18.0458 24.1233 18.1305 24.0648 18.2009 23.9912C18.2714 23.9177 18.3261 23.8306 18.3619 23.7352C18.3977 23.6399 18.4137 23.5382 18.409 23.4365C18.4044 23.3348 18.3791 23.2351 18.3347 23.1434C18.2904 23.0517 18.2278 22.97 18.1509 22.9032H18.1433ZM26.8794 35.5434C26.8682 36.2025 26.9923 36.857 27.244 37.4663C27.4957 38.0756 27.8696 38.627 28.3428 39.0861C28.8159 39.5452 29.3781 39.9025 29.9947 40.1358C30.6113 40.369 31.2693 40.4734 31.9278 40.4425C32.6286 40.4363 33.3252 40.3345 33.9984 40.1401V37.945C33.403 38.2179 32.755 38.3571 32.1 38.3527C31.7224 38.3666 31.3458 38.3043 30.9928 38.1694C30.6398 38.0345 30.3176 37.8298 30.0455 37.5675C29.7734 37.3053 29.5569 36.9909 29.4091 36.6431C29.2613 36.2953 29.1851 35.9213 29.1851 35.5434C29.1851 35.1655 29.2613 34.7914 29.4091 34.4436C29.5569 34.0958 29.7734 33.7814 30.0455 33.5191C30.3176 33.2569 30.6398 33.0522 30.9928 32.9173C31.3458 32.7824 31.7224 32.7201 32.1 32.734C32.7546 32.7357 33.4014 32.8752 33.9984 33.1435V30.9485C33.3256 30.7488 32.6277 30.6463 31.9259 30.6442C31.2674 30.6129 30.6094 30.7172 29.9929 30.9507C29.3764 31.1843 28.8145 31.542 28.342 32.0017C27.8695 32.4615 27.4966 33.0134 27.2463 33.6233C26.996 34.2332 26.8737 34.888 26.8871 35.5472L26.8794 35.5434ZM41.349 30.6461C40.3848 30.6668 39.448 30.9718 38.6563 31.5226C37.8646 32.0735 37.2531 32.8457 36.8984 33.7427C36.5437 34.6396 36.4617 35.6213 36.6626 36.5646C36.8635 37.508 37.3384 38.371 38.0278 39.0456C38.7171 39.7202 39.5903 40.1763 40.5378 40.3567C41.4852 40.5372 42.4649 40.4339 43.3539 40.0599C44.243 39.686 45.0018 39.0579 45.5354 38.2544C46.069 37.4509 46.3536 36.5079 46.3534 35.5434C46.3609 34.8907 46.2364 34.2433 45.9872 33.64C45.7381 33.0367 45.3694 32.4901 44.9035 32.0329C44.4377 31.5758 43.8841 31.2176 43.2762 30.9799C42.6683 30.7423 42.0186 30.6301 41.3662 30.65L41.349 30.6461ZM41.3337 38.3182H41.3165C40.5805 38.3162 39.8756 38.0219 39.3566 37.5001C38.8376 36.9782 38.5472 36.2716 38.5493 35.5357C38.5513 34.7997 38.8456 34.0948 39.3674 33.5758C39.8893 33.0568 40.5959 32.7664 41.3318 32.7684H41.4275C41.7856 32.7747 42.139 32.8514 42.4675 32.9942C42.796 33.1371 43.0931 33.3432 43.3419 33.6009C43.5907 33.8586 43.7863 34.1628 43.9175 34.496C44.0488 34.8293 44.113 35.1852 44.1067 35.5434C44.1082 36.279 43.818 36.9852 43.2996 37.5072C42.7812 38.0292 42.077 38.3243 41.3414 38.3278L41.3337 38.3182ZM24.2461 15.2254C23.9865 15.2254 23.7327 15.3024 23.5168 15.4466C23.3009 15.5909 23.1326 15.7959 23.0333 16.0358C22.9339 16.2757 22.9079 16.5397 22.9585 16.7943C23.0092 17.049 23.1342 17.2829 23.3178 17.4665C23.5014 17.6501 23.7354 17.7751 23.99 17.8258C24.2447 17.8764 24.5086 17.8504 24.7485 17.7511C24.9884 17.6517 25.1934 17.4834 25.3377 17.2676C25.4819 17.0517 25.5589 16.7979 25.5589 16.5382C25.5574 16.1922 25.4196 15.8606 25.1752 15.6156C24.9309 15.3705 24.5998 15.2317 24.2538 15.2292L24.2461 15.2254ZM22.3171 34.7032L21.5918 34.4716C21.1631 34.3377 20.367 34.0889 20.367 33.3693C20.367 32.889 20.8187 32.3762 22.0894 32.3762C22.6792 32.3983 23.2653 32.4803 23.8385 32.6211V30.916C23.1075 30.7442 22.3599 30.6524 21.609 30.6423C19.4695 30.6423 18.1395 31.7465 18.1395 33.5243C18.1395 34.8276 18.9241 35.7079 20.5393 36.2189L20.744 36.2859L21.0713 36.3911C21.3736 36.4868 22.3056 36.7739 22.3056 37.5394C22.3056 37.9527 22.0473 38.6454 20.3192 38.6454C19.5922 38.63 18.8699 38.5259 18.1682 38.3355V40.1975C18.9892 40.3473 19.8215 40.4268 20.656 40.4348C22.5295 40.4348 24.5332 39.6177 24.5332 37.325C24.5088 36.7052 24.2783 36.1111 23.8783 35.637C23.4783 35.1628 22.9316 34.8356 22.3248 34.707L22.3171 34.7032Z`,
  ],
}

export const Expand = {
  prefix: 'fal',
  iconName: 'expand',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M12 14.8284V18C12 19.1046 11.1046 20 10 20C8.89543 20 8 19.1046 8 18L8 10C8 8.89543 8.89543 8 10 8L18 8C19.1046 8 20 8.89543 20 10C20 11.1046 19.1046 12 18 12H14.8284L19.4142 16.5858C20.1953 17.3668 20.1953 18.6332 19.4142 19.4142C18.6332 20.1953 17.3668 20.1953 16.5858 19.4142L12 14.8284ZM30 12C28.8954 12 28 11.1046 28 10C28 8.89543 28.8954 8 30 8H38C39.1046 8 40 8.89543 40 10V18C40 19.1046 39.1046 20 38 20C36.8954 20 36 19.1046 36 18V14.8284L31.4142 19.4142C30.6332 20.1953 29.3668 20.1953 28.5858 19.4142C27.8047 18.6332 27.8047 17.3668 28.5858 16.5858L33.1716 12H30ZM10 28C11.1046 28 12 28.8954 12 30L12 33.1716L16.5858 28.5858C17.3668 27.8047 18.6332 27.8047 19.4142 28.5858C20.1953 29.3668 20.1953 30.6332 19.4142 31.4142L14.8284 36H18C19.1046 36 20 36.8954 20 38C20 39.1046 19.1046 40 18 40H10C9.46957 40 8.96086 39.7893 8.58579 39.4142C8.21071 39.0391 8 38.5304 8 38L8 30C8 28.8954 8.89543 28 10 28ZM28.5858 31.4142C27.8047 30.6332 27.8047 29.3668 28.5858 28.5858C29.3668 27.8047 30.6332 27.8047 31.4142 28.5858L36 33.1716V30C36 28.8954 36.8954 28 38 28C39.1046 28 40 28.8954 40 30V38C40 39.1046 39.1046 40 38 40H30C28.8954 40 28 39.1046 28 38C28 36.8954 28.8954 36 30 36H33.1716L28.5858 31.4142Z`,
  ],
}

export const CircleWarning = {
  prefix: 'fal',
  iconName: 'circle-warning',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24 8C15.1634 8 8 15.1634 8 24C8 32.8366 15.1634 40 24 40C32.8366 40 40 32.8366 40 24C40 15.1634 32.8366 8 24 8ZM4 24C4 12.9543 12.9543 4 24 4C35.0457 4 44 12.9543 44 24C44 35.0457 35.0457 44 24 44C12.9543 44 4 35.0457 4 24Z M24 28C22.8954 28 22 27.1046 22 26L22 14C22 12.8954 22.8954 12 24 12C25.1046 12 26 12.8954 26 14L26 26C26 27.1046 25.1046 28 24 28Z M21 33C21 31.3431 22.3431 30 24 30C25.6569 30 27 31.3431 27 33C27 34.6569 25.6569 36 24 36C22.3431 36 21 34.6569 21 33Z`,
  ],
}

export const ClearAll = {
  prefix: 'fal',
  iconName: 'clear-all',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24.81,27.28l-3.65,3.68c-.71,.71-.7,1.86,0,2.57,.71,.71,1.86,.7,2.57,0l3.65-3.68,3.67,3.65c.71,.71,1.86,.7,2.57,0,.71-.71,.7-1.86,0-2.57l-3.67-3.65,3.65-3.67c.71-.71,.7-1.86,0-2.57-.71-.71-1.86-.7-2.57,0l-3.65,3.67-3.67-3.65c-.71-.71-1.86-.7-2.57,0-.71,.71-.7,1.86,0,2.57l3.67,3.65ZM40.96,10.92h-3.85v-3.91c0-1.66-1.35-3.01-3.01-3.01H7.04c-1.66,0-3.01,1.35-3.01,3.01v27.06c0,1.66,1.35,3.01,3.01,3.01h3.85v3.91c0,1.66,1.35,3.01,3.01,3.01h27.06c1.66,0,3.01-1.35,3.01-3.01V13.93c0-1.66-1.35-3.01-3.01-3.01Zm-30.07,3.01v19.51h-3.23V7.64h25.81v3.28H13.9c-1.66,0-3.01,1.35-3.01,3.01Zm29.44,26.43H14.53V14.56h25.81v25.81Z`,
  ],
}

export const NavbarMonitorIcon = {
  prefix: 'fal',
  iconName: 'navbar-monitor',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M4.6 16.6H43.4C44.4 16.6 45.2 15.8 45.2 14.8V3.49995C45.2 2.49995 44.4 1.69995 43.4 1.69995H4.6C3.6 1.69995 2.8 2.49995 2.8 3.49995V14.9C2.8 15.9 3.6 16.7 4.6 16.7V16.6ZM37.9 7.89995H36.7L34.6 6.59995C34.2 6.29995 33.6 6.29995 33.2 6.59995L30.8 7.89995L28.1 5.49995H41.7V13.4H23.2C23.6 13.4 23.9 13.2 24.1 12.9L27.3 8.69995L29.7 10.8C30.1 11.2 30.8 11.3 31.3 11L33.8 9.69995L35.5 10.8C35.7 10.9 36 11 36.3 11H38C38.8 11 39.4 10.4 39.4 9.59995C39.4 8.79995 38.8 8.19995 38 8.19995L37.9 7.89995ZM6.4 5.29995H26.3C26.3 5.29995 26.1 5.49995 26 5.59995L22.7 9.79995L19.6 7.19995C19.1 6.79995 18.4 6.79995 17.9 7.19995L16.4 8.19995H10.6C9.8 8.19995 9.2 8.79995 9.2 9.59995C9.2 10.4 9.8 11 10.6 11H16.8C17.1 11 17.4 11 17.6 10.8L18.5 10.2L21.9 13.1C22.1 13.3 22.4 13.4 22.7 13.4H6.4V5.29995ZM44 42.7H30.2V38.4C30.2 37.4 29.4 36.5999 28.4 36.5999H25.7V33.9H43.3C44.3 33.9 45.1 33.0999 45.1 32.0999V20.7C45.1 19.7 44.3 18.9 43.3 18.9H4.6C3.6 18.9 2.8 19.7 2.8 20.7V32.0999C2.8 33.0999 3.6 33.9 4.6 33.9H22.2V36.5999H19.5C18.5 36.5999 17.7 37.4 17.7 38.4V42.7H4C3 42.7 2.2 43.5 2.2 44.5C2.2 45.5 3 46.2999 4 46.2999H44C45 46.2999 45.8 45.5 45.8 44.5C45.8 43.5 45 42.7 44 42.7ZM37.9 25.2H36.7L34.6 23.9C34.2 23.6 33.6 23.6 33.2 23.9L30.8 25.2L28.1 22.7999H41.7V30.7H23.2C23.6 30.7 23.9 30.5 24.1 30.2L27.3 26L29.7 28.1C30.1 28.5 30.8 28.5999 31.3 28.2999L33.8 27L35.5 28.1C35.7 28.2 36 28.2999 36.3 28.2999H38C38.8 28.2999 39.4 27.7 39.4 26.9C39.4 26.1 38.8 25.5 38 25.5L37.9 25.2ZM6.4 30.5V22.6H26.3C26.3 22.6 26.1 22.8 26 22.9L22.7 27.1L19.6 24.5C19.1 24.1 18.4 24.1 17.9 24.5L16.4 25.5H10.6C9.8 25.5 9.2 26.1 9.2 26.9C9.2 27.7 9.8 28.2999 10.6 28.2999H16.8C17.1 28.2999 17.4 28.3 17.6 28.1L18.5 27.5L21.9 30.4C22.1 30.6 22.4 30.7 22.7 30.7H6.4V30.5ZM26.8 42.7999H21.3V40.2999H26.8V42.7999Z`,
  ],
}

export const BookOpen = {
  prefix: 'fal',
  iconName: 'book-open',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M44,4h-12c-2.7,0-5.2,1-7.1,2.9-.3,.3-.6,.7-.9,1.1-.3-.4-.6-.8-.9-1.1-1.9-1.9-4.4-2.9-7.1-2.9H4c-1.1,0-2,.9-2,2v30c0,1.1,.9,2,2,2h14c1.1,0,2.1,.4,2.8,1.2,.8,.8,1.2,1.8,1.2,2.8s.9,2,2,2,2-.9,2-2,.4-2.1,1.2-2.8,1.8-1.2,2.8-1.2h14c1.1,0,2-.9,2-2V6c0-1.1-.9-2-2-2Zm-22,31.1c-1.2-.7-2.6-1.1-4-1.1H6V8h10c1.6,0,3.1,.6,4.2,1.8s1.8,2.6,1.8,4.2v21.1Zm20-1.1h-12c-1.4,0-2.8,.4-4,1.1V14c0-1.6,.6-3.1,1.8-4.2,1.1-1.1,2.7-1.8,4.2-1.8h10v26Z`,
  ],
}

export const Minimize = {
  prefix: 'fal',
  iconName: 'minimize',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M32,22.5H16c-1.1,0-2,.9-2,2s.9,2,2,2h16c1.1,0,2-.9,2-2s-.9-2-2-2Zm6-18H10c-3.31,0-6,2.69-6,6v28c0,3.31,2.69,6,6,6h28c3.31,0,6-2.69,6-6V10.5c0-3.31-2.69-6-6-6Zm2,34c0,1.1-.9,2-2,2H10c-1.1,0-2-.9-2-2V10.5c0-1.1,.9-2,2-2h28c1.1,0,2,.9,2,2v28Z`,
  ],
}

export const SignInAlt = {
  prefix: 'fal',
  iconName: 'sign-in-alt',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M41 30.2C40.5252 29.9642 39.9763 29.9266 39.4737 30.0953C38.9712 30.2641 38.5562 30.6255 38.32 31.1C37.0541 33.6564 35.1278 35.8279 32.7406 37.3896C30.3533 38.9512 27.592 39.8462 24.7425 39.9819C21.8931 40.1176 19.0592 39.489 16.5344 38.1612C14.0096 36.8334 11.8857 34.8548 10.3826 32.4302C8.87961 30.0056 8.05213 27.2233 7.98592 24.3714C7.9197 21.5195 8.61716 18.7018 10.006 16.21C11.3949 13.7183 13.4247 11.6432 15.8852 10.1997C18.3457 8.75619 21.1474 7.99674 24 8.00002C26.9822 7.9871 29.9077 8.81529 32.4406 10.3895C34.9736 11.9638 37.0113 14.2202 38.32 16.9C38.5587 17.3774 38.9773 17.7404 39.4836 17.9092C39.99 18.078 40.5426 18.0387 41.02 17.8C41.4974 17.5613 41.8604 17.1428 42.0292 16.6364C42.198 16.1301 42.1587 15.5774 41.92 15.1C39.9132 11.0615 36.6005 7.81974 32.5195 5.90088C28.4384 3.98202 23.8287 3.4987 19.4384 4.52937C15.0481 5.56004 11.1351 8.04421 8.33427 11.5787C5.53348 15.1131 4.0094 19.4904 4.0094 24C4.0094 28.5096 5.53348 32.8869 8.33427 36.4214C11.1351 39.9558 15.0481 42.44 19.4384 43.4707C23.8287 44.5013 28.4384 44.018 32.5195 42.0992C36.6005 40.1803 39.9132 36.9385 41.92 32.9C42.0392 32.6628 42.1099 32.4041 42.1281 32.1392C42.1463 31.8744 42.1116 31.6085 42.026 31.3572C41.9403 31.1059 41.8055 30.8741 41.6294 30.6754C41.4532 30.4768 41.2393 30.3151 41 30.2ZM42 22H22.82L27.42 17.42C27.6065 17.2335 27.7544 17.0122 27.8553 16.7685C27.9563 16.5249 28.0082 16.2637 28.0082 16C28.0082 15.7363 27.9563 15.4752 27.8553 15.2315C27.7544 14.9879 27.6065 14.7665 27.42 14.58C27.2335 14.3935 27.0122 14.2456 26.7685 14.1447C26.5249 14.0438 26.2637 13.9918 26 13.9918C25.7363 13.9918 25.4752 14.0438 25.2315 14.1447C24.9879 14.2456 24.7665 14.3935 24.58 14.58L16.58 22.58C16.3979 22.7702 16.2552 22.9945 16.16 23.24C15.96 23.7269 15.96 24.2731 16.16 24.76C16.2552 25.0055 16.3979 25.2298 16.58 25.42L24.58 33.42C24.7659 33.6075 24.9871 33.7563 25.2309 33.8578C25.4746 33.9593 25.736 34.0116 26 34.0116C26.264 34.0116 26.5254 33.9593 26.7692 33.8578C27.0129 33.7563 27.2341 33.6075 27.42 33.42C27.6075 33.2341 27.7563 33.0129 27.8578 32.7692C27.9593 32.5255 28.0116 32.264 28.0116 32C28.0116 31.736 27.9593 31.4746 27.8578 31.2309C27.7563 30.9871 27.6075 30.7659 27.42 30.58L22.82 26H42C42.5304 26 43.0392 25.7893 43.4142 25.4142C43.7893 25.0392 44 24.5305 44 24C44 23.4696 43.7893 22.9609 43.4142 22.5858C43.0392 22.2107 42.5304 22 42 22Z`,
  ],
}
export const Award = {
  prefix: 'fal',
  iconName: 'award',
  icon: [
    48,
    48,
    [],
    'f0000',
    `m18,34.71c-.44-.29-.97-.39-1.49-.29-.53.11-.98.41-1.28.86l-2.39,3.68-.87-1.78c-.33-.69-1.03-1.14-1.79-1.14h-1.51l2.52-3.86c.2-.32.31-.69.31-1.07,0-1.1-.9-1.99-2-1.99-.71,0-1.37.38-1.68.93l-4.49,6.88c-.41.62-.44,1.43-.08,2.07.36.63,1.02,1.03,1.73,1.03h3.95l1.89,3.87c.32.64.96,1.07,1.66,1.11h.12c.68,0,1.3-.34,1.66-.9l4.31-6.61c.3-.45.41-.99.3-1.52-.11-.53-.42-.98-.87-1.27Zm26.67,2.27l-4.53-6.93c-.6-.92-1.84-1.18-2.77-.58h.01c-.45.28-.76.74-.86,1.27-.1.52,0,1.05.29,1.49l2.52,3.85h-1.52c-.76,0-1.47.45-1.78,1.13l-.88,1.8-2.41-3.68c-.29-.45-.75-.76-1.28-.86-.52-.11-1.05,0-1.5.29-.92.6-1.17,1.84-.57,2.77l4.33,6.61c.36.56,1,.91,1.67.91h.11c.74-.04,1.4-.5,1.67-1.12l1.87-3.84h3.94c.72,0,1.39-.41,1.73-1.03.39-.65.38-1.44-.04-2.08ZM23.98,11.64c-4.28,0-7.77,3.49-7.77,7.77s3.49,7.77,7.77,7.77,7.77-3.49,7.77-7.77-3.49-7.77-7.77-7.77Zm0,12.06c-2.36,0-4.28-1.92-4.28-4.28s1.92-4.28,4.28-4.28,4.28,1.92,4.28,4.28-1.92,4.28-4.28,4.28Zm7.21,9.43c.26.09.61.18,1,.16.69-.03,1.34-.31,1.83-.8.54-.54.82-1.24.79-1.95-.02-1.96,1.21-3.67,3.02-4.24.3-.08.61-.24.88-.44.56-.43.93-1.04,1.04-1.71.14-.72-.04-1.46-.5-2.1-1.17-1.57-1.18-3.67-.08-5.15l.14-.16c.09-.11.16-.22.22-.34l.21-.23v-.37c.14-.61.06-1.27-.22-1.84-.33-.66-.9-1.14-1.61-1.37-1.86-.59-3.09-2.29-3.07-4.24,0-.34-.06-.68-.15-.91-.23-.68-.71-1.23-1.42-1.58-.64-.3-1.34-.33-2.01-.11-.47.16-.95.24-1.4.24-1.45-.02-2.8-.73-3.61-1.87-.2-.29-.45-.53-.65-.65-1.23-.87-2.94-.58-3.8.65-.82,1.16-2.16,1.85-3.58,1.85-.47,0-.95-.08-1.36-.23-.98-.36-2.13-.09-2.85.65-.54.54-.81,1.24-.79,1.95.02,1.95-1.21,3.65-3.02,4.22-.31.08-.6.23-.9.44-1.2.9-1.45,2.61-.56,3.81,1.17,1.57,1.17,3.68-.04,5.3h-.03c-.15.23-.28.48-.36.73-.22.7-.16,1.42.21,2.12.35.62.9,1.07,1.56,1.3,1.86.6,3.1,2.29,3.08,4.14-.03.34.02.69.13,1.04.23.68.71,1.23,1.4,1.58.63.3,1.37.34,2.04.12,1.95-.64,3.9.06,5,1.58.18.27.42.51.67.68.47.33,1,.5,1.58.5.09,0,.17,0,.26-.02l.24-.02c.74-.14,1.36-.54,1.73-1.1,1.14-1.6,3.14-2.25,4.98-1.63Zm-7.21-1.89c-1.55-1.49-3.62-2.34-5.78-2.34-.39,0-.79.03-1.18.08-.44-2.53-2.01-4.7-4.27-5.9,1.14-2.32,1.14-5.01,0-7.33,2.28-1.21,3.85-3.37,4.28-5.9.38.05.76.08,1.15.08,2.19,0,4.27-.86,5.8-2.35,1.82,1.75,4.41,2.63,6.96,2.26.44,2.52,2.01,4.68,4.27,5.86-1.14,2.32-1.14,5.02.02,7.33v.03c-2.28,1.21-3.86,3.38-4.29,5.91-2.56-.37-5.12.47-6.96,2.27Z`,
  ],
}

export const FileBookmark = {
  prefix: 'fal',
  iconName: 'file-bookmark',
  icon: [
    48,
    48,
    [],
    'f0000',
    `m15,20h2c.53,0,1.04-.21,1.41-.59.38-.38.59-.88.59-1.41s-.21-1.04-.59-1.41c-.38-.38-.88-.59-1.41-.59h-2c-.53,0-1.04.21-1.41.59-.38.38-.59.88-.59,1.41s.21,1.04.59,1.41c.38.38.88.59,1.41.59Zm8,12h-8c-.53,0-1.04.21-1.41.59s-.59.88-.59,1.41.21,1.04.59,1.41.88.59,1.41.59h8c.53,0,1.04-.21,1.41-.59.38-.38.59-.88.59-1.41s-.21-1.04-.59-1.41c-.38-.38-.88-.59-1.41-.59Zm0-8h-8c-.53,0-1.04.21-1.41.59-.38.38-.59.88-.59,1.41s.21,1.04.59,1.41c.38.38.88.59,1.41.59h8c.53,0,1.04-.21,1.41-.59.38-.38.59-.88.59-1.41s-.21-1.04-.59-1.41c-.38-.38-.88-.59-1.41-.59Zm13.84-5.24c.15-.36.19-.77.12-1.15-.07-.39-.26-.75-.54-1.03l-12-12c-.17-.16-.35-.28-.56-.38-.06,0-.12,0-.18,0l-.56-.2h-12.12c-1.59,0-3.12.63-4.24,1.76s-1.76,2.65-1.76,4.24v28c0,1.59.63,3.12,1.76,4.24,1.13,1.13,2.65,1.76,4.24,1.76h12c.53,0,1.04-.21,1.41-.59.38-.38.59-.88.59-1.41s-.21-1.04-.59-1.41c-.38-.38-.88-.59-1.41-.59h-12c-.53,0-1.04-.21-1.41-.59-.38-.38-.59-.88-.59-1.41V10c0-.53.21-1.04.59-1.41.38-.38.88-.59,1.41-.59h10v6c0,1.59.63,3.12,1.76,4.24,1.13,1.13,2.65,1.76,4.24,1.76h8c.39,0,.78-.12,1.11-.34.33-.22.58-.53.73-.9h0Zm-9.84-2.76c-.53,0-1.04-.21-1.41-.59-.38-.38-.59-.88-.59-1.41v-3.18l5.18,5.18h-3.18Zm14,8h-10c-.53,0-1.04.21-1.41.59s-.59.88-.59,1.41v16c0,.36.1.72.29,1.03.19.31.45.56.77.73.31.16.66.24,1.01.22.35-.02.69-.13.99-.32l3.94-2.6,4,2.6c.3.17.64.27.98.27.35,0,.69-.08.99-.25.3-.17.56-.41.74-.7.18-.29.28-.63.29-.97v-16c0-.53-.21-1.04-.59-1.41-.38-.38-.88-.59-1.41-.59Zm-2,14.24l-1.88-1.26c-.33-.22-.72-.34-1.12-.34s-.79.12-1.12.34l-1.88,1.26v-10.24h6v10.24Z`,
  ],
}

export const Reboot = {
  prefix: 'fal',
  iconName: 'reboot',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M25.5858 4.58579C26.3668 3.80474 27.6332 3.80474 28.4142 4.58579L34.4142 10.5858C35.1953 11.3668 35.1953 12.6332 34.4142 13.4142L28.4142 19.4142C27.6332 20.1953 26.3668 20.1953 25.5858 19.4142C24.8047 18.6332 24.8047 17.3668 25.5858 16.5858L28.1716 14H25C17.9046 14 12 19.9046 12 27C12 34.0954 17.9046 40 25 40C32.0954 40 38 34.0954 38 27C38 25.8954 38.8954 25 40 25C41.1046 25 42 25.8954 42 27C42 36.3046 34.3046 44 25 44C15.6954 44 8 36.3046 8 27C8 17.6954 15.6954 10 25 10H28.1716L25.5858 7.41421C24.8047 6.63317 24.8047 5.36683 25.5858 4.58579Z`,
  ],
}

export const Radios = {
  prefix: 'fal',
  iconName: 'radios',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M9.62229 9.74677C10.4013 10.3883 10.5082 11.5492 9.86669 12.3283C7.23937 15.4979 5.66603 19.561 5.66603 23.9985C5.66603 28.4359 7.23937 32.4991 9.86669 35.6687C10.5082 36.4477 10.4013 37.601 9.62229 38.2502C8.84326 38.8994 7.68998 38.7848 7.04079 38.0058C3.89412 34.2022 2 29.3218 2 23.9985C2 18.6751 3.89412 13.7947 7.04079 9.99117C7.68235 9.21214 8.84326 9.10522 9.62229 9.74677ZM38.3777 9.74677C39.1567 9.10522 40.31 9.21214 40.9592 9.99117C44.1059 13.7947 46 18.6827 46 23.9985C46 29.3142 44.1059 34.2022 40.9592 38.0058C40.3177 38.7848 39.1567 38.8917 38.3777 38.2502C37.5987 37.6086 37.4918 36.4477 38.1333 35.6687C40.753 32.4991 42.334 28.4359 42.334 23.9985C42.334 19.561 40.7606 15.4979 38.1333 12.3283C37.4918 11.5492 37.5987 10.396 38.3777 9.74677ZM20.9412 23.9985C20.9412 23.1882 21.263 22.4112 21.836 21.8382C22.4089 21.2653 23.1859 20.9434 23.9962 20.9434C24.8064 20.9434 25.5835 21.2653 26.1564 21.8382C26.7293 22.4112 27.0512 23.1882 27.0512 23.9985C27.0512 24.8087 26.7293 25.5858 26.1564 26.1587C25.5835 26.7316 24.8064 27.0535 23.9962 27.0535C23.1859 27.0535 22.4089 26.7316 21.836 26.1587C21.263 25.5858 20.9412 24.8087 20.9412 23.9985ZM16.2746 18.003C14.9839 19.6603 14.2201 21.7377 14.2201 23.9985C14.2201 26.2592 14.9839 28.3366 16.2746 29.994C16.8932 30.7959 16.7558 31.9415 15.9538 32.5678C15.1519 33.1941 14.0062 33.049 13.38 32.247C11.6081 29.971 10.5541 27.107 10.5541 23.9985C10.5541 20.89 11.6081 18.0259 13.38 15.7499C13.9986 14.948 15.1519 14.8105 15.9538 15.4291C16.7558 16.0478 16.8932 17.201 16.2746 18.003ZM34.6124 15.7499C36.3843 18.0259 37.4383 20.89 37.4383 23.9985C37.4383 27.107 36.3843 29.971 34.6124 32.247C33.9938 33.049 32.8405 33.1865 32.0385 32.5678C31.2366 31.9492 31.0991 30.7959 31.7178 29.994C33.0085 28.3366 33.7723 26.2592 33.7723 23.9985C33.7723 21.7377 33.0085 19.6603 31.7178 18.003C31.0991 17.201 31.2366 16.0554 32.0385 15.4291C32.8405 14.8028 33.9861 14.948 34.6124 15.7499Z`,
  ],
}

export const AssignedVlan = {
  prefix: 'fal',
  iconName: 'assign-vlan',
  icon: [
    48,
    48,
    [],
    'f0000',
    `m42,33.34v-12.34h-16v-6.34c2.33-.82,4-3.05,4-5.66,0-3.31-2.69-6-6-6s-6,2.69-6,6c0,2.61,1.67,4.83,4,5.66v6.34H6v12.34c-2.33.82-4,3.05-4,5.66,0,3.31,2.69,6,6,6s6-2.69,6-6c0-2.61-1.67-4.84-4-5.66v-8.34h12v8.34c-2.33.82-4,3.05-4,5.66,0,3.31,2.69,6,6,6s6-2.69,6-6c0-2.61-1.67-4.84-4-5.66v-8.34h12v8.34c-2.33.82-4,3.05-4,5.66,0,3.31,2.69,6,6,6s6-2.69,6-6c0-2.61-1.67-4.84-4-5.66Zm-34,7.66c-1.1,0-2-.9-2-2s.9-2,2-2,2,.9,2,2-.9,2-2,2Zm16,0c-1.1,0-2-.9-2-2s.9-2,2-2,2,.9,2,2-.9,2-2,2Zm0-30c-1.1,0-2-.9-2-2s.9-2,2-2,2,.9,2,2-.9,2-2,2Zm16,30c-1.1,0-2-.9-2-2s.9-2,2-2,2,.9,2,2-.9,2-2,2Z`,
  ],
}

export const GripLine = {
  prefix: 'fal',
  iconName: 'grip-line',
  icon: [48, 48, [], 'f0000', `M6,31.88h36v-4.5H6v4.5ZM6,16.12v4.5h36v-4.5H6Z`],
}

export const LineColumns = {
  prefix: 'fal',
  iconName: 'line-columns',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M6,20.63h15.98v-4.5H6v4.5ZM6,31.87h15.98v-4.5H6v4.5ZM26.54,16.13v4.5h15.46v-4.5h-15.46ZM26.54,31.87h15.46v-4.5h-15.46v4.5Z`,
  ],
}

export const AlignCenter = {
  prefix: 'fal',
  iconName: 'align-center',
  icon: [
    48,
    48,
    [],
    'f0000',
    `m42.14,26.73H5.86c-.41,0-.79.16-1.08.45-.29.29-.45.67-.45,1.08s.16.79.45,1.08c.***********,1.08.45h36.28c.4,0,.8-.16,1.08-.45.29-.29.45-.67.45-1.08s-.16-.79-.45-1.08c-.29-.29-.68-.45-1.08-.45Zm-27.75-8.53c-.41,0-.79.16-1.08.45-.29.29-.45.67-.45,1.08s.16.79.45,1.08c.***********,1.08.45h19.22c.41,0,.79-.16,1.08-.45.28-.29.45-.68.45-1.08s-.16-.79-.45-1.08c-.29-.29-.67-.45-1.08-.45H14.39Zm-8.53-5.46h36.28c.4,0,.8-.16,1.08-.45.28-.29.45-.68.45-1.08s-.16-.79-.45-1.08c-.28-.28-.68-.45-1.08-.45H5.86c-.41,0-.79.16-1.08.45-.29.29-.45.67-.45,1.08s.16.79.45,1.08c.***********,1.08.45Zm27.75,22.52H14.39c-.4,0-.8.16-1.08.45-.29.29-.45.67-.45,1.08s.16.79.45,1.08c.***********,1.08.45h19.22c.41,0,.79-.16,1.08-.45.29-.29.45-.67.45-1.08s-.16-.79-.45-1.08c-.29-.29-.67-.45-1.08-.45Z`,
  ],
}

export const AlignLeft = {
  prefix: 'fal',
  iconName: 'align-left',
  icon: [
    48,
    48,
    [],
    'f0000',
    `m7.37,12.7h33.26c.36,0,.69-.16.94-.45.25-.29.39-.68.39-1.09s-.14-.8-.39-1.09c-.25-.29-.59-.45-.94-.45H7.37c-.35,0-.69.16-.94.45-.25.29-.39.68-.39,1.09s.14.8.39,1.09c.25.29.59.45.94.45Zm0,8.56h25.87c.36,0,.69-.16.94-.45.25-.29.39-.68.39-1.09s-.14-.8-.39-1.09c-.25-.29-.59-.45-.94-.45H7.37c-.35,0-.69.16-.94.45-.25.29-.39.68-.39,1.09s.14.8.39,1.09c.25.29.59.45.94.45Zm33.26,5.48H7.37c-.35,0-.69.17-.94.45-.25.29-.39.68-.39,1.09s.14.8.39,1.09c.25.29.59.45.94.45h33.26c.36,0,.69-.16.94-.45.25-.29.39-.68.39-1.09s-.14-.8-.39-1.09c-.25-.29-.59-.45-.94-.45Zm-7.39,8.56H7.37c-.35,0-.69.17-.94.45-.25.29-.39.68-.39,1.09s.14.8.39,1.09c.25.29.59.45.94.45h25.87c.35,0,.69-.17.94-.45.25-.29.39-.68.39-1.09s-.14-.8-.39-1.09c-.25-.29-.59-.45-.94-.45Z`,
  ],
}

export const AlignRight = {
  prefix: 'fal',
  iconName: 'align-right',
  icon: [
    48,
    48,
    [],
    'f0000',
    `m7.37,12.7h33.26c.35,0,.69-.16.94-.45.25-.29.39-.68.39-1.09s-.14-.8-.39-1.09c-.25-.29-.59-.45-.94-.45H7.37c-.36,0-.69.16-.94.45-.25.29-.39.68-.39,1.09s.14.8.39,1.09c.25.29.59.45.94.45Zm33.26,22.6H14.76c-.36,0-.69.16-.94.45-.25.29-.39.68-.39,1.09s.14.8.39,1.09c.25.29.59.45.94.45h25.87c.35,0,.69-.17.94-.45.25-.29.39-.68.39-1.09s-.14-.8-.39-1.09c-.25-.29-.59-.45-.94-.45Zm0-17.12H14.76c-.36,0-.69.16-.94.45-.25.29-.39.68-.39,1.09s.14.8.39,1.09c.25.29.59.45.94.45h25.87c.35,0,.69-.16.94-.45.25-.29.39-.68.39-1.09s-.14-.8-.39-1.09c-.25-.29-.59-.45-.94-.45Zm0,8.56H7.37c-.36,0-.69.16-.94.45-.25.29-.39.68-.39,1.09s.14.8.39,1.09c.25.29.59.45.94.45h33.26c.35,0,.69-.17.94-.45.25-.29.39-.68.39-1.09s-.14-.8-.39-1.09c-.25-.29-.59-.45-.94-.45Z`,
  ],
}

export const ArrowFromRight = {
  prefix: 'fal',
  iconName: 'arrow-from-right',
  icon: [
    48,
    48,
    [],
    'f0000',
    `m7.82,9.87c-.41,0-.81.17-1.09.45-.29.29-.45.69-.45,1.09v25.17c0,.41.16.8.45,1.09.58.58,1.61.58,2.19,0,.29-.29.45-.68.45-1.09V11.42c0-.41-.17-.81-.45-1.09-.29-.29-.68-.45-1.09-.45Zm33.79,13.55h0c-.07-.2-.18-.37-.33-.52l-7.19-7.19c-.15-.15-.32-.26-.5-.34-.57-.24-1.26-.1-1.69.34-.29.29-.46.68-.46,1.1s.16.81.46,1.1l4.56,4.54H15.01c-.41,0-.8.16-1.09.45-.29.29-.45.68-.45,1.09s.16.8.45,1.09c.29.29.68.45,1.09.45h21.44l-4.56,4.54c-.15.15-.26.31-.34.5-.08.19-.12.39-.12.6s.04.41.12.59c.08.19.19.36.34.5.15.15.32.26.5.34.38.16.81.16,1.19,0,.19-.08.36-.19.5-.34l7.19-7.19c.14-.14.25-.32.32-.51.16-.38.16-.8,0-1.18Z`,
  ],
}

export const ArrowFromLeft = {
  prefix: 'fal',
  iconName: 'arrow-from-left',
  icon: [
    48,
    48,
    [],
    'f0000',
    `m32.99,22.44H11.54l4.56-4.54c.15-.15.26-.31.34-.5.08-.19.12-.39.12-.6s-.04-.41-.12-.59c-.08-.19-.19-.36-.34-.5-.15-.15-.32-.26-.5-.34-.38-.16-.81-.16-1.19,0-.19.08-.36.19-.5.34l-7.19,7.19c-.14.14-.25.32-.32.51-.16.38-.16.8,0,1.18h0c.07.2.18.37.33.52l7.19,7.19c.15.15.32.26.5.34.57.24,1.26.1,1.69-.34.29-.29.46-.68.46-1.1s-.16-.81-.46-1.1l-4.56-4.54h21.44c.41,0,.8-.16,1.09-.45.29-.29.45-.68.45-1.09s-.16-.8-.45-1.09c-.29-.29-.68-.45-1.09-.45Zm8.29-12.13c-.58-.58-1.61-.58-2.19,0-.29.29-.45.68-.45,1.09v25.17c0,.41.17.81.45,1.09.29.29.68.45,1.09.45s.81-.17,1.09-.45c.29-.29.45-.69.45-1.09V11.41c0-.41-.16-.8-.45-1.09Z`,
  ],
}

export const Drag = {
  prefix: 'fal',
  iconName: 'drag',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M14 14H10V18H14V14ZM14 22H10V26H14V22ZM10 30H14V34H10V30ZM22 14H18V18H22V14ZM18 22H22V26H18V22ZM22 30H18V34H22V30ZM26 14H30V18H26V14ZM30 22H26V26H30V22ZM26 30H30V34H26V30ZM38 14H34V18H38V14ZM34 22H38V26H34V22ZM38 30H34V34H38V30Z`,
  ],
}

export const KeyValue = {
  prefix: 'fal',
  iconName: 'key-value',
  icon: [
    48,
    48,
    [],
    'f0000',
    `m6.67,8.66c.32-.16.64-.35.97-.58v6.5h3.33V3.8h-2.73l-.07.15c-.28.61-.67,1.12-1.16,1.51-.52.42-1.24.77-2.14,1.06l-.17.06v2.83l.32-.1c.65-.2,1.2-.42,1.65-.64Zm4.05,24.7h-2.48l-.07.15c-.28.61-.67,1.12-1.16,1.51-.52.42-1.24.77-2.14,1.06l-.17.06v2.83l.32-.1c.65-.2,1.21-.42,1.65-.64.32-.16.64-.35.97-.58v6.48h3.33v-10.52h-.25v-.25Zm0-14.73h-2.48l-.07.15c-.28.61-.67,1.12-1.16,1.51-.52.41-1.24.77-2.14,1.06l-.17.06v2.83l.32-.1c.65-.2,1.2-.42,1.65-.64.32-.16.64-.35.97-.58v6.48h3.33v-10.52h-.25v-.25Zm18.84-7.48h11.78c1.08,0,1.96-.88,1.96-1.96s-.88-1.96-1.96-1.96h-11.78c-1.08,0-1.96.88-1.96,1.96s.88,1.96,1.96,1.96Zm-8.7,27.19c.29-.23.5-.46.65-.69.29-.43.43-.9.43-1.41,0-.86-.36-1.57-1.06-2.14-.69-.55-1.75-.83-3.16-.83-1.05,0-1.93.22-2.6.67-.86.58-1.3,1.35-1.3,2.28,0,.56.15,1.05.45,1.5.15.22.35.42.62.6-.42.23-.74.51-.96.85-.3.46-.45,1-.45,1.64s.16,1.23.48,1.77c.32.54.79.95,1.41,1.22.6.26,1.46.4,2.56.4.62,0,1.17-.06,1.67-.17.53-.12.97-.31,1.34-.57.37-.25.67-.61.94-1.1.26-.48.38-.97.38-1.53s-.15-1.11-.44-1.57c-.24-.37-.56-.68-.97-.93Zm-3.67-2.61c.34-.32.93-.35,1.31.01.17.16.25.38.25.67,0,.27-.08.48-.24.65-.15.16-.35.24-.61.24-.3,0-.52-.08-.7-.25-.17-.16-.25-.38-.25-.67v-.13c.01-.22.09-.39.23-.52Zm1.43,5.92c-.23.25-.44.35-.72.35s-.53-.11-.75-.35c-.19-.2-.3-.43-.33-.72v-.2c0-.41.11-.72.32-.94.43-.45,1.04-.46,1.49.01.22.23.33.53.33.92s-.11.68-.33.92Zm3.62-29.86h-4c.07-.06.13-.12.2-.17.17-.15.51-.39,1.11-.81.98-.69,1.64-1.31,2.03-1.92.39-.6.58-1.24.58-1.9s-.17-1.21-.51-1.71c-.36-.51-.83-.89-1.41-1.12-.58-.23-1.41-.36-2.4-.36s-1.74.12-2.32.37c-.57.25-1.02.62-1.35,1.11-.32.48-.54,1.13-.65,1.95l-.04.26,3.31.26.03-.23c.08-.53.2-.87.38-1.04.37-.35,1.03-.36,1.4-.01.17.17.26.37.26.61s-.1.48-.3.76c-.2.28-.7.72-1.52,1.33-1.39,1.03-2.31,1.9-2.81,2.66-.52.78-.82,1.59-.92,2.46l-.03.28h8.96v-2.79Zm19.1,24.99h-11.78c-1.08,0-1.96.88-1.96,1.96s.88,1.96,1.96,1.96h11.78c1.08,0,1.96-.88,1.96-1.96s-.88-1.96-1.96-1.96Zm-22.44-14.7c-.5,0-.94.09-1.33.27-.21.1-.42.22-.62.37.08-.7.19-1.17.32-1.39.2-.38.48-.56.84-.56.17,0,.34.06.47.18.13.12.23.37.29.69l.04.23,3.02-.37.22-.04v-.29c-.19-.58-.44-1.07-.77-1.44-.33-.38-.75-.68-1.26-.88-.5-.2-1.17-.3-2-.3-1.43,0-2.56.46-3.37,1.35-.81.9-1.22,2.3-1.22,4.18,0,1.31.19,2.33.58,3.11.41.82.92,1.39,1.57,1.75.65.36,1.51.55,2.56.55.88,0,1.62-.15,2.21-.45.6-.3,1.07-.76,1.41-1.35.34-.59.5-1.23.5-1.95,0-1.05-.34-1.93-1-2.62-.67-.69-1.49-1.04-2.46-1.04Zm0,4.83c-.21.23-.45.34-.75.34-.32,0-.6-.13-.82-.39-.21-.24-.33-.58-.35-1.01v-.11c0-.46.12-.82.35-1.07.2-.23.47-.35.79-.35s.55.11.76.36c.22.26.33.64.33,1.14s-.1.86-.31,1.09Zm22.44-4.86h-11.78c-1.08,0-1.96.88-1.96,1.96s.88,1.96,1.96,1.96h11.78c1.08,0,1.96-.88,1.96-1.96s-.88-1.96-1.96-1.96Z`,
  ],
}

export const LeftArrow = {
  prefix: 'fal',
  iconName: 'left-arrow',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M42 22H18.82L23.42 17.42C23.6065 17.2335 23.7544 17.0121 23.8553 16.7685C23.9562 16.5249 24.0082 16.2637 24.0082 16C24.0082 15.7363 23.9562 15.4751 23.8553 15.2315C23.7544 14.9879 23.6065 14.7665 23.42 14.58C23.2335 14.3935 23.0121 14.2456 22.7685 14.1447C22.5249 14.0438 22.2637 13.9918 22 13.9918C21.7363 13.9918 21.4751 14.0438 21.2315 14.1447C20.9879 14.2456 20.7665 14.3935 20.58 14.58L12.58 22.58C12.3979 22.7702 12.2552 22.9945 12.16 23.24C11.96 23.7269 11.96 24.2731 12.16 24.76C12.2552 25.0055 12.3979 25.2298 12.58 25.42L20.58 33.42C20.7659 33.6075 20.9871 33.7562 21.2308 33.8578C21.4746 33.9593 21.736 34.0116 22 34.0116C22.264 34.0116 22.5254 33.9593 22.7692 33.8578C23.0129 33.7562 23.2341 33.6075 23.42 33.42C23.6075 33.2341 23.7562 33.0129 23.8578 32.7692C23.9593 32.5254 24.0116 32.264 24.0116 32C24.0116 31.736 23.9593 31.4746 23.8578 31.2308C23.7562 30.9871 23.6075 30.7659 23.42 30.58L18.82 26H42C42.5304 26 43.0391 25.7893 43.4142 25.4142C43.7893 25.0391 44 24.5304 44 24C44 23.4696 43.7893 22.9609 43.4142 22.5858C43.0391 22.2107 42.5304 22 42 22ZM6 6C5.46957 6 4.96086 6.21071 4.58579 6.58579C4.21071 6.96086 4 7.46957 4 8V40C4 40.5304 4.21071 41.0391 4.58579 41.4142C4.96086 41.7893 5.46957 42 6 42C6.53043 42 7.03914 41.7893 7.41421 41.4142C7.78929 41.0391 8 40.5304 8 40V8C8 7.46957 7.78929 6.96086 7.41421 6.58579C7.03914 6.21071 6.53043 6 6 6V6Z`,
  ],
}

export const RightArrow = {
  prefix: 'fal',
  iconName: 'right-arrow',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M35.42 22.58L27.42 14.58C27.2335 14.3935 27.0121 14.2456 26.7685 14.1447C26.5249 14.0438 26.2637 13.9918 26 13.9918C25.7363 13.9918 25.4751 14.0438 25.2315 14.1447C24.9879 14.2456 24.7665 14.3935 24.58 14.58C24.3935 14.7665 24.2456 14.9879 24.1447 15.2315C24.0438 15.4751 23.9918 15.7363 23.9918 16C23.9918 16.2637 24.0438 16.5249 24.1447 16.7685C24.2456 17.0121 24.3935 17.2335 24.58 17.42L29.18 22H6C5.46957 22 4.96086 22.2107 4.58579 22.5858C4.21071 22.9609 4 23.4696 4 24C4 24.5304 4.21071 25.0391 4.58579 25.4142C4.96086 25.7893 5.46957 26 6 26H29.18L24.58 30.58C24.3925 30.7659 24.2438 30.9871 24.1422 31.2308C24.0407 31.4746 23.9884 31.736 23.9884 32C23.9884 32.264 24.0407 32.5254 24.1422 32.7692C24.2438 33.0129 24.3925 33.2341 24.58 33.42C24.7659 33.6075 24.9871 33.7562 25.2308 33.8578C25.4746 33.9593 25.736 34.0116 26 34.0116C26.264 34.0116 26.5254 33.9593 26.7692 33.8578C27.0129 33.7562 27.2341 33.6075 27.42 33.42L35.42 25.42C35.6021 25.2298 35.7448 25.0055 35.84 24.76C36.04 24.2731 36.04 23.7269 35.84 23.24C35.7448 22.9945 35.6021 22.7702 35.42 22.58ZM42 8C41.4696 8 40.9609 8.21071 40.5858 8.58579C40.2107 8.96086 40 9.46957 40 10V38C40 38.5304 40.2107 39.0391 40.5858 39.4142C40.9609 39.7893 41.4696 40 42 40C42.5304 40 43.0391 39.7893 43.4142 39.4142C43.7893 39.0391 44 38.5304 44 38V10C44 9.46957 43.7893 8.96086 43.4142 8.58579C43.0391 8.21071 42.5304 8 42 8V8Z`,
  ],
}

export const GridTable = {
  prefix: 'fal',
  iconName: 'grid-table',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M42.9788 6.00021H4.55184C4.14647 5.99479 3.75548 6.15031 3.46458 6.43269C3.17369 6.71507 3.00662 7.10127 3 7.50662V40.4944C3.00142 40.7385 3.06274 40.9785 3.17858 41.1933C3.29442 41.4082 3.46124 41.5913 3.66438 41.7266V41.9651H4.21681C4.32586 41.9885 4.43708 42.0002 4.5486 42H42.9755C43.0865 42.0002 43.1972 41.9886 43.3057 41.9651H43.3868V41.9424C43.7113 41.8613 43.9999 41.6749 44.2074 41.4126C44.4149 41.1502 44.5297 40.8265 44.5338 40.492V7.50419C44.527 7.09855 44.3594 6.71223 44.0677 6.43019C43.7761 6.14815 43.3844 5.99348 42.9788 6.00021ZM9.13354 40.6729H4.43746C4.40554 40.6551 4.37885 40.6293 4.36006 40.5979C4.34127 40.5666 4.33103 40.5309 4.33038 40.4944V36.5852H9.13354V40.6729ZM9.13354 35.2864H4.33038V31.0479H9.13354V35.2864ZM9.13354 29.7572H4.33038V25.2826H9.13354V29.7572ZM9.13354 23.992H4.33038V19.1507H9.13354V23.992ZM9.13354 17.8609H4.33038V13.1299H9.13354V17.8609ZM20.3201 40.6647H10.4558V36.5738H20.3233L20.3201 40.6647ZM20.3201 35.2783H10.4558V31.0398H20.3233L20.3201 35.2783ZM20.3201 29.7499H10.4558V25.2753H20.3233L20.3201 29.7499ZM20.3201 23.9847H10.4558V19.1434H20.3233L20.3201 23.9847ZM20.3201 17.8528H10.4558V13.1194H20.3233L20.3201 17.8528ZM20.3201 11.8287H10.4558V7.27137H20.3233L20.3201 11.8287ZM31.6267 40.6566H21.6489V36.5738H31.6267V40.6566ZM31.6267 35.271H21.6489V31.0325H31.6267V35.271ZM31.6267 29.7418H21.6489V25.2672H31.6267V29.7418ZM31.6267 23.9766H21.6489V19.1329H31.6267V23.9766ZM31.6267 17.8447H21.6489V13.1113H31.6267V17.8447ZM31.6267 11.8206H21.6489V7.26326H31.6267V11.8206ZM43.1929 40.4676C43.1919 40.5042 43.1815 40.5398 43.1626 40.5711C43.1437 40.6024 43.1169 40.6282 43.085 40.6461H32.9571V36.5617H43.1905L43.1929 40.4676ZM43.1929 35.2678H32.9587V31.0325H43.1929V35.2678ZM43.1929 29.7386H32.9587V25.2672H43.1929V29.7386ZM43.1929 23.9733H32.9587V19.1329H43.1929V23.9733ZM43.1929 17.8422H32.9587V13.1113H43.1929V17.8422ZM43.1929 11.8182H32.9587V7.26326H42.9715C43.0279 7.26296 43.0824 7.28432 43.1236 7.32295C43.1648 7.36159 43.1896 7.41455 43.1929 7.47093V11.8182Z`,
  ],
}

export const Encryption = {
  prefix: 'fal',
  iconName: 'encryption',
  icon: [
    48,
    48,
    [],
    'f0000',
    `m39.81,8c-.13-.27-.32-.51-.55-.7-.23-.19-.5-.32-.79-.39-.29-.07-.59-.07-.89,0-2.14.45-4.34.45-6.48.02s-4.17-1.31-5.96-2.56c-.33-.23-.73-.36-1.14-.36s-.81.12-1.14.36c-1.79,1.25-3.82,2.12-5.96,2.56-2.14.44-4.35.43-6.48-.02-.29-.06-.59-.06-.89,0-.29.07-.56.2-.79.39-.23.19-.42.43-.55.7-.13.27-.19.57-.19.86v14.9c0,2.87.68,5.69,1.99,8.24s3.21,4.75,5.55,6.42l7.3,5.2c.34.24.74.37,1.16.37s.82-.13,1.16-.37l7.3-5.2c2.33-1.67,4.24-3.87,5.55-6.42,1.31-2.55,1.99-5.38,1.99-8.24v-14.9c0-.3-.07-.59-.19-.86Zm-3.81,15.76c0,2.23-.53,4.43-1.55,6.41-1.02,1.98-2.5,3.69-4.31,4.99l-6.14,4.38-6.14-4.38c-1.81-1.3-3.29-3.01-4.31-4.99-1.02-1.98-1.55-4.18-1.55-6.41v-12.6c4.19.36,8.39-.61,12-2.78,3.61,2.17,7.81,3.14,12,2.78v12.6Z`,
  ],
}

export const SolidThumbsUp = {
  prefix: 'fal',
  iconName: 'solid-thumbs-up',
  icon: [
    48,
    48,
    [],
    'f0000',
    `m9,18.2H3c-1.54,0-2.8,1.26-2.8,2.8v21c0,1.54,1.26,2.8,2.8,2.8h6c1.54,0,2.8-1.26,2.8-2.8v-21c0-1.54-1.26-2.8-2.8-2.8Zm36.21,6.76l.27-.14c1.43-.74,2.32-2.21,2.32-3.82,0-2.37-1.93-4.3-4.3-4.3h-13.85l.18-.3c1.29-2.17,2.19-4.52,2.68-6.99l.22-1.07c.23-1.13,0-2.27-.64-3.23-.64-.96-1.61-1.61-2.74-1.83-.28-.06-.57-.08-.84-.08-2.01,0-3.81,1.42-4.22,3.46l-.21,1.07c-.63,3.15-2.36,6-4.87,8.01l-.69.55c-2.1,1.69-3.3,4.2-3.3,6.88v10.43c0,2.95,1.46,5.69,3.91,7.32l3.61,2.41c1.45.96,3.14,1.48,4.89,1.48h9.14c2.37,0,4.3-1.93,4.3-4.3,0-.31-.03-.63-.1-.93l-.04-.18.17-.06c1.77-.57,2.96-2.21,2.96-4.08,0-.79-.21-1.56-.62-2.23l-.15-.25.28-.05c2.02-.38,3.48-2.16,3.48-4.22,0-1.31-.58-2.53-1.6-3.35l-.24-.19Z`,
  ],
}

export const SolidSleeveThumbsUp = {
  prefix: 'fal',
  iconName: 'solid-sleeve-thumbs-up',
  icon: [
    48,
    48,
    [],
    'f0000',
    `m9,18.2H3c-1.54,0-2.8,1.26-2.8,2.8v21c0,1.54,1.26,2.8,2.8,2.8h6c1.54,0,2.8-1.26,2.8-2.8v-21c0-1.54-1.26-2.8-2.8-2.8Zm37.01,8.5l.08-.08c1.1-1.11,1.7-2.57,1.7-4.12,0-3.2-2.6-5.8-5.8-5.8h-8.96l.13-.28c.45-.99.82-1.99,1.1-2.99l.53-1.88c.48-1.68.28-3.45-.57-4.98-.85-1.53-2.24-2.64-3.92-3.12-1.68-.48-3.45-.28-4.98.57-1.53.85-2.64,2.24-3.12,3.92l-.53,1.88c-.35,1.24-1,2.4-1.87,3.36l-4.81,5.29c-.37.4-.56.93-.53,1.48s.26,1.05.67,1.42c.84.76,2.14.7,2.9-.14l4.81-5.29c1.29-1.42,2.25-3.15,2.78-4.99l.53-1.88c.37-1.3,1.73-2.05,3.03-1.68,1.3.37,2.05,1.72,1.69,3.03l-.54,1.88c-.54,1.9-1.39,3.67-2.52,5.26-.44.62-.5,1.44-.14,2.13.35.68,1.05,1.11,1.82,1.11h12.53c.94,0,1.7.76,1.7,1.7,0,.7-.44,1.34-1.1,1.59-.64.24-1.11.77-1.27,1.43-.16.66,0,1.35.45,1.85.27.31.42.71.42,1.12,0,.82-.58,1.52-1.38,1.67-.7.14-1.29.63-1.54,1.29-.25.67-.13,1.43.31,1.99.24.31.36.67.36,1.05,0,.7-.44,1.33-1.08,1.58-.98.38-1.51,1.44-1.23,2.46.04.13.06.29.06.46,0,.94-.76,1.7-1.7,1.7h-9.14c-1.23,0-2.42-.36-3.44-1.04l-5.78-3.85c-.34-.23-.73-.35-1.13-.35-.13,0-.27.01-.4.04-.54.11-1.01.42-1.31.88-.31.46-.41,1-.31,1.54.11.53.42,1,.88,1.3l5.78,3.85c1.69,1.13,3.67,1.73,5.71,1.73h9.14c3.15,0,5.7-2.47,5.8-5.62v-.09s.08-.06.08-.06c1.38-1.11,2.17-2.76,2.17-4.53,0-.38-.04-.76-.12-1.18l-.02-.12.1-.08c1.46-1.11,2.29-2.79,2.29-4.62,0-.57-.08-1.14-.25-1.69l-.04-.11Z`,
  ],
}

export const ArrowUpRightFromCircles = {
  prefix: 'fal',
  iconName: 'arrow-up-right-from-circles',
  icon: [
    48,
    48,
    [],
    'f0000',
    `m24.86,24.54c-.08-.31-.24-.6-.47-.83-.23-.23-.52-.39-.83-.48-.31-.08-.64-.08-.96,0L3.9,28.44c-.36.1-.68.31-.92.59-.24.29-.38.64-.42,1.01-.03.37.05.74.23,1.07.18.32.46.58.8.74l3.8,1.83-4.4,4.37c-.3.26-.54.57-.71.92-.17.35-.27.73-.29,1.13-.05,1.2.61,2.43,2.06,3.88.92.94,2.14,1.99,3.62,1.99.08,0,.17,0,.25,0,.65-.05,1.58-.34,2.39-1.41l4.16-4.27,1.68,4c.15.35.4.65.72.85.32.2.7.29,1.08.27.38-.02.74-.16,1.04-.4.3-.24.51-.56.61-.93l5.24-18.57c.09-.31.09-.64.01-.96Zm-7.35,13.51l-.73-1.72c-.12-.28-.3-.52-.54-.71-.23-.19-.51-.32-.81-.37-.3-.06-.6-.04-.89.05-.29.09-.55.25-.76.47l-6.18,6.34s-.05.06-.08.09c-.31-.23-.6-.48-.86-.76,0,0-.01-.01-.02-.02-.31-.3-.6-.63-.85-.99l6-5.96c.21-.21.36-.46.45-.74.09-.28.11-.57.06-.86-.05-.29-.17-.56-.34-.79-.18-.23-.4-.42-.67-.55l-1.76-.85,10.9-3.03-2.94,10.41Zm21.28-14.58c0-7.86-6.39-14.25-14.25-14.25s-14.21,6.35-14.25,14.18l3.71-1.03c.28-2.7,1.59-5.18,3.66-6.95,2.06-1.76,4.72-2.67,7.43-2.52,2.71.14,5.26,1.32,7.13,3.29s2.91,4.58,2.91,7.29c0,5.44-4.13,9.94-9.42,10.53l-1.05,3.73c7.8-.07,14.12-6.43,14.12-14.25Zm6.79-4.19c-.83-4.16-2.87-7.99-5.87-10.99h0c-3-2.99-6.82-5.04-10.98-5.86-4.16-.83-8.47-.4-12.4,1.22-3.92,1.62-7.27,4.37-9.63,7.9-2.36,3.53-3.62,7.67-3.62,11.92,0,.64.03,1.28.09,1.91l3.59-1c-.02-.3-.02-.6-.02-.91,0-9.81,7.98-17.8,17.8-17.8s17.8,7.98,17.8,17.8-7.98,17.8-17.8,17.8c-.29,0-.58,0-.87-.02l-1.01,3.6c.62.05,1.25.08,1.88.08,4.24,0,8.39-1.26,11.92-3.61s6.28-5.71,7.9-9.63c1.62-3.92,2.05-8.24,1.22-12.4Zm-17.71,7.06l-1.19,4.23c1.23-.37,2.35-1.06,3.23-1.99.89-.93,1.52-2.08,1.83-3.33.31-1.25.29-2.56-.06-3.8-.35-1.24-1.01-2.37-1.93-3.27-.92-.9-2.05-1.56-3.3-1.89s-2.55-.34-3.8-.01c-1.25.33-2.38.97-3.31,1.87-.92.9-1.59,2.02-1.95,3.26l4.35-1.21c.41-.11.83-.17,1.26-.18.68-.3,1.43-.39,2.16-.27.73.13,1.41.47,1.95.98.54.51.91,1.17,1.08,1.89.17.72.11,1.48-.15,2.17.02.52-.04,1.04-.18,1.54Z`,
  ],
}

export const Screen = {
  prefix: 'fal',
  iconName: 'screen',
  icon: [
    48,
    48,
    [],
    'f0000',
    `m43.79,8.18c-.39-.37-.91-.58-1.46-.58H5.67c-.55,0-1.07.21-1.46.58-.39.37-.61.89-.61,1.42v28.8c0,.53.22,1.05.61,1.42.39.37.91.58,1.46.58h36.67c.55,0,1.07-.21,1.46-.58.39-.37.61-.89.61-1.42V9.6c0-.53-.22-1.05-.61-1.42Zm-3.52,28.22H7.73V11.64h32.53v24.76Z`,
  ],
}

export const SplitedScreen = {
  prefix: 'fal',
  iconName: 'splited-screen',
  icon: [
    48,
    48,
    [],
    'f0000',
    `m43.79,8.18c-.39-.37-.91-.58-1.46-.58H5.67c-.55,0-1.07.21-1.46.58-.39.37-.61.89-.61,1.42v28.8c0,.53.22,1.05.61,1.42.39.37.91.58,1.46.58h36.67c.55,0,1.07-.21,1.46-.58.39-.37.61-.89.61-1.42V9.6c0-.53-.22-1.05-.61-1.42Zm-21.86,28.22H7.73V11.64h14.2v24.76Zm18.33,0h-14.2V11.64h14.2v24.76Z`,
  ],
}

export const AutoScroll = {
  prefix: 'fal',
  iconName: 'auto-scroll',
  icon: [
    48,
    48,
    [],
    'f0000',
    `m14.99,13.98h.01c.53,0,1.04-.21,1.41-.59l7.59-7.59,7.59,7.59c.78.78,2.05.78,2.83,0,.38-.38.59-.88.59-1.41s-.21-1.04-.59-1.41L25.41,1.57c-.75-.76-2.07-.76-2.83,0l-9,9c-.38.38-.59.88-.59,1.41s.21,1.04.59,1.41c.38.38.88.59,1.41.59Zm16.58,20.6h.02l-7.59,7.59-7.59-7.59c-.75-.76-2.07-.76-2.83,0-.78.78-.78,2.05,0,2.83l9,9c.38.38.88.59,1.41.59s1.04-.21,1.41-.59l9-9c.78-.78.78-2.05,0-2.83s-2.05-.78-2.83,0Zm-13.84-10.59c0,3.46,2.82,6.28,6.28,6.28s6.28-2.82,6.28-6.28-2.82-6.28-6.28-6.28-6.28,2.82-6.28,6.28Zm8.55,0c0,1.26-1.02,2.28-2.28,2.28s-2.28-1.02-2.28-2.28,1.02-2.28,2.28-2.28,2.28,1.02,2.28,2.28Z`,
  ],
}

export const Ncm = {
  prefix: 'fal',
  iconName: 'ncm',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24.57,20.52c-.73,0-1.38,.3-1.85,.78-.45,.46-.72,1.1-.72,1.79,0,1.42,1.15,2.58,2.58,2.58,.73,0,1.39-.3,1.86-.79,.44-.46,.72-1.09,.72-1.78,0-1.42-1.15-2.58-2.58-2.58Zm9.56,4.01c.07-.47,.12-.95,.12-1.44,0-.4-.03-.79-.08-1.18l2.54-.78-1.03-3.34-2.55,.79c-.41-.77-.92-1.48-1.52-2.11l1.6-2.17-2.82-2.08-1.6,2.17c-.77-.37-1.59-.65-2.46-.81v-2.7h-3.5v2.7c-.74,.13-1.46,.35-2.14,.65l-1.52-2.22-2.89,1.97,1.52,2.22c-.05,.05-.11,.1-.17,.16-.56,.58-1.03,1.22-1.42,1.9l-2.53-.88-1.15,3.3,2.51,.88c-.08,.5-.13,1.01-.13,1.53,0,.37,.03,.73,.07,1.09l-2.53,.78,1.03,3.34,2.51-.78c.41,.79,.92,1.51,1.52,2.15l-1.56,2.11,2.82,2.08,1.55-2.1c.78,.39,1.62,.67,2.51,.83v2.6h3.5v-2.6c.76-.14,1.5-.36,2.19-.68l1.47,2.15,2.89-1.97-1.48-2.16c.05-.05,.1-.09,.15-.15,.57-.59,1.05-1.25,1.44-1.95l2.48,.87,1.15-3.3-2.5-.87Zm-5.1,2.84c-1.17,1.22-2.76,1.9-4.45,1.9-3.4,0-6.17-2.77-6.17-6.17,0-1.61,.62-3.14,1.74-4.29,1.17-1.21,2.75-1.88,4.44-1.88,3.4,0,6.17,2.77,6.17,6.17,0,1.6-.61,3.12-1.72,4.28ZM6.85,23.1c0-3.3,.92-6.47,2.61-9.23l.11,1.55,3.49-.25-.55-7.69-7.78,.6,.27,3.49,1.85-.14c-2.27,3.44-3.5,7.47-3.5,11.67,0,11.7,9.52,21.22,21.22,21.22v-3.5c-9.77,0-17.72-7.95-17.72-17.72Zm35.4,11.72c2.29-3.45,3.54-7.5,3.54-11.72C45.79,11.4,36.27,1.88,24.57,1.88v3.5c9.77,0,17.72,7.95,17.72,17.72,0,3.22-.88,6.31-2.48,9.01l-.09-1.31-3.49,.25,.55,7.69,7.78-.6-.27-3.49-2.03,.16Z`,
  ],
}

export const BackupNow = {
  prefix: 'fal',
  iconName: 'backup-now',
  icon: [
    48,
    48,
    [],
    'f0000',
    `m24.59,4.59c.78-.78,2.05-.78,2.83,0l6,6c.78.78.78,2.05,0,2.83l-6,6c-.78.78-2.05.78-2.83,0-.78-.78-.78-2.05,0-2.83l2.59-2.59h-3.17c-7.1,0-13,5.9-13,13s5.9,13,13,13,13-5.9,13-13c0-1.1.9-2,2-2s2,.9,2,2c0,9.3-7.7,17-17,17S7,36.3,7,27,14.7,10,24,10h3.17l-2.59-2.59c-.78-.78-.78-2.05,0-2.83Z`,
  ],
}

export const Compare = {
  prefix: 'fal',
  iconName: 'compare',
  icon: [
    48,
    48,
    [],
    'f0000',
    `m28.59,4.59c.78-.78,2.05-.78,2.83,0l8,8c.78.78.78,2.05,0,2.83l-8,8c-.78.78-2.05.78-2.83,0-.78-.78-.78-2.05,0-2.83l4.59-4.59H10c-1.1,0-2-.9-2-2s.9-2,2-2h23.17l-4.59-4.59c-.78-.78-.78-2.05,0-2.83Zm-9.17,20c.78.78.78,2.05,0,2.83l-4.59,4.59h23.17c1.1,0,2,.9,2,2s-.9,2-2,2H14.83l4.59,4.59c.78.78.78,2.05,0,2.83-.78.78-2.05.78-2.83,0l-8-8c-.38-.38-.59-.88-.59-1.41s.21-1.04.59-1.41l8-8c.78-.78,2.05-.78,2.83,0Z`,
  ],
}

export const Restore = {
  prefix: 'fal',
  iconName: 'restore',
  icon: [
    48,
    48,
    [],
    'f0000',
    `m27,7c-9.3,0-17,7.7-17,17v3.17l-2.59-2.59c-.78-.78-2.05-.78-2.83,0-.78.78-.78,2.05,0,2.83l6,6c.78.78,2.05.78,2.83,0l6-6c.78-.78.78-2.05,0-2.83-.78-.78-2.05-.78-2.83,0l-2.59,2.59v-3.17c0-7.1,5.9-13,13-13s13,5.9,13,13-5.9,13-13,13c-1.1,0-2,.9-2,2s.9,2,2,2c9.3,0,17-7.7,17-17S36.3,7,27,7Z`,
  ],
}

export const FilledStar = {
  prefix: 'fal',
  iconName: 'filled-star',
  icon: [
    48,
    48,
    [],
    'f0000',
    `m43.96,19.34c-.13-.37-.36-.69-.66-.93-.31-.24-.67-.38-1.06-.42l-11.38-1.66-5.1-10.34c-.16-.34-.42-.62-.74-.82-.32-.2-.69-.31-1.06-.31s-.74.11-1.06.31c-.32.2-.57.48-.74.82l-5.1,10.32-11.38,1.68c-.37.05-.72.21-1,.45-.29.24-.5.56-.62.91-.11.35-.12.72-.03,1.07.09.35.27.67.53.93l8.26,8-2,11.36c-.07.37-.03.76.11,1.12.14.35.38.66.69.88.3.22.66.34,1.03.37.37.02.74-.05,1.07-.23l10.24-5.34,10.2,5.36c.28.16.6.24.92.24.42,0,.84-.13,1.18-.38.31-.22.55-.53.69-.88s.18-.74.11-1.12l-2-11.36,8.26-8c.29-.24.5-.57.62-.93.11-.36.12-.75.02-1.11Z`,
  ],
}

export const File = {
  prefix: 'fal',
  iconName: 'File',
  icon: [
    48,
    48,
    [],
    'f0000',
    `m43.96,19.34c-.13-.37-.36-.69-.66-.93-.31-.24-.67-.38-1.06-.42l-11.38-1.66-5.1-10.34c-.16-.34-.42-.62-.74-.82-.32-.2-.69-.31-1.06-.31s-.74.11-1.06.31c-.32.2-.57.48-.74.82l-5.1,10.32-11.38,1.68c-.37.05-.72.21-1,.45-.29.24-.5.56-.62.91-.11.35-.12.72-.03,1.07.09.35.27.67.53.93l8.26,8-2,11.36c-.07.37-.03.76.11,1.12.14.35.38.66.69.88.3.22.66.34,1.03.37.37.02.74-.05,1.07-.23l10.24-5.34,10.2,5.36c.28.16.6.24.92.24.42,0,.84-.13,1.18-.38.31-.22.55-.53.69-.88s.18-.74.11-1.12l-2-11.36,8.26-8c.29-.24.5-.57.62-.93.11-.36.12-.75.02-1.11Z`,
  ],
}

export const Tag = {
  prefix: 'fal',
  iconName: 'tag',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M15,12c-1.66,0-3,1.34-3,3s1.34,3,3,3,3-1.34,3-3-1.34-3-3-3ZM43.41,24.59L23.41,4.59c-.38-.38-.88-.59-1.41-.59H6c-1.1,0-2,.9-2,2v16c0,.53.21,1.04.59,1.41l20,20c.78.78,2.05.78,2.83,0l16-16c.78-.78.78-2.05,0-2.83ZM26,39.17L8,21.17V8h13.17l18,18-13.17,13.17Z`,
  ],
}

export const Integration = {
  prefix: 'fal',
  iconName: 'integration',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M13.72,17.52c-.73-.73-2-.73-2.73,0-.75.75-.75,1.97,0,2.73l.16.16-2.45,2.45c-4.04,4.04-4.53,10.37-1.25,14.96l-4.08,4.08c-.36.36-.56.85-.56,1.36s.2,1,.56,1.36l.19.19h.03c.32.23.72.36,1.14.36h0c.52,0,1.01-.2,1.36-.55l4.08-4.08c4.59,3.28,10.92,2.79,14.96-1.25l2.44-2.45.16.16c.75.75,1.97.75,2.73,0,.36-.36.56-.85.56-1.36s-.2-1-.56-1.36L13.72,17.52ZM22.42,36.57c-1.47,1.47-3.42,2.28-5.49,2.28s-4.03-.81-5.49-2.28c-3.03-3.03-3.03-7.96,0-10.98h0l2.45-2.45,10.99,10.99-2.45,2.45ZM45.19,4.74c0-.52-.2-1-.57-1.36-.7-.7-2.03-.7-2.73,0l-4.08,4.08c-4.59-3.29-10.92-2.79-14.96,1.25l-2.45,2.45-.16-.16c-.73-.73-2-.73-2.73,0-.75.75-.75,1.97,0,2.73l3.21,3.21-1.33,1.33c-.71.71-.71,1.87,0,2.58.38.38.89.59,1.44.59h0c.54,0,1.05-.21,1.43-.59l1.19-1.19,4.89,4.89-1.29,1.29c-.36.36-.55.83-.55,1.33s.2.97.55,1.33c.77.77,2.02.77,2.79,0l1.22-1.23,3.21,3.21c.36.36.85.56,1.36.56s1-.2,1.36-.56c.36-.36.56-.85.56-1.36s-.2-1-.56-1.36l-.16-.16,2.45-2.45c4.04-4.04,4.53-10.37,1.25-14.96l4.08-4.08c.36-.36.56-.85.56-1.36ZM36.56,22.42l-2.45,2.45-10.99-10.98,2.45-2.45c1.47-1.47,3.42-2.28,5.49-2.28s4.03.81,5.49,2.28c3.03,3.03,3.03,7.96,0,10.99Z`,
  ],
}

export const TicketId = {
  prefix: 'fal',
  iconName: 'ticket-id',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M18,20c-.53,0-1.04.21-1.41.59-.38.38-.59.88-.59,1.41v4c0,.53.21,1.04.59,1.41.38.38.88.59,1.41.59s1.04-.21,1.41-.59c.38-.38.59-.88.59-1.41v-4c0-.53-.21-1.04-.59-1.41-.38-.38-.88-.59-1.41-.59ZM42,22c.53,0,1.04-.21,1.41-.59s.59-.88.59-1.41v-8c0-.53-.21-1.04-.59-1.41-.38-.38-.88-.59-1.41-.59H6c-.53,0-1.04.21-1.41.59-.38.38-.59.88-.59,1.41v8c0,.53.21,1.04.59,1.41.38.38.88.59,1.41.59s1.04.21,1.41.59c.38.38.59.88.59,1.41s-.21,1.04-.59,1.41c-.38.38-.88.59-1.41.59s-1.04.21-1.41.59c-.38.38-.59.88-.59,1.41v8c0,.53.21,1.04.59,1.41.38.38.88.59,1.41.59h36c.53,0,1.04-.21,1.41-.59s.59-.88.59-1.41v-8c0-.53-.21-1.04-.59-1.41s-.88-.59-1.41-.59-1.04-.21-1.41-.59-.59-.88-.59-1.41.21-1.04.59-1.41.88-.59,1.41-.59ZM40,18.36c-1.16.42-2.16,1.19-2.87,2.2-.71,1.01-1.09,2.21-1.09,3.44s.38,2.43,1.09,3.44c.71,1.01,1.71,1.78,2.87,2.2v4.36h-20c0-.53-.21-1.04-.59-1.41-.38-.38-.88-.59-1.41-.59s-1.04.21-1.41.59c-.38.38-.59.88-.59,1.41h-8v-4.36c1.16-.42,2.16-1.19,2.87-2.2.71-1.01,1.09-2.21,1.09-3.44s-.38-2.43-1.09-3.44c-.71-1.01-1.71-1.78-2.87-2.2v-4.36h8c0,.53.21,1.04.59,1.41.38.38.88.59,1.41.59s1.04-.21,1.41-.59c.38-.38.59-.88.59-1.41h20v4.36Z`,
  ],
}

export const AccessKey = {
  prefix: 'fal',
  iconName: 'access-key',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M14.89,11.89c-1.79,0-3.24,1.45-3.24,3.24s1.45,3.24,3.24,3.24,3.24-1.45,3.24-3.24-1.45-3.24-3.24-3.24ZM44.91,33.96l-11.81-11.8c2.26-7.92-1.87-16.22-9.68-19.15-5.81-2.18-12.39-.75-16.77,3.64-6.18,6.17-6.2,16.22-.03,22.4,4.07,4.08,10.01,5.63,15.54,4.06l1.05,1.05c.84.84,2.01,1.33,3.2,1.33h.44c.16,2.42,2.1,4.35,4.52,4.52v.42c0,1.2.49,2.37,1.33,3.21l1.34,1.34c.69.66,1.59,1.02,2.55,1.03h5.99c1.89,0,3.42-1.54,3.43-3.43v-6.01c0-.97-.39-1.89-1.09-2.6ZM42,42h-5.28l-1.19-1.19c-.1-.1-.16-.24-.16-.38v-.74c0-2.02-1.64-3.67-3.67-3.67-.47,0-.86-.39-.86-.86v-.22c0-1.9-1.55-3.45-3.45-3.45h-.98c-.14,0-.27-.06-.37-.15l-1.25-1.25h0c-.67-.67-1.57-1.03-2.5-1.03-.35,0-.71.05-1.06.16-4.18,1.25-8.69.11-11.77-2.98-4.61-4.62-4.6-12.14.03-16.75,3.28-3.28,8.2-4.35,12.54-2.72,6.03,2.26,9.13,8.81,7.05,14.9-.33.98-.09,2.04.64,2.76l12.29,12.28v5.3Z`,
  ],
}

export const Fan = {
  prefix: 'fal',
  iconName: 'fan',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M23.9302 21.4291C22.5536 21.4291 21.5004 22.5526 21.5004 23.9292C21.5004 25.3058 22.6239 26.4292 23.9302 26.4292C25.2365 26.4292 26.36 25.3058 26.36 23.9292C26.36 22.5526 25.3756 21.4291 23.9302 21.4291ZM43.9776 24.7104C43.1377 18.079 37.4946 13.0617 30.8366 13.0617C29.7233 13.0617 28.6202 13.1496 27.517 13.3449L28.1223 7.31896C28.2102 6.40097 27.8782 5.49235 27.2145 4.84858C26.5508 4.20482 25.6231 3.90168 24.7152 4.02825C18.0707 4.85757 13.0627 10.5022 13.0627 17.1634C13.0627 18.2767 13.1506 19.39 13.3458 20.4838L7.3243 19.8744C6.41646 19.7767 5.50862 20.1087 4.87345 20.7635C4.21929 21.4277 3.90702 22.3651 4.02421 23.2831C4.86408 29.9238 10.5072 34.9318 17.1652 34.9318C18.2785 34.9318 19.3816 34.8439 20.4848 34.6486L19.8832 40.6796C19.7856 41.5874 20.1176 42.4961 20.7715 43.1312C21.3442 43.6875 22.1098 44 22.9067 44C23.0335 44 23.1703 43.9902 23.2972 43.9805C29.9271 43.1406 34.8678 37.4959 34.8678 30.8317C34.8678 29.7184 34.7799 28.6144 34.5848 27.5113L40.6092 28.1265C41.517 28.2144 42.4256 27.8824 43.06 27.2281C43.7823 26.5699 44.0948 25.5698 43.9776 24.7104ZM31.3523 23.4213C30.4358 23.3288 29.7374 24.2253 30.0515 25.0909L30.3896 26.0245C30.9168 27.4793 31.1904 29.1012 31.1904 30.8395C31.1904 35.3123 28.027 39.16 23.7019 40.1054L24.5863 31.3473C24.6788 30.4309 23.7824 29.7324 22.9167 30.0465L21.9839 30.3846C20.5292 30.9218 18.908 31.1854 17.1705 31.1854C12.6985 31.1854 8.86559 28.0213 7.90462 23.6946L16.6549 24.579C17.5713 24.6717 18.2697 23.7751 17.9557 22.9094L17.6175 21.976C17.0863 20.5228 16.8128 18.8978 16.8128 17.1634C16.8128 12.6906 19.9762 8.85064 24.3013 7.89749L23.4224 16.6477C23.3299 17.5642 24.2263 18.2626 25.092 17.9485L26.0248 17.6106C27.4795 17.0832 29.1006 16.8098 30.8382 16.8098C35.3102 16.8098 39.1587 19.9739 40.1041 24.3006L31.3523 23.4213Z`,
  ],
}

export const PowerSupply = {
  prefix: 'fal',
  iconName: 'power-supply',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M11.1667 6.27767V4.44434H7.5V6.27767V12.9999H11.1667V6.27767ZM25.8333 6.27767V4.44434H22.1667V6.27767V12.9999H25.8333V6.27767ZM3.83333 15.4443H2V19.111H3.83333H4.44444V23.9999C4.44444 30.1263 8.95139 35.1985 14.8333 36.0846V43.5555H18.5V36.0846C19.6611 35.9089 20.7688 35.5728 21.7924 35.0992C21.6319 34.2742 21.5556 33.4263 21.5556 32.5554C21.5556 32.0131 21.5861 31.4784 21.6472 30.9589C20.2493 31.9596 18.5229 32.5554 16.6667 32.5554C11.9382 32.5554 8.11111 28.7284 8.11111 23.9999V19.111H25.2222V23.3277C26.2764 22.2124 27.5139 21.2804 28.8889 20.5777V19.111H29.5H31.3333V15.4443H29.5H28.8889H25.2222H8.11111H4.44444H3.83333ZM35 43.5555C37.9174 43.5555 40.7153 42.3965 42.7782 40.3336C44.8411 38.2707 46 35.4728 46 32.5554C46 29.6381 44.8411 26.8402 42.7782 24.7773C40.7153 22.7144 37.9174 21.5554 35 21.5554C32.0826 21.5554 29.2847 22.7144 27.2218 24.7773C25.1589 26.8402 24 29.6381 24 32.5554C24 35.4728 25.1589 38.2707 27.2218 40.3336C29.2847 42.3965 32.0826 43.5555 35 43.5555ZM36.8333 26.4443L36.016 31.3332H41.1111L33.1667 38.6666L33.984 33.7777H28.8889L36.8333 26.4443Z`,
  ],
}

export const PCI = {
  prefix: 'fal',
  iconName: 'pci',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M6.4 14.6008C5.795 14.6008 5.3 15.0958 5.3 15.7008V33.3008C5.3 33.9058 5.795 34.4008 6.4 34.4008H41.6C42.205 34.4008 42.7 33.9058 42.7 33.3008V15.7008C42.7 15.0958 42.205 14.6008 41.6 14.6008H6.4ZM2 15.7008C2 13.2739 3.97313 11.3008 6.4 11.3008H41.6C44.0269 11.3008 46 13.2739 46 15.7008V33.3008C46 35.7277 44.0269 37.7008 41.6 37.7008H6.4C3.97313 37.7008 2 35.7277 2 33.3008V15.7008ZM9.7 16.8008H25.1C26.3169 16.8008 27.3 17.7839 27.3 19.0008V21.2008C27.3 22.4177 26.3169 23.4008 25.1 23.4008H9.7C8.48313 23.4008 7.5 22.4177 7.5 21.2008V19.0008C7.5 17.7839 8.48313 16.8008 9.7 16.8008ZM7.5 31.1008C7.5 30.4958 7.995 30.0008 8.6 30.0008H26.2C26.805 30.0008 27.3 30.4958 27.3 31.1008C27.3 31.7058 26.805 32.2008 26.2 32.2008H8.6C7.995 32.2008 7.5 31.7058 7.5 31.1008ZM29.5 31.1008C29.5 30.4958 29.995 30.0008 30.6 30.0008H39.4C40.005 30.0008 40.5 30.4958 40.5 31.1008C40.5 31.7058 40.005 32.2008 39.4 32.2008H30.6C29.995 32.2008 29.5 31.7058 29.5 31.1008ZM8.6 25.6008C8.89174 25.6008 9.17153 25.7167 9.37782 25.923C9.58411 26.1293 9.7 26.409 9.7 26.7008C9.7 26.9925 9.58411 27.2723 9.37782 27.4786C9.17153 27.6849 8.89174 27.8008 8.6 27.8008C8.30826 27.8008 8.02847 27.6849 7.82218 27.4786C7.61589 27.2723 7.5 26.9925 7.5 26.7008C7.5 26.409 7.61589 26.1293 7.82218 25.923C8.02847 25.7167 8.30826 25.6008 8.6 25.6008ZM11.9 26.7008C11.9 26.409 12.0159 26.1293 12.2222 25.923C12.4285 25.7167 12.7083 25.6008 13 25.6008C13.2917 25.6008 13.5715 25.7167 13.7778 25.923C13.9841 26.1293 14.1 26.409 14.1 26.7008C14.1 26.9925 13.9841 27.2723 13.7778 27.4786C13.5715 27.6849 13.2917 27.8008 13 27.8008C12.7083 27.8008 12.4285 27.6849 12.2222 27.4786C12.0159 27.2723 11.9 26.9925 11.9 26.7008ZM17.4 25.6008C17.6917 25.6008 17.9715 25.7167 18.1778 25.923C18.3841 26.1293 18.5 26.409 18.5 26.7008C18.5 26.9925 18.3841 27.2723 18.1778 27.4786C17.9715 27.6849 17.6917 27.8008 17.4 27.8008C17.1083 27.8008 16.8285 27.6849 16.6222 27.4786C16.4159 27.2723 16.3 26.9925 16.3 26.7008C16.3 26.409 16.4159 26.1293 16.6222 25.923C16.8285 25.7167 17.1083 25.6008 17.4 25.6008ZM20.7 26.7008C20.7 26.409 20.8159 26.1293 21.0222 25.923C21.2285 25.7167 21.5083 25.6008 21.8 25.6008C22.0917 25.6008 22.3715 25.7167 22.5778 25.923C22.7841 26.1293 22.9 26.409 22.9 26.7008C22.9 26.9925 22.7841 27.2723 22.5778 27.4786C22.3715 27.6849 22.0917 27.8008 21.8 27.8008C21.5083 27.8008 21.2285 27.6849 21.0222 27.4786C20.8159 27.2723 20.7 26.9925 20.7 26.7008ZM26.2 25.6008C26.4917 25.6008 26.7715 25.7167 26.9778 25.923C27.1841 26.1293 27.3 26.409 27.3 26.7008C27.3 26.9925 27.1841 27.2723 26.9778 27.4786C26.7715 27.6849 26.4917 27.8008 26.2 27.8008C25.9083 27.8008 25.6285 27.6849 25.4222 27.4786C25.2159 27.2723 25.1 26.9925 25.1 26.7008C25.1 26.409 25.2159 26.1293 25.4222 25.923C25.6285 25.7167 25.9083 25.6008 26.2 25.6008Z`,
  ],
}

export const Brush = {
  prefix: 'fal',
  iconName: 'brush',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M9.16,36.6c-1.22,0-2.22,1-2.22,2.22s1,2.22,2.22,2.22,2.22-1,2.22-2.22-1-2.22-2.22-2.22ZM45.51,16.85l-2.21-2.2c-.67-.67-1.61-1.03-2.52-1,.03-.93-.32-1.84-1-2.52l-4.75-4.75c-.42-.42-.94-.72-1.52-.88-.16-.56-.46-1.09-.87-1.51l-1.49-1.49c-.65-.65-1.5-1-2.42-1h0c-.91,0-1.76.36-2.4,1l-14.92,14.92c-1.33,1.33-1.33,3.5,0,4.83l3.18,3.18c.19.19.28.45.26.72-.03.27-.18.51-.4.66-9.51,6.07-10.4,6.95-10.82,7.38-2.82,2.82-2.82,7.4,0,10.22,1.36,1.36,3.18,2.11,5.11,2.11s3.75-.75,5.11-2.11c.43-.42,1.31-1.31,7.37-10.82.15-.23.39-.38.66-.41.26-.03.52.06.72.25l3.18,3.18c1.33,1.33,3.5,1.33,4.83,0l14.92-14.92c1.33-1.33,1.33-3.5,0-4.83ZM28.15,33.46l-2.83-2.83c-1.03-1.03-2.46-1.52-3.92-1.37-1.46.16-2.74.96-3.53,2.19-3.77,5.9-6.2,9.51-6.84,10.17-.63.63-1.49.96-2.35.97-.88,0-1.71-.35-2.33-.97-.63-.63-.97-1.46-.97-2.34,0-.88.35-1.71.97-2.33.45-.45,2.57-2,10.16-6.84,1.23-.78,2.03-2.07,2.19-3.53.16-1.46-.34-2.89-1.37-3.92l-2.83-2.83,1.61-1.61,13.63,13.63-1.61,1.61ZM32.54,29.09l-13.64-13.64,9.83-9.83.89.89-1.52,3.37c-.15.32-.08.68.17.94.25.25.62.31.95.16l3.29-1.59,4.21,4.21-6.12,9.11c-.23.33-.19.77.1,1.06.29.29.73.33,1.06.1l9.07-6.16,1.54,1.54-9.84,9.84Z`,
  ],
}

export const DataElement = {
  prefix: 'fal',
  iconName: 'data-element',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M31.56,22.88c-5.9,0-10.69,4.78-10.69,10.69s4.78,10.69,10.69,10.69,10.69-4.78,10.69-10.69-4.78-10.69-10.69-10.69ZM28.23,38.12h-3v-3h3v3ZM28.23,32.14h-3v-3h3v3ZM38.56,38.12h-7.95v-3h7.95v3ZM38.56,32.14h-7.95v-3h7.95v3ZM9.75,35.75v-6.54c2.47,1.17,5.1,1.94,7.79,2.3.2-1.38.6-2.7,1.17-3.92-5.61-.57-8.96-2.63-8.96-3.84v-6.54c3.74,1.78,7.86,2.65,12,2.54,4.14.11,8.26-.76,12-2.54v2.35c1.41.22,2.76.64,4,1.25v-9.06c0-5.26-8-8-16-8S5.75,6.49,5.75,11.75v24c0,5.25,7.97,7.99,15.95,8-1.22-1.19-2.23-2.59-2.96-4.15-5.63-.57-8.99-2.63-8.99-3.85ZM21.75,7.75c7.44,0,12,2.58,12,4s-4.56,4-12,4-12-2.58-12-4,4.56-4,12-4Z`,
  ],
}

export const OldestMessage = {
  prefix: 'fal',
  iconName: 'oldest-message',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M38,8H10c-1.59,0-3.12,.63-4.24,1.76-1.13,1.13-1.76,2.65-1.76,4.24v20c0,1.59,.63,3.12,1.76,4.24,1.13,1.13,2.65,1.76,4.24,1.76h28c1.59,0,3.12-.63,4.24-1.76,1.13-1.13,1.76-2.65,1.76-4.24V14c0-1.59-.63-3.12-1.76-4.24-1.13-1.13-2.65-1.76-4.24-1.76Zm-.82,4l-11.76,11.76c-.19,.19-.41,.34-.65,.44-.24,.1-.51,.15-.77,.15s-.53-.05-.77-.15c-.24-.1-.46-.25-.65-.44L10.82,12h26.36Zm2.82,22c0,.53-.21,1.04-.59,1.41s-.88,.59-1.41,.59H10c-.53,0-1.04-.21-1.41-.59-.38-.38-.59-.88-.59-1.41V14.82l11.76,11.76c1.12,1.12,2.65,1.75,4.24,1.75s3.11-.63,4.24-1.75l11.76-11.76v19.18Z`,
  ],
}

export const CiscoWanLink = {
  prefix: 'fal',
  iconName: 'cisco-wan-link',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M12.0505 37.2503L9.72047 43.6003H11.7705L12.1105 42.5103H13.6805L14.0205 43.6003H16.0805L13.7305 37.2503H12.0505ZM12.6005 40.9103L12.8905 39.9803L13.1805 40.9103H12.6005ZM9.22047 34.0303L9.52047 34.1403L36.2405 34.4103H36.2705L36.5505 34.3303C39.0105 33.6803 41.2305 32.2203 42.8005 30.2103C44.3705 28.2103 45.2605 25.7003 45.3105 23.1603C45.3505 20.6203 44.5505 18.0803 43.0605 16.0203C41.7005 14.1503 39.9205 12.7303 37.9005 11.9103C36.5005 9.19028 34.2705 6.87028 31.6105 5.39028C28.6505 3.73028 25.1605 3.09028 21.8005 3.56028C18.4405 4.04028 15.2705 5.63028 12.8905 8.05028C10.8205 10.1403 9.40047 12.7703 8.76047 15.6503C7.26047 16.2703 5.94047 17.2503 4.91047 18.4903C3.57047 20.1103 2.78047 22.1603 2.68047 24.2603C2.58047 26.3603 3.16047 28.4703 4.33047 30.2203C5.50047 31.9703 7.23047 33.3203 9.21047 34.0203L9.22047 34.0303ZM8.27047 21.2503C9.02047 20.3403 10.0605 19.6903 11.2005 19.4003L12.6405 19.0403L12.8305 17.5703C13.1405 15.1403 14.2705 12.8403 15.9905 11.0903C17.7105 9.34028 20.0005 8.19028 22.4305 7.85028C24.8605 7.51028 27.3805 7.97028 29.5205 9.17028C31.6605 10.3703 33.3805 12.2603 34.3605 14.5103L34.7605 15.4303L35.7205 15.7203C37.1905 16.1603 38.5605 17.1803 39.5805 18.5703C40.5305 19.8703 41.0305 21.4703 41.0005 23.0803C40.9705 24.6903 40.4105 26.2703 39.4205 27.5403C38.4905 28.7303 37.1805 29.6203 35.7305 30.0703L10.3805 29.8303C9.40047 29.4103 8.54047 28.7003 7.96047 27.8203C7.31047 26.8403 6.98047 25.6603 7.04047 24.4803C7.10047 23.3103 7.54047 22.1603 8.29047 21.2503H8.27047ZM7.89047 37.2503L7.40047 39.9103L6.81047 37.2503H5.21047L4.63047 39.9003L4.15047 37.2503H2.23047L3.58047 43.6003H5.44047L6.01047 41.1203L6.59047 43.6003H8.45047L9.80047 37.2503H7.88047H7.89047ZM46.0605 37.2503H43.6805L42.3405 39.2003L42.3105 39.2503V37.2503H40.4005V43.6003H42.3105V41.9003L42.5905 41.5903L43.7405 43.6003H46.0005L43.8805 40.1003L46.0605 37.2503ZM37.4405 40.1903L35.7705 37.2403H33.9605V43.5903H35.8705V40.6403L37.5505 43.5903H39.3505V37.2403H37.4405V40.1903ZM30.9105 43.6003H32.8205V37.2503H30.9105V43.6003ZM20.0305 40.1903L18.3605 37.2403H16.5505V43.5903H18.4605V40.6403L20.1405 43.5903H21.9405V37.2403H20.0305V40.1903ZM27.6005 37.2503H25.6905V43.6003H30.0605V42.0203H27.6005V37.2503Z`,
  ],
}

export const LeftToRightArrow = {
  prefix: 'fal',
  iconName: 'left-to-right-arrow',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M35.6583 22.58L27.6583 14.58C27.4718 14.3935 27.2504 14.2456 27.0068 14.1447C26.7631 14.0438 26.502 13.9918 26.2383 13.9918C25.9746 13.9918 25.7134 14.0438 25.4698 14.1447C25.2261 14.2456 25.0048 14.3935 24.8183 14.58C24.6318 14.7665 24.4839 14.9879 24.383 15.2315C24.282 15.4751 24.2301 15.7363 24.2301 16C24.2301 16.2637 24.282 16.5249 24.383 16.7685C24.4839 17.0121 24.6318 17.2335 24.8183 17.42L29.4183 22H6.23828C5.70785 22 5.19914 22.2107 4.82407 22.5858C4.44899 22.9609 4.23828 23.4696 4.23828 24C4.23828 24.5304 4.44899 25.0391 4.82407 25.4142C5.19914 25.7893 5.70785 26 6.23828 26H29.4183L24.8183 30.58C24.6308 30.7659 24.482 30.9871 24.3805 31.2308C24.279 31.4746 24.2267 31.736 24.2267 32C24.2267 32.264 24.279 32.5254 24.3805 32.7692C24.482 33.0129 24.6308 33.2341 24.8183 33.42C25.0042 33.6075 25.2254 33.7562 25.4691 33.8578C25.7128 33.9593 25.9743 34.0116 26.2383 34.0116C26.5023 34.0116 26.7637 33.9593 27.0074 33.8578C27.2512 33.7562 27.4724 33.6075 27.6583 33.42L35.6583 25.42C35.8404 25.2298 35.9831 25.0055 36.0783 24.76C36.2783 24.2731 36.2783 23.7269 36.0783 23.24C35.9831 22.9945 35.8404 22.7702 35.6583 22.58ZM42.2383 8C41.7078 8 41.1991 8.21071 40.8241 8.58579C40.449 8.96086 40.2383 9.46957 40.2383 10V38C40.2383 38.5304 40.449 39.0391 40.8241 39.4142C41.1991 39.7893 41.7078 40 42.2383 40C42.7687 40 43.2774 39.7893 43.6525 39.4142C44.0276 39.0391 44.2383 38.5304 44.2383 38V10C44.2383 9.46957 44.0276 8.96086 43.6525 8.58579C43.2774 8.21071 42.7687 8 42.2383 8V8Z`,
  ],
}
export const RightToLeftArrow = {
  prefix: 'fal',
  iconName: 'right-to-left-arrow',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M34.0017 22H10.8217L15.4217 17.42C15.6082 17.2335 15.7561 17.0121 15.8571 16.7685C15.958 16.5249 16.0099 16.2637 16.0099 16C16.0099 15.7363 15.958 15.4751 15.8571 15.2315C15.7561 14.9879 15.6082 14.7665 15.4217 14.58C15.2353 14.3935 15.0139 14.2456 14.7702 14.1447C14.5266 14.0438 14.2655 13.9918 14.0017 13.9918C13.738 13.9918 13.4769 14.0438 13.2332 14.1447C12.9896 14.2456 12.7682 14.3935 12.5817 14.58L4.58175 22.58C4.39966 22.7702 4.25693 22.9945 4.16175 23.24C3.96171 23.7269 3.96171 24.2731 4.16175 24.76C4.25693 25.0055 4.39966 25.2298 4.58175 25.42L12.5817 33.42C12.7677 33.6075 12.9889 33.7562 13.2326 33.8578C13.4763 33.9593 13.7377 34.0116 14.0017 34.0116C14.2658 34.0116 14.5272 33.9593 14.7709 33.8578C15.0146 33.7562 15.2358 33.6075 15.4217 33.42C15.6092 33.2341 15.758 33.0129 15.8595 32.7692C15.9611 32.5254 16.0133 32.264 16.0133 32C16.0133 31.736 15.9611 31.4746 15.8595 31.2308C15.758 30.9871 15.6092 30.7659 15.4217 30.58L10.8217 26H34.0017C34.5322 26 35.0409 25.7893 35.416 25.4142C35.791 25.0391 36.0017 24.5304 36.0017 24C36.0017 23.4696 35.791 22.9609 35.416 22.5858C35.0409 22.2107 34.5322 22 34.0017 22ZM42.0017 8C41.4713 8 40.9626 8.21071 40.5875 8.58579C40.2125 8.96086 40.0017 9.46957 40.0017 10V38C40.0017 38.5304 40.2125 39.0391 40.5875 39.4142C40.9626 39.7893 41.4713 40 42.0017 40C42.5322 40 43.0409 39.7893 43.416 39.4142C43.791 39.0391 44.0017 38.5304 44.0017 38V10C44.0017 9.46957 43.791 8.96086 43.416 8.58579C43.0409 8.21071 42.5322 8 42.0017 8V8Z`,
  ],
}

export const BulkUsers = {
  prefix: 'fal',
  iconName: 'bulk-users',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M26.6 24.44C27.6672 23.5163 28.5231 22.3738 29.1097 21.09C29.6964 19.8063 30 18.4114 30 17C30 14.3478 28.9464 11.8043 27.0711 9.92893C25.1957 8.05357 22.6522 7 20 7C17.3478 7 14.8043 8.05357 12.9289 9.92893C11.0536 11.8043 10 14.3478 10 17C9.99998 18.4114 10.3036 19.8063 10.8903 21.09C11.4769 22.3738 12.3328 23.5163 13.4 24.44C10.6003 25.7078 8.22493 27.7551 6.55796 30.3371C4.89099 32.9191 4.00294 35.9266 4 39C4 39.5304 4.21071 40.0391 4.58579 40.4142C4.96086 40.7893 5.46957 41 6 41C6.53043 41 7.03914 40.7893 7.41421 40.4142C7.78929 40.0391 8 39.5304 8 39C8 35.8174 9.26428 32.7652 11.5147 30.5147C13.7652 28.2643 16.8174 27 20 27C23.1826 27 26.2348 28.2643 28.4853 30.5147C30.7357 32.7652 32 35.8174 32 39C32 39.5304 32.2107 40.0391 32.5858 40.4142C32.9609 40.7893 33.4696 41 34 41C34.5304 41 35.0391 40.7893 35.4142 40.4142C35.7893 40.0391 36 39.5304 36 39C35.9971 35.9266 35.109 32.9191 33.442 30.3371C31.7751 27.7551 29.3997 25.7078 26.6 24.44V24.44ZM20 23C18.8133 23 17.6533 22.6481 16.6666 21.9888C15.6799 21.3295 14.9108 20.3925 14.4567 19.2961C14.0026 18.1997 13.8838 16.9933 14.1153 15.8295C14.3468 14.6656 14.9182 13.5965 15.7574 12.7574C16.5965 11.9182 17.6656 11.3468 18.8295 11.1153C19.9933 10.8838 21.1997 11.0026 22.2961 11.4567C23.3925 11.9108 24.3295 12.6799 24.9888 13.6666C25.6481 14.6533 26 15.8133 26 17C26 18.5913 25.3679 20.1174 24.2426 21.2426C23.1174 22.3679 21.5913 23 20 23ZM43.42 18.26C43.2341 18.0725 43.0129 17.9238 42.7692 17.8222C42.5254 17.7207 42.264 17.6684 42 17.6684C41.736 17.6684 41.4746 17.7207 41.2308 17.8222C40.9871 17.9238 40.7659 18.0725 40.58 18.26L36.58 22.26L35.34 21C35.1541 20.8125 34.9329 20.6638 34.6892 20.5622C34.4454 20.4607 34.184 20.4084 33.92 20.4084C33.656 20.4084 33.3946 20.4607 33.1508 20.5622C32.9071 20.6638 32.6859 20.8125 32.5 21C32.1275 21.3747 31.9184 21.8816 31.9184 22.41C31.9184 22.9384 32.1275 23.4453 32.5 23.82L35.18 26.5C35.5547 26.8725 36.0616 27.0816 36.59 27.0816C37.1184 27.0816 37.6253 26.8725 38 26.5L43.34 21.16C43.5419 20.9786 43.7048 20.7581 43.8188 20.5118C43.9327 20.2654 43.9954 19.9985 44.0028 19.7272C44.0103 19.4559 43.9625 19.1859 43.8623 18.9336C43.7621 18.6814 43.6116 18.4522 43.42 18.26V18.26Z`,
  ],
}

export const ExportXLSXIcon = {
  prefix: 'fal',
  iconName: 'export-xlsx',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M10.5 40.25H16.75V44H10.5C7.74219 44 5.5 41.7578 5.5 39V9C5.5 6.24219 7.74219 4 10.5 4H23.4297C24.7578 4 26.0312 4.52344 26.9687 5.46094L34.0391 12.5312C34.9766 13.4687 35.5 14.7422 35.5 16.0703V27.75H31.75V16.5H25.5C24.1172 16.5 23 15.3828 23 14V7.75H10.5C9.8125 7.75 9.25 8.3125 9.25 9V39C9.25 39.6875 9.8125 40.25 10.5 40.25ZM23 32.75C23 33.3203 23.1719 33.875 23.4844 34.3437L24.25 35.4922L25.0156 34.3437C25.3281 33.8672 25.5 33.3125 25.5 32.75C25.5 32.0625 26.0625 31.5 26.75 31.5C27.4375 31.5 28 32.0625 28 32.75C28 33.8125 27.6875 34.8516 27.0937 35.7344L25.75 37.75L27.0937 39.7656C27.6875 40.6484 28 41.6875 28 42.75C28 43.4375 27.4375 44 26.75 44C26.0625 44 25.5 43.4375 25.5 42.75C25.5 42.1797 25.3281 41.625 25.0156 41.1563L24.25 40L23.4844 41.1484C23.1719 41.625 23 42.1797 23 42.7422C23 43.4297 22.4375 43.9922 21.75 43.9922C21.0625 43.9922 20.5 43.4297 20.5 42.7422C20.5 41.6797 20.8125 40.6406 21.4063 39.7578L22.75 37.75L21.4063 35.7344C20.8125 34.8516 20.5 33.8125 20.5 32.75C20.5 32.0625 21.0625 31.5 21.75 31.5C22.4375 31.5 23 32.0625 23 32.75ZM30.5 42.75V32.75C30.5 32.0625 31.0625 31.5 31.75 31.5C32.4375 31.5 33 32.0625 33 32.75V41.5H35.5C36.1875 41.5 36.75 42.0625 36.75 42.75C36.75 43.4375 36.1875 44 35.5 44H31.75C31.0625 44 30.5 43.4375 30.5 42.75ZM37.375 35.0703C37.375 33.1016 38.9688 31.5 40.9453 31.5H43C43.6875 31.5 44.25 32.0625 44.25 32.75C44.25 33.4375 43.6875 34 43 34H40.9453C40.3594 34 39.875 34.4766 39.875 35.0703C39.875 35.4766 40.1016 35.8437 40.4688 36.0234L42.9062 37.2422C44.1172 37.8437 44.875 39.0781 44.875 40.4297C44.875 42.3984 43.2812 44 41.3047 44H39.25C38.5625 44 38 43.4375 38 42.75C38 42.0625 38.5625 41.5 39.25 41.5H41.3047C41.8906 41.5 42.375 41.0234 42.375 40.4297C42.375 40.0234 42.1484 39.6563 41.7812 39.4766L39.3438 38.2578C38.1406 37.6563 37.375 36.4219 37.375 35.0703Z`,
  ],
}

export const Threads = {
  prefix: 'fal',
  iconName: 'threads',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M14.31,10.48h19.46c1.1,0,2-.9,2-2s-.9-2-2-2H14.31c-1.1,0-2,.9-2,2s.9,2,2,2ZM14.31,17.72h19.46c1.1,0,2-.9,2-2s-.9-2-2-2H14.31c-1.1,0-2,.9-2,2s.9,2,2,2ZM14.31,32.18h19.46c1.1,0,2-.9,2-2s-.9-2-2-2H14.31c-1.1,0-2,.9-2,2s.9,2,2,2ZM14.31,24.96h19.46c1.1,0,2-.9,2-2s-.9-2-2-2H14.31c-1.1,0-2,.9-2,2s.9,2,2,2ZM43.08,20.11c-.57-.73-1.53-1.15-2.87-1.23l-.53-.03v15.92c0,.57-.15,1.02-.45,1.31-.32.32-.83.49-1.51.49H9.81c-.49,0-1-.04-1.37-.31-.41-.29-.61-.83-.61-1.69v-15.72h-.51c-1.09.04-1.94.32-2.54.84-.71.61-1.07,1.57-1.07,2.84v15.18c0,1.32.35,2.26,1.07,2.89.72.63,1.82.93,3.35.93h31.46c1.61,0,2.71-.4,3.37-1.23.51-.64.75-1.52.75-2.77v-15.28c0-.9-.2-1.59-.62-2.13Z`,
  ],
}

export const EnableLogging = {
  prefix: 'fal',
  iconName: 'enable-logging',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M8 8C8 5.79086 9.79086 4 12 4H28C28.5304 4 29.0391 4.21071 29.4142 4.58579L39.4142 14.5858C39.7893 14.9609 40 15.4696 40 16V40C40 42.2091 38.2091 44 36 44H12C9.79086 44 8 42.2091 8 40V8ZM35.1716 16L28 8.82843V16H35.1716ZM24 8L12 8V40H36V20H26C24.8954 20 24 19.1046 24 18V8ZM16 26C16 24.8954 16.8954 24 18 24H30C31.1046 24 32 24.8954 32 26C32 27.1046 31.1046 28 30 28H18C16.8954 28 16 27.1046 16 26ZM16 34C16 32.8954 16.8954 32 18 32H30C31.1046 32 32 32.8954 32 34C32 35.1046 31.1046 36 30 36H18C16.8954 36 16 35.1046 16 34Z`,
  ],
}

export const Upgrade = {
  prefix: 'fal',
  iconName: 'upgrade',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M43.5098 18.9594C42.9498 18.3794 42.1598 18.0394 41.3398 18.0394H38.1798C38.1198 17.8794 38.0598 17.7194 37.9898 17.5594L40.2298 15.3294C40.8098 14.7494 41.1298 13.9794 41.1298 13.1694C41.1298 12.3594 40.8198 11.5994 40.2498 10.9994L36.5598 7.28938C35.3998 6.12938 33.3898 6.12938 32.2298 7.28938L30.0698 9.43937C29.8898 9.35937 29.7198 9.28938 29.5398 9.21938V6.15937C29.5198 4.47938 28.1398 3.10938 26.4598 3.10938H21.2198C19.5298 3.10938 18.1598 4.47938 18.1598 6.16938V9.14937C18.0198 9.20937 17.8798 9.25938 17.7298 9.32938L15.6498 7.20938C14.5598 6.06938 12.5298 5.99938 11.3098 7.16938L7.5798 10.8294C6.9998 11.3894 6.6598 12.1794 6.6598 12.9994C6.6598 13.8194 6.9698 14.5694 7.5398 15.1694L9.6598 17.3194C9.5798 17.4994 9.5098 17.6794 9.4398 17.8594H6.3898C5.5898 17.8494 4.8298 18.1594 4.2298 18.7294C3.6498 19.3094 3.3298 20.0794 3.3298 20.8794L3.2998 26.1394C3.2998 27.8394 4.6698 29.2194 6.3498 29.2194H9.4798C9.5398 29.3794 9.5998 29.5294 9.6798 29.6894L7.4498 31.9094C6.8698 32.4894 6.5498 33.2594 6.5498 34.0694C6.5498 34.8794 6.8598 35.6394 7.4298 36.2394L11.1198 39.9494C12.2798 41.1094 14.2998 41.0894 15.4298 39.9694L17.7398 37.6794C17.8698 37.7294 17.9998 37.7894 18.1298 37.8394V41.1094C18.1298 42.7994 19.4998 44.1694 21.1898 44.1694H27.7698V40.1794H22.1398V37.1894C22.1398 35.8794 21.2998 34.7094 20.0698 34.2994C19.7098 34.1794 19.3198 34.0194 18.8798 33.8094C17.7498 33.2794 16.2898 33.5194 15.4098 34.3994L13.2998 36.5094L10.8898 34.0894L12.9298 32.0594C13.8698 31.1194 14.0998 29.7094 13.5098 28.5294C13.2998 28.1094 13.1298 27.6994 12.9998 27.2894C12.5598 26.0794 11.4098 25.2594 10.1398 25.2594H7.2998L7.31981 21.8294H10.1098C11.4198 21.8394 12.5798 21.0094 12.9998 19.7894C13.1598 19.3394 13.3398 18.8994 13.5498 18.4794C14.1098 17.3194 13.8798 15.9194 12.9798 14.9894L11.0198 13.0294L13.4598 10.6294L15.3598 12.5694C16.2698 13.5094 17.6798 13.7494 18.8498 13.1894C19.2598 12.9994 19.6798 12.8294 20.0698 12.6894C21.3198 12.2694 22.1498 11.1094 22.1498 9.79938V7.08938H25.5498V9.84938C25.5498 11.1394 26.3698 12.2994 27.5898 12.7394C28.0498 12.8994 28.4798 13.0794 28.8698 13.2794C30.0498 13.8694 31.4698 13.6394 32.3898 12.7194L34.3598 10.7594L36.7598 13.1694L34.7198 15.1994C33.8198 16.0994 33.5698 17.4894 34.1098 18.6594C34.2898 19.0494 34.4598 19.4794 34.5998 19.8894C35.0198 21.1494 36.1798 21.9894 37.4898 21.9894H40.3798L40.3598 25.2594L44.3398 25.2294L44.3698 21.1094C44.3698 20.2994 44.0598 19.5394 43.4998 18.9494L43.5098 18.9594ZM36.6198 23.7194C35.8598 22.9594 34.6298 22.9594 33.8698 23.7194L28.0898 29.4994C27.3298 30.2594 27.3298 31.4894 28.0898 32.2494C28.8498 33.0094 30.0798 33.0094 30.8398 32.2494L33.2998 29.7894V42.4394C33.2998 43.5094 34.1698 44.3894 35.2498 44.3894C36.3298 44.3894 37.1998 43.5194 37.1998 42.4394V29.7894L39.6598 32.2494C40.4198 33.0094 41.6498 33.0094 42.4098 32.2494C43.1698 31.4894 43.1698 30.2594 42.4098 29.4994L36.6298 23.7194H36.6198ZM23.9998 20.0294C26.1898 20.0294 27.9698 21.8094 27.9698 23.9994H31.9698C31.9698 19.6094 28.3898 16.0294 23.9998 16.0294C19.6098 16.0294 16.0298 19.6094 16.0298 23.9994C16.0298 28.3894 19.6098 31.9694 23.9998 31.9694V27.9694C21.8098 27.9694 20.0298 26.1894 20.0298 23.9994C20.0298 21.8094 21.8098 20.0294 23.9998 20.0294ZZ`,
  ],
}

export const Terminal = {
  prefix: 'fal',
  iconName: 'terminal',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M40.01,7.54h0s-1.44,0-1.44,0c-.16,0-.32.06-.44.18-.12.12-.18.27-.18.44v1.53c0,.16.06.32.18.44.12.12.27.18.44.18h1.44c.16,0,.32-.06.44-.18.12-.12.18-.27.18-.44v-1.53c0-.16-.06-.32-.18-.44-.12-.12-.27-.18-.44-.18ZM35.86,7.54h0s-1.44,0-1.44,0c-.16,0-.32.06-.44.18-.12.12-.18.27-.18.44v1.53c0,.16.06.32.18.44.12.12.27.18.44.18h1.44c.16,0,.32-.06.44-.18.12-.12.18-.27.18-.44v-1.53c0-.16-.06-.32-.18-.44s-.27-.18-.44-.18ZM31.72,7.54h0s-1.44,0-1.44,0c-.16,0-.32.06-.44.18-.12.12-.18.27-.18.44v1.53c0,.16.06.32.18.44.12.12.27.18.44.18h1.44c.16,0,.32-.06.44-.18.12-.12.18-.27.18-.44v-1.53c0-.16-.06-.32-.18-.44-.12-.12-.27-.18-.44-.18ZM10.27,18.21c-.25-.22-.58-.34-.92-.34-.34,0-.66.15-.9.39-.24.24-.38.56-.39.9,0,.34.11.67.34.92l4.27,4.27-4.14,4.18c-.23.25-.35.58-.34.92.01.34.15.66.39.9.24.24.56.37.9.38.34,0,.67-.12.92-.34l5.06-5.1c.25-.25.38-.58.38-.93s-.14-.68-.38-.93l-5.2-5.2ZM44.69,4.84c-.84-.84-1.98-1.31-3.16-1.31H6.47c-1.19,0-2.32.47-3.16,1.31-.84.84-1.31,1.98-1.31,3.16v31.99c0,1.19.47,2.32,1.31,3.16s1.98,1.31,3.16,1.31h35.06c1.19,0,2.32-.47,3.16-1.31.84-.84,1.31-1.98,1.31-3.16V8.01c0-1.19-.47-2.32-1.31-3.16ZM43.85,39.99c0,.62-.25,1.21-.68,1.64-.44.44-1.03.68-1.64.68H6.47c-.62,0-1.21-.25-1.64-.68-.44-.44-.68-1.03-.68-1.64V14.82h36.3c.28,0,.56-.1.77-.29.21-.2.33-.47.34-.75,0-.28-.1-.56-.29-.77-.2-.21-.47-.33-.75-.34H4.15v-4.66c0-.62.25-1.21.68-1.64s1.03-.68,1.64-.68h35.06c.62,0,1.21.25,1.64.68.44.44.68,1.03.68,1.64v31.99ZM26.03,28.18h-7.04c-.35,0-.69.14-.93.39-.25.25-.39.58-.39.93s.14.69.39.93c.25.25.58.39.93.39h7.04c.35,0,.69-.14.93-.39.25-.25.39-.58.39-.93s-.14-.69-.39-.93c-.25-.25-.58-.39-.93-.39Z`,
  ],
}

export const HardwareDetails = {
  prefix: 'fal',
  iconName: 'hardware-details',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M28 18H20C19.4696 18 18.9609 18.2107 18.5858 18.5858C18.2107 18.9609 18 19.4696 18 20V28C18 28.5304 18.2107 29.0391 18.5858 29.4142C18.9609 29.7893 19.4696 30 20 30H28C28.5304 30 29.0391 29.7893 29.4142 29.4142C29.7893 29.0391 30 28.5304 30 28V20C30 19.4696 29.7893 18.9609 29.4142 18.5858C29.0391 18.2107 28.5304 18 28 18ZM26 26H22V22H26V26ZM42 26C42.5304 26 43.0391 25.7893 43.4142 25.4142C43.7893 25.0391 44 24.5304 44 24C44 23.4696 43.7893 22.9609 43.4142 22.5858C43.0391 22.2107 42.5304 22 42 22H38V18H42C42.5304 18 43.0391 17.7893 43.4142 17.4142C43.7893 17.0391 44 16.5304 44 16C44 15.4696 43.7893 14.9609 43.4142 14.5858C43.0391 14.2107 42.5304 14 42 14H37.64C37.3414 13.1612 36.8598 12.3994 36.2302 11.7698C35.6006 11.1402 34.8388 10.6586 34 10.36V6C34 5.46957 33.7893 4.96086 33.4142 4.58579C33.0391 4.21071 32.5304 4 32 4C31.4696 4 30.9609 4.21071 30.5858 4.58579C30.2107 4.96086 30 5.46957 30 6V10H26V6C26 5.46957 25.7893 4.96086 25.4142 4.58579C25.0391 4.21071 24.5304 4 24 4C23.4696 4 22.9609 4.21071 22.5858 4.58579C22.2107 4.96086 22 5.46957 22 6V10H18V6C18 5.46957 17.7893 4.96086 17.4142 4.58579C17.0391 4.21071 16.5304 4 16 4C15.4696 4 14.9609 4.21071 14.5858 4.58579C14.2107 4.96086 14 5.46957 14 6V10.36C13.1612 10.6586 12.3994 11.1402 11.7698 11.7698C11.1402 12.3994 10.6586 13.1612 10.36 14H6C5.46957 14 4.96086 14.2107 4.58579 14.5858C4.21071 14.9609 4 15.4696 4 16C4 16.5304 4.21071 17.0391 4.58579 17.4142C4.96086 17.7893 5.46957 18 6 18H10V22H6C5.46957 22 4.96086 22.2107 4.58579 22.5858C4.21071 22.9609 4 23.4696 4 24C4 24.5304 4.21071 25.0391 4.58579 25.4142C4.96086 25.7893 5.46957 26 6 26H10V30H6C5.46957 30 4.96086 30.2107 4.58579 30.5858C4.21071 30.9609 4 31.4696 4 32C4 32.5304 4.21071 33.0391 4.58579 33.4142C4.96086 33.7893 5.46957 34 6 34H10.36C10.6586 34.8388 11.1402 35.6006 11.7698 36.2302C12.3994 36.8598 13.1612 37.3414 14 37.64V42C14 42.5304 14.2107 43.0391 14.5858 43.4142C14.9609 43.7893 15.4696 44 16 44C16.5304 44 17.0391 43.7893 17.4142 43.4142C17.7893 43.0391 18 42.5304 18 42V38H22V42C22 42.5304 22.2107 43.0391 22.5858 43.4142C22.9609 43.7893 23.4696 44 24 44C24.5304 44 25.0391 43.7893 25.4142 43.4142C25.7893 43.0391 26 42.5304 26 42V38H30V42C30 42.5304 30.2107 43.0391 30.5858 43.4142C30.9609 43.7893 31.4696 44 32 44C32.5304 44 33.0391 43.7893 33.4142 43.4142C33.7893 43.0391 34 42.5304 34 42V37.64C34.8388 37.3414 35.6006 36.8598 36.2302 36.2302C36.8598 35.6006 37.3414 34.8388 37.64 34H42C42.5304 34 43.0391 33.7893 43.4142 33.4142C43.7893 33.0391 44 32.5304 44 32C44 31.4696 43.7893 30.9609 43.4142 30.5858C43.0391 30.2107 42.5304 30 42 30H38V26H42ZM34 32C34 32.5304 33.7893 33.0391 33.4142 33.4142C33.0391 33.7893 32.5304 34 32 34H16C15.4696 34 14.9609 33.7893 14.5858 33.4142C14.2107 33.0391 14 32.5304 14 32V16C14 15.4696 14.2107 14.9609 14.5858 14.5858C14.9609 14.2107 15.4696 14 16 14H32C32.5304 14 33.0391 14.2107 33.4142 14.5858C33.7893 14.9609 34 15.4696 34 16V32Z`,
  ],
}

export const HealthDiagnostics = {
  prefix: 'fal',
  iconName: 'health-diagnostics',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M26.4875 40.7498H12.4875C11.8975 40.7498 11.3175 40.5098 10.8975 40.0898C10.4775 39.6698 10.2375 39.0898 10.2375 38.4998V10.4998C10.2375 9.89977 10.4675 9.32977 10.8975 8.90977C11.3175 8.48977 11.8875 8.24977 12.4875 8.24977H22.7375V14.4998C22.7375 16.0398 23.3375 17.4798 24.4175 18.5698C25.5075 19.6598 26.9475 20.2498 28.4875 20.2498H34.7375V24.4998C34.7375 24.9598 34.9275 25.4098 35.2475 25.7398C35.8975 26.3898 37.0675 26.3898 37.7175 25.7398C38.0475 25.4098 38.2275 24.9598 38.2275 24.4998V18.3798C38.2075 18.2398 38.1775 18.0798 38.1175 17.9198V17.8398L38.1075 17.7198C38.0275 17.5598 37.9175 17.4098 37.7975 17.2698L25.8075 5.27977C25.6775 5.15977 25.5175 5.04977 25.3475 4.96977C25.3175 4.96977 25.2875 4.96977 25.2675 4.96977L25.1775 4.98977L25.0975 4.94977C24.9275 4.85977 24.7375 4.78977 24.5475 4.75977H12.4775C10.9375 4.75977 9.49754 5.35977 8.40754 6.44977C7.31754 7.53977 6.72754 8.97977 6.72754 10.5198V38.5198C6.72754 40.0298 7.33754 41.5198 8.40754 42.5898C9.49754 43.6798 10.9375 44.2698 12.4775 44.2698H26.4775C26.9375 44.2698 27.3875 44.0798 27.7175 43.7598C28.0475 43.4398 28.2275 42.9798 28.2275 42.5198C28.2275 42.0598 28.0375 41.6098 27.7175 41.2798C27.3975 40.9498 26.9375 40.7698 26.4775 40.7698L26.4875 40.7498ZM26.2375 10.7198L32.2675 16.7498H28.4875C27.8975 16.7498 27.3175 16.5098 26.8975 16.0898C26.4775 15.6698 26.2375 15.0898 26.2375 14.4998V10.7198ZM42.1175 37.8298C42.0275 37.6198 41.8975 37.4198 41.7375 37.2598C41.5775 37.0898 41.3875 36.9598 41.1675 36.8698C40.7375 36.6898 40.2475 36.6898 39.8175 36.8698C39.6075 36.9598 39.4075 37.0898 39.2475 37.2498L38.2375 38.2898V32.4898C38.2375 32.0298 38.0475 31.5798 37.7275 31.2498C37.0775 30.5998 35.9075 30.5998 35.2575 31.2498C34.9275 31.5798 34.7475 32.0298 34.7475 32.4898V38.2898L33.7375 37.2498C33.4175 36.9198 32.9575 36.7398 32.4975 36.7398C32.0375 36.7398 31.5875 36.9198 31.2575 37.2598C30.9275 37.5898 30.7475 38.0298 30.7475 38.4998C30.7475 38.9698 30.9275 39.4098 31.2575 39.7398L35.2575 43.7398C35.4175 43.8998 35.6175 44.0198 35.8275 44.0998C36.2675 44.2898 36.7375 44.2898 37.1575 44.0998C37.3875 44.0098 37.5775 43.8898 37.7475 43.7298L41.7475 39.7298C41.9175 39.5598 42.0375 39.3698 42.1275 39.1598C42.2175 38.9498 42.2575 38.7198 42.2575 38.4898C42.2575 38.2598 42.2075 38.0298 42.1275 37.8198L42.1175 37.8298ZM33.6775 27.7898C33.6775 27.0098 32.9975 26.2998 32.2175 26.2598H31.2075L30.0175 24.4698C29.7875 24.0798 29.3375 23.8198 28.7575 23.7298C28.2675 23.7298 27.8175 23.9298 27.5275 24.2998L26.8075 25.1798L25.4775 22.4598C25.2475 21.9198 24.6575 21.5598 24.0275 21.5598C23.4475 21.5598 22.9275 21.9598 22.6475 22.6198L20.9075 27.6398L19.7175 25.3198C19.4675 24.8198 18.9675 24.5098 18.4175 24.5098C17.8675 24.5098 17.3675 24.7598 17.0475 25.2498L16.2675 26.5498H14.2775C13.4375 26.5498 12.7475 27.2398 12.7475 28.0798C12.7475 28.9198 13.4575 29.6098 14.2775 29.6098H17.1475C17.4875 29.6098 17.7875 29.5198 18.0275 29.3598L18.2575 29.1998L19.8375 32.2398C20.0775 32.7298 20.6275 33.0498 21.2075 33.0498H21.4075C21.9375 32.9898 22.4075 32.5998 22.6675 31.9898L24.3275 27.0698L25.0975 28.6798C25.3275 29.1398 25.8075 29.4698 26.3575 29.5598C26.8775 29.5598 27.3775 29.3498 27.6675 28.9898L28.5475 27.8798L29.0575 28.6698C29.3975 29.0898 29.8775 29.3498 30.3175 29.3498H32.1375C32.9775 29.3498 33.6675 28.6598 33.6675 27.8198L33.6775 27.7898Z`,
  ],
}

export const HciIcon = {
  prefix: 'fal',
  iconName: 'hci-topology',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M8.85,25.7c0,.7.6,1.3,1.3,1.3s1.3-.6,1.3-1.3-.6-1.3-1.3-1.3-1.3.6-1.3,1.3ZM8.85,35.1c0,.7.6,1.3,1.3,1.3s1.3-.6,1.3-1.3-.6-1.3-1.3-1.3-1.3.6-1.3,1.3ZM5.85,18h-.1v-3.1c0-.7.5-1.2,1.2-1.2h34.4l-.5-1c0-.1,0-.2-.1-.3v-.2l-4.8-7.7c-.9-1.5-2.5-2.4-4.2-2.4H11.75c-1.7,0-3.3.9-4.2,2.4l-4.9,7.8v.2c-.4.7-.6,1.6-.6,2.4v3.1c0,.8.2,1.6.6,2.3.2.3.3.6.5.8-.2.3-.4.5-.5.8-.4.7-.6,1.5-.6,2.3v3.1c0,.8.2,1.6.6,2.3.2.3.3.6.5.8-.2.3-.4.5-.5.8-.4.7-.6,1.5-.6,2.3v3.1c0,2.8,2.2,5,5,5h7.4v-3.8h-7.4c-.7,0-1.2-.5-1.2-1.2v-3.1c0-.7.5-1.2,1.2-1.2h7.4v-3.8h-7.4c-.7,0-1.2-.5-1.2-1.2v-3.1c0-.7.5-1.2,1.2-1.2h7.4v-3.8h-7.4c-.7,0-1.2-.5-1.2-1.2ZM10.75,6.5c.2-.4.6-.6,1-.6h19.8c.4,0,.8.2,1,.6l2.1,3.4H8.65l2.1-3.4ZM32.31,30.23c.27-.16.68-.17.95-.02.11.06.2.16.27.3l.11.22h1.62l-.21-.54c-.18-.46-.48-.83-.88-1.08-.73-.47-1.77-.51-2.57-.06-.37.21-.67.5-.89.87-.22.37-.33.79-.33,1.25s.11.88.33,1.26c.22.37.52.66.89.87.37.21.78.31,1.22.31.5,0,.95-.13,1.34-.37.41-.25.71-.62.89-1.08l.21-.54h-1.62l-.11.22c-.07.14-.16.24-.27.3-.26.15-.68.14-.94-.02-.14-.09-.26-.2-.34-.36-.08-.16-.13-.36-.13-.59s.04-.42.12-.59c.08-.16.19-.27.34-.35ZM8.85,16.4c0,.7.6,1.3,1.3,1.3s1.3-.6,1.3-1.3-.6-1.3-1.3-1.3-1.3.6-1.3,1.3ZM36.01,33.57h1.46v-4.79h-1.46v4.79ZM44.5,33.35c.51,0,1.02-.21,1.38-.57s.57-.86.57-1.38-.2-1.01-.57-1.38-.87-.57-1.38-.57h-2.4v-1.9h2.4c.51,0,1.02-.21,1.38-.57s.57-.86.57-1.38-.2-1.01-.57-1.38c-.36-.36-.87-.57-1.38-.57h-2.82c-.24-.55-.58-1.05-1.01-1.47-.42-.42-.92-.77-1.47-1.01v-2.82c0-.52-.2-1.01-.57-1.38-.73-.73-2.03-.73-2.76,0-.37.37-.57.86-.57,1.38v2.4h-1.9v-2.4c0-.52-.2-1.01-.57-1.38-.74-.74-2.02-.74-2.76,0-.37.37-.57.86-.57,1.38v2.4h-1.9v-2.4c0-.52-.2-1.01-.57-1.38-.74-.74-2.02-.74-2.76,0-.37.37-.57.86-.57,1.38v2.82c-.55.24-1.05.59-1.47,1.01-.42.42-.77.92-1.01,1.47h-2.82c-.52,0-1.01.2-1.38.57-.37.37-.57.86-.57,1.38s.2,1.01.57,1.38c.37.37.86.57,1.38.57h2.4v1.9h-2.4c-.52,0-1.01.2-1.38.57-.37.37-.57.86-.57,1.38s.2,1.01.57,1.38c.37.37.86.57,1.38.57h2.4v1.9h-2.4c-.52,0-1.01.2-1.38.57-.37.37-.57.86-.57,1.38s.21,1.02.57,1.38.86.57,1.38.57h2.82c.24.55.59,1.05,1.01,1.47s.92.77,1.47,1.01v2.82c0,.51.21,1.02.57,1.38.74.74,2.02.74,2.76,0,.36-.36.57-.87.57-1.38v-2.4h1.9v2.4c0,.51.21,1.02.57,1.38.74.74,2.02.74,2.76,0,.36-.36.57-.87.57-1.38v-2.4h1.9v2.4c0,.51.21,1.02.57,1.38s.86.57,1.38.57,1.01-.2,1.38-.57c.37-.37.57-.86.57-1.38v-2.82c.54-.24,1.05-.58,1.47-1.01.42-.42.77-.93,1.01-1.47h2.82c.52,0,1.01-.2,1.38-.57.37-.37.57-.86.57-1.38s-.2-1.01-.57-1.38-.87-.57-1.38-.57h-2.4v-1.9h2.4ZM38.2,37.2c0,.25-.1.49-.28.67-.18.18-.42.28-.67.28h-11.6c-.25,0-.49-.1-.67-.28-.18-.18-.28-.42-.28-.67v-11.6c0-.25.1-.49.28-.67s.42-.28.67-.28h11.6c.25,0,.5.1.67.28.18.18.28.42.28.67v11.6ZM28.03,30.49h-1.08v-1.71h-1.46v4.79h1.46v-1.75h1.08v1.75h1.46v-4.79h-1.46v1.71Z`,
  ],
}

export const runbook = {
  prefix: 'fal',
  iconName: 'runbook',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M39.3 21.5999C38.7 21.5999 38 21.5999 37.4 21.8999V17.7999C37.4 16.0999 36 14.6999 34.3 14.6999H30.5C30.6 14.1999 30.7 13.5999 30.7 13.0999C30.7 9.3999 27.7 6.3999 24 6.3999C20.3 6.3999 17.3 9.3999 17.3 13.0999C17.3 16.7999 17.3 14.1999 17.5 14.6999H13.6C11.9 14.6999 10.5 16.0999 10.5 17.7999V21.8999C9.9 21.6999 9.3 21.6999 8.7 21.6999C5 21.6999 2 24.6999 2 28.3999C2 32.0999 5 35.0999 8.7 35.0999C12.4 35.0999 9.9 35.0999 10.5 34.8999V38.6999C10.5 40.3999 11.9 41.7999 13.6 41.7999H20.3C21 41.7999 21.6 41.3999 21.9 40.7999C22.2 40.1999 22.1 39.3999 21.6 38.8999C21.1 38.2999 20.8 37.5999 20.8 36.7999C20.8 35.0999 22.2 33.5999 24 33.5999C25.8 33.5999 27.2 34.9999 27.2 36.7999C27.2 38.5999 26.9 38.2999 26.4 38.8999C25.9 39.3999 25.8 40.1999 26.1 40.7999C26.4 41.3999 27 41.7999 27.7 41.7999H34.3C36 41.7999 37.4 40.3999 37.4 38.6999V34.8999C38 35.0999 38.6 35.1999 39.3 35.1999C43 35.1999 46 32.1999 46 28.4999C46 24.7999 43 21.7999 39.3 21.7999V21.5999ZM39.3 31.4999C38.4 31.4999 37.6 31.0999 37 30.4999C36.5 29.9999 35.7 29.7999 35.1 29.9999C34.5 30.1999 34 30.8999 34 31.5999V38.0999H30.7C30.8 37.5999 30.9 37.0999 30.9 36.5999C30.9 32.8999 27.9 29.8999 24.2 29.8999C20.5 29.8999 17.5 32.8999 17.5 36.5999C17.5 40.2999 17.5 37.5999 17.7 38.0999H14.2V31.6999C14.2 30.9999 13.8 30.2999 13.1 30.0999C12.4 29.7999 11.7 29.9999 11.2 30.4999C10.6 31.0999 9.8 31.4999 8.9 31.4999C7.2 31.4999 5.7 30.0999 5.7 28.2999C5.7 26.4999 7.1 25.0999 8.9 25.0999C10.7 25.0999 10.6 25.3999 11.2 26.0999C11.7 26.5999 12.5 26.7999 13.1 26.4999C13.8 26.1999 14.2 25.5999 14.2 24.8999V18.1999H20.7C21.4 18.1999 22 17.7999 22.3 17.0999C22.6 16.4999 22.4 15.6999 22 15.1999C21.4 14.5999 21.1 13.7999 21.1 12.9999C21.1 11.2999 22.5 9.7999 24.3 9.7999C26.1 9.7999 27.5 11.1999 27.5 12.9999C27.5 14.7999 27.2 14.5999 26.6 15.1999C26.1 15.6999 26 16.4999 26.3 17.0999C26.6 17.7999 27.2 18.1999 27.9 18.1999H34.2V24.9999C34.2 25.6999 34.6 26.3999 35.3 26.6999C36 26.9999 36.8 26.6999 37.2 26.1999C37.8 25.4999 38.6 25.1999 39.5 25.1999C41.2 25.1999 42.7 26.5999 42.7 28.3999C42.7 30.1999 41.3 31.5999 39.5 31.5999L39.3 31.4999Z`,
  ],
}

export const Sites = {
  prefix: 'fal',
  iconName: 'sites',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M40,2H8c-3.31,0-6,2.69-6,6v8c0,3.31,2.69,6,6,6h32c3.31,0,6-2.69,6-6v-8c0-3.31-2.69-6-6-6ZM42,16c0,1.1-.9,2-2,2H8c-1.1,0-2-.9-2-2v-8c0-1.1.9-2,2-2h32c1.1,0,2,.9,2,2v8ZM12.02,10h-.02c-1.1,0-1.99.9-1.99,2s.91,2,2.01,2,2-.9,2-2-.9-2-2-2ZM12.02,34h-.02c-1.1,0-1.99.89-1.99,2s.91,2,2.01,2,2-.89,2-2-.9-2-2-2ZM40,26H8c-3.31,0-6,2.69-6,6v8c0,3.31,2.69,6,6,6h32c3.31,0,6-2.69,6-6v-8c0-3.31-2.69-6-6-6ZM42,40c0,1.1-.9,2-2,2H8c-1.1,0-2-.9-2-2v-8c0-1.1.9-2,2-2h32c1.1,0,2,.9,2,2v8Z`,
  ],
}

export const Manager = {
  prefix: 'fal',
  iconName: 'manager',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M43.3,21.99h-18.49v-3.98h2.41c.94,0,1.71-.76,1.71-1.71v-7.81c0-.94-.76-1.71-1.71-1.71h-6.65c-.94,0-1.71.76-1.71,1.71v7.81c0,.94.76,1.71,1.71,1.71h2.41v3.98H4.7c-.94,0-1.71.76-1.71,1.71v5.6c0,.94.76,1.71,1.71,1.71h3.36v5.63c-.86.36-1.46,1.21-1.46,2.2,0,1.32,1.07,2.38,2.38,2.38s2.38-1.07,2.38-2.38c0-.99-.6-1.84-1.46-2.2v-5.63h8.12v5.63c-.86.36-1.46,1.21-1.46,2.2,0,1.32,1.07,2.38,2.38,2.38s2.38-1.07,2.38-2.38c0-.99-.6-1.84-1.46-2.2v-5.63h8.12v5.63c-.86.36-1.46,1.21-1.46,2.2,0,1.32,1.07,2.38,2.38,2.38s2.38-1.07,2.38-2.38c0-.99-.6-1.84-1.46-2.2v-5.63h8.12v5.63c-.86.36-1.46,1.21-1.46,2.2,0,1.32,1.07,2.38,2.38,2.38s2.38-1.07,2.38-2.38c0-.99-.6-1.84-1.46-2.2v-5.63h3.51c.94,0,1.71-.76,1.71-1.71v-5.6c0-.94-.76-1.71-1.71-1.71Z`,
  ],
}

export const Controller = {
  prefix: 'fal',
  iconName: 'controller',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M44.85,19.26c-.52-1.62-2.25-2.52-3.87-2-1.2.38-1.99,1.43-2.12,2.6l-6.47,2.06c-1.32-2.77-3.96-4.77-7.11-5.2v-6.79c1.08-.48,1.83-1.56,1.83-2.81,0-1.7-1.38-3.08-3.08-3.08s-3.08,1.38-3.08,3.08c0,1.26.75,2.33,1.83,2.81v6.8c-3.19.43-5.85,2.48-7.15,5.29l-6.49-1.99c-.14-1.17-.95-2.21-2.15-2.58-1.63-.5-3.35.42-3.85,2.04-.5,1.63.42,3.35,2.04,3.85,1.2.37,2.45-.04,3.22-.93l6.5,1.99c-.08.49-.13.98-.13,1.49,0,2.57,1.05,4.9,2.74,6.58l-4.09,5.42c-1.15-.27-2.4.14-3.15,1.14-1.02,1.36-.75,3.29.6,4.32,1.36,1.02,3.29.75,4.31-.6.76-1,.8-2.32.23-3.35l4.09-5.42c1.34.75,2.88,1.19,4.53,1.19s3.21-.44,4.55-1.2l4.1,5.41c-.57,1.03-.52,2.35.24,3.35,1.03,1.36,2.96,1.62,4.32.59s1.62-2.96.59-4.32c-.76-1-2.01-1.4-3.16-1.13l-4.1-5.41c1.68-1.68,2.72-4,2.72-6.56,0-.54-.06-1.08-.15-1.6l6.47-2.06c.78.88,2.04,1.27,3.23.89,1.62-.52,2.52-2.25,2-3.87h.01Z`,
  ],
}

export const Validator = {
  prefix: 'fal',
  iconName: 'validator',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M43.65,21.65h-1.56c-1.15-8.92-8.79-15.83-18.02-15.83S7.23,12.73,6.07,21.65h-1.72c-1.3,0-2.35,1.05-2.35,2.35s1.05,2.35,2.35,2.35h1.73c1.15,8.91,8.78,15.83,18.01,15.83s16.84-6.91,18.01-15.83h1.56c1.3,0,2.35-1.05,2.35-2.35s-1.05-2.35-2.35-2.35ZM37.32,26.35h2.13c-1.14,7.46-7.59,13.21-15.37,13.21s-14.22-5.74-15.37-13.21h1.96c1.3,0,2.35-1.05,2.35-2.35s-1.05-2.35-2.35-2.35h-1.96c1.14-7.46,7.59-13.21,15.37-13.21s14.22,5.74,15.37,13.21h-2.13c-1.3,0-2.35,1.05-2.35,2.35s1.05,2.35,2.35,2.35Z`,
  ],
}

export const WanEdges = {
  prefix: 'fal',
  iconName: 'wan-edges',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M31.05,20.01l10.66-10.67-3.65-3.65-10.66,10.66-1.92-1.92v7.49h7.48l-1.91-1.91ZM27.12,23.93l-3.65,3.65,10.66,10.67-1.92,1.92h7.49v-7.49l-1.92,1.92-10.66-10.67ZM32.97,21.93h0,0s0,0,0,0ZM16.38,27.38l-10.67,10.66,3.65,3.65,10.67-10.66,1.92,1.92v-7.49h-7.49l1.92,1.92ZM20.34,24.45h0s3.65-3.64,3.65-3.64l-10.66-10.67,1.92-1.92h-7.49v7.48l1.92-1.92,10.66,10.67Z`,
  ],
}

export const Tunnel = {
  prefix: 'fal',
  iconName: 'tunnel',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M29.64,31.72c-.53-.68-1.53-3.38-1.53-7.72s.99-7.04,1.53-7.72l.02-.02c.37.46.92,1.86,1.26,4h2.36c-.21-1.54-.52-2.97-.92-4.1-.64-1.79-1.49-2.99-2.45-3.16H12.71c-1.14,0-2.61,2.8-2.61,10.98s1.46,11,2.61,11.02h17.18c.96-.19,1.81-1.38,2.45-3.16.37-1.04.67-2.32.87-3.71h-2.38c-.34,1.93-.85,3.16-1.19,3.59ZM7.67,22.77H2v3.02h5.68c-.04-.58-.04-1.16-.04-1.79,0-.43.02-.82.02-1.23ZM35.11,19.03s1.07,2.07,1.07,3.64h-5.88v3.02h5.83c-.32,1.91-1.03,3.09-1.03,3.09l10.89-4.87h.02l-10.89-4.87Z`,
  ],
}

export const BFDSessions = {
  prefix: 'fal',
  iconName: 'bfd-sessions',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M19.11,22.98c0-1-.8-1.8-1.8-1.8H5.8c-1-.02-1.8,.8-1.8,1.8s.8,1.8,1.8,1.8h11.49c1,0,1.82-.8,1.82-1.8Zm.25,5.64c0-1-.8-1.8-1.8-1.8H8.24c-1,0-1.8,.8-1.8,1.8s.8,1.8,1.8,1.8h9.33c1,0,1.8-.8,1.8-1.8ZM29.8,11.38v-3.05h1.36c1,0,1.8-.8,1.8-1.8s-.8-1.82-1.8-1.82h-6.33c-1,0-1.8,.8-1.8,1.8s.8,1.8,1.8,1.8h1.36v3.07c-3.31,.38-6.44,1.76-8.93,4.02-.75,.67-.8,1.82-.13,2.55,.67,.75,1.82,.8,2.55,.13,2.27-2.05,5.22-3.18,8.29-3.18,6.84,0,12.4,5.56,12.4,12.4s-5.56,12.4-12.4,12.4c-4.44,0-8.56-2.4-10.78-6.27-.47-.87-1.58-1.18-2.45-.67-.87,.49-1.16,1.6-.67,2.45,2.85,4.98,8.18,8.09,13.91,8.09,8.84,0,16.02-7.18,16.02-16,0-8.22-6.22-15-14.2-15.91Zm5.49,12.95c.75-.65,.82-1.8,.16-2.55-.65-.75-1.8-.82-2.55-.16l-5.51,4.84-2.02-2.07c-.71-.71-1.84-.73-2.55-.04-.71,.69-.73,1.84-.04,2.55l4.42,4.51,8.07-7.07Z`,
  ],
}

export const Fair = {
  prefix: 'fal',
  iconName: 'fair',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24.02,28.01c1.1,0,2-.9,2-2v-8c0-1.1-.9-2-2-2s-2,.9-2,2v8c0,1.1.9,2,2,2ZM24.04,32.01h-.02c-1.1,0-1.99.9-1.99,2s.91,2,2.01,2,2-.9,2-2-.9-2-2-2ZM45.22,35.01L29.22,7.02c-.52-.92-1.28-1.69-2.2-2.23-1.83-1.07-4.21-1.07-6.04,0-.92.53-1.68,1.3-2.2,2.22L2.79,35.01c-.53.92-.81,1.96-.8,3.01,0,1.06.28,2.1.82,3.01.53.91,1.3,1.67,2.22,2.19.9.51,1.93.78,2.94.78h32.06c1.05,0,2.09-.28,3-.81.91-.53,1.67-1.29,2.19-2.2.53-.91.8-1.95.8-3,0-1.05-.28-2.08-.8-3ZM41.75,39.01c-.17.3-.43.56-.73.73-.3.18-.65.27-1,.27H8c-.34,0-.7-.09-1-.26-.31-.17-.56-.43-.74-.73-.18-.3-.27-.65-.27-1,0-.35.09-.7.27-1.01L22.26,8.99c.17-.31.43-.56.73-.74.31-.18.65-.27,1.01-.27s.7.09,1.01.27c.31.18.56.44.74.75l16,28s0,0,0,0c.17.3.27.65.27,1s-.09.69-.27,1Z`,
  ],
}

export const DeclareIncident = {
  prefix: 'fal',
  iconName: 'declare-incident',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M18 20C17.4696 20 16.9609 20.2107 16.5858 20.5858C16.2107 20.9609 16 21.4696 16 22V26C16 26.5304 16.2107 27.0391 16.5858 27.4142C16.9609 27.7893 17.4696 28 18 28C18.5304 28 19.0391 27.7893 19.4142 27.4142C19.7893 27.0391 20 26.5304 20 26V22C20 21.4696 19.7893 20.9609 19.4142 20.5858C19.0391 20.2107 18.5304 20 18 20ZM42 22C42.5304 22 43.0391 21.7893 43.4142 21.4142C43.7893 21.0391 44 20.5304 44 20V12C44 11.4696 43.7893 10.9609 43.4142 10.5858C43.0391 10.2107 42.5304 10 42 10H6C5.46957 10 4.96086 10.2107 4.58579 10.5858C4.21071 10.9609 4 11.4696 4 12V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22C6.53043 22 7.03914 22.2107 7.41421 22.5858C7.78929 22.9609 8 23.4696 8 24C8 24.5304 7.78929 25.0391 7.41421 25.4142C7.03914 25.7893 6.53043 26 6 26C5.46957 26 4.96086 26.2107 4.58579 26.5858C4.21071 26.9609 4 27.4696 4 28V36C4 36.5304 4.21071 37.0391 4.58579 37.4142C4.96086 37.7893 5.46957 38 6 38H42C42.5304 38 43.0391 37.7893 43.4142 37.4142C43.7893 37.0391 44 36.5304 44 36V28C44 27.4696 43.7893 26.9609 43.4142 26.5858C43.0391 26.2107 42.5304 26 42 26C41.4696 26 40.9609 25.7893 40.5858 25.4142C40.2107 25.0391 40 24.5304 40 24C40 23.4696 40.2107 22.9609 40.5858 22.5858C40.9609 22.2107 41.4696 22 42 22ZM40 18.36C38.8417 18.7804 37.8409 19.5473 37.1337 20.5563C36.4264 21.5654 36.047 22.7678 36.047 24C36.047 25.2322 36.4264 26.4346 37.1337 27.4437C37.8409 28.4527 38.8417 29.2196 40 29.64V34H20C20 33.4696 19.7893 32.9609 19.4142 32.5858C19.0391 32.2107 18.5304 32 18 32C17.4696 32 16.9609 32.2107 16.5858 32.5858C16.2107 32.9609 16 33.4696 16 34H8V29.64C9.1583 29.2196 10.1591 28.4527 10.8663 27.4437C11.5736 26.4346 11.953 25.2322 11.953 24C11.953 22.7678 11.5736 21.5654 10.8663 20.5563C10.1591 19.5473 9.1583 18.7804 8 18.36V14H16C16 14.5304 16.2107 15.0391 16.5858 15.4142C16.9609 15.7893 17.4696 16 18 16C18.5304 16 19.0391 15.7893 19.4142 15.4142C19.7893 15.0391 20 14.5304 20 14H40V18.36Z`,
  ],
}

export const CiscoMerakiDevices = {
  prefix: 'fal',
  iconName: 'cisco-meraki-devices',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M27.3003 22.3381C26.6903 22.3381 26.1303 22.6781 25.8503 23.2181L23.5303 27.6381L21.2003 23.1581C20.9403 22.6581 20.4203 22.3381 19.8503 22.3381C18.8803 22.3381 18.0903 23.1281 18.0903 24.0981V31.8081C18.0903 32.5381 18.6803 33.1281 19.4103 33.1281C20.1403 33.1281 20.7803 32.5181 20.7803 31.7581V27.6981C21.4303 29.0281 22.3303 30.7981 22.7203 31.2881C22.8003 31.3981 23.0603 31.7281 23.4803 31.7481C23.7103 31.7581 24.0203 31.6881 24.3903 31.2281L26.2903 27.5881V31.7981C26.2903 32.5281 26.8903 33.1281 27.6203 33.1281C28.3503 33.1281 28.9803 32.5181 28.9803 31.7681V24.0181C28.9803 23.0881 28.2303 22.3381 27.3003 22.3381ZM39.1903 19.3081C37.4103 13.7281 32.2003 9.82812 26.2103 9.82812C21.5503 9.82812 17.2103 12.2481 14.7303 16.1081C14.3403 16.0481 13.9403 16.0081 13.5503 16.0081C10.0303 16.0081 7.09031 18.5681 6.51031 21.9381C2.84031 22.6281 0.0703125 25.8481 0.0703125 29.7081C0.0703125 34.6981 3.26031 38.1781 7.84031 38.1781L38.5303 38.1081C43.7203 38.1081 47.9403 33.8881 47.9403 28.6981C47.9403 23.5081 44.0703 19.6481 39.1903 19.3081ZM38.5303 34.1081L7.84031 34.1781C5.52031 34.1781 4.07031 32.4681 4.07031 29.7081C4.07031 27.5481 5.83031 25.7981 7.98031 25.7981C8.08031 25.7981 8.19031 25.7981 8.31031 25.8181L10.8903 25.9881L10.4103 23.4481C10.3903 23.3581 10.3903 23.2581 10.3903 23.1681C10.3903 21.4281 11.8003 20.0081 13.5403 20.0081C14.0003 20.0081 14.4503 20.1081 14.8503 20.2881L16.6503 21.1181L17.5003 19.3281C19.0803 15.9881 22.5003 13.8281 26.2103 13.8281C30.8603 13.8281 34.8403 17.1381 35.6803 21.7081L36.0303 23.5981L37.9303 23.3281C38.1303 23.2981 38.3303 23.2881 38.5303 23.2881C41.5103 23.2881 43.9403 25.7181 43.9403 28.6981C43.9403 31.6781 41.5103 34.1081 38.5303 34.1081Z`,
  ],
}

export const PerfScore = {
  prefix: 'fal',
  iconName: 'perf-score',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M30.8183 20.6894L25.2583 26.2494C24.9254 26.1609 24.5828 26.1138 24.2383 26.1094C23.1775 26.1094 22.1601 26.5308 21.4099 27.2809C20.6598 28.0311 20.2383 29.0485 20.2383 30.1094C20.2428 30.4538 20.2898 30.7964 20.3783 31.1294L18.8183 32.6894C18.6309 32.8753 18.4821 33.0965 18.3806 33.3402C18.279 33.5839 18.2267 33.8454 18.2267 34.1094C18.2267 34.3734 18.279 34.6348 18.3806 34.8785C18.4821 35.1222 18.6309 35.3434 18.8183 35.5294C19.0043 35.7168 19.2255 35.8656 19.4692 35.9672C19.7129 36.0687 19.9743 36.121 20.2383 36.121C20.5024 36.121 20.7638 36.0687 21.0075 35.9672C21.2512 35.8656 21.4724 35.7168 21.6583 35.5294L23.2183 33.9694C23.5513 34.0579 23.8939 34.1049 24.2383 34.1094C25.2992 34.1094 26.3166 33.6879 27.0668 32.9378C27.8169 32.1877 28.2383 31.1702 28.2383 30.1094C28.2339 29.7649 28.1869 29.4223 28.0983 29.0894L33.6583 23.5294C34.0349 23.1528 34.2465 22.642 34.2465 22.1094C34.2465 21.5768 34.0349 21.066 33.6583 20.6894C33.2817 20.3128 32.7709 20.1012 32.2383 20.1012C31.7057 20.1012 31.1949 20.3128 30.8183 20.6894V20.6894ZM24.2383 8.10938C21.6119 8.10938 19.0112 8.62669 16.5847 9.63178C14.1582 10.6369 11.9534 12.1101 10.0962 13.9672C6.34548 17.718 4.23834 22.805 4.23834 28.1094C4.22849 32.0705 5.40579 35.9437 7.61834 39.2294C7.91539 39.6696 8.37515 39.9739 8.8965 40.0751C9.41786 40.1764 9.95808 40.0664 10.3983 39.7694C10.8386 39.4723 11.1428 39.0126 11.2441 38.4912C11.3454 37.9699 11.2354 37.4296 10.9383 36.9894C9.32738 34.5805 8.4009 31.7792 8.25785 28.8848C8.1148 25.9904 8.76053 23.1115 10.1261 20.5554C11.4917 17.9994 13.5258 15.8622 16.0113 14.3721C18.4969 12.8821 21.3404 12.095 24.2383 12.095C27.1363 12.095 29.9798 12.8821 32.4653 14.3721C34.9509 15.8622 36.985 17.9994 38.3506 20.5554C39.7162 23.1115 40.3619 25.9904 40.2188 28.8848C40.0758 31.7792 39.1493 34.5805 37.5383 36.9894C37.3909 37.2073 37.288 37.4521 37.2353 37.7098C37.1827 37.9676 37.1814 38.2332 37.2316 38.4914C37.2817 38.7496 37.3823 38.9955 37.5276 39.2147C37.6729 39.434 37.8601 39.6225 38.0783 39.7694C38.4094 39.9919 38.7995 40.1103 39.1983 40.1094C39.5259 40.1099 39.8487 40.0299 40.1381 39.8764C40.4276 39.723 40.6749 39.5008 40.8583 39.2294C43.0709 35.9437 44.2482 32.0705 44.2383 28.1094C44.2383 22.805 42.1312 17.718 38.3805 13.9672C34.6298 10.2165 29.5427 8.10938 24.2383 8.10938V8.10938Z`,
  ],
}

export const LinkAlt = {
  prefix: 'fal',
  iconName: 'link-alt',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M32.4 12C32.4 10.8954 33.2954 10 34.4 10C35.6735 10 36.9249 10.2559 38.1428 10.743C39.4682 11.2732 40.4884 12.06 41.4142 12.9858C42.34 13.9116 43.1268 14.9318 43.657 16.2572C44.1441 17.4751 44.4 18.7265 44.4 20C44.4 21.2735 44.1441 22.5249 43.657 23.7428C43.1334 25.0516 42.3597 26.0628 41.449 26.9793L39.4799 29.1453C39.4585 29.1688 39.4366 29.1918 39.4142 29.2142L33.0142 35.6142C32.0884 36.54 31.0682 37.3268 29.7428 37.857C28.5249 38.3441 27.2735 38.6 26 38.6C24.7265 38.6 23.4751 38.3441 22.2572 37.857C20.9318 37.3268 19.9116 36.54 18.9858 35.6142C18.06 34.6884 17.2732 33.6682 16.743 32.3428C16.2465 31.1014 16 29.8489 16 28.4C16 27.1265 16.2559 25.8751 16.743 24.6572C17.2732 23.3318 18.06 22.3116 18.9858 21.3858L21.1858 19.1858C21.9668 18.4047 23.2332 18.4047 24.0142 19.1858C24.7953 19.9668 24.7953 21.2332 24.0142 22.0142L21.8142 24.2142C21.14 24.8884 20.7268 25.4682 20.457 26.1428C20.1441 26.9249 20 27.6735 20 28.4C20 29.3511 20.1535 30.0986 20.457 30.8572C20.7268 31.5318 21.14 32.1116 21.8142 32.7858C22.4884 33.46 23.0682 33.8732 23.7428 34.143C24.5249 34.4559 25.2735 34.6 26 34.6C26.7265 34.6 27.4751 34.4559 28.2572 34.143C28.9318 33.8732 29.5116 33.46 30.1858 32.7858L36.5521 26.4194L38.5201 24.2547C38.5415 24.2312 38.5634 24.2082 38.5858 24.1858C39.26 23.5116 39.6732 22.9318 39.943 22.2572C40.2559 21.4751 40.4 20.7265 40.4 20C40.4 19.2735 40.2559 18.5249 39.943 17.7428C39.6732 17.0682 39.26 16.4884 38.5858 15.8142C37.9116 15.14 37.3318 14.7268 36.6572 14.457C35.8751 14.1441 35.1265 14 34.4 14C33.2954 14 32.4 13.1046 32.4 12ZM22.6 15C21.8735 15 21.1249 15.1441 20.3428 15.457C19.6682 15.7268 19.0884 16.14 18.4142 16.8142L11.8479 23.3806L9.87988 25.5453C9.85854 25.5688 9.83665 25.5918 9.81421 25.6142C9.13999 26.2884 8.72678 26.8682 8.45695 27.5428C8.14411 28.3249 8 29.0735 8 29.8C8 30.5265 8.14411 31.2751 8.45695 32.0572C8.72678 32.7318 9.13999 33.3116 9.81421 33.9858C10.4884 34.66 11.0682 35.0732 11.7428 35.343C12.5249 35.6559 13.2735 35.8 14 35.8C15.1046 35.8 16 36.6954 16 37.8C16 38.9046 15.1046 39.8 14 39.8C12.7265 39.8 11.4751 39.5441 10.2572 39.057C8.93179 38.5268 7.91156 37.74 6.98579 36.8142C6.06001 35.8884 5.27322 34.8682 4.74305 33.5428C4.25589 32.3249 4 31.0735 4 29.8C4 28.5265 4.25589 27.2751 4.74305 26.0572C5.26657 24.7484 6.04032 23.7372 6.95102 22.8207L8.92012 20.6547C8.94146 20.6312 8.96335 20.6082 8.98579 20.5858L15.5858 13.9858C16.5116 13.06 17.5318 12.2732 18.8572 11.743C20.0751 11.2559 21.3265 11 22.6 11C23.8735 11 25.1249 11.2559 26.3428 11.743C27.6682 12.2732 28.6884 13.06 29.6142 13.9858C30.54 14.9116 31.3268 15.9318 31.857 17.2572C32.3441 18.4751 32.6 19.7265 32.6 21C32.6 22.2735 32.3441 23.5249 31.857 24.7428C31.3329 26.053 30.558 27.065 29.646 27.9824L27.4743 30.3514C26.7279 31.1657 25.4628 31.2207 24.6486 30.4743C23.8343 29.7279 23.7793 28.4628 24.5257 27.6486L26.7257 25.2486C26.7453 25.2272 26.7653 25.2063 26.7858 25.1858C27.46 24.5116 27.8732 23.9318 28.143 23.2572C28.4559 22.4751 28.6 21.7265 28.6 21C28.6 20.2735 28.4559 19.5249 28.143 18.7428C27.8732 18.0682 27.46 17.4884 26.7858 16.8142C26.1116 16.14 25.5318 15.7268 24.8572 15.457C24.0751 15.1441 23.3265 15 22.6 15Z`,
  ],
}

export const MoveUp = {
  prefix: 'fal',
  iconName: 'move-up',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M17.9922 36.5059C16.3356 36.5059 14.9922 37.8493 14.9922 39.5059C14.9922 41.1624 16.3356 42.5059 17.9922 42.5059C19.6488 42.5059 20.9922 41.1624 20.9922 39.5059C20.9922 37.8493 19.6488 36.5059 17.9922 36.5059ZM17.9922 21.5059C16.3356 21.5059 14.9922 22.8493 14.9922 24.5059C14.9922 26.1624 16.3356 27.5059 17.9922 27.5059C19.6488 27.5059 20.9922 26.1624 20.9922 24.5059C20.9922 22.8493 19.6488 21.5059 17.9922 21.5059ZM29.9922 12.5059C31.6488 12.5059 32.9922 11.1624 32.9922 9.50586C32.9922 7.8493 31.6488 6.50586 29.9922 6.50586C28.3356 6.50586 26.9922 7.8493 26.9922 9.50586C26.9922 11.1624 28.3356 12.5059 29.9922 12.5059ZM29.9922 21.5059C28.3356 21.5059 26.9922 22.8493 26.9922 24.5059C26.9922 26.1624 28.3356 27.5059 29.9922 27.5059C31.6488 27.5059 32.9922 26.1624 32.9922 24.5059C32.9922 22.8493 31.6488 21.5059 29.9922 21.5059ZM17.9922 6.50586C16.3356 6.50586 14.9922 7.8493 14.9922 9.50586C14.9922 11.1624 16.3356 12.5059 17.9922 12.5059C19.6488 12.5059 20.9922 11.1624 20.9922 9.50586C20.9922 7.8493 19.6488 6.50586 17.9922 6.50586ZM29.9922 36.5059C28.3356 36.5059 26.9922 37.8493 26.9922 39.5059C26.9922 41.1624 28.3356 42.5059 29.9922 42.5059C31.6488 42.5059 32.9922 41.1624 32.9922 39.5059C32.9922 37.8493 31.6488 36.5059 29.9922 36.5059Z`,
  ],
}

export const RightArrowFaceRight = {
  prefix: 'fal',
  iconName: 'right-arrow-face-right',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24.5858 10.5858C25.3668 9.80474 26.6332 9.80474 27.4142 10.5858L39.4142 22.5858C39.7893 22.9609 40 23.4696 40 24C40 24.5304 39.7893 25.0391 39.4142 25.4142L27.4142 37.4142C26.6332 38.1953 25.3668 38.1953 24.5858 37.4142C23.8047 36.6332 23.8047 35.3668 24.5858 34.5858L33.1716 26L10 26C8.89543 26 8 25.1046 8 24C8 22.8954 8.89543 22 10 22L33.1716 22L24.5858 13.4142C23.8047 12.6332 23.8047 11.3668 24.5858 10.5858Z`,
  ],
}

export const TrendingUp = {
  prefix: 'fal',
  iconName: 'trending-up',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M32 16C30.8954 16 30 15.1046 30 14C30 12.8954 30.8954 12 32 12H44C45.1046 12 46 12.8954 46 14V26C46 27.1046 45.1046 28 44 28C42.8954 28 42 27.1046 42 26V18.8284L28.4142 32.4142C27.6332 33.1953 26.3668 33.1953 25.5858 32.4142L17 23.8284L5.41421 35.4142C4.63317 36.1953 3.36684 36.1953 2.58579 35.4142C1.80474 34.6332 1.80474 33.3668 2.58579 32.5858L15.5858 19.5858C16.3668 18.8047 17.6332 18.8047 18.4142 19.5858L27 28.1716L39.1716 16H32Z`,
  ],
}

export const TrendingDown = {
  prefix: 'fal',
  iconName: 'trending-down',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M32 32C30.8954 32 30 32.8954 30 34C30 35.1046 30.8954 36 32 36H44C45.1046 36 46 35.1046 46 34V22C46 20.8954 45.1046 20 44 20C42.8954 20 42 20.8954 42 22V29.1716L28.4142 15.5858C27.6332 14.8047 26.3668 14.8047 25.5858 15.5858L17 24.1716L5.41421 12.5858C4.63317 11.8047 3.36684 11.8047 2.58579 12.5858C1.80474 13.3668 1.80474 14.6332 2.58579 15.4142L15.5858 28.4142C16.3668 29.1953 17.6332 29.1953 18.4142 28.4142L27 19.8284L39.1716 32H32Z`,
  ],
}

export const ServiceSet = {
  prefix: 'fal',
  iconName: 'service-set',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M33.3947 6.34706C33.3947 5.91498 33.745 5.56471 34.177 5.56471C34.6091 5.56471 34.9594 5.91498 34.9594 6.34706C34.9594 7.67095 36.5601 8.33407 37.4963 7.39788C37.8018 7.09235 38.2971 7.09235 38.6027 7.39788C38.9082 7.70341 38.9082 8.19876 38.6027 8.50429C37.6664 9.44052 38.3296 11.0412 39.6535 11.0412C40.0856 11.0412 40.4359 11.3914 40.4359 11.8235C40.4359 12.2556 40.0856 12.6059 39.6535 12.6059C38.3295 12.6059 37.6666 14.2066 38.6027 15.1428C38.9082 15.4483 38.9082 15.9437 38.6027 16.2492C38.2972 16.5547 37.8018 16.5547 37.4963 16.2492C36.5601 15.313 34.9594 15.976 34.9594 17.3C34.9594 17.7321 34.6091 18.0824 34.177 18.0824C33.745 18.0824 33.3947 17.7321 33.3947 17.3C33.3947 15.976 31.7939 15.313 30.8578 16.2492C30.5522 16.5547 30.0569 16.5547 29.7514 16.2492C29.4458 15.9437 29.4458 15.4483 29.7514 15.1428C30.6875 14.2067 30.0246 12.6059 28.7006 12.6059C28.2685 12.6059 27.9182 12.2556 27.9182 11.8235C27.9182 11.3914 28.2685 11.0412 28.7006 11.0412C30.0245 11.0412 30.6876 9.44048 29.7514 8.50428C29.4459 8.19875 29.4459 7.70339 29.7514 7.39787C30.0569 7.09234 30.5523 7.09234 30.8578 7.39787C31.794 8.33403 33.3947 7.671 33.3947 6.34706ZM34.177 4C32.9395 4 31.9256 4.95784 31.8364 6.17262C30.9143 5.37675 29.5201 5.41636 28.645 6.29145C27.7699 7.16655 27.7303 8.56076 28.5262 9.48285C27.3114 9.57208 26.3535 10.5859 26.3535 11.8235C26.3535 13.0611 27.3113 14.075 28.5261 14.1642C27.7302 15.0863 27.7698 16.4805 28.6449 17.3556C29.5201 18.2307 30.9143 18.2703 31.8364 17.4744C31.9256 18.6892 32.9395 19.6471 34.177 19.6471C35.4146 19.6471 36.4285 18.6892 36.5177 17.4744C37.4398 18.2703 38.834 18.2307 39.7091 17.3556C40.5842 16.4805 40.6238 15.0863 39.828 14.1642C41.0427 14.075 42.0006 13.0611 42.0006 11.8235C42.0006 10.5859 41.0427 9.57208 39.8279 9.48285C40.6238 8.56076 40.5842 7.16656 39.7091 6.29146C38.834 5.41637 37.4398 5.37676 36.5177 6.17262C36.4285 4.95784 35.4146 4 34.177 4ZM33.0706 10.7171C33.3641 10.4237 33.7621 10.2588 34.177 10.2588C34.592 10.2588 34.99 10.4237 35.2835 10.7171C35.5769 11.0106 35.7417 11.4085 35.7417 11.8235C35.7417 12.2385 35.5769 12.6365 35.2835 12.9299C34.99 13.2234 34.592 13.3882 34.177 13.3882C33.7621 13.3882 33.3641 13.2234 33.0706 12.9299C32.7772 12.6365 32.6123 12.2385 32.6123 11.8235C32.6123 11.4085 32.7772 11.0106 33.0706 10.7171ZM34.177 8.69412C33.3471 8.69412 32.5511 9.02382 31.9642 9.6107C31.3773 10.1976 31.0476 10.9936 31.0476 11.8235C31.0476 12.6535 31.3773 13.4495 31.9642 14.0364C32.5511 14.6232 33.3471 14.9529 34.177 14.9529C35.007 14.9529 35.803 14.6232 36.3899 14.0364C36.9767 13.4495 37.3065 12.6535 37.3065 11.8235C37.3065 10.9936 36.9767 10.1976 36.3899 9.6107C35.803 9.02382 35.007 8.69412 34.177 8.69412ZM16.6 20.2C16.6 19.4268 17.2268 18.8 18 18.8C18.7732 18.8 19.4 19.4268 19.4 20.2C19.4 22.5691 22.2643 23.7557 23.9396 22.0804C24.4864 21.5337 25.3728 21.5337 25.9195 22.0804C26.4663 22.6271 26.4663 23.5136 25.9195 24.0603C24.2442 25.7357 25.431 28.6 27.8 28.6C28.5732 28.6 29.2 29.2268 29.2 30C29.2 30.7732 28.5732 31.4 27.8 31.4C25.4308 31.4 24.2444 34.2645 25.9196 35.9397C26.4664 36.4864 26.4664 37.3728 25.9196 37.9196C25.3729 38.4663 24.4865 38.4663 23.9397 37.9196C22.2645 36.2443 19.4 37.4308 19.4 39.8C19.4 40.5732 18.7732 41.2 18 41.2C17.2268 41.2 16.6 40.5732 16.6 39.8C16.6 37.4307 13.7355 36.2444 12.0602 37.9196C11.5135 38.4663 10.6271 38.4663 10.0803 37.9196C9.53361 37.3729 9.53361 36.4864 10.0803 35.9397C11.7555 34.2646 10.5693 31.4 8.2 31.4C7.4268 31.4 6.8 30.7732 6.8 30C6.8 29.2268 7.4268 28.6 8.2 28.6C10.5691 28.6 11.7558 25.7356 10.0804 24.0603C9.5337 23.5136 9.5337 22.6271 10.0804 22.0804C10.6272 21.5337 11.5136 21.5337 12.0603 22.0804C13.7356 23.7556 16.6 22.5692 16.6 20.2ZM18 16C15.7854 16 13.9711 17.714 13.8114 19.8878C12.1614 18.4636 9.6665 18.5345 8.10054 20.1005C6.53457 21.6665 6.4637 24.1614 7.88792 25.8114C5.71407 25.9711 4 27.7854 4 30C4 32.2146 5.71402 34.0289 7.88783 34.1886C6.46361 35.8386 6.53448 38.3335 8.10045 39.8995C9.66643 41.4655 12.1614 41.5364 13.8114 40.1121C13.9711 42.2859 15.7854 44 18 44C20.2146 44 22.0289 42.2859 22.1886 40.1121C23.8386 41.5363 26.3336 41.4655 27.8995 39.8995C29.4655 38.3335 29.5364 35.8386 28.1122 34.1886C30.286 34.0289 32 32.2146 32 30C32 27.7854 30.2859 25.9711 28.1121 25.8114C29.5363 24.1614 29.4654 21.6665 27.8994 20.1005C26.3335 18.5346 23.8386 18.4637 22.1886 19.8878C22.0289 17.714 20.2146 16 18 16ZM16.0201 28.0201C16.5452 27.495 17.2574 27.2 18 27.2C18.7426 27.2 19.4548 27.495 19.9799 28.0201C20.505 28.5452 20.8 29.2574 20.8 30C20.8 30.7426 20.505 31.4548 19.9799 31.9799C19.4548 32.505 18.7426 32.8 18 32.8C17.2574 32.8 16.5452 32.505 16.0201 31.9799C15.495 31.4548 15.2 30.7426 15.2 30C15.2 29.2574 15.495 28.5452 16.0201 28.0201ZM18 24.4C16.5148 24.4 15.0904 24.99 14.0402 26.0402C12.99 27.0904 12.4 28.5148 12.4 30C12.4 31.4852 12.99 32.9096 14.0402 33.9598C15.0904 35.01 16.5148 35.6 18 35.6C19.4852 35.6 20.9096 35.01 21.9598 33.9598C23.01 32.9096 23.6 31.4852 23.6 30C23.6 28.5148 23.01 27.0904 21.9598 26.0402C20.9096 24.99 19.4852 24.4 18 24.4Z`,
  ],
}

export const StoragePool = {
  prefix: 'fal',
  iconName: 'storage-pool',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M41.3,4.41H6.7c-1.49,0-2.7,1.21-2.7,2.7v34.59c0,1.49,1.21,2.7,2.7,2.7h34.59c1.49,0,2.7-1.21,2.7-2.7V7.11c0-1.49-1.21-2.7-2.7-2.7ZM40.36,40.77H7.64V8.04h32.73v32.73ZM21.74,28.84c-2.75,0-4.98,2.23-4.98,4.98s2.23,4.98,4.98,4.98,4.98-2.23,4.98-4.98-2.23-4.98-4.98-4.98ZM21.74,35.16c-.74,0-1.35-.6-1.35-1.35s.6-1.35,1.35-1.35,1.35.6,1.35,1.35-.6,1.35-1.35,1.35ZM26.09,20.04c2.75,0,4.98-2.23,4.98-4.98s-2.23-4.98-4.98-4.98-4.98,2.23-4.98,4.98,2.23,4.98,4.98,4.98ZM26.09,13.71c.74,0,1.35.6,1.35,1.35s-.6,1.35-1.35,1.35-1.35-.6-1.35-1.35.6-1.35,1.35-1.35ZM14.51,27.29c2.75,0,4.98-2.23,4.98-4.98s-2.23-4.98-4.98-4.98-4.98,2.23-4.98,4.98,2.23,4.98,4.98,4.98ZM14.51,20.97c.74,0,1.35.6,1.35,1.35s-.6,1.35-1.35,1.35-1.35-.6-1.35-1.35.6-1.35,1.35-1.35ZM28.33,26.57c0,2.75,2.23,4.98,4.98,4.98s4.98-2.23,4.98-4.98-2.23-4.98-4.98-4.98-4.98,2.23-4.98,4.98ZM33.32,25.23c.74,0,1.35.6,1.35,1.35s-.6,1.35-1.35,1.35-1.35-.6-1.35-1.35.6-1.35,1.35-1.35Z`,
  ],
}

export const Sigma = {
  prefix: 'fal',
  iconName: 'sigma',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M37.5,14.75c1.1,0,2-.9,2-2v-6.75c0-1.1-.9-2-2-2H10.5c-.76,0-1.45.43-1.79,1.11-.34.68-.27,1.49.19,2.09l12.6,16.8-12.6,16.8c-.45.61-.53,1.42-.19,2.09.34.68,1.03,1.11,1.79,1.11h27c1.1,0,2-.9,2-2v-6.75c0-1.1-.9-2-2-2s-2,.9-2,2v4.75H14.5l11.1-14.8c.53-.71.53-1.69,0-2.4l-11.1-14.8h21v4.75c0,1.1.9,2,2,2Z',
  ],
}

export const Close = {
  prefix: 'fal',
  iconName: 'close',
  icon: [
    48,
    48,
    [],
    'f0000',
    'M10.5858 10.5858C11.3668 9.80474 12.6332 9.80474 13.4142 10.5858L24 21.1716L34.5858 10.5858C35.3668 9.80474 36.6332 9.80474 37.4142 10.5858C38.1953 11.3668 38.1953 12.6332 37.4142 13.4142L26.8284 24L37.4142 34.5858C38.1953 35.3668 38.1953 36.6332 37.4142 37.4142C36.6332 38.1953 35.3668 38.1953 34.5858 37.4142L24 26.8284L13.4142 37.4142C12.6332 38.1953 11.3668 38.1953 10.5858 37.4142C9.80474 36.6332 9.80474 35.3668 10.5858 34.5858L21.1716 24L10.5858 13.4142C9.80474 12.6332 9.80474 11.3668 10.5858 10.5858Z',
  ],
}

export const Tenants = {
  prefix: 'fal',
  iconName: 'tenants',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M44.48,26.36c0-2.4-1.95-4.36-4.36-4.36h-12.24c-2.4,0-4.36,1.95-4.36,4.36v.61h-7.41v-10.97h4.01c2.4,0,4.36-1.95,4.36-4.36v-5.29c0-2.4-1.95-4.36-4.36-4.36H7.88c-2.4,0-4.36,1.95-4.36,4.36v5.29c0,2.4,1.95,4.36,4.36,4.36h4.23v21.33c0,2.78,2.26,5.04,5.04,5.04h6.45c.35,2.06,2.13,3.64,4.28,3.64h12.24c2.4,0,4.36-1.95,4.36-4.36v-5.29c0-.87-.26-1.68-.7-2.36.44-.68.7-1.49.7-2.36v-5.29h0ZM7.88,12c-.2,0-.36-.16-.36-.36v-5.29c0-.2.16-.36.36-.36h12.24c.2,0,.36.16.36.36v5.29c0,.2-.16.36-.36.36H7.88ZM23.52,36.36v2.01h-6.38c-.57,0-1.04-.46-1.04-1.04v-6.36h7.41v.68c0,.87.26,1.68.7,2.36-.44.68-.7,1.49-.7,2.36h.01ZM40.48,41.64c0,.2-.16.36-.36.36h-12.24c-.2,0-.36-.16-.36-.36v-5.29c0-.2.16-.36.36-.36h12.24c.2,0,.36.16.36.36v5.29ZM40.48,31.64c0,.2-.16.36-.36.36h-12.24c-.2,0-.36-.16-.36-.36v-5.29c0-.2.16-.36.36-.36h12.24c.2,0,.36.16.36.36v5.29Z`,
  ],
}

export const Fabric = {
  prefix: 'fal',
  iconName: 'fabric',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M30 34C29.6044 34 29.2178 34.1173 28.8889 34.3371C28.56 34.5568 28.3036 34.8692 28.1522 35.2346C28.0009 35.6001 27.9613 36.0022 28.0384 36.3902C28.1156 36.7781 28.3061 37.1345 28.5858 37.4142C28.8655 37.6939 29.2219 37.8844 29.6098 37.9616C29.9978 38.0387 30.3999 37.9991 30.7654 37.8478C31.1308 37.6964 31.4432 37.44 31.6629 37.1111C31.8827 36.7822 32 36.3956 32 36C32 35.4696 31.7893 34.9609 31.4142 34.5858C31.0391 34.2107 30.5304 34 30 34ZM18 34H12C11.4696 34 10.9609 34.2107 10.5858 34.5858C10.2107 34.9609 10 35.4696 10 36C10 36.5304 10.2107 37.0391 10.5858 37.4142C10.9609 37.7893 11.4696 38 12 38H18C18.5304 38 19.0391 37.7893 19.4142 37.4142C19.7893 37.0391 20 36.5304 20 36C20 35.4696 19.7893 34.9609 19.4142 34.5858C19.0391 34.2107 18.5304 34 18 34ZM36 34C35.6044 34 35.2178 34.1173 34.8889 34.3371C34.56 34.5568 34.3036 34.8692 34.1522 35.2346C34.0009 35.6001 33.9613 36.0022 34.0384 36.3902C34.1156 36.7781 34.3061 37.1345 34.5858 37.4142C34.8655 37.6939 35.2219 37.8844 35.6098 37.9616C35.9978 38.0387 36.3999 37.9991 36.7654 37.8478C37.1308 37.6964 37.4432 37.44 37.6629 37.1111C37.8827 36.7822 38 36.3956 38 36C38 35.4696 37.7893 34.9609 37.4142 34.5858C37.0391 34.2107 36.5304 34 36 34ZM30 22C29.6044 22 29.2178 22.1173 28.8889 22.3371C28.56 22.5568 28.3036 22.8692 28.1522 23.2346C28.0009 23.6001 27.9613 24.0022 28.0384 24.3902C28.1156 24.7781 28.3061 25.1345 28.5858 25.4142C28.8655 25.6939 29.2219 25.8844 29.6098 25.9616C29.9978 26.0387 30.3999 25.9991 30.7654 25.8478C31.1308 25.6964 31.4432 25.44 31.6629 25.1111C31.8827 24.7822 32 24.3956 32 24C32 23.4696 31.7893 22.9609 31.4142 22.5858C31.0391 22.2107 30.5304 22 30 22ZM18 22H12C11.4696 22 10.9609 22.2107 10.5858 22.5858C10.2107 22.9609 10 23.4696 10 24C10 24.5304 10.2107 25.0391 10.5858 25.4142C10.9609 25.7893 11.4696 26 12 26H18C18.5304 26 19.0391 25.7893 19.4142 25.4142C19.7893 25.0391 20 24.5304 20 24C20 23.4696 19.7893 22.9609 19.4142 22.5858C19.0391 22.2107 18.5304 22 18 22ZM36 10C35.6044 10 35.2178 10.1173 34.8889 10.3371C34.56 10.5568 34.3036 10.8692 34.1522 11.2346C34.0009 11.6001 33.9613 12.0022 34.0384 12.3902C34.1156 12.7781 34.3061 13.1345 34.5858 13.4142C34.8655 13.6939 35.2219 13.8844 35.6098 13.9616C35.9978 14.0387 36.3999 13.9991 36.7654 13.8478C37.1308 13.6964 37.4432 13.44 37.6629 13.1111C37.8827 12.7822 38 12.3956 38 12C38 11.4696 37.7893 10.9609 37.4142 10.5858C37.0391 10.2107 36.5304 10 36 10ZM36 22C35.6044 22 35.2178 22.1173 34.8889 22.3371C34.56 22.5568 34.3036 22.8692 34.1522 23.2346C34.0009 23.6001 33.9613 24.0022 34.0384 24.3902C34.1156 24.7781 34.3061 25.1345 34.5858 25.4142C34.8655 25.6939 35.2219 25.8844 35.6098 25.9616C35.9978 26.0387 36.3999 25.9991 36.7654 25.8478C37.1308 25.6964 37.4432 25.44 37.6629 25.1111C37.8827 24.7822 38 24.3956 38 24C38 23.4696 37.7893 22.9609 37.4142 22.5858C37.0391 22.2107 36.5304 22 36 22ZM44 10C44 8.4087 43.3679 6.88258 42.2426 5.75736C41.1174 4.63214 39.5913 4 38 4H10C8.4087 4 6.88258 4.63214 5.75736 5.75736C4.63214 6.88258 4 8.4087 4 10V14C4.00882 15.4801 4.56442 16.9047 5.56 18C4.56442 19.0953 4.00882 20.5199 4 22V26C4.00882 27.4801 4.56442 28.9047 5.56 30C4.56442 31.0953 4.00882 32.5199 4 34V38C4 39.5913 4.63214 41.1174 5.75736 42.2426C6.88258 43.3679 8.4087 44 10 44H38C39.5913 44 41.1174 43.3679 42.2426 42.2426C43.3679 41.1174 44 39.5913 44 38V34C43.9912 32.5199 43.4356 31.0953 42.44 30C43.4356 28.9047 43.9912 27.4801 44 26V22C43.9912 20.5199 43.4356 19.0953 42.44 18C43.4356 16.9047 43.9912 15.4801 44 14V10ZM40 38C40 38.5304 39.7893 39.0391 39.4142 39.4142C39.0391 39.7893 38.5304 40 38 40H10C9.46957 40 8.96086 39.7893 8.58579 39.4142C8.21071 39.0391 8 38.5304 8 38V34C8 33.4696 8.21071 32.9609 8.58579 32.5858C8.96086 32.2107 9.46957 32 10 32H38C38.5304 32 39.0391 32.2107 39.4142 32.5858C39.7893 32.9609 40 33.4696 40 34V38ZM40 26C40 26.5304 39.7893 27.0391 39.4142 27.4142C39.0391 27.7893 38.5304 28 38 28H10C9.46957 28 8.96086 27.7893 8.58579 27.4142C8.21071 27.0391 8 26.5304 8 26V22C8 21.4696 8.21071 20.9609 8.58579 20.5858C8.96086 20.2107 9.46957 20 10 20H38C38.5304 20 39.0391 20.2107 39.4142 20.5858C39.7893 20.9609 40 21.4696 40 22V26ZM40 14C40 14.5304 39.7893 15.0391 39.4142 15.4142C39.0391 15.7893 38.5304 16 38 16H10C9.46957 16 8.96086 15.7893 8.58579 15.4142C8.21071 15.0391 8 14.5304 8 14V10C8 9.46957 8.21071 8.96086 8.58579 8.58579C8.96086 8.21071 9.46957 8 10 8H38C38.5304 8 39.0391 8.21071 39.4142 8.58579C39.7893 8.96086 40 9.46957 40 10V14ZM30 10C29.6044 10 29.2178 10.1173 28.8889 10.3371C28.56 10.5568 28.3036 10.8692 28.1522 11.2346C28.0009 11.6001 27.9613 12.0022 28.0384 12.3902C28.1156 12.7781 28.3061 13.1345 28.5858 13.4142C28.8655 13.6939 29.2219 13.8844 29.6098 13.9616C29.9978 14.0387 30.3999 13.9991 30.7654 13.8478C31.1308 13.6964 31.4432 13.44 31.6629 13.1111C31.8827 12.7822 32 12.3956 32 12C32 11.4696 31.7893 10.9609 31.4142 10.5858C31.0391 10.2107 30.5304 10 30 10ZM18 10H12C11.4696 10 10.9609 10.2107 10.5858 10.5858C10.2107 10.9609 10 11.4696 10 12C10 12.5304 10.2107 13.0391 10.5858 13.4142C10.9609 13.7893 11.4696 14 12 14H18C18.5304 14 19.0391 13.7893 19.4142 13.4142C19.7893 13.0391 20 12.5304 20 12C20 11.4696 19.7893 10.9609 19.4142 10.5858C19.0391 10.2107 18.5304 10 18 10Z`,
  ],
}

export const ActiveUsers = {
  prefix: 'fal',
  iconName: 'active-users',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M20 8C15.5817 8 12 11.5817 12 16C12 20.4183 15.5817 24 20 24C24.4183 24 28 20.4183 28 16C28 11.5817 24.4183 8 20 8ZM8 16C8 9.37258 13.3726 4 20 4C26.6274 4 32 9.37258 32 16C32 22.6274 26.6274 28 20 28C13.3726 28 8 22.6274 8 16ZM43.3287 22.5052C44.1543 23.239 44.2287 24.5032 43.4948 25.3287L38.1615 31.3287C37.7819 31.7557 37.2379 32 36.6667 32C36.0954 32 35.5514 31.7557 35.1718 31.3287L32.5052 28.3287C31.7713 27.5032 31.8457 26.239 32.6713 25.5052C33.4968 24.7713 34.761 24.8457 35.4948 25.6713L36.6667 26.9896L40.5052 22.6713C41.239 21.8457 42.5032 21.7713 43.3287 22.5052ZM13 36C10.4811 36 8 38.4271 8 42C8 43.1046 7.10457 44 6 44C4.89543 44 4 43.1046 4 42C4 36.7364 7.78693 32 13 32H27C32.2131 32 36 36.7364 36 42C36 43.1046 35.1046 44 34 44C32.8954 44 32 43.1046 32 42C32 38.4271 29.5189 36 27 36H13Z`,
  ],
}

export const ChartSync = {
  prefix: 'fal',
  iconName: 'chart-sync',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M17.4172 13.09C16.6372 12.31 15.3672 12.31 14.5872 13.09L11.9972 15.68V10.51C11.9972 8.23 13.7172 6.51 15.9972 6.51H17.9972C19.0972 6.51 19.9972 5.61 19.9972 4.51C19.9972 3.41 19.0972 2.51 17.9972 2.51H15.9972C11.5072 2.51 7.99719 6.02 7.99719 10.51V15.68L5.40719 13.09C4.62719 12.31 3.35719 12.31 2.57719 13.09C1.79719 13.87 1.79719 15.14 2.57719 15.92L8.57719 21.92C8.57719 21.92 8.59719 21.94 8.60719 21.95C8.78719 22.13 8.99719 22.26 9.22719 22.35C9.46719 22.45 9.72719 22.5 9.99719 22.5C10.2672 22.5 10.5272 22.45 10.7672 22.35C10.9872 22.26 11.1972 22.12 11.3872 21.95C11.3972 21.94 11.4072 21.93 11.4172 21.92L17.4172 15.92C18.1972 15.14 18.1972 13.87 17.4172 13.09ZM20.8072 42.5H14.4072C13.3072 42.5 12.4072 43.4 12.4072 44.5C12.4072 45.6 13.3072 46.5 14.4072 46.5H20.8072C21.9072 46.5 22.8072 45.6 22.8072 44.5C22.8072 43.4 21.9072 42.5 20.8072 42.5ZM20.8072 26.5H14.4072C13.3072 26.5 12.4072 27.4 12.4072 28.5C12.4072 29.6 13.3072 30.5 14.4072 30.5H20.8072C21.9072 30.5 22.8072 29.6 22.8072 28.5C22.8072 27.4 21.9072 26.5 20.8072 26.5ZM8.00719 42.5H6.00719V40.5C6.00719 39.4 5.10719 38.5 4.00719 38.5C2.90719 38.5 2.00719 39.4 2.00719 40.5V44.5C2.00719 45.6 2.90719 46.5 4.00719 46.5H8.00719C9.10719 46.5 10.0072 45.6 10.0072 44.5C10.0072 43.4 9.10719 42.5 8.00719 42.5ZM8.00719 26.5H4.00719C2.90719 26.5 2.00719 27.4 2.00719 28.5V32.5C2.00719 33.6 2.90719 34.5 4.00719 34.5C5.10719 34.5 6.00719 33.6 6.00719 32.5V30.5H8.00719C9.10719 30.5 10.0072 29.6 10.0072 28.5C10.0072 27.4 9.10719 26.5 8.00719 26.5ZM40.0072 2.5H32.0072C28.6972 2.5 26.0072 5.19 26.0072 8.5V16.5C26.0072 19.81 28.6972 22.5 32.0072 22.5H40.0072C43.3172 22.5 46.0072 19.81 46.0072 16.5V8.5C46.0072 5.19 43.3172 2.5 40.0072 2.5ZM42.0072 16.5C42.0072 17.6 41.1072 18.5 40.0072 18.5H32.0072C30.9072 18.5 30.0072 17.6 30.0072 16.5V8.5C30.0072 7.4 30.9072 6.5 32.0072 6.5H40.0072C41.1072 6.5 42.0072 7.4 42.0072 8.5V16.5ZM44.0072 26.5H40.0072C38.9072 26.5 38.0072 27.4 38.0072 28.5C38.0072 29.6 38.9072 30.5 40.0072 30.5H42.0072V32.5C42.0072 33.6 42.9072 34.5 44.0072 34.5C45.1072 34.5 46.0072 33.6 46.0072 32.5V28.5C46.0072 27.4 45.1072 26.5 44.0072 26.5ZM33.6072 42.5H27.2072C26.1072 42.5 25.2072 43.4 25.2072 44.5C25.2072 45.6 26.1072 46.5 27.2072 46.5H33.6072C34.7072 46.5 35.6072 45.6 35.6072 44.5C35.6072 43.4 34.7072 42.5 33.6072 42.5ZM33.6072 26.5H27.2072C26.1072 26.5 25.2072 27.4 25.2072 28.5C25.2072 29.6 26.1072 30.5 27.2072 30.5H33.6072C34.7072 30.5 35.6072 29.6 35.6072 28.5C35.6072 27.4 34.7072 26.5 33.6072 26.5ZM44.0072 38.5C42.9072 38.5 42.0072 39.4 42.0072 40.5V42.5H40.0072C38.9072 42.5 38.0072 43.4 38.0072 44.5C38.0072 45.6 38.9072 46.5 40.0072 46.5H44.0072C45.1072 46.5 46.0072 45.6 46.0072 44.5V40.5C46.0072 39.4 45.1072 38.5 44.0072 38.5Z`,
  ],
}

export const Netroute = {
  prefix: 'fal',
  iconName: 'netroute',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M42.54,27.3h-6.89v-11.79c1.68-.68,2.88-2.32,2.88-4.24,0-2.53-2.05-4.58-4.58-4.58-1.9,0-3.52,1.15-4.22,2.8h-4.09v-4.78c0-.94-.76-1.71-1.71-1.71H6.98c-.94,0-1.71.76-1.71,1.71v12.61c-.85.1-1.51.79-1.51,1.67,0,.94.76,1.71,1.71,1.71h8.32v11.3c-1.66.69-2.83,2.32-2.83,4.23,0,2.53,2.05,4.58,4.58,4.58,1.97,0,3.45-1.21,4.09-2.81h4.26v5.31c0,.94.76,1.71,1.71,1.71h16.96c.94,0,1.71-.76,1.71-1.71v-14.28c0-.94-.76-1.71-1.71-1.71ZM33.94,10.11c.64,0,1.16.52,1.16,1.16s-.52,1.16-1.16,1.16-1.16-.52-1.16-1.16.52-1.16,1.16-1.16ZM8.68,17.28V6.42h13.54v10.87h-13.54ZM15.53,37.38c-.64,0-1.16-.52-1.16-1.16s.52-1.16,1.16-1.16c.52,0,1.01.39,1.01,1.16s-.49,1.16-1.01,1.16ZM23.87,29.01v5.56h-4.22c-.42-1.16-1.29-2.12-2.46-2.59v-11.28h8.25c.94,0,1.71-.76,1.71-1.71,0-.87-.67-1.57-1.51-1.67v-4.42h4.04c.45,1.18,1.37,2.12,2.54,2.6v11.8h-6.65c-.94,0-1.71.76-1.71,1.71ZM40.83,41.58h-13.54v-10.87h6.65s0,0,0,0,0,0,0,0h6.89v10.87Z`,
  ],
}

export const ContainerRuntime = {
  prefix: 'fal',
  iconName: 'container-runtime',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M24.0562 3.91086L24.0063 3.88086L6.45625 13.9109L6.40625 13.9409V34.0609L23.9562 44.0909L24.0063 44.1209L39.5262 35.2409L39.5662 35.2709V35.2209L41.5363 34.0909L41.5863 34.0609V13.9409L24.0562 3.91086ZM37.6463 29.6409L32.6862 26.7709V20.0209L24.9662 24.6009V33.7809L30.8962 30.2609L35.5262 32.9309L24.0063 39.4409L12.4563 32.9109L17.0863 30.2409L23.0562 33.7809V24.6009L15.3363 20.0209V26.7309L10.3562 29.6109V16.2709L22.0163 9.68086V14.9509L16.0363 18.4109L23.9762 22.9909L31.9363 18.4109L25.9363 14.9409V9.63086L37.6562 16.2709V29.6309L37.6463 29.6409Z`,
  ],
}

export const DockerContainer = {
  prefix: 'fal',
  iconName: 'docker-container',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M32.9405 1.64062L2.48047 20.4506V37.5406L16.1905 45.7306L46.4805 27.4706V10.1706L32.9405 1.64062ZM15.2905 40.5406L6.47047 35.2706V24.8206L15.2905 29.2806V40.5406ZM8.56047 21.3906L32.9205 6.35063L40.6905 11.2506L17.1105 25.7206L8.56047 21.3906ZM25.1505 35.6506L19.2805 39.1906V29.0706L25.1505 25.4706V35.6506ZM34.3205 30.1206L29.1505 33.2306V23.0106L34.3205 19.8406V30.1206ZM42.4705 25.2106L38.3205 27.7106V17.3906L42.4705 14.8406V25.2106V25.2106Z`,
  ],
}

export const CustomDashboard = {
  prefix: 'fal',
  iconName: 'custom-dashboard',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M19.28,26.41H3.58c-1,0-1.8.8-1.8,1.8v15.7c0,1,.8,1.8,1.8,1.8h15.7c1,0,1.8-.8,1.8-1.8v-15.7c0-1-.8-1.8-1.8-1.8ZM17.48,42.11H5.28v-12.2h12.2v12.2ZM19.28,1.71H3.58c-1,0-1.8.8-1.8,1.8v15.7c0,1,.8,1.8,1.8,1.8h15.7c1,0,1.8-.8,1.8-1.8V3.51c0-1-.8-1.8-1.8-1.8ZM17.48,17.41H5.28V5.21h12.2v12.2ZM28.28,21.01h15.7c1,0,1.8-.8,1.8-1.8V3.51c0-1-.8-1.8-1.8-1.8h-15.7c-1,0-1.8.8-1.8,1.8v15.7c0,1,.8,1.8,1.8,1.8ZM29.98,5.21h12.2v12.2h-12.2V5.21ZM45.3,30.68l-3.73-3.73c-.66-.65-1.72-.66-2.38,0l-12.14,12.14c-.32.32-.49.74-.49,1.19v3.73c0,.93.75,1.68,1.68,1.68h3.73c.45,0,.87-.17,1.19-.49l12.13-12.14c.66-.66.66-1.72,0-2.38ZM31.28,42.33h-1.35v-1.35l6.3-6.3,1.35,1.35-6.3,6.3ZM39.91,33.7l-1.35-1.35,1.82-1.82,1.35,1.35-1.82,1.82Z`,
  ],
}

export const CPG = {
  prefix: 'fal',
  iconName: 'cpg',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M40.93,3.71H6.34c-1.49,0-2.7,1.21-2.7,2.7v34.59c0,1.49,1.21,2.7,2.7,2.7h34.59c1.49,0,2.7-1.21,2.7-2.7V6.41c0-1.49-1.21-2.7-2.7-2.7ZM40,40.07H7.27V7.35h32.73v32.73ZM21.37,28.14c-2.75,0-4.98,2.23-4.98,4.98s2.23,4.98,4.98,4.98,4.98-2.23,4.98-4.98-2.23-4.98-4.98-4.98ZM21.37,34.47c-.74,0-1.35-.6-1.35-1.35s.6-1.35,1.35-1.35,1.35.6,1.35,1.35-.6,1.35-1.35,1.35ZM25.72,19.34c2.75,0,4.98-2.23,4.98-4.98s-2.23-4.98-4.98-4.98-4.98,2.23-4.98,4.98,2.23,4.98,4.98,4.98ZM25.72,13.01c.74,0,1.35.6,1.35,1.35s-.6,1.35-1.35,1.35-1.35-.6-1.35-1.35.6-1.35,1.35-1.35ZM14.15,26.6c2.75,0,4.98-2.23,4.98-4.98s-2.23-4.98-4.98-4.98-4.98,2.23-4.98,4.98,2.23,4.98,4.98,4.98ZM14.15,20.27c.74,0,1.35.6,1.35,1.35s-.6,1.35-1.35,1.35-1.35-.6-1.35-1.35.6-1.35,1.35-1.35ZM27.97,25.88c0,2.75,2.23,4.98,4.98,4.98s4.98-2.23,4.98-4.98-2.23-4.98-4.98-4.98-4.98,2.23-4.98,4.98ZM32.95,24.53c.74,0,1.35.6,1.35,1.35s-.6,1.35-1.35,1.35-1.35-.6-1.35-1.35.6-1.35,1.35-1.35Z`,
  ],
}

export const Volume = {
  prefix: 'fal',
  iconName: 'volume',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M16 33C15.6044 33 15.2178 33.1173 14.8889 33.3371C14.56 33.5568 14.3036 33.8692 14.1522 34.2346C14.0009 34.6001 13.9613 35.0022 14.0384 35.3902C14.1156 35.7781 14.3061 36.1345 14.5858 36.4142C14.8655 36.6939 15.2219 36.8844 15.6098 36.9616C15.9978 37.0387 16.3999 36.9991 16.7654 36.8478C17.1308 36.6964 17.4432 36.44 17.6629 36.1111C17.8827 35.7822 18 35.3956 18 35C18 34.4696 17.7893 33.9609 17.4142 33.5858C17.0391 33.2107 16.5304 33 16 33V33ZM24 4C16 4 8 6.74 8 12V36C8 41.26 16 44 24 44C32 44 40 41.26 40 36V12C40 6.74 32 4 24 4ZM36 36C36 37.42 31.44 40 24 40C16.56 40 12 37.42 12 36V29.46C15.7441 31.2399 19.8558 32.1102 24 32C28.1442 32.1102 32.2559 31.2399 36 29.46V36ZM36 24C36 25.42 31.44 28 24 28C16.56 28 12 25.42 12 24V17.46C15.7441 19.2399 19.8558 20.1102 24 20C28.1442 20.1102 32.2559 19.2399 36 17.46V24ZM24 16C16.56 16 12 13.42 12 12C12 10.58 16.56 8 24 8C31.44 8 36 10.58 36 12C36 13.42 31.44 16 24 16ZM16 21C15.6044 21 15.2178 21.1173 14.8889 21.3371C14.56 21.5568 14.3036 21.8692 14.1522 22.2346C14.0009 22.6001 13.9613 23.0022 14.0384 23.3902C14.1156 23.7781 14.3061 24.1345 14.5858 24.4142C14.8655 24.6939 15.2219 24.8844 15.6098 24.9616C15.9978 25.0387 16.3999 24.9991 16.7654 24.8478C17.1308 24.6964 17.4432 24.44 17.6629 24.1111C17.8827 23.7822 18 23.3956 18 23C18 22.4696 17.7893 21.9609 17.4142 21.5858C17.0391 21.2107 16.5304 21 16 21V21Z`,
  ],
}

export const Luns = {
  prefix: 'fal',
  iconName: 'luns',
  icon: [
    48,
    48,
    [],
    'f0000',
    `M28 18H20C19.4696 18 18.9609 18.2107 18.5858 18.5858C18.2107 18.9609 18 19.4696 18 20V28C18 28.5304 18.2107 29.0391 18.5858 29.4142C18.9609 29.7893 19.4696 30 20 30H28C28.5304 30 29.0391 29.7893 29.4142 29.4142C29.7893 29.0391 30 28.5304 30 28V20C30 19.4696 29.7893 18.9609 29.4142 18.5858C29.0391 18.2107 28.5304 18 28 18ZM26 26H22V22H26V26ZM42 26C42.5304 26 43.0391 25.7893 43.4142 25.4142C43.7893 25.0391 44 24.5304 44 24C44 23.4696 43.7893 22.9609 43.4142 22.5858C43.0391 22.2107 42.5304 22 42 22H38V18H42C42.5304 18 43.0391 17.7893 43.4142 17.4142C43.7893 17.0391 44 16.5304 44 16C44 15.4696 43.7893 14.9609 43.4142 14.5858C43.0391 14.2107 42.5304 14 42 14H37.64C37.3414 13.1612 36.8598 12.3994 36.2302 11.7698C35.6006 11.1402 34.8388 10.6586 34 10.36V6C34 5.46957 33.7893 4.96086 33.4142 4.58579C33.0391 4.21071 32.5304 4 32 4C31.4696 4 30.9609 4.21071 30.5858 4.58579C30.2107 4.96086 30 5.46957 30 6V10H26V6C26 5.46957 25.7893 4.96086 25.4142 4.58579C25.0391 4.21071 24.5304 4 24 4C23.4696 4 22.9609 4.21071 22.5858 4.58579C22.2107 4.96086 22 5.46957 22 6V10H18V6C18 5.46957 17.7893 4.96086 17.4142 4.58579C17.0391 4.21071 16.5304 4 16 4C15.4696 4 14.9609 4.21071 14.5858 4.58579C14.2107 4.96086 14 5.46957 14 6V10.36C13.1612 10.6586 12.3994 11.1402 11.7698 11.7698C11.1402 12.3994 10.6586 13.1612 10.36 14H6C5.46957 14 4.96086 14.2107 4.58579 14.5858C4.21071 14.9609 4 15.4696 4 16C4 16.5304 4.21071 17.0391 4.58579 17.4142C4.96086 17.7893 5.46957 18 6 18H10V22H6C5.46957 22 4.96086 22.2107 4.58579 22.5858C4.21071 22.9609 4 23.4696 4 24C4 24.5304 4.21071 25.0391 4.58579 25.4142C4.96086 25.7893 5.46957 26 6 26H10V30H6C5.46957 30 4.96086 30.2107 4.58579 30.5858C4.21071 30.9609 4 31.4696 4 32C4 32.5304 4.21071 33.0391 4.58579 33.4142C4.96086 33.7893 5.46957 34 6 34H10.36C10.6586 34.8388 11.1402 35.6006 11.7698 36.2302C12.3994 36.8598 13.1612 37.3414 14 37.64V42C14 42.5304 14.2107 43.0391 14.5858 43.4142C14.9609 43.7893 15.4696 44 16 44C16.5304 44 17.0391 43.7893 17.4142 43.4142C17.7893 43.0391 18 42.5304 18 42V38H22V42C22 42.5304 22.2107 43.0391 22.5858 43.4142C22.9609 43.7893 23.4696 44 24 44C24.5304 44 25.0391 43.7893 25.4142 43.4142C25.7893 43.0391 26 42.5304 26 42V38H30V42C30 42.5304 30.2107 43.0391 30.5858 43.4142C30.9609 43.7893 31.4696 44 32 44C32.5304 44 33.0391 43.7893 33.4142 43.4142C33.7893 43.0391 34 42.5304 34 42V37.64C34.8388 37.3414 35.6006 36.8598 36.2302 36.2302C36.8598 35.6006 37.3414 34.8388 37.64 34H42C42.5304 34 43.0391 33.7893 43.4142 33.4142C43.7893 33.0391 44 32.5304 44 32C44 31.4696 43.7893 30.9609 43.4142 30.5858C43.0391 30.2107 42.5304 30 42 30H38V26H42ZM34 32C34 32.5304 33.7893 33.0391 33.4142 33.4142C33.0391 33.7893 32.5304 34 32 34H16C15.4696 34 14.9609 33.7893 14.5858 33.4142C14.2107 33.0391 14 32.5304 14 32V16C14 15.4696 14.2107 14.9609 14.5858 14.5858C14.9609 14.2107 15.4696 14 16 14H32C32.5304 14 33.0391 14.2107 33.4142 14.5858C33.7893 14.9609 34 15.4696 34 16V32Z`,
  ],
}
