import Capitalize from 'lodash/capitalize'
import { WidgetTypeConstants } from '@components/widgets/constants'
import WidgetContextBuilder from '@components/widgets/widget-context-builder'
import Constants from '@constants'

export const CATEGORY_ID_MAP = {
  [Constants.SERVER]: 10000000002245,
  [Constants.NETWORK]: 10000000002246,
  [Constants.SERVICE_CHECK]: 10000000002247,
  [Constants.VIRTUALIZATION]: 10000000002248,
  [Constants.SERVICE]: 10000000002249,
  [Constants.PROCESS]: 10000000002250,
  [Constants.INTERFACE]: 10000000002251,
  [Constants.WAN_LINK]: 10000000002253,
  [Constants.OTHER]: 10000000002252,
  [Constants.HYPERCONVERGED_INFRASTRUCTURE]: 10000000002254,
  [Constants.SDN]: 10000000002255,
  [Constants.STORAGE]: 10000000002256,
  [Constants.CONTAINER]: 10000000002257,
}

const CATEGORY_COUNTER_MAP = {
  [Constants.SERVER]: [
    'system.cpu.percent',
    'system.memory.used.percent',
    'system.disk.used.percent',
  ],
  [Constants.NETWORK]: [
    'ping.latency.ms',
    'ping.packet.lost.percent',
    'total.interfaces',
    'system.cpu.percent',
    'system.memory.used.percent',
    'system.disk.used.percent',
  ],
  [Constants.VIRTUALIZATION]: [
    'esxi.cpu.percent',
    'esxi.memory.used.percent',
    'esxi.disk.used.percent',
    'citrix.xen.cpu.percent',
    'citrix.xen.memory.used.percent',
    'citrix.xen.disk.used.percent',
    'hyperv.cpu.percent',
    'hyperv.memory.used.percent',
    'hyperv.disk.used.percent',
    'total.vms',
  ],
  [Constants.HYPERCONVERGED_INFRASTRUCTURE]: [
    'nutanix.cpu.percent',
    'nutanix.memory.used.percent',
    'nutanix.cluster',
    'total.vms',
  ],
  [Constants.SDN]: [
    'cisco.vmanage.cpu.percent',
    'cisco.vmanage.memory.used.percent',
    'cisco.vbond.cpu.percent',
    'cisco.vbond.memory.used.percent',
    'cisco.vsmart.cpu.percent',
    'cisco.vsmart.memory.used.percent',
    'cisco.vedge.cpu.percent',
    'cisco.vedge.memory.used.percent',
  ],
  [Constants.INTERFACE]: [
    'interface~name',
    'interface~index',
    'interface~alias',
    'interface~status',
    'interface~traffic.bytes.per.sec',
    'interface~error.packets',
    'interface~traffic.utilization.percent',
    'interface~ip.address',
  ],
  [Constants.PROCESS]: [
    'system.process~name',
    'system.process~status',
    'system.process~cpu.percent',
    'system.process~memory.used.bytes',
  ],
  [Constants.WAN_LINK]: [
    'ipsla~name',
    'ipsla~operation.type',
    'ipsla~status',
    'ipsla~latency.ms',
  ],
  [Constants.CONTAINER]: [
    'docker.container~image',
    'docker.container~status',
    'docker.container~memory.used.bytes',
    'docker.container~cpu.percent',
    'docker.container~restarts',
    'docker.container~processes',
  ],
  [Constants.SERVICE]: ['system.service~status'],
  [Constants.SERVICE_CHECK]: ['service.check.latency.ms'],
  [Constants.OTHER]: ['ping.latency.ms', 'ping.packet.lost.percent'],
}

const CATEGORY_GROUP_BY_MAP = {
  [Constants.INTERFACE]: 'interface',
  [Constants.PROCESS]: 'system.process',
  [Constants.WAN_LINK]: 'ipsla',
  [Constants.SERVICE]: 'system.service',
  [Constants.CONTAINER]: 'docker.container',
}

const SEVERITY_TO_CONSIDER_IN_ALERT_COUNTS = [
  'DOWN',
  'UNREACHABLE',
  'CRITICAL',
  'MAJOR',
  'WARNING',
  'CLEAR',
]

const severityMap = SEVERITY_TO_CONSIDER_IN_ALERT_COUNTS.map(
  (severity) => `${severity.toLowerCase()}Alerts`
)

export function getWidgetForCategory(category, uuid) {
  const counters = CATEGORY_COUNTER_MAP[category] || []
  if (counters.length === 0) {
    return
  }
  const builder = new WidgetContextBuilder()
  builder.addGroup('metric')
  counters.forEach((counter) => {
    builder.addCounterToGroup({
      counter: counter,
      aggrigateFn: 'last',
      ...([
        Constants.INTERFACE,
        Constants.SERVICE,
        Constants.PROCESS,
        Constants.WAN_LINK,
        Constants.CONTAINER,
      ].includes(category)
        ? {}
        : {
            entityType: 'category',
            entities: [category],
          }),
    })
  })
  builder.setCategory(WidgetTypeConstants.GRID)
  builder.setTimeLine({
    selectedKey: 'today',
  })
  builder.addResultBy([
    ...([
      Constants.INTERFACE,
      Constants.PROCESS,
      Constants.SERVICE,
      Constants.WAN_LINK,
      Constants.CONTAINER,
    ].includes(category)
      ? [CATEGORY_GROUP_BY_MAP[category]]
      : ['Monitor']),
  ])
  return {
    id: -1,
    [Constants.UI_EVENT_UUID]: uuid,
    ...builder.generateWidgetDefinition(),
  }
}

export function getWidgetForAlertCountData(uuid) {
  const builder = new WidgetContextBuilder()
  builder.addGroup('policy.stream')
  builder.addCounterToGroup({
    counter: 'severity',
    aggrigateFn: 'count',
    type: 'policy.stream',
  })
  builder.setCategory(WidgetTypeConstants.GRID)
  builder.setTimeLine({
    selectedKey: 'today',
  })
  builder.addResultBy(['object.id', 'severity'], 'policy.stream')

  let generatedWidget = {
    id: -1,
    [Constants.UI_EVENT_UUID]: uuid,
    ...builder.generateWidgetDefinition(),
  }

  let policyStreamGroupIndex = generatedWidget[
    'visualization.data.sources'
  ].findIndex((group) => group.type === 'policy.stream')

  generatedWidget['visualization.data.sources'][policyStreamGroupIndex] = {
    ...generatedWidget['visualization.data.sources'][policyStreamGroupIndex],
    severity: SEVERITY_TO_CONSIDER_IN_ALERT_COUNTS,
    'severity.result': true,
    category: 'metric',
  }

  return generatedWidget
}
const INSTANCE_COUNTER_MAP = {
  interface: [
    'interface~index',
    'interface~name',
    'interface~alias',
    'interface~status',
    'interface~in.traffic.bits.per.sec',
    'interface~out.traffic.bits.per.sec',
    'interface~received.error.packets',
    'interface~sent.error.packets',
    'interface~traffic.utilization.percent',
    'interface~ip.address',
  ],
  'citrix.xen.vm': ['citrix.xen.vm~status', 'citrix.xen.vm~ip'],
  'hyperv.vm': ['hyperv.vm~status', 'hyperv.vm~ip'],
  'esxi.vm': ['esxi.vm~status', 'esxi.vm~ip'],
  'nutanix.vm': ['nutanix.vm~status', 'nutanix.vm~ip'],
}

export function getWidgetForInstance(monitorId, instanceType, uuid) {
  const counters = INSTANCE_COUNTER_MAP[instanceType] || []
  if (counters.length === 0) {
    return
  }
  const builder = new WidgetContextBuilder()
  builder.addGroup('metric')
  counters.forEach((counter) => {
    builder.addCounterToGroup({
      counter: counter,
      aggrigateFn: 'last',
      entityType: 'Monitor',
      entities: [monitorId],
    })
  })
  builder.setCategory(WidgetTypeConstants.GRID)
  builder.setTimeLine({
    selectedKey: 'today',
  })
  builder.addResultBy([instanceType])
  return {
    id: -1,
    [Constants.UI_EVENT_UUID]: uuid,
    ...builder.generateWidgetDefinition(),
  }
}

export function alertCounts(item) {
  return severityMap
    .map((severity) => {
      return {
        severity: severity.replace('Alerts', ''),
        counts: item[severity],
        title: Capitalize(severity.replace('Alerts', '')),
      }
    })
    .filter((data) => data.counts)
}

export const INVENTORY_SERVERSIDE_DEF = {
  [Constants.SERVER]: {
    id: 10000000002245,
    'visualization.name': 'Server Inventory Summary',
    'visualization.timeline': {
      'relative.timeline': 'today',
      'visualization.time.range.inclusive': 'no',
    },
    'visualization.category': 'Grid',
    'visualization.type': 'Grid',
    'visualization.data.sources': [
      {
        type: 'metric',
        'join.type': 'custom',
        filters: {
          'data.filter': {},
          'result.filter': {},
        },
        'visualization.result.by': ['monitor'],
        'data.points': [
          {
            'data.point': 'system.cpu.percent',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['Server'],
          },
          {
            'data.point': 'system.memory.used.percent',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['Server'],
          },
          {
            'data.point': 'system.disk.used.percent',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['Server'],
          },
          {
            'data.point': 'system.os.name',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['Server'],
          },
          {
            'data.point': 'system.os.version',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['Server'],
          },
          {
            'data.point': 'system.model',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['Server'],
          },
          {
            'data.point': 'system.vendor',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['Server'],
          },
        ],
      },
    ],
    'visualization.properties': {
      grid: {
        searchable: 'yes',
        'column.selection': 'no',
        header: 'yes',
        style: {
          'header.font.size': 'medium',
        },
        columns: [],
      },
    },
    'join.type': 'custom',
    'join.result': 'inventory',
    'join.columns': ['monitor'],
    'visualization.result.by': ['monitor'],
    'container.type': 'dashboard',
  },
  [Constants.NETWORK]: {
    id: 10000000002246,
    'visualization.name': 'Network Inventory Summary',
    'visualization.timeline': {
      'relative.timeline': 'today',
      'visualization.time.range.inclusive': 'no',
    },
    'visualization.category': 'Grid',
    'visualization.type': 'Grid',
    'visualization.data.sources': [
      {
        type: 'metric',
        'join.type': 'custom',
        filters: {
          'data.filter': {},
          'result.filter': {},
        },
        'visualization.result.by': ['monitor'],
        'data.points': [
          {
            'data.point': 'ping.latency.ms',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['Network'],
          },
          {
            'data.point': 'ping.packet.lost.percent',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['Network'],
          },
          {
            'data.point': 'total.interfaces',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['Network'],
          },
          {
            'data.point': 'system.cpu.percent',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['Network'],
          },
          {
            'data.point': 'system.memory.used.percent',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['Network'],
          },
          {
            'data.point': 'system.disk.used.percent',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['Network'],
          },
          {
            'data.point': 'system.serial.number',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['Network'],
          },
          {
            'data.point': 'system.os.version',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['Network'],
          },
          {
            'data.point': 'system.model',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['Network'],
          },
          {
            'data.point': 'system.model.number',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['Network'],
          },
          {
            'data.point': 'system.description',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['Network'],
          },
        ],
      },
    ],
    'visualization.properties': {
      grid: {
        searchable: 'yes',
        'column.selection': 'no',
        header: 'yes',
        style: {
          'header.font.size': 'medium',
        },
        columns: [],
      },
    },
    'join.type': 'custom',
    'join.result': 'inventory',
    'join.columns': ['monitor'],
    'visualization.result.by': ['monitor'],
    'container.type': 'dashboard',
  },
  [Constants.SERVICE_CHECK]: {
    id: 10000000002247,
    'visualization.name': 'Service Check Inventory Summary',
    'visualization.timeline': {
      'relative.timeline': 'today',
      'visualization.time.range.inclusive': 'no',
    },
    'visualization.category': 'Grid',
    'visualization.type': 'Grid',
    'visualization.data.sources': [
      {
        type: 'metric',
        'join.type': 'custom',
        filters: {
          'data.filter': {},
          'result.filter': {},
        },
        'visualization.result.by': ['monitor'],
        'data.points': [
          {
            'data.point': 'service.check.latency.ms',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['Service Check'],
          },
          {
            'data.point': 'service.check.status',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['Service Check'],
          },
        ],
      },
    ],
    'visualization.properties': {
      grid: {
        searchable: 'yes',
        'column.selection': 'no',
        header: 'yes',
        style: {
          'header.font.size': 'medium',
        },
        columns: [],
      },
    },
    'join.type': 'custom',
    'join.result': 'inventory',
    'join.columns': ['monitor'],
    'visualization.result.by': ['monitor'],
    'container.type': 'dashboard',
  },
  [Constants.VIRTUALIZATION]: {
    id: 10000000002248,
    'visualization.name': 'Virtualization Inventory Summary',
    'visualization.timeline': {
      'relative.timeline': 'today',
      'visualization.time.range.inclusive': 'no',
    },
    'visualization.category': 'Grid',
    'visualization.type': 'Grid',
    'visualization.data.sources': [
      {
        type: 'metric',
        'join.type': 'custom',
        filters: {
          'data.filter': {},
          'result.filter': {},
        },
        'visualization.result.by': ['monitor'],
        'data.points': [
          {
            'data.point': 'esxi.cpu.percent',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['Virtualization'],
          },
          {
            'data.point': 'esxi.os.version',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['Virtualization'],
          },
          {
            'data.point': 'esxi.memory.used.percent',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['Virtualization'],
          },
          {
            'data.point': 'esxi.disk.used.percent',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['Virtualization'],
          },
          {
            'data.point': 'citrix.xen.cpu.percent',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['Virtualization'],
          },
          {
            'data.point': 'citrix.xen.memory.used.percent',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['Virtualization'],
          },
          {
            'data.point': 'citrix.xen.disk.used.percent',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['Virtualization'],
          },
          {
            'data.point': 'citrix.xen.os.version',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['Virtualization'],
          },
          {
            'data.point': 'hyperv.version',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['Virtualization'],
          },
          {
            'data.point': 'hyperv.os.version',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['Virtualization'],
          },
          {
            'data.point': 'hyperv.cpu.percent',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['Virtualization'],
          },
          {
            'data.point': 'hyperv.memory.used.percent',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['Virtualization'],
          },
          {
            'data.point': 'hyperv.disk.used.percent',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['Virtualization'],
          },
          {
            'data.point': 'total.vms',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['Virtualization'],
          },
        ],
      },
    ],
    'visualization.properties': {
      grid: {
        searchable: 'yes',
        'column.selection': 'no',
        header: 'yes',
        style: {
          'header.font.size': 'medium',
        },
        columns: [],
      },
    },
    'join.type': 'custom',
    'join.result': 'inventory',
    'join.columns': ['monitor'],
    'visualization.result.by': ['monitor'],
    'container.type': 'dashboard',
  },
  [Constants.HYPERCONVERGED_INFRASTRUCTURE]: {
    id: 10000000002254,
    'visualization.name': 'HCI Inventory Summary',
    'visualization.timeline': {
      'relative.timeline': 'today',
      'visualization.time.range.inclusive': 'no',
    },
    'visualization.category': 'Grid',
    'visualization.type': 'Grid',
    'visualization.data.sources': [
      {
        type: 'metric',
        'join.type': 'custom',
        filters: {
          'data.filter': {},
          'result.filter': {},
        },
        'visualization.result.by': ['monitor'],
        'data.points': [
          {
            'data.point': 'nutanix.cpu.percent',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['HCI'],
          },
          {
            'data.point': 'nutanix.memory.used.percent',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['HCI'],
          },
          {
            'data.point': 'nutanix.cluster',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['HCI'],
          },
          {
            'data.point': 'total.vms',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['HCI'],
          },
        ],
      },
    ],
    'visualization.properties': {
      grid: {
        searchable: 'yes',
        'column.selection': 'no',
        header: 'yes',
        style: {
          'header.font.size': 'medium',
        },
        columns: [],
      },
    },
    'join.type': 'custom',
    'join.result': 'inventory',
    'join.columns': ['monitor'],
    'visualization.result.by': ['monitor'],
    'container.type': 'dashboard',
  },
  [Constants.SDN]: {
    id: 10000000002255,
    'visualization.name': 'SDN Inventory Summary',
    'visualization.timeline': {
      'relative.timeline': 'today',
      'visualization.time.range.inclusive': 'no',
    },
    'visualization.category': 'Grid',
    'visualization.type': 'Grid',
    'visualization.data.sources': [
      {
        type: 'metric',
        'join.type': 'custom',
        filters: {
          'data.filter': {},
          'result.filter': {},
        },
        'visualization.result.by': ['monitor'],
        'data.points': [
          {
            'data.point': 'cisco.vmanage.cpu.percent',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['SDN'],
          },
          {
            'data.point': 'cisco.vmanage.memory.used.percent',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['SDN'],
          },
          {
            'data.point': 'cisco.vbond.cpu.percent',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['SDN'],
          },
          {
            'data.point': 'cisco.vbond.memory.used.percent',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['SDN'],
          },
          {
            'data.point': 'cisco.vsmart.cpu.percent',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['SDN'],
          },
          {
            'data.point': 'cisco.vsmart.memory.used.percent',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['SDN'],
          },
          {
            'data.point': 'cisco.vedge.cpu.percent',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['SDN'],
          },
          {
            'data.point': 'cisco.vedge.memory.used.percent',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['SDN'],
          },

          {
            'data.point': 'cisco.meraki.switch.cpu.percent',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['SDN'],
          },
          {
            'data.point': 'cisco.meraki.switch.memory.used.percent',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['SDN'],
          },

          {
            'data.point': 'cisco.meraki.security.cpu.percent',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['SDN'],
          },

          {
            'data.point': 'cisco.meraki.security.memory.used.percent',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['SDN'],
          },
          {
            'data.point': 'cisco.meraki.radio.cpu.percent',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['SDN'],
          },

          {
            'data.point': 'cisco.meraki.radio.memory.used.percent',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['SDN'],
          },
          {
            'data.point': 'cisco.meraki.security.public.ip.address',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['SDN'],
          },
          {
            'data.point': 'cisco.aci.fabric.global.health.score',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['SDN'],
          },
          {
            'data.point': 'cisco.aci.tenant.global.health.score',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['SDN'],
          },
        ],
      },
    ],
    'visualization.properties': {
      grid: {
        searchable: 'yes',
        'column.selection': 'no',
        header: 'yes',
        style: {
          'header.font.size': 'medium',
        },
        columns: [],
      },
    },
    'join.type': 'custom',
    'join.result': 'inventory',
    'join.columns': ['monitor'],
    'visualization.result.by': ['monitor'],
    'container.type': 'dashboard',
  },
  [Constants.SERVICE]: {
    id: 10000000002249,
    'visualization.name': 'System Service Inventory Summary',
    'visualization.timeline': {
      'relative.timeline': 'today',
      'visualization.time.range.inclusive': 'no',
    },
    'visualization.category': 'Grid',
    'visualization.type': 'Grid',
    'visualization.data.sources': [
      {
        type: 'metric',
        'join.type': 'custom',
        filters: {
          'data.filter': {},
          'result.filter': {},
        },
        'visualization.result.by': ['monitor', 'system.service'],
        'data.points': [
          {
            'data.point': 'system.service~status',
            aggregator: 'last',
          },
        ],
      },
    ],
    'visualization.properties': {
      grid: {
        searchable: 'yes',
        'column.selection': 'no',
        header: 'yes',
        style: {
          'header.font.size': 'medium',
        },
        columns: [],
      },
    },
    'join.type': 'custom',
    'join.result': 'inventory',
    'join.columns': ['monitor'],
    'visualization.result.by': ['monitor', 'system.service'],
    'container.type': 'dashboard',
  },
  [Constants.PROCESS]: {
    id: 10000000002250,
    'visualization.name': 'System Process Inventory Summary',
    'visualization.timeline': {
      'relative.timeline': 'today',
      'visualization.time.range.inclusive': 'no',
    },
    'visualization.category': 'Grid',
    'visualization.type': 'Grid',
    'visualization.data.sources': [
      {
        type: 'metric',
        'join.type': 'custom',
        filters: {
          'data.filter': {},
          'result.filter': {},
        },
        'visualization.result.by': ['monitor', 'system.process'],
        'data.points': [
          {
            'data.point': 'system.process~name',
            aggregator: 'last',
          },
          {
            'data.point': 'system.process~status',
            aggregator: 'last',
          },
          {
            'data.point': 'system.process~cpu.percent',
            aggregator: 'last',
          },
          {
            'data.point': 'system.process~memory.used.bytes',
            aggregator: 'last',
          },
        ],
      },
    ],
    'visualization.properties': {
      grid: {
        searchable: 'yes',
        'column.selection': 'no',
        header: 'yes',
        style: {
          'header.font.size': 'medium',
        },
        columns: [],
      },
    },
    'join.type': 'custom',
    'join.result': 'inventory',
    'join.columns': ['monitor'],
    'visualization.result.by': ['monitor', 'system.process'],
    'container.type': 'dashboard',
  },
  [Constants.INTERFACE]: {
    id: 10000000002251,
    'visualization.name': 'Interface Inventory Summary',
    'visualization.timeline': {
      'relative.timeline': 'today',
      'visualization.time.range.inclusive': 'no',
    },
    'visualization.category': 'Grid',
    'visualization.type': 'Grid',
    'visualization.data.sources': [
      {
        type: 'metric',
        'join.type': 'custom',
        filters: {
          'data.filter': {},
          'result.filter': {},
        },
        'visualization.result.by': ['monitor', 'interface'],
        'data.points': [
          {
            'data.point': 'interface~name',
            aggregator: 'last',
          },
          {
            'data.point': 'interface~index',
            aggregator: 'last',
          },
          {
            'data.point': 'interface~alias',
            aggregator: 'last',
          },
          {
            'data.point': 'interface~status',
            aggregator: 'last',
          },
          {
            'data.point': 'interface~traffic.bits.per.sec',
            aggregator: 'last',
          },
          {
            'data.point': 'interface~error.packets',
            aggregator: 'last',
          },
          {
            'data.point': 'interface~traffic.utilization.percent',
            aggregator: 'last',
          },
          {
            'data.point': 'interface~ip.address',
            aggregator: 'last',
          },
        ],
      },
    ],
    'visualization.properties': {
      grid: {
        searchable: 'yes',
        'column.selection': 'no',
        header: 'yes',
        style: {
          'header.font.size': 'medium',
        },
        columns: [],
      },
    },
    'join.type': 'custom',
    'join.result': 'inventory',
    'join.columns': ['monitor'],
    'visualization.result.by': ['monitor', 'interface'],
    'container.type': 'dashboard',
  },
  [Constants.WAN_LINK]: {
    id: 10000000002253,
    'visualization.name': 'WAN Link Inventory Summary',
    'visualization.timeline': {
      'relative.timeline': 'today',
      'visualization.time.range.inclusive': 'no',
    },
    'visualization.category': 'Grid',
    'visualization.type': 'Grid',
    'visualization.data.sources': [
      {
        type: 'metric',
        'join.type': 'custom',
        filters: {
          'data.filter': {},
          'result.filter': {},
        },
        'visualization.result.by': ['monitor', 'ipsla'],
        'data.points': [
          {
            'data.point': 'ipsla~name',
            aggregator: 'last',
          },
          {
            'data.point': 'ipsla~operation.type',
            aggregator: 'last',
          },
          {
            'data.point': 'ipsla~status',
            aggregator: 'last',
          },
          {
            'data.point': 'ipsla~latency.ms',
            aggregator: 'last',
          },
        ],
      },
    ],
    'visualization.properties': {
      grid: {
        searchable: 'yes',
        'column.selection': 'no',
        header: 'yes',
        style: {
          'header.font.size': 'medium',
        },
        columns: [],
      },
    },
    'visualization.tags': [
      'source.ip.address',
      'destination.ip.address',
      'source.interface.name',
    ],
    'join.type': 'custom',
    'join.result': 'inventory',
    'join.columns': ['monitor'],
    'visualization.result.by': ['monitor', 'ipsla'],
    'container.type': 'dashboard',
  },
  [Constants.OTHER]: {
    id: 10000000002252,
    'visualization.name': 'Other Inventory Summary',
    'visualization.timeline': {
      'relative.timeline': 'today',
      'visualization.time.range.inclusive': 'no',
    },
    'visualization.category': 'Grid',
    'visualization.type': 'Grid',
    'visualization.data.sources': [
      {
        type: 'metric',
        'join.type': 'custom',
        filters: {
          'data.filter': {},
          'result.filter': {},
        },
        'visualization.result.by': ['monitor'],
        'data.points': [
          {
            'data.point': 'ping.latency.ms',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['Other'],
          },
          {
            'data.point': 'ping.packet.lost.percent',
            aggregator: 'last',
            'entity.type': 'category',
            entities: ['Other'],
          },
        ],
      },
    ],
    'visualization.properties': {
      grid: {
        searchable: 'yes',
        'column.selection': 'no',
        header: 'yes',
        style: {
          'header.font.size': 'medium',
        },
        columns: [],
      },
    },
    'join.type': 'custom',
    'join.result': 'inventory',
    'join.columns': ['monitor'],
    'visualization.result.by': ['monitor'],
    'container.type': 'dashboard',
  },
  [Constants.STORAGE]: {
    id: 10000000002256,
    'visualization.name': 'Storage Inventory Summary',
    'visualization.timeline': {
      'relative.timeline': 'today',
      'visualization.time.range.inclusive': 'no',
    },
    'visualization.category': 'Grid',
    'visualization.type': 'Grid',
    'visualization.data.sources': [
      {
        type: 'metric',
        'join.type': 'custom',
        filters: { 'data.filter': {}, 'result.filter': {} },
        'visualization.result.by': ['monitor'],
        'data.points': [],
      },
    ],
    'visualization.properties': {
      grid: {
        searchable: 'yes',
        'column.selection': 'no',
        header: 'yes',
        style: { 'header.font.size': 'medium' },
        columns: [],
      },
    },
    'join.type': 'custom',
    'join.result': 'inventory',
    'join.columns': ['monitor'],
    'visualization.result.by': ['monitor'],
    'container.type': 'dashboard',
  },
  [Constants.CONTAINER]: {
    id: 10000000002257,
    'visualization.name': 'Container Inventory Summary',
    'visualization.timeline': {
      'relative.timeline': 'today',
      'visualization.time.range.inclusive': 'no',
    },
    'visualization.category': 'Grid',
    'visualization.type': 'Grid',
    'visualization.data.sources': [
      {
        type: 'metric',
        'join.type': 'custom',
        filters: {
          'data.filter': {},
          'result.filter': {},
        },
        'visualization.result.by': ['monitor', 'docker.container'],
        'data.points': [
          {
            'data.point': 'docker.container~image',
            aggregator: 'last',
          },
          {
            'data.point': 'docker.container~status',
            aggregator: 'last',
          },
          {
            'data.point': 'docker.container~memory.used.bytes',
            aggregator: 'last',
          },
          {
            'data.point': 'docker.container~restarts',
            aggregator: 'last',
          },
          {
            'data.point': 'docker.container~cpu.percent',
            aggregator: 'last',
          },
          {
            'data.point': 'docker.container~processes',
            aggregator: 'last',
          },
        ],
      },
    ],
    'visualization.properties': {
      grid: {
        searchable: 'yes',
        'column.selection': 'no',
        header: 'yes',
        style: {
          'header.font.size': 'medium',
        },
        columns: [],
      },
    },
    'visualization.tags': [],
    'join.type': 'custom',
    'join.result': 'inventory',
    'join.columns': ['monitor'],
    'visualization.result.by': ['monitor', 'docker.container'],
    'container.type': 'dashboard',
  },
}
