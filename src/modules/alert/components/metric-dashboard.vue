<template>
  <FlotoContentLoader :loading="loading">
    <FlotoScrollView>
      <div class="flex flex-col flex-1">
        <MRow class="flex mt-2 alert-dashboard" :gutter="8">
          <MCol :size="4" class="h-full">
            <div class="vue-grid-item h-full flex flex-col">
              <small
                class="font-500 mx-2 my-2 m-0 inline-block text-size-medium"
                >Metric Alert Overview</small
              >
              <div class="flex-1 min-h-0 flex flex-col">
                <ChartView
                  v-if="pieChartData"
                  :key="view"
                  class="mb-3"
                  :widget="pieChartWidget"
                  :data="pieChartData"
                  enable-legend
                  is-preview
                  disable-severity-colors
                  pie-inner-size="70%"
                  :use-force-drill-down="true"
                  @force-drill-down="$emit('force-drill-down', $event)"
                />
              </div>
            </div>
          </MCol>
          <MCol :size="8" class="h-full">
            <div class="vue-grid-item h-full flex flex-col">
              <small
                class="font-500 mx-2 my-2 m-0 inline-block text-size-medium"
                >Metric Alert By Policy Type</small
              >
              <div class="flex-1 min-h-0">
                <ChartView
                  :widget="columnChartWidget"
                  :data="columnChartDataComputed"
                  enable-legend
                  is-preview
                  availability-colors
                  :use-force-drill-down="true"
                  @force-drill-down="$emit('force-drill-down', $event)"
                />
              </div>
            </div>
          </MCol>
          <!-- <MCol :size="2" class="h-full flex flex-col">
            <div class="vue-grid-item mb-2 flex flex-col" style="height: 49%">
              <small
                class="font-500 mx-2 my-2 m-0 inline-block text-size-medium"
                >Acknowledged Alerts
              </small>

              <div class="w-full flex justify-center items-center">
                <NumberAnimation
                  :style="{
                    fontSize: '60px',
                    fontWeight: 600,
                  }"
                  class="text-primary"
                  :from="0"
                  :to="10"
                  :duration="0.5"
                />
                <MIcon name="question" />
              </div>
            </div>
            <div class="vue-grid-item flex flex-col" style="height: 49%">
              <small
                class="font-500 mx-2 my-2 m-0 inline-block text-size-medium"
                >Unacknowledged Alerts
              </small>
              <div class="w-full flex justify-center items-center">
                <NumberAnimation
                  :style="{
                    fontSize: '60px',
                    fontWeight: 600,
                  }"
                  class="text-primary"
                  :from="0"
                  :to="10"
                  :duration="0.5"
                />
                <MIcon name="question" />
              </div>
            </div>
          </MCol> -->
        </MRow>
        <div
          class="flex-1 flex flex-col vue-grid-item my-2"
          style="flex-shrink: 0; min-height: 30vh"
        >
          <small class="font-500 mx-2 my-2 m-0 inline-block text-size-medium"
            >Today Alert Trend</small
          >
          <div class="flex-1 min-h-0">
            <ChartView
              :widget="chartHistoricalWidget"
              :data="historicChartDataComputed"
              is-preview
              availability-colors
              :use-force-drill-down="true"
              @force-drill-down="$emit('force-drill-down', $event, true)"
            />
          </div>
        </div>
        <div v-if="boxes.length" style="flex: 2">
          <SeverityCountBox
            :view="view"
            :boxes="boxes"
            :timeline="timeline"
            :group-type="tabType"
            :selected="severity"
          />
        </div>
        <div
          v-else-if="view === 'live' && boxes.length === 0"
          class="flex-1 flex flex-col vue-grid-item my-2"
        >
          <FlotoNoData />
        </div>
      </div>
    </FlotoScrollView>
  </FlotoContentLoader>
</template>

<script>
import GroupBy from 'lodash/groupBy'
import isEqual from 'lodash/isEqual'

import Capitalize from 'lodash/capitalize'
import Constants from '@constants'
import Bus from '@utils/emitter'
import { buildWidgetContext, makeCounter } from '@utils/socket-event-as-api'
import { WidgetTypeConstants } from '@components/widgets/constants'
import SeverityCountBox from '../components/dashboard/severity-count-box.vue'
import {
  getAlertCountData,
  getColumnChartByType,
  getHistoricChart,
  getPieChartByType,
} from '../helpers/alert-helper'
import ChartView from '@/src/components/widgets/views/chart-view.vue'
// import NumberAnimation from 'vue-number-animation/Number.vue'

const SEVERITY_TO_CONSIDER = [
  Constants.DOWN,
  Constants.UNREACHABLE,
  Constants.CRITICAL,
  Constants.MAJOR,
  Constants.WARNING,
  // Constants.CLEAR,
]

export default {
  name: 'MetricDashboard',
  components: {
    SeverityCountBox,
    ChartView,
    // NumberAnimation,
  },
  inject: { SocketContext: { default: {} } },
  props: {
    timeline: {
      type: Object,
      required: true,
    },
    view: {
      type: String,
      default: 'live',
    },
    severity: {
      type: String,
      default: 'total',
    },
  },
  data() {
    return {
      count: {},
      boxes: [],
      loading: true,
      tabType: 'metric',
      columnChartData: {},
      historicChartData: {},
      pieChartData: null,
    }
  },
  computed: {
    columnChartDataComputed() {
      const severity = this.severity
      if (severity && severity !== 'total') {
        return {
          ...this.columnChartData,
          series: (this.columnChartData.series || []).filter(
            (c) => c.counter.toLowerCase() === severity.toLowerCase()
          ),
        }
      }
      return this.columnChartData
    },
    historicChartDataComputed() {
      const severity = this.severity
      if (severity && severity !== 'total') {
        return {
          ...this.historicChartData,
          series: (this.historicChartData.series || []).filter(
            (c) => c.entity.toLowerCase() === severity.toLowerCase()
          ),
        }
      }
      return this.historicChartData
    },
    pieChartWidget() {
      return buildWidgetContext({
        groupType: this.view === 'live' ? 'alert' : 'alert.flap',
        category: WidgetTypeConstants.GAUGE,
        widgetType: WidgetTypeConstants.PIE,
        groupCategory: this.tabType,
        counters: [makeCounter('severity', 'count')],
        resultBy: ['policy.type'],
        timeline: this.timeline,
      })
        .appendToGroup('alert', { severity: SEVERITY_TO_CONSIDER })
        .setWidgetProperties({
          legendEnabled: true,
        })
        .getContext()
    },
    chartHistoricalWidget() {
      return buildWidgetContext({
        groupType: this.view === 'live' ? 'policy.flap' : 'policy.flap',
        category: WidgetTypeConstants.CHART,
        widgetType: WidgetTypeConstants.STACKED_VERTICAL_BAR,
        groupCategory: this.tabType,
        counters: [makeCounter('severity', 'count')],
        resultBy: ['severity'],
        timeline: this.timeline,
      })
        .setWidgetProperties({
          styleSetting: {
            chartOptions: {
              yAxis: {
                allowDecimal: false,
              },
            },
          },
        })
        .appendToGroup('alert', { severity: SEVERITY_TO_CONSIDER })
        .getContext()
    },
    columnChartWidget() {
      return buildWidgetContext({
        groupType: this.view === 'live' ? 'policy.stream' : 'policy.flap',
        timeline: this.timeline,
        category: WidgetTypeConstants.TOPN,
        widgetType: WidgetTypeConstants.STACKED_VERTICAL_BAR,
        groupCategory: this.tabType,
        counters: [makeCounter('severity', 'count')],
        resultBy: ['policy.type', 'severity'],
      })
        .setWidgetProperties({
          styleSetting: {
            chartOptions: {
              yAxis: {
                allowDecimal: false,
              },
            },
          },
        })
        .appendToGroup('policy.stream', { severity: SEVERITY_TO_CONSIDER })
        .setWidgetProperties({
          styleSettings: {
            chartOptions: {
              yAxis: {
                allowDecimals: false,
              },
            },
          },
        })
        .getContext()
    },
  },
  watch: {
    view(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.requestAlertData()
      }
    },
    timeline(newValue, oldValue) {
      if (!isEqual(newValue, oldValue)) {
        this.requestAlertData()
      }
    },
  },
  created() {
    Bus.$on('socket:connected', this.requestAlertData)
    if (this.SocketContext.connected) {
      this.requestAlertData()
    }
    this.$once('hook:beforeDestroy', () => {
      Bus.$off('socket:connected', this.requestAlertData)
    })
  },
  methods: {
    requestPieChartData() {
      getPieChartByType(
        this.timeline,
        this.tabType,
        SEVERITY_TO_CONSIDER,
        this.view
      ).then((data) => {
        this.pieChartData = Object.freeze(data)
      })
    },
    requestColumnChartData() {
      getColumnChartByType(
        this.timeline,
        this.tabType,
        SEVERITY_TO_CONSIDER,
        this.view
      ).then((data) => {
        this.columnChartData = Object.freeze(data)
      })
    },
    requestHistoricChartData() {
      // if (this.view === 'live') {
      //   return
      // }
      getHistoricChart(this.timeline, this.tabType, SEVERITY_TO_CONSIDER).then(
        (data) => {
          this.historicChartData = Object.freeze(data)
        }
      )
    },
    requestAlertData() {
      this.loading = true
      this.pieChartData = null
      this.columnChartData = {}
      this.historicChartData = {}
      this.requestPieChartData()
      this.requestColumnChartData()
      this.requestHistoricChartData()
      getAlertCountData(this.timeline, this.tabType, this.view).then((rows) => {
        this.handleCountReceived(rows)
      })
    },
    async handleCountReceived(rows) {
      const categoryGroups = GroupBy(rows, 'object.category')
      const boxes = Object.keys(categoryGroups).map((category) => {
        const severityGroups = categoryGroups[category]
          .filter((item) => SEVERITY_TO_CONSIDER.includes(item.severity))
          .reduce(
            (prev, item) => ({
              ...prev,
              [item.severity]:
                (prev[item.severity] || 0) +
                (item.count || item['severity.count'] || 0),
            }),
            {}
          )
        const total = Object.keys(severityGroups).reduce(
          (prev, key) => prev + severityGroups[key],
          0
        )
        return {
          category: category,
          text:
            category.toLowerCase() === 'hci'
              ? category.toUpperCase()
              : Capitalize(category),
          ...SEVERITY_TO_CONSIDER.reduce(
            (obj, severity) => ({
              ...obj,
              [severity.toLowerCase()]: severityGroups[severity] || 0,
              [`${severity.toLowerCase()}_percentage`]:
                (100 * (severityGroups[severity] || 0)) / total,
            }),
            {}
          ),
          total,
        }
      })
      this.boxes = boxes
      this.count = Object.freeze(
        boxes.reduce(
          (result, item) => {
            return Object.keys(result).reduce((total, key) => {
              total[key] += item[key] || 0
              return total
            }, result)
          },
          SEVERITY_TO_CONSIDER.reduce(
            (obj, severity) => ({
              ...obj,
              [severity.toLowerCase()]: 0,
            }),
            {}
          )
        )
      )
      this.$emit('count', this.count)
      this.loading = false
    },
    navigateToStream() {
      this.$router.push(
        this.$currentModule.getRoute('stream', {
          params: {
            tab: this.tabType,
            category: 'Server',
          },
          query: {
            t: encodeURIComponent(btoa(JSON.stringify(this.timeline))),
            view: this.view,
            stream: 'all',
          },
        })
      )
    },
  },
}
</script>
