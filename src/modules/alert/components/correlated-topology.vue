<template>
  <div
    ref="mainContainer"
    class="background-neutral-light flex flex-col flex-1 min-h-0 w-full h-full rounded-lg"
  >
    <FlotoContentLoader :loading="loading" :row-gutter="0">
      <Graph
        ref="graphRef"
        layout="preset"
        show-dashed-edges
        center
        :root-key="String(target)"
        fit
        :edge-tooltip="EdgeToolTipComponent"
        :nodes="nodes"
        :edges="edges"
        :tooltip-component="EdgeToolTipComponent"
        disable-preference-persist
      />
    </FlotoContentLoader>
  </div>
</template>

<script>
import Bus from '@utils/emitter'
import { generateId } from '@utils/id'
import { WidgetTypeConstants } from '@components/widgets/constants'
import { topologyWorker, objectDBWorker, severityDBWorker } from '@/src/workers'
import { getMonitorsApi } from '../../settings/monitoring/monitors-api'
import { SEVERITY_MAP, transformMonitorForLocalDb } from '@data/monitor'
import { UserPreferenceComputed } from '@state/modules/user-preference'
import Graph from '@components/monitor-graph/graph.vue'
import EdgeTooltip from '@modules/topology/components/edge-tooltip.vue'
import { correlatedTopologyWidget } from '../helpers/alert-helper'

export default {
  name: 'CorrelatedTopology',
  components: {
    Graph,
  },
  props: {
    alert: {
      type: Object,
      required: true,
    },
    timeline: {
      type: Object,
      default: undefined,
    },
  },
  data() {
    this.view = 'tree'
    return {
      guid: generateId(),
      result: undefined,
      nodes: [],
      edges: {},
      loading: true,
    }
  },
  computed: {
    ...UserPreferenceComputed,

    EdgeToolTipComponent() {
      return EdgeTooltip
    },
    target() {
      return this.alert.monitor
    },
  },
  created() {
    Bus.$on(this.$constants.UI_WIDGET_RESULT_EVENT, this.handleReceiveData)
    this.$once('hook:beforeDestroy', () => {
      Bus.$off(this.$constants.UI_WIDGET_RESULT_EVENT, this.handleReceiveData)
    })

    this.requestCorrelatedTopology()
  },
  methods: {
    requestCorrelatedTopology() {
      let widget = correlatedTopologyWidget().generateWidgetDefinition()
      widget[this.$constants.UI_EVENT_UUID] = this.guid
      widget['policy.id'] = this.alert.policyId
      widget['entity.id'] = this.alert.monitor
      widget['metric'] = this.alert.metric
      widget['instance'] = this.alert.instance

      Bus.$emit('server:event', {
        'event.type': this.$constants.UI_WIDGET_RESULT_EVENT,
        'event.context': widget,
      })
    },
    handleReceiveData(response) {
      if (response[this.$constants.UI_EVENT_UUID] !== this.guid) {
        return
      }
      this.result = response
      this.buildNodeAndEdges()
    },

    async buildNodeAndEdges() {
      if (!this.result) {
        return
      }
      if (!this.result?.result?.[WidgetTypeConstants.GRID]?.data?.[0]) {
        this.nodes = []
        this.edges = []
        this.loading = false
        return
      }
      let result
      const severities = await severityDBWorker.getSeverity({})
      let objectCache = await objectDBWorker.getObjectsAsMap({}, ['id', 'name'])
      let getAllObjectFromApi = await getMonitorsApi(
        {
          params: {
            'admin.role': 'yes',
          },
        },
        false
      )
      let allObjects = getAllObjectFromApi.result.map(
        transformMonitorForLocalDb
      )

      let severityMap = {}
      for (let i = 0; i < (severities || []).length; i++) {
        if (!severities[i].instance) {
          severityMap[severities[i].entity] = severities[i].severity
        }
      }
      allObjects = allObjects.reduce((previous, item) => {
        return {
          ...previous,
          [item.id]: {
            ...item,
            severity: severityMap[item.id],
            severityNumber: SEVERITY_MAP[severityMap[item.id]],
            hasPermission: !!objectCache[item.id],
          },
        }
      }, {})

      severityMap = null

      result = await topologyWorker.makeTopologyNodes(
        {
          result: {
            ...JSON.parse(
              this.result.result[WidgetTypeConstants.GRID].data[0][
                'correlation.map'
              ] || {}
            ),
          },
        },
        {
          view: this.view,
          theme: this.theme,
          link: true,
          filledNode: true,
          height: this.$refs.mainContainer.offsetHeight,
          width: this.$refs.mainContainer.offsetWidth,
        },
        allObjects,
        severities
      )
      this.nodes = Object.freeze(result.nodes)
      this.edges = Object.freeze(result.edges)

      this.$nextTick(() => {
        this.loading = false
      })
    },
  },
}
</script>
