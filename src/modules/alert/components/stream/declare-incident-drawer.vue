<template>
  <FlotoDrawerForm
    :open="open"
    width="40%"
    @cancel="handleCancel"
    @submit="handleFormSubmit"
    @reset="resetForm"
  >
    <template v-slot:header> Declare Incident </template>
    <MRow>
      <MCol :size="12">
        <FlotoFormItem
          v-model="formData.subject"
          :disabled="disabled"
          label="Subject"
          rules="required"
        />
      </MCol>
    </MRow>

    <MRow>
      <MCol :size="12">
        <FlotoFormItem
          v-model="formData.description"
          :disabled="disabled"
          label="Description"
          rules="required"
          type="textarea"
          :rows="5"
        />
      </MCol>
    </MRow>

    <MRow>
      <MCol :size="6">
        <FlotoFormItem label="Integration Profile" rules="required">
          <IntegrationProfileProvier>
            <IntegrationProfilePicker
              id="integration-profile-dropdown"
              v-model="formData.incidents"
              class="w-full"
              searchable
              is-grid-selector
              :max-allowed-selections="1"
            />
          </IntegrationProfileProvier>
        </FlotoFormItem>
      </MCol>
      <MCol :size="2" class="text-right">
        <MPermissionChecker
          :permission="$constants.INTEGRATION_CREATE_PERMISSION"
        >
          <!-- <FlotoLink
              id="create-new-action-btn"
              :to="

              "
              as-button
              outline
              class="items-center mt-6"
            >
              Create Integration Profile
            </FlotoLink> -->

          <MButton
            class="items-center mt-6"
            @click="navigateToIntegrationProfile"
          >
            Create Integration Profile
          </MButton>
        </MPermissionChecker>
      </MCol>
    </MRow>

    <!-- <MRow :size="12">
        <MCol :size="6">
          <FlotoFormItem label="Urgency" rules="required">
            <FlotoDropdownPicker
              v-model="formData.urgency"
              placeholder="Select"
              :options="urgencyOptions"
              allow-clear
            />
          </FlotoFormItem>
        </MCol>
      </MRow> -->

    <!-- <MRow>
        <MCol :size="6">
          <FlotoFormItem id="add-email-btn" label="Notify via Email">
            <FlotoTagsPicker
              v-model="formData.email"
              always-text-mode
              type="email"
              :full-width="true"
              placeholder="Email Recipients"
              title="Email Recipients"
            />
          </FlotoFormItem>
        </MCol>
        <MCol :size="6">
          <FlotoFormItem id="add-number-btn" label="Notify via SMS">
            <FlotoTagsPicker
              v-model="formData.sms"
              type="mobile_number"
              always-text-mode
              :full-width="true"
              placeholder="SMS Recipients"
              title="SMS Recipients"
            />
          </FlotoFormItem>
        </MCol>
      </MRow> -->

    <MCol :size="12" class="mt-4">
      <span class="text-neutral"> For more information: </span>
      <a
        href="https://docs.motadata.com/motadata-aiops-docs/integration-profile/create-integration-profile"
        target="_blank"
        >Motadata Integration</a
      >
      <MIcon name="external-link" class="ml-1 text-primary" />
    </MCol>

    <template v-slot:actions="{ submit, reset }">
      <span class="mandatory">
        <span class="text-secondary-red">*</span> fields are mandatory</span
      >
      <MButton id="reset-btn-id" variant="default" class="mr-2" @click="reset"
        >Reset</MButton
      >
      <MButton id="submit-btn" :loading="processing" @click="submit"
        >Declare Incident</MButton
      >
    </template>
  </FlotoDrawerForm>
</template>

<script>
// import Bus from '@utils/emitter'
import Bus from '@utils/emitter'

import IntegrationProfilePicker from '@components/data-picker/integration-profile-picker.vue'
import IntegrationProfileProvier from '@components/data-provider/integration-profile-provider.vue'

export default {
  name: 'DeclareIncidentDrawer',
  components: { IntegrationProfilePicker, IntegrationProfileProvier },
  props: {
    alert: {
      type: Object,
      required: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    // this.urgencyOptions = [
    //   { key: 'low', text: 'Low' },
    //   { key: 'medium', text: 'Medium' },
    //   { key: 'high', text: 'High' },
    //   { key: 'urgent', text: 'Urgent' },
    // ]
    return {
      formData: {
        subject: this.buildSubject(),
        description:
          'Object Name: $$$object.name$$$\nIP / Host: $$$object.ip$$$\nObject Type: $$$object.type$$$\nMetric: $$$counter$$$\nMetric Value: $$$value$$$\nSeverity: $$$severity$$$\nPolicy Name: $$$policy.name$$$\nPolicy Type: $$$policy.type$$$\nMessage: $$$policy.message$$$',
      },
      open: true,
      processing: false,
    }
  },
  beforeDestroy() {
    this.handleCancel()
  },
  methods: {
    handleCancel(nevigateToIntegrationProfile) {
      this.open = false
      setTimeout(() => {
        this.$emit('cancel')

        if (nevigateToIntegrationProfile) {
          this.$router.push(
            this.$modules.getModuleRoute('integration', 'integration-profile')
          )
        }
      }, 400)
    },
    resetForm() {
      this.formData = {}
    },
    handleFormSubmit() {
      this.processing = true

      Bus.$emit(this.$constants.EVENT_SEVERITY_UPDATED)

      Bus.$emit('server:event', {
        'event.type': this.$currentModule.getConfig().SERVICENOW_INTEGRATION,
        'event.context': {
          ...this.transformDataForDeclarIncident(),
        },
      })
      this.open = false
      setTimeout(() => {
        this.$emit('cancel')
      }, 400)
    },
    navigateToIntegrationProfile() {
      this.handleCancel(true)
    },
    buildSubject() {
      return `${this.alert.policy}-${this.alert.monitorDisplay}-${
        this.alert.counterRawName
      }${
        this.alert.instance && this.alert.instance !== ''
          ? `-${this.alert.instance}`
          : ''
      }`
    },
    transformDataForDeclarIncident() {
      return {
        severity: this.alert.severity,
        'policy.threshold': this.alert.policyThreshold,
        'object.category': this.alert.category,
        'entity.id': this.alert.monitor,
        metric: this.alert.counterRawName,
        id: this.formData.incidents,
        'policy.type': this.alert.policyType,
        value: this.alert.value,
        'object.name': this.alert.monitorDisplay,
        'policy.id': this.alert.policyId,
        'create.severity': true,
        subject: this.formData.subject,
        description: this.formData.description,
        ...(this.alert.instance && this.alert.instance !== ''
          ? { instance: this.alert.instance }
          : {}),
      }
    },
  },
}
</script>
