<template>
  <FlotoContentLoader :loading="loading" style="min-height: 200px">
    <MRow :gutter="0">
      <MCol>
        <MRow :gutter="0">
          <MCol :size="6">
            <h6 class="text-left font-normal" style="font-size: 0.75rem">
              Message :
              <span v-html="alert.message" />
            </h6>
            <p
              v-if="alert.groupCategory === 'metric'"
              class="flex text-small small opacity-mid"
            >
              {{ alert.duration | duration }}
            </p>
          </MCol>
          <MCol class="text-right" :size="6">
            <span class="mr-2">
              <a @click.stop="handleShowLastTriggeredModal('notifications')">
                Last Triggered Notifications
              </a>
            </span>
            <span class="mr-2">|</span>
            <span>
              <a @click.stop="handleShowLastTriggeredModal('actions')">
                Last Triggered Actions
              </a>
            </span>
            <!-- <MButton
            v-if="childAlertCount > 0"
            id="chid-alert"
            class="ml-3 radius-full"
            outline
            @click="onClickChildAlert"
          >
            {{ childAlertCount }} Child Alert
            </MButton>-->
          </MCol>
        </MRow>
        <MRow :gutter="0" class="relative pb-2">
          <MCol :size="shouldRenderChart ? 5 : 12" class="flex">
            <div class="flex flex-col flex-1 min-w-0 justify-between">
              <div>
                <table
                  key="tabular-list"
                  class="w-full item-list-table rounded bordered"
                >
                  <tr class="mt-10">
                    <td class="alert-table-td alert-td-width">
                      <span>Alert ID :</span>
                    </td>
                    <td class="alert-table-td">
                      <span>{{ alert.policyId }}</span>
                    </td>
                  </tr>
                  <tr v-if="alert.groupCategory === 'metric'">
                    <td class="alert-table-td">
                      <span>Source Host :</span>
                    </td>
                    <td class="alert-table-td test">{{ monitor.ip }}</td>
                  </tr>
                  <tr>
                    <td class="alert-table-td">
                      <span>Metric :</span>
                    </td>
                    <td class="alert-table-td">
                      <span>{{ alert.metric }}</span>
                    </td>
                  </tr>
                  <tr v-if="shouldShowTriggerCondition">
                    <td class="alert-table-td">
                      <span>Trigger Conditions :</span>
                    </td>
                    <td class="alert-table-td">
                      <span>{{ alert.triggerCondition }}</span>
                    </td>
                  </tr>
                  <tr v-if="alert.groupCategory === 'metric'">
                    <td class="alert-table-td">
                      <span>Group :</span>
                    </td>
                    <td class="alert-table-td">
                      <GroupPicker
                        v-if="monitor.groups"
                        :value="monitor.groups"
                        disabled
                      />
                    </td>
                  </tr>
                  <tr v-if="alert.groupCategory !== 'metric'">
                    <td class="alert-table-td">
                      <span>Last Modified at :</span>
                    </td>
                    <td class="alert-table-td">
                      {{ alert.lastSeen | datetime }}
                    </td>
                  </tr>
                </table>
              </div>

              <div class="mt-3">
                <MButton outline @click="navigateToDetail('overview')">
                  View Detail
                </MButton>
                <!-- <MButton
            class="ml-3"
            outline
            @click="onClickViewDetail('rca')"
          >
            RCA
            </MButton>-->
                <!-- <MButton
              class="ml-3"
              outline
              @click="navigateToDetail('collaboration')"
            >
              Collaboration
            </MButton> -->
              </div>
            </div>
          </MCol>
          <MCol
            v-if="shouldRenderChart"
            :size="7"
            class="flex"
            style="height: 250px"
          >
            <div class="flex flex-col flex-1 ml-4 bordered rounded-lg">
              <div
                class="flex-1 flex pb-0"
                :class="{ 'p-2': !isCorrelatedAlert }"
              >
                <AlertTrendChart
                  v-if="!isCorrelatedAlert"
                  :alert="alert"
                  :timeline="timeline"
                  :aggrigator="trendChartAggrigator"
                />
                <CorrelatedTopology
                  v-else
                  :alert="alert"
                  :timeline="timeline"
                />
              </div>
            </div>
          </MCol>
          <LastTriggeredModal
            v-if="showLastTriggeredModal"
            :alert="alert"
            :type="lastTriggeredType"
            :timeline="timeline"
            @close="handleCloseLastTriggeredModal"
          />
        </MRow>
      </MCol>
    </MRow>
  </FlotoContentLoader>
</template>

<script>
import { objectDBWorker } from '@/src/workers'
import AlertTrendChart from '../alert-trend-chart.vue'
import CorrelatedTopology from '../correlated-topology.vue'
import LastTriggeredModal from './last-triggerd-modal.vue'

export default {
  name: 'ExpandedRow',
  components: {
    AlertTrendChart,
    LastTriggeredModal,
    CorrelatedTopology,
  },
  inject: { counterContext: { default: { options: new Map() } } },
  props: {
    alert: {
      type: Object,
      required: true,
    },
    timeline: {
      type: Object,
      required: true,
    },
    view: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      loading: false,
      monitor: {},
      showLastTriggeredModal: null,
      lastTriggeredType: null,
    }
  },
  computed: {
    shouldRenderChart() {
      const alert = this.alert
      if (
        ['availability', 'log', 'flow'].includes(
          this.alert.policyType.toLowerCase()
        )
      ) {
        return true
      }
      const counter = this.counterContext.options.get(alert.counterRawName)
      if (counter) {
        return counter.dataType.includes('numeric')
      }
      return false
    },
    isCorrelatedAlert() {
      return this.alert.isCorrelatedAlert
    },
    shouldShowTriggerCondition() {
      return (
        this.alert.severity?.toLowerCase() !== 'clear' &&
        this.alert.policyType?.toLowerCase() !== 'availability'
      )
    },
    trendChartAggrigator() {
      if (['log', 'flow'].includes(this.alert.policyType.toLowerCase())) {
        const alert = this.alert
        if (alert.counterRawName === 'status') {
          return 'avg'
        }
        const counter = this.counterContext.options.get(alert.counterRawName)
        if (counter) {
          return counter.dataType.includes('numeric') ? 'avg' : 'count'
        }
        return 'count'
      }
      return 'avg'
    },
  },
  created() {
    this.getMonitor()
  },
  methods: {
    handleShowLastTriggeredModal(type) {
      this.showLastTriggeredModal = true
      this.lastTriggeredType = type
    },
    handleCloseLastTriggeredModal() {
      this.showLastTriggeredModal = null
      this.lastTriggeredType = null
    },
    navigateToDetail(tab) {
      const alert = this.alert
      const param = {
        ...alert,
        view: this.view,
      }
      this.$router.push(
        this.$currentModule.getRoute('detail', {
          params: {
            ...this.$route.params,
            tab: tab || 'overview',
            uuid: encodeURIComponent(btoa(JSON.stringify(param))),
          },
          query: {
            t: encodeURIComponent(btoa(JSON.stringify(this.timeline))),
          },
        })
      )
    },
    async getMonitor() {
      const monitor = await objectDBWorker.getObjectById(this.alert.monitor)
      if (monitor) {
        this.monitor = Object.freeze(monitor)
      }
    },
  },
}
</script>
