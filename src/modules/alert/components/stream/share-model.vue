<template>
  <FlotoConfirmModal
    ref="modelRef"
    open
    hide-icon
    :variant="`primary-alt ${increaseZIndex ? 'omnibox-model-z-index' : ''}`"
    width="30%"
    @confirm="handleConfirmAction"
  >
    <template v-slot:message>
      <FlotoForm
        :show-notification="false"
        class="-m-3"
        @submit="handleConfirmAction"
        @reset="resetForm"
      >
        <div class="flex items-center">
          <div class="flex-1">
            <slot name="title">
              <h5 class="mb-0 text-primary">Share {{ shareType }}</h5>
            </slot>
          </div>
          <MButton
            id="close-used-count"
            variant="transparent"
            :shadow="false"
            shape="circle"
            @click="hideShareModal"
          >
            <MIcon name="times" class="text-neutral-light" />
          </MButton>
        </div>

        <MDivider />
        <UserProvider>
          <HandlerProvider ref="handlerProviderRef">
            <FlotoFormItem rules="required" label="Share With">
              <UserOrEmailPicker
                v-model="formData.notifyTo"
                name="parameter-name"
                title="@User or Email or /Handle"
                always-text-mode
                rules="required"
                disable-justify-around
              />
            </FlotoFormItem>
          </HandlerProvider>
        </UserProvider>

        <FlotoFormItem
          v-model="formData.message"
          rules="required"
          label="Message"
          type="textarea"
          :rows="3"
        />

        <template v-slot:submit="{ submit, reset }">
          <MRow>
            <MCol class="text-right">
              <MButton
                id="ip-cancle"
                variant="default"
                class="mr-2"
                @click="reset"
              >
                Reset
              </MButton>
              <MButton
                id="send-test-ip-id"
                name="test-ip"
                :loading="processing"
                @click="submit"
              >
                Share
              </MButton>
            </MCol>
          </MRow>
        </template>
      </FlotoForm>
    </template>
    <template v-slot:action-container>
      <span />
    </template>
  </FlotoConfirmModal>
</template>

<script>
import UserOrEmailPicker from '@components/data-picker/user-or-email-picker.vue'
import HandlerProvider from '@/src/components/data-provider/handler-provider.vue'
import UserProvider from '@/src/components/data-provider/user-provider.vue'

export default {
  name: 'ShareModel',
  components: { UserOrEmailPicker, HandlerProvider, UserProvider },
  props: {
    shareType: {
      type: String,
      default: () => {
        return ''
      },
    },
    processing: {
      type: Boolean,
      default: false,
    },
    increaseZIndex: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      formData: {},
    }
  },
  created() {
    this.setStyle()
  },

  methods: {
    hideShareModal() {
      this.$emit('hide', true)
    },
    handleConfirmAction() {
      let handlerMap = {}

      if (this.$refs.handlerProviderRef) {
        handlerMap = this.$refs.handlerProviderRef.gethandlerMaps()
      }

      const recipients = this.formData.notifyTo.map((r) => {
        const isHandles = r.startsWith('/')
        const recipient = r.startsWith('@') ? r.slice(1) : r

        const type = r.startsWith('@')
          ? 'user'
          : isHandles
          ? 'channel'
          : 'email'

        return {
          type,
          recipient,

          ...(isHandles
            ? {
                id: isHandles ? handlerMap[recipient]?.id : undefined,

                integration: isHandles
                  ? handlerMap[recipient]?.integrationType
                  : undefined,
              }
            : {}),
        }
      })
      this.$emit('submit', this.formData, recipients)
    },
    resetForm() {
      this.formData = {}
    },
    setStyle() {
      setTimeout(() => {
        this.$nextTick(() => {
          document.querySelectorAll('.ant-modal-root').forEach((root) => {
            const mask = root.querySelector('.ant-modal-mask')
            const isOmniboxModel = root.querySelector('.omnibox-model-z-index')
            if (isOmniboxModel) {
              mask.style.zIndex = '1055' // Set the z-index if the child exists
            }
          })
        })
      }, 0)
    },
  },
}
</script>
