<template>
  <MRow class="slide-filters items-center pr-2">
    <!-- <MCol :size="2">
      <FlotoFormItem label="Severity">
        <FlotoDropdownPicker
          id="filter-monitor-picker"
          v-model="currentValue.severities"
          multiple
          :options="severityOptions"
        >
          <template v-slot:before-menu-text="{ item }">
            <Severity
              :severity="item.key"
              :shadow="true"
              :disable-tooltip="true"
              class="mx-1"
            />
          </template>
        </FlotoDropdownPicker>
      </FlotoFormItem>
    </MCol> -->
    <!-- <MCol :size="2">
      <FlotoFormItem label="Monitor">
        <MonitorPicker
          id="filter-monitor-picker"
          v-model="currentValue.monitors"
          class="mt-1"
          multiple
        />
      </FlotoFormItem>
    </MCol> -->
    <MCol :size="2" class="alert-multi-spacer">
      <FlotoFormItem label="Alert">
        <PolicyPicker
          id="alert-picker"
          v-model="currentValue.policies"
          class="mt-1"
          placeholder="Alerts"
          multiple
        />
      </FlotoFormItem>
    </MCol>
    <MCol :size="2">
      <FlotoFormItem label="Metric">
        <FlotoDropdownPicker
          id="metric-picker"
          v-model="currentValue.metrics"
          class="mt-1"
          :options="metricOptions"
          placeholder="Metric"
          multiple
        />
      </FlotoFormItem>
    </MCol>
    <MCol :size="2">
      <FlotoFormItem label="Tag">
        <FlotoDropdownPicker
          id="tag"
          v-model="currentValue.tags"
          class="mt-1"
          :options="tagsOptions"
          placeholder="Tag"
          multiple
        />
      </FlotoFormItem>
    </MCol>
    <!-- <MCol :size="2">
      <FlotoFormItem label="Acknowledged">
        <FlotoDropdownPicker
          id="acknowledged"
          v-model="currentValue.acknowledgeds"
          :options="acknowledgedOptions"
          placeholder="Acknowledged"
        />
      </FlotoFormItem>
    </MCol> -->
    <MCol class="flex justiy-between items-start text-right" :size="2">
      <div class="flex-1 items-center mt-6">
        <MButton
          id="reset-btn"
          variant="default"
          @click="
            $emit('change', {
              groups: [],
              monitors: [],
              policies: [],
              metrics: [],
              tags: [],
              severities: [],
              acknowledgeds: [],
            })
          "
        >
          Reset
        </MButton>
        <MButton id="apply-btn" class="ml-2" @click="apply">Apply</MButton>
      </div>
    </MCol>
    <MButton
      id="close-filter"
      variant="transparent"
      :shadow="false"
      shape="circle"
      style="position: absolute; top: 0; right: 0"
      class="monitor-agent-filter-close"
      @click="$emit('hide')"
    >
      <MIcon name="times" class="text-neutral-light" />
    </MButton>
  </MRow>
</template>

<script>
import Uniq from 'lodash/uniq'
import UniqBy from 'lodash/uniqBy'
import Constants from '@constants'
// import MonitorPicker from '@components/data-picker/monitor-picker.vue'
import PolicyPicker from '@components/data-picker/policy-picker.vue'

const SEVERITY_TO_CONSIDER = [
  Constants.DOWN,
  Constants.UNREACHABLE,
  Constants.CRITICAL,
  Constants.MAJOR,
  Constants.WARNING,
  Constants.CLEAR,
]

export default {
  name: 'Filters',
  components: {
    // MonitorPicker,
    PolicyPicker,
  },
  inject: { policyContext: { default: { options: new Map() } } },
  model: {
    event: 'change',
  },
  props: {
    value: { type: [Object, Array], required: true },
    category: { type: String, required: true },
  },
  data() {
    this.severityOptions = SEVERITY_TO_CONSIDER.map((i) => ({
      text: i,
      key: i,
    }))
    this.acknowledgedOptions = [
      { key: 'yes', text: 'Yes' },
      { key: 'no', text: 'No' },
    ]
    return {
      currentValue: { ...this.value },
    }
  },
  computed: {
    metricOptions() {
      return UniqBy(
        Array.from(this.policyContext.options.values())
          .filter(({ metric }) => metric)
          .map(({ metric }) => ({
            key: metric,
            text: metric.replace(/[~^]/g, '.'),
          })),
        'key'
      )
    },
    tagsOptions() {
      const tagsMap = {}
      Array.from(this.policyContext.options.values()).forEach(
        ({ tags, key }) => {
          tags.forEach((tag) => {
            tagsMap[tag] = [...(tagsMap[tag] || []), key]
          })
        }
      )
      return Uniq(Object.keys(tagsMap)).map((tag) => ({
        text: tag,
        key: tag,
      }))
    },
  },
  watch: {
    value(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.currentValue = { ...newValue }
      }
    },
  },
  methods: {
    apply() {
      this.$emit('change', this.currentValue)
    },
  },
}
</script>
