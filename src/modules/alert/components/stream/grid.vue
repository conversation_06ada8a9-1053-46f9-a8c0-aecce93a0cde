<template>
  <CounterProvider
    v-if="shouldShowModule"
    :search-params="counterSearchParams"
    should-include-motadata-agent-counters
  >
    <FlotoContentLoader :loading="loading">
      <MGrid
        ref="baseGridRef"
        class="h-100 hide-expand-column"
        :resource-name="groupType"
        :columns="columns"
        :search-term="searchTerm"
        :data="data"
        :filters="filters"
        @column-change="$emit('column-change', $event)"
      >
        <!-- expandable
          detail="detailRow"
          <template v-slot:detailRow="{ props }">
            <ExpandedRow
              :alert="props.dataItem"
              :timeline="timeline"
              :view="view"
            />
          </template> -->

        <template v-slot:policy="{ item }">
          <div
            class="flex items-center cursor-pointer"
            @click="navigateToDetail(item, 'overview')"
          >
            <!-- <MIcon
                :name="expanded ? 'chevron-down' : 'chevron-right'"
                class="text-neutral-light mr-1"
              /> -->
            <Severity :severity="item.severity" class="mr-2" />
            <a class="expandeble-row text-ellipsis">
              {{ item.policy }}
            </a>
          </div>
        </template>

        <template v-slot:type="{ item, props }">
          <MTag rounded :closable="false" class="tag-primary">
            {{ item[props.field] }}
          </MTag>
        </template>

        <template v-slot:instance="{ item }">
          <span :title="item.instance"> {{ item.instance }}</span>
        </template>

        <template v-slot:monitor="{ item }">
          <FlotoLink
            :to="
              $modules.getModuleRoute('inventory', 'monitor-template', {
                params: {
                  monitorId: item.monitor,
                  category:
                    item.category ===
                    $currentModule.getConfig().CORRELATED_POLICIES
                      ? $constants.NETWORK
                      : [
                          $constants.SERVER,
                          $constants.MIDDLEWARE,
                          $constants.DATABASE,
                          $constants.WEB_SERVER,
                          $constants.CUSTOM,
                        ].includes(item.category)
                      ? $constants.SERVER
                      : item.category,
                },
              })
            "
          >
            {{ item.monitorDisplay }}
          </FlotoLink>
        </template>

        <template v-slot:thresholdValue="{ item }">
          <b>{{ item.value || '--' }}</b>
        </template>

        <template v-slot:tag="{ item }">
          <SelectedItemPills
            v-if="item.tag.length"
            :value="item.tag"
            disabled
            placeholder="---"
            multiple
          />
          <span v-else />
        </template>

        <template v-slot:metric="{ item }">
          <span :title="item.metric"> {{ item.metric }}</span>
        </template>

        <template v-slot:count="{ item }">
          {{ item.count }}
        </template>

        <template v-slot:firstSeen="{ item }">
          {{ item.firstSeen | datetime }}
        </template>

        <template v-slot:lastSeen="{ item }">
          {{ item.lastSeen | datetime }}
        </template>

        <template v-slot:netRouteFirstSeen="{ item }">
          {{ (Math.round(item.firstSeen) / 1000) | datetime }}
        </template>

        <template v-slot:netRouteLastSeen="{ item }">
          {{ (Math.round(item.netRouteLastSeen) / 1000) | datetime }}
        </template>

        <template v-slot:duration="{ item }">
          <template v-if="item.duration">
            {{ item.duration | duration }}
          </template>
          <span v-else> -- </span>
        </template>

        <template v-slot:netroute-path="{ item }">
          <NetroutePicker
            :value="item.netroute_id"
            :disabled="true"
            :text-only="true"
          />
        </template>

        <template v-slot:incidentDetails="{ item }">
          <a class="text-primary" @click="$emit('incident-detail', item)">
            {{ item.displayIncidentDetails }}
            <MIcon v-if="item.incidentDetails" name="external-link"></MIcon>
          </a>
        </template>

        <template v-slot:correlatedKeys="{ item }">
          <span :title="item.correlatedKeys"> {{ item.correlatedKeys }}</span>
        </template>

        <!-- <template v-slot:counts="{ item }">
            <template v-if="Object.keys(item.counts || {}).length">
              <span
                v-for="(severity, index) in Object.keys(item.counts || {})"
                :key="severity"
                :class="{ text: true, [severity.toLowerCase()]: true }"
                :title="severity"
              >
                <b>
                  {{ (item.counts || {})[severity] | numberFormat }}
                </b>
                <span
                  v-if="index < Object.keys(item.counts || {}).length - 1"
                  class="mx-1 text-neutral-light"
                >
                  |
                </span>
              </span>
            </template>
            <span v-else />
          </template> -->

        <template v-slot:acknowledged="{ item }">
          <Actions
            :alert="item"
            :timeline="timeline"
            @change="handleUpdateAlert"
          >
            <template v-slot="{ performAction }">
              <MIcon
                :name="
                  item.acknowledged
                    ? 'solid-thumbs-up'
                    : 'solid-sleeve-thumbs-up'
                "
                :class="{ 'text-secondary-green': item.acknowledged }"
                size="sm"
                class="cursor-pointer"
                @click="
                  performAction(
                    !item.acknowledged ? 'acknowledge' : 'unacknowledge'
                  )
                "
              />
            </template>
          </Actions>
        </template>

        <template v-slot:actions="{ item }">
          <Actions
            :alert="item"
            use-dropdown
            :timeline="timeline"
            :excluded-actions="[
              'acknowledge',
              'unacknowledge',
              'last-triggered-notifications',
              'last-triggered-actions',
            ]"
            :disable-declare-incident-drawer="disableDeclareIncidentDrawer"
            @declare-incident="$emit('declare-incident', $event)"
            @change="handleUpdateAlert"
          />
        </template>
      </MGrid>
    </FlotoContentLoader>
  </CounterProvider>

  <FlotoModuleNoData v-else :module="resolvedModuleType" />
</template>

<script>
import FindIndex from 'lodash/findIndex'
import { generateId } from '@utils/id'
import Bus from '@utils/emitter'
import Config from '../../config'
import Constants from '@constants'
import Severity from '@components/severity.vue'
import SelectedItemPills from '@components/dropdown-trigger/selected-item-pills.vue'
import CounterProvider from '@components/data-provider/counter-provider.vue'
import { WidgetTypeConstants } from '@components/widgets/constants'
import { alertWorker, objectDBWorker } from '@/src/workers'
import Actions from './actions.vue'
// import ExpandedRow from './expanded-row.vue'
import {
  getMetricAlertStreamGrid,
  getLogFlowAlertStreamGrid,
} from '../../helpers/alert-helper'
import { convertTimeLineForServer } from '@/src/components/widgets/helper'
import { UserPreferenceComputed } from '@state/modules/user-preference'
import exportData from '@/src/modules/settings/monitoring/helpers/export-pdf-csv'
import { downloadFile } from '@/src/utils/download'
import { UserPreferenceMethods } from '@/src/state/modules/user-preference'
import { getSourceApi } from '@components/widgets/widgets-api'
import { getAllLogInventoryApi } from '@modules/settings/log-settings/log-settings-api'
import { authComputed } from '@state/modules/auth'
import NetroutePicker from '@/src/components/data-picker/netroute-picker.vue'
import { fetchNetRoutesApi } from '../../../netroute/netroute-api'

const SEVERITY_TO_CONSIDER = [
  Constants.DOWN,
  Constants.UNREACHABLE,
  Constants.CRITICAL,
  Constants.MAJOR,
  Constants.WARNING,
  Constants.CLEAR,
]

export default {
  name: 'Grid',
  components: {
    Severity,
    // ExpandedRow,
    Actions,
    SelectedItemPills,
    CounterProvider,
    NetroutePicker,
  },
  inject: { SocketContext: { default: {} } },
  props: {
    timeline: {
      type: Object,
      default: undefined,
    },
    category: {
      type: String,
      default: 'Network',
    },
    view: {
      type: String,
      default: undefined,
    },
    groupType: {
      type: String,
      default: undefined,
    },
    filters: {
      type: [Array, Object],
      default: undefined,
    },
    searchTerm: {
      type: String,
      default: undefined,
    },
    columns: {
      type: Array,
      default() {
        return []
      },
    },
    disableDeclareIncidentDrawer: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    this.eventInterval = 60000
    return {
      loading: true,
      data: [],
      guid: generateId(),
      options: {
        nextRequestGroup: 'policy.flap',
      },
      fragmentedData: [],
      queryProgress: null,
      currentBatch: 1,
      responseGroup: null,
      shouldShowModule: true,
    }
  },
  computed: {
    ...authComputed,
    ...UserPreferenceComputed,
    counterSearchParams() {
      return {
        'visualization.group.type': this.groupType,
        'visualization.category': WidgetTypeConstants.GRID,
      }
    },
    considerIncrementalResult() {
      return this.groupType === 'metric' && this.view === 'flap'
    },
    resolvedModuleType() {
      const groupTypeMap = {
        netroute: ['netroute-source-to-destination', 'netroute-hop-to-hop'],
      }
      const matchedKey = Object.keys(groupTypeMap).find((key) =>
        groupTypeMap[key].includes(this.groupType)
      )
      return `alert-${matchedKey || this.groupType}`
    },
  },
  watch: {
    timeline: {
      handler(newValue, oldValue) {
        if (newValue !== oldValue) {
          this.startStreaming()
        }
      },
    },
    category: {
      handler(newValue, oldValue) {
        if (newValue !== oldValue) {
          this.startStreaming()
        }
      },
    },
  },
  created() {
    Bus.$on('socket:connected', this.startStreaming)
    if (this.SocketContext.connected) {
      this.startStreaming()
      this.getNoDataPrefrenceAndUpdate(this.groupType)

      if (this.groupType !== 'metric') {
        this.startInterval()
      }
    }
  },
  beforeDestroy() {
    Bus.$off('socket:connected', this.startStreaming)
    this.stopHeartbeat()
    this.stopeInterval()
  },
  methods: {
    ...UserPreferenceMethods,
    async handleUpdateAlert(alert) {
      const index = FindIndex(this.data, { id: alert.id })
      if (index === -1) {
        return
      }
      this.data = Object.freeze([
        ...this.data.slice(0, index),
        { ...alert },
        ...this.data.slice(index + 1),
      ])
      const counts = await alertWorker.calculateTotal(
        this.data || [],
        SEVERITY_TO_CONSIDER
      )
      this.$emit('counts', counts)
    },

    startStreaming() {
      // if (this.$refs.incidentDetailDrawerRef) {
      //   this.$refs.incidentDetailDrawerRef.hide()
      // }
      this.abortQuery()
      this.loading = true
      this.queryProgress = null
      this.data = []
      this.fragmentedData = []
      this.currentBatch = 1
      this.startRequestAlertDataIncremental()
    },

    startRequestAlertDataIncremental() {
      const fn =
        this.groupType === 'metric' || this.groupType.indexOf('netroute') >= 0
          ? getMetricAlertStreamGrid
          : getLogFlowAlertStreamGrid

      this.options.queryId = this.__parentQueryId
      fn(
        this.timeline,
        this.groupType,
        this.view,
        this.data,
        this.options
      ).then(async (data) => {
        this.data = []
        if (this.considerIncrementalResult) {
          data.meta.requestGroup === 'policy.flap' &&
          data.meta.progress &&
          data.meta.progress >= 100
            ? (this.options.nextRequestGroup = 'policy.stream')
            : data.meta.requestGroup === 'policy.stream'
            ? (this.options.nextRequestGroup = 'policy.acknowledgement')
            : (this.options.nextRequestGroup = 'policy.flap')
        }
        this.handelIncrementResult(data)
      })
    },

    async handelIncrementResult(data) {
      if (this.considerIncrementalResult) {
        this.__parentQueryId = data.meta.queryId
        this.responseGroup = data?.meta?.requestGroup
        this.queryProgress = data.meta.progress
        this.fragmentedData = this.queryProgress
          ? [...this.fragmentedData, ...data.data]
          : data.data
      }
      if (this.considerIncrementalResult) {
        data = this.fragmentedData
      }
      const category = this.category
      if (category === Config.CORRELATED_POLICIES) {
        data = data
          .filter((d) => d.isCorrelatedAlert)
          .map((fd) => {
            return {
              ...fd,
              category: Config.CORRELATED_POLICIES,
            }
          })
      } else if (category && this.groupType === 'metric') {
        data = data.filter((item) => item.category === category)
      }
      this.data = Object.freeze(data)
      this.loading = false
      const counts = await alertWorker.calculateTotal(
        this.data || [],
        SEVERITY_TO_CONSIDER
      )
      this.$emit('total', this.data.length)
      this.$emit('counts', counts)

      if (this.currentBatch === 1 && this.considerIncrementalResult) {
        setTimeout(() => {
          this.sendActiveSessionEvent()
          this.scheduleUpdate()
        }, 100)
      }

      if (
        this.responseGroup !== 'policy.acknowledgement' &&
        this.considerIncrementalResult
      ) {
        setTimeout(() => {
          this.$nextTick(() => {
            this.currentBatch++
            this.startRequestAlertDataIncremental()
          })
        }, 100)
      }
    },

    sendActiveSessionEvent() {
      if (!this.this.__parentQueryId) {
        return
      }
      Bus.$emit('server:event', {
        'event.type': this.$constants.UI_WIDGET_ACTIVE_SESSION,
        'event.context': {
          'query.id': this.__parentQueryId,
          [this.$constants.UI_EVENT_UUID]: this.guid,
          'event.context': {
            ...(this.serverParams || {}),
            ...(this.timeline
              ? {
                  'visualization.timeline': convertTimeLineForServer(
                    this.timeline
                  ),
                }
              : {}),
          },
        },
      })
    },

    scheduleUpdate() {
      this.stopHeartbeat()
      this.__streamingTimer = setInterval(this.sendActiveSessionEvent, 10000)
    },

    abortQuery() {
      if (this.__parentQueryId) {
        this.queryProgress = null
        Bus.$emit('server:event', {
          'event.type': this.$constants.UI_WIDGET_ABORT_EVENT,
          'event.context': {
            'query.id': this.__parentQueryId,
          },
        })
      }
    },
    stopHeartbeat() {
      if (this.__streamingTimer) {
        clearInterval(this.__streamingTimer)
        this.__streamingTimer = null
      }
    },
    navigateToDetail(item, tab) {
      const alert = item
      const param = {
        ...alert,
        view: this.view,
      }
      this.$router.push(
        this.$currentModule.getRoute('detail', {
          params: {
            ...this.$route.params,
            tab: tab || 'overview',
            uuid: encodeURIComponent(btoa(JSON.stringify(param))),
          },
          query: {
            t: encodeURIComponent(btoa(JSON.stringify(this.timeline))),
          },
        })
      )
    },
    async exportCsv() {
      this.$successNotification({
        message: 'Success',
        description: `The file will be downloaded once ready`,
      })
      const contextData = this.$refs.baseGridRef.getContextData()
      const data = await this.$refs.baseGridRef.getFilteredData()
      exportData(
        this.columns.filter((c) => !c.hidden && c.key !== 'actions'),
        data.data,
        'csv',
        contextData,
        {
          dateTimeFormat: this.dateFormat,
          timezone: this.timezone,
        }
      ).then((blob) => {
        downloadFile(blob, undefined, `alerts.csv`)
      })
    },
    async getNoDataPrefrenceAndUpdate(groupType) {
      let { hasModulePrefrence } =
        await this.getLendingPagePrefrenceByGroupType(groupType)

      if (!hasModulePrefrence) {
        await this.updateNoDataPrefrence(groupType, hasModulePrefrence)
      }

      this.shouldShowModule = await this.getStaticLendingPagePreferenceByModule(
        {
          module:
            groupType === 'metric' ? 'object' : (groupType || '').toLowerCase(),
        }
      )
      this.$emit('update-should-show-module', this.shouldShowModule)
    },

    async getLendingPagePrefrenceByGroupType(groupType) {
      const hasModulePrefrence =
        await this.getStaticLendingPagePreferenceByModule({
          module:
            groupType === 'metric' ? 'object' : (groupType || '').toLowerCase(),
        })

      return {
        hasModulePrefrence,
      }
    },

    async updateNoDataPrefrence(groupType, hasModulePrefrence) {
      const update = async (c, prefrence) => {
        const type = c === 'metric' ? 'object' : (c || '').toLowerCase()

        if (hasModulePrefrence !== prefrence) {
          await this.updateStaticLendingPagePreferences({
            lendingPagePreferences: {
              [type]: prefrence,
            },
          })
        }
      }

      if (groupType === 'metric') {
        const objects = await objectDBWorker.getObjects({})

        await update(groupType, !!objects.length)
      } else if (groupType === 'log') {
        if (this.hasPermission(this.$constants.LOG_SETTINGS_READ_PERMISSION)) {
          let logInventories = await getAllLogInventoryApi()

          logInventories = logInventories.filter(
            (logInventory) => !logInventory.isHealthAgentLogInventory
          )

          await update(groupType, !!logInventories.length)
        }
      } else if (groupType === 'flow') {
        if (this.hasPermission(this.$constants.FLOW_SETTINGS_READ_PERMISSION)) {
          const source = await getSourceApi('flow')
          const flowSourceOptions = Object.freeze(
            Object.keys(source.result || {}).map((i) => ({
              key: i,
              text: source[i],
            }))
          )
          await update(groupType, !!flowSourceOptions.length)
        }
      } else if (groupType === 'trap') {
        const source = await getSourceApi('trap')
        const trapSourceOptions = Object.freeze(
          Object.keys(source.result || {}).map((i) => ({
            key: i,
            text: source[i],
          }))
        )
        await update(groupType, !!trapSourceOptions.length)
      } else if (groupType.toLowerCase().indexOf('netroute') >= 0) {
        const source = await fetchNetRoutesApi()
        await update(groupType, !!source.length)
      }
    },
    startInterval() {
      if (this.eventInterval) {
        this.__eventInterval = setInterval(
          this.startStreaming,
          this.eventInterval
        )
      }
    },
    stopeInterval() {
      if (this.__eventInterval) {
        clearInterval(this.__eventInterval)
      }
    },
  },
}
</script>
