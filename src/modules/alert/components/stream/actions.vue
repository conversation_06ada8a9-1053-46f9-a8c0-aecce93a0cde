<template>
  <span>
    <slot :performAction="performAction">
      <FlotoGridActions
        v-if="useDropdown"
        :actions="actions"
        :resource="alert"
        @post-comment="performAction('post-comment')"
        @claim="performAction('claim')"
        @unclaim="performAction('unclaim')"
        @acknowledge="performAction('acknowledge')"
        @unacknowledge="performAction('unacknowledge')"
        @clear="performAction('clear')"
        @suppress="performAction('suppress')"
        @history="performAction('history')"
        @note="performAction('note')"
        @incident="performAction('incident')"
        @share="performAction('share')"
      />
      <div v-else class="flex flex-1 items-center justify-center">
        <MButton
          v-for="action in actions"
          :key="action.key"
          class="squared-button mr-2"
          variant="neutral-lightest"
          :title="action.name"
          @click="performAction(action.key)"
        >
          <MIcon
            :name="action.icon"
            class="mr-1"
            :class="{
              'text-secondary-green':
                action.icon === 'solid-thumbs-up' && alert.acknowledged,
            }"
          />
        </MButton>
      </div>
    </slot>
    <SupressModal
      v-if="showSuppressModal"
      @hide="showSuppressModal = false"
      @submit="handleSupressAlert"
    />
    <NoteModal
      v-if="showNoteModal"
      :default-value="alert.note"
      @hide="showNoteModal = false"
      @submit="handleNoteAlert"
    />
    <ClearModal
      v-if="showClearModal"
      :default-value="alert.note"
      :policy-name="alert.policy"
      @hide="showClearModal = false"
      @submit="handleClearAlert"
    />

    <ShareModal
      v-if="showShareModal"
      :policy-name="alert.policy"
      share-type="Alert"
      @hide="showShareModal = false"
      @submit="handleShareAlert"
    />
    <DeclareIncidentDrawer
      v-if="showDeclareIncidentDrawerFor"
      :alert="showDeclareIncidentDrawerFor"
      @cancel="showDeclareIncidentDrawerFor = null"
    />
    <RunbookPluginProvider v-if="showLastTriggeredModal">
      <IntegrationProfileProvier>
        <LastTriggeredModal
          :alert="alert"
          :type="lastTriggeredType"
          :timeline="timeline"
          @close="handleCloseLastTriggeredModal"
        />
      </IntegrationProfileProvier>
    </RunbookPluginProvider>
  </span>
</template>

<script>
import Bus from '@utils/emitter'
import Moment from 'moment'

import { authComputed } from '@state/modules/auth'
import SupressModal from './suppress-modal.vue'
import NoteModal from './note-modal.vue'
import ClearModal from './clear-modal.vue'
import LastTriggeredModal from './last-triggerd-modal.vue'
import RunbookPluginProvider from '@/src/components/data-provider/runbook-plugin-provider.vue'
import IntegrationProfileProvier from '@components/data-provider/integration-profile-provider.vue'
import DeclareIncidentDrawer from './declare-incident-drawer.vue'
import ShareModal from './share-model.vue'

const ALL_ACTIONS = [
  { key: 'clear', name: 'Clear Alert', icon: 'monitor-enable' },
  // { key: 'post-comment', name: 'Post Comment', icon: 'comment-dots' },
  {
    key: 'acknowledge',
    name: 'Acknowledge Alert',
    icon: 'solid-sleeve-thumbs-up',
  },
  {
    key: 'note',
    name: 'Add Note',
    icon: 'comment-dots',
  },
  {
    key: 'unacknowledge',
    name: 'Unacknowledge Alert',
    icon: 'solid-thumbs-up',
  },
  { key: 'history', name: 'History', icon: 'history' },
  {
    key: 'suppress',
    name: 'Suppress Alert',
    icon: 'monitor-disable',
  },
  {
    key: 'incident',
    name: 'Declare Incident',
    icon: 'declare-incident',
  },
  {
    key: 'share',
    name: 'Share',
    icon: 'share-alt',
  },
  {
    key: 'last-triggered-notifications',
    name: 'Last Triggered Notifications',
    icon: 'alert',
  },
  {
    key: 'last-triggered-actions',
    name: 'Last Triggered Actions',
    icon: 'arrow-up-right-from-circles',
  },
]

export default {
  name: 'Actions',
  components: {
    SupressModal,
    LastTriggeredModal,
    NoteModal,
    RunbookPluginProvider,
    IntegrationProfileProvier,
    ClearModal,
    DeclareIncidentDrawer,
    ShareModal,
  },
  inject: {
    HandlerProviderContext: { default: { options: [] } },
  },
  props: {
    useDropdown: {
      type: Boolean,
      default: false,
    },
    excludedActions: {
      type: Array,
      default() {
        return []
      },
    },
    timeline: {
      type: Object,
      default: undefined,
    },
    alert: {
      type: Object,
      required: true,
    },
    disableDeclareIncidentDrawer: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      showNoteModal: false,
      showSuppressModal: false,
      showLastTriggeredModal: null,
      lastTriggeredType: null,
      showClearModal: false,
      showDeclareIncidentDrawerFor: null,
      showShareModal: false,
    }
  },
  computed: {
    ...authComputed,
    isNetPathPolicy() {
      return (this.alert.groupCategory || []).indexOf('netroute') >= 0
    },
    actions() {
      const alert = this.alert
      let excludedKeys = [...this.excludedActions]
      if (alert.acknowledged) {
        excludedKeys.push('acknowledge')
      } else {
        excludedKeys.push('unacknowledge')
      }
      if (
        (alert.severity || '').toLowerCase() === 'clear' ||
        (!['metric threshold', 'availability'].includes(
          (alert.policyType || '').toLowerCase()
        ) &&
          alert.groupCategory !== 'netroute.metric')
      ) {
        excludedKeys.push('clear')
      }
      if (!this.hasPermission(this.$constants.ALERT_UPDATE_PERMISSION)) {
        excludedKeys.push('clear', 'suppress', 'note', 'incident')
      }
      if (
        !this.hasPermission(
          this.$constants.PLUGIN_LIBRARY_SETTINGS_READ_PERMISSION
        )
      ) {
        excludedKeys.push(
          'last-triggered-actions',
          'last-triggered-notifications'
        )
      }
      if (
        !(
          alert.groupCategory === 'metric' ||
          alert.groupCategory === 'netroute.metric'
        )
      ) {
        excludedKeys.push('note', 'acknowledge', 'unacknowledge')
      }
      if (alert.incidentDetails) {
        excludedKeys.push('incident')
      }
      if (alert.groupCategory !== 'metric') {
        excludedKeys.push('incident')
      }
      if (alert.groupCategory === 'netroute.event') {
        excludedKeys.push('share', 'last-triggered-actions')
      }
      if (alert.groupCategory === 'netroute.metric') {
        excludedKeys.push('share', 'last-triggered-actions')
      }

      return ALL_ACTIONS.filter(({ key }) => !excludedKeys.includes(key))
    },
    isAvailabilityPolicy() {
      return ['availability'].includes(
        (this.alert.policyType || '').toLowerCase()
      )
    },
  },
  methods: {
    handleClearAlert(note) {
      this.handleNoteAlert(note)
      this.performAction('clear', this.alert, true)
      this.showClearModal = false
    },
    handleNoteAlert(note) {
      const alert = this.alert
      Bus.$emit('server:event', {
        'event.type': this.$currentModule.getConfig().ALERT_STREAM_NOTE,
        'event.context': {
          'policy.id': alert.policyId,
          'alert.type': alert.groupCategory === 'metric' ? 0 : 1,

          'policy.note': note,
          ...(alert.instance ? { instance: alert.instance } : {}),
          ...(this.isNetPathPolicy
            ? {
                'netroute.id': alert.netroute_id,
                'policy.evaluation.type': alert.routeEvaluationType,
              }
            : {
                [alert.groupCategory === 'metric'
                  ? 'object.id'
                  : 'event.source']: alert.monitor,
              }),
        },
      })

      this.$emit('change', {
        ...alert,
        note,
        actionType: 'note',
      })
      this.showNoteModal = false
    },
    handleSupressAlert(time) {
      const alert = this.alert
      Bus.$emit('server:event', {
        'event.type':
          this.$currentModule.getConfig().ALERT_STREAM_SUPRESS_ACTION,
        'event.context': {
          'action.type': 'suppress',
          'policy.id': alert.policyId,
          'alert.type': alert.groupCategory === 'metric' ? 0 : 1,

          'policy.suppression.time': time,
          ...(alert.instance ? { instance: alert.instance } : {}),
          ...(this.isNetPathPolicy
            ? {
                'netroute.id': alert.netroute_id,
                'policy.evaluation.type': alert.routeEvaluationType,
              }
            : {
                [alert.groupCategory === 'metric'
                  ? 'object.id'
                  : 'event.source']: alert.monitor,
              }),
        },
      })

      this.showSuppressModal = false
    },
    performAction(action, alertItem, shouldPerformAction) {
      // const isAvailabilityPolicy = this.isAvailabilityPolicy
      const alert = alertItem || this.alert
      const user = this.user
      switch (action) {
        case 'post-comment':
          this.$router.push(
            this.$currentModule.getRoute('detail', {
              params: {
                ...this.$route.params,
                tab: 'collaboration',
                uuid: encodeURIComponent(
                  btoa(
                    JSON.stringify({
                      ...alert,
                      view: 'live',
                    })
                  )
                ),
              },
              query: {
                t: encodeURIComponent(btoa(JSON.stringify(this.timeline))),
              },
            })
          )
          return
        case 'history':
          this.$router.push(
            this.$currentModule.getRoute('detail', {
              params: {
                ...this.$route.params,
                tab: 'overview',
                uuid: encodeURIComponent(
                  btoa(
                    JSON.stringify({
                      ...alert,
                      view: 'live',
                    })
                  )
                ),
              },
              query: {
                t: encodeURIComponent(btoa(JSON.stringify(this.timeline))),
              },
            })
          )
          break
        case 'acknowledge':
        case 'unacknowledge':
          Bus.$emit('server:event', {
            'event.type':
              this.$currentModule.getConfig().ALERT_STREAM_ACKNOWLEDGE_ACTION,
            'event.context': {
              acknowledge: action === 'acknowledge' ? 'yes' : 'no',
              'action.type': action,
              'policy.id': alert.policyId,
              'alert.type': alert.groupCategory === 'metric' ? 0 : 1,

              ...(alert.instance ? { instance: alert.instance } : {}),
              'user.id': user.id,
              'user.name': user.userName,
              ...(this.isNetPathPolicy
                ? {
                    'netroute.id': alert.netroute_id,
                    'policy.evaluation.type': alert.routeEvaluationType,
                  }
                : {
                    [alert.groupCategory === 'metric'
                      ? 'object.id'
                      : 'event.source']: alert.monitor,
                  }),
            },
          })
          this.$emit('change', {
            ...alert,
            acknowledged: action === 'acknowledge',
            ...(action === 'acknowledge'
              ? { acknowledgedBy: user.id, acknowledgedByUser: user.userName }
              : {}),
            actionType: action,
          })
          break
        case 'clear':
          if (!shouldPerformAction) {
            this.showClearModal = true
            break
          }
          Bus.$emit('server:event', {
            'event.type':
              this.$currentModule.getConfig().ALERT_STREAM_CLEAR_ACTION,
            'event.context': {
              'action.type': action,
              'policy.id': alert.policyId,
              metric: alert.counterRawName,
              'user.id': user.id,
              'user.name': user.userName,
              'alert.type': alert.groupCategory === 'metric' ? 0 : 1,

              ...(alert.instance ? { instance: alert.instance } : {}),
              ...(this.isNetPathPolicy
                ? {
                    'netroute.id': alert.netroute_id,
                    'policy.evaluation.type': alert.routeEvaluationType,
                  }
                : {
                    [alert.groupCategory === 'metric'
                      ? 'object.id'
                      : 'event.source']: alert.monitor,
                  }),
            },
          })
          this.$successNotification({
            message: 'Successful',
            description: `The ${alert.policy} alert has been successfully cleared`,
          })
          break
        case 'suppress':
          this.showSuppressModal = true
          break
        case 'note':
          this.showNoteModal = true
          break
        case 'incident':
          if (this.disableDeclareIncidentDrawer) {
            this.$emit('declare-incident', this.alert)
          } else {
            this.showDeclareIncidentDrawerFor = alert
          }
          break
        case 'share':
          this.showShareModal = true

          break

        case 'last-triggered-notifications':
        case 'last-triggered-actions':
          this.handleShowLastTriggeredModal(
            action === 'last-triggered-notifications'
              ? 'notifications'
              : 'actions'
          )
          break

        default:
          break
      }
    },
    handleShowLastTriggeredModal(type) {
      this.lastTriggeredType = type
      this.showLastTriggeredModal = true
    },
    handleCloseLastTriggeredModal() {
      this.showLastTriggeredModal = null
      this.lastTriggeredType = null
    },
    handleShareAlert(formData, recipients) {
      const alert = this.alert

      Bus.$emit('server:event', {
        'event.type': this.$constants.UI_ACTION_WIDGET_SHARE,
        'event.context': {
          recipients,
          'user.name': this.user.userName,
          type: 'Alert',
          ...(alert || {}),

          'alert.message': (alert.message || '')
            .replace(/<[^>]+>/g, '')
            .replace(/\s+/g, ' ')
            .trim(),
          message: formData.message,
          'policy.type': alert.policyType,
          'policy.id': alert.policyId,
          'last.seen': alert.lastSeen,
          Timestamp: Moment().unix() * 1000,
        },
      })

      this.$successNotification({
        message: 'Successful',
        description: `Shared successfully`,
      })

      this.showShareModal = false
    },
  },
}
</script>
