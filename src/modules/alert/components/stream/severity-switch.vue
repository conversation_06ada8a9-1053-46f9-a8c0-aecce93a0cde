<template>
  <MRadioGroup
    v-model="innertValue"
    :options="severityOptions"
    as-button
    class="radio-toggle-shadow alert-severity-buttons"
  >
    <template v-slot:option="{ option }">
      <div
        v-if="option.value !== 'acknowledge'"
        class="inline-flex"
        style="position: relative"
      >
        <Severity
          :severity="option.value"
          class="mr-1"
          :disable-tooltip="true"
        />
      </div>
      <MIcon
        v-if="option.value === 'acknowledge'"
        name="solid-sleeve-thumbs-up"
        class="mr-2 text-secondary-green"
      />
      <span class="font-500 justify-end mr-1" style="position: relative">
        {{ option.count | numberFormat }}
      </span>
      <span style="position: relative">
        {{ option.text }}
      </span>
    </template>
  </MRadioGroup>
</template>

<script>
import Severity from '@components/severity.vue'

export default {
  name: 'SeveritySwitch',
  components: {
    Severity,
  },
  model: { event: 'change' },
  props: {
    severityOptions: { type: Array, required: true },
    value: { type: [String, Array], default: undefined },
  },
  computed: {
    innertValue: {
      get() {
        return this.value
      },
      set(value) {
        if (value === this.value) {
          this.$emit('change', undefined)
        } else {
          this.$emit('change', value)
        }
      },
    },
  },
}
</script>
