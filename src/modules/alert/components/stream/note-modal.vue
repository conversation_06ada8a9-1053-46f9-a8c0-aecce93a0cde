<template>
  <FlotoConfirmModal
    open
    hide-icon
    variant="primary-alt"
    overlay-class-name="no-padding-confrim-modal"
    @confirm="handleConfirmAction"
  >
    <template v-slot:message>
      <FlotoForm
        :show-notification="false"
        class="-m-3"
        @submit="handleConfirmAction"
      >
        <div class="flex items-center">
          <div class="flex-1">
            <slot name="title">
              <h5 class="mb-0 text-primary"
                >{{ defaultValue ? 'Update' : 'Add' }} Note</h5
              >
            </slot>
          </div>
          <MButton
            id="close-used-count"
            variant="transparent"
            :shadow="false"
            shape="circle"
            @click="hideNoteModal"
          >
            <MIcon name="times" class="text-neutral-light" />
          </MButton>
        </div>

        <MDivider />

        <FlotoFormItem class="mt-1 full-border-text-area" rules="required">
          <MInput
            v-model="note"
            type="textarea"
            placeholder="Add Note"
            as-input
            :rows="4"
          />
        </FlotoFormItem>
        <template v-slot:submit="{ submit }">
          <MRow>
            <MCol class="text-right">
              <MButton
                id="ip-cancle"
                variant="default"
                class="mr-2"
                @click="hideNoteModal"
              >
                Cancel
              </MButton>
              <MButton id="send-test-ip-id" name="test-ip" @click="submit">
                {{ defaultValue ? 'Update' : 'Add' }} Note
              </MButton>
            </MCol>
          </MRow>
        </template>
      </FlotoForm>
    </template>
    <template v-slot:action-container>
      <span />
    </template>
  </FlotoConfirmModal>
</template>

<script>
export default {
  name: 'NoteModal',
  components: {},
  props: {
    defaultValue: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      note: this.defaultValue,
    }
  },
  methods: {
    hideNoteModal() {
      this.$emit('hide', true)
    },
    handleConfirmAction() {
      this.$emit('submit', this.note)
    },
  },
}
</script>
