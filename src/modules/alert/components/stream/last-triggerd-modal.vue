<template>
  <IncrementalResultProvider
    :serverside-widget-defination="widget"
    :timeline="timeline"
    @patchRecived="patchRecived"
  >
    <MModal
      open
      :width="1020"
      overlay-class-name="scrollable-modal hide-footer"
      @cancel="$emit('close')"
    >
      <template v-slot:trigger>
        <span />
      </template>
      <template v-slot:title>
        <div class="flex items-center">
          <div class="flex-1">
            <slot name="title">
              <h4 class="mb-0 text-primary">
                Last Triggered {{ capitalizedType }}
              </h4>
            </slot>
          </div>
          <MButton
            variant="transparent"
            :shadow="false"
            shape="circle"
            @click="$emit('close')"
          >
            <MIcon name="times" class="text-neutral-light" />
          </MButton>
        </div>
      </template>

      <FlotoContentLoader :loading="loading">
        <div class="flex flex-1 flex-col min-h-0">
          <MGrid
            :data="data"
            :columns="columns"
            :expandable="type === 'actions'"
            :detail="type === 'actions' ? 'detailRow' : undefined"
          >
            <template v-if="type === 'actions'" v-slot:detailRow="{ item }">
              <div
                style="white-space: pre-line"
                v-html="getResult(item.value)"
              />
            </template>
            <template v-slot:type="{ item }">
              {{ typeMap[item.type] }}
            </template>
            <template v-slot:actionName="{ item, toggleExpand }">
              <MButton
                v-if="type === 'actions'"
                variant="transparent"
                shape="circle"
                :shadow="false"
                @click="toggleExpand"
              >
                <MIcon
                  :name="item.expanded ? 'chevron-down' : 'chevron-right'"
                />
              </MButton>
              {{ actionName(item.actionId, item.type) }}
            </template>
            <template v-slot:status="{ item }">
              <MStatusTag :status="item.status" />
            </template>
            <template v-slot:datetime="{ item }">
              {{ item.datetime | datetime }}
            </template>
            <template v-slot:output="{ item }">
              <pre class="text-ellipsis" v-text="getResult(item.value)" />
            </template>
            <template v-slot:recipients="{ item }">
              <SelectedItemPills :value="item.value" />
            </template>
          </MGrid>
        </div>
      </FlotoContentLoader>
      <template v-slot:footer>
        <span />
      </template>
    </MModal>
  </IncrementalResultProvider>
</template>

<script>
import { generateId } from '@utils/id'
import Capitalize from 'lodash/capitalize'
import SelectedItemPills from '@components/dropdown-trigger/selected-item-pills.vue'
import IncrementalResultProvider from '@components/data-provider/incremental-result-provider.vue'

import {
  getLastTriggeredData,
  transformLastTriggeredData,
} from '../../helpers/alert-helper'

const COLUMNS = [
  // { key: 'action', width: '0px' },
  { key: 'type', name: 'Type', width: '160px' },
  { key: 'actionName', name: 'Action', width: '160px' },
  { key: 'status', name: 'Status', width: '120px' },
  { key: 'datetime', name: 'Timestamp', width: '250px' },
  { key: 'output', name: 'Output' },
  { key: 'recipients', name: 'Recipients' },
]

const NotificationTypeMap = {
  0: 'Notification',
  1: 'Notification',
}

const ActionTypeMap = {
  2: 'Runbook',
  3: 'Incident',
}

export default {
  name: 'LastTriggerdModal',
  components: {
    SelectedItemPills,
    IncrementalResultProvider,
  },
  inject: {
    runbookPluginContext: { default: { options: new Map() } },
    IntegrationProfileProviderContext: { default: { options: new Map() } },
  },
  props: {
    type: {
      type: String,
      default: 'notifications',
    },
    alert: {
      type: Object,
      required: true,
    },
    timeline: {
      type: Object,
      required: true,
    },
  },
  data() {
    const keyToExclude =
      this.type === 'notifications'
        ? ['output', 'actionName']
        : ['recipients', 'type']
    return {
      loading: true,
      guid: generateId(),
      columns: COLUMNS.filter((c) => keyToExclude.includes(c.key) === false),
      data: [],
    }
  },
  computed: {
    typeMap() {
      if (this.type === 'notifications') {
        return NotificationTypeMap
      }
      return ActionTypeMap
    },
    capitalizedType() {
      return Capitalize(this.type)
    },
    tab() {
      return this.$route.params.tab
    },
    widget() {
      return getLastTriggeredData(this.type, this.alert, this.timeline, true)
    },
  },
  created() {
    // this.getData()
  },
  methods: {
    // getData() {
    //   getLastTriggeredData(this.type, this.alert, this.timeline, this.tab).then(
    //     (data) => {
    //       this.data = Object.freeze(data)
    //       this.loading = false
    //     }
    //   )
    // },
    // getJson(json) {
    //   return json ? Trim(JSON.stringify(json, undefined, 4)) : ''
    // },
    getResult(json) {
      return json
    },
    actionName(id, type) {
      if (
        this.IntegrationProfileProviderContext.options.find(
          (item) => item.id === id
        ) &&
        type === 3
      ) {
        return this.IntegrationProfileProviderContext.options.find(
          (item) => item.id === id
        ).name
      }
      if (this.runbookPluginContext.options.has(id) && type === 2) {
        return this.runbookPluginContext.options.get(id).text
      }
      return ''
    },
    patchRecived(data) {
      this.loading = false
      this.data = [
        ...this.data,
        ...(data || []).map((row) => transformLastTriggeredData(row)),
      ]
    },
  },
}
</script>
