<template>
  <FlotoConfirmModal
    open
    hide-icon
    variant="primary-alt"
    @confirm="handleConfirmAction"
  >
    <template v-slot:message>
      <FlotoForm :show-notification="false" @submit="handleConfirmAction">
        <div class="flex items-center">
          <div class="flex-1">
            <slot name="title">
              <h5 class="mb-0 text-primary">Suppress Alert</h5>
            </slot>
          </div>
          <MButton
            id="close-used-count"
            variant="transparent"
            :shadow="false"
            shape="circle"
            @click="hideSuppressAlertModal"
          >
            <MIcon name="times" class="text-neutral-light" />
          </MButton>
        </div>

        <MDivider />

        <FlotoFormItem class="mt-3" rules="required">
          <FlotoDropdownPicker
            v-model="duration"
            placeholder="Select Time"
            as-input
            :allow-clear="true"
            :options="timeOptions"
          />
        </FlotoFormItem>
        <template v-slot:submit="{ submit }">
          <MRow>
            <MCol class="text-right">
              <MButton
                id="send-test-ip-id"
                name="test-ip"
                class="mr-2"
                @click="submit"
              >
                Suppress
              </MButton>
              <MButton
                id="ip-cancle"
                variant="default"
                @click="hideSuppressAlertModal"
              >
                Cancel
              </MButton>
            </MCol>
          </MRow>
        </template>
      </FlotoForm>
    </template>
    <template v-slot:action-container>
      <span />
    </template>
  </FlotoConfirmModal>
</template>

<script>
export default {
  name: 'SuppressModal',
  components: {},
  props: {},
  data() {
    this.timeOptions = [
      { key: 60 * 60, text: '1 hour' },
      { key: 3 * 60 * 60, text: '3 hours' },
      { key: 6 * 60 * 60, text: '6 hours' },
      { key: 9 * 60 * 60, text: '9 hours' },
      { key: 12 * 60 * 60, text: '12 hours' },
      { key: 24 * 60 * 60, text: '24 hours' },
      { key: 48 * 60 * 60, text: '48 hours' },
    ]
    return {
      duration: undefined,
    }
  },
  methods: {
    hideSuppressAlertModal() {
      this.$emit('hide', true)
    },
    handleConfirmAction() {
      this.$emit('submit', this.duration)
    },
  },
}
</script>
