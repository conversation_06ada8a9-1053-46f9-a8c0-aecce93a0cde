<template>
  <WidgetContainer
    :widget="isStatusChart ? statusWidget : metricWidget"
    :max-y="isStatusChart ? 100 : undefined"
    :availability-colors="isStatusChart"
    :alert-series="!useBands ? alertSeries : undefined"
    class="w-full"
    watch-widget
    is-preview
    :should-ignore-object-id-series="alert.groupCategory === 'metric'"
  />
</template>

<script>
import Moment from 'moment'

// import Bus from '@utils/emitter'
// import { convertTimeLineForServer } from '@components/widgets/helper'
// import { getAlertOverlayCount } from '../helpers/alert-helper'
import { UserPreferenceComputed } from '@state/modules/user-preference'

import { buildWidgetContext, makeCounter } from '@utils/socket-event-as-api'
import { WidgetTypeConstants } from '@components/widgets/constants'
import WidgetContainer from '@components/widgets/views/container.vue'

export default {
  name: 'AlertTrend<PERSON>hart',
  components: {
    WidgetContainer,
  },
  props: {
    alert: {
      type: Object,
      required: true,
    },
    timeline: {
      type: Object,
      default: undefined,
    },
    view: { type: String, default: 'live' },
    aggrigator: { type: String, default: 'avg' },
    useBands: {
      type: Boolean,
      default: false,
    },
    plotBands: {
      type: Array,
      default: undefined,
    },
    activeItem: {
      type: Object,
      default: undefined,
    },
    granularity: {
      type: Object,
      default: undefined,
    },
  },
  data() {
    this.rangeSeries = null
    return {
      anomalySeries: undefined,
      additionalSeries: undefined,
      alertSeries: [],
    }
  },
  computed: {
    ...UserPreferenceComputed,
    isForecastPolicy() {
      return (
        this.alert.policyType &&
        this.alert.policyType.toLowerCase().includes('forecast')
      )
    },
    metricWidget() {
      const alert = this.alert
      const timeline = this.timeline
      return {
        ...buildWidgetContext({
          groupType: alert.groupCategory,
          timeline,
          category: this.isForecastPolicy
            ? WidgetTypeConstants.FORECAST
            : WidgetTypeConstants.CHART,
          widgetType: this.isForecastPolicy
            ? undefined
            : WidgetTypeConstants.LINE,
          counters: [
            makeCounter(
              alert.counterRawName,
              alert.groupCategory === 'metric' && !this.isForecastPolicy
                ? '__NONE__'
                : this.aggrigator,
              alert.groupCategory === 'metric'
                ? 'Monitor'
                : alert.groupCategory === 'netroute.metric'
                ? 'NetRoute'
                : undefined,
              alert.groupCategory === 'metric'
                ? alert.monitor
                : alert.groupCategory === 'netroute.metric'
                ? alert.netroute_id
                : undefined
            ),
          ],

          ...(this.isForecastPolicy
            ? {
                resultBy: [
                  'monitor',
                  ...(alert.instance &&
                  alert.counterRawName &&
                  alert.counterRawName?.split('~')?.[0]
                    ? [alert.counterRawName.split('~')[0]]
                    : []),
                ],
              }
            : {}),

          ...(alert.instance
            ? {
                preFilters: {
                  condition: 'and',
                  inclusion: 'include',
                  conditions: [
                    {
                      operand: alert.counterRawName.split('~')[0],
                      operator: '=',
                      value: alert.instance,
                    },
                  ],
                },
              }
            : {}),

          // @TODO  future improvement for the alert tarnd in log flow drilldown
          // ...(['log', 'flow', 'trap'].includes(alert.groupCategory)
          //   ? {
          //       preFilters: {
          //         condition: 'and',
          //         inclusion: 'include',
          //         conditions: [
          //           {
          //             operand: 'policy.id',
          //             operator: '=',
          //             value: alert.policyId,
          //           },
          //         ],
          //       },
          //     }
          //   : {}),

          ...(this.isForecastPolicy && this.granularity
            ? {
                granularity: this.granularity,
              }
            : {}),
        })
          .appendToGroup(alert.groupCategory, {
            ...(alert.groupCategory === 'metric' && !this.isForecastPolicy
              ? {
                  additionalUntouchedRequestChunk: {
                    'join.type': 'custom',
                    'join.result': 'grid.to.chart',
                  },
                }
              : {}),
            ...(alert.groupCategory === 'metric' &&
            this.isForecastPolicy &&
            alert.instance &&
            alert.counterRawName.indexOf('~') >= 0
              ? {
                  instance: alert.instance,
                }
              : {}),
          })
          .setWidgetProperties(
            this.useBands && this.activeItem
              ? {
                  styleSetting: {
                    chartOptions: {
                      yAxis: {
                        plotBands: this.yAxisPlotBands,
                        plotLines: this.yAxisPlotLines,
                      },
                      xAxis: {
                        plotBands: this.xAxisPlotBands,
                        plotLines: this.xAxisPlotLines,
                      },
                    },
                  },
                }
              : {}
          )
          .getContext(),
        timeRangeInclusive: true,

        ...(alert.groupCategory === 'metric' && !this.isForecastPolicy
          ? {
              joinQueryContext: {
                'join.type': 'custom',
                'join.result': 'grid.to.chart',
              },
            }
          : {}),

        ...(this.isForecastPolicy
          ? {
              joinQueryContext: {
                'visualization.result.type': 2,
                'passover.step1.query': 'yes',
              },
            }
          : {}),
      }
    },
    statusWidget() {
      const alert = this.alert
      const timeline = this.timeline
      let counters = [
        'downtime',
        'suspendtime',
        'maintenancetime',
        'unreachabletime',
        'disabletime',
        'uptime',
      ]
      const instanceName = alert.counterRawName.split('~')[0]
      if (alert.instance) {
        counters = counters.map((i) => `${instanceName}~${i}.percent`)
      } else {
        counters = counters.map((i) => `monitor.${i}.percent`)
      }
      return buildWidgetContext({
        groupType: 'availability',
        timeline,
        category: WidgetTypeConstants.CHART,
        widgetType: WidgetTypeConstants.STACKED_VERTICAL_BAR,
        counters: counters.map((c) =>
          makeCounter(c, 'avg', 'Monitor', alert.monitor)
        ),
        ...(alert.instance
          ? {
              preFilters: {
                condition: 'and',
                inclusion: 'include',
                conditions: [
                  {
                    operand: alert.counterRawName.split('~')[0],
                    operator: '=',
                    value: alert.instance,
                  },
                ],
              },
            }
          : {}),
      }).getContext()
    },
    isStatusChart() {
      return this.alert.policyType === 'Availability'
    },
    yAxisPlotLines() {
      return [
        ...(+this.yAxisPlotBands?.[0]?.to
          ? [
              {
                color: `var(--severity-${this.activeItem.severity.toLowerCase()})`,
                width: 1,
                value: +this.yAxisPlotBands[0].to,
                zIndex: 9999,
                dashStyle: 'dash',
              },
            ]
          : []),

        ...(+this.yAxisPlotBands?.[0]?.from
          ? [
              {
                color: `var(--severity-${this.activeItem.severity.toLowerCase()})`,
                width: 1,
                value: +this.yAxisPlotBands[0].from,
                zIndex: 9999,
                dashStyle: 'dash',
              },
            ]
          : []),
      ]
    },
    yAxisPlotBands() {
      return this.plotBands?.filter(
        (b) => b.key === this.activeItem?.severity?.toLowerCase()
      )
    },
    xAxisPlotLines() {
      const offset = Moment().tz(this.timezone).utcOffset() * 60 * 1000
      if (this.alert.groupCategory === 'metric' && !this.isForecastPolicy) {
        return [
          {
            color: `var(--page-text-color)`,
            width: 1,
            value: +this.activeItem.time * 1000 + offset,
            zIndex: 9999,
          },
        ]
      }
      return []
    },
  },
  watch: {
    timeline(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.requestAlertTrend()
      }
    },
  },
  created() {
    this.requestAlertTrend()
  },
  methods: {
    requestAlertTrend() {
      if (this.isStatusChart && !this.useBands) {
        return
      }
      // getAlertOverlayCount(this.alert, this.timeline).then((data) => {
      //   this.alertSeries = Object.freeze(data)
      // })
      return null
    },
  },
}
</script>
