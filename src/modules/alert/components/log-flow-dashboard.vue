<template>
  <FlotoContentLoader :loading="loading">
    <FlotoScrollView>
      <div class="flex flex-col flex-1">
        <!-- <MRow
          class="flex mt-2"
          :gutter="8"
          style="flex-shrink: 0; height: 25vh"
        >
          <MCol auto-size class="h-full w-1/3">
            <div class="vue-grid-item h-full">
            <small class="mx-2 my-2">Top Source Host by Alert Count</small>
              <ChartView
                v-if="pieChartData"
                :widget="pieChartWidget"
                :data="pieChartData"
                enable-legend
                is-preview
                disable-severity-colors
                pie-inner-size="70%"
              />
            </div>
          </MCol>
          <MCol auto-size class="h-full w-2/3">
            <div class="vue-grid-item h-full">
            <small class="mx-2 my-2">Top Group by Alert Count</small>
              <ChartView
                :widget="columnChartWidget"
                :data="columnChartDataComputed"
                enable-legend
                is-preview
                availability-colors
              />
            </div>
          </MCol>
        </MRow> -->
        <MRow class="flex mt-2 alert-dashboard" :gutter="8">
          <MCol auto-size class="h-full w-1/3">
            <div class="vue-grid-item h-full flex flex-col">
              <small class="font-500 mx-2 my-2"
                >{{ categoryName }} Alert By Severity Overview</small
              >
              <div class="flex-1 min-h-0 flex flex-col">
                <ChartView
                  v-if="pieChartData"
                  :key="severity"
                  :widget="alertBySeverityWidget"
                  :data="pieChartDataBySeverity"
                  is-preview
                  availability-colors
                  enable-legend
                  pie-inner-size="70%"
                  :use-force-drill-down="true"
                  @force-drill-down="$emit('force-drill-down', $event)"
                />
              </div>
            </div>
          </MCol>
          <MCol auto-size class="h-full w-2/3">
            <div class="vue-grid-item h-full flex flex-col">
              <small class="font-500 mx-2 my-2"
                >{{ categoryName }} Alert By Severity</small
              >
              <ChartView
                :widget="columnChartWidget"
                :data="columnChartDataComputed"
                enable-legend
                is-preview
                availability-colors
                :use-force-drill-down="true"
                @force-drill-down="$emit('force-drill-down', $event)"
              />
            </div>
          </MCol>
        </MRow>
        <div
          class="flex flex-col vue-grid-item mt-2"
          style="flex-shrink: 0; height: 30vh"
        >
          <small class="font-500 mx-2 my-2"
            >{{ categoryName }} Alert Trend</small
          >
          <div class="flex-1 min-h-0 flex flex-col">
            <ChartView
              :widget="chartHistoricalWidget"
              :data="historicChartDataComputed"
              is-preview
              availability-colors
            />
          </div>
        </div>
      </div>
    </FlotoScrollView>
  </FlotoContentLoader>
</template>

<script>
import Capitalize from 'lodash/capitalize'
import isEqual from 'lodash/isEqual'
import Bus from '@utils/emitter'
import Constants from '@constants'
import { buildWidgetContext, makeCounter } from '@utils/socket-event-as-api'
import { WidgetTypeConstants } from '@components/widgets/constants'
import ChartView from '@/src/components/widgets/views/chart-view.vue'
import {
  getAlertCountData,
  getHistoricChart,
  getAlertBySeverityChart,
  getPieChartByType,
  getColumnChartByType,
} from '../helpers/alert-helper'
import { sortedSeriesData } from '@components/widgets/helper'

const SEVERITY_TO_CONSIDER = [
  // Constants.DOWN,
  // Constants.UNREACHABLE,
  Constants.CRITICAL,
  Constants.MAJOR,
  Constants.WARNING,
  // Constants.CLEAR,
]

export default {
  name: 'LogFlowDashboard',
  components: {
    ChartView,
  },
  inject: { SocketContext: { default: {} } },
  props: {
    timeline: {
      type: Object,
      required: true,
    },
    severity: {
      type: String,
      default: 'total',
    },
    category: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      loading: true,
      count: {},
      historicChartData: {},
      columnChartData: {},
      alertBySeverityChartData: {},
      alertTrendChartData: {},
      pieChartData: null,
    }
  },
  computed: {
    categoryName() {
      return Capitalize(this.category)
    },
    columnChartDataComputed() {
      const severity = this.severity
      if (severity && severity !== 'total') {
        return {
          ...this.columnChartData,
          series: (this.columnChartData.series || []).filter(
            (c) => c.name.toLowerCase() === severity.toLowerCase()
          ),
        }
      }
      return this.columnChartData
    },
    alertBySeverityChartDataComputed() {
      const severity = this.severity
      if (severity && severity !== 'total') {
        return {
          ...this.alertBySeverityChartData,
          categories: (this.alertBySeverityChartData.categories || []).filter(
            (c) => c.toLowerCase() === severity.toLowerCase()
          ),
          series: (this.alertBySeverityChartData.series || []).map((c) => ({
            ...c,
            data: c.data.filter(
              (d) => d.severity.toLowerCase() === severity.toLowerCase()
            ),
          })),
        }
      }
      return this.alertBySeverityChartData
    },
    columnChartWidget() {
      return buildWidgetContext({
        groupType: 'policy',
        category: WidgetTypeConstants.TOPN,
        widgetType: WidgetTypeConstants.STACKED_VERTICAL_BAR,
        groupCategory: this.category,
        counters: [makeCounter('severity', 'count')],
        resultBy: ['policy', 'severity'],
        timeline: this.timeline,
      })
        .setWidgetProperties({
          styleSetting: {
            chartOptions: {
              yAxis: {
                allowDecimal: false,
              },
            },
          },
        })
        .appendToGroup('policy', { severity: SEVERITY_TO_CONSIDER })
        .setWidgetProperties({
          styleSettings: {
            chartOptions: {
              yAxis: {
                allowDecimals: false,
              },
            },
          },
        })
        .getContext()
    },
    historicChartDataComputed() {
      const severity = this.severity
      if (severity && severity !== 'total') {
        return {
          ...this.historicChartData,
          series: (this.historicChartData.series || []).filter(
            (c) => c.entity.toLowerCase() === severity.toLowerCase()
          ),
        }
      }
      return this.historicChartData
    },
    chartHistoricalWidget() {
      return buildWidgetContext({
        groupType: 'policy',
        category: WidgetTypeConstants.CHART,
        widgetType: WidgetTypeConstants.STACKED_VERTICAL_BAR,
        groupCategory: this.category,
        counters: [makeCounter('severity', 'count')],
        resultBy: ['severity'],
        timeline: this.timeline,
      })
        .setWidgetProperties({
          styleSetting: {
            chartOptions: {
              yAxis: {
                allowDecimal: false,
              },
            },
          },
        })
        .appendToGroup('policy', { severity: SEVERITY_TO_CONSIDER })
        .getContext()
    },
    alertTrendWidget() {
      return buildWidgetContext({
        groupType: 'policy',
        category: WidgetTypeConstants.CHART,
        widgetType: WidgetTypeConstants.STACKED_VERTICAL_BAR,
        groupCategory: this.category,
        counters: [makeCounter('severity', 'count')],
        timeline: this.timeline,
        resultBy: ['severity'],
      })
        .setWidgetProperties({
          styleSetting: {
            chartOptions: {
              yAxis: {
                allowDecimal: false,
              },
            },
          },
        })
        .appendToGroup('policy', { severity: SEVERITY_TO_CONSIDER })
        .getContext()
    },
    alertBySeverityWidget() {
      return buildWidgetContext({
        groupType: 'policy',
        category: WidgetTypeConstants.GAUGE,
        widgetType: WidgetTypeConstants.PIE,
        groupCategory: this.category,
        counters: [makeCounter('severity', 'count')],
        resultBy: ['severity'],
        timeline: this.timeline,
      })
        .setWidgetProperties({
          legendEnabled: true,
          // styleSetting: {
          //   chartOptions: {
          //     yAxis: {
          //       allowDecimal: false,
          //     },
          //   },
          // },
        })
        .appendToGroup('policy', { severity: SEVERITY_TO_CONSIDER })
        .getContext()
    },
    pieChartDataBySeverity() {
      const series = this.pieChartData?.series || []
      return {
        ...this.pieChartData,
        series: sortedSeriesData(
          series.map((s, index) => ({
            ...s,
            data: s.data.filter((d, i) => {
              if (this.severity === 'total') return true
              else return d.name.toLowerCase() === this.severity
            }),
          }))
        ),
      }
    },
  },
  watch: {
    view(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.requestAlertData()
        this.requestPieChartData()
      }
    },
    timeline(newValue, oldValue) {
      if (!isEqual(newValue, oldValue)) {
        this.requestAlertData()
        this.requestPieChartData()
      }
    },
  },
  created() {
    Bus.$on('socket:connected', this.requestAlertData)
    if (this.SocketContext.connected) {
      this.requestAlertData()
    }
    this.$once('hook:beforeDestroy', () => {
      Bus.$off('socket:connected', this.requestAlertData)
    })
  },
  methods: {
    requestPieChartData() {
      getPieChartByType(
        this.timeline,
        this.category,
        SEVERITY_TO_CONSIDER,
        this.view
      ).then((data) => {
        this.pieChartData = Object.freeze(data)
      })
    },
    requestColumnChartData() {
      getColumnChartByType(
        this.timeline,
        this.category,
        SEVERITY_TO_CONSIDER,
        this.view
      ).then((data) => {
        this.columnChartData = Object.freeze(data)
      })
    },
    requestHistoricChartData() {
      getHistoricChart(this.timeline, this.category, SEVERITY_TO_CONSIDER).then(
        (data) => {
          this.historicChartData = Object.freeze(data)
        }
      )
    },
    requestAlertBySeverityChartData() {
      getAlertBySeverityChart(
        this.timeline,
        this.category,
        SEVERITY_TO_CONSIDER
      ).then((data) => {
        this.alertBySeverityChartData = Object.freeze({
          ...data,
          series: data.series.map((s, index) => ({
            ...s,
            data: s.data.map((d, i) => ({
              y: d,
              ...(data.categories[i]
                ? {
                    severity: data.categories[i],
                    color: `var(--severity-${data.categories[
                      i
                    ].toLowerCase()})`,
                  }
                : {}),
            })),
            // entity: data.categories[index].toLowerCase(),
            // monitor: data.categories[index].toLowerCase(),
            // ip: data.categories[index].toLowerCase(),
          })),
        })
      })
    },
    requestAlertTrendChartData() {
      getHistoricChart(
        this.timeline,
        this.category,
        SEVERITY_TO_CONSIDER,
        []
      ).then((data) => {
        this.alertTrendChartData = Object.freeze(data)
      })
    },
    requestAlertData() {
      this.loading = true
      this.pieChartData = null
      this.columnChartData = {}
      this.requestPieChartData()
      this.requestColumnChartData()
      this.requestHistoricChartData()
      this.requestAlertBySeverityChartData()
      this.requestAlertTrendChartData()
      getAlertCountData(this.timeline, this.category, this.view).then(
        (rows) => {
          this.handleCountReceived(rows)
        }
      )
    },
    async handleCountReceived(rows) {
      this.count = Object.freeze(
        rows.reduce(
          (result, item) => {
            if (SEVERITY_TO_CONSIDER.includes(item.severity)) {
              return {
                ...result,
                [item.severity.toLowerCase()]:
                  (result[item.severity.toLowerCase()] || 0) +
                  item['severity.count'],
              }
            }
            return result
          },
          SEVERITY_TO_CONSIDER.reduce(
            (obj, severity) => ({
              ...obj,
              [severity.toLowerCase()]: 0,
            }),
            {}
          )
        )
      )
      this.$emit('count', this.count)
      this.loading = false
    },
  },
}
</script>
