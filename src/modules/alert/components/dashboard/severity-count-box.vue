<template>
  <div
    class="pb-4"
    :style="{
      display: 'grid',
      'grid-auto-rows': 'minmax(180px, max-content)',
      'grid-template-columns': 'repeat(auto-fill, minmax(600px, 1fr))',
      'grid-gap': '8px',
    }"
  >
    <div v-for="alert in boxes" :key="alert.text" ref="boxRef" class="flex">
      <div
        class="flex flex-1 vue-grid-item"
        :style="{ width: `${boxWidth}px !important` }"
      >
        <div
          class="flex flex-col cursor-pointer px-2 flex-1"
          @click="navigateToDetail(alert)"
        >
          <div class="flex items-center">
            <div class="font-500 pt-3 m-0 pb-3 inline-block text-size-medium">
              {{ alert.text }}
            </div>
            <MTag rounded :closable="false" class="tag-primary ml-2">
              {{ alert.total | numberFormat }}
            </MTag>
          </div>
          <div
            class="flex pb-4 flex-1 w-full"
            :gutter="0"
            :style="{ width: `${boxWidth - 16}px !important` }"
          >
            <!-- :style="{ width: `${boxWidth - 16}px !important` }" -->
            <template v-for="s in severities">
              <div
                :key="s"
                class="flex flex-1 mx-1 items-start severity-text min-w-0"
                :size="3"
                :class="{
                  'cursor-auto': !alert[s],
                }"
                @click.stop="
                  () => (alert[s] ? navigateToDetail(alert, s) : undefined)
                "
              >
                <div
                  class="flex-1 h-full items-start flex flex-col min-w-0"
                  :style="
                    selected && selected !== 'total'
                      ? { opacity: selected === s ? 1 : 0.4 }
                      : !alert[s]
                      ? { opacity: 0.4 }
                      : {}
                  "
                >
                  <RadialProgress
                    :value="alert[`${s}_percentage`]"
                    :color="`var(--severity-${s.toLowerCase()})`"
                    :stroke-width="6"
                  >
                    <h4
                      class="text m-0 text-2xl font-600"
                      :class="{
                        [s]: true,
                        [`font-bold`]: selected === s,
                      }"
                    >
                      {{ (alert[`${s}`] || 0) | numberFormat }}
                    </h4>
                    <template v-slot:bottom-header>
                      <div class="mt-1 text-neutral text-center">
                        {{
                          `${s.charAt(0).toUpperCase()}${s
                            .slice(1)
                            .toLowerCase()}`
                        }}
                      </div>
                    </template>
                  </RadialProgress>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Uniq from 'lodash/uniq'
import RadialProgress from '@components/radial-progress.vue'
// import Severity from '@components/severity.vue'

export default {
  name: 'SeverityCountBox',
  components: {
    RadialProgress,
    // Severity,
  },
  props: {
    boxes: { type: Array, required: true },
    selected: { type: String, required: true },
    view: { type: String, default: undefined },
    groupType: { type: String, default: undefined },
    timeline: { type: Object, default: undefined },
  },
  data() {
    return {
      boxWidth: undefined,
    }
  },
  computed: {
    severities() {
      return Uniq(
        this.boxes
          .reduce(
            (result, box) => [
              ...result,
              ...Object.keys(box).filter(
                (key) => !['text', 'category', 'total'].includes(key)
              ),
            ],
            []
          )
          .filter((key) => /_percentage$/.test(key) === false)
      )
    },
  },
  mounted() {
    this.$_resizeObserver = new ResizeObserver(() => {
      this.boxWidth = this.$el.querySelector('div:first-child').offsetWidth
    })
    this.$_resizeObserver.observe(this.$el.querySelector('div:first-child'))

    this.$once('hook:beforeDestroy', () => {
      if (this.$_resizeObserver) {
        this.$_resizeObserver.disconnect()
        this.$_resizeObserver = undefined
      }
    })
    this.boxWidth = this.$el.querySelector('div:first-child').offsetWidth
  },
  methods: {
    getNonZeroSeveritiesForAlert(alert) {
      return Object.keys(alert).filter(
        (key) => !['text', 'category', 'total'].includes(key) && alert[key]
      )
    },
    navigateToDetail(item, severity) {
      const tab =
        item.text === this.$constants.LOG
          ? 'log'
          : item.text === this.$constants.FLOW
          ? 'flow'
          : item.text === this.$constants.CORRELATED_ALERTS
          ? 'correlated'
          : this.groupType
      const view = this.view
      this.$router.push(
        this.$currentModule.getRoute('stream', {
          params: {
            category: item.category,
            tab,
          },
          query: {
            t: encodeURIComponent(btoa(JSON.stringify(this.timeline))),
            ...(severity || view
              ? {
                  ...(severity ? { severity } : {}),
                  view,
                }
              : {}),
          },
        })
      )
    },
  },
}
</script>

<style lang="less" scoped>
.severity-text {
  &:last-child {
    border-right: none;
  }
}
</style>
