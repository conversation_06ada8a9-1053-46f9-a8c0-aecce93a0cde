<template>
  <FlotoDrawer
    ref="drawerRef"
    :scrolled-content="false"
    width="65%"
    :wrap-style="{ zIndex: 998 }"
    :open="isO<PERSON> && <PERSON>(drillDownContext)"
    @hide="handleDrawerHide"
  >
    <template v-slot:title>
      {{
        drillDownContext
          ? `monitor.${drillDownContext.severity.toLowerCase()}`
          : ''
      }}
    </template>
    <div
      v-if="isO<PERSON> && Boolean(drillDownContext)"
      class="flex flex-col h-full w-full px-2 pl-4"
    >
      <WidgetContainer
        :widget="trendCountWidget"
        is-preview
        watch-widget
        class="w-full"
      />
    </div>
  </FlotoDrawer>
</template>

<script>
import Bus from '@utils/emitter'

import { buildWidgetContext } from '@utils/socket-event-as-api'
import { WidgetTypeConstants } from '@components/widgets/constants'
import {
  // getDefaultDataForGroup,
  DEFAULT_STREAM_CATEGORY_GROUP,
  getWidgetProperties,
  // canRenderWidgetPreview,
  overrideWidgetPropertyByWidgetCategory,
} from '@components/widgets/helper'
import WidgetContainer from '@components/widgets/views/container.vue'

export default {
  name: 'AlertTrendDrillDown',
  components: {
    WidgetContainer,
  },
  props: {
    // open: {
    //   type: Boolean,
    //   default: false,
    // },
    drillDownContext: {
      type: Object,
      default: undefined,
    },
    item: { type: Object, default: undefined },
  },
  data() {
    return {
      isOpen: false,
    }
  },

  computed: {
    trendCountWidget() {
      const defaultGroupData =
        DEFAULT_STREAM_CATEGORY_GROUP[this.drillDownContext.tabType]

      const widgetProperties = getWidgetProperties(
        WidgetTypeConstants.STREAM,
        overrideWidgetPropertyByWidgetCategory(WidgetTypeConstants.STREAM)
      )

      const widgetDef = buildWidgetContext({
        groupCategory: 'metric',
        groupType:
          this.drillDownContext.tabType === 'metric' ? 'policy.flap' : 'policy',
        timeline: this.drillDownContext.timeRange,
        widgetType: WidgetTypeConstants.GRID,
        category: WidgetTypeConstants.STREAM,
        counters: [
          ...defaultGroupData.counters.map((c) => ({
            ...c,
            counter: c.counter.key,
          })),
        ],
      })
        .appendToGroup(
          this.drillDownContext.tabType === 'metric' ? 'policy.flap' : 'policy',
          {
            ...defaultGroupData,

            ...(this.drillDownContext.severity
              ? { severity: [this.drillDownContext.severity.toUpperCase()] }
              : {}),
          }
        )
        .setWidgetProperties({
          ...widgetProperties,
          columnSettings: widgetProperties.columnSettings.map((c) => {
            return {
              ...c,
              ...(['object.type', 'object.ip'].includes(c.rawName)
                ? { hidden: false }
                : {}),
            }
          }),
          searchable: true,
        })
        .getContext()

      return { ...widgetDef, timeRangeInclusive: true }
    },
  },

  watch: {
    drillDownContext: {
      immediate: true,
      handler(newValue) {
        if (newValue) {
          this.isOpen = true
        }
      },
    },
  },
  created() {
    Bus.$on('row-click', this.handleDrawerHide)
  },
  beforeDestroy() {
    Bus.$off('row-click', this.handleDrawerHide)
  },
  methods: {
    handleDrawerHide() {
      this.isOpen = false
      this.$emit('hide')

      if (this.$refs.drawerRef) {
        this.$refs.drawerRef.hide()
      }
    },
  },
}
</script>
