<template>
  <div class="inline-flex">
    <div
      v-for="key in Object.keys(count)"
      :key="key"
      class="rounded-widget mr-2 inline-flex px-4"
      :class="{
        [`${key} active`]: value === key,
        [`${key} hovered text-neutral`]: value !== key,
      }"
      @click="onClickSeverity(key)"
    >
      <div class="pt-2 pb-2 w-title pr-2">
        {{ capitalize(key) }}
      </div>
      <div class="pt-2 pb-2 text-right w-count text" :class="key">
        {{ count[key] | numberFormat }}
      </div>
    </div>
  </div>
</template>

<script>
import Capitalize from 'lodash/capitalize'

export default {
  name: 'SeveritySwitch',
  model: { event: 'change' },
  props: {
    count: {
      type: Object,
      default() {
        return {}
      },
    },
    value: {
      type: String,
      default: undefined,
    },
  },
  methods: {
    onClickSeverity(severity) {
      if (severity === this.value) {
        this.$emit('change', 'total')
      } else {
        this.$emit('change', severity)
      }
    },
    capitalize(key) {
      return Capitalize(key)
    },
  },
}
</script>
