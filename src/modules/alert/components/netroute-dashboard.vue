<template>
  <FlotoContentLoader :loading="loading">
    <FlotoScrollView>
      <div class="flex flex-col flex-1">
        <MRow class="flex mt-2 alert-dashboard" :gutter="8">
          <MCol :size="6" class="h-full">
            <div class="vue-grid-item h-full flex flex-col">
              <small
                class="font-500 mx-2 my-2 m-0 inline-block text-size-medium"
                >{{ netrouteTypeDisplay }} Alert by Severity Overview</small
              >
              <div class="flex-1 min-h-0 flex flex-col">
                <ChartView
                  v-if="pieChartData"
                  :key="view"
                  class="mb-3"
                  :widget="pieChartWidget"
                  :data="pieChartDataComputed"
                  enable-legend
                  availability-colors
                  is-preview
                  pie-inner-size="70%"
                  :use-force-drill-down="true"
                  @force-drill-down="$emit('force-drill-down', $event)"
                />
              </div>
            </div>
          </MCol>
          <MCol :size="6" class="h-full">
            <div class="vue-grid-item h-full flex flex-col">
              <small class="font-500 mx-2 my-2"
                >{{ netrouteTypeDisplay }} Alert By Severity</small
              >
              <ChartView
                :widget="columnChartWidget"
                :data="columnChartDataComputed"
                enable-legend
                is-preview
                availability-colors
                :use-force-drill-down="true"
                @force-drill-down="$emit('force-drill-down', $event)"
              />
            </div>
          </MCol>
        </MRow>
        <MRow class="flex mt-2 alert-dashboard" :gutter="8">
          <MCol :size="12" class="h-full">
            <div class="vue-grid-item h-full flex flex-col">
              <small
                class="font-500 mx-2 my-2 m-0 inline-block text-size-medium"
                >{{ netrouteTypeDisplay }} Alert Trend</small
              >
              <div class="flex-1 min-h-0 flex flex-col">
                <ChartView
                  :widget="chartHistoricalWidget"
                  :data="historicChartDataComputed"
                  is-preview
                  availability-colors
                />
              </div>
            </div>
          </MCol>
        </MRow>
      </div>
    </FlotoScrollView>
  </FlotoContentLoader>
</template>

<script>
import GroupBy from 'lodash/groupBy'
import isEqual from 'lodash/isEqual'

import Capitalize from 'lodash/capitalize'
import Constants from '@constants'
import Bus from '@utils/emitter'
import { buildWidgetContext, makeCounter } from '@utils/socket-event-as-api'
import { WidgetTypeConstants } from '@components/widgets/constants'
import {
  getAlertCountData,
  getColumnChartByType,
  // getAlertBySeverityChartAlertTrendData,
  // getAlertBySeverityChartAlertTrendDataHopToHop,
  getPieChartByType,
  getHistoricChart,
} from '../helpers/alert-helper'
import ChartView from '@/src/components/widgets/views/chart-view.vue'

const SEVERITY_TO_CONSIDER = [
  Constants.DOWN,
  Constants.UNREACHABLE,
  Constants.CRITICAL,
  Constants.MAJOR,
  Constants.WARNING,
]

export default {
  name: 'NetRouteDashboard',
  components: {
    ChartView,
  },
  inject: { SocketContext: { default: {} } },
  props: {
    timeline: {
      type: Object,
      required: true,
    },
    view: {
      type: String,
      default: 'live',
    },
    category: {
      type: String,
      required: true,
    },
    severity: {
      type: String,
      default: 'total',
    },
  },
  data() {
    return {
      count: {},
      boxes: [],
      loading: true,
      tabType: this.$constants.NETROUTE,
      columnChartData: {},
      historicChartData: {},
      pieChartData: null,
    }
  },
  computed: {
    netrouteTypeDisplay() {
      return this.category === 'netroute-source-to-destination'
        ? 'Source To Destination'
        : 'Hop To Hop'
    },
    netrouteTypeOption() {
      return this.category === 'netroute-source-to-destination'
        ? this.$constants.SOURCE_TO_DESTINATION
        : this.$constants.HOP_TO_HOP
    },
    netrouteGroupCategory() {
      return this.netrouteTypeOption === Constants.SOURCE_TO_DESTINATION
        ? 'netroute.metric'
        : 'netroute.event'
    },
    alertTrendWidget() {
      return buildWidgetContext({
        groupType:
          this.netrouteTypeOption === Constants.SOURCE_TO_DESTINATION
            ? 'policy.stream'
            : 'policy',
        category: WidgetTypeConstants.CHART,
        widgetType: WidgetTypeConstants.STACKED_VERTICAL_BAR,
        groupCategory: this.netrouteGroupCategory,
        counters: [makeCounter('severity', 'count')],
        resultBy: ['severity'],
        timeline: this.timeline,
      })
        .setWidgetProperties({
          styleSetting: {
            chartOptions: {
              yAxis: {
                allowDecimal: false,
              },
            },
          },
        })
        .setWidgetProperties({
          styleSettings: {
            chartOptions: {
              yAxis: {
                allowDecimals: false,
              },
            },
          },
        })
        .getContext()
    },
    pieChartWidget() {
      return buildWidgetContext({
        groupType:
          this.netrouteTypeOption === Constants.SOURCE_TO_DESTINATION
            ? 'policy.stream'
            : 'policy',
        category: WidgetTypeConstants.GAUGE,
        widgetType: WidgetTypeConstants.PIE,
        groupCategory: this.netrouteGroupCategory,
        counters: [makeCounter('severity', 'count')],
        resultBy: ['policy.type'],
        timeline: this.timeline,
      })
        .appendToGroup('alert', { severity: SEVERITY_TO_CONSIDER })
        .setWidgetProperties({
          legendEnabled: true,
        })
        .getContext()
    },
    chartHistoricalWidget() {
      return buildWidgetContext({
        groupType:
          this.netrouteTypeOption === Constants.SOURCE_TO_DESTINATION
            ? 'policy.stream'
            : 'policy',
        groupCategory: this.netrouteGroupCategory,
        category: WidgetTypeConstants.CHART,
        widgetType: WidgetTypeConstants.STACKED_VERTICAL_BAR,
        counters: [makeCounter('severity', 'count')],
        resultBy: ['severity'],
        timeline: this.timeline,
      })
        .setWidgetProperties({
          styleSetting: {
            chartOptions: {
              yAxis: {
                allowDecimal: false,
              },
            },
          },
        })
        .appendToGroup('alert', { severity: SEVERITY_TO_CONSIDER })
        .getContext()
    },
    columnChartWidget() {
      return buildWidgetContext({
        groupType:
          this.netrouteTypeOption === Constants.SOURCE_TO_DESTINATION
            ? 'policy.stream'
            : 'policy',
        groupCategory: this.netrouteGroupCategory,
        category: WidgetTypeConstants.TOPN,
        widgetType: WidgetTypeConstants.STACKED_VERTICAL_BAR,
        counters: [makeCounter('severity', 'count')],
        resultBy: ['policy', 'severity'],
        timeline: this.timeline,
      })
        .setWidgetProperties({
          styleSetting: {
            chartOptions: {
              yAxis: {
                allowDecimal: false,
              },
            },
          },
        })
        .appendToGroup('policy', { severity: SEVERITY_TO_CONSIDER })
        .setWidgetProperties({
          styleSettings: {
            chartOptions: {
              yAxis: {
                allowDecimals: false,
              },
            },
          },
        })
        .getContext()
    },
    historicChartDataComputed() {
      const severity = this.severity
      if (severity && severity !== 'total') {
        return {
          ...this.historicChartData,
          series: (this.historicChartData.series || []).filter(
            (c) => c.entity.toLowerCase() === severity.toLowerCase()
          ),
        }
      }
      return this.historicChartData
    },
    columnChartDataComputed() {
      const severity = this.severity
      if (severity && severity !== 'total') {
        return {
          ...this.columnChartData,
          series: (this.columnChartData.series || []).filter(
            (c) => c.counter.toLowerCase() === severity.toLowerCase()
          ),
        }
      }
      return this.columnChartData
    },
    pieChartDataComputed() {
      const severity = this.severity
      if (severity && severity !== 'total') {
        return {
          categories: (this.pieChartData.categories || []).filter(
            (c) => c.toLowerCase() === severity
          ),
          series: (this.pieChartData.series || []).filter(
            (i) => i.data[0].name.toLowerCase() === severity.toLowerCase()
          ),
        }
      }
      return this.pieChartData
    },
  },
  watch: {
    view(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.requestAlertData()
      }
    },
    timeline(newValue, oldValue) {
      if (!isEqual(newValue, oldValue)) {
        this.requestAlertData()
      }
    },
  },
  created() {
    Bus.$on('socket:connected', this.requestAlertData)
    if (this.SocketContext.connected) {
      this.requestAlertData()
    }
    this.$once('hook:beforeDestroy', () => {
      Bus.$off('socket:connected', this.requestAlertData)
    })
  },
  methods: {
    requestPieChartData() {
      getPieChartByType(
        this.timeline,
        Constants.NETROUTE,
        SEVERITY_TO_CONSIDER,
        this.view,
        {
          netrouteType: this.netrouteTypeOption,
        }
      ).then((data) => {
        this.pieChartData = Object.freeze(data)
      })
    },
    requestAlertBySeverityChartData() {
      getColumnChartByType(
        this.timeline,
        Constants.NETROUTE,
        SEVERITY_TO_CONSIDER,
        this.view,
        {
          netrouteType: this.netrouteTypeOption,
        }
      ).then((data) => {
        this.columnChartData = Object.freeze(data)
      })
    },
    requestHistoricChartData() {
      // if (this.view === 'live') {
      //   return
      // }
      getHistoricChart(
        this.timeline,
        Constants.NETROUTE,
        SEVERITY_TO_CONSIDER,
        undefined,
        {
          netrouteType: this.netrouteTypeOption,
        }
      ).then((data) => {
        this.historicChartData = Object.freeze(data)
      })
    },
    requestAlertData() {
      this.loading = true
      this.pieChartData = null
      this.columnChartData = {}
      this.historicChartData = {}
      this.requestAlertBySeverityChartData()
      this.requestPieChartData()
      this.requestHistoricChartData()
      getAlertCountData(this.timeline, Constants.NETROUTE, this.view, {
        netrouteType: this.netrouteTypeOption,
      }).then((rows) => {
        this.handleCountReceived(rows)
      })
    },
    async handleCountReceived(rows) {
      const categoryGroups = GroupBy(rows, 'object.category')
      const boxes = Object.keys(categoryGroups).map((category) => {
        const severityGroups = categoryGroups[category]
          .filter((item) => SEVERITY_TO_CONSIDER.includes(item.severity))
          .reduce(
            (prev, item) => ({
              ...prev,
              [item.severity]:
                (prev[item.severity] || 0) +
                (item.count || item['severity.count'] || 0),
            }),
            {}
          )
        const total = Object.keys(severityGroups).reduce(
          (prev, key) => prev + severityGroups[key],
          0
        )
        return {
          category: category,
          text:
            category.toLowerCase() === 'hci'
              ? category.toUpperCase()
              : Capitalize(category),
          ...SEVERITY_TO_CONSIDER.reduce(
            (obj, severity) => ({
              ...obj,
              [severity.toLowerCase()]: severityGroups[severity] || 0,
              [`${severity.toLowerCase()}_percentage`]:
                (100 * (severityGroups[severity] || 0)) / total,
            }),
            {}
          ),
          total,
        }
      })
      this.boxes = boxes
      this.count = Object.freeze(
        boxes.reduce(
          (result, item) => {
            return Object.keys(result).reduce((total, key) => {
              total[key] += item[key] || 0
              return total
            }, result)
          },
          SEVERITY_TO_CONSIDER.reduce(
            (obj, severity) => ({
              ...obj,
              [severity.toLowerCase()]: 0,
            }),
            {}
          )
        )
      )
      this.$emit('count', this.count)
      this.loading = false
    },
    navigateToStream() {
      this.$router.push(
        this.$currentModule.getRoute('stream', {
          params: {
            tab: this.tabType,
            category: 'Server',
          },
          query: {
            t: encodeURIComponent(btoa(JSON.stringify(this.timeline))),
            view: this.view,
            stream: 'all',
          },
        })
      )
    },
  },
}
</script>
