<template>
  <div
    ref="containerRef"
    :style="{ height: `${height}px` }"
    class="w-full flex overflow-hidden flex-col"
  >
    <div
      v-if="segmentedDataMap.length && !loading && isDataReceived"
      class="flex w-full h-full rounded overflow-hidden"
    >
      <div
        v-for="(item, index) in segmentedDataMap"
        :key="index"
        :style="{
          backgroundColor: `var(--severity-${item.severity.toLowerCase()}) !important`,
          width: `${item.percentage}%`,
          height: '100%',
        }"
        @mouseenter="showTooltip($event, item, index)"
        @mouseleave="hideTooltip"
      />
    </div>
    <div v-else class="w-full h-full rounded overflow-hidden" />

    <Portal v-if="activeItem" to="heatmap-widget-tooltip">
      <div
        ref="tooltipContainerRef"
        class="ant-popover readable-content-overlay shadow-lg rounded heatmap-tooltip"
      >
        <div
          data-popper-arrow
          class="popover-arrow always-visible"
          :class="{
            hidden: !activeItem,
            block: activeItem,
          }"
          :style="{
            background: `var(--severity-${activeItem.severity.toLowerCase()}) !important`,
          }"
        />
        <div v-if="activeItem" class="ant-popover-content">
          <div
            role="popover"
            class="ant-popover-inner relative"
            style="padding: 0 !important"
          >
            <SegmentedCellTooltip :item="activeItem" />
          </div>
        </div>
      </div>
    </Portal>
  </div>
</template>

<script>
import { createPopper } from '@popperjs/core'
import SegmentedCellTooltip from './segmented-cell-tooltip.vue'

import SumBy from 'lodash/sumBy'

export default {
  name: 'SegmentedDataLine',
  components: {
    SegmentedCellTooltip,
  },
  props: {
    data: {
      type: Array,
      required: true,
    },
    height: {
      type: Number,
      default: 20,
    },
    isDataReceived: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      containerWidth: undefined,
      segmentedDataMap: [],
      activeItem: null,
      loading: false,
    }
  },
  watch: {
    data(newValue) {
      if (newValue) {
        this.buildSegmentedData()
      }
    },
  },
  created() {
    this.buildSegmentedData()
  },

  methods: {
    buildSegmentedData() {
      this.loading = true
      const data = Object.freeze(this.data)
      const totalDuration = SumBy(data, 'duration')

      if (data.length) {
        let dataMap = []
        // let currentSeverity = data[0].severity
        // let flapIndex = 0
        // let noFlapingCount = 0

        // if (data.length === 1) {
        //   dataMap = [
        //     {
        //       endTime: data[0].time,
        //       startTime: data[0].time,
        //       percentage: 100,
        //       severity: data[0].severity,
        //       duration: data[0].duration,
        //     },
        //   ]
        // } else {

        for (let index = 0; index < data.length; index++) {
          // if (
          //   data[index].severity &&
          //   data[index].severity !== currentSeverity
          // ) {
          dataMap = [
            {
              endTime: data[index].time + data[index].duration,
              startTime: data[index].time,
              percentage: (data[index].duration * 100) / totalDuration,
              severity: data[index].severity,
              duration: data[index].duration,
            },
            ...dataMap,
          ]

          // flapIndex = index
          // currentSeverity = data[index].severity || currentSeverity

          //   if (index === data.length - 1) {
          //     dataMap = [
          //       {
          //         endTime: data[index ? index - 1 : 0].time,
          //         startTime: data[index].time,
          //         percentage: 100 / data.length,
          //         severity: data[index].severity,
          //         duration: data[index].duration,
          //       },
          //       ...dataMap,
          //     ]
          //   }
          // } else {
          // noFlapingCount = noFlapingCount + 1

          // if (index === data.length - 1) {
          //   dataMap = [
          //     {
          //       endTime: data[flapIndex ? flapIndex - 1 : 0].time,
          //       startTime: data[index].time,
          //       percentage: ((index + 1 - flapIndex) * 100) / data.length,
          //       severity: data[flapIndex].severity,
          //       duration: data[flapIndex].duration,
          //     },
          //     ...dataMap,
          //   ]
          // }
          // if (
          //   noFlapingCount === data.length - 1 &&
          //   index === data.length - 1
          // ) {
          //   dataMap = [
          //     {
          //       endTime: data[0].time,
          //       startTime: data[data.length - 1].time,
          //       percentage: 100,
          //       severity: data[0].severity,
          //       duration: data[0].duration,
          //     },
          //   ]
          // }
          // }
        }
        // }

        this.segmentedDataMap = dataMap
        this.loading = false
      }
    },

    showTooltip(e, item, index) {
      this.activeItem = Object.freeze({
        ...item,
      })
      if (this.popperInstance) {
        this.popperInstance.destroy()
      }
      this.$nextTick(() => {
        this.popperInstance = createPopper(
          e.target,
          this.$refs.tooltipContainerRef,
          {
            placement: 'auto',
            modifiers: [
              {
                name: 'offset',
                options: {
                  offset: [8, 8],
                },
              },
            ],
          }
        )
      })
    },
    hideTooltip() {
      this.activeItem = null
      if (this.popperInstance) {
        this.popperInstance.destroy()
        this.popperInstance = null
      }
    },
  },
}
</script>

<style lang="less" scoped>
.popover-arrow {
  background: var(--dropdown-background);

  &.block.always-visible {
    display: block;
  }

  &::before {
    position: absolute;
    top: -4px;
    left: 0;
    z-index: -1;
    width: 10px;
    height: 10px;
    content: '';
    background: var(--chart-tooltip-background);
    transition: transform 0.2s ease-out 0s, visibility 0.2s ease-out 0s;
    transform: rotate(45deg);
    transform-origin: center center;
  }
}

[data-popper-placement^='top'] > [data-popper-arrow] {
  bottom: 2px;
  left: -5px !important;
}

[data-popper-placement^='left'] > [data-popper-arrow] {
  right: 5px;
}

[data-popper-placement^='right'] > [data-popper-arrow] {
  left: -5px;
}

[data-popper-placement^='bottom'] > [data-popper-arrow] {
  top: 0;
  left: -5px !important;
}
</style>
