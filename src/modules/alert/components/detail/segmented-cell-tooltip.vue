<template>
  <div class="flex items-center" style="max-width: 35vw">
    <div class="flex flex-col flex-1 min-w-0 px-2">
      <div class="text-ellipsis">
        <span style="font-size: 11px">
          Start Time : {{ item.startTime | datetime }}
        </span>
      </div>
      <div class="text-ellipsis">
        <span style="font-size: 11px" class="font-500 mr-1 text-base">
          End Time : {{ item.endTime | datetime }}
        </span>
      </div>
      <div class="text-ellipsis">
        <span style="font-size: 11px; font-weight: 600">
          Duration : {{ item.duration | duration }}
        </span>
      </div>
    </div>
    <div
      class="px-4 py-2 h-full flex items-center justify-center"
      :class="item.severity.toLowerCase()"
    >
      {{ `${item.severity.toLowerCase()}` }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'SegmentedCellTooltip',
  props: {
    item: {
      type: [Object],
      default: undefined,
    },
  },
}
</script>
