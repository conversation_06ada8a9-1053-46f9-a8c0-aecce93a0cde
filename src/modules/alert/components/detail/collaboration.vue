<template>
  <div class="min-h-0 flex flex-col flex-1">
    <UserProvider>
      <FlotoContentLoader :loading="loading">
        <CollaborationViewer
          :messages="data"
          :viewer-id="user.id"
          :send-message-fn="handleSendNewMessage"
        />
      </FlotoContentLoader>
    </UserProvider>
  </div>
</template>

<script>
import Moment from 'moment'
import { generateId } from '@utils/id'
import Bus from '@utils/emitter'
import CollaborationViewer from '@components/collaboration/collaboration.vue'
import { authComputed } from '@state/modules/auth'
import UserProvider from '@components/data-provider/user-provider.vue'

export default {
  name: 'Collaboration',
  components: {
    CollaborationViewer,
    UserProvider,
  },
  inject: { SocketContext: { default: {} } },
  props: {
    alert: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      guid: generateId(),
      loading: true,
      data: [],
    }
  },
  computed: {
    ...authComputed,
  },
  created() {
    Bus.$on('socket:connected', this.fetchConversation)
    this.$once('hook:beforeDestroy', () => {
      Bus.$off(
        this.$currentModule.getConfig().ALERT_STREAM_COLLABORATION,
        this.handleDataReceived
      )
      Bus.$off('socket:connected', this.fetchConversation)
    })

    Bus.$on(
      this.$currentModule.getConfig().ALERT_STREAM_COLLABORATION,
      this.handleDataReceived
    )
    if (this.SocketContext.connected) {
      this.fetchConversation()
    }
    this.loading = true
  },
  methods: {
    handleDataReceived(payload) {
      if (payload[this.$constants.UI_EVENT_UUID] !== this.guid) {
        return
      }
      if (payload['result'] != null) {
        const result = payload['result'].map((conversation) => ({
          id: conversation.uuid,
          senderId: conversation['user.id'],
          content: conversation.comment,
          dateTime: conversation.timestamp,
        }))
        this.data = Object.freeze(result)
      }
      this.$nextTick(() => {
        this.loading = false
      })
    },
    fetchConversation() {
      Bus.$emit('server:event', {
        'event.type':
          this.$currentModule.getConfig().ALERT_STREAM_COLLABORATION,
        'event.context': {
          [this.$constants.UI_EVENT_UUID]: this.guid,
          action: 'comment',
          uuid: this.alert.id,
        },
      })
    },
    handleSendNewMessage(message) {
      Bus.$emit('server:event', {
        'event.type': this.$currentModule.getConfig().ALERT_STREAM_ACTION,
        'event.context': {
          action: 'comment',
          uuid: this.alert.id,
          comment: message.content,
          'user.id': this.user.id,
        },
      })
      return new Promise((resolve) => {
        this.data = [
          ...this.data,
          {
            ...message,
            id: generateId(),
            senderId: this.user.id,
            content: message.content,
            dateTime: Moment().format('hh:mm:ss A DD/MM/YYYY'),
          },
        ]
        resolve()
      })
    },
  },
}
</script>
