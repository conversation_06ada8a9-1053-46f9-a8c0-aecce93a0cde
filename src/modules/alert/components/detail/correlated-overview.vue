<template>
  <div class="min-h-0 flex flex-col flex-1">
    <div class="flex flex-col min-h-0 flex-1 w-full pr-3 pl-2">
      <MRow :gutter="0">
        <MCol
          :size="4"
          class="wiget-background-color mt-3 rounded-lg text-left"
        >
          <table class="item-list-table with-out-border">
            <colgroup>
              <col style="width: 150px" />
              <col />
            </colgroup>
            <tbody>
              <tr>
                <td class="text-neutral px-4 pt-2 pb-2">
                  <span class="text-ellipsis correlated-details">Monitor</span>
                </td>
                <td
                  class="text-ellipsis px-4 pt-2 pb-2"
                  style="font-size: 0.8rem"
                  >{{ monitor.name }}</td
                >
              </tr>
              <tr>
                <td class="text-neutral px-4 pt-2 pb-2">
                  <span class="text-ellipsis correlated-details">IP</span>
                </td>
                <td
                  class="text-ellipsis px-4 pt-2 pb-2"
                  style="font-size: 0.8rem"
                  >{{ monitor.ip }}</td
                >
              </tr>
              <tr>
                <td class="text-neutral px-4 pt-2 pb-2">
                  <span
                    class="text-ellipsis correlated-details"
                    style="font-size: 0.8rem"
                    >Alert ID</span
                  >
                </td>
                <td
                  class="text-ellipsis px-4 pt-2 pb-2"
                  style="font-size: 0.8rem"
                  >{{ alert.policyId }}</td
                >
              </tr>
              <tr>
                <td class="text-neutral px-4 pt-2 pb-2">
                  <span class="text-ellipsis correlated-details">Host</span>
                </td>
                <td
                  class="px-4 pt-2 pb-2 text-ellipsis"
                  style="font-size: 0.8rem"
                  >{{ monitor.host }}</td
                >
              </tr>
              <tr>
                <td class="text-neutral px-4 pt-2 pb-2">
                  <span class="text-ellipsis correlated-details"
                    >Correlated Alerts</span
                  >
                </td>
                <td
                  class="text-ellipsis px-4 pt-2 pb-2"
                  style="font-size: 0.8rem"
                  >{{ alert.correlatedKeys }}</td
                >
              </tr>
              <tr>
                <td class="text-neutral px-4 pt-2 pb-2">
                  <span class="text-ellipsis correlated-details"
                    >Alert Policy</span
                  >
                </td>
                <td
                  class="text-ellipsis px-4 pt-2 pb-2"
                  style="font-size: 0.8rem"
                  >{{ alert.policy }}</td
                >
              </tr>
              <tr>
                <td class="text-neutral px-4 pt-2 pb-2">
                  <span class="text-ellipsis correlated-details">Severity</span>
                </td>
                <td
                  class="text-ellipsis px-4 pt-2 pb-2 flex items-center"
                  style="font-size: 0.8rem"
                  ><Severity
                    :severity="(alert.severity || '').toLowerCase()"
                    class="mr-2"
                  />{{ alert.severity }}</td
                >
              </tr>
              <tr>
                <td class="text-neutral px-4 pt-2 pb-2">
                  <span class="text-ellipsis correlated-details"
                    >First seen</span
                  >
                </td>
                <td
                  class="text-ellipsis px-4 pt-2 pb-2"
                  style="font-size: 0.8rem"
                >
                  {{ alert.firstSeen | timeago }}
                </td>
              </tr>
            </tbody>
          </table>
        </MCol>
        <MCol :size="8" class="text-left mt-3 pl-3">
          <CorrelatedTopology :alert="alert" :timeline="timeline" />
        </MCol>
      </MRow>
      <MRow :gutter="0" class="mt-3">
        <FlotoContentLoader :loading="loading">
          <div class="mr-2 flex">
            <MInput
              v-model="searchTerm"
              class="search-box"
              placeholder="Search"
            >
              <template v-slot:prefix>
                <MIcon name="search" />
              </template>
              <template v-if="searchTerm" v-slot:suffix>
                <MIcon
                  name="times-circle"
                  class="text-neutral-light cursor-pointer"
                  @click="searchTerm = undefined"
                />
              </template>
            </MInput>
          </div>

          <MGrid
            class="min-w-0"
            :columns="columns"
            :data="rows"
            :search-term="searchTerm"
            :paging="false"
          >
            <template v-slot:policy_name="{ item }">
              <div class="flex items-center">
                <Severity
                  disable-tooltip
                  :severity="item.severity"
                  :center="false"
                  class="mr-1"
                />
                <AlertDrilldown :alert="item" :field="item.policy_name" />
              </div>
            </template>
            <template v-slot:policy_first_trigger_tick="{ item }">
              {{ formatDateTime(item.policy_first_trigger_tick) }}
            </template>
            <template v-slot:object_id="{ item }">
              <MonitorName
                :value="item.object_id || item.monitor"
                :row="item"
              />
            </template>
          </MGrid>
        </FlotoContentLoader>
      </MRow>
    </div>
  </div>
</template>

<script>
import Bus from '@utils/emitter'
import CorrelatedTopology from '../correlated-topology.vue'

// import Constants from '@constants'
import { WidgetTypeConstants } from '@components/widgets/constants'
import { buildWidgetContext, makeCounter } from '@utils/socket-event-as-api'
import buildWidgetResult from '@components/widgets/result-builder'
import MonitorName from '@components/widgets/views/grid/view-more/monitor-name.vue'
import Severity from '@components/severity.vue'
import AlertDrilldown from '@components/widgets/views/grid/view-more/alert-drilldown.vue'
import datetime from '@src/filters/datetime'
import { generateId } from '@utils/id'

export default {
  name: 'CorrelatedOverview',
  components: {
    CorrelatedTopology,
    MonitorName,
    Severity,
    AlertDrilldown,
  },
  props: {
    view: {
      type: String,
      default: undefined,
    },
    timeline: {
      type: Object,
      required: true,
    },
    monitor: {
      type: Object,
      required: true,
    },
    alert: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      data: {},
      loading: true,
      guid: generateId(),
      searchTerm: undefined,
    }
  },
  computed: {
    columns() {
      return [
        {
          key: 'policy_name',
          name: 'Alert',
          searchable: true,
          sortable: true,
        },
        {
          key: 'policy_id',
          name: 'Alert ID',
          searchable: true,
          sortable: true,
        },
        {
          key: 'policy_type',
          name: 'Type',
          searchable: true,
          sortable: true,
        },
        {
          key: 'object_id',
          name: 'Monitor',
          searchable: true,
          sortable: true,
        },
        {
          key: 'policy_first_trigger_tick',
          name: 'First Triggered at',
          searchable: true,
          sortable: true,
        },
      ]
    },
    rows() {
      return this.data.rows || []
    },
    policyWidget() {
      let counters = [
        'severity',
        'policy.name',
        'instance',
        'policy.type',
        'policy.id',
        'metric',
        'value',
        'object.id',
        'duration',
        'policy.first.trigger.tick',
      ]
      return buildWidgetContext({
        groupCategory: 'metric',
        groupType: 'policy.stream',
        timeline: {
          selectedKey: 'today',
        },
        widgetType: WidgetTypeConstants.ACTIVE_ALERT,
        category: WidgetTypeConstants.GRID,
        counters: counters.map((c) =>
          makeCounter(c, 'last', 'Monitor', [this.monitor.id])
        ),
      })
        .setWidgetProperties({
          searchable: true,
          columnSettings: [
            {
              name: 'policy.name',
              displayName: 'Policy Name',
              type: 'alert',
              orderIndex: 1,
              style: {
                'width.percent': 10,
                'color.conditions': [],
              },
            },
            {
              name: 'policy.type',
              displayName: 'Policy Type',
              hidden: true,
              orderIndex: 2,
            },
            {
              name: 'object.id',
              displayName: 'Monitor',
              hidden: true,
              selectable: false,
              type: 'monitor',
              orderIndex: 3,
            },
            {
              name: 'instance',
              displayName: 'Instance',
              selectable: false,
              orderIndex: 4,
            },
            {
              name: 'severity',
              hidden: true,
              selectable: false,
              orderIndex: 5,
            },
            {
              name: 'metric',
              displayName: 'Metric',
              disable: false,
              orderIndex: 6,
            },
            {
              name: 'value',
              displayName: ' Value',
              disable: false,
              orderIndex: 7,
            },
            {
              name: 'duration',
              displayName: 'Duration',
              disable: false,
              orderIndex: 8,
            },
            {
              name: 'policy.id',
              displayName: 'Policy Id',
              hidden: true,
              disable: false,
              orderIndex: 9,
            },
          ],
        })
        .appendToGroup('policy.stream', {
          target: {
            entityType: 'monitor',
            entities: [this.monitor.id],
          },
        })
    },
  },
  created() {
    Bus.$on(this.$constants.UI_WIDGET_RESULT_EVENT, this.handleReceiveData)
    this.$once('hook:beforeDestroy', () => {
      Bus.$off(this.$constants.UI_WIDGET_RESULT_EVENT, this.handleReceiveData)
    })

    this.requestData()
  },
  methods: {
    formatDateTime(value) {
      return datetime(Math.round(value / 1000))
    },
    requestData() {
      this.loading = true
      let widget = this.policyWidget.generateWidgetDefinition()
      widget['visualization.data.sources'][0] = {
        ...widget['visualization.data.sources'][0],
        'correlation.root.object': this.monitor.id,
        instance: this.alert.instance,
        metric: this.alert.counterRawName,
      }
      widget.id = -1
      widget[this.$constants.UI_EVENT_UUID] = this.guid
      Bus.$emit('server:event', {
        'event.type': this.$constants.UI_WIDGET_RESULT_EVENT,
        'event.context': widget,
      })
    },
    async handleReceiveData(response) {
      if (response[this.$constants.UI_EVENT_UUID] !== this.guid) {
        return
      }
      const result = await buildWidgetResult(
        this.policyWidget.getContext(),
        response
      )
      this.data = Object.freeze(result)

      this.loading = false
    },
  },
}
</script>
<style lang="less" scoped>
.correlated-details {
  font-size: 0.8rem;
  font-weight: 500;
  color: var(--neutral-regular);
}
</style>
