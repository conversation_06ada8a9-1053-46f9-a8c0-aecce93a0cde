<template>
  <AgentProvider>
    <div class="min-h-0 flex flex-col flex-1">
      <div class="flex flex-col min-h-0 flex-1 w-full mt-2 px-2">
        <!-- <MRow>
          <MCol :size="12" class="text-left">
            <span class="flex">
              <Severity :severity="alert.severity" class="mr-2" />
              <span class="text" :class="alert.severity.toLowerCase()">
                {{ alert.severity }} :
              </span>
              <span class="ml-2" v-html="alert.message" />
            </span>
          </MCol>
        </MRow> -->
        <MRow class="vue-grid-item rounded py-6" :gutter="0">
          <MCol :size="3" class="pl-10">
            <table class="item-list-table with-out-border">
              <colgroup>
                <col style="width: 150px" />
                <col />
              </colgroup>
              <tbody>
                <tr>
                  <td class="text-neutral px-4 pt-2 pb-2">
                    <span class="text-ellipsis metric-details">Path</span>
                  </td>
                  <td
                    class="text-ellipsis px-4 pt-2 pb-2"
                    style="font-size: 0.8rem"
                  >
                    {{ netroute.pathName }}
                  </td>
                </tr>
                <tr>
                  <td class="text-neutral px-4 pt-2 pb-2">
                    <span class="text-ellipsis metric-details">Source</span>
                  </td>
                  <td
                    class="text-ellipsis px-4 pt-2 pb-2"
                    style="font-size: 0.8rem"
                    ><AgentPicker :value="netroute.source" disabled text-only
                  /></td>
                </tr>
                <tr>
                  <td class="text-neutral px-4 pt-2 pb-2">
                    <span class="text-ellipsis metric-details"
                      >Destination</span
                    >
                  </td>
                  <td
                    class="px-4 pt-2 pb-2 text-ellipsis"
                    style="font-size: 0.8rem"
                    >{{ netroute.destination }}</td
                  >
                </tr>
                <tr>
                  <td class="text-neutral px-4 pt-2 pb-2">
                    <span class="text-ellipsis metric-details"
                      >Policy Evaluation Type</span
                    >
                  </td>
                  <td
                    class="px-4 pt-2 pb-2 text-ellipsis"
                    style="font-size: 0.8rem"
                    >{{
                      (policyContext['policy.context'] || {})[
                        'policy.evaluation.type'
                      ] === $constants.HOP_TO_HOP
                        ? 'Hop To Hop'
                        : 'Source To Destination'
                    }}</td
                  >
                </tr>
              </tbody>
            </table>
          </MCol>
          <MCol :size="3" class="pl-10 border-left">
            <table class="item-list-table with-out-border">
              <colgroup>
                <col style="width: 150px" />
                <col />
              </colgroup>
              <tbody>
                <tr>
                  <td class="px-4 pt-2 pb-2 pl-6">
                    <span class="text-ellipsis metric-details"
                      >NetRoute Tag</span
                    >
                  </td>
                  <td
                    class="text-ellipsis px-4 pt-2 pb-2"
                    style="font-size: 0.8rem"
                  >
                    <LooseTags :value="netroute.tags || []" disabled />
                  </td>
                </tr>
                <tr>
                  <td class="px-4 pt-2 pb-2">
                    <span class="text-ellipsis metric-details">Tag</span>
                  </td>
                  <td
                    class="text-ellipsis px-4 pt-2 pb-2"
                    style="font-size: 0.8rem"
                  >
                    <LooseTags
                      v-if="(alert.tag || []).length"
                      :value="alert.tag || []"
                      :max-items="2"
                      disabled
                  /></td>
                </tr>
                <tr>
                  <td class="px-4 pt-2 pb-2">
                    <span class="text-ellipsis metric-details">Alert ID</span>
                  </td>
                  <td
                    class="text-ellipsis px-4 pt-2 pb-2"
                    style="font-size: 0.8rem"
                    >{{ alert.policyId }}</td
                  >
                </tr>
                <tr>
                  <td class="px-4 pt-2 pb-2">
                    <span class="text-ellipsis metric-details">Alert Type</span>
                  </td>
                  <td
                    class="text-ellipsis px-4 pt-2 pb-2"
                    style="font-size: 0.8rem"
                    >{{ alert.policyType }}</td
                  >
                </tr>
              </tbody>
            </table>
          </MCol>
          <MCol :size="3" class="pl-10 border-left">
            <table class="item-list-table with-out-border">
              <colgroup>
                <col style="width: 150px" />
                <col />
              </colgroup>
              <tbody>
                <!-- <tr>
                  <td class="px-4 pt-2 pb-2">
                    <span class="text-neutral">Ticket</span>
                  </td>
                  <td class="text-ellipsis px-4 pt-2 pb-2">{{ alert.ticket }}</td>
                </tr> -->
                <tr>
                  <td class="text-neutral px-4 pt-2 pb-2">
                    <span class="text-ellipsis metric-details">Metric</span>
                  </td>
                  <td
                    class="text-ellipsis px-4 pt-2 pb-2"
                    style="font-size: 0.8rem"
                    >{{ alert.metric }}</td
                  >
                </tr>
                <tr v-if="shouldShowTriggerCondition">
                  <td class="text-neutral px-4 pt-2 pb-2">
                    <span class="text-ellipsis metric-details"
                      >Trigger Condition</span
                    >
                  </td>
                  <td
                    class="text-ellipsis px-4 pt-2 pb-2"
                    style="font-size: 0.8rem"
                    :title="triggerCondition"
                    >{{ triggerCondition }}</td
                  >
                </tr>
                <tr>
                  <td class="text-neutral px-4 pt-2 pb-2">
                    <span class="text-ellipsis metric-details">First seen</span>
                  </td>
                  <td
                    class="text-ellipsis px-4 pt-2 pb-2"
                    style="font-size: 0.8rem"
                  >
                    {{ formatDateTime(alert.firstSeen) }}
                  </td>
                </tr>
                <tr v-if="alert.groupCategory === 'netroute.event'">
                  <td class="text-neutral px-4 pt-2 pb-2">
                    <span class="text-ellipsis metric-details">Last seen</span>
                  </td>
                  <td
                    class="text-ellipsis px-4 pt-2 pb-2"
                    style="font-size: 0.8rem"
                  >
                    {{ formatDateTime(alert.netRouteLastSeen) }}
                  </td>
                </tr>
              </tbody>
            </table>
          </MCol>
          <MCol :size="3" class="pl-10 border-left">
            <table class="item-list-table with-out-border">
              <colgroup>
                <col style="width: 150px" />
                <col />
              </colgroup>
              <tbody>
                <tr v-if="alert.groupCategory !== 'netroute.event'">
                  <td class="px-4 pt-2 pb-2">
                    <span class="text-ellipsis metric-details"
                      >Active Since</span
                    >
                  </td>
                  <td
                    class="text-ellipsis px-4 pt-2 pb-2"
                    style="font-size: 0.8rem"
                    >{{ alert.duration | duration }}</td
                  >
                </tr>
                <tr>
                  <td class="px-4 pt-2 pb-2">
                    <span class="text-ellipsis metric-details">Flap Count</span>
                  </td>
                  <td
                    class="text-ellipsis px-4 pt-2 pb-2"
                    style="font-size: 0.8rem"
                    >{{ isHopToHop ? alert.count : history.length }}</td
                  >
                </tr>
                <!-- <tr>
                  <td class="text-neutral px-4 pt-2 pb-2">
                    <span class="text-ellipsis metric-details">Acknowledge </span>
                  </td>
                  <td class="text-ellipsis px-4 pt-2 pb-2">
                    <MIcon
                      v-if="alert.acknowledged"
                      class="mr-1 text-secondary-green"
                      size="lg"
                      name="check-circle"
                      style="font-size: 0.8rem"
                    />
                  </td>
                </tr> -->
                <tr v-if="alert.groupCategory !== 'netroute.event'">
                  <td class="text-neutral px-4 pt-2 pb-2">
                    <span class="text-ellipsis metric-details"
                      >Acknowledge By</span
                    >
                  </td>
                  <td
                    class="text-ellipsis px-4 pt-2 pb-2"
                    style="font-size: 0.8rem"
                  >
                    <template v-if="alert.acknowledged">
                      {{ alert.acknowledgedByUser || alert.acknowledgedBy }}
                    </template>
                  </td>
                </tr>
              </tbody>
            </table>
          </MCol>
        </MRow>
        <div class="flex flex-1 flex-col min-h-0">
          <MRow :gutter="8">
            <!-- <MCol :size="3" style="padding-right: 0.5rem">
              <div
                class="vue-grid-item mt-2 text-center rounded flex items-center justify-center"
                style="height: 320px"
              >
                <div v-if="view !== 'flap'" class="flex-col pt-4">
                  <span class="mt-10 w-full text-ellipsis metric-details"
                    >Active Since</span
                  >
                  <h3>{{ alert.duration | duration }}</h3>
                  <MDivider />
                  <span class="text-ellipsis metric-details">
                    Triggered in {{ timeRangeWords }}
                  </span>
                  <h3 class="mt-3 pb-2">{{ alert.count }}</h3>
                </div>
                <div v-else class="flex-col pt-4">
                  <span class="text-ellipsis metric-details">
                    Flap count in {{ timeRangeWords }}
                  </span>
                  <h1 class="mt-3 pb-2 text-5xl">{{ alert.count }}</h1>
                </div>
              </div>
            </MCol> -->
            <MCol v-if="!isHopToHop" :size="isAvailabilityPolicy ? 6 : 12">
              <div
                class="flex flex-col vue-grid-item mt-2 rounded"
                style="height: 360px"
              >
                <small class="font-500 mx-2 my-2">Alert History</small>
                <div
                  class="w-full my-2"
                  style="padding-right: 0.5rem; padding-left: 3rem"
                >
                  <IncrementalResultProvider
                    ref="historyLineResultProviderRef"
                    :serverside-widget-defination="historyLineWidget"
                    :timeline="timeline"
                    @patchRecived="patchRecivedForHistoryLine"
                  >
                    <SegmentedDataLine
                      v-if="lineHistoryData.length"
                      :data="lineHistoryData"
                      :is-data-received="isDataRecived"
                    />
                  </IncrementalResultProvider>
                </div>
                <div
                  :style="{ width: `${isAvailabilityPolicy ? 60 : 100}%` }"
                  class="self-center flex"
                  style="flex-shrink: 0; height: 80%"
                >
                  <WidgetContainer
                    :widget="
                      isAvailabilityPolicy ? availabilityChart : lineChart
                    "
                    :availability-colors="isAvailabilityPolicy"
                    is-preview
                    :height="280"
                    watch-widget
                    :use-initial-request="isAvailabilityPolicy"
                  />
                </div>
              </div>
            </MCol>
            <MCol :size="6">
              <div
                class="vue-grid-item mt-2 mr-2 rounded flex-1 flex flex-col min-h-0 h-full w-full"
                :style="{ height: `${isAvailabilityPolicy ? 360 : 300}px` }"
              >
                <IncrementalResultProvider
                  ref="alertHistoryResultProviderRef"
                  :serverside-widget-defination="alertHistoryWidget"
                  :timeline="timeline"
                  @patchRecived="alertHistoryPatchRecived"
                >
                  <FlotoContentLoader :loading="!isHistoryDataRecived">
                    <Timeline
                      :items="history"
                      :active-item="
                        !isAvailabilityPolicy ? activeItem : undefined
                      "
                      @click="activeItem = $event"
                    >
                      <template v-slot:title>
                        <small class="font-500 mx-2 my-2">History</small>
                      </template>
                      <template v-slot:actions="{ item }">
                        <a
                          v-if="
                            !((item.text || '').toLowerCase() || '').includes(
                              'manual clear'
                            )
                          "
                          :href="buildRouteForHistroy(item)"
                          class="hover-action"
                          target="_blank"
                        >
                          <MIcon
                            name="external-link"
                            class="ml-1 text-primary"
                          />
                        </a>
                      </template>
                    </Timeline>
                  </FlotoContentLoader>
                </IncrementalResultProvider>
              </div>
            </MCol>
            <MCol v-if="!isAvailabilityPolicy" :size="6">
              <div
                class="vue-grid-item mt-2 rounded flex flex-col min-h-0 flex-1 w-full"
                style="height: 300px"
              >
                <FlotoContentLoader :loading="!isHistoryDataRecived">
                  <template v-if="activeItem">
                    <small class="font-500 mx-2 my-2">
                      {{ isHopToHop ? 'Path' : 'Metric Trend' }}
                    </small>
                    <NetrouteHopToHopResult
                      v-if="isHopToHop"
                      :key="activeItem.id"
                      :result-item="activeItem"
                    />
                    <AlertTrendChart
                      v-else
                      :key="activeItem.id"
                      :alert="alert"
                      :timeline="timeLineForAlertTrend"
                      :view="view"
                      :use-bands="!isAvailabilityPolicy"
                      :plot-bands="plotBands"
                      :active-item="activeItem"
                    />
                  </template>
                  <FlotoNoData
                    v-else
                    hide-svg
                    header-tag="h5"
                    icon="exclamation-triangle"
                    variant="neutral"
                  />
                </FlotoContentLoader>
              </div>
            </MCol>
          </MRow>
        </div>
      </div>
    </div>
  </AgentProvider>
</template>
<script>
import AgentProvider from '@components/data-provider/agent-provider.vue'
import AgentPicker from '@components/data-picker/agent-picker.vue'
import Config from '../../config'
import IsEmpty from 'lodash/isEmpty'

import { generateId } from '@utils/id'
// import CloneDeep from 'lodash/cloneDeep'
import Bus from '@utils/emitter'
// import Severity from '@components/severity.vue'
import Timeline from '@components/timeline.vue'
// import Constants from '@constants'

import Moment from 'moment'

import {
  AVAILABLE_RANGE_OPTIONS,
  WidgetTypeConstants,
  TIME_FORMAT,
} from '@components/widgets/constants'
import WidgetContainer from '@components/widgets/views/container.vue'
import { buildWidgetContext, makeCounter } from '@utils/socket-event-as-api'
import AlertTrendChart from '../alert-trend-chart.vue'
import {
  // getSingleAlertCount,
  getUserNameFromId,
  triggerCondition,
  makePlotBands,
  transformAlertHistoryDataForClient,
  getNetRouteHistoryData,
  getNetRouteHopToHopHistoryData,
} from '../../helpers/alert-helper'
import datetime from '@src/filters/datetime'
import { getPolicyApi } from '@modules/settings/policy-settings/policy-api.js'
import SegmentedDataLine from './segmented-data-line.vue'
import LooseTags from '@components/loose-tags.vue'
import IncrementalResultProvider from '@components/data-provider/incremental-result-provider.vue'
import { convertTimeLineForServer } from '@/src/components/widgets/helper'
import { fetchNetRouteApi } from '@/src/modules/netroute/netroute-api'
import NetrouteHopToHopResult from './netroute-hop-to-hop-result.vue'

// const SEVERITY_TO_CONSIDER = [
//   Constants.DOWN,
//   Constants.UNREACHABLE,
//   Constants.CRITICAL,
//   Constants.MAJOR,
//   Constants.WARNING,
//   Constants.CLEAR,
// ]

export default {
  name: 'NetrouteOverview',
  components: {
    // Severity,
    WidgetContainer,
    Timeline,
    AlertTrendChart,
    SegmentedDataLine,
    LooseTags,
    IncrementalResultProvider,
    AgentProvider,
    AgentPicker,
    NetrouteHopToHopResult,
  },
  inject: {
    SocketContext: { default: {} },
    counterContext: { default: { options: new Map() } },
  },
  props: {
    view: {
      type: String,
      default: undefined,
    },
    timeline: {
      type: Object,
      required: true,
    },
    alert: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      isDataLoaded: false,
      guid: generateId(),
      overviewChartSeries: [],
      history: [],
      triggerCondition: '',
      activeItem: null,
      lineHistoryData: [],
      isDataRecived: false,
      incrementalResult: [],
      policySeverity: undefined,
      isHistoryDataRecived: false,
      policyContext: {},
      netroute: {},
    }
  },
  computed: {
    isHopToHop() {
      return this.alert.groupCategory === 'netroute.event'
    },
    trendCountWidget() {
      const alert = this.alert
      const timeline = this.timeline
      return (
        buildWidgetContext({
          groupCategory: alert.groupCategory,
          category: WidgetTypeConstants.CHART,
          timeline,
          widgetType: WidgetTypeConstants.STACKED_VERTICAL_BAR,
          groupType: 'policy',
          resultBy: ['severity'],
          counters: [
            makeCounter('severity', 'count', 'Monitor', [alert.monitor]),
          ],
          ...(alert.instance
            ? {
                preFilters: {
                  condition: 'and',
                  inclusion: 'include',
                  conditions: [
                    {
                      operand: 'instance',
                      operator: '=',
                      value: alert.instance,
                    },
                  ],
                },
              }
            : {}),
        })
          .appendToGroup('policy', {
            alertIds: [alert.policyId],
            target: {
              entityType: 'Monitor',
              entities: [alert.monitor],
            },
            // severity: SEVERITY_TO_CONSIDER,
          })
          .setWidgetProperties({
            styleSetting: {
              chartOptions: {
                yAxis: { allowDecimals: false },
              },
            },
          })
          // .setWidgetProperties({
          //   styleSetting: {
          //     chartOptions: {
          //       colors: ['#f04e3e'],
          //       yAxis: {
          //         allowDecimal: false,
          //       },
          //       plotOptions: {
          //         series: {
          //           dataLabels: {
          //             enabled: true,
          //             verticalAlign: 'top',
          //             align: 'center',
          //             overflow: 'none',
          //             inside: true,
          //             color: 'white',
          //             style: {
          //               fontWeight: 'bold',
          //               fontSize: '14px',
          //             },
          //           },
          //         },
          //       },
          //     },
          //   },
          // })
          .getContext()
      )
    },
    timeRangeWords() {
      const timeline = this.timeline
      return (
        AVAILABLE_RANGE_OPTIONS.find(
          (c) => (timeline || {}).selectedKey === c.key
        ) || {}
      ).text
    },
    isAnomalyPolicy() {
      return (
        this.alert.policyType &&
        this.alert.policyType.toLowerCase().includes('anomaly')
      )
    },
    shouldShowTriggerCondition() {
      return (
        (this.alert.severity || '')?.toLowerCase() !== 'clear' &&
        this.alert.policyType?.toLowerCase() !== 'availability'
      )
    },
    lineChart() {
      const timeline = this.timeline
      const alert = this.alert
      return buildWidgetContext({
        groupType: 'netroute.metric',
        category: WidgetTypeConstants.CHART,

        widgetType: this.isAvailabilityPolicy
          ? WidgetTypeConstants.PIE
          : WidgetTypeConstants.LINE,
        counters: [
          makeCounter(this.alert.counterRawName, 'avg', 'NetRoute', [
            alert.netroute_id,
          ]),
        ],
        timeline,
        ...(this.isAvailabilityPolicy ? { resultBy: ['monitor'] } : {}),
      })
        .setWidgetProperties({
          styleSetting: {
            chartOptions: {
              yAxis: {
                allowDecimals: false,
                plotBands: this.plotBands,
              },
            },
          },
        })
        .getContext()
    },
    timeLineForAlertTrend() {
      if (this.activeItem && !this.isAvailabilityPolicy) {
        return {
          startDate:
            Moment.unix(this.activeItem.time).subtract(5, 'minutes').unix() *
            1000,
          endDate:
            Moment.unix(this.activeItem.time).add(5, 'minutes').unix() * 1000,
          startTime: Moment.unix(this.activeItem.time)
            .subtract(5, 'minutes')
            .format(TIME_FORMAT),
          endTime: Moment.unix(this.activeItem.time)
            .add(5, 'minutes')
            .format(TIME_FORMAT),
          selectedKey: 'custom',
          dailyRollingData: false,
        }
      }

      return this.timeline
    },
    plotBands() {
      return makePlotBands(
        this?.alert.policySeverity || this.policySeverity || {}
      )
    },
    isAvailabilityPolicy() {
      return this.alert.metric === 'status'
    },

    availabilityChart() {
      const alert = this.alert
      const timeline = this.timeline
      return buildWidgetContext({
        groupType: 'netroute.availability',
        timeline,
        category: WidgetTypeConstants.CUSTOM,
        widgetType: WidgetTypeConstants.PIE,
        resultBy: ['monitor'],
        counters: [
          makeCounter(
            'netroute.downtime.percent',
            'avg',
            'NetRoute',
            alert.netroute_id
          ),
          makeCounter(
            'netroute.uptime.percent',
            'avg',
            'NetRoute',
            alert.netroute_id
          ),
        ],
      })
        .setWidgetProperties({
          styleSetting: {
            legendEnabled: true,
          },
        })
        .getContext()
    },

    alertHistoryWidget() {
      if (this.isHopToHop) {
        return getNetRouteHopToHopHistoryData(
          this.alert,
          this.timeline,
          false,
          true
        )
      }
      return getNetRouteHistoryData(this.alert, this.timeline, false, true)
    },
    historyLineWidget() {
      if (this.isHopToHop) {
        return getNetRouteHopToHopHistoryData(
          this.alert,
          this.timeline,
          true,
          true
        )
      }
      return getNetRouteHistoryData(this.alert, this.timeline, true, true)
    },
  },
  watch: {
    timeline(newValue, oldValue) {
      if (newValue !== oldValue) {
        if (this.$refs.historyLineResultProviderRef) {
          this.$refs.historyLineResultProviderRef.setGuid()
        }
        if (this.$refs.alertHistoryResultProviderRef) {
          this.$refs.alertHistoryResultProviderRef.setGuid()
        }

        this.requestData()
        this.requestIncrementalData()
      }
    },
  },
  async created() {
    Bus.$on('socket:connected', this.requestData)

    Bus.$on('ui.action.policy.active.flap.get', this.onReceivedCacheData)

    this.$once('hook:beforeDestroy', () => {
      Bus.$off('ui.action.policy.active.flap.get', this.onReceivedCacheData)
    })
    if (this.SocketContext.connected) {
      this.requestData()
    }

    await this.assembleTriggerConditionForAlert()
  },
  methods: {
    buildRouteForHistroy(item) {
      return `/netroute/netroute-graph/${
        this.netroute.id
      }?t=${encodeURIComponent(btoa(JSON.stringify(this.timeline)))}&c=${
        item.time * 1000
      }`
    },
    async requestData() {
      this.isDataLoaded = false

      // getSingleAlertCount(
      //   this.alert,
      //   this.timeline,
      //   this.view,
      //   alert.monitor
      // ).then(async (data) => {
      //   let userName
      //   if (this.alert.acknowledged && this.alert.acknowledgedBy) {
      //     userName = await getUserNameFromId(this.alert.acknowledgedBy)
      //   }
      //   if (data.length) {
      //     this.$emit('update:alert', {
      //       ...this.alert,
      //       count: data[0]['severity.count'],
      //       acknowledgedByUser: userName,
      //     })
      //   } else {
      //     this.$emit('update:alert', {
      //       ...this.alert,
      //       count: 0,
      //       acknowledgedByUser: userName,
      //     })
      //   }
      // })

      this.netroute = await fetchNetRouteApi(this.alert.netroute_id)

      let userName
      if (this.alert.acknowledged && this.alert.acknowledgedBy) {
        userName = await getUserNameFromId(this.alert.acknowledgedBy)
        this.$emit('update:alert', {
          ...this.alert,
          acknowledgedByUser: userName,
        })
      }
      // this.requestAlertHistoryData()
      // this.requestAlertHistoryDataForHistoryLine()
    },
    formatDateTime(value) {
      return datetime(Math.round(value / 1000))
    },
    async assembleTriggerConditionForAlert() {
      this.policyContext = await getPolicyApi(
        this.alert.policyId,
        'netroute',
        null,
        true,
        false,
        false
      )
      // if (this.alert.triggerCondition) {
      //   this.triggerCondition = this.alert.triggerCondition
      // } else {
      this.triggerCondition = triggerCondition(
        this.alert['policyType'],
        this.policyContext?.['policy.context']?.['policy.severity'],
        this.alert['value'],
        this.alert['policyThreshold'],
        this.alert['severity'],
        this.alert['metric']?.replace(/[~^]/g, '.'),
        this.policyContext
      )
      // }

      if (this.alert.policySeverity) {
        this.policySeverity = this.alert.policySeverity
      } else {
        this.policySeverity =
          this.policyContext?.['policy.context']?.['policy.severity']
      }
    },
    // requestAlertHistoryData() {
    //   getAlertHistoryData(this.alert, this.timeline).then((history) => {
    //     this.history = Object.freeze(history)
    //     this.activeItem = Object.freeze(CloneDeep(history[0]))

    //     this.$emit('update:alert', {
    //       ...this.alert,
    //       count: (history || []).length,
    //     })
    //   })
    // },
    // requestAlertHistoryDataForHistoryLine() {
    //   this.isDataRecived = false
    //   getAlertHistoryData(this.alert, this.timeline, true).then((history) => {
    //     this.lineHistoryData = Object.freeze(history)
    //     this.isDataRecived = true
    //   })
    // },
    alertHistoryPatchRecived(data) {
      this.isHistoryDataRecived = true
      this.history = [
        ...this.history,
        ...(data || []).map((row) =>
          transformAlertHistoryDataForClient(
            row,
            this.alert,
            this.policyContext
          )
        ),
      ]
      this.activeItem = this.history[0]
    },
    patchRecivedForHistoryLine(data) {
      this.isDataRecived = true
      this.lineHistoryData = [
        ...this.lineHistoryData,
        ...(data || []).map((row) =>
          transformAlertHistoryDataForClient(row, this.alert)
        ),
      ]

      if (this.$refs.historyLineResultProviderRef) {
        let progress = this.$refs.historyLineResultProviderRef.getProgress()

        if (progress === 100) {
          this.getHistoryLineCacheData()
        }
      }
    },

    requestIncrementalData() {
      this.isDataRecived = false
      this.isHistoryDataRecived = false
      this.history = []
      this.lineHistoryData = []
      if (this.$refs.historyLineResultProviderRef) {
        this.$refs.historyLineResultProviderRef.requestData()
      }
      if (this.$refs.alertHistoryResultProviderRef) {
        this.$refs.alertHistoryResultProviderRef.requestData()
      }
    },
    getHistoryLineCacheData() {
      Bus.$emit('server:event', {
        'event.type': Config.UI_ACTION_POLICY_ACTIVE_FLAP_GET,
        'event.context': {
          'entity.id': this.alert.monitor,
          'policy.id': this.alert.policyId,
          metric: this.alert.counterRawName,
          'visualization.timeline': convertTimeLineForServer(this.timeline),

          ...(this.alert.instance
            ? {
                instance: this.alert.instance,
              }
            : {}),
        },
      })
    },
    onReceivedCacheData(event) {
      if (!IsEmpty(event.result)) {
        this.lineHistoryData = [
          transformAlertHistoryDataForClient(event.result, this.alert),

          ...this.lineHistoryData,
        ]
      }
    },
  },
}
</script>
<style lang="less" scoped>
.metric-details {
  font-size: 0.8rem;
  font-weight: 500;
  color: var(--neutral-regular);
}
</style>
