<template>
  <div class="h-full rounded flex flex-col">
    <FlotoContentLoader :loading="loading">
      <MRow class="mt-2">
        <MCol :size="6" class="flex">
          <span class="text-ellipsis log-flow-details mr-2">Alert Type:</span>

          <span class="flex flex-1 min-w-0">
            <span
              class="text-ellipsis"
              style="font-size: 0.8rem"
              :title="alert.alertType"
            >
              {{ alert.alertType }}</span
            >
          </span>
        </MCol>
        <MCol :size="6" class="flex">
          <span class="text-ellipsis log-flow-details mr-2"
            >Trigger Conditions:</span
          >

          <span class="flex flex-1 min-w-0">
            <span
              :title="item.text"
              class="text-ellipsis"
              style="font-size: 0.8rem"
            >
              {{ item.text }}</span
            >
          </span>
        </MCol>
      </MRow>
      <MRow class="flex mt-2">
        <MCol :size="6" class="flex">
          <span class="text-ellipsis log-flow-details mr-2 pt-2"
            >Result By:</span
          >

          <span class="flex flex-1 min-w-0">
            <span class="text-ellipsis" style="font-size: 0.8rem"
              ><SelectedItemPills :value="resultBy"
            /></span>
          </span>
        </MCol>
      </MRow>
      <div v-if="filters" class="flex w-full mt-2 mb-2" style="max-width: 100%">
        <FilterTrigger :filters="filters" placeholder="Filters" />
      </div>
      <div class="flex flex-1 flex-col min-h-0">
        <VirtualTable :columns="columns" :data="data.value">
          <template v-slot:value="{ item: row, props }">
            {{ row[`${props.field}_formatted`] || row[`${props.field}`] }}
          </template>
          <template v-slot:drilldown="{ item: row }">
            <LogFlowDrildown
              :item="row"
              :timeline="data.evaluationWindow"
              :category="category"
              :alert="alert"
              :policy="policy"
            />
          </template>
        </VirtualTable>
      </div>
    </FlotoContentLoader>
  </div>
</template>

<script>
import { generateId } from '@utils/id'
// import TimeRangePicker from '@components/widgets/time-range-picker.vue'
import VirtualTable from '@components/crud/virtual-table.vue'
import { getLogFlowAlertHistoryDetail } from '../../helpers/alert-helper'
import SelectedItemPills from '@/src/components/dropdown-trigger/selected-item-pills.vue'
import { isUnitConvertible } from '@/src/utils/unit-checker'
import applyUnit from '@/src/utils/unit-applier'
import LogFlowDrildown from './log-flow-drilldown.vue'
import FilterTrigger from '@components/filters/filter-trigger.vue'
import { FILTER_CONDITION_DEFAULT_DATA } from '@components/widgets/constants'

export default {
  name: 'LogFlowHistoryDetail',
  components: {
    // TimeRangePicker,
    VirtualTable,
    SelectedItemPills,
    LogFlowDrildown,
    FilterTrigger,
  },
  props: {
    alert: {
      type: Object,
      required: true,
    },
    item: {
      type: Object,
      required: true,
    },
    timeline: {
      type: Object,
      required: true,
    },
    policy: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      loading: true,
      data: {
        value: [],
        evaluationWindow: {},
      },
      firstRow: {},
    }
  },
  computed: {
    columns() {
      const columns = [
        ...(Object.keys(this.firstRow).filter((key) => key !== 'filters') ||
          ''),
        'drilldown',
      ]
      const resultBy = this.resultBy
      return columns.map((c) => ({
        name: c !== 'drilldown' ? c.replace(/[~^]/g, '.') : ' ',
        key: c.replace(/[~^\.]/g, '_'),
        ...(c !== 'drilldown'
          ? {
              searchable: true,
              sortable: true,
              ...(resultBy.includes(c) ? {} : { cellRender: 'value' }),
            }
          : {}),
      }))
    },
    resultBy() {
      const columns = Object.keys(this.firstRow)
      return columns.filter(
        (c) => c.indexOf('^') === -1 && !['filters'].includes(c)
      )
    },
    counterColumn() {
      const columns = Object.keys(this.firstRow)
      return (columns.find((c) => c.indexOf('^') >= 0) || '').replace(
        /[~^]/g,
        '.'
      )
    },
    category() {
      return this.alert.groupCategory
    },
    filters() {
      return this.policy?.conditions?.filters || FILTER_CONDITION_DEFAULT_DATA
    },
  },
  watch: {
    item(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.getDetail()
      }
    },
  },
  created() {
    this.getDetail()
  },
  methods: {
    getDetail() {
      getLogFlowAlertHistoryDetail(
        this.item,
        this.timeline,
        this.category
      ).then((data) => {
        const firstItem = Object.freeze((data.value.result || [])[0]) || {}
        this.firstRow = {
          ...firstItem,
          filters: this.generateFilterCondition(firstItem),
        }
        this.$nextTick(() => {
          const counter = this.counterColumn
          this.data = Object.freeze({
            ...data,
            value: (data.value.result || []).map((i) => {
              return Object.keys(i).reduce(
                (result, column) => {
                  return {
                    ...result,
                    [column.replace(/[~^\.]/g, '_')]: i[column],
                    ...(isUnitConvertible(counter)
                      ? {
                          [`${column.replace(/[~^\.]/g, '_')}_formatted`]:
                            applyUnit(counter, i[column]),
                        }
                      : {}),
                  }
                },
                {
                  filters: this.generateFilterCondition(i),
                }
              )
            }),
          })
          this.loading = false
        })
      })
    },

    generateFilterCondition(row) {
      return Object.keys(row).reduce(
        (acc, key) => [
          ...acc,
          ...(key.replace(/[~^]/g, '.') !== this.counterColumn
            ? [
                {
                  operator: '=',
                  operand: key,
                  value: row[key],
                  key: generateId(),
                },
              ]
            : []),
        ],
        []
      )
    },
  },
}
</script>
<style lang="less" scoped>
.log-flow-details {
  font-size: 0.8rem;
  font-weight: 500;
  color: var(--neutral-regular);
}
</style>
