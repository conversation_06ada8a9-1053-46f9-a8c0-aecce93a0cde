<template>
  <FlotoLink :to="drilldown">
    <MIcon name="eye" /> View in {{ historyDetailCategory }} Explorer
  </FlotoLink>
</template>

<script>
import Capitalize from 'lodash/capitalize'
import { generateId } from '@utils/id'
import { WidgetTypeConstants } from '@components/widgets/constants'
export default {
  name: 'LogFlowDrilldown',
  props: {
    item: {
      type: Object,
      required: true,
    },
    timeline: {
      type: Object,
      required: true,
    },
    category: {
      type: String,
      required: true,
    },
    alert: {
      type: Object,
      required: true,
    },
    policy: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {}
  },

  computed: {
    policyFiltergroups() {
      return this.policy?.conditions?.filters?.pre?.groups
    },

    numberOfRequiredGroups() {
      return Math.ceil(this.allConditions.length / 3)
    },

    allConditions() {
      let conditions = []
      // this.policyFiltergroups?.forEach((element) => {
      //   element.conditions.forEach((condition) => {
      //     if (condition.value && condition.operand && condition.operator) {
      //       conditions.push(condition)
      //     }
      //   })
      // })

      return [...conditions, ...this.item.filters]
    },

    drilldownFilter() {
      return {
        pre: {
          ...this.policy?.conditions?.filters?.pre,
        },
        drillDownFilters: {
          condition: 'and',
          groups: this.groups,
          inclusion: 'include',
        },
      }
    },
    groups() {
      const isEmptyFilter = (this.policyFiltergroups || []).find((g) =>
        (g.conditions || []).find((c) => !c.operator)
      )
      const groups = [...(isEmptyFilter ? [] : this.policyFiltergroups || [])]
      for (let index = 0; index < this.numberOfRequiredGroups; index++) {
        groups.push({
          condition: 'and',
          inclusion: 'include',
          key: generateId(),
          conditions: this.buildCondition(index),
        })
      }

      return groups
    },

    drilldown() {
      if (this.isLogDrillDown) {
        return this.$modules.getModuleRoute('log', 'log-search', {
          params: { id: this.alert.alertId },
          query: {
            t: encodeURIComponent(btoa(JSON.stringify(this.timeline))),
            filter: encodeURIComponent(
              btoa(JSON.stringify(this.drilldownFilter))
            ),
          },
        })
      } else {
        const policyConditions = this.policy?.conditions
        let data = {
          counters: [
            {
              aggrigateFn: policyConditions?.condition?.aggrigateFn,
            },
          ],
          resultBy: policyConditions?.resultBy,
          countersValue: [this.alert.metric],
          target: {
            entities: policyConditions?.entities,
            entityType: policyConditions?.entityType,
          },
          filters: this.drilldownFilter,
          chartType: WidgetTypeConstants.AREA,
        }
        return this.$modules.getModuleRoute('flow', 'explorer', {
          query: {
            flow: encodeURIComponent(btoa(JSON.stringify(data))),
            t: encodeURIComponent(btoa(JSON.stringify(this.timeline))),
          },
        })
      }
    },
    isLogDrillDown() {
      return this.category === 'log'
    },
    historyDetailCategory() {
      return Capitalize(this.category || '')
    },
  },

  methods: {
    buildCondition(index) {
      let to = index * 3
      let from = !index ? index + 3 : index * 3 + 3
      return this.allConditions.slice(to, from).filter(Boolean)
    },
  },
}
</script>
