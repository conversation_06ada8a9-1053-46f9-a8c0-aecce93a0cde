<template>
  <div class="min-h-0 flex flex-col flex-1">
    <div class="flex flex-col min-h-0 flex-1 w-full mt-2 pl-2">
      <!-- <MRow>
        <MCol :size="12" class="text-left">
          <span class="flex">
            <Severity :severity="alert.severity" class="mr-2" />
            <span class="text" :class="alert.severity.toLowerCase()">
              {{ alert.severity }} :
            </span>
            <span class="ml-2" v-html="alert.message" />
          </span>
        </MCol>
      </MRow> -->
      <MRow class="rounded" style="max-height: 50vh" :gutter="8">
        <MCol :size="4" class="h-full">
          <div class="vue-grid-item rounded flex flex-col h-full">
            <table class="item-list-table with-out-border">
              <colgroup>
                <col style="width: 150px" />
                <col />
              </colgroup>
              <tbody>
                <tr>
                  <td class="px-4 pt-2 pb-2">
                    <span class="text-ellipsis log-flow-details"
                      >Alert ID :</span
                    >
                  </td>
                  <td
                    class="text-ellipsis px-4 pt-2 pb-2"
                    style="font-size: 0.8rem"
                  >
                    {{ alert.policyId }}
                  </td>
                </tr>
                <tr>
                  <td class="text-neutral px-4 pt-2 pb-2">
                    <span class="text-ellipsis log-flow-details">Metric :</span>
                  </td>
                  <td
                    class="text-ellipsis px-4 pt-2 pb-2"
                    style="font-size: 0.8rem"
                  >
                    {{ alert.metric }}
                  </td>
                </tr>
                <tr v-if="(alert.severity || '').toLowerCase() !== 'clear'">
                  <td class="text-neutral px-4 pt-2 pb-2">
                    <span class="text-ellipsis log-flow-details">
                      Trigger Condition :
                    </span>
                  </td>
                  <td
                    class="text-ellipsis px-4 pt-2 pb-2"
                    style="font-size: 0.8rem"
                    >{{ alert.triggerCondition }}
                  </td>
                </tr>
                <tr>
                  <td class="text-neutral px-4 pt-2 pb-2">
                    <span class="text-ellipsis log-flow-details"
                      >Severity :</span
                    >
                  </td>
                  <td
                    class="text-ellipsis px-4 pt-2 pb-2 text"
                    :class="alert.severity.toLowerCase()"
                    style="font-size: 0.8rem"
                  >
                    {{ alert.severity }}
                  </td>
                </tr>
                <tr>
                  <td class="text-neutral px-4 pt-2 pb-2">
                    <span class="text-ellipsis log-flow-details">Count :</span>
                  </td>
                  <td
                    class="text-ellipsis px-4 pt-2 pb-2"
                    style="font-size: 0.8rem"
                  >
                    {{ alertCount }}
                  </td>
                </tr>
                <tr>
                  <td class="text-neutral px-4 pt-2 pb-2">
                    <span class="text-ellipsis log-flow-details"
                      >First seen :</span
                    >
                  </td>
                  <td
                    class="text-ellipsis px-4 pt-2 pb-2"
                    style="font-size: 0.8rem"
                  >
                    {{ formatDateTime(alert.firstSeen) }}
                  </td>
                </tr>
                <tr>
                  <td class="text-neutral px-4 pt-2 pb-2">
                    <span class="text-ellipsis log-flow-details"
                      >Last seen :</span
                    >
                  </td>
                  <td
                    class="text-ellipsis px-4 pt-2 pb-2"
                    style="font-size: 0.8rem"
                  >
                    {{ alert.lastSeen | datetime }}
                  </td>
                </tr>
                <!-- <tr>
                  <td class="text-neutral px-4 pt-2 pb-2">
                    <span class="text-ellipsis log-flow-details"
                      >Acknowledge :</span
                    >
                  </td>
                  <td class="text-ellipsis px-4 pt-2 pb-2">
                    <MIcon
                      v-if="alert.acknowledged"
                      class="mr-1 text-secondary-green"
                      size="lg"
                      name="check-circle"
                    />
                  </td>
                </tr>
                <tr>
                  <td class="text-neutral px-4 pt-2 pb-2">
                    <span class="text-ellipsis log-flow-details"
                      >Acknowledge By :</span
                    >
                  </td>
                  <td
                    class="text-ellipsis px-4 pt-2 pb-2"
                    style="font-size: 0.8rem"
                  >
                    <template v-if="alert.acknowledged">
                      {{ alert.acknowledgedByUser || alert.acknowledgedBy }}
                    </template>
                  </td>
                </tr> -->
              </tbody>
            </table>
          </div>
        </MCol>
        <MCol :size="4" class="h-full">
          <div class="vue-grid-item rounded flex flex-col h-full">
            <small class="font-500 mx-2 my-2">Alert Trend</small>
            <div
              class="flex flex-1 min-h-0 items-center justify-center flex-col w-full"
            >
              <AlertTrendChart
                :alert="alert"
                :aggrigator="trendChartAggrigator"
                :timeline="timeline"
              />
            </div>
          </div>
        </MCol>
        <MCol :size="4" class="h-full">
          <div class="vue-grid-item flex rounded flex-col h-full mr-2">
            <small class="font-500 mx-2 my-2">Alert Count</small>
            <div
              class="flex flex-1 min-h-0 items-center justify-center flex-col min-w-0"
            >
              <WidgetContainer
                :widget="trendCountWidget"
                is-preview
                watch-widget
                class="w-full"
              />
            </div>
          </div>
        </MCol>
      </MRow>
      <MRow class="flex-1 min-h-0 mt-2 mb-2" :gutter="8">
        <MCol :size="6">
          <div class="vue-grid-item flex rounded flex-col h-full px-2">
            <div class="flex flex-1 min-h-0">
              <div
                class="flex-1 flex flex-col min-h-0 h-full"
                style="max-height: 50vh"
              >
                <template v-if="history.length">
                  <h4 class="text-primary-alt font-500 mt-2">History</h4>
                  <Timeline
                    :items="history"
                    :active-item="activeItem"
                    ignore-severity
                    @click="activeItem = $event"
                  />
                </template>
                <FlotoNoData
                  v-else
                  hide-svg
                  header-tag="h5"
                  icon="exclamation-triangle"
                  variant="neutral"
                  message="No history found for selected timeline"
                />
              </div>
            </div>
          </div>
        </MCol>
        <MCol :size="6">
          <div
            v-if="activeItem"
            class="flex flex-1 flex-col min-h-0 h-full mr-2"
          >
            <div class="vue-grid-item flex rounded flex-col h-full px-2">
              <div class="flex flex-1 min-h-0">
                <div
                  class="flex-1 flex flex-col min-h-0 h-full w-full"
                  style="max-height: 50vh"
                >
                  <LogFlowHistoryDetail
                    :alert="alert"
                    :item="activeItem"
                    :timeline="timeLineForAlertTrend"
                    :policy="policy"
                  />
                </div>
              </div>
            </div>
          </div>
        </MCol>
      </MRow>
    </div>
  </div>
</template>
<script>
import Moment from 'moment'

import CloneDeep from 'lodash/cloneDeep'
import { generateId } from '@utils/id'
import Bus from '@utils/emitter'
// import Severity from '@components/severity.vue'
import Timeline from '@components/timeline.vue'
import {
  AVAILABLE_RANGE_OPTIONS,
  WidgetTypeConstants,
  TIME_FORMAT,
} from '@components/widgets/constants'
import WidgetContainer from '@components/widgets/views/container.vue'
import { buildWidgetContext, makeCounter } from '@utils/socket-event-as-api'
import AlertTrendChart from '../alert-trend-chart.vue'
import {
  getAlertHistoryData,
  // getSingleAlertCount,
  getUserNameFromId,
} from '../../helpers/alert-helper'
import LogFlowHistoryDetail from './log-flow-history-detail.vue'
import { getPolicyApi } from '@modules/settings/policy-settings/policy-api.js'
import datetime from '@src/filters/datetime'

export default {
  name: 'LogFlowOverview',
  components: {
    // Severity,
    WidgetContainer,
    Timeline,
    AlertTrendChart,
    LogFlowHistoryDetail,
  },
  inject: {
    SocketContext: { default: {} },
    counterContext: { default: { options: new Map() } },
  },
  props: {
    timeline: {
      type: Object,
      required: true,
    },
    alert: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      isDataLoaded: false,
      guid: generateId(),
      overviewChartSeries: [],
      history: [],
      activeItem: null,
      policy: {},
    }
  },
  computed: {
    alertCount() {
      return this.alert.count !== undefined && this.alert.count !== null
        ? this.alert.count
        : this.history.length
    },
    trendCountWidget() {
      const alert = this.alert
      const timeline = this.timeline
      return (
        buildWidgetContext({
          groupCategory: alert.groupCategory,
          category: WidgetTypeConstants.CHART,
          timeline,
          widgetType: WidgetTypeConstants.VERTICAL_BAR,
          groupType: 'policy',
          counters: [makeCounter('severity', 'count')],
          conditions: [
            ...(alert.instance
              ? [
                  {
                    operand: 'instance',
                    operator: '=',
                    value: alert.instance,
                  },
                ]
              : []),
          ],
          preFilters: {
            condition: 'and',
            inclusion: 'include',
            conditions: [
              { operand: 'policy.id', operator: '=', value: alert.policyId },
            ],
          },
        })
          .appendToGroup('policy', {
            alertIds: [+alert.policyId],
          })
          .setWidgetProperties({
            styleSetting: {
              chartOptions: {
                colors: [
                  this.alert.severity === this.$constants.WARNING
                    ? 'var(--severity-warning)'
                    : this.alert.severity === this.$constants.CRITICAL
                    ? 'var(--severity-critical)'
                    : 'var(--severity-major)',
                ],
                yAxis: { allowDecimals: false },
              },
            },
          })
          // .setWidgetProperties({
          //   styleSetting: {
          //     chartOptions: {
          //       colors: ['#f04e3e'],
          //       yAxis: {
          //         allowDecimal: false,
          //       },
          //       plotOptions: {
          //         series: {
          //           dataLabels: {
          //             enabled: true,
          //             verticalAlign: 'top',
          //             align: 'center',
          //             overflow: 'none',
          //             inside: true,
          //             color: 'white',
          //             style: {
          //               fontWeight: 'bold',
          //               fontSize: '14px',
          //             },
          //           },
          //         },
          //       },
          //     },
          //   },
          // })
          .getContext()
      )
    },
    trendChartAggrigator() {
      const alert = this.alert
      if (alert.counterRawName === 'status') {
        return 'avg'
      }
      const counter = this.counterContext.options.get(alert.counterRawName)
      if (counter) {
        return counter.dataType.includes('numeric') ? 'avg' : 'count'
      }
      return 'count'
    },
    timeRangeWords() {
      const timeline = this.timeline
      return (
        AVAILABLE_RANGE_OPTIONS.find((c) => timeline.selectedKey === c.key) ||
        {}
      ).text
    },
    timeLineForAlertTrend() {
      if (this.activeItem && !this.isAvailabilityPolicy) {
        return {
          startDate:
            Moment.unix(this.activeItem.time).subtract(5, 'minutes').unix() *
            1000,
          endDate:
            Moment.unix(this.activeItem.time).add(2, 'minutes').unix() * 1000,
          startTime: Moment.unix(this.activeItem.time)
            .subtract(5, 'minutes')
            .format(TIME_FORMAT),
          endTime: Moment.unix(this.activeItem.time)
            .add(2, 'minutes')
            .format(TIME_FORMAT),
          selectedKey: 'custom',
          dailyRollingData: false,
        }
      }

      return this.timeline
    },
  },
  watch: {
    timeline(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.requestData()
      }
    },
  },
  async created() {
    Bus.$on('socket:connected', this.requestData)

    this.$once('hook:beforeDestroy', () => {})
    if (this.SocketContext.connected) {
      this.requestData()
    }
    await this.getPolicy()
  },
  methods: {
    getPolicy() {
      getPolicyApi(this.alert?.policyId, this.alert?.groupCategory).then(
        (data) => {
          this.policy = data
        }
      )
    },
    async requestData() {
      this.isDataLoaded = false

      // getSingleAlertCount(this.alert, this.timeline).then(async (data) => {
      //   let userName
      //   if (this.alert.acknowledged && this.alert.acknowledgedBy) {
      //     userName = await getUserNameFromId(this.alert.acknowledgedBy)
      //   }
      //   if (data.length) {
      //     this.$emit('update:alert', {
      //       ...this.alert,
      //       count: data[0]['severity.count'],
      //       acknowledgedByUser: userName,
      //     })
      //   } else {
      //     this.$emit('update:alert', {
      //       ...this.alert,
      //       acknowledgedByUser: userName,
      //     })
      //   }
      // })

      let userName
      if (this.alert.acknowledged && this.alert.acknowledgedBy) {
        userName = await getUserNameFromId(this.alert.acknowledgedBy)

        this.$emit('update:alert', {
          ...this.alert,

          acknowledgedByUser: userName,
        })
      }
      getAlertHistoryData(this.alert, this.timeline).then((history) => {
        this.history = Object.freeze(history)
        this.activeItem = Object.freeze(CloneDeep(history[0]))
        // this.$emit('update:alert', {
        //   ...this.alert,
        //   count: (history || []).length,
        // })
        this.isDataLoaded = true
      })
    },
    formatDateTime(value) {
      return datetime(Math.round(value))
    },
  },
}
</script>
<style lang="less" scoped>
.log-flow-details {
  font-size: 0.8rem;
  font-weight: 500;
  color: var(--neutral-regular);
}
</style>
