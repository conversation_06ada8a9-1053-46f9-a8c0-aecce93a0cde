<template>
  <div class="min-h-0 flex flex-col flex-1">
    <div class="flex flex-col min-h-0 flex-1 w-full mt-2 px-2">
      <!-- <MRow>
        <MCol :size="12" class="text-left">
          <span class="flex">
            <Severity :severity="alert.severity" class="mr-2" />
            <span class="text" :class="alert.severity.toLowerCase()">
              {{ alert.severity }} :
            </span>
            <span class="ml-2" v-html="alert.message" />
          </span>
        </MCol>
      </MRow> -->
      <MRow class="vue-grid-item rounded py-6" :gutter="0">
        <MCol :size="3" class="pl-10">
          <table class="item-list-table with-out-border">
            <colgroup>
              <col style="width: 150px" />
              <col />
            </colgroup>
            <tbody>
              <tr>
                <td class="text-neutral px-4 pt-2 pb-2">
                  <span class="text-ellipsis metric-details">Monitor</span>
                </td>
                <td
                  class="text-ellipsis px-4 pt-2 pb-2"
                  style="font-size: 0.8rem"
                  >{{ monitor.name }}</td
                >
              </tr>
              <tr>
                <td class="text-neutral px-4 pt-2 pb-2">
                  <span class="text-ellipsis metric-details">IP</span>
                </td>
                <td
                  class="text-ellipsis px-4 pt-2 pb-2"
                  style="font-size: 0.8rem"
                  >{{ monitor.ip }}</td
                >
              </tr>
              <tr>
                <td class="text-neutral px-4 pt-2 pb-2">
                  <span class="text-ellipsis metric-details">Host</span>
                </td>
                <td
                  class="px-4 pt-2 pb-2 text-ellipsis"
                  style="font-size: 0.8rem"
                  >{{ monitor.host }}</td
                >
              </tr>
              <tr>
                <td class="text-neutral px-4 pt-2 pb-2">
                  <span class="text-ellipsis metric-details">Instance</span>
                </td>
                <td
                  class="px-4 pt-2 pb-2 text-ellipsis"
                  style="font-size: 0.8rem"
                  >{{ alert.instance }}</td
                >
              </tr>
            </tbody>
          </table>
        </MCol>
        <MCol :size="3" class="pl-10 border-left">
          <table class="item-list-table with-out-border">
            <colgroup>
              <col style="width: 150px" />
              <col />
            </colgroup>
            <tbody>
              <tr>
                <td class="px-4 pt-2 pb-2 pl-6">
                  <span class="text-ellipsis metric-details">Group</span>
                </td>
                <td
                  class="text-ellipsis px-4 pt-2 pb-2"
                  style="font-size: 0.8rem"
                >
                  <GroupPicker :value="monitor.groups" multiple disabled />
                </td>
              </tr>
              <tr>
                <td class="px-4 pt-2 pb-2">
                  <span class="text-ellipsis metric-details">Tag</span>
                </td>
                <td
                  class="text-ellipsis px-4 pt-2 pb-2"
                  style="font-size: 0.8rem"
                >
                  <LooseTags
                    v-if="(alert.tag || []).length"
                    :value="alert.tag || []"
                    :max-items="2"
                    disabled
                /></td>
              </tr>
              <tr>
                <td class="px-4 pt-2 pb-2">
                  <span class="text-ellipsis metric-details">Alert ID</span>
                </td>
                <td
                  class="text-ellipsis px-4 pt-2 pb-2"
                  style="font-size: 0.8rem"
                  >{{ alert.policyId }}</td
                >
              </tr>
              <tr>
                <td class="px-4 pt-2 pb-2">
                  <span class="text-ellipsis metric-details">Alert Type</span>
                </td>
                <td
                  class="text-ellipsis px-4 pt-2 pb-2"
                  style="font-size: 0.8rem"
                  >{{ alert.policyType }}</td
                >
              </tr>
            </tbody>
          </table>
        </MCol>
        <MCol :size="3" class="pl-10 border-left">
          <table class="item-list-table with-out-border">
            <colgroup>
              <col style="width: 150px" />
              <col />
            </colgroup>
            <tbody>
              <!-- <tr>
                <td class="px-4 pt-2 pb-2">
                  <span class="text-neutral">Ticket</span>
                </td>
                <td class="text-ellipsis px-4 pt-2 pb-2">{{ alert.ticket }}</td>
              </tr> -->
              <tr>
                <td class="text-neutral px-4 pt-2 pb-2">
                  <span class="text-ellipsis metric-details">Metric</span>
                </td>
                <td
                  class="text-ellipsis px-4 pt-2 pb-2"
                  style="font-size: 0.8rem"
                  >{{ alert.metric }}</td
                >
              </tr>
              <tr v-if="shouldShowTriggerCondition">
                <td class="text-neutral px-4 pt-2 pb-2">
                  <span class="text-ellipsis metric-details"
                    >Trigger Condition</span
                  >
                </td>
                <td
                  class="text-ellipsis px-4 pt-2 pb-2"
                  style="font-size: 0.8rem"
                  :title="triggerCondition"
                  >{{ triggerCondition }}</td
                >
              </tr>

              <tr>
                <td class="text-neutral px-4 pt-2 pb-2">
                  <span class="text-ellipsis metric-details">{{
                    isAnomalyPolicy ? 'Sample Violation' : 'Metric Value'
                  }}</span>
                </td>
                <td
                  class="px-4 pt-2 pb-2 text-ellipsis"
                  style="font-size: 0.8rem"
                  >{{
                    isAnomalyPolicy ? alert.policyThreshold : alert.value
                  }}</td
                >
              </tr>
              <tr>
                <td class="text-neutral px-4 pt-2 pb-2">
                  <span class="text-ellipsis metric-details">First seen</span>
                </td>
                <td
                  class="text-ellipsis px-4 pt-2 pb-2"
                  style="font-size: 0.8rem"
                >
                  {{ formatDateTime(alert.firstSeen) }}
                </td>
              </tr>
            </tbody>
          </table>
        </MCol>
        <MCol :size="3" class="pl-10 border-left">
          <table class="item-list-table with-out-border">
            <colgroup>
              <col style="width: 150px" />
              <col />
            </colgroup>
            <tbody>
              <tr>
                <td class="px-4 pt-2 pb-2">
                  <span class="text-ellipsis metric-details">Active Since</span>
                </td>
                <td
                  class="text-ellipsis px-4 pt-2 pb-2"
                  style="font-size: 0.8rem"
                  >{{ alert.duration | duration }}</td
                >
              </tr>
              <tr>
                <td class="px-4 pt-2 pb-2">
                  <span class="text-ellipsis metric-details">Flap Count</span>
                </td>
                <td
                  class="text-ellipsis px-4 pt-2 pb-2"
                  style="font-size: 0.8rem"
                  >{{ history.length }}</td
                >
              </tr>
              <!-- <tr>
                <td class="text-neutral px-4 pt-2 pb-2">
                  <span class="text-ellipsis metric-details">Acknowledge </span>
                </td>
                <td class="text-ellipsis px-4 pt-2 pb-2">
                  <MIcon
                    v-if="alert.acknowledged"
                    class="mr-1 text-secondary-green"
                    size="lg"
                    name="check-circle"
                    style="font-size: 0.8rem"
                  />
                </td>
              </tr> -->
              <tr>
                <td class="text-neutral px-4 pt-2 pb-2">
                  <span class="text-ellipsis metric-details"
                    >Acknowledge By</span
                  >
                </td>
                <td
                  class="text-ellipsis px-4 pt-2 pb-2"
                  style="font-size: 0.8rem"
                >
                  <template v-if="alert.acknowledged">
                    {{ alert.acknowledgedByUser || alert.acknowledgedBy }}
                  </template>
                </td>
              </tr>
            </tbody>
          </table>
        </MCol>
      </MRow>
      <div class="flex flex-1 flex-col min-h-0">
        <MRow :gutter="8">
          <!-- <MCol :size="3" style="padding-right: 0.5rem">
            <div
              class="vue-grid-item mt-2 text-center rounded flex items-center justify-center"
              style="height: 320px"
            >
              <div v-if="view !== 'flap'" class="flex-col pt-4">
                <span class="mt-10 w-full text-ellipsis metric-details"
                  >Active Since</span
                >
                <h3>{{ alert.duration | duration }}</h3>
                <MDivider />
                <span class="text-ellipsis metric-details">
                  Triggered in {{ timeRangeWords }}
                </span>
                <h3 class="mt-3 pb-2">{{ alert.count }}</h3>
              </div>
              <div v-else class="flex-col pt-4">
                <span class="text-ellipsis metric-details">
                  Flap count in {{ timeRangeWords }}
                </span>
                <h1 class="mt-3 pb-2 text-5xl">{{ alert.count }}</h1>
              </div>
            </div>
          </MCol> -->
          <MCol v-if="!isForecastPolicy" :size="isAvailabilityPolicy ? 6 : 12">
            <div
              class="flex flex-col vue-grid-item mt-2 rounded"
              style="height: 360px"
            >
              <small class="font-500 mx-2 my-2">Alert History</small>
              <div
                class="w-full my-2"
                style="padding-right: 0.5rem; padding-left: 3rem"
              >
                <IncrementalResultProvider
                  ref="historyLineResultProviderRef"
                  :serverside-widget-defination="historyLineWidget"
                  :timeline="timeline"
                  @patchRecived="patchReceivedForHistoryLine"
                >
                  <SegmentedDataLine
                    v-if="lineHistoryData.length"
                    :data="lineHistoryData"
                    :is-data-received="isDataReceived"
                  />
                </IncrementalResultProvider>
              </div>
              <div
                :style="{ width: `${isAvailabilityPolicy ? 60 : 100}%` }"
                class="self-center flex"
                style="flex-shrink: 0; height: 80%"
              >
                <WidgetContainer
                  :widget="isAvailabilityPolicy ? availabilityChart : lineChart"
                  :availability-colors="isAvailabilityPolicy"
                  is-preview
                  :height="280"
                  watch-widget
                  :use-initial-request="isAvailabilityPolicy"
                />
              </div>
            </div>
          </MCol>
          <MCol :size="6">
            <div
              class="vue-grid-item mt-2 mr-2 rounded flex-1 flex flex-col min-h-0 h-full w-full"
              :style="{ height: `${isAvailabilityPolicy ? 360 : 300}px` }"
            >
              <IncrementalResultProvider
                ref="alertHistoryResultProviderRef"
                :serverside-widget-defination="alertHistoryWidget"
                :timeline="timeline"
                @patchRecived="alertHistoryPatchReceived"
              >
                <FlotoContentLoader :loading="!isHistoryDataReceived">
                  <Timeline
                    :items="history"
                    :active-item="
                      !isAvailabilityPolicy ? activeItem : undefined
                    "
                    @click="activeItem = $event"
                  >
                    <template v-slot:title>
                      <small class="font-500 mx-2 my-2">History</small>
                    </template>
                  </Timeline>
                </FlotoContentLoader>
              </IncrementalResultProvider>
            </div>
          </MCol>
          <MCol v-if="!isAvailabilityPolicy" :size="6">
            <div
              class="vue-grid-item mt-2 rounded flex flex-col min-h-0 flex-1 w-full"
              style="height: 300px"
            >
              <FlotoContentLoader :loading="!isHistoryDataReceived">
                <template v-if="activeItem">
                  <small class="font-500 mx-2 my-2">Metric Trend</small>
                  <AlertTrendChart
                    v-if="shouldRenderChart"
                    :alert="alert"
                    :timeline="timeLineForAlertTrend"
                    :view="view"
                    :use-bands="!isAvailabilityPolicy"
                    :plot-bands="plotBands"
                    :active-item="activeItem"
                    :granularity="granularity"
                  />
                  <FlotoNoData
                    v-else
                    hide-svg
                    header-tag="h5"
                    icon="exclamation-triangle"
                    variant="neutral"
                  />
                </template>
                <FlotoNoData
                  v-else
                  hide-svg
                  header-tag="h5"
                  icon="exclamation-triangle"
                  variant="neutral"
                />
              </FlotoContentLoader>
            </div>
          </MCol>
        </MRow>
      </div>
    </div>
  </div>
</template>
<script>
import Config from '../../config'
import IsEmpty from 'lodash/isEmpty'

import { generateId } from '@utils/id'
// import CloneDeep from 'lodash/cloneDeep'
import Bus from '@utils/emitter'
// import Severity from '@components/severity.vue'
import Timeline from '@components/timeline.vue'
// import Constants from '@constants'

import Moment from 'moment'

import {
  AVAILABLE_RANGE_OPTIONS,
  WidgetTypeConstants,
  TIME_FORMAT,
} from '@components/widgets/constants'
import WidgetContainer from '@components/widgets/views/container.vue'
import { buildWidgetContext, makeCounter } from '@utils/socket-event-as-api'
import AlertTrendChart from '../alert-trend-chart.vue'
import {
  getAlertHistoryData,
  // getSingleAlertCount,
  getUserNameFromId,
  triggerCondition,
  makePlotBands,
  transformAlertHistoryDataForClient,
} from '../../helpers/alert-helper'
import datetime from '@src/filters/datetime'
import { getPolicyApi } from '@modules/settings/policy-settings/policy-api.js'
import SegmentedDataLine from './segmented-data-line.vue'
import LooseTags from '@components/loose-tags.vue'
import IncrementalResultProvider from '@components/data-provider/incremental-result-provider.vue'
import { convertTimeLineForServer } from '@/src/components/widgets/helper'

// const SEVERITY_TO_CONSIDER = [
//   Constants.DOWN,
//   Constants.UNREACHABLE,
//   Constants.CRITICAL,
//   Constants.MAJOR,
//   Constants.WARNING,
//   Constants.CLEAR,
// ]

export default {
  name: 'MetricOverview',
  components: {
    // Severity,
    WidgetContainer,
    Timeline,
    AlertTrendChart,
    SegmentedDataLine,
    LooseTags,
    IncrementalResultProvider,
  },
  inject: {
    SocketContext: { default: {} },
    counterContext: { default: { options: new Map() } },
  },
  props: {
    view: {
      type: String,
      default: undefined,
    },
    timeline: {
      type: Object,
      required: true,
    },
    monitor: {
      type: Object,
      required: true,
    },
    alert: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      isDataLoaded: false,
      guid: generateId(),
      overviewChartSeries: [],
      history: [],
      triggerCondition: '',
      activeItem: null,
      lineHistoryData: [],
      isDataReceived: false,
      incrementalResult: [],
      policySeverity: undefined,
      isHistoryDataReceived: false,
      policyContext: {},
    }
  },
  computed: {
    trendCountWidget() {
      const alert = this.alert
      const timeline = this.timeline
      return (
        buildWidgetContext({
          groupCategory: alert.groupCategory,
          category: WidgetTypeConstants.CHART,
          timeline,
          widgetType: WidgetTypeConstants.STACKED_VERTICAL_BAR,
          groupType: 'policy',
          resultBy: ['severity'],
          counters: [
            makeCounter('severity', 'count', 'Monitor', [alert.monitor]),
          ],
          ...(alert.instance
            ? {
                preFilters: {
                  condition: 'and',
                  inclusion: 'include',
                  conditions: [
                    {
                      operand: 'instance',
                      operator: '=',
                      value: alert.instance,
                    },
                  ],
                },
              }
            : {}),
        })
          .appendToGroup('policy', {
            alertIds: [alert.policyId],
            target: {
              entityType: 'Monitor',
              entities: [alert.monitor],
            },
            // severity: SEVERITY_TO_CONSIDER,
          })
          .setWidgetProperties({
            styleSetting: {
              chartOptions: {
                yAxis: { allowDecimals: false },
              },
            },
          })
          // .setWidgetProperties({
          //   styleSetting: {
          //     chartOptions: {
          //       colors: ['#f04e3e'],
          //       yAxis: {
          //         allowDecimal: false,
          //       },
          //       plotOptions: {
          //         series: {
          //           dataLabels: {
          //             enabled: true,
          //             verticalAlign: 'top',
          //             align: 'center',
          //             overflow: 'none',
          //             inside: true,
          //             color: 'white',
          //             style: {
          //               fontWeight: 'bold',
          //               fontSize: '14px',
          //             },
          //           },
          //         },
          //       },
          //     },
          //   },
          // })
          .getContext()
      )
    },
    shouldRenderChart() {
      const alert = this.alert
      if (['availability'].includes(this.alert.policyType.toLowerCase())) {
        return true
      }
      if (alert.counterRawName === 'status') {
        return true
      }
      const counter = this.counterContext.options.get(alert.counterRawName)
      if (counter) {
        return counter.dataType.includes('numeric')
      }
      return false
    },
    timeRangeWords() {
      const timeline = this.timeline
      return (
        AVAILABLE_RANGE_OPTIONS.find(
          (c) => (timeline || {}).selectedKey === c.key
        ) || {}
      ).text
    },
    isAnomalyPolicy() {
      return (
        this.alert.policyType &&
        this.alert.policyType.toLowerCase().includes('anomaly')
      )
    },
    isForecastPolicy() {
      return (
        this.alert.policyType &&
        this.alert.policyType.toLowerCase().includes('forecast')
      )
    },
    shouldShowTriggerCondition() {
      return (
        (this.alert.severity || '')?.toLowerCase() !== 'clear' &&
        this.alert.policyType?.toLowerCase() !== 'availability'
      )
    },
    lineChart() {
      const timeline = this.timeline
      const alert = this.alert
      return buildWidgetContext({
        groupType: 'metric',
        category: WidgetTypeConstants.CHART,

        widgetType: this.isAvailabilityPolicy
          ? WidgetTypeConstants.PIE
          : WidgetTypeConstants.LINE,
        counters: [
          makeCounter(this.alert.counterRawName, 'avg', 'Monitor', [
            alert.monitor,
          ]),
        ],
        ...(alert.instance
          ? {
              preFilters: {
                condition: 'and',
                inclusion: 'include',
                conditions: [
                  {
                    operand: alert.counterRawName.split('~')[0],
                    operator: '=',
                    value: alert.instance,
                  },
                ],
              },
            }
          : {}),
        timeline,
        ...(this.isAvailabilityPolicy ? { resultBy: ['monitor'] } : {}),
      })
        .setWidgetProperties({
          styleSetting: {
            chartOptions: {
              yAxis: {
                allowDecimals: false,
                plotBands: this.plotBands,
              },
            },
          },
        })
        .getContext()
    },
    timeLineForAlertTrend() {
      if (
        this.activeItem &&
        !this.isAvailabilityPolicy &&
        !this.isForecastPolicy
      ) {
        return {
          startDate:
            Moment.unix(this.activeItem.time).subtract(1, 'hour').unix() * 1000,
          endDate:
            Moment.unix(this.activeItem.time).add(1, 'hour').unix() * 1000,
          startTime: Moment.unix(this.activeItem.time)
            .subtract(1, 'hour')
            .format(TIME_FORMAT),
          endTime: Moment.unix(this.activeItem.time)
            .add(1, 'hour')
            .format(TIME_FORMAT),
          selectedKey: 'custom',
          dailyRollingData: false,
        }
      } else if (this.activeItem && this.isForecastPolicy) {
        const evaluationWindow =
          this.policyContext?.['policy.context']?.['evaluation.window']

        const match = evaluationWindow.match(/-(\d+)([hd])/) // Matches -number[h/d]
        if (!match) return this.timeline

        const value = parseInt(match[1], 10)
        const unit = match[2] === 'h' ? 'hours' : 'days' // 'h' -> hours, 'd' -> days
        return {
          startDate:
            Moment.unix(this.activeItem.time).subtract(value, unit).unix() *
            1000,
          endDate: Moment.unix(this.activeItem.time).unix() * 1000,
          startTime: Moment.unix(this.activeItem.time)
            .subtract(value, unit)
            .format(TIME_FORMAT),
          endTime: Moment.unix(this.activeItem.time).format(TIME_FORMAT),
          selectedKey: 'custom',
          dailyRollingData: false,
        }
      }

      return this.timeline
    },

    granularity() {
      const evaluationWindow =
        this.policyContext?.['policy.context']?.['evaluation.window']

      if (evaluationWindow) {
        const granularityMap = {
          '-12h': { unit: 'm', value: 15 }, // 15 minutes
          '-24h': { unit: 'm', value: 30 }, // 30 minutes
          '-48h': { unit: 'h', value: 1 }, // 1 hour
          '-7d': { unit: 'h', value: 3 }, // 3 hours
          '-14d': { unit: 'h', value: 6 }, // 6 hours
          '-30d': { unit: 'h', value: 12 }, // 12 hours
          '-60d': { unit: 'd', value: 1 }, // 1 day
          '-90d': { unit: 'd', value: 1 }, // 1 day
        }
        return granularityMap[evaluationWindow]
      } else {
        return undefined
      }
    },

    plotBands() {
      return makePlotBands(
        this?.alert.policySeverity || this.policySeverity || {}
      )
    },
    isAvailabilityPolicy() {
      return this.alert.policyType === 'Availability'
    },

    availabilityChart() {
      const alert = this.alert
      const timeline = this.timeline
      let counters = [
        'downtime',
        'suspendtime',
        'maintenancetime',
        'unreachabletime',
        'disabletime',
        'uptime',
      ]
      const instanceName = alert.counterRawName.split('~')[0]
      if (alert.instance) {
        counters = counters.map((i) => `${instanceName}~${i}.percent`)
      } else {
        counters = counters.map((i) => `monitor.${i}.percent`)
      }
      return buildWidgetContext({
        groupType: 'availability',
        timeline,
        category: WidgetTypeConstants.CUSTOM,
        widgetType: WidgetTypeConstants.PIE,
        counters: counters.map((c) =>
          makeCounter(c, 'avg', 'Monitor', alert.monitor)
        ),
        ...(alert.instance
          ? {
              preFilters: {
                condition: 'and',
                inclusion: 'include',
                conditions: [
                  {
                    operand: alert.counterRawName.split('~')[0],
                    operator: '=',
                    value: alert.instance,
                  },
                ],
              },
            }
          : {}),
      })
        .setWidgetProperties({
          styleSetting: {
            legendEnabled: true,
          },
        })
        .getContext()
    },

    alertHistoryWidget() {
      return getAlertHistoryData(this.alert, this.timeline, false, true)
    },
    historyLineWidget() {
      return getAlertHistoryData(this.alert, this.timeline, true, true)
    },
  },
  watch: {
    timeline(newValue, oldValue) {
      if (newValue !== oldValue) {
        if (this.$refs.historyLineResultProviderRef) {
          this.$refs.historyLineResultProviderRef.setGuid()
        }
        if (this.$refs.alertHistoryResultProviderRef) {
          this.$refs.alertHistoryResultProviderRef.setGuid()
        }

        this.requestData()
        this.requestIncrementalData()
      }
    },
  },
  async created() {
    Bus.$on('socket:connected', this.requestData)

    Bus.$on('ui.action.policy.active.flap.get', this.onReceivedCacheData)

    this.$once('hook:beforeDestroy', () => {
      Bus.$off('ui.action.policy.active.flap.get', this.onReceivedCacheData)
    })
    if (this.SocketContext.connected) {
      this.requestData()
    }

    await this.assembleTriggerConditionForAlert()
  },
  methods: {
    async requestData() {
      this.isDataLoaded = false

      // getSingleAlertCount(
      //   this.alert,
      //   this.timeline,
      //   this.view,
      //   alert.monitor
      // ).then(async (data) => {
      //   let userName
      //   if (this.alert.acknowledged && this.alert.acknowledgedBy) {
      //     userName = await getUserNameFromId(this.alert.acknowledgedBy)
      //   }
      //   if (data.length) {
      //     this.$emit('update:alert', {
      //       ...this.alert,
      //       count: data[0]['severity.count'],
      //       acknowledgedByUser: userName,
      //     })
      //   } else {
      //     this.$emit('update:alert', {
      //       ...this.alert,
      //       count: 0,
      //       acknowledgedByUser: userName,
      //     })
      //   }
      // })

      let userName
      if (this.alert.acknowledged && this.alert.acknowledgedBy) {
        userName = await getUserNameFromId(this.alert.acknowledgedBy)
        this.$emit('update:alert', {
          ...this.alert,
          acknowledgedByUser: userName,
        })
      }
      // this.requestAlertHistoryData()
      // this.requestAlertHistoryDataForHistoryLine()
    },
    formatDateTime(value) {
      return datetime(Math.round(value / 1000))
    },
    async assembleTriggerConditionForAlert() {
      this.policyContext = await getPolicyApi(
        this.alert.policyId,
        'metric',
        null,
        true,
        false,
        false
      )
      // if (this.alert.triggerCondition) {
      //   this.triggerCondition = this.alert.triggerCondition
      // } else {
      this.triggerCondition = triggerCondition(
        this.alert['policyType'],
        this.policyContext?.['policy.context']?.['policy.severity'],
        this.alert['value'],
        this.alert['policyThreshold'],
        this.alert['severity'],
        this.alert['metric']?.replace(/[~^]/g, '.'),
        this.policyContext
      )
      // }

      if (this.alert.policySeverity) {
        this.policySeverity = this.alert.policySeverity
      } else {
        this.policySeverity =
          this.policyContext?.['policy.context']?.['policy.severity']
      }
    },
    // requestAlertHistoryData() {
    //   getAlertHistoryData(this.alert, this.timeline).then((history) => {
    //     this.history = Object.freeze(history)
    //     this.activeItem = Object.freeze(CloneDeep(history[0]))

    //     this.$emit('update:alert', {
    //       ...this.alert,
    //       count: (history || []).length,
    //     })
    //   })
    // },
    // requestAlertHistoryDataForHistoryLine() {
    //   this.isDataReceived = false
    //   getAlertHistoryData(this.alert, this.timeline, true).then((history) => {
    //     this.lineHistoryData = Object.freeze(history)
    //     this.isDataReceived = true
    //   })
    // },
    alertHistoryPatchReceived(data) {
      this.isHistoryDataReceived = true
      this.history = [
        ...this.history,
        ...(data || []).map((row) =>
          transformAlertHistoryDataForClient(
            row,
            this.alert,
            this.policyContext
          )
        ),
      ]
      this.activeItem = this.history[0]
    },
    patchReceivedForHistoryLine(data) {
      this.isDataReceived = true
      this.lineHistoryData = [
        ...this.lineHistoryData,
        ...(data || []).map((row) =>
          transformAlertHistoryDataForClient(row, this.alert)
        ),
      ]

      if (this.$refs.historyLineResultProviderRef) {
        let progress = this.$refs.historyLineResultProviderRef.getProgress()

        if (progress === 100) {
          this.getHistoryLineCacheData()
        }
      }
    },

    requestIncrementalData() {
      this.isDataReceived = false
      this.isHistoryDataReceived = false
      this.history = []
      this.lineHistoryData = []
      if (this.$refs.historyLineResultProviderRef) {
        this.$refs.historyLineResultProviderRef.requestData()
      }
      if (this.$refs.alertHistoryResultProviderRef) {
        this.$refs.alertHistoryResultProviderRef.requestData()
      }
    },
    getHistoryLineCacheData() {
      Bus.$emit('server:event', {
        'event.type': Config.UI_ACTION_POLICY_ACTIVE_FLAP_GET,
        'event.context': {
          'entity.id': this.alert.monitor,
          'policy.id': this.alert.policyId,
          metric: this.alert.counterRawName,
          'visualization.timeline': convertTimeLineForServer(this.timeline),

          ...(this.alert.instance
            ? {
                instance: this.alert.instance,
              }
            : {}),
        },
      })
    },
    onReceivedCacheData(event) {
      if (!IsEmpty(event.result)) {
        this.lineHistoryData = [
          transformAlertHistoryDataForClient(event.result, this.alert),

          ...this.lineHistoryData,
        ]
      }
    },
  },
}
</script>
<style lang="less" scoped>
.metric-details {
  font-size: 0.8rem;
  font-weight: 500;
  color: var(--neutral-regular);
}
</style>
