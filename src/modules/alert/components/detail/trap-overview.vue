<template>
  <div class="min-h-0 flex flex-col flex-1">
    <div class="flex flex-col min-h-0 flex-1 w-full mt-2 pl-2">
      <MRow class="rounded" style="max-height: 50vh" :gutter="8">
        <MCol :size="4" class="h-full">
          <div class="vue-grid-item rounded flex flex-col h-full">
            <table class="item-list-table with-out-border">
              <colgroup>
                <col style="width: 150px" />
                <col />
              </colgroup>
              <tbody>
                <tr>
                  <td class="px-4 pt-2 pb-2">
                    <span class="text-ellipsis trap-details">Alert ID :</span>
                  </td>
                  <td
                    class="text-ellipsis px-4 pt-2 pb-2"
                    style="font-size: 0.8rem"
                  >
                    {{ alert.policyId }}
                  </td>
                </tr>
                <tr>
                  <td class="text-neutral px-4 pt-2 pb-2">
                    <span class="text-ellipsis trap-details">Source :</span>
                  </td>
                  <td
                    class="text-ellipsis px-4 pt-2 pb-2"
                    style="font-size: 0.8rem"
                  >
                    {{ alert.eventSource }}
                  </td>
                </tr>
                <!-- <tr>
                  <td class="text-neutral px-4 pt-2 pb-2">
                    <span class="text-ellipsis trap-details">Trap Type :</span>
                  </td>
                  <td
                    class="text-ellipsis px-4 pt-2 pb-2"
                    style="font-size: 0.8rem"
                  >
                    {{ alert.trapType }}
                  </td>
                </tr> -->
                <tr>
                  <td class="text-neutral px-4 pt-2 pb-2">
                    <span class="text-ellipsis trap-details">Metric :</span>
                  </td>
                  <td
                    class="text-ellipsis px-4 pt-2 pb-2"
                    style="font-size: 0.8rem"
                  >
                    {{ alert.metric }}
                  </td>
                </tr>
                <tr v-if="(alert.severity || '').toLowerCase() !== 'clear'">
                  <td class="text-neutral px-4 pt-2 pb-2">
                    <span class="text-ellipsis trap-details">
                      Trigger Conditions :
                    </span>
                  </td>
                  <td
                    class="text-ellipsis px-4 pt-2 pb-2"
                    style="font-size: 0.8rem"
                    >{{ alert.triggerCondition }}
                  </td>
                </tr>
                <tr>
                  <td class="text-neutral px-4 pt-2 pb-2">
                    <span class="text-ellipsis trap-details">Severity :</span>
                  </td>
                  <td
                    class="text-ellipsis px-4 pt-2 pb-2 text"
                    :class="alert.severity.toLowerCase()"
                    style="font-size: 0.8rem"
                  >
                    {{ alert.severity }}
                  </td>
                </tr>
                <tr>
                  <td class="text-neutral px-4 pt-2 pb-2">
                    <span class="text-ellipsis trap-details"
                      >Trigger Count :</span
                    >
                  </td>
                  <td
                    class="text-ellipsis px-4 pt-2 pb-2"
                    style="font-size: 0.8rem"
                  >
                    {{ alertCount }}
                  </td>
                </tr>
                <tr>
                  <td class="text-neutral px-4 pt-2 pb-2">
                    <span class="text-ellipsis trap-details">Last Seen :</span>
                  </td>
                  <td
                    class="text-ellipsis px-4 pt-2 pb-2"
                    style="font-size: 0.8rem"
                  >
                    {{ alert.lastSeen | datetime }}
                  </td>
                </tr>
                <tr>
                  <td class="text-neutral px-4 pt-2 pb-2">
                    <span class="text-ellipsis trap-details">First Seen :</span>
                  </td>
                  <td
                    class="text-ellipsis px-4 pt-2 pb-2"
                    style="font-size: 0.8rem"
                  >
                    {{ formatDateTime(alert.firstSeen) }}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </MCol>
        <MCol :size="8" class="h-full">
          <div class="vue-grid-item flex rounded flex-col h-full mr-2">
            <small class="font-500 mx-2 my-2">Alert Trend</small>
            <div
              class="flex flex-1 min-h-0 items-center justify-center flex-col min-w-0"
            >
              <WidgetContainer
                :widget="trendCountWidget"
                is-preview
                watch-widget
                class="w-full"
              />
            </div>
          </div>
        </MCol>
      </MRow>

      <MRow class="flex-1 min-h-0 mt-2 mb-2" :gutter="8">
        <MCol :size="6">
          <div
            class="vue-grid-item mr-2 rounded flex-1 flex flex-col min-h-0 h-full w-full"
            style="max-height: 60vh"
          >
            <IncrementalResultProvider
              ref="alertHistoryResultProviderRef"
              :serverside-widget-defination="alertHistoryWidget"
              :timeline="timeline"
              @patchRecived="alertHistoryPatchRecived"
            >
              <FlotoContentLoader :loading="!isHistoryDataRecived">
                <Timeline
                  :items="history"
                  :active-item="activeItem"
                  use-guid-as-key
                  @click="activeItem = $event"
                >
                  <template v-slot:title>
                    <small class="font-500 mx-2 my-2">History</small>
                  </template>
                </Timeline>
              </FlotoContentLoader>
            </IncrementalResultProvider>
            <!-- <template v-if="history.length">
                  <h4 class="text-primary-alt font-500 mt-2">History</h4>
                  <Timeline
                    :items="history"
                    :active-item="activeItem"
                    ignore-severity
                    @click="activeItem = $event"
                  />
                </template>
                <FlotoNoData
                  v-else
                  hide-svg
                  header-tag="h5"
                  icon="exclamation-triangle"
                  variant="neutral"
                  message="No history found for selected timeline"
                /> -->
          </div>
        </MCol>
        <MCol :size="6">
          <div
            v-if="activeItem"
            class="flex flex-1 flex-col min-h-0 h-full mr-2"
          >
            <div class="vue-grid-item flex rounded flex-col h-full px-2">
              <div class="flex flex-1 min-h-0">
                <div
                  class="flex-1 flex flex-col min-h-0 h-full w-full"
                  style="max-height: 50vh"
                >
                  <TrapHistoryDetail
                    :alert="alert"
                    :item="activeItem"
                    :timeline="timeline"
                    :policy="policy"
                  />
                </div>
              </div>
            </div>
          </div>
        </MCol>
      </MRow>
    </div>
  </div>
</template>
<script>
import CloneDeep from 'lodash/cloneDeep'
import { generateId } from '@utils/id'
import Bus from '@utils/emitter'
// import Severity from '@components/severity.vue'
import {
  AVAILABLE_RANGE_OPTIONS,
  WidgetTypeConstants,
} from '@components/widgets/constants'
import {
  getAlertHistoryData,
  // getSingleAlertCount,
  getUserNameFromId,
  transformAlertHistoryDataForClient,
} from '../../helpers/alert-helper'
import { getPolicyApi } from '@modules/settings/policy-settings/policy-api.js'
import datetime from '@src/filters/datetime'
import WidgetContainer from '@components/widgets/views/container.vue'
import Timeline from '@components/timeline.vue'
import TrapHistoryDetail from './trap-history-detail.vue'

import { buildWidgetContext, makeCounter } from '@utils/socket-event-as-api'
import IncrementalResultProvider from '@components/data-provider/incremental-result-provider.vue'

export default {
  name: 'TrapOverview',
  components: {
    WidgetContainer,
    Timeline,
    TrapHistoryDetail,
    IncrementalResultProvider,
  },
  inject: {
    SocketContext: { default: {} },
    counterContext: { default: { options: new Map() } },
  },
  props: {
    timeline: {
      type: Object,
      required: true,
    },
    alert: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      isDataLoaded: false,
      guid: generateId(),
      overviewChartSeries: [],
      history: [],
      activeItem: null,
      policy: {},
      isHistoryDataRecived: false,
    }
  },
  computed: {
    alertCount() {
      return this.alert.count !== undefined && this.alert.count !== null
        ? this.alert.count
        : this.history.length
    },
    trendCountWidget() {
      const alert = this.alert
      const timeline = this.timeline
      return buildWidgetContext({
        groupCategory: alert.groupCategory,
        category: WidgetTypeConstants.CHART,
        timeline,
        widgetType: WidgetTypeConstants.VERTICAL_BAR,
        groupType: 'policy',
        counters: [makeCounter('severity', 'count')],
        preFilters: {
          condition: 'and',
          inclusion: 'include',
          conditions: [
            { operand: 'policy.id', operator: '=', value: alert.policyId },

            ...(alert.groupCategory === 'trap'
              ? [
                  {
                    operand: 'event.source',
                    operator: '=',
                    value: alert.eventSource,
                  },
                ]
              : []),
          ],
        },
      })
        .appendToGroup('policy', {
          alertIds: [+alert.policyId],
          // additionalUntouchedRequestChunk: {
          //   'drill.down': 'yes',
          // },
        })
        .setWidgetProperties({
          styleSetting: {
            chartOptions: {
              colors: [
                this.alert.severity === this.$constants.WARNING
                  ? 'var(--severity-warning)'
                  : this.alert.severity === this.$constants.CRITICAL
                  ? 'var(--severity-critical)'
                  : 'var(--severity-major)',
              ],
              yAxis: { allowDecimals: false },
            },
          },
        })
        .getContext()
    },
    timeRangeWords() {
      const timeline = this.timeline
      return (
        AVAILABLE_RANGE_OPTIONS.find((c) => timeline.selectedKey === c.key) ||
        {}
      ).text
    },
    alertHistoryWidget() {
      return getAlertHistoryData(this.alert, this.timeline, false, true)
    },
  },
  watch: {
    timeline(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.requestData()
        this.requestIncrementalData()
      }
    },
  },
  async created() {
    Bus.$on('socket:connected', this.requestData)

    this.$once('hook:beforeDestroy', () => {})
    if (this.SocketContext.connected) {
      this.requestData()
    }
    await this.getPolicy()
  },
  methods: {
    getPolicy() {
      getPolicyApi(this.alert?.policyId, this.alert?.groupCategory).then(
        (data) => {
          this.policy = data
        }
      )
    },
    async requestData() {
      this.isDataLoaded = false

      let userName
      if (this.alert.acknowledged && this.alert.acknowledgedBy) {
        userName = await getUserNameFromId(this.alert.acknowledgedBy)

        this.$emit('update:alert', {
          ...this.alert,

          acknowledgedByUser: userName,
        })
      }
      getAlertHistoryData(this.alert, this.timeline).then((history) => {
        this.history = Object.freeze(history)
        this.activeItem = Object.freeze(CloneDeep(history[0]))
        this.$emit('update:alert', {
          ...this.alert,
          count: (history || []).length,
        })
        this.isDataLoaded = true
      })
    },
    formatDateTime(value) {
      return datetime(Math.round(value))
    },
    alertHistoryPatchRecived(data) {
      this.isHistoryDataRecived = true
      this.history = [
        ...this.history,
        ...(data || []).map((row) =>
          transformAlertHistoryDataForClient(
            row,
            this.alert,
            this.policyContext
          )
        ),
      ]
      this.activeItem = this.history[0]
    },
    requestIncrementalData() {
      this.isHistoryDataRecived = false
      this.history = []

      if (this.$refs.alertHistoryResultProviderRef) {
        this.$refs.alertHistoryResultProviderRef.requestData()
      }
    },
  },
}
</script>
<style lang="less" scoped>
.trap-details {
  font-size: 0.8rem;
  font-weight: 500;
  color: var(--neutral-regular);
}
</style>
