<template>
  <div class="h-full rounded flex flex-col">
    <FlotoContentLoader :loading="loading">
      <MRow class="mt-2">
        <MCol :size="6" class="flex">
          <span class="text-ellipsis trap-details mr-2">Trap Name:</span>
          <span class="flex flex-1 min-w-0">
            <span
              class="text-ellipsis"
              style="font-size: 0.8rem"
              :title="alert.policy"
            >
              {{ alert.policy }}</span
            >
          </span>
        </MCol>
        <MCol :size="6" class="flex">
          <span class="text-ellipsis trap-details mr-2"
            >Trigger Conditions:</span
          >
          <span class="flex flex-1 min-w-0">
            <span
              class="text-ellipsis"
              style="font-size: 0.8rem"
              :title="item.text"
            >
              {{ item.text }}</span
            >
          </span>
        </MCol>

        <MCol :size="12" class="flex mt-4">
          <span class="text-ellipsis trap-details mr-2"
            >Trap Filter Criteria:
          </span>
          <span v-if="filters" class="flex flex-1 min-w-0">
            <div class="text-ellipsis" :title="tooltip" v-html="groups" />
          </span>
        </MCol>
      </MRow>
      <!-- <div v-if="filters" class="flex w-full mt-2" style="max-width: 100%">
        <FilterTrigger :filters="filters" placeholder="Filters" />
      </div> -->
      <br />
      <span class="text-ellipsis trap-details pb-2">Message</span>
      <pre
        v-if="!isEmptyMessage"
        style="overflow-wrap: break-word"
        class="mb-0"
        v-html="message"
      />

      <span class="text-ellipsis trap-details pb-2">Raw Trap</span>
      <pre
        v-if="!isEmptyRawMessage"
        style="overflow-wrap: break-word"
        class="mb-0"
        v-html="rawMessage"
      />
    </FlotoContentLoader>
  </div>
</template>

<script>
import { generateId } from '@utils/id'
import Omit from 'lodash/omit'
import IsEmpty from 'lodash/isEmpty'
// import { isUnitConvertible } from '@/src/utils/unit-checker'
// import applyUnit from '@/src/utils/unit-applier'
import { getLogFlowAlertHistoryDetail } from '../../helpers/alert-helper'
import { OPERATOR_MAP } from '@components/widgets/constants'

// import FilterTrigger from '@components/filters/filter-trigger.vue'
// import { FILTER_CONDITION_DEFAULT_DATA } from '@components/widgets/constants'

export default {
  name: 'TrapHistoryDetail',
  components: {
    // TimeRangePicker,
    // FilterTrigger,
  },
  props: {
    alert: {
      type: Object,
      required: true,
    },
    item: {
      type: Object,
      required: true,
    },
    timeline: {
      type: Object,
      required: true,
    },
    policy: {
      type: Object,
      required: true,
    },
  },
  data() {
    this.disabledKeys = [
      'plugin.id',
      'datastore.type',
      'event',
      'policy.id',
      'event.timestamp',
      'event.source',
      'visualization.timeline',
      'trap.raw.message',
    ]
    return {
      loading: true,
      data: {
        value: [],
        evaluationWindow: {},
      },
      firstRow: {},
    }
  },
  computed: {
    category() {
      return this.alert.groupCategory
    },
    filters() {
      return this.policy?.conditions?.filterGroup
    },
    message() {
      const values = Omit(
        Object.values(this.data?.value?.[0] || {})[0] || {},
        this.disabledKeys
      )
      return JSON.stringify(values || {}, undefined, 2)
    },
    rawMessage() {
      const values = JSON.parse(
        Omit(Object.values(this.data?.value?.[0] || {})[0] || {})[
          'trap.raw.message'
        ] || '{}'
      )
      return JSON.stringify(values || {}, undefined, 2)
    },
    isEmptyMessage() {
      return IsEmpty(JSON.parse(this.message))
    },
    isEmptyRawMessage() {
      return IsEmpty(JSON.parse(this.rawMessage))
    },
    groups() {
      const appliedGroupCondition = this.filters?.grouping

      const groups = this.filters?.conditions || []

      if (
        !groups?.[0]?.operand ||
        !groups?.[0]?.operator ||
        !groups?.[0]?.value
      ) {
        return ''
      }

      return groups.reduce(
        (result, group, index) =>
          `${result} ${
            index !== 0 && index < groups?.length
              ? `<strong class="text-primary">${(
                  appliedGroupCondition || ''
                ).toUpperCase()}</strong>`
              : ''
          } ${groups?.length > 1 ? '' : ''}${this.getGroupText(
            group
            // groups.length > 1
          )}${groups?.length > 1 ? '' : ''}`,
        ''
      )
    },
    tooltip() {
      return `${this.groups}`.replace(/<[^>]+>/g, '')
    },
  },
  watch: {
    item(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.getDetail()
      }
    },
  },

  created() {
    this.getDetail()
  },
  methods: {
    getDetail() {
      getLogFlowAlertHistoryDetail(
        this.item,
        this.timeline,
        this.category
      ).then((data) => {
        const firstItem = Object.freeze((data.value.result || [])[0]) || {}
        this.firstRow = {
          ...firstItem,
          filters: this.generateFilterCondition(firstItem),
        }
        this.$nextTick(() => {
          this.data = Object.freeze({
            ...data,
            value: data.value.result || [],
          })
          this.loading = false
        })
      })
    },

    generateFilterCondition(row) {
      return Object.keys(row).reduce(
        (acc, key) => [
          ...acc,
          ...(key.replace(/[~^]/g, '.') !== this.counterColumn
            ? [
                {
                  operator: '=',
                  operand: key,
                  value: row[key],
                  key: generateId(),
                },
              ]
            : []),
        ],
        []
      )
    },
    getGroupText(group) {
      return `Varbind ${group?.operand} <strong>${
        OPERATOR_MAP[group?.operator] || ''
      }</strong> ${group?.value}`
    },
  },
}
</script>
<style lang="less" scoped>
.trap-details {
  font-size: 0.8rem;
  font-weight: 500;
  color: var(--neutral-regular);
}
</style>
