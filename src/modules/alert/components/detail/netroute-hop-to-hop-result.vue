<template>
  <div class="h-full">
    <FlotoContentLoader :loading="loading">
      <VirtualTable :columns="columns" :data="results">
        <template v-slot:path="{ item }">
          <span :class="[...item.classes, 'text']">
            {{ item.path }}
          </span>
        </template>
        <template v-slot:value="{ item }">
          <span
            v-if="item.classes.length"
            :class="[...item.classes, 'text', 'lightest-bg', 'rounded', 'px-1']"
          >
            {{ item.value }}
          </span>
          <span v-else>
            {{ item.value }}
          </span>
        </template>
      </VirtualTable>
    </FlotoContentLoader>
  </div>
</template>

<script>
import { WidgetTypeConstants } from '@/src/components/widgets/constants'
import VirtualTable from '@components/crud/virtual-table.vue'
import {
  buildWidgetContext,
  getWidgetResponseApi,
  makeCounter,
} from '@/src/utils/socket-event-as-api'
import { isUnitConvertible } from '@/src/utils/unit-checker'
import applyUnit from '@/src/utils/unit-applier'

export default {
  name: 'NetRouteHopToHop',
  components: {
    VirtualTable,
  },
  props: {
    resultItem: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      results: [],
      loading: true,
      columns: [
        {
          key: 'path',
          name: 'Path',
          searchable: true,
          sortable: false,
        },
        {
          key: 'value',
          name: 'Value',
          searchable: true,
          sortable: false,
        },
      ],
    }
  },
  created() {
    this.getData()
  },
  methods: {
    async getData() {
      let widget = buildWidgetContext({
        groupType: 'policy.result',
        category: WidgetTypeConstants.CHART,
        widgetType: WidgetTypeConstants.LINE,
        groupCategory: 'netroute.event',
        counters: [makeCounter('policy.trigger.value', '__NONE__')],
        preFilters: {
          condition: 'and',
          inclusion: 'include',
          conditions: [
            {
              operand: 'policy.trigger.id',
              operator: '=',
              value: this.resultItem.id,
            },
          ],
        },
      }).generateWidgetDefinition()
      let data = await getWidgetResponseApi(widget)
      if (data.length) {
        this.results = JSON.parse(
          JSON.parse(data[0]['policy.trigger.value.value']).result
        ).map((item) => ({
          path: `${item['netroute.source']}   →   ${item['netroute.destination.ip']}`,
          severity: item.severity,
          value: isUnitConvertible(item.metric)
            ? applyUnit(item.metric, item.value)
            : item.value,
          classes: ['clear'].includes((item.severity || '').toLowerCase())
            ? []
            : [(item.severity || '').toLowerCase()],
        }))
      }
      this.loading = false
    },
  },
}
</script>
