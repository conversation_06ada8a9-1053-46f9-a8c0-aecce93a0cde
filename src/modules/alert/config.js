export default {
  name: 'alert',
  routePrefix: 'alerts',
  routeNamePrefix: 'alert',
  directRoutesAvailable: true,
  CORRELATED_POLICIES: 'Correlated Policies',

  // ALERT_EVENTS
  ALERT_STREAM_ACTION: 'ui.action.alert.stream',
  ALERT_STREAM_CLEAR_ACTION: 'ui.action.policy.clear',
  ALERT_STREAM_SUPRESS_ACTION: 'ui.action.policy.suppress',
  ALERT_STREAM_ACKNOWLEDGE_ACTION: 'ui.action.policy.acknowledge',
  ALERT_STREAM: 'ui.event.alert.stream.render',
  ALERT_STREAM_COLLABORATION: 'ui.event.alert.stream.collaboration',
  ALERT_STREAM_NOTE: 'ui.action.policy.comment.update',
  ALERT_INTEGRATION_TICKET: 'ui.action.integration.response.get',
  SERVICENOW_INTEGRATION: 'ui.action.declare.incident',
  UI_ACTION_POLICY_ACTIVE_FLAP_GET: 'ui.action.policy.active.flap.get',
  UI_ACTION_POLICY_DETAILS_FETCH: 'ui.action.policy.query',
}
