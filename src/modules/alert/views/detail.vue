<template>
  <GroupProvider>
    <CounterProvider
      :search-params="counterSearchParams"
      should-include-motadata-agent-counters
    >
      <FlotoContentLoader :loading="loading">
        <FlotoFixedView>
          <div class="flex flex-1 flex-col min-h-0 min-w-0">
            <MRow class="min-w-0 border-bot p-1">
              <MCol class="flex-1 min-w-0 flex items-center">
                <MIcon
                  id="back-btn-id"
                  name="chevron-left"
                  size="lg"
                  class="cursor-pointer custome-spacer text-neutral-light"
                  style="margin-top: 0 !important"
                  @click="navigateBack"
                />
                <FlotoPageHeader
                  :use-divider="false"
                  :title="alert.policy"
                  class="-mt-1"
                >
                  <template v-slot:before-title>
                    <Severity :severity="alert.severity" class="mt-1" />
                  </template>
                </FlotoPageHeader>
              </MCol>
              <div
                class="px-2 radius-corner h-full items-center justify-center inline-flex font-small mb-1"
              >
                <TimeRangePicker
                  v-model="timeline"
                  class="h-full"
                  :allow-clear="false"
                />
              </div>
              <MCol v-if="showActions" auto-size class="flex items-center">
                <div
                  class="flex-1 text-right alert-actions flex justify-end mb-1"
                >
                  <Actions
                    :alert="alert"
                    :excluded-actions="['history']"
                    :timeline="timeline"
                    @change="handleUpdateAlert"
                  />
                </div>
              </MCol>
              <!-- <MCol :size="12">
                <MDivider class="my-1" />
              </MCol> -->
            </MRow>
            <!-- <MRow v-if="category !== 'Correlated Policies'" :gutter="0">
              <MCol class="relative flex items-center border-bot">
                <div class="flex-1">
                  <MTab
                    :value="tab"
                    class="no-border"
                    @change="handleTabChange"
                  >
                    <MTabPane key="overview" tab="Overview" />
                    <MTabPane key="rca" tab="RCA" disabled />
                    <MTabPane key="collaboration" tab="Collaboration" />
                  </MTab>
                </div>
                <div
                  class="px-2 radius-corner h-full items-center justify-center inline-flex font-small mb-1"
                >
                  <TimeRangePicker
                    v-model="timeline"
                    class="h-full"
                    :allow-clear="false"
                  />
                </div>
              </MCol>
            </MRow> -->
            <FlotoScrollView class="overflow-x-hidden">
              <div
                v-if="alert.id"
                class="flex flex-1 flex-col"
                style="background: var(--dashboard-background)"
              >
                <template v-if="tab === 'overview'">
                  <CorrelatedOverview
                    v-if="alert.isCorrelatedAlert"
                    :monitor="monitor"
                    :alert.sync="alert"
                    :view="view"
                    :timeline="timeline"
                    @alert="alert = $event"
                  />
                  <MetricOverview
                    v-else-if="alert.groupCategory === 'metric'"
                    :monitor="monitor"
                    :alert.sync="alert"
                    :view="view"
                    :timeline="timeline"
                    @alert="alert = $event"
                  />
                  <NetRouteOverview
                    v-else-if="
                      alert.groupCategory === 'netroute.metric' ||
                      alert.groupCategory === 'netroute.event'
                    "
                    :alert.sync="alert"
                    :view="view"
                    :timeline="timeline"
                    @alert="alert = $event"
                  />
                  <TrapOverview
                    v-else-if="alert.groupCategory === 'trap'"
                    :alert.sync="alert"
                    :timeline="timeline"
                    @alert="alert = $event"
                  />
                  <LogFlowOverview
                    v-else
                    :alert.sync="alert"
                    :timeline="timeline"
                    @alert="alert = $event"
                  />
                </template>
                <!-- <AlertRCATab v-else-if="tab === 'rca'" :alert="item" /> -->
                <Collaboration
                  v-else-if="tab === 'collaboration'"
                  :alert="alert"
                />
              </div>
            </FlotoScrollView>
          </div>
        </FlotoFixedView>
      </FlotoContentLoader>
    </CounterProvider>
  </GroupProvider>
</template>

<script>
import Bus from '@utils/emitter'
import Config from '../config.js'
import { generateId } from '@utils/id'

import { objectDBWorker } from '@/src/workers'
import GroupProvider from '@components/data-provider/group-provider.vue'
import TimeRangePicker from '@components/widgets/time-range-picker.vue'
import { UserPreferenceComputed } from '@state/modules/user-preference'
import CounterProvider from '@components/data-provider/counter-provider.vue'
import { WidgetTypeConstants } from '@components/widgets/constants'
import Actions from '../components/stream/actions.vue'
import MetricOverview from '../components/detail/metric-overview.vue'
import LogFlowOverview from '../components/detail/log-flow-overview.vue'
import TrapOverview from '../components/detail/trap-overview.vue'
import NetRouteOverview from '../components/detail/netroute-overview.vue'
import CorrelatedOverview from '../components/detail/correlated-overview.vue'
import Collaboration from '../components/detail/collaboration.vue'
import Severity from '@components/severity.vue'

import { transformAlertDataForPolicyDetailsDrillDown } from '../helpers/alert-helper'

export default {
  name: 'Detail',
  page() {
    return {
      title: `Alert History`,
    }
  },
  components: {
    MetricOverview,
    LogFlowOverview,
    Collaboration,
    Actions,
    TimeRangePicker,
    GroupProvider,
    CounterProvider,
    CorrelatedOverview,
    Severity,
    TrapOverview,
    NetRouteOverview,
  },
  inject: { SocketContext: { default: {} } },
  data() {
    return {
      loading: true,
      tab: null,
      category: null,
      alert: {},
      monitor: {},
      timeline: undefined,
      view: undefined,
      guid: generateId(),
    }
  },
  computed: {
    ...UserPreferenceComputed,
    counterSearchParams() {
      return {
        'visualization.group.type': this.alert.groupCategory,
        'visualization.category': WidgetTypeConstants.GRID,
      }
    },
    showActions() {
      return (
        this.view !== 'flap' ||
        ['log', 'flow'].includes((this.alert.policyType || '').toLowerCase())
      )
    },
  },
  async created() {
    const t = this.$route.query.t
    if (t) {
      try {
        this.timeline = JSON.parse(atob(decodeURIComponent(t)))
      } catch (e) {
        this.timeline = {
          selectedKey: 'today',
        }
      }
    } else {
      this.timeline = {
        selectedKey: 'today',
      }
    }
    this.tab = this.$route.params.tab
    this.category = this.$route.params.category
    const uuid = this.$route.params.uuid
    const parsed = atob(decodeURIComponent(uuid))

    try {
      const alert = JSON.parse(parsed)
      this.view = alert.view
      if (alert && alert['policy.drill.down.template'] === 'yes') {
        this.getAlertData(alert)
      } else {
        this.alert = Object.freeze({
          ...alert,
        })
        await this.getMonitor()
      }
    } catch (e) {
      this.$router.replace({ name: '404' })
    }

    Bus.$on(
      Config.UI_ACTION_POLICY_DETAILS_FETCH,
      this.handelPolicyContextReceived
    )
    this.$once('hook:beforeDestroy', () => {
      Bus.$off(
        Config.UI_ACTION_POLICY_DETAILS_FETCH,
        this.handelPolicyContextReceived
      )
    })
  },
  methods: {
    async getMonitor() {
      const monitor = await objectDBWorker.getObjectById(this.alert.monitor)
      if (monitor) {
        this.monitor = Object.freeze(monitor)
      }
      this.loading = false
    },
    navigateBack() {
      this.$router.back()
    },
    handleTabChange(tab) {
      this.$router.replace(
        this.$currentModule.getRoute('detail', {
          params: {
            ...this.$route.params,
            tab,
          },
          query: {
            t: encodeURIComponent(btoa(JSON.stringify(this.timeline))),
          },
        })
      )
    },
    handleUpdateAlert(update) {
      this.alert = Object.freeze({ ...this.alert, ...update })
      if (update.actionType) {
        this.$router.replace(
          this.$currentModule.getRoute('detail', {
            params: {
              ...this.$route.params,
              uuid: encodeURIComponent(btoa(JSON.stringify(this.alert))),
            },
            query: {
              t: encodeURIComponent(btoa(JSON.stringify(this.timeline))),
            },
          })
        )
      }
    },
    getAlertData(alert) {
      Bus.$emit('server:event', {
        'event.type': Config.UI_ACTION_POLICY_DETAILS_FETCH,
        'event.context': {
          [this.$constants.UI_EVENT_UUID]: this.guid,
          'policy.id': +alert['policy.id'] || alert.policyId,
          metric: alert['metric'] || alert.counterRawName,
          'entity.id': +alert['entity.id'] || alert.monitor,
          instance: alert.instance,
          'policy.type': alert['policy.type'],
        },
      })
    },
    async handelPolicyContextReceived(context) {
      if (context[this.$constants.UI_EVENT_UUID] !== this.guid) {
        return
      }
      const uuid = this.$route.params.uuid
      const parsed = atob(decodeURIComponent(uuid))

      let alert

      try {
        alert = JSON.parse(parsed)
      } catch (e) {
        this.$router.replace({ name: '404' })
      }
      const data = transformAlertDataForPolicyDetailsDrillDown({
        ...(alert || {}),
        ...(context.result || {}),
      })
      this.alert = Object.freeze({
        ...data,
      })
      await this.getMonitor()
    },
  },
}
</script>
