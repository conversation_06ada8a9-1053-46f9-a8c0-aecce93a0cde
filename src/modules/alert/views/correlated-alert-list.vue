<template>
  <FlotoContentLoader :loading="loading">
    <div class="flex flex-col h-full flex-1 content-inner-panel">
      <MRow :gutter="0">
        <MCol class="mt-3">
          <MInput
            v-model="searchTerm"
            class="search-box"
            placeholder="Search"
            name="search"
          >
            <template v-slot:prefix>
              <MIcon name="search" />
            </template>
            <template v-if="searchTerm" v-slot:suffix>
              <MIcon
                name="times-circle"
                class="text-neutral-light cursor-pointer"
                @click="searchTerm = undefined"
              />
            </template>
          </MInput>
        </MCol>
        <MCol :size="7" class="col text-right fixed-size">
          <div class="mt-2">
            <span class="mr-2 text-primary">10</span>
            <span class="mr-3 text-primary">Correlated Alerts</span>
            <span class="mr-3">|</span>
            <span class="mr-2">28</span>
            <span class="mr-3">Correlate Alerts</span>
          </div>
        </MCol>
        <MCol :size="12" :class="{ 'mt-4': openFilters }">
          <!-- <AlertFilters
            v-if="openFilters"
            v-model="filters"
            :type="type"
            @change="applyFilter"
            @hide="openFilters = !openFilters"
          /> -->
        </MCol>
      </MRow>
      <div class="flex flex-1 min-h-0">
        <MGrid
          class="h-100 hide-expand-column"
          default-sort="-alert"
          resource-name="CorrelatedAlert"
          :columns="columns"
          :search-term="searchTerm"
          :data="correlatedData"
          :filters="gridFilters"
          expandable
          detail="detailRow"
        >
          <template v-slot:detailRow>
            <CorrelatedAlertRowTabs :alert="alertRowData" />
          </template>
          <template v-slot:alert="{ item, toggleExpand }">
            <div class="flex items-center">
              <Severity :severity="item.severity" class="mr-2" />
              <span class="mr-3">
                <MIcon name="network-discovery" class="list-icon" />
                {{ item.count }}
              </span>
              <a
                class="expandeble-row"
                @click="activateItemDetails(item, toggleExpand)"
              >
                {{ item.alert }}
              </a>
            </div>
          </template>
          <template v-slot:acknowledge="{ item }">
            <div class="text-ellipsis text-secondary-green">
              <div class="flex items-center text-ellipsis">
                <MIcon
                  class="mr-1"
                  :name="
                    item.acknowledge === 'yes'
                      ? 'monitor-enable'
                      : 'monitor-disable'
                  "
                />
              </div>
            </div>
          </template>
          <template v-slot:actions="{ item }">
            <FlotoGridActions
              v-if="!item.isDefault"
              :actions="actions"
              :resource="item"
            />
          </template>
        </MGrid>
      </div>
    </div>
  </FlotoContentLoader>
</template>
<script>
import Severity from '@components/severity.vue'
// import AlertFilters from '../components/alert-filters.vue'
// import CorrelatedAlertRowTabs from '../components/correlated-alert-row-tabs.vue'
// import { getAllCorrelatedDetailsApi } from './../alert-api'
export default {
  name: 'CorrelatedAlertList',
  components: {
    Severity,
    // AlertFilters,
    // CorrelatedAlertRowTabs,
  },
  props: {
    showFilters: { type: Boolean, required: true },
    type: { type: String, required: true },
    columns: { type: Array, required: true },
  },
  data() {
    this.actions = [
      { key: 'view-detail', name: 'View Detail', icon: 'monitor-enable' },
      { key: 'clear-alert', name: 'Clear Alert', icon: 'monitor-enable' },
      {
        key: 'view-correlation',
        name: 'View Correlation',
        icon: 'monitor-enable',
      },
      { key: 'post-comment', name: 'Post Comment', icon: 'comment-dots' },
      { key: 'claimed-by-admin', name: 'Claim by Admin', icon: 'thumbs-up' },
      {
        key: 'unacknowledged-alert',
        name: 'Unacknowledged Alert',
        icon: 'check-circle',
      },
      { key: 'history', name: 'History', icon: 'history' },
      { key: 'disable-alert', name: 'Disable Alert', icon: 'monitor-disable' },
    ]
    return {
      loading: true,
      openFilters: false,
      filters: {
        groups: [],
        monitors: [],
        alerts: [],
        metrics: [],
        tags: [],
        acknowledgeds: [],
        sources: [],
      },
      searchTerm: undefined,
      correlatedData: [],
      alertRowData: undefined,
    }
  },
  computed: {
    gridFilters() {
      let filters
      const value = this.filters
      if (value.groups && value.groups.length) {
        filters = [
          ...(filters || []),
          {
            field: 'group',
            operator: 'array_contains',
            value: value.groups,
          },
        ]
      }
      if (value.monitors && value.monitors.length) {
        filters = [
          ...(filters || []),
          {
            field: 'monitor',
            operator: 'array_contains',
            value: value.monitors,
          },
        ]
      }
      if (value.alerts && value.alerts.length) {
        filters = [
          ...(filters || []),
          {
            field: 'alert',
            operator: 'array_contains',
            value: value.alerts,
          },
        ]
      }
      if (value.metrics && value.metrics.length) {
        filters = [
          ...(filters || []),
          {
            field: 'metric',
            operator: 'contains',
            value: value.metrics,
          },
        ]
      }
      if (value.acknowledgeds && value.acknowledgeds.length) {
        filters = [
          ...(filters || []),
          {
            field: 'acknowledged',
            operator: 'contains',
            value: value.acknowledgeds,
          },
        ]
      }

      return filters
    },
  },
  watch: {
    showFilters(newVal) {
      this.openFilters = newVal
    },
  },
  created() {
    this.fetchCorrelatedData()
  },
  methods: {
    fetchCorrelatedData() {
      // getAllCorrelatedDetailsApi().then((data) => {
      //   this.correlatedData = Object.freeze(data)
      //   this.$nextTick(() => {
      //     this.loading = false
      //   })
      // })
    },
    activateItemDetails(item, toggleExpand) {
      this.alertRowData = item
      toggleExpand()
    },
    applyFilter() {
      this.openFilters = false
    },
  },
}
</script>
<style lang="less" scoped>
.k-hierarchy-cell {
  display: none;
}
</style>
