import lazyLoadView from '@router/lazy-loader'
import ContainerView from './views/main'
import configs from './config'

const routePrefix = configs.routePrefix

const moduleName = configs.name

const routeNamePrefix = configs.routeNamePrefix

export default [
  {
    path: `/${routePrefix}`,
    component: ContainerView,
    meta: { moduleName },
    children: [
      {
        path: '',
        redirect: {
          name: `${routeNamePrefix}.dashboard`,
        },
        name: routeNamePrefix,
      },
      {
        path: 'dashboard/:type?',
        name: `${routeNamePrefix}.dashboard`,
        component: () =>
          lazyLoadView(
            import(
              /* webpackChunkName: "alert-metric-explorer" */ './views/dashboard'
            )
          ),
        meta: {},
      },
      {
        path: 'correlated-alerts/:type/:tab',
        name: `${routeNamePrefix}.correlated-alert-list`,
        component: () =>
          lazyLoadView(
            import(
              /* webpackChunkName: "alert-metric-explorer" */ './views/correlated-alert-list'
            )
          ),
        meta: {},
      },
      {
        path: 'detail/:category/:tab/:uuid',
        name: `${routeNamePrefix}.detail`,
        component: () =>
          lazyLoadView(
            import(
              /* webpackChunkName: "alert-metric-explorer" */ './views/detail'
            )
          ),
        meta: {},
      },
      {
        path: ':category/:tab',
        name: `${routeNamePrefix}.stream`,
        component: () =>
          lazyLoadView(
            import(
              /* webpackChunkName: "alert-metric-explorer" */ './views/stream'
            )
          ),
        meta: {},
      },
    ],
  },
]
