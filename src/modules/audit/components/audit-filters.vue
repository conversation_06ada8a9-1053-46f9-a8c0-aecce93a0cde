<template>
  <MRow class="items-center">
    <MCol v-if="operationTypeOption.length" :size="3">
      <FlotoFormItem label="Operation Type">
        <FlotoDropdownPicker
          id="operation-type"
          v-model="currentValue.operationType"
          class="mt-1"
          :options="operationTypeOption"
        />
      </FlotoFormItem>
    </MCol>
    <MCol :size="3">
      <FlotoFormItem label="Users">
        <FlotoDropdownPicker
          id="users"
          v-model="currentValue.users"
          class="mt-1"
          :options="usersOption"
          placeholder="Select"
          multiple
        />
      </FlotoFormItem>
    </MCol>
    <MCol :size="3">
      <FlotoFormItem label="Status">
        <FlotoDropdownPicker
          id="status"
          v-model="currentValue.status"
          class="mt-1"
          :options="statusOptions"
        />
      </FlotoFormItem>
    </MCol>
    <MCol class="relative mt-3">
      <MButton
        id="reset-btn"
        variant="default"
        @click="
          $emit('change', {
            operationType: 'all',
            users: [],
            status: 'all',
            resetSelectedModule: true,
          })
        "
      >
        Reset
      </MButton>
      <MButton id="apply-btn" class="ml-2" @click="apply">Apply</MButton>
    </MCol>

    <MButton
      id="close-filter"
      variant="transparent"
      :shadow="false"
      shape="circle"
      style="position: absolute; top: 0; right: 0"
      class="monitor-agent-filter-close"
      @click="$emit('hide')"
    >
      <MIcon name="times" class="text-neutral-light" />
    </MButton>
  </MRow>
</template>

<script>
import Omit from 'lodash/omit'

export default {
  name: 'AuditFilters',
  model: {
    event: 'change',
  },
  props: {
    value: { type: Object, required: true },
    // moduleType: { type: String, required: true },
    usersOption: { type: Array, required: true },
    operationTypeOption: { type: Array, required: true },
  },
  data() {
    this.statusOptions = [
      {
        key: 'all',
        name: 'All',
        id: 'all',
      },
      {
        key: 'succeed',
        name: 'Success',
        id: 'success',
      },
      {
        key: 'fail',
        name: 'Fail',
        id: 'failed',
      },
    ]
    return {
      currentValue: { ...this.value },
    }
  },
  watch: {
    value(newValue) {
      this.currentValue = { ...newValue }
    },
  },
  methods: {
    apply() {
      this.$emit('change', Omit(this.currentValue, 'resetSelectedModule'))
    },
  },
}
</script>
