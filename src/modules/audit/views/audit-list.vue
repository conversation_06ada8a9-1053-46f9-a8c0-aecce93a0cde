<template>
  <IncrementalResultProvider
    ref="resultProviderRef"
    :serverside-widget-defination="widget"
    :timeline="selectedAuditTimeLine"
    @patchRecived="patchRecived"
  >
    <div class="flex flex-col flex-1 min-h-0 content-inner-panel">
      <div class="flex flex-col">
        <FlotoPageHeader title="Audit">
          <template v-slot:before-title>
            <MIcon name="audit" class="text-primary-alt pl-2" />
          </template>
          <template v-slot:after-title>
            <div class="flex items-center justify-end">
              <!-- <MInput
                  v-model="searchTerm"
                  class="search-box"
                  placeholder="Search"
                >
                  <template v-slot:prefix>
                    <MIcon name="search" class="excluded-header-icon" />
                  </template>
                  <template v-if="searchTerm" v-slot:suffix>
                    <MIcon
                      name="times-circle"
                      class="text-neutral-light cursor-pointer"
                      @click="searchTerm = ''"
                    />
                  </template>
                </MInput> -->
              <div class="flex items-center ml-2">
                <TimeRangePicker
                  v-model="selectedAuditTimeLine"
                  class="mr-1"
                  :hide-custom-time-range="false"
                  @change="changeDateTime"
                />
                <!-- <FlotoGridActions
                    id="bulk_action"
                    style="font-size: 0.8rem"
                    :resource="{}"
                    :actions="auditBulkActions"
                    @csv="exportBulkCSV(selectedItems)"
                    @pdf="exportBulkPDF(selectedItems)"
                  /> -->
              </div>
            </div>
          </template>
        </FlotoPageHeader>
      </div>
      <MPersistedColumns
        v-model="columns"
        :default-value="columns"
        :module-key="`${selectedAuditModule}-audit`"
        :available-columns="availableColumns"
      >
        <template
          v-slot="{
            columns: persistedColumns,
            setColumns: updatePersistedColumns,
          }"
        >
          <FlotoFixedView>
            <FlotoScrollView>
              <div class="flex flex-col h-full flex-1 min-h-0 pl-2">
                <!-- <MRow v-if="!selectedAuditModule">
                <MCol :size="2" class="mt-2">
                  <FlotoFormItem label="Module Type" class="mb-2">
                    <FlotoDropdownPicker
                      id="module-type"
                      v-model="selectedAuditModule"
                      :options="moduleOptions"
                      placeholder="Select"
                    />
                  </FlotoFormItem>
                </MCol>
              </MRow> -->

                <MRow
                  class="flex mt-2 alert-dashboard"
                  :gutter="8"
                  style="margin-right: 0 !important"
                >
                  <MCol :size="2" class="h-full">
                    <div class="vue-grid-item h-full flex flex-col">
                      <small
                        class="font-500 mx-2 my-2 m-0 inline-block text-size-medium"
                        >Audit Event</small
                      >
                      <div class="flex-1 min-h-0 flex flex-col">
                        <ChartView
                          v-if="pieChartData"
                          class="mb-3"
                          :widget="pieChartWidget"
                          :data="pieChartData"
                          is-preview
                          disable-severity-colors
                          pie-inner-size="70%"
                        />
                      </div>
                    </div>
                  </MCol>

                  <MCol :size="5" class="h-full">
                    <div class="vue-grid-item flex rounded flex-col h-full">
                      <small class="mx-2 my-1"
                        >Top Audit Events By Module</small
                      >
                      <div
                        class="flex flex-1 min-h-0 items-center justify-center flex-col min-w-0"
                      >
                        <WidgetContainer
                          :widget="verticalBarByModules"
                          is-preview
                          watch-widget
                          class="w-full"
                        />
                      </div>
                    </div>
                  </MCol>

                  <MCol :size="5" class="h-full">
                    <div class="vue-grid-item flex rounded flex-col h-full">
                      <small class="mx-2 my-1">Top Audit Events by user</small>
                      <div
                        class="flex flex-1 min-h-0 items-center justify-center flex-col min-w-0"
                      >
                        <WidgetContainer
                          :widget="verticalBarByUser"
                          is-preview
                          watch-widget
                          class="w-full"
                        />
                      </div>
                    </div>
                  </MCol>
                </MRow>
                <div
                  class="flex-1 flex flex-col vue-grid-item my-2 mr-1"
                  style="flex-shrink: 0; min-height: 30vh"
                >
                  <small
                    class="font-500 mx-2 my-2 m-0 inline-block text-size-medium"
                    >Audit Trend</small
                  >
                  <div class="flex-1 min-h-0">
                    <ChartView
                      :widget="chartHistoricalWidget"
                      :data="historicChartDataComputed"
                      is-preview
                      enable-legend
                    />
                  </div>
                </div>

                <MRow class="mt-2 mr-1" :gutter="0">
                  <MCol :size="12">
                    <div class="flex items-center justify-between">
                      <MInput
                        v-model="searchTerm"
                        class="search-box"
                        placeholder="Search"
                      >
                        <template v-slot:prefix>
                          <MIcon name="search" class="excluded-header-icon" />
                        </template>
                        <template v-if="searchTerm" v-slot:suffix>
                          <MIcon
                            name="times-circle"
                            class="text-neutral-light cursor-pointer"
                            @click="searchTerm = ''"
                          />
                        </template>
                      </MInput>
                      <div class="flex items-center ml-2">
                        <!-- <FlotoGridActions
                    id="bulk_action"
                    style="font-size: 0.8rem"
                    :resource="{}"
                    :actions="auditBulkActions"
                    @csv="exportBulkCSV(selectedItems)"
                    @pdf="exportBulkPDF(selectedItems)"
                  /> -->

                        <ColumnSelector
                          v-model="columns"
                          :columns="availableColumns"
                          @change="updatePersistedColumns"
                        >
                          <template v-slot:trigger="{ toggle }">
                            <MButton
                              class="squared-button mr-2"
                              variant="neutral-lightest"
                              @click="toggle"
                            >
                              <MIcon name="eye" class="excluded-header-icon" />
                            </MButton>
                          </template>
                        </ColumnSelector>

                        <MButton
                          :shadow="false"
                          class="squared-button mr-2"
                          :rounded="false"
                          variant="neutral-lightest"
                          title="Export As CSV"
                          @click="exportBulkCSV(selectedItems)"
                        >
                          <MIcon name="export-csv" />
                        </MButton>

                        <MButton
                          v-if="queryProgress && queryProgress === 100"
                          :shadow="false"
                          class="squared-button mr-2"
                          :rounded="false"
                          variant="neutral-lightest"
                          title="Export As PDF"
                          @click="exportBulkPDF(selectedItems)"
                        >
                          <MIcon name="export-pdf" />
                        </MButton>

                        <MButton
                          id="filter-btn"
                          class="squared-button"
                          variant="neutral-lightest"
                          @click="isFilterVisible = !isFilterVisible"
                        >
                          <MIcon name="filter" class="excluded-header-icon" />
                        </MButton>
                      </div> </div
                  ></MCol>
                </MRow>
                <MRow
                  v-if="isFilterVisible"
                  class="mt-2 slide-filters mr-1"
                  :gutter="0"
                >
                  <MCol :size="2" class="mr-4 mt-1">
                    <FlotoFormItem label="Module Type">
                      <FlotoDropdownPicker
                        id="module-type"
                        v-model="selectedAuditModule"
                        :options="moduleOptions"
                        placeholder="Select"
                        allow-clear
                      />
                    </FlotoFormItem>
                  </MCol>
                  <MCol>
                    <AuditFilters
                      v-model="appliedFilters"
                      :module-type="selectedAuditModule"
                      :users-option="userOptions"
                      :operation-type-option="operationTypeOption"
                      @change="filterChange"
                      @hide="isFilterVisible = !isFilterVisible"
                    />
                  </MCol>
                </MRow>
                <FlotoContentLoader :loading="loading">
                  <div
                    class="flex flex-col flex-1 min-h-0 mr-1"
                    style="flex-shrink: 0; min-height: auto"
                    :class="{
                      bordered: !auditGridData.length,
                      rounded: !auditGridData.length,
                      'mt-4': !auditGridData.length,
                    }"
                  >
                    <Transition name="placeholder" mode="in-out" appear>
                      <!-- <div
                        v-if="!selectedAuditModule"
                        class="flex items-center justify-center flex-1 flex-col"
                      >
                        <div class="flex flex-col" style="max-height: 400px">
                          <SvgImage class="w-full" />
                        </div>
                        <h4 class="text-center text-primary mt-2">
                          Please select the module for which you want to view
                          the audit trail.
                        </h4>
                      </div> -->

                      <FlotoNoData
                        v-if="!auditGridData.length"
                        hide-svg
                        header-tag="h5"
                        icon="exclamation-triangle"
                        variant="neutral"
                      />

                      <MGrid
                        v-else
                        ref="gridRef"
                        :key="selectedAuditModule"
                        :search-term="searchTerm"
                        :columns="persistedColumns"
                        :data="auditGridData"
                        class="h-100"
                        :use-padding="false"
                        as-table
                        resource-name="Audit"
                        :default-page-size="50"
                        @selection-change="selectedItems = $event"
                      >
                        <template v-slot:status="{ item }">
                          <MStatusTag :status="item.status" />
                        </template>
                        <template v-slot:time="{ item }">
                          {{ item.time | datetime }}
                        </template>
                        <template v-slot:message="{ item }">
                          {{ item.message }}
                          <span v-if="item.showMore">
                            <br />
                            <a @click="detailModelShow(item)"> Show more... </a>
                          </span>
                        </template>
                      </MGrid>
                    </Transition>
                  </div>
                </FlotoContentLoader>

                <FlotoDrawer :open="Boolean(detailItem)" @hide="onCancel">
                  <template v-slot:trigger>
                    <span />
                  </template>
                  <template v-slot:title>
                    <div class="flex items-center">
                      <div class="flex-1">
                        <slot name="title">
                          <h4 class="mb-0 text-primary">Audit Trail Details</h4>
                        </slot>
                      </div>
                    </div>
                  </template>
                  <div v-if="detailItem" class="audit-detail">
                    <span class="notification-message">{{
                      detailItem.message
                    }}</span>
                    <table
                      key="tabular-list"
                      class="item-list-table w-full audit-table-message"
                    >
                      <thead class="bg-neutral-lighter">
                        <tr>
                          <td
                            class="sticky"
                            style="
                              width: 50%;
                              color: var(--neutral-dark) !important;
                            "
                            >New Value</td
                          >
                          <td
                            class="sticky"
                            style="
                              width: 50%;
                              color: var(--neutral-dark) !important;
                            "
                            >Old Value</td
                          >
                        </tr>
                      </thead>
                      <tbody>
                        <tr v-for="item in detailItem.data" :key="item.id">
                          <td> {{ item.newValue.replace(/\\/g, '') }} </td>
                          <td> {{ item.oldValue.replace(/\\/g, '') }} </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  <template v-slot:footer>
                    <span />
                  </template>
                </FlotoDrawer>
              </div>
            </FlotoScrollView>
          </FlotoFixedView>
        </template>
      </MPersistedColumns>
    </div>
  </IncrementalResultProvider>
</template>
<script>
import Uniq from 'lodash/uniq'
import UniqBy from 'lodash/uniqBy'

import Constants from '@constants'
import { WidgetTypeConstants } from '@components/widgets/constants'
import TimeRangePicker from '@components/widgets/time-range-picker.vue'
// import SvgImage from '../components/audit-svg.svg'
import AuditFilters from '../components/audit-filters.vue'
import ChartView from '@/src/components/widgets/views/chart-view.vue'
import WidgetContainer from '@components/widgets/views/container.vue'
import ColumnSelector from '@components/column-selector.vue'
import IncrementalResultProvider from '@components/data-provider/incremental-result-provider.vue'
import {
  exportApi,
  fetchAuditDataApi,
  fetchAuditSupportDataApi,
  fetchAuditUsersAndOperationsApi,
  transformAuditForList,
  getPieChartByStatus,
  getAuditHistoricChart,
  AUDIT_STATUS_COLORS,
  AUDIT_COUNTERS,
  AUDIT_STATUS,
  AUDIT_GRID_COLUMN,
  tranformDataForUserAndOperation,
} from '../audit-api'
import { generateId } from '@utils/id'
import MRow from '@motadata/ui/components/Grid/Row'
import { buildWidgetContext, makeCounter } from '@utils/socket-event-as-api'
import Bus from '@utils/emitter'
import exportData from '@modules/settings/monitoring/helpers/export-pdf-csv'
import { downloadFile } from '@utils/download'
import { UserPreferenceComputed } from '@state/modules/user-preference'

export default {
  name: 'AuditList',
  components: {
    MRow,
    AuditFilters,
    // SvgImage,
    TimeRangePicker,
    ChartView,
    WidgetContainer,
    ColumnSelector,
    IncrementalResultProvider,
  },
  inject: { SocketContext: { default: {} } },
  data() {
    this.actions = [
      { key: 'csv', name: 'Export CSV', icon: 'export-csv' },
      // { key: 'pdf', name: 'Export PDF', icon: 'export-pdf' },
    ]
    this.auditBulkActions = [
      { key: 'csv', name: 'Export CSV', icon: 'export-csv' },
      // { key: 'pdf', name: 'Export PDF', icon: 'export-pdf' },
    ]
    return {
      loading: true,
      selectedAuditTimeLine: {
        selectedKey: 'today',
      },
      selectedAuditModule: undefined,
      detailItem: undefined,
      appliedFilters: {
        operationType: 'all',
        users: [],
        status: 'all',
      },
      auditModuleOption: [],
      userOptions: [],
      selectedItems: [],
      availableOperations: [],
      auditGridData: [],
      queryProgress: null,
      currentBatch: 1,
      error: null,
      searchTerm: '',
      isPaused: false,
      guid: generateId(),
      pieChartData: null,
      historicChartData: {},
      isFilterVisible: false,
      columns: [],
      availableColumns: [],
    }
  },
  computed: {
    ...UserPreferenceComputed,
    moduleOptions() {
      const modules = this.auditModuleOption
      return modules.map((moduleName) => ({
        text: moduleName.text,
        key: moduleName.key,
      }))
    },
    operationTypeOption() {
      return [{ key: 'all', text: 'All' }].concat(
        this.availableOperations.map((o) => ({ key: o, text: o }))
      )
    },
    pieChartWidget() {
      return buildWidgetContext({
        groupType: 'audit',
        category: WidgetTypeConstants.GAUGE,
        widgetType: WidgetTypeConstants.PIE,
        counters: [makeCounter(AUDIT_COUNTERS.STATUS, 'count')],
        resultBy: [AUDIT_COUNTERS.STATUS],
        timeline: this.selectedAuditTimeLine,
      }).getContext()
    },
    verticalBarByModules() {
      return buildWidgetContext({
        groupType: 'audit',
        timeline: this.selectedAuditTimeLine,
        category: WidgetTypeConstants.TOPN,
        widgetType: WidgetTypeConstants.VERTICAL_BAR,
        counters: [makeCounter(AUDIT_COUNTERS.MODULE, 'count')],
        resultBy: [AUDIT_COUNTERS.MODULE],
      })
        .setWidgetProperties({
          styleSetting: {
            // rotation: -45,
            chartOptions: {
              yAxis: {
                allowDecimals: false,
              },
            },
          },
          sortingSetting: {
            direction: 'desc',
            topCount: 5,
          },
        })
        .getContext()
    },

    verticalBarByUser() {
      return buildWidgetContext({
        groupType: 'audit',
        timeline: this.selectedAuditTimeLine,
        category: WidgetTypeConstants.TOPN,
        widgetType: WidgetTypeConstants.VERTICAL_BAR,
        counters: [makeCounter(AUDIT_COUNTERS.USER, 'count')],
        resultBy: [AUDIT_COUNTERS.USER],
      })
        .setWidgetProperties({
          styleSetting: {
            // rotation: -45,
            chartOptions: {
              yAxis: {
                allowDecimal: false,
              },
            },
          },
          sortingSetting: {
            direction: 'desc',
            topCount: 5,
          },
        })
        .getContext()
    },

    chartHistoricalWidget() {
      return buildWidgetContext({
        groupType: 'audit',
        category: WidgetTypeConstants.CHART,
        widgetType: WidgetTypeConstants.STACKED_VERTICAL_BAR,

        counters: [makeCounter(AUDIT_COUNTERS.STATUS, 'count')],
        resultBy: [AUDIT_COUNTERS.STATUS],
        timeline: this.timeline,
      })
        .setWidgetProperties({
          styleSetting: {
            chartOptions: {
              yAxis: {
                allowDecimal: false,
              },
            },
          },
        })
        .getContext()
    },

    historicChartDataComputed() {
      return {
        ...(this.historicChartData || {}),
        series: (this.historicChartData?.series || []).map((s) => ({
          ...s,
          name: s.monitor,
          color:
            (s.monitor || s.ip).toLowerCase() === AUDIT_STATUS.SUCCESS
              ? AUDIT_STATUS_COLORS[AUDIT_STATUS.SUCCESS]
              : AUDIT_STATUS_COLORS[AUDIT_STATUS.FAIL],
        })),
      }
    },
    widget() {
      return fetchAuditUsersAndOperationsApi(
        this.selectedAuditModule,
        this.selectedAuditTimeLine,
        true
      )
    },
  },
  watch: {
    // selectedAuditModule(newValue, oldValue) {
    //   if (newValue !== oldValue) {
    //     this.isFilterVisible = false
    //     this.requestDataRaw()
    //   }
    // },
  },
  created() {
    this.getSupportData()
    this.requestDataRaw()
    this.requestChartData()
    this.columns = AUDIT_GRID_COLUMN
    this.availableColumns = AUDIT_GRID_COLUMN
  },

  beforeDestroy() {
    this.stopHeartbeat()
    this.abortQuery()
  },

  methods: {
    onCancel() {
      this.detailItem = undefined
    },
    // resetFilterModuleType() {
    //   this.appliedFilters = {
    //     operationType: 'all',
    //     users: [],
    //     status: 'all',
    //     selectedAuditTimeLine: {
    //       selectedKey: 'today',
    //     },
    //   }
    //   this.isFilterVisible = false
    // },
    filterChange(filter) {
      if (this.$refs.gridRef) {
        this.$refs.gridRef.resetSelection()
        this.$refs.gridRef.resetSkip()
      }

      if (filter.resetSelectedModule) {
        this.selectedAuditModule = undefined
      }
      this.requestDataRaw()
      this.isFilterVisible = false
    },
    getSupportData() {
      return fetchAuditSupportDataApi(this.selectedAuditTimeLine).then(
        (data) => {
          this.auditModuleOption = Object.freeze(data)
          this.loading = false
        }
      )
    },
    exportBulkCSV(selectedItems) {
      const guid = generateId()
      this.SocketContext.addGuidForEvent(Constants.UI_EVENT_CSV_EXPORT, guid)
      return exportApi(
        this.selectedAuditModule,
        selectedItems,
        this.appliedFilters,
        this.selectedAuditTimeLine,
        guid
      )
    },
    async exportBulkPDF(selectedItems) {
      if (this.$refs?.gridRef) {
        const type = 'pdf'
        const columns = this.columns.filter((obj) => obj.key && !obj.hidden)
        const options = {
          dateTimeFormat: this.dateFormat,
          timezone: this.timezone,
        }

        let items =
          (await this.$refs?.gridRef?.getFilteredDataWithoutTake())?.data || []
        const contextData = this.$refs.gridRef.getContextData()

        this.$successNotification({
          message: 'Success',
          description: `The file will be downloaded once ready`,
        })

        exportData(columns, items, type, contextData, options).then((blob) => {
          downloadFile(blob, undefined, `audit.${type}`)
        })
      }

      // const guid = generateId()
      // this.SocketContext.addGuidForEvent(Constants.UI_EVENT_CSV_EXPORT, guid)
      // return exportApi(
      //   this.selectedAuditModule,
      //   selectedItems,
      //   this.appliedFilters,
      //   this.selectedAuditTimeLine,
      //   guid
      // )
    },
    changeDateTime() {
      if (this.$refs.gridRef) {
        this.$refs.gridRef.resetSelection()
        this.$refs.gridRef.resetSkip()
      }
      this.requestDataRaw()
      this.requestChartData()
    },
    async fetchAuditModuleData(queryId) {
      // this.getUserAndOperationsData()
      this.requestUsersAndOperationsData()
      return fetchAuditDataApi(
        this.selectedAuditModule,
        this.selectedAuditTimeLine,
        this.appliedFilters,
        queryId,
        this.guid
      ).then((response) => {
        this.handleDataReceived(response)
      })
    },
    // getUserAndOperationsData() {
    //   return fetchAuditUsersAndOperationsApi(
    //     this.selectedAuditModule,
    //     this.selectedAuditTimeLine
    //   ).then(({ users, operations }) => {
    //     this.userOptions = Object.freeze(
    //       users.map((user) => ({ text: user, key: user }))
    //     )
    //     this.availableOperations = Object.freeze(operations)
    //   })
    // },
    detailModelShow(item) {
      const updatedMessage = item.oldNewMessage.substring(
        item.oldNewMessage.indexOf('{')
      )
      const updatedItem = JSON.parse(updatedMessage)
      this.detailItem = {
        message: item.message,
        updatedMessage: updatedMessage,
        data: Object.keys(updatedItem['old.prop.value']).map((item) => ({
          oldValue: `${item}: ${updatedItem['old.prop.value'][item]}`,
          newValue: `${item}: ${updatedItem['new.prop.value'][item]}`,
          id: generateId(),
        })),
      }
    },
    requestDataRaw() {
      this.abortQuery()
      this.loading = true
      this.error = null
      this.queryProgress = null
      this.auditGridData = []
      this.isPaused = false
      this.currentBatch = 1
      this.startAuditIncremental()
    },
    async startAuditIncremental(queryId = null) {
      this.fetchAuditModuleData(queryId)
    },
    handleDataReceived(response) {
      this.__parentQueryId = response.result.queryMeta.parentQueryId
      this.queryProgress = response.result.queryMeta.progress

      if (response[this.$constants.UI_EVENT_UUID] !== this.guid) {
        return
      }
      this.auditGridData = [
        ...(this.auditGridData || []),
        ...(
          ((response.result || {})[WidgetTypeConstants.GRID] || {}).data || []
        ).map(transformAuditForList),
      ]
      this.loading = false
      if (response.result.error) {
        this.error = response.result.error
        if (response.result.queryMeta.progress >= 100) {
          this.stopHeartbeat()
          return
        }
      }

      if (this.currentBatch === 1) {
        this.sendActiveSessionEvent()
        // start active session event
        this.scheduleUpdate()
        // ask first 50 records
      }
      if (response.result.queryMeta.progress < 100) {
        if (this.isPaused) {
          return
        }
        setTimeout(() => {
          this.$nextTick(() => {
            this.requestNextBatch()
          })
        }, 100)
      }
    },
    async requestNextBatch() {
      this.currentBatch++
      this.startAuditIncremental(this.__parentQueryId)
    },
    abortQuery() {
      if (this.__parentQueryId) {
        this.queryProgress = null
        this.isPaused = true
        Bus.$emit('server:event', {
          'event.type': this.$constants.UI_WIDGET_ABORT_EVENT,
          'event.context': {
            'query.id': this.__parentQueryId,
          },
        })
      }
    },
    sendActiveSessionEvent() {
      if (!this.__parentQueryId) {
        return
      }
      Bus.$emit('server:event', {
        'event.type': this.$constants.UI_WIDGET_ACTIVE_SESSION,
        'event.context': {
          'query.id': this.__parentQueryId,
          [this.$constants.UI_EVENT_UUID]: this.guid,
        },
      })
    },
    scheduleUpdate() {
      this.stopHeartbeat()
      this.__streamingTimer = setInterval(this.sendActiveSessionEvent, 10000)
    },
    stopHeartbeat() {
      if (this.__streamingTimer) {
        clearInterval(this.__streamingTimer)
        this.__streamingTimer = null
      }
    },
    requestChartData() {
      this.pieChartData = null
      this.historicChartData = null
      this.requestPieChartData()
      this.requestAuditHistoricChartData()
    },
    requestPieChartData() {
      getPieChartByStatus(this.selectedAuditTimeLine).then((data) => {
        this.pieChartData = Object.freeze(data)
      })
    },

    requestAuditHistoricChartData() {
      getAuditHistoricChart(this.selectedAuditTimeLine).then((data) => {
        this.historicChartData = Object.freeze(data)
      })
    },
    patchRecived(data) {
      const { users, operations } = tranformDataForUserAndOperation(data)

      this.userOptions = UniqBy(
        [
          ...this.userOptions,
          ...Object.freeze(users.map((user) => ({ text: user, key: user }))),
        ],
        'key'
      )
      this.availableOperations = Uniq([
        ...this.availableOperations,
        ...Object.freeze(operations),
      ])
    },

    requestUsersAndOperationsData() {
      this.userOptions = []
      this.availableOperations = []
      if (this.$refs.resultProviderRef) {
        this.$refs.resultProviderRef.requestData()
      }
    },
  },
}
</script>
<style lang="less" scoped>
.audit-detail {
  & .notification-message {
    display: inline-block;
    padding: 0 0 10px;
  }

  & .audit-table-message {
    & thead {
      & tr {
        & td:nth-child(1) {
          color: var(--neutral-dark);
          background: var(--mesasge-success) !important;
          border-color: var(--page-background-color);
        }

        & td:nth-child(2) {
          color: var(--neutral-dark);
          background: var(--mesasge-error) !important;
          border-color: var(--page-background-color);
        }
      }
    }

    & tbody {
      & tr {
        & td:nth-child(1) {
          color: var(--neutral-dark);
          background: var(--mesasge-success) !important;
          border-color: var(--page-background-color);
        }

        & td:nth-child(2) {
          color: var(--neutral-dark);
          background: var(--mesasge-error) !important;
          border-color: var(--page-background-color);
        }
      }
    }
  }
}
</style>
