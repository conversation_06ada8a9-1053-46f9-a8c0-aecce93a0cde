import { Module } from '@plugins/modular'
import Routes from './audit-routes'
import configs from './config'

class AuditModule extends Module {
  /**
   * @constructor
   * @param {[key: string]: string} config
   */
  constructor(config = configs) {
    /** string name this name is used to get module from module manager */
    super(config.name, config)
  }
  routes = Routes
}

export default AuditModule
