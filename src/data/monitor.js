/**
 * transform Monitor list received from server
 */
import Trim from 'lodash/trim'
import Uniq from 'lodash/uniq'
import Invert from 'lodash/invert'
import { generateId } from '@utils/id'
import Constants from '@constants'
import DiscoveryTypes from '@src/statics/discovery-type'
import {
  transformMapForClient,
  transformMapForServer,
} from '@modules/settings/network-discovery/helpers/network-discovery'

export const SEVERITY_MAP = {
  DOWN: 1,
  CRITICAL: 2,
  MAJOR: 3,
  WARNING: 4,
  CLEAR: 5,
  UP: 6,
  UNREACHABLE: 7,
  MAINTENANCE: 8,
  DISABLE: 9,
  UNKNOWN: 10,
  SUSPENDED: 11,
  // NONE: 10,
}

export const SEVERITY_NUMBER_MAP = Invert(SEVERITY_MAP)

export const colorMap = {
  down: '#e72b25',
  maintenance: '#099dd9',
  // unknown: '#D3D3D3',
  clear: '#89c540',
  major: '#f58518',
  warning: '#f5bc18',
  critical: '#f04e3e',
  unreachable: '#8d3abc',
  disable: '#808080',
  suspended: '#555555',
}

export const URL_TYPES = ['HTTP', 'HTTPS'].map((i) => ({
  text: i,
  value: i.toLowerCase(),
}))

export const URL_METHODS = ['GET', 'POST'].map((i) => ({ text: i, value: i }))

export const YES_NO_OPTIONS = [
  { value: 'Yes', text: 'YES' },
  { value: 'No', text: 'NO' },
]

export const AUTH_TYPES = [
  { text: 'None', key: 'no_auth' },
  { text: 'Basic', key: 'basic' },
  { text: 'NTLM', key: 'ntlm' },
  { text: 'Digest', key: 'digest' },
  { text: 'API Key', key: 'apikey' },
]

export const DNS_TYPES = [
  'A',
  'AAAA',
  'ALIAS',
  'CNAME',
  'MX',
  'NS',
  'PTR',
  'SOA',
  'SRV',
  'TXT',
].map((x) => ({ key: x, text: x }))

export const MONITOR_DISCOVERY_PARAMS = [
  {
    key: 'port',
    label: 'Port',
    paramName: 'port',
    clientKey: 'port',
    validationRules: { required: true, port: true },
    inputType: 'number',
    attrs: {
      min: 1,
      max: 65535,
      precision: 0,
    },
  },
  {
    key: 'retry.count',
    label: 'Retry Count',
    paramName: 'retry.count',
    clientKey: 'retryCount',
    validationRules: { required: true, min_value: 0, max_value: 3 },
    inputType: 'number',
    attrs: {
      min: 0,
      max: 3,
      precision: 0,
    },
  },
  {
    key: 'ping.check.status',
    label: 'Ping Check',
    paramName: 'ping.check.status',
    clientKey: 'pingCheck',
    inputType: 'boolean',
    translate(value) {
      return value === 'yes'
    },
    translateForServer(value) {
      return value ? 'yes' : 'no'
    },
  },
  {
    key: 'interface.discovery',
    label: 'Interface Discovery',
    paramName: 'interface.discovery',
    clientKey: 'interfaceDiscover',
    inputType: 'boolean',
    supportedMonitorTypes: DiscoveryTypes[Constants.NETWORK],
    translate(value) {
      return value === 'yes'
    },
    translateForServer(value) {
      return value ? 'yes' : 'no'
    },
  },
  {
    key: 'ss.bin.path',
    label: 'Bin Path',
    paramName: 'ss.bin.path',
    clientKey: 'ssbinPath',
    validationRules: { required: true },
  },
  {
    key: 'tenant.id',
    label: 'Tenant ID',
    paramName: 'tenant.id',
    clientKey: 'tenantId',
    validationRules: { required: true },
  },
  {
    key: 'url.protocol',
    label: 'URL Type',
    inputType: 'radio',
    paramName: 'url.protocol',
    clientKey: 'urlType',
    options: URL_TYPES,
    supportedMonitorTypes: [Constants.URL],
    validationRules: { required: true },
  },
  {
    key: 'url.method',
    label: 'URL Method',
    inputType: 'radio',
    paramName: 'url.method',
    clientKey: 'urlMethod',
    options: URL_METHODS,
    supportedMonitorTypes: [Constants.URL],
    validationRules: { required: true },
  },
  {
    key: 'url.json',
    label: 'JSON URL',
    paramName: 'url.json',
    inputType: 'radio',
    options: YES_NO_OPTIONS,
    clientKey: 'jsonUrl',
    supportedMonitorTypes: [Constants.URL],
    validationRules: { required: true },
  },
  {
    key: 'url.content.search.keyword',
    label: 'URL Content',
    paramName: 'url.content.search.keyword',
    clientKey: 'urlContent',
    supportedMonitorTypes: [Constants.URL],
  },
  {
    key: 'url.authentication.type',
    label: 'Authentication Type',
    paramName: 'url.authentication.type',
    clientKey: 'authType',
    inputType: 'dropdown',
    options: AUTH_TYPES,
  },
  {
    key: 'url.endpoint',
    label: 'URL Endpoint',
    paramName: 'url.endpoint',
    clientKey: 'urlEndpoint',
    supportedMonitorTypes: [Constants.URL],
  },
  {
    key: 'url.headers',
    label: 'Headers',
    paramName: 'url.headers',
    clientKey: 'headers',
    inputType: 'multiFormItems',
    attrs: {
      name: 'Header name',
      value: 'Header value',
      buttonText: 'Add Header',
      inputName: 'header-name',
      inputValue: 'header-value',
    },
    supportedMonitorTypes: [Constants.URL],
    translate: transformMapForClient,
    translateForServer: transformMapForServer,
    size: 12,
  },
  {
    key: 'url.parameters',
    label: 'Parameters',
    paramName: 'url.parameters',
    clientKey: 'parameters',
    inputType: 'multiFormItems',
    attrs: {
      name: 'Parameters name',
      value: 'Parameters value',
      buttonText: 'Add Parameter',
      inputName: 'parameter-name',
      inputValue: 'parameter-value',
    },
    supportedMonitorTypes: [Constants.URL],
    translate: transformMapForClient,
    translateForServer: transformMapForServer,
    size: 12,
  },
  {
    key: 'username',
    label: 'User Name',
    inputType: 'username',
    paramName: 'username',
    clientKey: 'username',
    supportedMonitorTypes: [Constants.RADIUS, Constants.FTP],
    validationRules: { required: true },
  },
  {
    key: 'password',
    label: 'Password',
    paramName: 'password',
    clientKey: 'password',
    inputType: 'password',
    supportedMonitorTypes: [Constants.RADIUS, Constants.FTP],
  },
  {
    key: 'radius.secret',
    label: 'RADIUS Secret',
    paramName: 'radius.secret',
    clientKey: 'radiusSecret',
    inputType: 'password',
    supportedMonitorTypes: [Constants.RADIUS],
    validationRules: { required: true },
  },
  {
    key: 'dns.lookup.address',
    label: 'Lookup Address',
    paramName: 'dns.lookup.address',
    clientKey: 'lookupAddress',
    supportedMonitorTypes: [Constants.DNS],
    validationRules: { required: true },
    attrs: {
      placeholder: 'eg. mindarraysystem.com',
    },
  },
  {
    key: 'dns.record.type',
    label: 'DNS Type',
    paramName: 'dns.record.type',
    clientKey: 'dnsRecordType',
    inputType: 'dropdown',
    options: DNS_TYPES,
    supportedMonitorTypes: [Constants.DNS],
    validationRules: { required: true },
  },
]

function transformMonitorDiscoveryParams(monitor) {
  try {
    const context = monitor['object.context']
    const contextKeys = Object.keys(context)
    const valueMap = {}
    const monitorType = monitor['object.type']
    MONITOR_DISCOVERY_PARAMS.forEach((field) => {
      if (
        contextKeys.indexOf(field.paramName) >= 0 ||
        (field.supportedMonitorTypes &&
          field.supportedMonitorTypes.indexOf(monitorType) >= 0)
      ) {
        if (field.translate) {
          valueMap[field.clientKey] = field.translate(context[field.paramName])
        } else {
          valueMap[field.clientKey] = context[field.paramName]
        }
      }
    })
    return valueMap
  } catch (e) {
    return {}
  }
}

function transformMonitorDiscoveryParamsForServer(monitor) {
  const params = {}
  const clientKeys = Object.keys(monitor.discoveryParams || {})
  if (clientKeys.length) {
    const values = monitor.discoveryParams
    params['object.context'] = {}
    clientKeys.forEach((clientKey) => {
      const field = MONITOR_DISCOVERY_PARAMS.find(
        (f) => f.clientKey === clientKey
      )
      if (field && clientKey in values) {
        if (Trim(values[clientKey])) {
          params['object.context'][field.paramName] = field.translateForServer
            ? field.translateForServer(values[clientKey])
            : values[clientKey]
        }
      }
    })
  }
  return params
}

export function transformMonitorForSelection(monitor) {
  return {
    id: monitor['config.object.id'] || monitor[Constants.ID_PROPERTY],
    name: monitor['object.name'],
    ip: monitor['object.ip'],
    groups: monitor['object.groups'],
    type: monitor['object.type'],
    severity: monitor.severity || 'UNKNOWN',
    remoteMethod: monitor['object.discovery.method'],
    snmpDeviceCatalogId: monitor['object.snmp.device.catalog'],
    isAgent: monitor['object.discovery.method'] === Constants.AGENT_METHOD,
  }
}

export function transformMonitorForList(monitor) {
  return {
    id: monitor['config.object.id'] || monitor[Constants.ID_PROPERTY],
    status: monitor['object.state'],
    name:
      monitor['object.category'] === Constants.SERVICE_CHECK
        ? monitor['object.target']
        : monitor['object.name'],
    serviceCheckName: monitor['object.name'],
    ip: monitor['object.ip'],
    objectId: monitor['object.id'],
    host: monitor['object.host'],
    groups: monitor['object.groups'],
    agent: monitor['object.agent'],
    tags: monitor['object.tags'] || [],
    tagsStr: (monitor['object.tags'] || []).join(','),
    systemTags: monitor['object.tags'] || [],
    pollingFailNotification:
      monitor['object.monitor.polling.failed.notification.status'] === 'yes',
    emailRecipients: monitor['object.email.notification.recipients'] || [],
    smsRecipients: monitor['object.sms.notification.recipients'] || [],
    notifyEvery:
      monitor['object.monitor.polling.failed.renotification.timer.seconds'] ||
      1800,
    oid: monitor['object.system.oid'],
    customMonitoringFields: monitor['object.custom.fields']
      ? transformCustomMonitoringFieldsClient(monitor['object.custom.fields'])
      : [],
    type: monitor['object.type'],
    category: monitor['object.category'],
    apps: Uniq((monitor.apps || []).map((a) => a['metric.type'])),
    appsSearchKey: Uniq(monitor.apps || []).join(','),
    scheduled: monitor['object.scheduler'],
    scheduleCount: monitor['object.maintenance.scheduler.count'] || 0,
    credentialProfileId: monitor['credential.profile.id'],
    rpe: monitor['object.event.processors'],
    monitorHourProfile: monitor['object.business.hour.profile'],
    logStatus: monitor['object.log.status'],
    flowStatus: monitor['object.flow.status'],
    ...(monitor['object.custom.fields'] ? monitor['object.custom.fields'] : {}),
    discoveryParams: {
      ...transformMonitorDiscoveryParams(monitor),
    },
    region: monitor['object.resource.group'] || monitor['object.region'],
    accountId: monitor['object.account.id'],
    severity: monitor.severity || 'UNKNOWN',
    severityNumber: SEVERITY_MAP[monitor.severity || 'UNKNOWN'],
    target: monitor['object.target'],
    port: (monitor['object.context'] || {}).port,
    serviceCheckTarget: `${monitor['object.target']}${
      monitor['object.context'] && monitor['object.context'].port
        ? `:${monitor['object.context'].port}`
        : ''
    }`,
    appProcesses: Object.keys(monitor['app.process'] || {}).reduce(
      (prev, app) => ({
        ...prev,
        [app]: {
          app,
          name: Object.keys(monitor['app.process'][app])[0],
          type: Object.values(monitor['app.process'][app])[0],
        },
      }),
      {}
    ),
    remoteMethod: monitor['object.discovery.method'],
    cloudType: monitor['object.vendor'],
    isAgent: monitor['object.discovery.method'] === Constants.AGENT_METHOD,
    agentHealthType: monitor['agent.health.diagnosis.type'],
    instanceCount: monitor['object.instances'],
    creationTime: monitor['object.creation.time'],
  }
}

export function transformMonitorForLocalDb(monitor, options = {}) {
  return {
    id: monitor.id,
    name: monitor['object.name'],
    status: monitor['object.state'],
    ip: monitor['object.ip'],
    objectId: monitor['object.id'],
    host: monitor['object.host'],
    groups: monitor['object.groups'] || [],
    agent: monitor['object.agent'],
    isAgent: Boolean(monitor['object.agent']),
    makeModel: monitor['object.make.model'],
    type: monitor['object.type'],
    category: monitor['object.category'],
    target: monitor['object.target'],
    severity: monitor.severity || 'UNKNOWN',
    severityNumber: SEVERITY_MAP[monitor.severity || 'UNKNOWN'],
    apps: Uniq((monitor.apps || []).map((a) => a['metric.type'])),
    appsSearchKey: Uniq((monitor.apps || []).map((a) => a['metric.type'])).join(
      ','
    ),
    appsDiscoveredByAgent: Uniq(
      (monitor.apps || [])
        .filter(
          (app) => app['metric.discovery.method'] === Constants.AGENT_METHOD
        )
        .map((a) => a['metric.type'])
    ),
    vms: Uniq((monitor.vms || []).map((v) => v.instance)),
    instanceIpMap: monitor.vms || monitor['access.points'],
    tags: monitor['object.tags'] || [],
    tagsStr: (monitor['object.tags'] || []).join(','),
    systemTags: monitor['object.tags'] || [],
    cloudType: monitor['object.vendor'],
    vendor: monitor['object.vendor'],
    accessPoints: (monitor['access.points'] || []).map((ap) => ap.instance),
    appProcesses: Object.keys(monitor['app.process'] || {}).reduce(
      (prev, app) => ({
        ...prev,
        [app]: {
          app,
          name: Object.keys(monitor['app.process'][app])[0],
          type: Object.values(monitor['app.process'][app])[0],
        },
      }),
      {}
    ),
    agentHealthType: monitor['agent.health.diagnosis.type'],
    ...(options?.transformTag
      ? {
          tags: monitor['object.tags'] || [],
          tagsStr: (monitor['object.tags'] || []).join(','),
          systemTags: monitor['object.tags'] || [],
        }
      : {}),
  }
}

function transformCustomMonitoringFieldsClient(fields = {}) {
  return Object.keys(fields).map((field) => ({
    key: generateId(),
    field: parseInt(field),
    name: fields[field],
  }))
}

function transformCustomMonitoringFieldsServer(monitor) {
  return (monitor.customMonitoringFields || []).reduce((obj, value) => {
    obj[value.field] = value.name
    return obj
  }, {})
}

export function transformMonitorForServer(monitor) {
  return {
    [Constants.ID_PROPERTY]: monitor._id,
    'object.name': Trim(monitor['name']),
    ...(monitor.category === Constants.SERVICE_CHECK
      ? { 'object.name': Trim(monitor['serviceCheckName']) }
      : {}),
    ...(monitor.host ? { 'object.host': Trim(monitor['host']) } : {}),
    ...(monitor.ip ? { 'object.ip': Trim(monitor['ip']) } : {}),
    'object.groups': monitor['groups'],
    'object.state': monitor['status'],
    'object.event.processors': monitor['rpe'],
    ...(monitor.credentialProfileId
      ? { 'credential.profile.id': Trim(monitor['credentialProfileId']) }
      : {}),
    ...((monitor.customMonitoringFields || []).length
      ? {
          'object.custom.fields':
            transformCustomMonitoringFieldsServer(monitor),
        }
      : { 'object.custom.fields': {} }),
    'object.business.hour.profile': monitor['monitorHourProfile'],
    'object.log.status': monitor['logStatus'],
    'object.flow.status': monitor['flowStatus'],
    'object.scheduler.status': monitor['scheduled'],
    'object.tags': monitor.tags || [],
    'object.sms.notification.recipients': monitor.smsRecipients || [],
    'object.email.notification.recipients': monitor.emailRecipients || [],
    'object.monitor.polling.failed.renotification.timer.seconds':
      monitor.notifyEvery,
    'object.monitor.polling.failed.notification.status':
      monitor.pollingFailNotification ? 'yes' : 'no',
    ...(monitor.accountId
      ? { 'object.account.id': Trim(monitor['accountId']) }
      : {}),
    ...(monitor.instanceId
      ? { 'object.instance.id': Trim(monitor['instanceId']) }
      : {}),
    ...(monitor.target ? { 'object.target': Trim(monitor['target']) } : {}),
    ...transformMonitorDiscoveryParamsForServer(monitor),
  }
}
