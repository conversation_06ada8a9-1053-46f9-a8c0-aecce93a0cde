import SortBy from 'lodash/sortBy'
import Trim from 'lodash/trim'
import Omit from 'lodash/omit'

export function transformRecursive(
  collection,
  transformFn,
  childKey = 'child',
  orderingKey = undefined,
  level = 1
) {
  const transformedColletion = []
  for (let i = 0; i < collection.length; i++) {
    const c = transformFn(collection[i], level)
    if (c && collection[i][childKey]) {
      c[childKey] = transformRecursive(
        orderingKey
          ? SortBy(collection[i][childKey], orderingKey)
          : collection[i][childKey],
        transformFn,
        childKey,
        orderingKey,
        level + 1
      )
    }
    if (c) {
      transformedColletion.push(c)
    }
  }
  return transformedColletion
}

export function findValuePath(
  collection,
  value,
  childKey = 'child',
  key = 'id',
  matcherFn = undefined
) {
  const currentPath = []
  let found = false

  function search(haystack) {
    for (let i = 0; i < haystack.length; i++) {
      currentPath.push(haystack[i][key])
      if (matcherFn) {
        if (matcherFn(haystack[i])) {
          found = true
          break
        }
      }
      if (haystack[i][key] === value) {
        found = true
        break
      }
      if (
        haystack[i][childKey] &&
        Array.isArray(haystack[i][childKey]) &&
        (haystack[i][childKey] || []).length
      ) {
        search(haystack[i][childKey])
        if (found) break
      }
      currentPath.pop()
    }
  }

  search(collection)
  return currentPath
}

export function findValueObject(
  collection,
  value,
  childKey = 'child',
  key = 'id',
  matcherFn = undefined
) {
  let foundItem

  function search(haystack) {
    for (let i = 0; i < haystack.length; i++) {
      if (matcherFn) {
        if (matcherFn(haystack[i])) {
          foundItem = haystack[i]
          break
        }
      }
      if (haystack[i][key] === value) {
        foundItem = haystack[i]
        break
      }
      if (
        haystack[i][childKey] &&
        Array.isArray(haystack[i][childKey]) &&
        haystack[i][childKey].length
      ) {
        search(haystack[i][childKey])
        if (foundItem) break
      }
    }
  }
  search(collection)
  return foundItem
}

export function flattenRecursive(collection, childKey = 'child', arr = []) {
  for (let i = 0; i < collection.length; i++) {
    arr.push(Omit(collection[i], [childKey]))
    if (collection[i][childKey]) {
      flattenRecursive(collection[i][childKey], childKey, arr)
    }
  }
  return arr
}

export function buildHierarchy(
  collection,
  childKey = 'child',
  key = 'id',
  parentKeyName = 'parentId'
) {
  const topLevelItems = collection.filter((c) => !c[parentKeyName])
  const attachChildren = (item) => {
    return {
      ...item,
      [childKey]: collection
        .filter((c) =>
          (Array.isArray(c[parentKeyName])
            ? c[parentKeyName]
            : [c[parentKeyName]]
          ).includes(item[key])
        )
        .map(attachChildren),
    }
  }
  return topLevelItems.map(attachChildren)
}

export function getRecursiveLevel(
  collection,
  childKey,
  maxLevel,
  currentLevel = 0
) {
  if (maxLevel !== 0 && currentLevel > maxLevel) {
    return false
  }
  return collection
    .map((item) => {
      return {
        ...item,
        [childKey]: getRecursiveLevel(
          item[childKey] || [],
          maxLevel,
          currentLevel + 1
        ),
      }
    })
    .filter(Boolean)
}

export function searchRecursive(
  searchTerm,
  collection,
  childKey = 'child',
  searchKey = 'name',
  shouldIncludeUnmatchedChildren = true,
  filterFn = undefined
) {
  return collection
    .map(function search(item) {
      const children =
        item[childKey] && item[childKey].length
          ? searchRecursive(
              Trim(searchTerm),
              item[childKey],
              childKey,
              searchKey,
              shouldIncludeUnmatchedChildren,
              filterFn
            )
          : false
      let shouldInclude = children && children.length
      if (!shouldInclude) {
        if (filterFn) {
          shouldInclude = filterFn(item, Trim(searchTerm))
        } else {
          shouldInclude = item[searchKey].toLowerCase().indexOf(searchTerm) >= 0
        }
      }
      if (shouldInclude) {
        return {
          ...item,
          [childKey]: (children || []).length
            ? children
            : shouldIncludeUnmatchedChildren
            ? item[childKey]
            : [],
        }
      }
      return false
    })
    .filter(Boolean)
}

export function searchByIdRecursive(
  ids = [],
  collection,
  childKey = 'child',
  searchKey = 'id'
) {
  return collection
    .map(function search(item) {
      const children =
        item[childKey] && item[childKey].length
          ? searchByIdRecursive(ids, item[childKey], childKey, searchKey)
          : false
      if ((children && children.length) || ids.includes(item[searchKey])) {
        return {
          ...item,
          [childKey]: (children || []).length ? children : item[childKey],
        }
      }
      return false
    })
    .filter(Boolean)
}

export function removeItemFromList(
  item,
  collection,
  key = 'id',
  childKey = 'child'
) {
  return collection
    .map(function updatedItem(loopItem) {
      if (loopItem[key] === item[key]) {
        return null
      }
      return {
        ...loopItem,
        ...(loopItem[childKey]
          ? {
              [childKey]: loopItem[childKey].map(updatedItem).filter(Boolean),
            }
          : {}),
      }
    })
    .filter(Boolean)
}

export function changeParentForItem(
  item,
  parentId,
  collection,
  parentKey = 'parentId',
  key = 'id',
  childKey = 'child'
) {
  const removedCollection = removeItemFromList(item, collection, key, childKey)
  if (!parentId) {
    return [...removedCollection, { ...item, parentId: undefined }]
  }
  return removedCollection.map(function checkCollection(loopItem) {
    return {
      ...loopItem,
      ...(loopItem[key] === parentId ? { expanded: true } : {}),
      ...(loopItem[childKey]
        ? {
            [childKey]:
              loopItem[key] === parentId
                ? [...loopItem[childKey], { ...item, [parentKey]: parentId }]
                : loopItem[childKey].map(checkCollection),
          }
        : {}),
    }
  })
}

export function getDepth(obj, childKey = 'child') {
  let depth = 1
  const getDepthCount = (obj) => {
    if ((obj[childKey] || []).length) {
      depth++
      return obj[childKey].forEach(getDepthCount)
    }
  }
  getDepthCount(obj)
  return depth
}

export async function transformRecursiveAsync(
  collection,
  transformFn,
  childKey = 'child',
  orderingKey = undefined,
  level = 1,
  additionalTransformFn
) {
  const transformedColletion = []
  for (let i = 0; i < collection.length; i++) {
    let c = await transformFn(collection[i], level)
    if (c && collection[i][childKey]) {
      const children = await transformRecursiveAsync(
        orderingKey
          ? SortBy(collection[i][childKey], orderingKey)
          : collection[i][childKey],
        transformFn,
        childKey,
        orderingKey,
        level + 1,
        additionalTransformFn
      )
      c[childKey] = (children || []).concat(c[childKey] || [])
      if (additionalTransformFn) {
        c = additionalTransformFn(c)
      }
    }
    if (c) {
      transformedColletion.push(c)
    }
  }
  return transformedColletion
}
