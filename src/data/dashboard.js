import Omit from 'lodash/omit'
import Constants from '@constants'
import { generateId } from '@utils/id'

function dashboardTabForClient(tab) {
  return {
    dashboardId: tab['tab.id'],
    text: tab.text,
    isUserCreated: tab['user.created'] === 'yes',
  }
}

function dashboardTabForServer(tab) {
  return {
    'tab.id': tab.dashboardId,
    text: tab.text,
    'user.created': tab.isUserCreated ? 'yes' : 'no',
  }
}

function dashboardOptionsForClient(options = {}) {
  return {
    ...options,
    ...('title' in options ? { title: options.title } : {}),
    ...('show.action' in options
      ? { showWidgetAction: options['show.action'] !== 'no' }
      : {}),
    ...('show.title' in options
      ? { showTitle: options['show.title'] !== 'no' }
      : {}),
  }
}

function dashboardOptionsForServer(options = {}) {
  return {
    ...('title' in options ? { title: options.title } : {}),
    ...('showWidgetAction' in options
      ? { 'show.action': options.showWidgetAction === false ? 'no' : 'yes' }
      : {}),
    ...('showTitle' in options
      ? { 'show.title': options.showTitle === false ? 'no' : 'yes' }
      : {}),
  }
}

function transformDashlet(dashlet) {
  if (dashlet['sub.widgets']) {
    return {
      x: dashlet.x,
      y: dashlet.y,
      h: dashlet.h,
      w: dashlet.w,
      i: dashlet.i || generateId(),
      options: dashboardOptionsForClient(dashlet.options),
      children: dashlet['sub.widgets'].map(transformDashlet),
    }
  }
  return {
    x: dashlet.x,
    y: dashlet.y,
    h: dashlet.h,
    w: dashlet.w,
    id: dashlet.id,
    i: dashlet.i || generateId(),
    options: dashboardOptionsForClient(dashlet.options),
  }
}

function makeClientTranslator(containerType = 'dashboard', excludedKeys = []) {
  return (dashboard) => {
    const transformed = {
      id: dashboard[Constants.ID_PROPERTY],
      name: dashboard[`${containerType}.name`],
      category: dashboard[`${containerType}.category`],
      createdAt: Math.round(
        (dashboard[`${containerType}.context`] || {})[
          `${containerType}.create.time`
        ] / 1000
      ),
      updatedAt: Math.round(
        (dashboard[`${containerType}.context`] || {})[
          `${containerType}.modification.time`
        ] / 1000
      ),
      security: dashboard[`${containerType}.access.type`],
      users:
        (dashboard[`${containerType}.context`] || {})[
          `${containerType}.users`
        ] || [],
      createdBy: (dashboard[`${containerType}.context`] || {})[
        `${containerType}.create.by`
      ],
      dashboardTabs: (dashboard[`${containerType}.tabs`] || [])
        .map(dashboardTabForClient)
        .concat(
          (dashboard['custom.tabs'] || []).length
            ? dashboard['custom.tabs'].map(dashboardTabForClient)
            : []
        ),
      widgets: (
        (dashboard[`${containerType}.context`] || {})[
          `${containerType}.widgets`
        ] ||
        dashboard[`${containerType}.widgets`] ||
        []
      ).map(transformDashlet),
      style: {
        fontSize: (dashboard.style || {})['font.size'] || 'medium',
        horizontalGap: (dashboard.style || {})['h.gap'] || 8,
        verticalGap: (dashboard.style || {})['v.gap'] || 8,
        rowHeight: (dashboard.style || {})['row.height'] || 50,
      },
      canEdit: dashboard._type !== '0',
    }

    return Omit(transformed, excludedKeys)
  }
}

function transformDashletForServer(dashlet, forTemplate = false) {
  if (dashlet.children) {
    return {
      x: dashlet.x,
      y: dashlet.y,
      h: dashlet.h,
      w: dashlet.w,
      ...(forTemplate ? {} : { i: dashlet.i }),
      options: dashboardOptionsForClient(dashlet.options),
      'sub.widgets': dashlet.children.map(transformDashletForServer),
    }
  }
  return {
    x: dashlet.x,
    y: dashlet.y,
    h: dashlet.h,
    w: dashlet.w,
    id: dashlet.id,
    ...(forTemplate ? {} : { i: dashlet.i }),
    options: dashboardOptionsForServer(dashlet.options),
  }
}

function makeServerTranslator(containerType = 'dashboard', excludedKeys = []) {
  return (dashboard) => {
    const transformed = {
      id: dashboard[Constants.ID_PROPERTY],
      [`${containerType}.name`]: dashboard.name,
      [`${containerType}.category`]: dashboard.category,
      [`${containerType}.access.type`]: dashboard.security,
      [`${containerType}.users`]: dashboard.users || [],
      [`${containerType}.tabs`]: (dashboard.dashboardTabs || [])
        .filter((i) => !i.isUserCreated)
        .map(dashboardTabForServer),

      ...((dashboard.dashboardTabs || []).find((t) => t.isUserCreated)
        ? {
            'custom.tabs': (dashboard.dashboardTabs || [])
              .filter((t) => t.isUserCreated)
              .map(dashboardTabForServer),
          }
        : { 'custom.tabs': [] }),

      style: {
        'font.size': dashboard.style.fontSize,
        'h.gap': dashboard.style.horizontalGap,
        'v.gap': dashboard.style.verticalGap,
        'row.height': dashboard.style.rowHeight,
      },
      ...(containerType === 'template'
        ? {
            [`${containerType}.widgets`]: (dashboard.widgets || []).map((i) =>
              transformDashletForServer(i, containerType === 'template')
            ),
            [`${containerType}.users`]: dashboard.users,
          }
        : {
            [`${containerType}.context`]: {
              [`${containerType}.widgets`]: (dashboard.widgets || []).map((i) =>
                transformDashletForServer(i, containerType === 'template')
              ),
              [`${containerType}.users`]: dashboard.users,
            },
          }),
    }

    return Omit(transformed, excludedKeys)
  }
}

const DASHBOARD_FNS = {
  client: makeClientTranslator('dashboard'),
  server: makeServerTranslator('dashboard'),
}

const TEMPLATE_FNS = {
  client: makeClientTranslator('template', [
    'createdBy',
    'createdAt',
    'security',
    'users',
  ]),
  server: makeServerTranslator('template', [
    'template.users',
    'template.visibility',
  ]),
}

export function transformDashboard(dashboard) {
  return DASHBOARD_FNS.client(dashboard)
}

export function transformDashboardForServer(dashboard) {
  return DASHBOARD_FNS.server(dashboard)
}

export function transformTemplate(dashboard) {
  return TEMPLATE_FNS.client(dashboard)
}

export function transformTemplateForServer(dashboard) {
  return TEMPLATE_FNS.server(dashboard)
}
