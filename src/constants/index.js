import permissions from './permissions'
import monitorTypes from './monitor-types'

export default {
  // base constants
  ID_PROPERTY: 'id',
  EVENT_SUCCESS_STATUS: 'succeed',
  EVENT_FAIL_STATUS: 'fail',
  EVENT_PENDING_STATUS: 'pending',
  EVENT_TIMEOUT_STATUS: 'timeout',
  EVENT_ABORT_STATUS: 'abort',
  EVENT_RUNNING_STATUS: 'running',
  EVENT_UNKNOWN_STATUS: 'unknown',
  EVENT_FAILED_STATUS: 'failed',
  EVENT_SUCCEDED_STATUS: 'succeeded',

  STATUS_UP: 'Up',
  STATUS_DOWN: 'Down',
  STATUS_CLEAR: 'Clear',
  STATUS_UNKNOWN: 'Unknown',
  STATUS_MAINTENANCE: 'Maintenance',
  STATUS_DISABLE: 'Disable',
  STATE_DISABLE: 'DISABLE',
  STATE_ENABLE: 'ENABLE',
  STATE_SUSPEND: 'SUSPEND',
  STATE_INVALID: 'INVALID',
  <PERSON><PERSON><PERSON><PERSON>: 'Monitor',
  AGENT: 'Agent',
  USER: 'User',
  DISCOVERY: 'Discovery Profile',
  METIC_PLUGIN: 'Metric',
  POLICY: 'Policy',
  RUNBOOK: 'Runbook Plugin',
  EVENT_POLICY: 'Event Policy',
  AUDIT_POLICY: 'audit.policies',

  // discovery type
  SERVER: 'Server',
  CLOUD: 'Cloud',
  NETWORK: 'Network',
  SDN: 'SDN',
  VIRTUALIZATION: 'Virtualization',
  HYPERCONVERGED_INFRASTRUCTURE: 'HCI',
  STORAGE: 'Storage',
  SERVICE_CHECK: 'Service Check',
  WIRELESS: 'Wireless',
  WAN_LINK: 'WAN Link',
  OTHER: 'Other',
  CONTAINER: 'Container',

  // policy

  LOG: 'Log',
  FLOW: 'Flow',
  TRAP: 'Trap',
  CORRELATED_ALERTS: 'Correlated Alerts',

  // database
  DATABASE: 'Database',

  // protocol
  POWERSHELL: 'Powershell',
  SNMP_V12: 'SNMP V1/V2c',
  SNMP_V3: 'SNMP V3',
  SSH: 'SSH',
  JDBC: 'JDBC',
  HTTP: 'HTTP',
  HTTPS: 'HTTPS',
  JMX: 'JMX',
  JMS: 'JMS',
  PYTHON: 'Python',
  HTTP_HTTPS: 'HTTP/HTTPS',
  SNMP: 'SNMP',

  NCM: 'NCM',
  CONFIG_GROUP: 'config.metric',
  NETROUTE: 'NetRoute',
  SOURCE_TO_DESTINATION: 'Source-to-destination',
  HOP_TO_HOP: 'Hop-by-hop',

  // states
  STATE_RUNNING: 'Running',
  STATE_NOT_RUNNING: 'Not Running',
  STATE_IN_QUEUE: 'In Queue',
  STATE_IN_PROGRESS: 'In Progress',

  // events
  CREDENTIAL_PROFILE_TEST_EVENT: 'ui.action.credential.profile.test',
  REDISCOVER_PROGRESS_EVENT: 'ui.notification.rediscover.progress',
  REDISCOVER_EVENT: 'ui.event.rediscover',
  REDISCOVER_STOP_SUB_EVENT: 'rediscover.stop',
  WAN_REDISCOVER_EVENT: 'ui.action.rediscover',
  CONTAINER_REDISCOVER_EVENT: 'ui.action.rediscover',
  REDISCOVER_STOP_EVENT: 'ui.action.rediscover.stop',
  REDISCOVER_START_EVENT: 'ui.action.rediscover.start',
  REDISCOVER_ERROR_EVENT: 'ui.notification.rediscover.error',
  TOPOLOGY_ERROR_EVENT: 'ui.notification.topology.error',
  REDISCOVER_APP_PROGRESS_EVENT: 'ui.notification.app.discovery.progress',
  DISCOVERY_OBJECT_PROVISION_EVENT: 'ui.notification.object.provision.progress',
  DISCOVERY_INSTANCE_PROVISION_EVENT:
    'ui.notification.rediscover.provision.progress',
  UI_CONFIG_EVENT: 'ui.notification.config.change',
  UI_EVENT_CSV_EXPORT: 'ui.notification.csv.export.ready',
  UI_NOTIFICATION_DIAGNOSTICS_EXPORT_READY:
    'ui.notification.diagnostic.zip.available',

  UI_EVENT_MAIL_SERVICE_HEALTH: 'ui.notification.mail.service.health',
  UI_EVENT_SMS_SERVICE_HEALTH: 'ui.notification.sms.service.health',
  UI_EVENT_SERVICE_HEALTH: 'ui.notification.service.health',
  UI_EVENT_AGENT: 'ui.notification.agent',
  UI_EVENT_AGENT_DOWNLOAD_LOG: 'agent.download.log',
  UI_EVENT_MONITOR_VIEW_MORE: 'ui.monitor.view.more',

  UI_EVENT_STREAMING_START: 'ui.action.streaming.start',
  UI_EVENT_STREAMING_STOP: 'ui.action.streaming.stop',
  UI_EVENT_STREAMING_NOTIFICATION: 'ui.notification.streaming.broadcast',
  RULE_TAG_ASSIGN_REMOVE_EVENT: 'ui.action.tag.rule.run',

  UI_EVENT_UUID: 'ui.event.uuid',

  EVENT_LOG_EXPORT: 'event.log.export',

  FLOW_MAX_RESULT_BY: 4,
  LOG_MAX_RESULT_BY: 4,

  // local db objects events
  EVENT_MONITOR_DB_CHANGED: 'monitor.db.changed',
  UI_EVENT_OBJECTS: 'config.query',
  UI_EVENT_OBJECTS_INIT: 'ui.request.user.init',
  UI_EVENT_HEART_BEAT: 'user.ping',

  // local db counter severity events
  EVENT_SEVERITY_COUNTER_DB_CHANGED: 'counter.db.changed',
  EVENT_STATUS_COUNTER_DB_CHANGED: 'status.db.changed',
  EVENT_STATUS_DB_CHANGED: 'object.status.db.changed',
  UI_EVENT_COUNTER_SEVERITY: 'ui.event.counters.severity',
  UI_EVENT_APPLICATION_SEVERITY: 'ui.event.object.application.severity',
  UI_EVENT_SEND_USERAGENT: 'ui.action.user.navigate',

  // dashboard events
  UI_ACTION_INSTANCE_SEVERITY_FETCH: 'ui.action.instance.severity.query',
  UI_WIDGET_QUERY_ID_EVENT: 'ui.notification.widget.query.id',
  UI_WIDGET_RESULT_EVENT: 'ui.action.visualization.render',
  UI_WIDGET_ABORT_EVENT: 'ui.action.visualization.abort',
  UI_WIDGET_ACTIVE_SESSION: 'ui.action.session.active',
  UI_WIDGET_INACTIVE_SESSION: 'ui.action.session.inactive',
  UI_ACTION_DISCOVERY_EXPORT: 'ui.action.discovery.result.export',
  WIDGET_RELOAD_EVENT: 'ui.event.widget.reload',
  NOTIFICATION_COUNT: 'ui.notification.user.notification',
  NOTIFICATION_CLEAR_COUNT: 'ui.notification.user.notification.clear',

  UI_EVENT_SEVERITY_CHANGE: 'ui.event.object.severity',
  UI_EVENT_COUNTER_SEVERITY_CHANGED: 'ui.event.counter.severity',
  EVENT_SEVERITY_UPDATED: 'object.severity.updated',
  EVENT_COUNTER_SEVERITY_UPDATED: 'counter.severity.updated',

  UI_EVENT_LAST_POLL_TIME: 'ui.action.metric.poll.timestamp.fetch',
  UI_EVENT_MONITOR_STATUS: 'ui.notification.object.status',

  METRIC_DB_CHANGED: 'metric.db.changed',

  REMOTE_METHOD: 'REMOTE',
  AGENT_METHOD: 'AGENT',

  TOPOLOGY_SCANNER_STOP_EVENT: 'ui.action.topology.stop',
  TOPOLOGY_SCANNER_START_EVENT: 'ui.action.topology.start',

  ALERT_NOTIFICATION: 'active.notification.query',
  ALERT_NOTIFICATION_CLEAR: 'ui.notification.active.notification.clear',
  ALERT_NOTIFICATION_COUNT: 'ui.notification.active.notification.query',

  // AI Events
  UI_METRIC_EXPLORER_AI_REQUEST: 'ui.event.metric.explorer.ai.request',

  // backup restore events
  DATABASE_RESTORE: 'database.restore',
  MOTADATA_UPGRADE: 'master.upgrade',

  // user logout event
  USER_LOGOUT: 'ui.notification.user.logout',

  USER_SESSION_UPDATE: 'ui.action.user.session.update',

  UI_ACTION_WIDGET_SHARE: 'ui.action.share',
  RULE_BASED_TAGS_PREVIEW_EVENT: 'ui.action.tag.rule.test',
  // severity
  DOWN: 'DOWN',
  MAJOR: 'MAJOR',
  CRITICAL: 'CRITICAL',
  WARNING: 'WARNING',
  CLEAR: 'CLEAR',
  UNREACHABLE: 'UNREACHABLE',
  UNKNOWN: 'UNKNOWN',
  MAINTENANCE: 'MAINTENANCE',
  DISABLE: 'DISABLE',
  UP: 'UP',
  SUSPENDED: 'SUSPENDED',

  // compliance severity
  HIGH: 'HIGH',
  MEDIUM: 'MEDIUM',
  LOW: 'LOW',
  INFO: 'INFO',

  // template events
  POLL_NOW_EVENT: 'ui.action.poll',

  // ERROR CODE FOR UPGRADE

  ERROR_CODE_NOT_COMPATIBLE: 'MD138',
  ERROR_CODE_UPGRADE_REQUIRED: 'MD139',
  ERROR_CODE_SUCCESS: 'MD000',

  UI_NOTIFICATION_DISK_UTILIZATION_EXCEED:
    'ui.notification.disk.utilization.exceed',

  UI_NOTIFICATION_PASSWORD_EXPIRY: 'ui.notification.password.expire',
  UI_NOTIFICATION_LICENSE_EXPIRE: 'ui.notification.license.expire',

  UI_NOTIFICATION_REFRESH_TOKEN_EXPIRE: 'ui.notification.refresh.token.expire',

  UI_ALERT_NOTIFICATIONS: 'severity.change',

  UI_NOTIFICATION_POLICY_FLAP_SOUND_NOTIFICATION:
    'ui.notification.sound.notification',

  SEPARATOR_WITH_SLASH: '``\\|\\|``',

  // permissions
  ...permissions,

  // monitor type constants
  ...monitorTypes,
}
