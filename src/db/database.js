import Loki from 'lokijs'

const db = new Loki('motadata.db')

export default db

// export default class Database extends Dexie {
//   constructor() {
//     super(process.env.VUE_APP_DB_NAME)

//     this.version(process.env.VUE_APP_DB_VERSION).stores({
//       objects:
//         'id,name,host,type,category,status,severity,remoteMethod,*groups,severityNumber,objectId,ip',
//       severity: 'id,entity,counter,instance,severity,category',
//       user: 'id,name,userName',
//       metrics: 'id,pluginId,pollTime,metricType',
//     })
//   }
// }
