import Moment from 'moment'
import 'moment-timezone'
import { userDateTimeFormat, userTimezone } from '@utils/user'

export default function datetime(value, format, useTime = true) {
  if (!value || value <= 0) {
    return ''
  }
  if (!format) {
    format = userDateTimeFormat()
  }
  if (!useTime) {
    format = format.replace(' hh:mm:ss A', '')
  }
  let offset = 0
  let timezone = userTimezone()
  if (timezone) {
    offset = Moment().tz(timezone).utcOffset() * 60
  }
  if (Moment.isMoment(value)) {
    if (timezone) {
      return Moment.tz(value.unix() * 1000, timezone || 'UTC').format(format)
    }
    return value.format(format)
  }
  if (/^\d+$/.test(value)) {
    if (timezone) {
      return Moment.tz(value * 1000, timezone || 'UTC').format(format)
    }
    return Moment.unix(value + offset).format(format)
  }
  return value
}
