export default function msToUnit(ms, decimals = 2) {
  // if (!bytes) {
  //   return bytes
  // }
  if (isNaN(ms)) {
    return ''
  }
  if (ms <= 0 || ms === null || ms === undefined) {
    return '0 ms'
  }
  let seconds = (ms / 1000).toFixed(decimals)
  let minutes = (ms / (1000 * 60)).toFixed(decimals)
  let hours = (ms / (1000 * 60 * 60)).toFixed(decimals)
  let days = (ms / (1000 * 60 * 60 * 24)).toFixed(decimals)
  let months = (ms / (1000 * 60 * 60 * 24 * 30)).toFixed(decimals)
  if (ms < 1000) {
    return `${ms} ms`
  }
  if (seconds < 60) {
    return seconds + ' Sec'
  } else if (minutes < 60) {
    return minutes + ' Min'
  } else if (hours < 24) {
    return hours + ' Hrs'
  } else if (days < 30) {
    return days + ' Days'
  } else {
    return months + ' Months'
  }
}
