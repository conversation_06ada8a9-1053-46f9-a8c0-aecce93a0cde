import Vue from 'vue'
import dateTime from './datetime'
import bytesPerSecToSize, { bytesToSize, bytesToSmallBps } from './bytes'
import duration from './duration'
import kFormatter from './number-format'
import hz from './hz'
import timeago from './timeago'

Vue.filter('datetime', dateTime)
Vue.filter('bytes', bytesToSize)
Vue.filter('bps', bytesPerSecToSize)
Vue.filter('smallbps', bytesToSmallBps)
Vue.filter('duration', duration)
Vue.filter('numberFormat', kFormatter)
Vue.filter('timeago', timeago)
Vue.filter('hz', hz)
