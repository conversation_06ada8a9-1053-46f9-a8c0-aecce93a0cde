---
to: "src/views/<%= h.inflection.dasherize(name) %>/index.vue"
---
<%
  const fileName = h.inflection.dasherize(name)
  const importName = h.inflection.camelize(fileName.replace(/-/g, '_'))
%>
<template>
  <Layout>
    <%= h.inflection.titleize(name.replace(/-/g, '_')) %>
  </Layout>
</template>

<script>

export default {
  name: '<%= importName %>',
  page: {
    title: '<%= importName %>',
    meta: [{ name: 'description', content: '<%= importName %>' }],
  }
}
</script>

<%

if (useStyles) { %>
<style lang="scss" module>
@import '~@design';
</style>
<% } %>
