---
to: src/state/modules/<%= h.inflection.dasherize(name) %>/helpers.js
---
import { createNamespacedHelpers } from 'vuex'

const { mapState, mapGetters, mapActions } = createNamespacedHelpers('<%= h.inflection.dasherize(name) %>')

export const <%= h.inflection.capitalize(name) %>Computed = {
  ...mapState({}),
  ...mapGetters([]),
}

export const <%= h.inflection.capitalize(name) %>Methods = mapActions([])
