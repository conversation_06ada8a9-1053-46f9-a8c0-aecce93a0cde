---
to: src/state/modules/<%= h.inflection.dasherize(name) %>/module.js
---
import { isLoggedIn } from '@utils/auth'
export const state = {}

export const getters = {}

export const mutations = {}

export const actions = {
  init({ dispatch }) {
    if (isLoggedIn()) {
      dispatch('fetch')
    }
  },

  fetch() {
    // perform any api request which should be called only when user is logged in
  },

  /**
   * destroy all states when user is logged out
   */
  destroy({ commit }) {
    // perform state clean up here when user is logged out
  },
}
