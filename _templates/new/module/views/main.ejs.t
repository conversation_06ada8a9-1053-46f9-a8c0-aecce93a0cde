---
to: src/modules/<%= h.inflection.dasherize(name) %>/views/main.vue
---
<%
  const fileName = h.inflection.dasherize(name)
  const importName = h.inflection.capitalize(fileName.replace(/-/g, '_'), true) + 'Module'
  const componentName = h.inflection.titleize(fileName, true).replace(/-/g, '') + 'Module'
%>
<template>
  <FlotoModule>
    <RouterView :key="$route.fullPath" />
  </FlotoModule>
</template>

<script>

export default {
  name: '<%= componentName %>',
  page() {
    return {
      title: '<%= h.inflection.titleize(fileName, true).replace(/-/g, ' ') %>'
    }
  },
}
</script>
