---
to: src/modules/<%= h.inflection.dasherize(name) %>/<%= h.inflection.dasherize(name) %>.js
---
<%
  const fileName = h.inflection.dasherize(name)
  const moduleName = h.inflection.titleize(fileName, true).replace(/-/g, '') + 'Module'
%>
import { Module } from '@plugins/modular'
import Routes from './<%= h.inflection.dasherize(name) %>-routes'
import configs from './config'


class <%= moduleName %> extends Module {
  /**
   * @constructor
   * @param {[key: string]: string} config
   */
  constructor(config = configs) {
    /** string name this name is used to get module from module manager */
    super(config.name, config)
  }
  routes = Routes

}

export default <%= moduleName %>
